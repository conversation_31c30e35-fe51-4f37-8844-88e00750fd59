name: Deploy to Production Server via SSH

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy via SSH
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js on Runner (for PM2)
        uses: actions/setup-node@v3
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install PM2 globally on Runner
        run: npm install -g pm2

      - name: Deploy via SSH
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -e
            echo "=== BẮT ĐẦU TRIỂN KHAI ==="
            echo "--- T<PERSON>y cập thư mục dự án ---"
            cd /opt/project-1/frontend/redai-v201-fe-app || {
              echo "❌ Lỗi: Không thể truy cập thư mục dự án!"
              exit 1
            }
            echo "--- <PERSON><PERSON> dụng Node.js phiên bản 22 (nếu dùng nvm) ---"
            if [ -f ~/.nvm/nvm.sh ]; then
              export NVM_DIR=~/.nvm
              source ~/.nvm/nvm.sh
              nvm use 22
              echo "--- Kiểm tra phiên bản Node.js ---"
              node -v
            fi
            echo "--- Cập nhật mã nguồn từ GitHub ---"
            git fetch origin main
            git reset --hard origin/main
            git clean -fd
            echo "--- Xóa thư mục build cũ ---"
            rm -rf dist
            echo "--- Cài đặt dependencies ---"
            npm install
            echo "--- Build dự án ---"
            NODE_OPTIONS="--max-old-space-size=4096" npm run build
            echo "--- Dừng ứng dụng hiện tại (nếu đang chạy) ---"
            pm2 stop redai-fe || true
            echo "--- Khởi động ứng dụng với PM2 và cấu hình cho domain v2.redai.vn ---"
            export VITE_ALLOWED_HOSTS=localhost,127.0.0.1,v2.redai.vn,************* && pm2 start npm --name "redai-fe" -- run preview:host -- --port 5173 --host
            # Hoặc chạy trực tiếp file built:
            # pm2 start dist/main.js --name "redai-fe"
            echo "--- Lưu cấu hình PM2 ---"
            pm2 save
            echo "=== TRIỂN KHAI THÀNH CÔNG ==="
            echo "Ứng dụng đang chạy với PM2:"
            pm2 list