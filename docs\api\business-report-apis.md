# Business Report APIs Documentation

## Phân tích giao diện trang /business/report

Dựa trên giao diện hiện tại của ReportPage.tsx, trang báo cáo bao gồm:

### 1. **Cards tổng quan (Top metrics)**
- Tổ<PERSON> doanh thu (Total Revenue)
- <PERSON><PERSON><PERSON> đơn hàng (Total Orders) 
- <PERSON><PERSON><PERSON><PERSON> hàng mới (New Customers)

### 2. **Tabs với biểu đồ**
- <PERSON><PERSON> <PERSON><PERSON>u (Sales Chart)
- Tab <PERSON> hàng (Orders Chart)
- <PERSON>b <PERSON><PERSON><PERSON> hàng (Customers Chart)
- Tab <PERSON>h<PERSON> (Products Chart)

### 3. **Cards phụ**
- <PERSON><PERSON><PERSON> phẩ<PERSON> b<PERSON> (Top Selling Products)
- <PERSON><PERSON><PERSON><PERSON> hàng tiềm năng (Potential Customers)

---

## APIs cần tạo ở Backend

### 1. **API Tổng quan báo cáo**

#### `GET /api/v1/business/reports/overview`

**<PERSON><PERSON> tả**: L<PERSON>y dữ liệu tổng quan cho dashboard báo cáo

**Query Parameters**:
```typescript
interface ReportOverviewQueryDto {
  startDate?: string;  // Format: YYYY-MM-DD
  endDate?: string;    // Format: YYYY-MM-DD
  period?: 'day' | 'week' | 'month' | 'quarter' | 'year'; // Default: 'month'
}
```

**Response DTO**:
```typescript
interface ReportOverviewResponseDto {
  totalRevenue: number;
  totalOrders: number;
  newCustomers: number;
  period: string;
  startDate: string;
  endDate: string;
  previousPeriod?: {
    totalRevenue: number;
    totalOrders: number;
    newCustomers: number;
    revenueGrowth: number;    // % tăng trưởng
    ordersGrowth: number;     // % tăng trưởng
    customersGrowth: number;  // % tăng trưởng
  };
}
```

### 2. **API Biểu đồ doanh thu**

#### `GET /api/v1/business/reports/sales-chart`

**Mô tả**: Lấy dữ liệu biểu đồ doanh thu theo thời gian

**Query Parameters**:
```typescript
interface SalesChartQueryDto {
  startDate?: string;  // Format: YYYY-MM-DD
  endDate?: string;    // Format: YYYY-MM-DD
  groupBy?: 'day' | 'week' | 'month' | 'quarter'; // Default: 'month'
  currency?: string;   // Default: 'VND'
}
```

**Response DTO**:
```typescript
interface SalesChartResponseDto {
  data: Array<{
    period: string;      // Tên kỳ (T1, T2, ... hoặc 01/2024, 02/2024, ...)
    date: string;        // Format: YYYY-MM-DD
    revenue: number;     // Doanh thu
    orders: number;      // Số đơn hàng
    averageOrderValue: number; // Giá trị đơn hàng trung bình
  }>;
  summary: {
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    highestRevenue: number;
    lowestRevenue: number;
  };
}
```

### 3. **API Biểu đồ đơn hàng**

#### `GET /api/v1/business/reports/orders-chart`

**Mô tả**: Lấy dữ liệu biểu đồ đơn hàng theo trạng thái và thời gian

**Query Parameters**:
```typescript
interface OrdersChartQueryDto {
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month' | 'quarter';
  status?: 'pending' | 'confirmed' | 'shipping' | 'delivered' | 'cancelled';
}
```

**Response DTO**:
```typescript
interface OrdersChartResponseDto {
  data: Array<{
    period: string;
    date: string;
    totalOrders: number;
    pendingOrders: number;
    confirmedOrders: number;
    shippingOrders: number;
    deliveredOrders: number;
    cancelledOrders: number;
  }>;
  statusBreakdown: {
    pending: number;
    confirmed: number;
    shipping: number;
    delivered: number;
    cancelled: number;
  };
}
```

### 4. **API Biểu đồ khách hàng**

#### `GET /api/v1/business/reports/customers-chart`

**Mô tả**: Lấy dữ liệu biểu đồ khách hàng mới và tổng khách hàng

**Query Parameters**:
```typescript
interface CustomersChartQueryDto {
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month' | 'quarter';
  segment?: 'new' | 'returning' | 'vip';
}
```

**Response DTO**:
```typescript
interface CustomersChartResponseDto {
  data: Array<{
    period: string;
    date: string;
    newCustomers: number;
    returningCustomers: number;
    totalCustomers: number;
    vipCustomers: number;
  }>;
  summary: {
    totalNewCustomers: number;
    totalReturningCustomers: number;
    customerRetentionRate: number; // % giữ chân khách hàng
    averageCustomerLifetime: number; // Thời gian sống trung bình (ngày)
  };
}
```

### 5. **API Biểu đồ sản phẩm**

#### `GET /api/v1/business/reports/products-chart`

**Mô tả**: Lấy dữ liệu biểu đồ hiệu suất sản phẩm

**Query Parameters**:
```typescript
interface ProductsChartQueryDto {
  startDate?: string;
  endDate?: string;
  categoryId?: number;
  limit?: number; // Default: 10
  sortBy?: 'revenue' | 'quantity' | 'orders'; // Default: 'revenue'
}
```

**Response DTO**:
```typescript
interface ProductsChartResponseDto {
  data: Array<{
    productId: number;
    productName: string;
    categoryName: string;
    revenue: number;
    quantitySold: number;
    ordersCount: number;
    averagePrice: number;
    imageUrl?: string;
  }>;
  summary: {
    totalProducts: number;
    totalRevenue: number;
    totalQuantitySold: number;
    bestSellingProduct: {
      id: number;
      name: string;
      revenue: number;
    };
  };
}
```

### 6. **API Sản phẩm bán chạy**

#### `GET /api/v1/business/reports/top-selling-products`

**Mô tả**: Lấy danh sách sản phẩm bán chạy nhất

**Query Parameters**:
```typescript
interface TopSellingProductsQueryDto {
  startDate?: string;
  endDate?: string;
  limit?: number; // Default: 10
  categoryId?: number;
}
```

**Response DTO**:
```typescript
interface TopSellingProductsResponseDto {
  data: Array<{
    rank: number;
    productId: number;
    productName: string;
    categoryName: string;
    imageUrl?: string;
    quantitySold: number;
    revenue: number;
    ordersCount: number;
    averageRating?: number;
    growthRate: number; // % tăng trưởng so với kỳ trước
  }>;
  meta: {
    totalItems: number;
    period: string;
  };
}
```

### 7. **API Khách hàng tiềm năng**

#### `GET /api/v1/business/reports/potential-customers`

**Mô tả**: Lấy danh sách khách hàng tiềm năng

**Query Parameters**:
```typescript
interface PotentialCustomersQueryDto {
  limit?: number; // Default: 10
  minOrderValue?: number;
  minOrderCount?: number;
  lastOrderDays?: number; // Số ngày từ đơn hàng cuối
}
```

**Response DTO**:
```typescript
interface PotentialCustomersResponseDto {
  data: Array<{
    customerId: number;
    customerName: string;
    email: string;
    phone?: string;
    avatarUrl?: string;
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate: string;
    daysSinceLastOrder: number;
    potentialScore: number; // Điểm tiềm năng (0-100)
    tags: string[];
  }>;
  meta: {
    totalItems: number;
    averagePotentialScore: number;
  };
}
```

---

## Cấu trúc Controller và Service

### Controller Structure:
```typescript
@Controller('business/reports')
export class BusinessReportController {
  
  @Get('overview')
  async getOverview(@Query() query: ReportOverviewQueryDto) {}
  
  @Get('sales-chart')
  async getSalesChart(@Query() query: SalesChartQueryDto) {}
  
  @Get('orders-chart') 
  async getOrdersChart(@Query() query: OrdersChartQueryDto) {}
  
  @Get('customers-chart')
  async getCustomersChart(@Query() query: CustomersChartQueryDto) {}
  
  @Get('products-chart')
  async getProductsChart(@Query() query: ProductsChartQueryDto) {}
  
  @Get('top-selling-products')
  async getTopSellingProducts(@Query() query: TopSellingProductsQueryDto) {}
  
  @Get('potential-customers')
  async getPotentialCustomers(@Query() query: PotentialCustomersQueryDto) {}
}
```

### Service Methods:
```typescript
@Injectable()
export class BusinessReportService {
  
  async getReportOverview(userId: number, query: ReportOverviewQueryDto): Promise<ReportOverviewResponseDto> {}
  
  async getSalesChartData(userId: number, query: SalesChartQueryDto): Promise<SalesChartResponseDto> {}
  
  async getOrdersChartData(userId: number, query: OrdersChartQueryDto): Promise<OrdersChartResponseDto> {}
  
  async getCustomersChartData(userId: number, query: CustomersChartQueryDto): Promise<CustomersChartResponseDto> {}
  
  async getProductsChartData(userId: number, query: ProductsChartQueryDto): Promise<ProductsChartResponseDto> {}
  
  async getTopSellingProducts(userId: number, query: TopSellingProductsQueryDto): Promise<TopSellingProductsResponseDto> {}
  
  async getPotentialCustomers(userId: number, query: PotentialCustomersQueryDto): Promise<PotentialCustomersResponseDto> {}
}
```

---

## Database Queries cần thiết

### 1. **Doanh thu theo thời gian**:
```sql
SELECT 
  DATE_FORMAT(created_at, '%Y-%m') as period,
  SUM(total_amount) as revenue,
  COUNT(*) as orders,
  AVG(total_amount) as average_order_value
FROM orders 
WHERE user_id = ? AND status = 'completed'
  AND created_at BETWEEN ? AND ?
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
ORDER BY period;
```

### 2. **Khách hàng mới theo thời gian**:
```sql
SELECT 
  DATE_FORMAT(created_at, '%Y-%m') as period,
  COUNT(*) as new_customers
FROM customers 
WHERE user_id = ? AND created_at BETWEEN ? AND ?
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
ORDER BY period;
```

### 3. **Sản phẩm bán chạy**:
```sql
SELECT 
  p.id, p.name, p.image_url,
  SUM(oi.quantity) as quantity_sold,
  SUM(oi.price * oi.quantity) as revenue,
  COUNT(DISTINCT oi.order_id) as orders_count
FROM products p
JOIN order_items oi ON p.id = oi.product_id
JOIN orders o ON oi.order_id = o.id
WHERE o.user_id = ? AND o.status = 'completed'
  AND o.created_at BETWEEN ? AND ?
GROUP BY p.id
ORDER BY revenue DESC
LIMIT ?;
```

### 4. **Khách hàng tiềm năng**:
```sql
SELECT 
  c.id, c.name, c.email, c.phone, c.avatar_url,
  COUNT(o.id) as total_orders,
  SUM(o.total_amount) as total_spent,
  AVG(o.total_amount) as average_order_value,
  MAX(o.created_at) as last_order_date,
  DATEDIFF(NOW(), MAX(o.created_at)) as days_since_last_order
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id
WHERE c.user_id = ?
GROUP BY c.id
HAVING total_orders > 0
ORDER BY total_spent DESC, days_since_last_order ASC
LIMIT ?;
```

---

## Validation Rules

### Date Validation:
- `startDate` và `endDate` phải có format `YYYY-MM-DD`
- `endDate` phải lớn hơn hoặc bằng `startDate`
- Khoảng thời gian tối đa: 2 năm

### Pagination:
- `limit` tối đa: 100
- `limit` tối thiểu: 1

### Period Validation:
- `groupBy` chỉ chấp nhận: `day`, `week`, `month`, `quarter`
- Default period dựa trên khoảng thời gian:
  - <= 7 ngày: `day`
  - <= 3 tháng: `week`  
  - <= 2 năm: `month`
  - > 2 năm: `quarter`

---

## Error Handling

### Common Error Codes:
- `400`: Invalid query parameters
- `401`: Unauthorized access
- `403`: Insufficient permissions
- `404`: Data not found
- `500`: Internal server error

### Error Response Format:
```typescript
interface ErrorResponseDto {
  statusCode: number;
  message: string;
  error: string;
  timestamp: string;
  path: string;
}
```
