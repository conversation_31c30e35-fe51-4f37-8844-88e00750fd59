# API Proposal for Business Report Module

## Overview
Dựa trên giao diện trang `/business/report`, đây là đề xuất các API cần thiết để cung cấp dữ liệu cho module báo cáo kinh doanh.

## 1. Dashboard Overview APIs

### 1.1 Get Business Overview Statistics
**Endpoint:** `GET /v1/user/business/reports/overview`

**Description:** Lấy thống kê tổng quan kinh doanh (tổng doanh thu, tổng đơn hàng, khách hàng mới)

**Query Parameters:**
```typescript
interface OverviewQueryDto {
  startDate?: string; // ISO date string, default: start of current year
  endDate?: string;   // ISO date string, default: current date
  period?: 'day' | 'week' | 'month' | 'quarter' | 'year'; // default: 'year'
}
```

**Response:**
```typescript
interface BusinessOverviewDto {
  totalRevenue: {
    amount: number;
    currency: string;
    previousPeriodAmount?: number;
    growthPercentage?: number;
  };
  totalOrders: {
    count: number;
    previousPeriodCount?: number;
    growthPercentage?: number;
  };
  newCustomers: {
    count: number;
    previousPeriodCount?: number;
    growthPercentage?: number;
  };
  period: {
    startDate: string;
    endDate: string;
    type: string;
  };
}
```

## 2. Chart Data APIs

### 2.1 Get Revenue Chart Data
**Endpoint:** `GET /v1/user/business/reports/revenue-chart`

**Description:** Lấy dữ liệu biểu đồ doanh thu theo thời gian

**Query Parameters:**
```typescript
interface RevenueChartQueryDto {
  startDate?: string;
  endDate?: string;
  groupBy: 'day' | 'week' | 'month' | 'quarter' | 'year'; // default: 'month'
  currency?: string; // default: 'VND'
}
```

**Response:**
```typescript
interface RevenueChartDto {
  data: Array<{
    period: string; // e.g., "2023-01", "2023-Q1", "2023-W01"
    periodLabel: string; // e.g., "Tháng 1", "Quý 1", "Tuần 1"
    revenue: number;
    orderCount: number;
    averageOrderValue: number;
  }>;
  summary: {
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    currency: string;
  };
}
```

### 2.2 Get Orders Chart Data
**Endpoint:** `GET /v1/user/business/reports/orders-chart`

**Description:** Lấy dữ liệu biểu đồ đơn hàng theo trạng thái và thời gian

**Query Parameters:**
```typescript
interface OrdersChartQueryDto {
  startDate?: string;
  endDate?: string;
  groupBy: 'day' | 'week' | 'month' | 'quarter' | 'year';
  status?: string[]; // filter by order status
}
```

**Response:**
```typescript
interface OrdersChartDto {
  data: Array<{
    period: string;
    periodLabel: string;
    totalOrders: number;
    completedOrders: number;
    pendingOrders: number;
    cancelledOrders: number;
    statusBreakdown: Array<{
      status: string;
      count: number;
      percentage: number;
    }>;
  }>;
  summary: {
    totalOrders: number;
    completionRate: number;
    cancellationRate: number;
  };
}
```

### 2.3 Get Customers Chart Data
**Endpoint:** `GET /v1/user/business/reports/customers-chart`

**Description:** Lấy dữ liệu biểu đồ khách hàng (mới, quay lại, tổng)

**Query Parameters:**
```typescript
interface CustomersChartQueryDto {
  startDate?: string;
  endDate?: string;
  groupBy: 'day' | 'week' | 'month' | 'quarter' | 'year';
}
```

**Response:**
```typescript
interface CustomersChartDto {
  data: Array<{
    period: string;
    periodLabel: string;
    newCustomers: number;
    returningCustomers: number;
    totalActiveCustomers: number;
    customerRetentionRate: number;
  }>;
  summary: {
    totalNewCustomers: number;
    totalReturningCustomers: number;
    averageRetentionRate: number;
  };
}
```

### 2.4 Get Products Chart Data
**Endpoint:** `GET /v1/user/business/reports/products-chart`

**Description:** Lấy dữ liệu biểu đồ sản phẩm (bán chạy, tồn kho, etc.)

**Query Parameters:**
```typescript
interface ProductsChartQueryDto {
  startDate?: string;
  endDate?: string;
  groupBy: 'day' | 'week' | 'month' | 'quarter' | 'year';
  metric: 'sales' | 'inventory' | 'profit'; // default: 'sales'
}
```

**Response:**
```typescript
interface ProductsChartDto {
  data: Array<{
    period: string;
    periodLabel: string;
    totalProductsSold: number;
    uniqueProductsSold: number;
    averageProductPrice: number;
    topSellingProducts: Array<{
      productId: number;
      productName: string;
      quantitySold: number;
      revenue: number;
    }>;
  }>;
  summary: {
    totalProductsSold: number;
    totalUniqueProducts: number;
    averageProductPrice: number;
  };
}
```

## 3. Top Lists APIs

### 3.1 Get Top Selling Products
**Endpoint:** `GET /v1/user/business/reports/top-products`

**Description:** Lấy danh sách sản phẩm bán chạy

**Query Parameters:**
```typescript
interface TopProductsQueryDto {
  startDate?: string;
  endDate?: string;
  limit?: number; // default: 10
  sortBy?: 'quantity' | 'revenue' | 'profit'; // default: 'quantity'
}
```

**Response:**
```typescript
interface TopProductsDto {
  products: Array<{
    productId: number;
    productName: string;
    productImage?: string;
    quantitySold: number;
    revenue: number;
    profit?: number;
    averageRating?: number;
    category?: string;
  }>;
  period: {
    startDate: string;
    endDate: string;
  };
}
```

### 3.2 Get Potential Customers
**Endpoint:** `GET /v1/user/business/reports/potential-customers`

**Description:** Lấy danh sách khách hàng tiềm năng

**Query Parameters:**
```typescript
interface PotentialCustomersQueryDto {
  limit?: number; // default: 10
  criteria?: 'high_value' | 'frequent_buyer' | 'recent_activity'; // default: 'high_value'
}
```

**Response:**
```typescript
interface PotentialCustomersDto {
  customers: Array<{
    customerId: number;
    customerName: string;
    email?: string;
    phone?: string;
    totalOrders: number;
    totalSpent: number;
    lastOrderDate: string;
    averageOrderValue: number;
    potentialScore: number; // 0-100
    recommendedActions: string[];
  }>;
}
```

## 4. Additional Analytics APIs

### 4.1 Get Revenue Breakdown
**Endpoint:** `GET /v1/user/business/reports/revenue-breakdown`

**Description:** Phân tích chi tiết doanh thu theo nhiều tiêu chí

**Query Parameters:**
```typescript
interface RevenueBreakdownQueryDto {
  startDate?: string;
  endDate?: string;
  breakdownBy: 'product' | 'category' | 'customer_segment' | 'payment_method';
}
```

### 4.2 Get Performance Metrics
**Endpoint:** `GET /v1/user/business/reports/performance-metrics`

**Description:** Lấy các chỉ số hiệu suất kinh doanh

**Response:**
```typescript
interface PerformanceMetricsDto {
  conversionRate: number;
  averageOrderValue: number;
  customerLifetimeValue: number;
  customerAcquisitionCost: number;
  returnOnInvestment: number;
  inventoryTurnover: number;
  profitMargin: number;
}
```

## 5. Export APIs

### 5.1 Export Report Data
**Endpoint:** `POST /v1/user/business/reports/export`

**Description:** Xuất dữ liệu báo cáo ra file

**Request Body:**
```typescript
interface ExportReportDto {
  reportType: 'overview' | 'revenue' | 'orders' | 'customers' | 'products';
  format: 'excel' | 'pdf' | 'csv';
  startDate?: string;
  endDate?: string;
  includeCharts?: boolean;
}
```

**Response:**
```typescript
interface ExportReportResponseDto {
  downloadUrl: string;
  fileName: string;
  expiresAt: string;
}
```

## Implementation Notes

1. **Caching**: Implement Redis caching for frequently accessed report data
2. **Performance**: Use database indexing on date fields and user_id
3. **Real-time**: Consider WebSocket for real-time dashboard updates
4. **Permissions**: Ensure users can only access their own business data
5. **Rate Limiting**: Implement rate limiting for export APIs
6. **Data Aggregation**: Pre-calculate common metrics for better performance

## Error Codes

- `40001`: Invalid date range
- `40002`: Unsupported chart grouping
- `40003`: Export format not supported
- `40004`: Report data not available
- `50001`: Report generation failed
