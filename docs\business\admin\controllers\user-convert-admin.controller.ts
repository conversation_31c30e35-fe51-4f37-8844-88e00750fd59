import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserConvertAdminService } from '@modules/business/admin/services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryUserConvertDto,
  UserConvertResponseDto,
  UserConvertDetailResponseDto
} from '../dto';
import { ApiErrorResponse, ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến bản ghi chuyển đổi khách hàng cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS)
@ApiExtraModels(
  ApiResponseDto,
  UserConvertResponseDto,
  UserConvertDetailResponseDto,
  QueryUserConvertDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/user-converts')
export class UserConvertAdminController {
  constructor(private readonly userConvertAdminService: UserConvertAdminService) {}

  /**
   * Lấy danh sách bản ghi chuyển đổi khách hàng với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách bản ghi chuyển đổi khách hàng phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách bản ghi chuyển đổi khách hàng',
    description: 'Lấy danh sách bản ghi chuyển đổi khách hàng với phân trang, tìm kiếm, lọc và sắp xếp',
  })

  @ApiResponse({
    status: 200,
    description: 'Danh sách bản ghi chuyển đổi khách hàng',
    schema: ApiResponseDto.getPaginatedSchema(UserConvertResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_FETCH_ERROR,
      BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR
    ]
  )
  async getAllUserConverts(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryUserConvertDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserConvertResponseDto>>> {
    const result = await this.userConvertAdminService.getUserConverts(employeeId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách bản ghi chuyển đổi khách hàng thành công');
  }

  /**
   * Lấy thông tin chi tiết bản ghi chuyển đổi khách hàng theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param convertId ID của bản ghi chuyển đổi khách hàng
   * @returns Thông tin chi tiết bản ghi chuyển đổi khách hàng
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết bản ghi chuyển đổi khách hàng theo ID',
    description: 'Lấy thông tin chi tiết của một bản ghi chuyển đổi khách hàng cụ thể',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID của bản ghi chuyển đổi khách hàng',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết bản ghi chuyển đổi khách hàng',
    schema: ApiResponseDto.getSchema(UserConvertDetailResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_FETCH_ERROR,
  )
  async getUserConvertById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) convertId: number,
  ): Promise<ApiResponseDto<UserConvertDetailResponseDto>> {
    const result = await this.userConvertAdminService.getUserConvertById(employeeId, convertId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết bản ghi chuyển đổi khách hàng thành công');
  }
}
