import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize } from 'class-validator';

/**
 * DTO cho việc xóa nhiều trường tùy chỉnh cùng lúc
 */
export class BatchDeleteCustomFieldDto {
  /**
   * Mảng các ID của trường tùy chỉnh cần xóa
   */
  @ApiProperty({
    description: 'Mảng các ID của trường tùy chỉnh cần xóa',
    example: [1, 2, 3],
    type: [Number],
    minItems: 1,
  })
  @IsArray({ message: 'ids phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 ID để xóa' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là số' })
  @IsNotEmpty({ each: true, message: 'ID không được để trống' })
  ids: number[];
}

/**
 * DTO phản hồi cho việc xóa nhiều trường tùy chỉnh
 */
export class BatchDeleteCustomFieldResponseDto {
  /**
   * Số lượng trường tùy chỉnh đã xóa thành công
   */
  @ApiProperty({
    description: 'Số lượng trường tùy chỉnh đã xóa thành công',
    example: 2,
  })
  successCount: number;

  /**
   * Số lượng trường tùy chỉnh xóa thất bại
   */
  @ApiProperty({
    description: 'Số lượng trường tùy chỉnh xóa thất bại',
    example: 1,
  })
  failureCount: number;

  /**
   * Tổng số trường tùy chỉnh được xử lý
   */
  @ApiProperty({
    description: 'Tổng số trường tùy chỉnh được xử lý',
    example: 3,
  })
  totalCount: number;

  /**
   * Danh sách kết quả chi tiết cho từng trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Danh sách kết quả chi tiết cho từng trường tùy chỉnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: {
          type: 'number',
          description: 'ID của trường tùy chỉnh',
          example: 1,
        },
        success: {
          type: 'boolean',
          description: 'Trạng thái xóa thành công hay không',
          example: true,
        },
        message: {
          type: 'string',
          description: 'Thông báo kết quả',
          example: 'Xóa thành công',
        },
      },
    },
  })
  results: Array<{
    id: number;
    success: boolean;
    message: string;
  }>;
}
