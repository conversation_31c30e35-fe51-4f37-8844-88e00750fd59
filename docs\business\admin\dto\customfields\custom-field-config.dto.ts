import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsArray, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho tùy chọn trong cấu hình trường tùy chỉnh
 */
export class OptionDto {
  @ApiProperty({
    description: 'Nhãn hiển thị của tùy chọn',
    example: 'Đỏ',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Giá trị của tùy chọn',
    example: 'red',
  })
  value: any; // Sử dụng any để hỗ trợ nhiều kiểu dữ liệu
}

/**
 * DTO cho cấu hình validation trong trường tùy chỉnh
 */
export class ValidationDto {
  @ApiProperty({
    description: 'Mẫu regex để kiểm tra',
    example: '^[A-Z0-9]{10,15}$',
    required: false,
  })
  @IsOptional()
  @IsString()
  pattern?: string;

  @ApiProperty({
    description: 'Độ dài tối đa',
    example: 15,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  maxLength?: number;

  @ApiProperty({
    description: 'Độ dài tối thiểu',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  minLength?: number;
}

/**
 * DTO cho cấu hình trường tùy chỉnh
 */
export class CustomFieldConfigDto {
  @ApiProperty({
    description: 'Placeholder hiển thị trong trường nhập liệu',
    example: 'Nhập màu sắc',
    required: false,
  })
  @IsOptional()
  @IsString()
  placeholder?: string;

  @ApiProperty({
    description: 'Độ dài tối đa của trường',
    example: 50,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  maxLength?: number;

  @ApiProperty({
    description: 'Mô tả về trường',
    example: 'Màu sắc chính của sản phẩm',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Các tùy chọn cho trường select/dropdown',
    type: [OptionDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OptionDto)
  options?: OptionDto[];

  @ApiProperty({
    description: 'Giá trị mặc định',
    example: 'red',
    required: false,
  })
  @IsOptional()
  defaultValue?: any; // Sử dụng any để hỗ trợ nhiều kiểu dữ liệu

  @ApiProperty({
    description: 'Cấu hình validation',
    type: ValidationDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ValidationDto)
  validation?: ValidationDto;

  @ApiProperty({
    description: 'Các thuộc tính tùy chỉnh khác',
    required: false,
  })
  @IsOptional()
  additionalProperties?: any; // Sử dụng any để hỗ trợ nhiều kiểu dữ liệu
}
