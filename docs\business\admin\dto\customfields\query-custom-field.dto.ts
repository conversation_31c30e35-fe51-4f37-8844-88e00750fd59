import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@dto/query.dto';
import { CustomFieldStatus } from './custom-field-response.dto';

/**
 * DTO cho các tham số truy vấn danh sách trường tùy chỉnh
 */
export class QueryCustomFieldDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Loại trường',
    example: 'text',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Thành phần UI',
    example: 'input',
  })
  @IsOptional()
  @IsString()
  component?: string;

  @ApiPropertyOptional({
    description: 'ID nhân viên tạo',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  employeeId?: number;

  @ApiPropertyOptional({
    description: 'Trạng thái trường tùy chỉnh',
    enum: CustomFieldStatus,
    enumName: 'CustomFieldStatus',
    example: CustomFieldStatus.APPROVED,
  })
  @IsOptional()
  @IsEnum(CustomFieldStatus)
  status?: CustomFieldStatus;

  constructor(partial: Partial<QueryCustomFieldDto>) {
    super();
    Object.assign(this, partial);
  }
}
