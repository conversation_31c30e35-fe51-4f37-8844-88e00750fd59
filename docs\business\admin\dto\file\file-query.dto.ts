import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho query tìm kiếm file
 */
export class FileQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID thư mục chứa tệp tin',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  folderId?: number;
}
