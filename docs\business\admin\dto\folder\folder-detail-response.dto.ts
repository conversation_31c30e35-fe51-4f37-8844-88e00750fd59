import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { FileResponseDto } from '../file';

/**
 * DTO cho thông tin thư mục cha
 */
export class ParentFolderInfoDto {
  @ApiProperty({
    description: 'ID của thư mục cha',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên thư mục cha',
    example: 'Documents',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Đường dẫn thư mục cha',
    example: '/Documents',
  })
  @Expose()
  path: string;
}

/**
 * DTO cho thông tin người dùng sở hữu
 */
export class OwnerInfoDto {
  @ApiProperty({
    description: 'ID của người dùng sở hữu',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiPropertyOptional({
    description: 'Tên người dùng sở hữu',
    example: '<PERSON>',
  })
  @Expose()
  name?: string;
}

/**
 * DTO cho response chi tiết của folder
 */
export class FolderDetailResponseDto {
  @ApiProperty({
    description: 'ID của thư mục',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Documents',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Thông tin thư mục cha',
    type: ParentFolderInfoDto,
    nullable: true,
  })
  @Expose()
  @Type(() => ParentFolderInfoDto)
  parent?: ParentFolderInfoDto | null;

  @ApiProperty({
    description: 'Thông tin người dùng sở hữu',
    type: OwnerInfoDto,
  })
  @Expose()
  @Type(() => OwnerInfoDto)
  owner: OwnerInfoDto;

  @ApiProperty({
    description: 'Đường dẫn thư mục',
    example: '/Documents',
  })
  @Expose()
  path: string;

  @ApiPropertyOptional({
    description: 'ID kho ảo gốc',
    example: 1,
    nullable: true,
  })
  @Expose()
  root?: number | null;

  @ApiProperty({
    description: 'Số lượng file trong thư mục',
    example: 5,
  })
  @Expose()
  fileCount: number;

  @ApiProperty({
    description: 'Danh sách file trong thư mục',
    type: [FileResponseDto],
  })
  @Expose()
  @Type(() => FileResponseDto)
  files: FileResponseDto[];

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1620000000000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian tạo đã định dạng',
    example: '2023-05-03 14:30:00',
  })
  @Expose()
  formattedCreatedAt: string;

  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1620000000000,
  })
  @Expose()
  updatedAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật đã định dạng',
    example: '2023-05-03 14:30:00',
  })
  @Expose()
  formattedUpdatedAt: string;
}
