import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho query tìm kiếm folder
 */
export class FolderQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID thư mục cha',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  parentId?: number;

  @ApiPropertyOptional({
    description: 'ID người dùng sở hữu',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;
}
