import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Class cho thông tin người nhận hàng
 */
export class Recipient {
  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn A'
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0912345678'
  })
  @IsNotEmpty()
  @IsString()
  phone: string;
}

/**
 * Class cho thông tin vận chuyển
 */
export class LogisticInfo {
  /**
   * Địa chỉ giao hàng
   */
  @ApiProperty({
    description: 'Địa chỉ giao hàng',
    example: 'Số 1, Đường ABC, Quận XYZ, Hà Nội'
  })
  @IsNotEmpty()
  @IsString()
  address: string;

  /**
   * Đơn vị vận chuyển
   */
  @ApiProperty({
    description: 'Đơn vị vận chuyển',
    example: 'GHN',
    required: false
  })
  @IsOptional()
  @IsString()
  carrier?: string;

  /**
   * Mã vận đơn
   */
  @ApiProperty({
    description: 'Mã vận đơn',
    example: 'GHN123456789',
    required: false
  })
  @IsOptional()
  @IsString()
  trackingNumber?: string;

  /**
   * Thông tin người nhận
   */
  @ApiProperty({
    description: 'Thông tin người nhận',
    type: Recipient,
    required: false
  })
  @IsOptional()
  @IsObject()
  @Type(() => Recipient)
  recipient?: Recipient;

  /**
   * Các thông tin khác về vận chuyển
   */
  [key: string]: any;
}
