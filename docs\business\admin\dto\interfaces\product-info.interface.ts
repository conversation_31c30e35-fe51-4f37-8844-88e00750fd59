import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, Min } from 'class-validator';

/**
 * Class cho thông tin sản phẩm trong đơn hàng
 */
export class ProductInfo {
  /**
   * ID của sản phẩm
   */
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  productId: number;

  /**
   * Tên sản phẩm
   */
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Sản phẩm A'
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * <PERSON><PERSON> lượng
   */
  @ApiProperty({
    description: 'Số lượng',
    example: 2
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  quantity: number;

  /**
   * <PERSON><PERSON><PERSON> sản phẩm
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> sản phẩm',
    example: 100000
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  price: number;

  /**
   * <PERSON><PERSON><PERSON> thu<PERSON>c t<PERSON>h khác của sản phẩm
   */
  [key: string]: any;
}
