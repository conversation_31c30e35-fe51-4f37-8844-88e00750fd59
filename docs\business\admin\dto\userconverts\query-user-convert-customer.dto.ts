import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, IsUUID, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@dto/query.dto';

/**
 * DTO cho các tham số truy vấn danh sách khách hàng chuyển đổi
 */
export class QueryUserConvertCustomerDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID người dùng sở hữu khách hàng',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @IsOptional()
  @IsUUID('4')
  agentId?: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> tảng nguồn (Facebook, Web,...)',
    example: 'Facebook'
  })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiPropertyOptional({
    description: 'Thời gian tạo từ (timestamp)',
    example: 1625097600000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtFrom?: number;

  @ApiPropertyOptional({
    description: 'Thời gian tạo đến (timestamp)',
    example: 1625184000000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtTo?: number;
}
