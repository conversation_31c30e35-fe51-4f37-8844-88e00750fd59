import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { ConvertContent } from '../interfaces';

/**
 * DTO cho response trả về thông tin bản ghi chuyển đổi khách hàng
 */
export class UserConvertResponseDto {
  @ApiProperty({
    description: 'ID bản ghi chuyển đổi',
    example: 1,
    examples: [1, 2, 3]
  })
  @IsNotEmpty({ message: 'ID bản ghi chuyển đổi không được để trống' })
  @IsNumber({}, { message: 'ID bản ghi chuyển đổi phải là số' })
  id: number;

  @ApiProperty({
    description: 'ID khách hàng được chuyển đổi',
    example: 1,
    examples: [1, 2, 3, null],
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID khách hàng phải là số' })
  convertCustomerId: number | null;

  @ApiProperty({
    description: 'ID người dùng thực hiện chuyển đổi',
    example: 1,
    examples: [1, 2, 3, null],
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  userId: number | null;

  @ApiProperty({
    description: 'Loại chuyển đổi (ví dụ: online, offline, referral)',
    example: 'online',
    examples: ['online', 'offline', 'referral', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Loại chuyển đổi phải là chuỗi' })
  conversionType: string | null;

  @ApiProperty({
    description: 'Nguồn gốc chuyển đổi (ví dụ: website, social media, event)',
    example: 'website',
    examples: ['website', 'social media', 'event', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Nguồn gốc chuyển đổi phải là chuỗi' })
  source: string | null;

  @ApiProperty({
    description: 'Ghi chú thêm về chuyển đổi',
    example: 'Khách hàng quan tâm đến sản phẩm X',
    examples: ['Khách hàng quan tâm đến sản phẩm X', 'Khách hàng cần tư vấn thêm', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú phải là chuỗi' })
  notes: string | null;

  @ApiProperty({
    description: 'Thông tin bổ sung (JSON)',
    example: { additionalInfo: 'Thông tin thêm về khách hàng' },
    examples: [
      { additionalInfo: 'Thông tin thêm về khách hàng' },
      { referenceSource: 'Facebook', additionalInfo: 'Khách hàng tiềm năng' },
      null
    ],
    nullable: true,
    type: () => ConvertContent
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin bổ sung phải là đối tượng JSON' })
  @Type(() => ConvertContent)
  content: ConvertContent | null;

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
    examples: [1625097600000, 1630000000000]
  })
  @IsNotEmpty({ message: 'Thời gian tạo không được để trống' })
  @IsNumber({}, { message: 'Thời gian tạo phải là số' })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1625184000000,
    examples: [1625184000000, 1640000000000]
  })
  @IsNotEmpty({ message: 'Thời gian cập nhật không được để trống' })
  @IsNumber({}, { message: 'Thời gian cập nhật phải là số' })
  updatedAt: number;
}
