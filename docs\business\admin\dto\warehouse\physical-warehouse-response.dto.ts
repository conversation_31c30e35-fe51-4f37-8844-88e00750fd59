import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { WarehouseResponseDto } from './warehouse-response.dto';

/**
 * DTO cho response thông tin kho vật lý
 */
export class PhysicalWarehouseResponseDto {
  @ApiProperty({
    description: 'ID của kho',
    example: 1
  })
  @IsNumber()
  @Expose()
  warehouseId: number;

  @ApiProperty({
    description: 'Địa chỉ kho',
    example: '123 Storage St, Warehouse City'
  })
  @IsString()
  @Expose()
  address: string;

  @ApiProperty({
    description: 'Sức chứa kho',
    example: 5000,
    nullable: true
  })
  @IsOptional()
  @IsNumber()
  @Expose()
  capacity: number;

  @ApiProperty({
    description: 'Thông tin kho',
    type: () => WarehouseResponseDto
  })
  @Type(() => WarehouseResponseDto)
  @IsObject()
  @ValidateNested()
  @Expose()
  warehouse: WarehouseResponseDto;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<PhysicalWarehouseResponseDto>) {
    Object.assign(this, partial);
  }
}
