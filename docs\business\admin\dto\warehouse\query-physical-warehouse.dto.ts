import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho truy vấn danh sách kho vật lý
 */
export class QueryPhysicalWarehouseDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Lọc theo địa chỉ (tìm kiếm một phần)',
    example: 'Hà Nội'
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo sức chứa tối thiểu',
    example: 1000
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minCapacity?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo sức chứa tối đa',
    example: 5000
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxCapacity?: number;
}
