import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho truy vấn danh sách trường tùy chỉnh của kho
 */
export class QueryWarehouseCustomFieldDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Lọc theo ID kho',
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  warehouseId?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo ID trường tùy chỉnh',
    example: 3
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fieldId?: number;
}
