import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { WarehouseResponseDto } from './warehouse-response.dto';
import { CustomFieldValueResponseDto } from './warehouse-custom-field-response.dto';

/**
 * DTO cho response thông tin chi tiết kho ảo
 */
export class VirtualWarehouseDetailResponseDto {
  @ApiProperty({
    description: 'ID của kho',
    example: 2
  })
  @IsNumber()
  @Expose()
  warehouseId: number;

  @ApiProperty({
    description: 'Hệ thống liên kết',
    example: 'ERP System',
    nullable: true
  })
  @IsOptional()
  @IsString()
  @Expose()
  associatedSystem: string;

  @ApiProperty({
    description: 'Mục đích sử dụng',
    example: 'Digital inventory management',
    nullable: true
  })
  @IsOptional()
  @IsString()
  @Expose()
  purpose: string;

  @ApiProperty({
    description: 'Thông tin kho',
    type: () => WarehouseResponseDto
  })
  @Type(() => WarehouseResponseDto)
  @IsObject()
  @ValidateNested()
  @Expose()
  warehouse: WarehouseResponseDto;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh',
    type: [CustomFieldValueResponseDto],
    nullable: true
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldValueResponseDto)
  @Expose()
  customFields?: CustomFieldValueResponseDto[];

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<VirtualWarehouseDetailResponseDto>) {
    Object.assign(this, partial);
  }
}
