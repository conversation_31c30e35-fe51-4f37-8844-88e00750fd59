import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { WarehouseResponseDto } from './warehouse-response.dto';

/**
 * DTO cho response thông tin kho ảo
 */
export class VirtualWarehouseResponseDto {
  @ApiProperty({
    description: 'ID của kho',
    example: 2
  })
  @IsNumber()
  @Expose()
  warehouseId: number;

  @ApiProperty({
    description: 'Hệ thống liên kết',
    example: 'ERP System',
    nullable: true
  })
  @IsOptional()
  @IsString()
  @Expose()
  associatedSystem: string;

  @ApiProperty({
    description: 'Mục đích sử dụng',
    example: 'Digital inventory management',
    nullable: true
  })
  @IsOptional()
  @IsString()
  @Expose()
  purpose: string;

  @ApiProperty({
    description: 'Thông tin kho',
    type: () => WarehouseResponseDto
  })
  @Type(() => WarehouseResponseDto)
  @IsObject()
  @ValidateNested()
  @Expose()
  warehouse: WarehouseResponseDto;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<VirtualWarehouseResponseDto>) {
    Object.assign(this, partial);
  }
}
