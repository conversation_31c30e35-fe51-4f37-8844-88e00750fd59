import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho giá trị của trường tùy chỉnh
 */
export class CustomFieldValueDto {
  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    example: 'North',
    oneOf: [
      { type: 'string' },
      { type: 'number' },
      { type: 'boolean' },
      { type: 'array', items: { type: 'string' } }
    ]
  })
  @Expose()
  value: string | number | boolean | string[];

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<CustomFieldValueDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho cấu hình của trường tùy chỉnh
 */
export class CustomFieldConfigDto {
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> tùy chọn cho trường',
    example: ['North', 'South', 'East', 'West'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Expose()
  options?: string[];

  @ApiProperty({
    description: 'Giá trị tối thiểu',
    example: 0,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Expose()
  min?: number;

  @ApiProperty({
    description: 'Giá trị tối đa',
    example: 100,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Expose()
  max?: number;

  @ApiProperty({
    description: 'Mẫu định dạng (regex)',
    example: '^[A-Za-z0-9]+$',
    required: false
  })
  @IsOptional()
  @IsString()
  @Expose()
  pattern?: string;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<CustomFieldConfigDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho chi tiết trường tùy chỉnh
 */
export class FieldDetailsDto {
  @ApiProperty({
    description: 'Nhãn của trường',
    example: 'Khu vực'
  })
  @IsString()
  @Expose()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'TEXT'
  })
  @IsString()
  @Expose()
  type: string;

  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true
  })
  @IsBoolean()
  @Expose()
  required: boolean;

  @ApiProperty({
    description: 'Cấu hình của trường',
    type: CustomFieldConfigDto,
    example: { options: ['North', 'South', 'East', 'West'] }
  })
  @ValidateNested()
  @Type(() => CustomFieldConfigDto)
  @Expose()
  configJson: CustomFieldConfigDto;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<FieldDetailsDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho response thông tin trường tùy chỉnh của kho
 */
export class WarehouseCustomFieldResponseDto {
  @ApiProperty({
    description: 'ID của kho',
    example: 1
  })
  @IsNumber()
  @Expose()
  warehouseId: number;

  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 3
  })
  @IsNumber()
  @Expose()
  fieldId: number;

  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    example: { value: 'North' }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => CustomFieldValueDto)
  @Expose()
  value: CustomFieldValueDto;

  @ApiProperty({
    description: 'Tên kho',
    example: 'Kho chính'
  })
  @IsString()
  @Expose()
  warehouseName: string;

  @ApiProperty({
    description: 'Nhãn của trường tùy chỉnh',
    example: 'Khu vực'
  })
  @IsString()
  @Expose()
  fieldLabel: string;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<WarehouseCustomFieldResponseDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho response thông tin chi tiết trường tùy chỉnh của kho
 */
export class WarehouseCustomFieldDetailResponseDto extends WarehouseCustomFieldResponseDto {
  @ApiProperty({
    description: 'Chi tiết trường tùy chỉnh',
    example: {
      label: 'Khu vực',
      type: 'TEXT',
      required: true,
      configJson: { options: ['North', 'South', 'East', 'West'] }
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => FieldDetailsDto)
  @Expose()
  fieldDetails: FieldDetailsDto;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<WarehouseCustomFieldDetailResponseDto>) {
    super(partial);
    Object.assign(this, partial);
  }
}

/**
 * DTO cho response giá trị trường tùy chỉnh
 */
export class CustomFieldValueResponseDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 3
  })
  @IsNumber()
  @Expose()
  fieldId: number;

  @ApiProperty({
    description: 'Nhãn của trường tùy chỉnh',
    example: 'Khu vực'
  })
  @IsString()
  @Expose()
  label: string;

  @ApiProperty({
    description: 'Loại trường tùy chỉnh',
    example: 'TEXT'
  })
  @IsString()
  @Expose()
  type: string;

  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    example: { value: 'North' }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => CustomFieldValueDto)
  @Expose()
  value: CustomFieldValueDto;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<CustomFieldValueResponseDto>) {
    Object.assign(this, partial);
  }
}
