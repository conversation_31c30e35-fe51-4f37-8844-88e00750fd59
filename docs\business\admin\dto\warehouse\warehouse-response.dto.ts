import { ApiProperty } from '@nestjs/swagger';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Expose } from 'class-transformer';

/**
 * DTO cho response thông tin kho
 */
export class WarehouseResponseDto {
  @ApiProperty({
    description: 'ID của kho',
    example: 1
  })
  @IsNumber()
  @Expose()
  warehouseId: number;

  @ApiProperty({
    description: 'Tên kho',
    example: '<PERSON>ho chính'
  })
  @IsString()
  @Expose()
  name: string;

  @ApiProperty({
    description: '<PERSON>ô tả kho',
    example: '<PERSON>ho chứa hàng hóa chính của công ty',
    nullable: true
  })
  @IsOptional()
  @IsString()
  @Expose()
  description: string;

  @ApiProperty({
    description: 'Loại kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.PHYSICAL
  })
  @IsEnum(WarehouseTypeEnum)
  @Expose()
  type: WarehouseTypeEnum;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<WarehouseResponseDto>) {
    Object.assign(this, partial);
  }
}
