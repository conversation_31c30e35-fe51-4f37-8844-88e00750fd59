import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions/business.exception';

/**
 * Mã lỗi liên quan đến quản lý kho cho admin (30100-30150)
 */
export const ADMIN_WAREHOUSE_ERROR_CODES = {
  // Kế thừa các mã lỗi từ business exception
  ...BUSINESS_ERROR_CODES,

  /**
   * Lỗi khi không tìm thấy trường tùy chỉnh của kho
   */
  WAREHOUSE_CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    30100,
    'Không tìm thấy trường tùy chỉnh của kho',
    HttpStatus.NOT_FOUND
  ),

  /**
   * Lỗi khi không tìm thấy kho ảo
   */
  VIRTUAL_WAREHOUSE_NOT_FOUND: new ErrorCode(
    30101,
    'Không tìm thấy kho ảo',
    HttpStatus.NOT_FOUND
  ),

  /**
   * Lỗi khi không tìm thấy kho vật lý
   */
  PHYSICAL_WAREHOUSE_NOT_FOUND: new ErrorCode(
    30102,
    'Không tìm thấy kho vật lý',
    HttpStatus.NOT_FOUND
  ),

  /**
   * Lỗi khi loại kho không hợp lệ
   */
  INVALID_WAREHOUSE_TYPE: new ErrorCode(
    30103,
    'Loại kho không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  /**
   * Lỗi khi tìm kiếm trường tùy chỉnh của kho thất bại
   */
  WAREHOUSE_CUSTOM_FIELD_FIND_FAILED: new ErrorCode(
    30104,
    'Tìm kiếm trường tùy chỉnh của kho thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  /**
   * Lỗi khi tìm kiếm kho ảo thất bại
   */
  VIRTUAL_WAREHOUSE_FIND_FAILED: new ErrorCode(
    30105,
    'Tìm kiếm kho ảo thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  /**
   * Lỗi khi tìm kiếm kho vật lý thất bại
   */
  PHYSICAL_WAREHOUSE_FIND_FAILED: new ErrorCode(
    30106,
    'Tìm kiếm kho vật lý thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
};
