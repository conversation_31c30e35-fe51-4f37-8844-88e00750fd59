import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { FILE_ERROR_CODES } from '../exceptions/file.exception';
import { File } from '@modules/business/entities/file.entity';

/**
 * Helper xử lý validation cho File
 */
@Injectable()
export class FileValidationHelper {
  /**
   * Kiểm tra tệp tin có tồn tại không
   * @param file Tệp tin cần kiểm tra
   * @throws AppException nếu tệp tin không tồn tại
   */
  validateFileExists(file: File | null): asserts file is File {
    if (!file) {
      throw new AppException(
        FILE_ERROR_CODES.FILE_NOT_FOUND,
        'Tệp tin không tồn tại'
      );
    }
  }

  /**
   * Kiểm tra tệp tin có thuộc thư mục không
   * @param file Tệp tin cần kiểm tra
   * @param folderId ID thư mục
   * @throws AppException nếu tệp tin không thuộc thư mục
   */
  validateFileInFolder(file: File, folderId: number): void {
    if (file.folderId !== folderId) {
      throw new AppException(
        FILE_ERROR_CODES.FILE_UNAUTHORIZED,
        'Tệp tin không thuộc thư mục này'
      );
    }
  }
}
