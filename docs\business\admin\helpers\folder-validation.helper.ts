import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { FOLDER_ERROR_CODES } from '../exceptions/folder.exception';
import { Folder } from '@modules/business/entities/folder.entity';

/**
 * Helper xử lý validation cho Folder
 */
@Injectable()
export class FolderValidationHelper {
  /**
   * Kiểm tra thư mục có tồn tại không
   * @param folder Thư mục cần kiểm tra
   * @throws AppException nếu thư mục không tồn tại
   */
  validateFolderExists(folder: Folder | null): asserts folder is Folder {
    if (!folder) {
      throw new AppException(
        FOLDER_ERROR_CODES.FOLDER_NOT_FOUND,
        'Th<PERSON> mục không tồn tại'
      );
    }
  }

  /**
   * Kiểm tra thư mục có thuộc người dùng không
   * @param folder Th<PERSON> mục cần kiểm tra
   * @param userId ID người dùng
   * @throws AppException nếu thư mục không thuộc người dùng
   */
  validateFolderBelongsToUser(folder: Folder, userId: number): void {
    if (folder.userId !== userId) {
      throw new AppException(
        FOLDER_ERROR_CODES.FOLDER_UNAUTHORIZED,
        'Thư mục không thuộc người dùng này'
      );
    }
  }
}
