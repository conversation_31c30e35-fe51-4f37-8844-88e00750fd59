import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';

/**
 * Helper chứ<PERSON> các ví dụ cụ thể cho API error response của các controller warehouse
 */
export const WAREHOUSE_ERROR_EXAMPLES = {
  // Warehouse error examples
  WAREHOUSE_NOT_FOUND: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND.message,
    detail: {
      warehouseId: 999
    }
  },
  
  WAREHOUSE_FIND_FAILED: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_FIND_FAILED.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_FIND_FAILED.message,
    detail: {
      error: 'Lỗi kết nối cơ sở dữ liệu'
    }
  },
  
  // Physical warehouse error examples
  PHYSICAL_WAREHOUSE_NOT_FOUND: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_NOT_FOUND.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_NOT_FOUND.message,
    detail: {
      warehouseId: 999
    }
  },
  
  PHYSICAL_WAREHOUSE_FIND_FAILED: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_FIND_FAILED.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_FIND_FAILED.message,
    detail: {
      error: 'Lỗi kết nối cơ sở dữ liệu'
    }
  },
  
  // Virtual warehouse error examples
  VIRTUAL_WAREHOUSE_NOT_FOUND: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_NOT_FOUND.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_NOT_FOUND.message,
    detail: {
      warehouseId: 999
    }
  },
  
  VIRTUAL_WAREHOUSE_FIND_FAILED: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_FIND_FAILED.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_FIND_FAILED.message,
    detail: {
      error: 'Lỗi kết nối cơ sở dữ liệu'
    }
  },
  
  // Warehouse custom field error examples
  WAREHOUSE_CUSTOM_FIELD_NOT_FOUND: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND.message,
    detail: {
      warehouseId: 1,
      fieldId: 999
    }
  },
  
  WAREHOUSE_CUSTOM_FIELD_FIND_FAILED: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED.message,
    detail: {
      error: 'Lỗi kết nối cơ sở dữ liệu'
    }
  },
  
  // Other warehouse error examples
  INVALID_WAREHOUSE_TYPE: {
    code: ADMIN_WAREHOUSE_ERROR_CODES.INVALID_WAREHOUSE_TYPE.code,
    message: ADMIN_WAREHOUSE_ERROR_CODES.INVALID_WAREHOUSE_TYPE.message,
    detail: {
      type: 'UNKNOWN',
      validTypes: ['PHYSICAL', 'VIRTUAL']
    }
  }
};
