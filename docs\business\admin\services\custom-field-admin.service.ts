import { Injectable, Logger } from '@nestjs/common';
import { CustomFieldRepository } from '../../repositories';
import { QueryCustomFieldDto } from '../dto/customfields/query-custom-field.dto';
import { CreateCustomFieldDto } from '../dto/customfields/create-custom-field.dto';
import { UpdateCustomFieldDto } from '../dto/customfields/update-custom-field.dto';
import { CustomFieldResponseDto, CustomFieldStatus } from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { ValidationHelper } from '../helpers/validation.helper';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '../exceptions/business-admin.exception';
import { Transactional } from 'typeorm-transactional';
import { CustomField } from '@modules/business/entities';
import { BatchDeleteCustomFieldDto, BatchDeleteCustomFieldResponseDto } from '../dto';

/**
 * Service xử lý nghiệp vụ liên quan đến trường tùy chỉnh cho admin
 */
@Injectable()
export class CustomFieldAdminService {
  private readonly logger = new Logger(CustomFieldAdminService.name);

  constructor(
    private readonly customFieldAdminRepository: CustomFieldRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách trường tùy chỉnh với phân trang và lọc
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách trường tùy chỉnh phân trang
   */
  async getCustomFields(
    employeeId: number,
    queryDto: QueryCustomFieldDto,
  ): Promise<PaginatedResult<CustomFieldResponseDto>> {
    this.logger.log(`Bắt đầu lấy danh sách trường tùy chỉnh`, `method: ${this.getCustomFields.name}`);
    this.logger.log(`Nhân viên ${employeeId} đang lấy danh sách trường tùy chỉnh với query: ${JSON.stringify(queryDto)}`);

    try {
      // Lấy danh sách trường tùy chỉnh từ repository
      this.logger.log(`Đang truy vấn danh sách trường tùy chỉnh từ database`);
      const customFieldsResult = await this.customFieldAdminRepository.findCustomFields(queryDto);
      this.logger.log(`Đã tìm thấy ${customFieldsResult.items.length} trường tùy chỉnh`);

      // Chuyển đổi từ entity sang DTO
      this.logger.log(`Đang chuyển đổi dữ liệu sang DTO`);
      const items = customFieldsResult.items.map(customField => this.mapToCustomFieldResponseDto(customField));

      this.logger.log(`Hoàn tất lấy danh sách trường tùy chỉnh`);
      return {
        items,
        meta: customFieldsResult.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách trường tùy chỉnh: ${error.message}`, error.stack, `method: ${this.getCustomFields.name}`);
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_FETCH_ERROR,
        'Lỗi khi lấy danh sách trường tùy chỉnh',
      );
    }
  }

  /**
   * Tạo trường tùy chỉnh mới
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param createDto DTO chứa thông tin trường tùy chỉnh mới
   * @returns Trường tùy chỉnh đã tạo
   */
  @Transactional()
  async createCustomField(
    employeeId: number,
    createDto: CreateCustomFieldDto,
  ): Promise<CustomFieldResponseDto> {
    this.logger.log(`Bắt đầu tạo trường tùy chỉnh mới`, `method: ${this.createCustomField.name}`);
    this.logger.log(`Nhân viên ${employeeId} đang tạo trường tùy chỉnh mới: ${JSON.stringify(createDto)}`);

    try {
      // Validate dữ liệu đầu vào
      this.logger.log(`Đang kiểm tra dữ liệu đầu vào`);
      this.validationHelper.validateCustomFieldData(
        createDto.configId,
        createDto.label,
        createDto.type,
      );

      // Kiểm tra configId đã tồn tại chưa
      this.logger.log(`Đang kiểm tra configId ${createDto.configId} đã tồn tại chưa`);
      const existingField = await this.customFieldAdminRepository.findCustomFieldByConfigId(createDto.configId);

      if (existingField) {
        this.logger.warn(`ConfigId ${createDto.configId} đã tồn tại`);
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.CONFIG_ID_DUPLICATE,
          'ConfigId đã tồn tại',
        );
      }

      // Tạo trường tùy chỉnh mới
      this.logger.log(`Đang tạo trường tùy chỉnh mới`);
      const newCustomField = this.customFieldAdminRepository.create({
        configId: createDto.configId,
        label: createDto.label,
        type: createDto.type,
        required: createDto.required,
        configJson: createDto.configJson,
        employeeId: employeeId,
        createAt: Date.now(),
      });

      // Lưu trường tùy chỉnh
      this.logger.log(`Đang lưu trường tùy chỉnh vào database`);
      const savedCustomField = await this.customFieldAdminRepository.save(newCustomField);
      this.logger.log(`Đã lưu trường tùy chỉnh với ID: ${savedCustomField.id}`);

      // Chuyển đổi từ entity sang DTO
      this.logger.log(`Đang chuyển đổi dữ liệu sang DTO`);
      return this.mapToCustomFieldResponseDto(savedCustomField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo trường tùy chỉnh: ${error.message}`, error.stack, `method: ${this.createCustomField.name}`);
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_CREATION_ERROR,
        'Lỗi khi tạo trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy chi tiết trường tùy chỉnh theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param fieldId ID của trường tùy chỉnh
   * @returns Chi tiết trường tùy chỉnh
   */
  async getCustomFieldById(
    employeeId: number,
    fieldId: number,
  ): Promise<CustomFieldResponseDto> {
    this.logger.log(`Bắt đầu lấy chi tiết trường tùy chỉnh`, `method: ${this.getCustomFieldById.name}`);
    this.logger.log(`Nhân viên ${employeeId} đang lấy chi tiết trường tùy chỉnh với ID: ${fieldId}`);

    try {
      // Lấy thông tin trường tùy chỉnh
      const customField = await this.customFieldAdminRepository.findCustomFieldById(fieldId);
      this.validationHelper.validateCustomFieldExists(customField);

      // Đảm bảo customField không phải là null sau khi validate
      const validCustomField = customField as CustomField;
      this.logger.log(`Đã tìm thấy trường tùy chỉnh: ${validCustomField.label}`);

      // Chuyển đổi từ entity sang DTO
      return this.mapToCustomFieldResponseDto(validCustomField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy chi tiết trường tùy chỉnh: ${error.message}`, error.stack, `method: ${this.getCustomFieldById.name}`);
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_FETCH_ERROR,
        'Lỗi khi lấy chi tiết trường tùy chỉnh',
      );
    }
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param fieldId ID của trường tùy chỉnh
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async updateCustomField(
    employeeId: number,
    fieldId: number,
    updateDto: UpdateCustomFieldDto,
  ): Promise<CustomFieldResponseDto> {
    this.logger.log(`Bắt đầu cập nhật trường tùy chỉnh`, `method: ${this.updateCustomField.name}`);
    this.logger.log(`Nhân viên ${employeeId} đang cập nhật trường tùy chỉnh ${fieldId}: ${JSON.stringify(updateDto)}`);

    try {
      // Kiểm tra trường tùy chỉnh tồn tại
      const customField = await this.customFieldAdminRepository.findCustomFieldById(fieldId);
      this.validationHelper.validateCustomFieldExists(customField);

      // Đảm bảo customField không phải là null sau khi validate
      // Vì validateCustomFieldExists sẽ throw exception nếu customField là null
      const validCustomField = customField as CustomField;

      // Kiểm tra configId nếu được cập nhật
      if (updateDto.configId && updateDto.configId !== validCustomField.configId) {
        const existingField = await this.customFieldAdminRepository.findCustomFieldByConfigId(updateDto.configId);

        if (existingField && existingField.id !== fieldId) {
          throw new AppException(
            BUSINESS_ADMIN_ERROR_CODES.CONFIG_ID_DUPLICATE,
            'ConfigId đã tồn tại',
          );
        }
      }

      // Cập nhật trường tùy chỉnh
      const updatedData = {
        configId: updateDto.configId || validCustomField.configId,
        label: updateDto.label || validCustomField.label,
        type: updateDto.type || validCustomField.type,
        required: updateDto.required !== undefined ? updateDto.required : validCustomField.required,
        configJson: updateDto.configJson || validCustomField.configJson,
      };

      // Lưu trường tùy chỉnh
      const updatedCustomField = await this.customFieldAdminRepository.save({
        ...validCustomField,
        ...updatedData
      });

      // Chuyển đổi từ entity sang DTO
      return this.mapToCustomFieldResponseDto(updatedCustomField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật trường tùy chỉnh: ${error.message}`, error.stack, `method: ${this.updateCustomField.name}`);
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_UPDATE_ERROR,
        'Lỗi khi cập nhật trường tùy chỉnh',
      );
    }
  }

  /**
   * Xóa nhiều trường tùy chỉnh cùng lúc (soft delete)
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param batchDeleteDto DTO chứa danh sách ID cần xóa
   * @returns Kết quả xóa nhiều trường tùy chỉnh
   */
  @Transactional()
  async batchDeleteCustomFields(
    employeeId: number,
    batchDeleteDto: BatchDeleteCustomFieldDto,
  ): Promise<BatchDeleteCustomFieldResponseDto> {
    this.logger.log(`Bắt đầu xóa nhiều trường tùy chỉnh`, `method: ${this.batchDeleteCustomFields.name}`);
    this.logger.log(`Nhân viên ${employeeId} đang xóa ${batchDeleteDto.ids.length} trường tùy chỉnh`);

    try {
      const { ids } = batchDeleteDto;
      const results: Array<{ id: number; success: boolean; message: string }> = [];
      let successCount = 0;
      let failureCount = 0;

      // Xử lý từng ID
      for (const id of ids) {
        try {
          this.logger.log(`Đang xử lý xóa trường tùy chỉnh với ID: ${id}`);

          // Tìm trường tùy chỉnh theo ID
          const customField = await this.customFieldAdminRepository.findCustomFieldById(id);

          if (!customField) {
            this.logger.warn(`Không tìm thấy trường tùy chỉnh với ID: ${id}`);
            results.push({
              id,
              success: false,
              message: 'Không tìm thấy trường tùy chỉnh',
            });
            failureCount++;
            continue;
          }

          // Kiểm tra trường tùy chỉnh đã bị xóa chưa
          if (customField.status === CustomFieldStatus.DELETED) {
            this.logger.warn(`Trường tùy chỉnh với ID ${id} đã bị xóa trước đó`);
            results.push({
              id,
              success: false,
              message: 'Trường tùy chỉnh đã bị xóa',
            });
            failureCount++;
            continue;
          }

          // Thực hiện soft delete
          customField.status = CustomFieldStatus.DELETED;
          await this.customFieldAdminRepository.save(customField);

          this.logger.log(`Đã xóa thành công trường tùy chỉnh với ID: ${id}`);
          results.push({
            id,
            success: true,
            message: 'Xóa thành công',
          });
          successCount++;

        } catch (error) {
          this.logger.error(`Lỗi khi xóa trường tùy chỉnh với ID ${id}: ${error.message}`, error.stack);
          results.push({
            id,
            success: false,
            message: `Lỗi khi xóa: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BatchDeleteCustomFieldResponseDto = {
        successCount,
        failureCount,
        totalCount: ids.length,
        results,
      };

      this.logger.log(`Hoàn tất xóa nhiều trường tùy chỉnh. Thành công: ${successCount}, Thất bại: ${failureCount}`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhiều trường tùy chỉnh: ${error.message}`, error.stack, `method: ${this.batchDeleteCustomFields.name}`);
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_BATCH_DELETE_ERROR,
        'Lỗi khi xóa nhiều trường tùy chỉnh',
      );
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param customField Entity trường tùy chỉnh
   * @returns DTO trường tùy chỉnh
   */
  private mapToCustomFieldResponseDto(customField: CustomField): CustomFieldResponseDto {
    this.logger.log(`Đang chuyển đổi trường tùy chỉnh ${customField.id} sang DTO`, `method: ${this.mapToCustomFieldResponseDto.name}`);
    return {
      id: customField.id,
      configId: customField.configId,
      label: customField.label,
      type: customField.type,
      required: customField.required,
      configJson: customField.configJson,
      employeeId: customField.employeeId,
      userId: customField.userId,
      createAt: customField.createAt,
      status: customField.status as unknown as CustomFieldStatus,
    };
  }
}
