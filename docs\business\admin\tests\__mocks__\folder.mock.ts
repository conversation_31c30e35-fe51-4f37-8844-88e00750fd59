/**
 * Mock data cho Folder
 */
export const mockFolder = {
  id: 1,
  name: 'Documents',
  parentId: null,
  userId: 1,
  path: '/Documents',
  root: null,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho danh sách Folder
 */
export const mockFolders = [
  mockFolder,
  {
    id: 2,
    name: 'Images',
    parentId: null,
    userId: 1,
    path: '/Images',
    root: null,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  },
  {
    id: 3,
    name: 'Reports',
    parentId: 1,
    userId: 1,
    path: '/Documents/Reports',
    root: null,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  },
];

/**
 * Mock data cho FolderResponseDto
 */
export const mockFolderResponseDto = {
  id: 1,
  name: 'Documents',
  parentId: null,
  userId: 1,
  path: '/Documents',
  root: null,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho FolderDetailResponseDto
 */
export const mockFolderDetailResponseDto = {
  id: 1,
  name: 'Documents',
  parent: null,
  owner: {
    id: 1,
    name: 'User 1',
  },
  path: '/Documents',
  root: null,
  fileCount: 2,
  files: [
    {
      id: 1,
      name: 'test-file.pdf',
      folderId: 1,
      size: 1024000,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    },
    {
      id: 2,
      name: 'document.docx',
      folderId: 1,
      size: 512000,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    },
  ],
  createdAt: 1625097600000,
  formattedCreatedAt: '01/07/2021 00:00:00',
  updatedAt: 1625097600000,
  formattedUpdatedAt: '01/07/2021 00:00:00',
};

/**
 * Mock data cho FolderDetailResponseDto với thư mục cha
 */
export const mockSubFolderDetailResponseDto = {
  id: 3,
  name: 'Reports',
  parent: {
    id: 1,
    name: 'Documents',
    path: '/Documents',
  },
  owner: {
    id: 1,
    name: 'User 1',
  },
  path: '/Documents/Reports',
  root: null,
  fileCount: 1,
  files: [
    {
      id: 4,
      name: 'report.pdf',
      folderId: 3,
      size: 1024000,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    },
  ],
  createdAt: 1625097600000,
  formattedCreatedAt: '01/07/2021 00:00:00',
  updatedAt: 1625097600000,
  formattedUpdatedAt: '01/07/2021 00:00:00',
};

/**
 * Mock data cho PaginatedResult<FolderResponseDto>
 */
export const mockFoldersPaginatedResult = {
  items: [
    mockFolderResponseDto,
    {
      id: 2,
      name: 'Images',
      parentId: null,
      userId: 1,
      path: '/Images',
      root: null,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    },
  ],
  meta: {
    currentPage: 1,
    itemsPerPage: 10,
    itemCount: 2,
    totalItems: 2,
    totalPages: 1,
  },
};
