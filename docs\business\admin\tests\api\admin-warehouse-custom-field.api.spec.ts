import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { AdminWarehouseCustomFieldService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { WarehouseCustomFieldResponseDto, WarehouseCustomFieldDetailResponseDto } from '../../dto/warehouse';
import { AdminWarehouseCustomFieldController } from '../../controllers';

describe('AdminWarehouseCustomFieldController (e2e)', () => {
  let app: INestApplication;
  let adminWarehouseCustomFieldService: AdminWarehouseCustomFieldService;

  const mockAdminWarehouseCustomFieldService = {
    findAll: jest.fn() as jest.Mock,
    findOne: jest.fn() as jest.Mock,
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AdminWarehouseCustomFieldController],
      providers: [
        {
          provide: AdminWarehouseCustomFieldService,
          useValue: mockAdminWarehouseCustomFieldService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    adminWarehouseCustomFieldService = moduleFixture.get<AdminWarehouseCustomFieldService>(AdminWarehouseCustomFieldService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/warehouse-custom-fields', () => {
    it('nên trả về danh sách trường tùy chỉnh của kho phân trang', async () => {
      // Arrange
      const mockCustomFields: WarehouseCustomFieldResponseDto[] = [
        {
          warehouseId: 1,
          fieldId: 3,
          value: {
            value: 'North'
          },
          warehouseName: 'Kho chính',
          fieldLabel: 'Khu vực'
        },
        {
          warehouseId: 1,
          fieldId: 4,
          value: {
            value: true
          },
          warehouseName: 'Kho chính',
          fieldLabel: 'Có máy lạnh'
        }
      ];

      const mockPaginatedResult: PaginatedResult<WarehouseCustomFieldResponseDto> = {
        items: mockCustomFields,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 2,
          totalItems: 2,
          totalPages: 1,
        },
      };

      mockAdminWarehouseCustomFieldService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/warehouse-custom-fields')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách trường tùy chỉnh của kho thành công');
          expect(res.body.result.items).toHaveLength(2);
          expect(res.body.result.items[0].warehouseId).toBe(1);
          expect(res.body.result.items[0].fieldId).toBe(3);
          // Không kiểm tra thuộc tính type vì nó không tồn tại trong WarehouseCustomFieldResponseDto
          expect(res.body.result.items[0].value.value).toBe('North');
          expect(res.body.result.items[0].warehouseName).toBe('Kho chính');
          expect(res.body.result.meta.currentPage).toBe(1);
          expect(res.body.result.meta.totalItems).toBe(2);
        });
    });

    it('nên truyền các tham số truy vấn đúng cho service', async () => {
      // Arrange
      const mockPaginatedResult: PaginatedResult<WarehouseCustomFieldResponseDto> = {
        items: [],
        meta: {
          currentPage: 2,
          itemsPerPage: 5,
          itemCount: 0,
          totalItems: 0,
          totalPages: 0,
        },
      };

      mockAdminWarehouseCustomFieldService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/warehouse-custom-fields')
        .query({
          page: 2,
          limit: 5,
          warehouseId: 1,
          fieldId: 3
        })
        .expect(200)
        .expect(() => {
          const findAllMock = adminWarehouseCustomFieldService.findAll as jest.Mock;
          const lastCall = findAllMock.mock.calls[findAllMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            warehouseId: '1', // Query params are strings
            fieldId: '3' // Query params are strings
          }));
        });
    });
  });

  describe('GET /admin/warehouse-custom-fields/warehouses/:warehouseId/fields/:fieldId', () => {
    it('nên trả về thông tin chi tiết trường tùy chỉnh của kho theo ID kho và ID trường', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 3;

      const mockFieldDetails = {
        label: 'Khu vực',
        type: 'TEXT',
        required: true,
        configJson: {
          options: ['North', 'South', 'East', 'West']
        }
      };

      const mockCustomFieldDetail: WarehouseCustomFieldDetailResponseDto = {
        warehouseId: 1,
        fieldId: 3,
        value: {
          value: 'North'
        },
        warehouseName: 'Kho chính',
        fieldLabel: 'Khu vực',
        fieldDetails: mockFieldDetails
      };

      mockAdminWarehouseCustomFieldService.findOne.mockResolvedValue(mockCustomFieldDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/warehouse-custom-fields/warehouses/${warehouseId}/fields/${fieldId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy thông tin chi tiết trường tùy chỉnh của kho thành công');
          expect(res.body.result.warehouseId).toBe(1);
          expect(res.body.result.fieldId).toBe(3);
          // Không kiểm tra thuộc tính type vì nó không tồn tại trong WarehouseCustomFieldDetailResponseDto
          expect(res.body.result.value.value).toBe('North');
          expect(res.body.result.warehouseName).toBe('Kho chính');
          expect(res.body.result.fieldLabel).toBe('Khu vực');
          expect(res.body.result.fieldDetails).toBeDefined();
          expect(res.body.result.fieldDetails.label).toBe('Khu vực');
          expect(res.body.result.fieldDetails.type).toBe('TEXT');
          expect(res.body.result.fieldDetails.required).toBe(true);
          expect(res.body.result.fieldDetails.configJson.options).toEqual(['North', 'South', 'East', 'West']);
        });
    });

    it('nên truyền ID kho và ID trường đúng cho service', async () => {
      // Arrange
      const warehouseId = 123;
      const fieldId = 456;

      const mockFieldDetails = {
        label: 'Khu vực',
        type: 'TEXT',
        required: true,
        configJson: {
          options: ['North', 'South', 'East', 'West']
        }
      };

      const mockCustomFieldDetail: WarehouseCustomFieldDetailResponseDto = {
        warehouseId: warehouseId,
        fieldId: fieldId,
        value: {
          value: 'North'
        },
        warehouseName: 'Kho chính',
        fieldLabel: 'Khu vực',
        fieldDetails: mockFieldDetails
      };

      mockAdminWarehouseCustomFieldService.findOne.mockResolvedValue(mockCustomFieldDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/warehouse-custom-fields/warehouses/${warehouseId}/fields/${fieldId}`)
        .expect(200)
        .expect(() => {
          const findOneMock = adminWarehouseCustomFieldService.findOne as jest.Mock;
          expect(findOneMock).toHaveBeenCalledWith(expect.any(String), expect.any(String));
          // Kiểm tra xem tham số được truyền vào có chứa giá trị warehouseId và fieldId không
          const lastCall = findOneMock.mock.calls[findOneMock.mock.calls.length - 1];
          expect(lastCall[0]).toBe(String(warehouseId));
          expect(lastCall[1]).toBe(String(fieldId));
        });
    });
  });
});
