import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { CustomFieldAdminService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { CustomFieldResponseDto, CustomFieldStatus } from '../../dto';
import { CreateCustomFieldDto } from '../../dto';
import { UpdateCustomFieldDto } from '../../dto';
import { CustomFieldAdminController } from '../../controllers';

describe('CustomFieldAdminController (e2e)', () => {
  let app: INestApplication;
  let customFieldAdminService: CustomFieldAdminService;

  const mockCustomFieldAdminService = {
    getCustomFields: jest.fn() as jest.Mock,
    getCustomFieldById: jest.fn() as jest.Mock,
    createCustomField: jest.fn() as jest.Mock,
    updateCustomField: jest.fn() as jest.Mock,
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [CustomFieldAdminController],
      providers: [
        {
          provide: CustomFieldAdminService,
          useValue: mockCustomFieldAdminService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    customFieldAdminService = moduleFixture.get<CustomFieldAdminService>(CustomFieldAdminService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/custom-fields', () => {
    it('nên trả về danh sách trường tùy chỉnh phân trang', async () => {
      // Arrange
      const mockCustomFields: CustomFieldResponseDto[] = [
        {
          id: 1,
          component: 'input',
          configId: 'product_color',
          label: 'Màu sắc',
          type: 'text',
          required: true,
          configJson: {
            placeholder: 'Nhập màu sắc',
            maxLength: 50,
          },
          employeeId: 1,
          userId: null,
          createAt: 1625097600000,
          status: CustomFieldStatus.APPROVED,
        },
      ];

      const mockPaginatedResult: PaginatedResult<CustomFieldResponseDto> = {
        items: mockCustomFields,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 1,
          totalItems: 1,
          totalPages: 1,
        },
      };

      mockCustomFieldAdminService.getCustomFields.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/custom-fields')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách trường tùy chỉnh thành công');
          expect(res.body.result.items).toHaveLength(1);
          expect(res.body.result.items[0].id).toBe(1);
          expect(res.body.result.items[0].component).toBe('input');
          expect(res.body.result.items[0].label).toBe('Màu sắc');
          expect(res.body.result.meta.currentPage).toBe(1);
          expect(res.body.result.meta.totalItems).toBe(1);
        });
    });

    it('nên truyền các tham số truy vấn đúng cho service', async () => {
      // Arrange
      const mockPaginatedResult: PaginatedResult<CustomFieldResponseDto> = {
        items: [],
        meta: {
          currentPage: 2,
          itemsPerPage: 5,
          itemCount: 0,
          totalItems: 0,
          totalPages: 0,
        },
      };

      mockCustomFieldAdminService.getCustomFields.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/custom-fields')
        .query({
          page: 2,
          limit: 5,
          search: 'color',
          type: 'text',
          component: 'input',
          status: CustomFieldStatus.APPROVED,
        })
        .expect(200)
        .expect(() => {
          // Kiểm tra lần gọi cuối cùng
          const getCustomFieldsMock = customFieldAdminService.getCustomFields as jest.Mock;
          const lastCall = getCustomFieldsMock.mock.calls[getCustomFieldsMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.any(Number));
          expect(lastCall[1]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            search: 'color',
            type: 'text',
            component: 'input',
            status: CustomFieldStatus.APPROVED,
          }));
        });
    });
  });

  describe('POST /admin/custom-fields', () => {
    it('nên tạo trường tùy chỉnh mới thành công', async () => {
      // Arrange
      const createDto: CreateCustomFieldDto = {
        component: 'input',
        configId: 'product_color',
        label: 'Màu sắc',
        type: 'text',
        required: true,
        configJson: {
          placeholder: 'Nhập màu sắc',
          maxLength: 50,
        },
      };

      const mockCreatedCustomField: CustomFieldResponseDto = {
        id: 1,
        component: 'input',
        configId: 'product_color',
        label: 'Màu sắc',
        type: 'text',
        required: true,
        configJson: {
          placeholder: 'Nhập màu sắc',
          maxLength: 50,
        },
        employeeId: 1,
        userId: null,
        createAt: 1625097600000,
        status: CustomFieldStatus.APPROVED,
      };

      mockCustomFieldAdminService.createCustomField.mockResolvedValue(mockCreatedCustomField);

      // Act & Assert
      return request(app.getHttpServer())
        .post('/admin/custom-fields')
        .send(createDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.code).toBe(201);
          expect(res.body.message).toBe('Tạo trường tùy chỉnh thành công');
          expect(res.body.result.id).toBe(1);
          expect(res.body.result.component).toBe('input');
          expect(res.body.result.configId).toBe('product_color');
          expect(res.body.result.label).toBe('Màu sắc');
        });
    });

    it('nên truyền dữ liệu tạo mới đúng cho service', async () => {
      // Arrange
      const createDto: CreateCustomFieldDto = {
        component: 'select',
        configId: 'product_category',
        label: 'Danh mục',
        type: 'select',
        required: true,
        configJson: {
          placeholder: 'Chọn danh mục',
          options: [
            { label: 'Điện thoại', value: 'phone' },
            { label: 'Máy tính', value: 'computer' },
          ],
        },
      };

      const mockCreatedCustomField: CustomFieldResponseDto = {
        id: 2,
        component: 'select',
        configId: 'product_category',
        label: 'Danh mục',
        type: 'select',
        required: true,
        configJson: {
          placeholder: 'Chọn danh mục',
          options: [
            { label: 'Điện thoại', value: 'phone' },
            { label: 'Máy tính', value: 'computer' },
          ],
        },
        employeeId: 1,
        userId: null,
        createAt: 1625097600000,
        status: CustomFieldStatus.APPROVED,
      };

      mockCustomFieldAdminService.createCustomField.mockResolvedValue(mockCreatedCustomField);

      // Act & Assert
      return request(app.getHttpServer())
        .post('/admin/custom-fields')
        .send(createDto)
        .expect(201)
        .expect(() => {
          const createCustomFieldMock = customFieldAdminService.createCustomField as jest.Mock;
          expect(createCustomFieldMock).toHaveBeenCalledWith(
            expect.any(Number),
            expect.objectContaining({
              component: 'select',
              configId: 'product_category',
              label: 'Danh mục',
              type: 'select',
              required: true,
              configJson: {
                placeholder: 'Chọn danh mục',
                options: [
                  { label: 'Điện thoại', value: 'phone' },
                  { label: 'Máy tính', value: 'computer' },
                ],
              },
            }),
          );
        });
    });
  });

  describe('GET /admin/custom-fields/:id', () => {
    it('nên trả về thông tin chi tiết trường tùy chỉnh theo ID', async () => {
      // Arrange
      const fieldId = 1;
      const mockCustomField: CustomFieldResponseDto = {
        id: fieldId,
        component: 'input',
        configId: 'product_color',
        label: 'Màu sắc',
        type: 'text',
        required: true,
        configJson: {
          placeholder: 'Nhập màu sắc',
          maxLength: 50,
        },
        employeeId: 1,
        userId: null,
        createAt: 1625097600000,
        status: CustomFieldStatus.APPROVED,
      };

      mockCustomFieldAdminService.getCustomFieldById.mockResolvedValue(mockCustomField);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/custom-fields/${fieldId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy thông tin chi tiết trường tùy chỉnh thành công');
          expect(res.body.result.id).toBe(fieldId);
          expect(res.body.result.component).toBe('input');
          expect(res.body.result.configId).toBe('product_color');
          expect(res.body.result.label).toBe('Màu sắc');
          expect(res.body.result.type).toBe('text');
          expect(res.body.result.required).toBe(true);
          expect(res.body.result.configJson).toEqual({
            placeholder: 'Nhập màu sắc',
            maxLength: 50,
          });
          expect(res.body.result.employeeId).toBe(1);
          expect(res.body.result.createAt).toBe(1625097600000);
        });
    });

    it('nên truyền ID trường tùy chỉnh đúng cho service', async () => {
      // Arrange
      const fieldId = 2;
      const mockCustomField: CustomFieldResponseDto = {
        id: fieldId,
        component: 'select',
        configId: 'product_category',
        label: 'Danh mục',
        type: 'select',
        required: true,
        configJson: {
          placeholder: 'Chọn danh mục',
          options: [
            { label: 'Điện thoại', value: 'phone' },
            { label: 'Máy tính', value: 'computer' },
          ],
        },
        employeeId: 1,
        userId: null,
        createAt: 1625097600000,
        status: CustomFieldStatus.APPROVED,
      };

      mockCustomFieldAdminService.getCustomFieldById.mockResolvedValue(mockCustomField);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/custom-fields/${fieldId}`)
        .expect(200)
        .expect(() => {
          const getCustomFieldByIdMock = customFieldAdminService.getCustomFieldById as jest.Mock;
          expect(getCustomFieldByIdMock).toHaveBeenCalledWith(
            expect.any(Number),
            fieldId,
          );
        });
    });
  });

  describe('PUT /admin/custom-fields/:id', () => {
    it('nên cập nhật trường tùy chỉnh thành công', async () => {
      // Arrange
      const fieldId = 1;
      const updateDto: UpdateCustomFieldDto = {
        label: 'Màu sắc sản phẩm',
        required: false,
      };

      const mockUpdatedCustomField: CustomFieldResponseDto = {
        id: fieldId,
        component: 'input',
        configId: 'product_color',
        label: 'Màu sắc sản phẩm',
        type: 'text',
        required: false,
        configJson: {
          placeholder: 'Nhập màu sắc',
          maxLength: 50,
        },
        employeeId: 1,
        userId: null,
        createAt: 1625097600000,
        status: CustomFieldStatus.APPROVED,
      };

      mockCustomFieldAdminService.updateCustomField.mockResolvedValue(mockUpdatedCustomField);

      // Act & Assert
      return request(app.getHttpServer())
        .put(`/admin/custom-fields/${fieldId}`)
        .send(updateDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Cập nhật trường tùy chỉnh thành công');
          expect(res.body.result.id).toBe(fieldId);
          expect(res.body.result.label).toBe('Màu sắc sản phẩm');
          expect(res.body.result.required).toBe(false);
        });
    });

    it('nên truyền dữ liệu cập nhật đúng cho service', async () => {
      // Arrange
      const fieldId = 1;
      const updateDto: UpdateCustomFieldDto = {
        label: 'Màu sắc sản phẩm',
        required: false,
        configJson: {
          placeholder: 'Nhập màu sắc chính',
          maxLength: 100,
          description: 'Màu sắc chủ đạo của sản phẩm',
        },
      };

      const mockUpdatedCustomField: CustomFieldResponseDto = {
        id: fieldId,
        component: 'input',
        configId: 'product_color',
        label: 'Màu sắc sản phẩm',
        type: 'text',
        required: false,
        configJson: {
          placeholder: 'Nhập màu sắc chính',
          maxLength: 100,
          description: 'Màu sắc chủ đạo của sản phẩm',
        },
        employeeId: 1,
        userId: null,
        createAt: 1625097600000,
        status: CustomFieldStatus.APPROVED,
      };

      mockCustomFieldAdminService.updateCustomField.mockResolvedValue(mockUpdatedCustomField);

      // Act & Assert
      return request(app.getHttpServer())
        .put(`/admin/custom-fields/${fieldId}`)
        .send(updateDto)
        .expect(200)
        .expect(() => {
          const updateCustomFieldMock = customFieldAdminService.updateCustomField as jest.Mock;
          expect(updateCustomFieldMock).toHaveBeenCalledWith(
            expect.any(Number),
            fieldId,
            expect.objectContaining({
              label: 'Màu sắc sản phẩm',
              required: false,
              configJson: {
                placeholder: 'Nhập màu sắc chính',
                maxLength: 100,
                description: 'Màu sắc chủ đạo của sản phẩm',
              },
            }),
          );
        });
    });
  });
});
