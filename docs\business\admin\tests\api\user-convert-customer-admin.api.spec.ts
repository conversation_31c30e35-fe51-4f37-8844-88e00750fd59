import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { UserConvertCustomerAdminService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { UserConvertCustomerResponseDto, UserConvertCustomerDetailResponseDto } from '../../dto';

describe('UserConvertCustomerAdminController (e2e)', () => {
  let app: INestApplication;
  let userConvertCustomerAdminService: UserConvertCustomerAdminService;

  const mockUserConvertCustomerAdminService = {
    getUserConvertCustomers: jest.fn() as jest.Mock,
    getUserConvertCustomerById: jest.fn() as jest.Mock,
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [],
      providers: [
        {
          provide: UserConvertCustomerAdminService,
          useValue: mockUserConvertCustomerAdminService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    userConvertCustomerAdminService = moduleFixture.get<UserConvertCustomerAdminService>(UserConvertCustomerAdminService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    // Thiết lập mock routes
    app.use('/admin/business/user-convert-customers/:id', (req, res, next) => {
      if (req.method === 'GET' && req.params.id) {
        const id = parseInt(req.params.id, 10);
        mockUserConvertCustomerAdminService.getUserConvertCustomerById(req.employee.id, id)
          .then((result: UserConvertCustomerDetailResponseDto) => {
            res.status(200).json({
              code: 200,
              message: 'Lấy chi tiết khách hàng chuyển đổi thành công',
              result
            });
          })
          .catch((err: any) => {
            res.status(500).json({
              code: 500,
              message: err.message,
              errors: err.errors
            });
          });
      } else {
        next();
      }
    });

    app.use('/admin/business/user-convert-customers', (req, res, next) => {
      if (req.method === 'GET') {
        const queryParams = req.query;
        mockUserConvertCustomerAdminService.getUserConvertCustomers(req.employee.id, queryParams)
          .then((result: PaginatedResult<UserConvertCustomerResponseDto>) => {
            res.status(200).json({
              code: 200,
              message: 'Lấy danh sách khách hàng chuyển đổi thành công',
              result
            });
          })
          .catch((err: any) => {
            res.status(500).json({
              code: 500,
              message: err.message,
              errors: err.errors
            });
          });
      } else {
        next();
      }
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/business/user-convert-customers', () => {
    it('nên trả về danh sách khách hàng chuyển đổi phân trang', async () => {
      // Arrange
      const mockCustomers: UserConvertCustomerResponseDto[] = [
        {
          id: 1,
          avatar: 'avatars/customer-123.jpg',
          name: 'Nguyễn Văn A',
          email: { primary: '<EMAIL>' },
          phone: '0912345678',
          platform: 'Facebook',
          timezone: 'Asia/Ho_Chi_Minh',
          createdAt: 1625097600000,
          updatedAt: 1625184000000,
          userId: 3,
          agentId: '550e8400-e29b-41d4-a716-446655440000',
          metadata: [{ fieldName: 'address', fieldValue: 'Hà Nội' }]
        },
      ];

      const mockPaginatedResult: PaginatedResult<UserConvertCustomerResponseDto> = {
        items: mockCustomers,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 1,
          totalItems: 1,
          totalPages: 1,
        },
      };

      mockUserConvertCustomerAdminService.getUserConvertCustomers.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/business/user-convert-customers')
        .query({
          page: 2,
          limit: 5,
          search: 'Nguyễn',
          userId: 3,
          platform: 'Facebook',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách khách hàng chuyển đổi thành công');
          expect(res.body.result.items).toHaveLength(1);
          expect(res.body.result.items[0].id).toBe(1);
          expect(res.body.result.items[0].name).toBe('Nguyễn Văn A');

          const getUserConvertCustomersMock = userConvertCustomerAdminService.getUserConvertCustomers as jest.Mock;
          const lastCall = getUserConvertCustomersMock.mock.calls[getUserConvertCustomersMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.any(Number));
          expect(lastCall[1]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            search: 'Nguyễn',
            userId: '3', // Query params are strings
            platform: 'Facebook',
          }));
        });
    });
  });

  describe('GET /admin/business/user-convert-customers/:id', () => {
    it('nên trả về thông tin chi tiết khách hàng chuyển đổi theo ID', async () => {
      // Arrange
      const mockCustomerDetail: UserConvertCustomerDetailResponseDto = {
        id: 1,
        avatar: 'avatars/customer-123.jpg',
        name: 'Nguyễn Văn A',
        email: { primary: '<EMAIL>' },
        phone: '0912345678',
        platform: 'Facebook',
        timezone: 'Asia/Ho_Chi_Minh',
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        userId: 3,
        agentId: '550e8400-e29b-41d4-a716-446655440000',
        metadata: [{ fieldName: 'address', fieldValue: 'Hà Nội' }],
        converts: [
          {
            id: 1,
            convertCustomerId: 1,
            userId: 3,
            conversionType: 'online',
            source: 'website',
            notes: 'Khách hàng quan tâm đến sản phẩm X',
            content: { additionalInfo: 'Thông tin thêm về khách hàng' },
            createdAt: 1625097600000,
            updatedAt: 1625184000000
          }
        ]
      };

      mockUserConvertCustomerAdminService.getUserConvertCustomerById.mockResolvedValue(mockCustomerDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/business/user-convert-customers/1')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy chi tiết khách hàng chuyển đổi thành công');
          expect(res.body.result.id).toBe(1);
          expect(res.body.result.name).toBe('Nguyễn Văn A');
          expect(res.body.result.converts).toBeDefined();
          expect(res.body.result.converts).toHaveLength(1);
          expect(res.body.result.converts[0].id).toBe(1);
          expect(res.body.result.converts[0].conversionType).toBe('online');
        });
    });

    it('nên truyền ID khách hàng đúng cho service', async () => {
      // Arrange
      const customerId = 123;
      const mockCustomerDetail: UserConvertCustomerDetailResponseDto = {
        id: customerId,
        avatar: 'avatars/customer-123.jpg',
        name: 'Nguyễn Văn A',
        email: { primary: '<EMAIL>' },
        phone: '0912345678',
        platform: 'Facebook',
        timezone: 'Asia/Ho_Chi_Minh',
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        userId: 3,
        agentId: '550e8400-e29b-41d4-a716-446655440000',
        metadata: [],
        converts: []
      };

      mockUserConvertCustomerAdminService.getUserConvertCustomerById.mockResolvedValue(mockCustomerDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/business/user-convert-customers/${customerId}`)
        .expect(200)
        .expect(() => {
          const getUserConvertCustomerByIdMock = userConvertCustomerAdminService.getUserConvertCustomerById as jest.Mock;
          expect(getUserConvertCustomerByIdMock).toHaveBeenCalledWith(expect.any(Number), customerId);
        });
    });
  });
});
