import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateCustomFieldDto } from '../../dto/customfields/create-custom-field.dto';

describe('CreateCustomFieldDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 'input',
      configId: 'product_color',
      label: '<PERSON><PERSON>u sắc',
      type: 'text',
      required: true,
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
        description: '<PERSON><PERSON><PERSON> sắc chính của sản phẩm',
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu component', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      configId: 'product_color',
      label: '<PERSON><PERSON>u sắc',
      type: 'text',
      required: true,
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu configId', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 'input',
      label: 'Màu sắc',
      type: 'text',
      required: true,
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu label', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 'input',
      configId: 'product_color',
      type: 'text',
      required: true,
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu type', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 'input',
      configId: 'product_color',
      label: 'Màu sắc',
      required: true,
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu required', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 'input',
      configId: 'product_color',
      label: 'Màu sắc',
      type: 'text',
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu configJson', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 'input',
      configId: 'product_color',
      label: 'Màu sắc',
      type: 'text',
      required: true,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi component không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 123,
      configId: 'product_color',
      label: 'Màu sắc',
      type: 'text',
      required: true,
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi required không phải là boolean', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 'input',
      configId: 'product_color',
      label: 'Màu sắc',
      type: 'text',
      required: 'yes',
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isBoolean');
  });

  it('nên thất bại khi configJson không phải là object', async () => {
    // Arrange
    const dto = plainToInstance(CreateCustomFieldDto, {
      component: 'input',
      configId: 'product_color',
      label: 'Màu sắc',
      type: 'text',
      required: true,
      configJson: 'not an object',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isObject');
  });
});
