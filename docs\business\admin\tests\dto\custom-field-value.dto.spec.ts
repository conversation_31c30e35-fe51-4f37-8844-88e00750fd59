import { plainToInstance } from 'class-transformer';
import { CustomFieldValueDto } from '../../dto/customfields/custom-field-value.dto';

describe('CustomFieldValueDto', () => {
  it('nên chuyển đổi plain object thành instance của CustomFieldValueDto với giá trị chuỗi', () => {
    // Arrange
    const plainObject = {
      value: 'Đỏ',
    };

    // Act
    const dto = plainToInstance(CustomFieldValueDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldValueDto);
    expect(dto.value).toBe('Đỏ');
  });

  it('nên chuyển đổi plain object thành instance của CustomFieldValueDto với giá trị số', () => {
    // Arrange
    const plainObject = {
      value: 100,
    };

    // Act
    const dto = plainToInstance(CustomFieldValueDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldValueDto);
    expect(dto.value).toBe(100);
  });

  it('nên chuyển đổi plain object thành instance của CustomFieldValueDto với giá trị boolean', () => {
    // Arrange
    const plainObject = {
      value: true,
    };

    // Act
    const dto = plainToInstance(CustomFieldValueDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldValueDto);
    expect(dto.value).toBe(true);
  });

  it('nên chuyển đổi plain object thành instance của CustomFieldValueDto với giá trị mảng chuỗi', () => {
    // Arrange
    const plainObject = {
      value: ['Đỏ', 'Xanh', 'Vàng'],
    };

    // Act
    const dto = plainToInstance(CustomFieldValueDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldValueDto);
    expect(dto.value).toEqual(['Đỏ', 'Xanh', 'Vàng']);
  });

  it('nên chuyển đổi plain object thành instance của CustomFieldValueDto với giá trị null', () => {
    // Arrange
    const plainObject = {
      value: null,
    };

    // Act
    const dto = plainToInstance(CustomFieldValueDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldValueDto);
    expect(dto.value).toBeNull();
  });

  it('nên chuyển đổi plain object thành instance của CustomFieldValueDto với thuộc tính bổ sung', () => {
    // Arrange
    const plainObject = {
      value: 'Đỏ',
      additionalProperties: {
        displayColor: '#FF0000',
        sortOrder: 1,
      },
    };

    // Act
    const dto = plainToInstance(CustomFieldValueDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldValueDto);
    expect(dto.value).toBe('Đỏ');
    expect(dto.additionalProperties).toEqual({
      displayColor: '#FF0000',
      sortOrder: 1,
    });
  });
});
