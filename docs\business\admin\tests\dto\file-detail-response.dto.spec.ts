import { plainToInstance } from 'class-transformer';
import { FileDetailResponseDto, FolderInfoDto } from '../../dto/file/file-detail-response.dto';

describe('FileDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của FileDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'document.pdf',
      folder: {
        id: 5,
        name: 'Documents',
        path: '/Documents',
        root: 15
      },
      size: 1024000,
      storageKey: 'files/user/1/document.pdf',

      createdAt: 1620000000000,
      updatedAt: 1620000000000,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(FileDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FileDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('document.pdf');
    expect(dto.folder).toBeInstanceOf(FolderInfoDto);
    expect(dto.folder.id).toBe(5);
    expect(dto.folder.name).toBe('Documents');
    expect(dto.folder.path).toBe('/Documents');
    expect(dto.folder.root).toBe(15);
    expect(dto.size).toBe(1024000);
    expect(dto.storageKey).toBe('files/user/1/document.pdf');

    expect(dto.createdAt).toBe(1620000000000);
    expect(dto.updatedAt).toBe(1620000000000);
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của FileDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'document.pdf',
      folder: {
        id: 5,
        name: 'Documents',
        path: '/Documents',
        root: 15
      }
    };

    // Act
    const dto = plainToInstance(FileDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FileDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('document.pdf');
    expect(dto.folder).toBeInstanceOf(FolderInfoDto);
    expect(dto.folder.id).toBe(5);
    expect(dto.folder.name).toBe('Documents');
    expect(dto.folder.path).toBe('/Documents');
    expect(dto.folder.root).toBe(15);
    expect(dto.size).toBe(0);
    expect(dto.storageKey).toBeUndefined();

    expect(dto.createdAt).toBe(0);
    expect(dto.updatedAt).toBe(0);
  });
});
