import { plainToInstance } from 'class-transformer';
import { FileResponseDto } from '../../dto/file/file-response.dto';

describe('FileResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của FileResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'document.pdf',
      folderId: 5,
      size: 1024000,
      createdAt: 1620000000000,
      updatedAt: 1620000000000,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(FileResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FileResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('document.pdf');
    expect(dto.folderId).toBe(5);
    expect(dto.size).toBe(1024000);
    expect(dto.createdAt).toBe(1620000000000);
    expect(dto.updatedAt).toBe(1620000000000);
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của FileResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'document.pdf'
    };

    // Act
    const dto = plainToInstance(FileResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FileResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('document.pdf');
    expect(dto.folderId).toBeUndefined();
    expect(dto.size).toBe(0);
    expect(dto.createdAt).toBe(0);
    expect(dto.updatedAt).toBe(0);
  });

  it('nên chuyển đổi mảng plain object thành mảng instance của FileResponseDto', () => {
    // Arrange
    const plainArray = [
      {
        id: 1,
        name: 'document1.pdf',
        folderId: 5,
        size: 1024000,
        createdAt: 1620000000000,
        updatedAt: 1620000000000
      },
      {
        id: 2,
        name: 'document2.pdf',
        folderId: 5,
        size: 2048000,
        createdAt: 1620000000000,
        updatedAt: 1620000000000
      }
    ];

    // Act
    const dtos = plainToInstance(FileResponseDto, plainArray);

    // Assert
    expect(Array.isArray(dtos)).toBe(true);
    expect(dtos.length).toBe(2);
    expect(dtos[0]).toBeInstanceOf(FileResponseDto);
    expect(dtos[1]).toBeInstanceOf(FileResponseDto);
    expect(dtos[0].id).toBe(1);
    expect(dtos[1].id).toBe(2);
  });
});
