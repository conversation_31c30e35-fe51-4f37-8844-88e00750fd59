import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { FolderQueryDto } from '../../dto/folder/folder-query.dto';

describe('FolderQueryDto', () => {
  it('nên chuyển đổi plain object thành instance của FolderQueryDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10,
      parentId: 3,
      userId: 42,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(FolderQueryDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FolderQueryDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.parentId).toBe(3);
    expect(dto.userId).toBe(42);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của FolderQueryDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10
    };

    // Act
    const dto = plainToInstance(FolderQueryDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FolderQueryDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.parentId).toBeUndefined();
    expect(dto.userId).toBeUndefined();
  });

  it('nên validate thành công với dữ liệu hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(FolderQueryDto, {
      page: 1,
      limit: 10,
      parentId: 3,
      userId: 42
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên validate thất bại với parentId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(FolderQueryDto, {
      page: 1,
      limit: 10,
      parentId: 'not-a-number',
      userId: 42
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('parentId');
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên validate thất bại với userId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(FolderQueryDto, {
      page: 1,
      limit: 10,
      parentId: 3,
      userId: 'not-a-number'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('userId');
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });
});
