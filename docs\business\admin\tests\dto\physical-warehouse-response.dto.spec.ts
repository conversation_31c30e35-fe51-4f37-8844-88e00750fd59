import { plainToInstance } from 'class-transformer';
import { PhysicalWarehouseResponseDto } from '../../dto/warehouse/physical-warehouse-response.dto';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

describe('PhysicalWarehouseResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của PhysicalWarehouseResponseDto', () => {
    // Arrange
    const warehouseInfo = {
      warehouseId: 1,
      name: '<PERSON>ho chính',
      description: '<PERSON>ho chứa hàng hóa chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL
    };

    const plainObject = {
      warehouseId: 1,
      address: '123 Storage St, Warehouse City',
      capacity: 5000,
      warehouse: warehouseInfo,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(PhysicalWarehouseResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.address).toBe('123 Storage St, Warehouse City');
    expect(dto.capacity).toBe(5000);
    expect(dto.warehouse).toBeDefined();
    expect(dto.warehouse).toBeInstanceOf(Object);
    expect(dto.warehouse.warehouseId).toBe(1);
    expect(dto.warehouse.name).toBe('Kho chính');
    expect(dto.warehouse.description).toBe('Kho chứa hàng hóa chính của công ty');
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.PHYSICAL);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của PhysicalWarehouseResponseDto', () => {
    // Arrange
    const warehouseInfo = {
      warehouseId: 3,
      name: 'Kho phụ',
      type: WarehouseTypeEnum.PHYSICAL
    };

    const plainObject = {
      warehouseId: 3,
      address: '45 Phố Huế, Hai Bà Trưng, Hà Nội',
      warehouse: warehouseInfo
    };

    // Act
    const dto = plainToInstance(PhysicalWarehouseResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseResponseDto);
    expect(dto.warehouseId).toBe(3);
    expect(dto.address).toBe('45 Phố Huế, Hai Bà Trưng, Hà Nội');
    expect(dto.capacity).toBeUndefined();
    expect(dto.warehouse).toBeDefined();
    expect(dto.warehouse.warehouseId).toBe(3);
    expect(dto.warehouse.name).toBe('Kho phụ');
    expect(dto.warehouse.description).toBeUndefined();
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.PHYSICAL);
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const warehouseInfo = new WarehouseResponseDto({
      warehouseId: 1,
      name: 'Kho chính',
      description: 'Kho chứa hàng hóa chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL
    });

    const data = {
      warehouseId: 1,
      address: '123 Storage St, Warehouse City',
      capacity: 5000,
      warehouse: warehouseInfo
    };

    // Act
    const dto = new PhysicalWarehouseResponseDto(data);

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.address).toBe('123 Storage St, Warehouse City');
    expect(dto.capacity).toBe(5000);
    expect(dto.warehouse).toBe(warehouseInfo);
  });
});
