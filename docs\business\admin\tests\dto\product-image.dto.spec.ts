import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { ProductImageDto } from '../../dto/customfields/product-image.dto';

describe('ProductImageDto', () => {
  it('nên chuyển đổi plain object thành instance của ProductImageDto', () => {
    // Arrange
    const plainObject = {
      key: 'products/image1.jpg',
      position: 0,
      url: 'https://cdn.redai.vn/products/image1.jpg',
    };

    // Act
    const dto = plainToInstance(ProductImageDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(ProductImageDto);
    expect(dto.key).toBe('products/image1.jpg');
    expect(dto.position).toBe(0);
    expect(dto.url).toBe('https://cdn.redai.vn/products/image1.jpg');
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(ProductImageDto, {
      key: 'products/image1.jpg',
      position: 0,
      url: 'https://cdn.redai.vn/products/image1.jpg',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu key', async () => {
    // Arrange
    const dto = plainToInstance(ProductImageDto, {
      position: 0,
      url: 'https://cdn.redai.vn/products/image1.jpg',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('key');
  });

  it('nên thất bại khi thiếu position', async () => {
    // Arrange
    const dto = plainToInstance(ProductImageDto, {
      key: 'products/image1.jpg',
      url: 'https://cdn.redai.vn/products/image1.jpg',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('position');
  });

  it('nên thất bại khi thiếu url', async () => {
    // Arrange
    const dto = plainToInstance(ProductImageDto, {
      key: 'products/image1.jpg',
      position: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('url');
  });

  it('nên thất bại khi position không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(ProductImageDto, {
      key: 'products/image1.jpg',
      position: 'first',
      url: 'https://cdn.redai.vn/products/image1.jpg',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('position');
  });
});
