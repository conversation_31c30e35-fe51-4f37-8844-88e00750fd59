import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { ProductPriceDto } from '../../dto/customfields/product-price.dto';

describe('ProductPriceDto', () => {
  it('nên chuyển đổi plain object thành instance của ProductPriceDto', () => {
    // Arrange
    const plainObject = {
      currency: 'VND',
      listPrice: 1000000,
      salePrice: 900000,
    };

    // Act
    const dto = plainToInstance(ProductPriceDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(ProductPriceDto);
    expect(dto.currency).toBe('VND');
    expect(dto.listPrice).toBe(1000000);
    expect(dto.salePrice).toBe(900000);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(ProductPriceDto, {
      currency: 'VND',
      listPrice: 1000000,
      salePrice: 900000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu currency', async () => {
    // Arrange
    const dto = plainToInstance(ProductPriceDto, {
      listPrice: 1000000,
      salePrice: 900000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('currency');
  });

  it('nên thất bại khi thiếu listPrice', async () => {
    // Arrange
    const dto = plainToInstance(ProductPriceDto, {
      currency: 'VND',
      salePrice: 900000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('listPrice');
  });

  it('nên thất bại khi thiếu salePrice', async () => {
    // Arrange
    const dto = plainToInstance(ProductPriceDto, {
      currency: 'VND',
      listPrice: 1000000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('salePrice');
  });

  it('nên thất bại khi listPrice không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(ProductPriceDto, {
      currency: 'VND',
      listPrice: 'một triệu',
      salePrice: 900000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('listPrice');
  });

  it('nên thất bại khi salePrice không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(ProductPriceDto, {
      currency: 'VND',
      listPrice: 1000000,
      salePrice: 'chín trăm nghìn',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('salePrice');
  });
});
