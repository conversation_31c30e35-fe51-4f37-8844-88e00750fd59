import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryCustomFieldDto } from '../../dto/customfields/query-custom-field.dto';

describe('QueryCustomFieldDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      page: 1,
      limit: 10,
      search: 'color',
      type: 'text',
      component: 'input',
      employeeId: 1,
      sortBy: 'createdAt',
      sortOrder: 'DESC',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với thông tin một phần', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      page: 1,
      limit: 10,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO trống vì tất cả các trường đều là tùy chọn', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi page không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      page: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi limit không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      limit: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi type không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      type: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi component không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      component: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi employeeId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      employeeId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên chuyển đổi employeeId từ chuỗi sang số', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      employeeId: '1',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.employeeId).toBe(1);
  });

  it('nên xác thực DTO với các giá trị hợp lệ cho sortBy và sortOrder', async () => {
    // Arrange
    const dto = plainToInstance(QueryCustomFieldDto, {
      sortBy: 'label',
      sortOrder: 'ASC',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
