import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserConvertDto } from '../../dto/userconverts/query-user-convert.dto';

describe('QueryUserConvertDto', () => {
  it('nên xác thực DTO hợp lệ với các tham số mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertDto, {
      page: 1,
      limit: 10,
      search: 'khách hàng',
      convertCustomerId: 1,
      userId: 123,
      conversionType: 'online',
      source: 'website',
      createdAtFrom: 1625097600000,
      createdAtTo: 1625184000000,
      sortBy: 'createdAt',
      sortDirection: 'DESC',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên chuyển đổi các giá trị từ chuỗi sang số', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertDto, {
      page: '1',
      limit: '10',
      convertCustomerId: '1',
      userId: '123',
      createdAtFrom: '1625097600000',
      createdAtTo: '1625184000000',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.convertCustomerId).toBe(1);
    expect(dto.userId).toBe(123);
    expect(dto.createdAtFrom).toBe(1625097600000);
    expect(dto.createdAtTo).toBe(1625184000000);
  });

  it('nên thất bại khi page là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertDto, {
      page: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('page');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi limit là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertDto, {
      limit: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('limit');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi convertCustomerId là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertDto, {
      convertCustomerId: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('convertCustomerId');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi userId là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertDto, {
      userId: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('userId');
    expect(errors[0].constraints).toHaveProperty('min');
  });
});
