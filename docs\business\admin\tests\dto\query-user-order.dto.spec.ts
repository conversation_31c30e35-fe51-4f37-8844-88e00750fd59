import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserOrderDto } from '../../dto/userconverts/query-user-order.dto';

describe('QueryUserOrderDto', () => {
  it('nên xác thực DTO hợp lệ với các tham số mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      page: 1,
      limit: 10,
      search: 'đơn hàng',
      userId: 123,
      userConvertCustomerId: 456,
      hasShipping: true,
      shippingStatus: 'pending',
      source: 'website',
      createdAtFrom: 1625097600000,
      createdAtTo: 1625184000000,
      sortBy: 'createdAt',
      sortDirection: 'DESC',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên chuyển đổi các giá trị từ chuỗi sang số và boolean', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      page: '1',
      limit: '10',
      userId: '123',
      userConvertCustomerId: '456',
      hasShipping: 'true',
      createdAtFrom: '1625097600000',
      createdAtTo: '1625184000000',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.userId).toBe(123);
    expect(dto.userConvertCustomerId).toBe(456);
    expect(dto.hasShipping).toBe(true);
    expect(dto.createdAtFrom).toBe(1625097600000);
    expect(dto.createdAtTo).toBe(1625184000000);
  });

  it('nên thất bại khi page là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      page: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('page');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi limit là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      limit: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('limit');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi userId là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      userId: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('userId');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi userConvertCustomerId là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      userConvertCustomerId: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('userConvertCustomerId');
    expect(errors[0].constraints).toHaveProperty('min');
  });
});
