import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserProductDto } from '../../dto';
import { PriceTypeEnum } from '@modules/business/enums';
import { EntityStatusEnum } from '@modules/business/enums/entity-status.enum';

describe('QueryUserProductDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      page: 1,
      limit: 10,
      search: 'product',
      createdAtFrom: 1625097600000,
      createdAtTo: 1625184000000,
      minPrice: 100,
      maxPrice: 1000,
      typePrice: PriceTypeEnum.HAS_PRICE,
      tags: 'tag1,tag2',
      userId: 123,
      status: EntityStatusEnum.PENDING,
      sortBy: 'createdAt',
      sortOrder: 'DESC',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với thông tin một phần', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      page: 1,
      limit: 10,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO trống vì tất cả các trường đều là tùy chọn', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi createdAtFrom không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      createdAtFrom: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi createdAtTo không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      createdAtTo: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi minPrice không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      minPrice: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi minPrice là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      minPrice: -100,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi maxPrice không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      maxPrice: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi maxPrice là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      maxPrice: -100,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi typePrice không phải là giá trị hợp lệ của PriceTypeEnum', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      typePrice: 'INVALID_TYPE',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('nên xác thực DTO với typePrice hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      typePrice: PriceTypeEnum.STRING_PRICE,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi tags không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      tags: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi userId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      userId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi status không phải là giá trị hợp lệ của EntityStatusEnum', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      status: 'INVALID_STATUS',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('nên xác thực DTO với status hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      status: EntityStatusEnum.APPROVED,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên chuyển đổi các giá trị từ chuỗi sang số và enum', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserProductDto, {
      createdAtFrom: '1625097600000',
      createdAtTo: '1625184000000',
      minPrice: '100',
      maxPrice: '1000',
      userId: '123',
      typePrice: 'HAS_PRICE',
      status: 'PENDING',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.createdAtFrom).toBe(1625097600000);
    expect(dto.createdAtTo).toBe(1625184000000);
    expect(dto.minPrice).toBe(100);
    expect(dto.maxPrice).toBe(1000);
    expect(dto.userId).toBe(123);
    expect(dto.typePrice).toBe(PriceTypeEnum.HAS_PRICE);
    expect(dto.status).toBe(EntityStatusEnum.PENDING);
  });
});
