import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateCustomFieldDto } from '../../dto/customfields/update-custom-field.dto';

describe('UpdateCustomFieldDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      component: 'input',
      configId: 'product_color',
      label: '<PERSON><PERSON><PERSON> sắc sản phẩm',
      type: 'text',
      required: false,
      configJson: {
        placeholder: 'Nhập màu sắc chính',
        maxLength: 100,
        description: '<PERSON><PERSON><PERSON> sắc chủ đạo của sản phẩm',
        validateRegex: '^[a-zA-Z\\s]+$',
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với thông tin một phần', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      label: 'Màu sắc sản phẩm',
      required: false,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO trống vì tất cả các trường đều là tùy chọn', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi component không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      component: 123,
      label: 'Màu sắc sản phẩm',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi configId không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      configId: 123,
      label: 'Màu sắc sản phẩm',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi label không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      label: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi type không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      type: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi required không phải là boolean', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      required: 'yes',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isBoolean');
  });

  it('nên thất bại khi configJson không phải là object', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      configJson: 'not an object',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isObject');
  });

  it('nên xác thực DTO với configJson phức tạp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateCustomFieldDto, {
      configJson: {
        placeholder: 'Nhập màu sắc chính',
        maxLength: 100,
        description: 'Màu sắc chủ đạo của sản phẩm',
        validateRegex: '^[a-zA-Z\\s]+$',
        options: [
          { label: 'Đỏ', value: 'red' },
          { label: 'Xanh', value: 'blue' },
          { label: 'Vàng', value: 'yellow' },
        ],
        defaultValue: 'red',
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
