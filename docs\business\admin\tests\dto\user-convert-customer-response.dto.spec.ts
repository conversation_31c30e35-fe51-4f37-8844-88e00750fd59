import { plainToInstance } from 'class-transformer';
import { UserConvertCustomerResponseDto } from '../../dto/userconverts/user-convert-customer-response.dto';

describe('UserConvertCustomerResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của UserConvertCustomerResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      avatar: 'avatars/customer-123.jpg',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: { primary: '<EMAIL>', secondary: '<EMAIL>' },
      phone: '0912345678',
      platform: 'Facebook',
      timezone: 'Asia/Ho_Chi_Minh',
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      userId: 123,
      agentId: '550e8400-e29b-41d4-a716-446655440000',
      metadata: [
        { fieldName: 'address', fieldValue: '<PERSON><PERSON>' },
        { fieldName: 'job', fieldValue: 'Developer' }
      ]
    };

    // Act
    const dto = plainToInstance(UserConvertCustomerResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertCustomerResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.avatar).toBe('avatars/customer-123.jpg');
    expect(dto.name).toBe('Nguyễn Văn A');
    expect(dto.email).toEqual({ primary: '<EMAIL>', secondary: '<EMAIL>' });
    expect(dto.phone).toBe('0912345678');
    expect(dto.platform).toBe('Facebook');
    expect(dto.timezone).toBe('Asia/Ho_Chi_Minh');
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625184000000);
    expect(dto.userId).toBe(123);
    expect(dto.agentId).toBe('550e8400-e29b-41d4-a716-446655440000');
    expect(dto.metadata).toHaveLength(2);
    expect(dto.metadata[0].fieldName).toBe('address');
    expect(dto.metadata[0].fieldValue).toBe('Hà Nội');
    expect(dto.metadata[1].fieldName).toBe('job');
    expect(dto.metadata[1].fieldValue).toBe('Developer');
  });

  it('nên xử lý đúng khi các trường tùy chọn là null', () => {
    // Arrange
    const plainObject = {
      id: 1,
      avatar: null,
      name: null,
      email: null,
      phone: null,
      platform: null,
      timezone: null,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      userId: null,
      agentId: null,
      metadata: []
    };

    // Act
    const dto = plainToInstance(UserConvertCustomerResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertCustomerResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.avatar).toBeNull();
    expect(dto.name).toBeNull();
    expect(dto.email).toBeNull();
    expect(dto.phone).toBeNull();
    expect(dto.platform).toBeNull();
    expect(dto.timezone).toBeNull();
    expect(dto.userId).toBeNull();
    expect(dto.agentId).toBeNull();
    expect(dto.metadata).toHaveLength(0);
  });

  it('nên xử lý đúng với mảng các UserConvertCustomerResponseDto', () => {
    // Arrange
    const plainArray = [
      {
        id: 1,
        avatar: 'avatars/customer-123.jpg',
        name: 'Nguyễn Văn A',
        email: { primary: '<EMAIL>' },
        phone: '0912345678',
        platform: 'Facebook',
        timezone: 'Asia/Ho_Chi_Minh',
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        userId: 123,
        agentId: '550e8400-e29b-41d4-a716-446655440000',
        metadata: [{ fieldName: 'address', fieldValue: 'Hà Nội' }]
      },
      {
        id: 2,
        avatar: 'avatars/customer-456.jpg',
        name: 'Trần Thị B',
        email: { primary: '<EMAIL>' },
        phone: '0987654321',
        platform: 'Web',
        timezone: 'Asia/Ho_Chi_Minh',
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        userId: 123,
        agentId: '550e8400-e29b-41d4-a716-446655440000',
        metadata: [{ fieldName: 'address', fieldValue: 'Hồ Chí Minh' }]
      }
    ];

    // Act
    const dtos = plainToInstance(UserConvertCustomerResponseDto, plainArray);

    // Assert
    expect(Array.isArray(dtos)).toBe(true);
    expect(dtos.length).toBe(2);
    expect(dtos[0]).toBeInstanceOf(UserConvertCustomerResponseDto);
    expect(dtos[0].id).toBe(1);
    expect(dtos[0].name).toBe('Nguyễn Văn A');
    expect(dtos[1]).toBeInstanceOf(UserConvertCustomerResponseDto);
    expect(dtos[1].id).toBe(2);
    expect(dtos[1].name).toBe('Trần Thị B');
  });
});
