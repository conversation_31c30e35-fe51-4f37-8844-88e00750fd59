import { plainToInstance } from 'class-transformer';
import { UserOrderDetailResponseDto } from '../../dto/userconverts/user-order-detail-response.dto';
import { UserConvertCustomerResponseDto } from '../../dto/userconverts/user-convert-customer-response.dto';

describe('UserOrderDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của UserOrderDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      userConvertCustomerId: 2,
      userId: 3,
      productInfo: [
        {
          productId: 1,
          name: 'Sản phẩm A',
          quantity: 2,
          price: 100000,
        },
        {
          productId: 2,
          name: 'Sản phẩm B',
          quantity: 1,
          price: 150000,
        },
      ],
      billInfo: {
        subtotal: 350000,
        tax: 35000,
        shipping: 30000,
        total: 415000,
        paymentMethod: 'COD',
      },
      hasShipping: true,
      shippingStatus: 'pending',
      logisticInfo: {
        address: 'Số 1, Đường ABC, Quận XYZ, Hà Nội',
        carrier: 'GHN',
        trackingNumber: 'GHN123456789',
      },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      source: 'website',
      customer: {
        id: 2,
        avatar: 'avatars/customer-123.jpg',
        name: 'Nguyễn Văn A',
        email: { primary: '<EMAIL>' },
        phone: '0912345678',
        platform: 'Facebook',
        timezone: 'Asia/Ho_Chi_Minh',
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        userId: 3,
        agentId: '550e8400-e29b-41d4-a716-446655440000',
        metadata: [{ fieldName: 'address', fieldValue: 'Hà Nội' }]
      }
    };

    // Act
    const dto = plainToInstance(UserOrderDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserOrderDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.userConvertCustomerId).toBe(2);
    expect(dto.userId).toBe(3);
    
    // Kiểm tra thông tin sản phẩm
    expect(dto.productInfo).toHaveLength(2);
    expect(dto.productInfo?.[0].productId).toBe(1);
    expect(dto.productInfo?.[0].name).toBe('Sản phẩm A');
    expect(dto.productInfo?.[0].quantity).toBe(2);
    expect(dto.productInfo?.[0].price).toBe(100000);
    expect(dto.productInfo?.[1].productId).toBe(2);
    
    // Kiểm tra thông tin hóa đơn
    expect(dto.billInfo).toBeDefined();
    expect(dto.billInfo?.subtotal).toBe(350000);
    expect(dto.billInfo?.tax).toBe(35000);
    expect(dto.billInfo?.shipping).toBe(30000);
    expect(dto.billInfo?.total).toBe(415000);
    expect(dto.billInfo?.paymentMethod).toBe('COD');
    
    // Kiểm tra thông tin vận chuyển
    expect(dto.hasShipping).toBe(true);
    expect(dto.shippingStatus).toBe('pending');
    expect(dto.logisticInfo).toBeDefined();
    expect(dto.logisticInfo?.address).toBe('Số 1, Đường ABC, Quận XYZ, Hà Nội');
    expect(dto.logisticInfo?.carrier).toBe('GHN');
    expect(dto.logisticInfo?.trackingNumber).toBe('GHN123456789');
    
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625184000000);
    expect(dto.source).toBe('website');
    
    // Kiểm tra thông tin khách hàng
    expect(dto.customer).toBeDefined();
    expect(dto.customer).toBeInstanceOf(UserConvertCustomerResponseDto);
    expect(dto.customer?.id).toBe(2);
    expect(dto.customer?.name).toBe('Nguyễn Văn A');
    expect(dto.customer?.email).toEqual({ primary: '<EMAIL>' });
    expect(dto.customer?.phone).toBe('0912345678');
    expect(dto.customer?.platform).toBe('Facebook');
    expect(dto.customer?.userId).toBe(3);
  });

  it('nên xử lý đúng khi customer là null', () => {
    // Arrange
    const plainObject = {
      id: 1,
      userConvertCustomerId: 2,
      userId: 3,
      productInfo: [{ productId: 1, name: 'Sản phẩm A', quantity: 2, price: 100000 }],
      billInfo: { subtotal: 200000, total: 200000, paymentMethod: 'COD' },
      hasShipping: true,
      shippingStatus: 'pending',
      logisticInfo: { address: 'Hà Nội' },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      source: 'website',
      customer: null
    };

    // Act
    const dto = plainToInstance(UserOrderDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserOrderDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.customer).toBeNull();
  });

  it('nên xử lý đúng khi không có trường customer', () => {
    // Arrange
    const plainObject = {
      id: 1,
      userConvertCustomerId: 2,
      userId: 3,
      productInfo: [{ productId: 1, name: 'Sản phẩm A', quantity: 2, price: 100000 }],
      billInfo: { subtotal: 200000, total: 200000, paymentMethod: 'COD' },
      hasShipping: true,
      shippingStatus: 'pending',
      logisticInfo: { address: 'Hà Nội' },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      source: 'website'
    };

    // Act
    const dto = plainToInstance(UserOrderDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserOrderDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.customer).toBeUndefined();
  });
});
