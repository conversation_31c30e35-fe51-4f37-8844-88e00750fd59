import { plainToInstance } from 'class-transformer';
import { UserProductResponseDto } from '../../dto/customfields/user-product-response.dto';
import { PriceTypeEnum } from '@modules/business/enums';

describe('UserProductResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của UserProductResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 123,
      name: 'Sản phẩm A',
      price: { amount: 1000, currency: 'VND' },
      typePrice: PriceTypeEnum.HAS_PRICE,
      description: '<PERSON><PERSON> tả chi tiết về sản phẩm A',
      images: [
        { url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
        { url: 'https://cdn.redai.vn/products/image2.jpg', position: 1 },
      ],
      tags: ['tag1', 'tag2'],
      createdBy: 456,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
    };

    // Act
    const dto = plainToInstance(UserProductResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserProductResponseDto);
    expect(dto.id).toBe(123);
    expect(dto.name).toBe('Sản phẩm A');
    expect(dto.price).toEqual({ amount: 1000, currency: 'VND' });
    expect(dto.typePrice).toBe(PriceTypeEnum.HAS_PRICE);
    expect(dto.description).toBe('Mô tả chi tiết về sản phẩm A');
    expect(dto.images).toHaveLength(2);
    expect(dto.images).toBeDefined();
    if (dto.images) {
      expect(dto.images[0].url).toBe('https://cdn.redai.vn/products/image1.jpg');
      expect(dto.images[0].position).toBe(0);
    }
    expect(dto.tags).toEqual(['tag1', 'tag2']);
    expect(dto.createdBy).toBe(456);
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625184000000);
  });

  it('nên xử lý đúng khi description là null', () => {
    // Arrange
    const plainObject = {
      id: 123,
      name: 'Sản phẩm A',
      price: { amount: 1000, currency: 'VND' },
      typePrice: PriceTypeEnum.HAS_PRICE,
      description: null,
      images: [
        { url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
      ],
      tags: ['tag1', 'tag2'],
      createdBy: 456,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
    };

    // Act
    const dto = plainToInstance(UserProductResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserProductResponseDto);
    expect(dto.description).toBeNull();
  });

  it('nên xử lý đúng khi images là null', () => {
    // Arrange
    const plainObject = {
      id: 123,
      name: 'Sản phẩm A',
      price: { amount: 1000, currency: 'VND' },
      typePrice: PriceTypeEnum.HAS_PRICE,
      description: 'Mô tả chi tiết về sản phẩm A',
      images: null,
      tags: ['tag1', 'tag2'],
      createdBy: 456,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
    };

    // Act
    const dto = plainToInstance(UserProductResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserProductResponseDto);
    expect(dto.images).toBeNull();
  });

  it('nên xử lý đúng khi tags là null', () => {
    // Arrange
    const plainObject = {
      id: 123,
      name: 'Sản phẩm A',
      price: { amount: 1000, currency: 'VND' },
      typePrice: PriceTypeEnum.HAS_PRICE,
      description: 'Mô tả chi tiết về sản phẩm A',
      images: [
        { url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
      ],
      tags: null,
      createdBy: 456,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
    };

    // Act
    const dto = plainToInstance(UserProductResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserProductResponseDto);
    expect(dto.tags).toBeNull();
  });

  it('nên xử lý đúng với các loại giá khác nhau', () => {
    // Arrange
    const plainObject = {
      id: 123,
      name: 'Sản phẩm A',
      price: { amount: 1000, currency: 'VND' },
      typePrice: PriceTypeEnum.STRING_PRICE,
      description: 'Mô tả chi tiết về sản phẩm A',
      images: [
        { url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
      ],
      tags: ['tag1', 'tag2'],
      createdBy: 456,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
    };

    // Act
    const dto = plainToInstance(UserProductResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserProductResponseDto);
    expect(dto.typePrice).toBe(PriceTypeEnum.STRING_PRICE);
  });

  it('nên xử lý đúng với mảng các UserProductResponseDto', () => {
    // Arrange
    const plainArray = [
      {
        id: 123,
        name: 'Sản phẩm A',
        price: { amount: 1000, currency: 'VND' },
        typePrice: PriceTypeEnum.HAS_PRICE,
        description: 'Mô tả chi tiết về sản phẩm A',
        images: [
          { url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
        ],
        tags: ['tag1', 'tag2'],
        createdBy: 456,
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
      },
      {
        id: 124,
        name: 'Sản phẩm B',
        price: { amount: 2000, currency: 'VND' },
        typePrice: PriceTypeEnum.NO_PRICE,
        description: 'Mô tả chi tiết về sản phẩm B',
        images: [
          { url: 'https://cdn.redai.vn/products/image3.jpg', position: 0 },
        ],
        tags: ['tag3', 'tag4'],
        createdBy: 456,
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
      },
    ];

    // Act
    const dtos = plainToInstance(UserProductResponseDto, plainArray);

    // Assert
    expect(dtos).toHaveLength(2);
    expect(dtos[0]).toBeInstanceOf(UserProductResponseDto);
    expect(dtos[1]).toBeInstanceOf(UserProductResponseDto);
    expect(dtos[0].id).toBe(123);
    expect(dtos[1].id).toBe(124);
    expect(dtos[0].name).toBe('Sản phẩm A');
    expect(dtos[1].name).toBe('Sản phẩm B');
  });
});
