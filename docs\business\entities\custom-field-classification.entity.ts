import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { UserClassification } from './user-classification.entity';
import { CustomField } from './custom-field.entity';

/**
 * Entity đại diện cho bảng custom_field_classifications trong cơ sở dữ liệu
 * Bảng quản lý giá trị trường tùy chỉnh cho phân loại sản phẩm
 */
@Entity('custom_field_classifications')
export class CustomFieldClassification {
  /**
   * ID của phân loại
   */
  @PrimaryColumn({ name: 'classification_id' })
  classificationId: number;

  /**
   * ID của trường tùy chỉnh
   */
  @PrimaryColumn({ name: 'custom_field_id' })
  customFieldId: number;

  /**
   * Giá trị của trường tùy chỉnh (dưới dạng JSON)
   */
  @Column({ name: 'value', type: 'jsonb', nullable: false, default: () => '{"value": ""}::jsonb', comment: '<PERSON><PERSON><PERSON> trị của trường tùy chỉnh' })
  value: any;
}
