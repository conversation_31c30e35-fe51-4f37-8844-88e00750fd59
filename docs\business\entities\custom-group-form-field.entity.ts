import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Interface cho cấu trúc JSONB của cột value
 */
export interface FieldValue {
  value: string;
}

/**
 * Interface cho cấu trúc JSONB của cột gird
 */
export interface GridConfig {
  [key: string]: any; // Tạm thời dùng kiểu chung vì không có cấu trúc cụ thể
}

/**
 * Entity đại diện cho bảng custom_group_form_field trong cơ sở dữ liệu
 * Bảng quản lý trường trong nhóm trường tùy chỉnh
 */
@Entity('custom_group_form_field')
export class CustomGroupFormField {
  /**
   * ID của trường tùy chỉnh, tham chiếu đến bảng custom_fields
   */
  @PrimaryColumn({
    name: 'field_id',
    type: 'integer',
    comment: 'ID của trường tùy chỉnh, tham chiếu đến bảng custom_fields',
  })
  fieldId: number;

  /**
   * ID của nhóm trường tùy chỉnh, tham chiếu đến bảng custom_group_form
   */
  @PrimaryColumn({
    name: 'form_group_id',
    type: 'integer',
    comment: 'ID của nhóm trường tùy chỉnh, tham chiếu đến bảng custom_group_form',
  })
  formGroupId: number;

  /**
   * Cấu hình grid
   */
  @Column({
    name: 'gird',
    type: 'jsonb',
    nullable: true,
    comment: 'Cấu hình grid dưới dạng JSONB',
  })
  gird: GridConfig | null;

  /**
   * Giá trị mặc định của trường
   */
  @Column({
    name: 'value',
    type: 'jsonb',
    nullable: false,
    default: () => '\'{"value": "example"}\'::jsonb',
    comment: 'Giá trị mặc định của trường dưới dạng JSONB',
  })
  value: FieldValue;
}