import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

/**
 * Entity đại diện cho bảng custom_group_form trong cơ sở dữ liệu
 * Bảng quản lý nhóm trường tùy chỉnh
 */
@Entity('custom_group_form')
export class CustomGroupForm {
  /**
   * ID của nhóm trường tùy chỉnh
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Nhãn hiển thị
   */
  @Column({
    name: 'label',
    length: 255,
    nullable: false,
    comment: 'Nhãn hiển thị',
  })
  label: string;

  /**
   * ID group form san pham
   *
   */
  @Column({ name: 'product_id', type: 'integer', nullable: true, comment: 'ID product'})
  productId: number | null;



  /**
   * ID người dùng tạo
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true, comment: 'ID người dùng tạo' })
  userId: number | null;

  /**
   * Thời gian tạo (millis)
   */
  @Column({
    name: 'create_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian tạo (millis)',
  })
  createAt: number;
}
