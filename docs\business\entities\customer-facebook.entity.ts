import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { UserConvertCustomer } from './user-convert-customer.entity';

/**
 * Entity đại diện cho bảng customer_facebook trong cơ sở dữ liệu
 * Thông tin khách hàng đến từ Facebook
 */
@Entity('customer_facebook')
export class CustomerFacebook {
  /**
   * ID khách hàng Facebook
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID riêng của khách hàng trên page
   */
  @Column({ 
    name: 'page_scoped_id', 
    length: 20, 
    nullable: false, 
    unique: true, 
    comment: 'ID riêng của khách hàng trên page' 
  })
  pageScopedId: string;

  /**
   * ID trang Facebook
   */
  @Column({ 
    name: 'page_id', 
    length: 255, 
    nullable: true, 
    comment: 'ID trang Facebook' 
  })
  pageId: string;

  /**
   * Tên người dùng
   */
  @Column({ 
    name: 'name', 
    type: 'text', 
    nullable: true, 
    comment: 'Tên người dùng' 
  })
  name: string;

  /**
   * Ảnh đại diện
   */
  @Column({ 
    name: 'avatar', 
    length: 255, 
    nullable: true, 
    comment: 'Ảnh đại diện' 
  })
  avatar: string;

  /**
   * Giới tính
   */
  @Column({ 
    name: 'gender', 
    length: 45, 
    nullable: true, 
    comment: 'Giới tính' 
  })
  gender: string;

  /**
   * Tham chiếu đến khách hàng chuyển đổi
   */
  @Column({ 
    name: 'user_convert_customer_id', 
    type: 'bigint', 
    nullable: true, 
    comment: 'Tham chiếu đến khách hàng chuyển đổi' 
  })
  userConvertCustomerId: number;

  /**
   * Quan hệ với UserConvertCustomer
   */
  @ManyToOne(() => UserConvertCustomer, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_convert_customer_id' })
  userConvertCustomer: UserConvertCustomer;
}
