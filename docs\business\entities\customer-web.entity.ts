import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { UserConvertCustomer } from './user-convert-customer.entity';

/**
 * Entity đại diện cho bảng customer_web trong cơ sở dữ liệu
 * Thông tin khách truy cập từ website
 */
@Entity('customer_web')
export class CustomerWeb {
  /**
   * ID khách truy cập web
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tên miền truy cập
   */
  @Column({ 
    name: 'domain', 
    type: 'text', 
    nullable: true, 
    comment: 'Tên miền truy cập' 
  })
  domain: string;

  /**
   * Đường dẫn trang
   */
  @Column({ 
    name: 'path', 
    type: 'text', 
    nullable: true, 
    comment: 'Đường dẫn trang' 
  })
  path: string;

  /**
   * Thiết bị sử dụng
   */
  @Column({ 
    name: 'device', 
    length: 200, 
    nullable: true, 
    comment: 'Thiết bị sử dụng' 
  })
  device: string;

  /**
   * Hệ điều hành
   */
  @Column({ 
    name: 'os', 
    length: 200, 
    nullable: true, 
    comment: 'Hệ điều hành' 
  })
  os: string;

  /**
   * Địa chỉ IP
   */
  @Column({ 
    name: 'ip', 
    length: 100, 
    nullable: true, 
    comment: 'Địa chỉ IP' 
  })
  ip: string;

  /**
   * Trình duyệt
   */
  @Column({ 
    name: 'browser', 
    length: 400, 
    nullable: true, 
    comment: 'Trình duyệt' 
  })
  browser: string;

  /**
   * Thời điểm kết thúc phiên truy cập
   */
  @Column({ 
    name: 'end_session_unix', 
    type: 'bigint', 
    nullable: true, 
    comment: 'Thời điểm kết thúc phiên truy cập' 
  })
  endSessionUnix: number;

  /**
   * Thời điểm bắt đầu phiên truy cập
   */
  @Column({ 
    name: 'start_session_unix', 
    type: 'bigint', 
    nullable: false, 
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint", 
    comment: 'Thời điểm bắt đầu phiên truy cập' 
  })
  startSessionUnix: number;

  /**
   * Favicon của trang web
   */
  @Column({ 
    name: 'favicon', 
    length: 500, 
    nullable: true, 
    comment: 'Favicon của trang web' 
  })
  favicon: string;

  /**
   * ID khách hàng chuyển đổi
   */
  @Column({ 
    name: 'user_convert_customer_id', 
    type: 'integer', 
    nullable: true, 
    comment: 'ID khách hàng chuyển đổi' 
  })
  userConvertCustomerId: number;

  /**
   * Quan hệ với UserConvertCustomer
   */
  @ManyToOne(() => UserConvertCustomer, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_convert_customer_id' })
  userConvertCustomer: UserConvertCustomer;
}
