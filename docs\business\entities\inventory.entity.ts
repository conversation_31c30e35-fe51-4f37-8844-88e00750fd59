import { Column, Entity, Join<PERSON><PERSON>um<PERSON>, <PERSON>ToOne, PrimaryGeneratedColumn } from 'typeorm';
import { UserProduct } from './user-product.entity';
import { Warehouse } from './warehouse.entity';

/**
 * Entity đại diện cho bảng inventory trong cơ sở dữ liệu
 * Quản lý số lượng tồn kho hiện tại với chi tiết số lượng
 */
@Entity('inventory')
export class Inventory {
  /**
   * ID bản ghi tồn kho
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID sản phẩm
   */
  @Column({ name: 'product_id', type: 'bigint', nullable: false, comment: 'ID sản phẩm' })
  productId: number;

  /**
   * ID kho chứa sản phẩm
   */
  @Column({ name: 'warehouse_id', type: 'integer', nullable: true, comment: 'ID kho chứa sản phẩm' })
  warehouseId: number | null;

  /**
   * <PERSON><PERSON> lượng hiện tại trong kho
   */
  @Column({ name: 'current_quantity', default: 0, nullable: false, comment: 'Số lượng hiện tại trong kho' })
  currentQuantity: number;

  /**
   * Tổng số lượng đã nhập vào kho (bao gồm cả đã xuất)
   */
  @Column({ name: 'total_quantity', default: 0, nullable: false, comment: 'Tổng số lượng đã nhập vào kho (bao gồm cả đã xuất)' })
  totalQuantity: number;

  /**
   * Số lượng sẵn sàng để bán hoặc sử dụng
   */
  @Column({ name: 'available_quantity', default: 0, nullable: false, comment: 'Số lượng sẵn sàng để bán hoặc sử dụng' })
  availableQuantity: number;

  /**
   * Số lượng bị giữ chỗ (ví dụ: cho đơn hàng chưa hoàn tất)
   */
  @Column({ name: 'reserved_quantity', default: 0, nullable: false, comment: 'Số lượng bị giữ chỗ (ví dụ: cho đơn hàng chưa hoàn tất)' })
  reservedQuantity: number;

  /**
   * Số lượng sản phẩm lỗi hoặc không sử dụng được
   */
  @Column({ name: 'defective_quantity', default: 0, nullable: false, comment: 'Số lượng sản phẩm lỗi hoặc không sử dụng được' })
  defectiveQuantity: number;

  /**
   * Thời gian cập nhật tồn kho gần nhất
   */
  @Column({
    name: 'last_updated',
    type: 'bigint',
    nullable: false,
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint",
    comment: 'Thời gian cập nhật tồn kho gần nhất'
  })
  lastUpdated: number;

  /**
   * Mã SKU (Stock Keeping Unit) của sản phẩm trong kho
   */
  @Column({ name: 'sku', type: 'varchar', length: 100, nullable: true, comment: 'Mã SKU (Stock Keeping Unit) của sản phẩm trong kho' })
  sku: string | null;

  /**
   * Mã vạch (Barcode) của sản phẩm trong kho
   */
  @Column({ name: 'barcode', type: 'varchar', length: 100, nullable: true, comment: 'Mã vạch (Barcode) của sản phẩm trong kho' })
  barcode: string | null;
}
