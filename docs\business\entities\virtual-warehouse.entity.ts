import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng virtual_warehouse trong cơ sở dữ liệu
 * Bảng quản lý kho ảo
 */
@Entity('virtual_warehouse')
export class VirtualWarehouse {
  /**
   * ID của kho
   */
  @PrimaryColumn({ name: 'warehouse_id' })
  warehouseId: number;

  /**
   * <PERSON><PERSON> thống liên kết
   */
  @Column({
    name: 'associated_system',
    length: 100,
    nullable: true,
    comment: '<PERSON><PERSON> thống liên kết',
  })
  associatedSystem: string;

  /**
   * <PERSON><PERSON><PERSON> đ<PERSON><PERSON> sử dụng
   */
  @Column({
    name: 'purpose',
    type: 'text',
    nullable: true,
    comment: '<PERSON><PERSON><PERSON> đích sử dụng',
  })
  purpose: string;
}
