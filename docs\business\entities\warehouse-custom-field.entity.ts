import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng warehouse_custom_field trong cơ sở dữ liệu
 * Bảng quản lý trường tùy chỉnh của kho
 */
@Entity('warehouse_custom_field')
export class WarehouseCustomField {
  /**
   * ID của kho
   */
  @PrimaryColumn({ name: 'warehouse_id' })
  warehouseId: number;

  /**
   * ID của trường tùy chỉnh
   */
  @PrimaryColumn({ name: 'field_id' })
  fieldId: number;

  /**
   * Giá trị của trường tùy chỉnh
   */
  @Column({
    name: 'value',
    type: 'jsonb',
    nullable: false,
    default: () => '\'{"value": "example"}\'::jsonb',
    comment: 'Giá trị của trường tùy chỉnh',
  })
  value: any;
}
