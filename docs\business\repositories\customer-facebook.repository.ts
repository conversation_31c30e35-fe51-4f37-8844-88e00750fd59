import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { CustomerFacebook } from '@modules/business/entities/customer-facebook.entity';

/**
 * Repository cho CustomerFacebook entity
 * Xử lý các thao tác database liên quan đến thông tin Facebook của khách hàng
 */
@Injectable()
export class CustomerFacebookRepository extends Repository<CustomerFacebook> {
  private readonly logger = new Logger(CustomerFacebookRepository.name);

  constructor(private dataSource: DataSource) {
    super(CustomerFacebook, dataSource.createEntityManager());
  }

  /**
   * Tìm tất cả thông tin Facebook của một khách hàng chuyển đổi
   * @param userConvertCustomerId ID khách hàng chuyển đổi
   * @returns Danh sách thông tin Facebook
   */
  async findByUserConvertCustomerId(userConvertCustomerId: number): Promise<CustomerFacebook[]> {
    try {
      this.logger.log(`Tì<PERSON> thông tin Facebook cho khách hàng chuyển đổi ID: ${userConvertCustomerId}`);

      return await this.find({
        where: { userConvertCustomerId },
        order: { id: 'DESC' }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm thông tin Facebook: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm thông tin Facebook: ${error.message}`);
    }
  }

  /**
   * Tìm thông tin Facebook theo page scoped ID
   * @param pageScopedId Page scoped ID
   * @returns Thông tin Facebook hoặc null
   */
  async findByPageScopedId(pageScopedId: string): Promise<CustomerFacebook | null> {
    try {
      this.logger.log(`Tìm thông tin Facebook theo page scoped ID: ${pageScopedId}`);

      return await this.findOne({
        where: { pageScopedId }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm thông tin Facebook theo page scoped ID: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm thông tin Facebook theo page scoped ID: ${error.message}`);
    }
  }

  /**
   * Tạo hoặc cập nhật thông tin Facebook
   * @param facebookData Dữ liệu Facebook
   * @returns Thông tin Facebook đã lưu
   */
  async createOrUpdate(facebookData: Partial<CustomerFacebook>): Promise<CustomerFacebook> {
    try {
      this.logger.log(`Tạo hoặc cập nhật thông tin Facebook cho khách hàng: ${facebookData.userConvertCustomerId}`);

      // Nếu có ID, thực hiện cập nhật
      if (facebookData.id) {
        const { userConvertCustomer, ...updateData } = facebookData;
        await this.update(facebookData.id, updateData);
        const updated = await this.findOne({ where: { id: facebookData.id } });
        if (!updated) {
          throw new Error(`Không tìm thấy thông tin Facebook với ID: ${facebookData.id} sau khi cập nhật`);
        }
        return updated;
      }

      // Nếu không có ID, tạo mới
      const facebook = this.create(facebookData);
      return await this.save(facebook);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo hoặc cập nhật thông tin Facebook: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo hoặc cập nhật thông tin Facebook: ${error.message}`);
    }
  }

  /**
   * Xóa tất cả thông tin Facebook của một khách hàng chuyển đổi
   * @param userConvertCustomerId ID khách hàng chuyển đổi
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByUserConvertCustomerId(userConvertCustomerId: number): Promise<number> {
    try {
      this.logger.log(`Xóa tất cả thông tin Facebook cho khách hàng chuyển đổi ID: ${userConvertCustomerId}`);

      const result = await this.delete({ userConvertCustomerId });
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin Facebook: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa thông tin Facebook: ${error.message}`);
    }
  }

  /**
   * Xóa thông tin Facebook theo danh sách ID
   * @param ids Danh sách ID cần xóa
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByIds(ids: number[]): Promise<number> {
    try {
      if (ids.length === 0) return 0;

      this.logger.log(`Xóa thông tin Facebook theo danh sách ID: ${ids.join(', ')}`);

      const result = await this.delete(ids);
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin Facebook theo ID: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa thông tin Facebook theo ID: ${error.message}`);
    }
  }
}
