import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { CustomerWeb } from '@modules/business/entities/customer-web.entity';

/**
 * Repository cho CustomerWeb entity
 * Xử lý các thao tác database liên quan đến thông tin Web của khách hàng
 */
@Injectable()
export class CustomerWebRepository extends Repository<CustomerWeb> {
  private readonly logger = new Logger(CustomerWebRepository.name);

  constructor(private dataSource: DataSource) {
    super(CustomerWeb, dataSource.createEntityManager());
  }

  /**
   * Tìm tất cả thông tin Web của một khách hàng chuyển đổi
   * @param userConvertCustomerId ID khách hàng chuyển đổi
   * @returns Danh sách thông tin Web
   */
  async findByUserConvertCustomerId(userConvertCustomerId: number): Promise<CustomerWeb[]> {
    try {
      this.logger.log(`Tìm thông tin Web cho khách hàng chuyển đổi ID: ${userConvertCustomerId}`);

      return await this.find({
        where: { userConvertCustomerId },
        order: { startSessionUnix: 'DESC' }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm thông tin Web: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm thông tin Web: ${error.message}`);
    }
  }

  /**
   * Tạo hoặc cập nhật thông tin Web
   * @param webData Dữ liệu Web
   * @returns Thông tin Web đã lưu
   */
  async createOrUpdate(webData: Partial<CustomerWeb>): Promise<CustomerWeb> {
    try {
      this.logger.log(`Tạo hoặc cập nhật thông tin Web cho khách hàng: ${webData.userConvertCustomerId}`);

      // Nếu có ID, thực hiện cập nhật
      if (webData.id) {
        const { userConvertCustomer, ...updateData } = webData;
        await this.update(webData.id, updateData);
        const updated = await this.findOne({ where: { id: webData.id } });
        if (!updated) {
          throw new Error(`Không tìm thấy thông tin Web với ID: ${webData.id} sau khi cập nhật`);
        }
        return updated;
      }

      // Nếu không có ID, tạo mới
      const web = this.create(webData);
      return await this.save(web);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo hoặc cập nhật thông tin Web: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo hoặc cập nhật thông tin Web: ${error.message}`);
    }
  }

  /**
   * Xóa tất cả thông tin Web của một khách hàng chuyển đổi
   * @param userConvertCustomerId ID khách hàng chuyển đổi
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByUserConvertCustomerId(userConvertCustomerId: number): Promise<number> {
    try {
      this.logger.log(`Xóa tất cả thông tin Web cho khách hàng chuyển đổi ID: ${userConvertCustomerId}`);

      const result = await this.delete({ userConvertCustomerId });
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin Web: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa thông tin Web: ${error.message}`);
    }
  }

  /**
   * Xóa thông tin Web theo danh sách ID
   * @param ids Danh sách ID cần xóa
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByIds(ids: number[]): Promise<number> {
    try {
      if (ids.length === 0) return 0;

      this.logger.log(`Xóa thông tin Web theo danh sách ID: ${ids.join(', ')}`);

      const result = await this.delete(ids);
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin Web theo ID: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa thông tin Web theo ID: ${error.message}`);
    }
  }

  /**
   * Tìm thông tin Web theo domain và IP
   * @param domain Domain
   * @param ip IP address
   * @returns Danh sách thông tin Web
   */
  async findByDomainAndIp(domain: string, ip: string): Promise<CustomerWeb[]> {
    try {
      this.logger.log(`Tìm thông tin Web theo domain: ${domain} và IP: ${ip}`);

      return await this.find({
        where: { domain, ip },
        order: { startSessionUnix: 'DESC' }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm thông tin Web theo domain và IP: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm thông tin Web theo domain và IP: ${error.message}`);
    }
  }
}
