import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { PhysicalWarehouse, Warehouse } from '@modules/business/entities';
import { PaginatedResult } from '@common/response'; // From user
import { QueryPhysicalWarehouseDto } from '@modules/business/admin/dto/warehouse'; // From admin
import { SearchHelper } from '@common/helpers/search.helper';

/**
 * Repository xử lý truy vấn dữ liệu cho entity PhysicalWarehouse,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class PhysicalWarehouseRepository extends Repository<PhysicalWarehouse> {
  private readonly logger = new Logger(PhysicalWarehouseRepository.name);

  constructor(private readonly dataSource: DataSource) { // Using user's 'readonly'
    super(PhysicalWarehouse, dataSource.createEntityManager());
  }

  // --- Base Query Builders (suffixed due to different aliases) ---

  /**
   * Tạo query builder cơ bản cho physical warehouse (User context)
   * @returns Query builder
   */
  private createBaseQuery_user(): SelectQueryBuilder<PhysicalWarehouse> {
    return this.createQueryBuilder('physical_warehouse'); // User's alias
  }

  /**
   * Tạo query builder cơ bản cho physical warehouse (Admin context)
   * @returns SelectQueryBuilder<PhysicalWarehouse>
   */
  private createBaseQuery_admin(): SelectQueryBuilder<PhysicalWarehouse> {
    this.logger.log('(Admin) Tạo query builder cơ bản cho physical warehouse');
    const qb = this.createQueryBuilder('physicalWarehouse'); // Admin's alias
    this.logger.log(`(Admin) Đã tạo query builder với alias 'physicalWarehouse'`);
    return qb;
  }

  // --- Methods with same name, differentiated by suffix ---

  /**
   * Tìm kho vật lý theo ID kho (User context version)
   * @param warehouseId ID của kho
   * @returns Thông tin kho vật lý hoặc null nếu không tìm thấy
   */
  async findByWarehouseId_user(warehouseId: number): Promise<PhysicalWarehouse | null> {
    try {
      return await this.createBaseQuery_user()
        .where('physical_warehouse.warehouseId = :warehouseId', { warehouseId }) // User alias
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kho vật lý theo ID ${warehouseId} (user): ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kho vật lý theo ID ${warehouseId} (user): ${error.message}`);
    }
  }

  // --- User specific or unique methods (or methods that don't clash by name) ---

  /**
   * Tìm kho vật lý theo ID kho và kết hợp với thông tin kho chung và trường tùy chỉnh (User context)
   * @param warehouseId ID của kho
   * @returns Thông tin đầy đủ của kho vật lý hoặc null nếu không tìm thấy
   */
  async findByWarehouseIdWithDetails(warehouseId: number): Promise<any | null> {
    try {
      const queryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          'pw.warehouse_id',
          'pw.address',
          'pw.capacity'
        ])
        .addSelect('w.name', 'name')
        .addSelect('w.description', 'description')
        .addSelect('w.type', 'type')
        .from(PhysicalWarehouse, 'pw')
        .innerJoin(Warehouse, 'w', 'pw.warehouseId = w.warehouseId')
        .where('pw.warehouseId = :warehouseId', { warehouseId });

      const physicalWarehouseWithDetails = await queryBuilder.getRawOne();

      if (!physicalWarehouseWithDetails) {
        return null;
      }

      // Logic WarehouseCustomField đã bị xóa
      /*
      const customFields = await this.dataSource
        .createQueryBuilder()
        .select('wcf.*')
        .from(WarehouseCustomField, 'wcf')
        .where('wcf.warehouseId = :warehouseId', { warehouseId })
        .getRawMany();
      */
      const customFields = []; // Thay thế bằng mảng rỗng

      return {
        id: physicalWarehouseWithDetails.pw_warehouse_id, // id và warehouseId giống nhau
        warehouseId: physicalWarehouseWithDetails.pw_warehouse_id,
        address: physicalWarehouseWithDetails.pw_address,
        capacity: physicalWarehouseWithDetails.pw_capacity,
        name: physicalWarehouseWithDetails.name,
        description: physicalWarehouseWithDetails.description,
        type: physicalWarehouseWithDetails.type,
        customFields: customFields || [],
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kho vật lý với thông tin chi tiết theo ID ${warehouseId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kho vật lý với thông tin chi tiết theo ID ${warehouseId}: ${error.message}`);
    }
  }

  /**
   * Tạo mới kho vật lý (User context)
   * @param physicalWarehouse Thông tin kho vật lý cần tạo
   * @returns Kho vật lý đã tạo
   */
  async createPhysicalWarehouse(physicalWarehouse: PhysicalWarehouse): Promise<PhysicalWarehouse> {
    try {
      return await this.save(physicalWarehouse);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo kho vật lý: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo kho vật lý: ${error.message}`);
    }
  }

  /**
   * Cập nhật kho vật lý (User context)
   * @param warehouseId ID của kho
   * @param updateData Dữ liệu cập nhật
   * @returns Kho vật lý đã cập nhật
   */
  async updatePhysicalWarehouse(warehouseId: number, updateData: Partial<PhysicalWarehouse>): Promise<PhysicalWarehouse> {
    try {
      await this.update({ warehouseId }, updateData);
      const updatedWarehouse = await this.findByWarehouseId_user(warehouseId); // Use suffixed user version
      if (!updatedWarehouse) {
        throw new Error(`Không tìm thấy kho vật lý với ID ${warehouseId} sau khi cập nhật`);
      }
      return updatedWarehouse;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật kho vật lý ${warehouseId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi cập nhật kho vật lý ${warehouseId}: ${error.message}`);
    }
  }

  /**
   * Xóa kho vật lý (User context)
   * @param warehouseId ID của kho
   * @returns Kết quả xóa
   */
  async deletePhysicalWarehouse(warehouseId: number): Promise<boolean> {
    try {
      const result = await this.delete({ warehouseId });
      return result && result.affected ? result.affected > 0 : false;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa kho vật lý ${warehouseId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa kho vật lý ${warehouseId}: ${error.message}`);
    }
  }

  /**
   * Xóa nhiều kho vật lý (User context)
   * @param warehouseIds Danh sách ID của các kho cần xóa
   * @returns Số lượng kho đã xóa thành công
   */
  async bulkDeletePhysicalWarehouses(warehouseIds: number[]): Promise<number> {
    try {
      if (warehouseIds.length === 0) return 0;

      this.logger.log(`Xóa nhiều kho vật lý theo danh sách ID: ${warehouseIds.join(', ')}`);

      const result = await this.delete(warehouseIds);
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhiều kho vật lý theo ID: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa nhiều kho vật lý theo ID: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm kho vật lý với các điều kiện lọc và phân trang (User context)
   * This method is named `findAll` and is distinct from admin's `findAllWithPagination`.
   * @param queryParams Tham số truy vấn
   * @returns Danh sách kho vật lý với phân trang
   */
  async findAll(queryParams: any): Promise<PaginatedResult<any>> {
    try {
      const {
        page = 1,
        limit = 10,
        offset = (page - 1) * limit,
        sortBy = 'warehouseId',
        sortDirection = 'ASC',
        search,
      } = queryParams;

      // Tạo query builder với join để lấy thông tin warehouse
      const queryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          'physical_warehouse.warehouse_id AS warehouse_id',
          'physical_warehouse.address AS address',
          'physical_warehouse.capacity AS capacity',
          'w.name AS name',
          'w.description AS description',
          'w.type AS type'
        ])
        .from(PhysicalWarehouse, 'physical_warehouse')
        .innerJoin('warehouse', 'w', 'w.warehouseId = physical_warehouse.warehouse_id');

      // Note: Warehouse không có cột userId - chúng là shared resources
      // Không filter theo userId như comment trong user-warehouse.service.ts

      // Đếm tổng số bản ghi
      const countQueryBuilder = this.dataSource
        .createQueryBuilder()
        .select('COUNT(*)')
        .from(PhysicalWarehouse, 'physical_warehouse')
        .innerJoin('warehouse', 'w', 'w.warehouseId = physical_warehouse.warehouse_id');

      // Xử lý tìm kiếm với SearchHelper
      if (search) {
        this.logger.log(`Tìm kiếm với từ khóa: "${search}"`);

        // Chỉ tìm kiếm theo name
        const normalizedSearch = SearchHelper.removeVietnameseAccents(search);
        this.logger.log(`Search term normalized: "${normalizedSearch}"`);

        // Tìm kiếm chỉ theo name với cả có dấu và không dấu
        const searchCondition = `(
          w.name ILIKE :searchOriginal OR
          translate(LOWER(w.name),
            'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ',
            'aaaaaaaaaaaaaaaaaeeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyyd'
          ) ILIKE :searchNormalized
        )`;

        const searchParams = {
          searchOriginal: `%${search}%`,
          searchNormalized: `%${normalizedSearch}%`
        };

        this.logger.log(`Search condition: ${searchCondition}`);
        this.logger.log(`Search parameters: ${JSON.stringify(searchParams)}`);

        queryBuilder.andWhere(searchCondition, searchParams);
        countQueryBuilder.andWhere(searchCondition, searchParams);
      }

      const total = await countQueryBuilder.getRawOne().then(result => parseInt(result.count));

      // Xử lý sắp xếp
      let orderField = `physical_warehouse.warehouse_id`; // Default sort field
      if (sortBy === 'name' || sortBy === 'description' || sortBy === 'type') {
        orderField = `w.${sortBy}`;
      } else if (sortBy === 'warehouseId') {
        orderField = `physical_warehouse.warehouse_id`;
      } else if (sortBy === 'address') {
        orderField = `physical_warehouse.address`;
      } else if (sortBy === 'capacity') {
        orderField = `physical_warehouse.capacity`;
      } else if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        // These fields don't exist in physical_warehouse table, use warehouseId instead
        orderField = `physical_warehouse.warehouse_id`;
      }

      queryBuilder
        .orderBy(orderField, sortDirection as 'ASC' | 'DESC')
        .offset(offset)
        .limit(limit);

      const rawItems = await queryBuilder.getRawMany();

      // Chuyển đổi dữ liệu raw thành format mong muốn
      const items = rawItems.map(item => ({
        id: item.warehouse_id, // id và warehouseId giống nhau
        warehouseId: item.warehouse_id,
        address: item.address,
        capacity: item.capacity,
        name: item.name,
        description: item.description,
        type: item.type,
        customFields: [], // Add empty custom fields array
      }));

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm kho vật lý (user findAll): ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kiếm kho vật lý (user findAll): ${error.message}`);
    }
  }

  // --- Admin specific or unique methods (or methods that don't clash by name) ---

  /**
   * Tìm kiếm kho vật lý theo ID kho với thông tin kho (Admin context)
   * This method is named `findByWarehouseIdWithWarehouse` and is unique to admin.
   * @param warehouseId ID của kho
   * @returns Kho vật lý với thông tin kho
   */
  async findByWarehouseIdWithWarehouse(warehouseId: number): Promise<any | null> {
    this.logger.log(`(Admin) Tìm kiếm kho vật lý với thông tin kho, warehouseId: ${warehouseId}`);

    const qb = this.createBaseQuery_admin(); // Uses admin's base query with 'physicalWarehouse' alias
    this.logger.log(`(Admin) Đã tạo query builder cơ bản`);

    this.logger.log(`(Admin) Thêm join với bảng warehouse`);
    qb.leftJoinAndSelect(
      'warehouse',
      'warehouse',
      'warehouse.warehouseId = physicalWarehouse.warehouseId' // Admin alias
    );

    this.logger.log(`(Admin) Thêm điều kiện lọc theo warehouseId: ${warehouseId}`);
    qb.where('physicalWarehouse.warehouseId = :warehouseId', { warehouseId }); // Admin alias

    const sqlQuery = qb.getQuery();
    const params = qb.getParameters();
    this.logger.log(`(Admin) Câu SQL để lấy chi tiết kho vật lý: ${sqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn: ${JSON.stringify(params)}`);

    try {
      const rawResult = await qb.getRawMany();
      this.logger.log(`(Admin) Kết quả truy vấn: ${rawResult.length} bản ghi`);

      if (rawResult.length > 0) {
        this.logger.log(`(Admin) Đã tìm thấy kho vật lý với warehouseId: ${warehouseId}`);
        this.logger.log(`(Admin) Mẫu dữ liệu thô: ${JSON.stringify(rawResult[0]).substring(0, 200)}...`);

        const item = rawResult[0];
        const formattedResult = {
          warehouseId: item.physicalWarehouse_warehouse_id,
          address: item.physicalWarehouse_address,
          capacity: item.physicalWarehouse_capacity,
          warehouse: {
            warehouseId: item.warehouse_warehouse_id,
            name: item.warehouse_name,
            description: item.warehouse_description,
            type: item.warehouse_type
          }
        };

        this.logger.log(`(Admin) Dữ liệu đã chuyển đổi: ${JSON.stringify(formattedResult).substring(0, 200)}...`);
        return formattedResult;
      } else {
        this.logger.log(`(Admin) Không tìm thấy kho vật lý với warehouseId: ${warehouseId}`);
        return null;
      }
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi thực hiện truy vấn lấy chi tiết kho vật lý: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tìm kiếm danh sách kho vật lý với phân trang (Admin context)
   * This method is named `findAllWithPagination` and is unique to admin.
   * @param queryDto DTO truy vấn
   * @returns Danh sách kho vật lý với phân trang
   */
  async findAllWithPagination(
    queryDto: QueryPhysicalWarehouseDto
  ): Promise<[any[], number]> {
    this.logger.log('(Admin) Tìm kiếm danh sách kho vật lý với phân trang');

    const { page, limit, address, minCapacity, maxCapacity, sortBy, sortDirection } = queryDto;
    this.logger.log(`(Admin) Tham số truy vấn: page=${page}, limit=${limit}, address=${address}, minCapacity=${minCapacity}, maxCapacity=${maxCapacity}, sortBy=${sortBy}, sortDirection=${sortDirection}`);

    const skip = (page - 1) * limit;
    this.logger.log(`(Admin) Phân trang: skip ${skip}, limit ${limit}`);

    const qb = this.createBaseQuery_admin(); // Uses admin's base query
    this.logger.log(`(Admin) Đã tạo query builder cơ bản`);

    this.logger.log(`(Admin) Thêm join với bảng warehouse`);
    qb.leftJoinAndSelect(
      'warehouse',
      'warehouse',
      'warehouse.warehouseId = physicalWarehouse.warehouseId' // Admin alias
    );

    if (address) {
      this.logger.log(`(Admin) Lọc theo địa chỉ: ${address}`);
      qb.andWhere('physicalWarehouse.address ILIKE :address', { address: `%${address}%` });
    }
    if (minCapacity !== undefined) {
      this.logger.log(`(Admin) Lọc theo sức chứa tối thiểu: ${minCapacity}`);
      qb.andWhere('physicalWarehouse.capacity >= :minCapacity', { minCapacity });
    }
    if (maxCapacity !== undefined) {
      this.logger.log(`(Admin) Lọc theo sức chứa tối đa: ${maxCapacity}`);
      qb.andWhere('physicalWarehouse.capacity <= :maxCapacity', { maxCapacity });
    }

    this.logger.log(`(Admin) Sắp xếp theo ${sortBy || 'warehouseId'} ${sortDirection}`);
    if (sortBy) {
      if (sortBy.startsWith('warehouse.')) {
        this.logger.log(`(Admin) Sắp xếp theo trường của bảng warehouse: ${sortBy}`);
        qb.orderBy(sortBy, sortDirection);
      } else if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        this.logger.log(`(Admin) Sắp xếp theo ${sortBy} không có trong bảng, sử dụng warehouseId thay thế`);
        qb.orderBy('physicalWarehouse.warehouseId', sortDirection); // Admin alias
      } else {
        this.logger.log(`(Admin) Sắp xếp theo trường của bảng physical_warehouse: ${sortBy}`);
        qb.orderBy(`physicalWarehouse.${sortBy}`, sortDirection); // Admin alias
      }
    } else {
      this.logger.log(`(Admin) Không có sortBy, sắp xếp mặc định theo warehouseId DESC`);
      qb.orderBy('physicalWarehouse.warehouseId', 'DESC'); // Admin alias
    }

    qb.offset(skip).limit(limit);

    const sqlQuery = qb.getQuery();
    const params = qb.getParameters();
    this.logger.log(`(Admin) Câu SQL để lấy dữ liệu: ${sqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn: ${JSON.stringify(params)}`);

    const countQb = this.createBaseQuery_admin(); // Count query for admin
    if (address) {
      countQb.andWhere('physicalWarehouse.address ILIKE :address', { address: `%${address}%` });
    }
    if (minCapacity !== undefined) {
      countQb.andWhere('physicalWarehouse.capacity >= :minCapacity', { minCapacity });
    }
    if (maxCapacity !== undefined) {
      countQb.andWhere('physicalWarehouse.capacity <= :maxCapacity', { maxCapacity });
    }

    const countSqlQuery = countQb.getQuery();
    const countParams = countQb.getParameters();
    this.logger.log(`(Admin) Câu SQL để đếm tổng số bản ghi: ${countSqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn count: ${JSON.stringify(countParams)}`);

    try {
      const total = await countQb.getCount();
      this.logger.log(`(Admin) Tổng số bản ghi: ${total}`);

      try {
        const rawItems = await qb.getRawMany();
        this.logger.log(`(Admin) Đã tìm thấy ${rawItems.length}/${total} kho vật lý`);

        if (rawItems.length > 0) {
          this.logger.log(`(Admin) Mẫu dữ liệu đầu tiên: ${JSON.stringify(rawItems[0]).substring(0, 200)}...`);
        }

        const items = rawItems.map(item => {
          return {
            warehouseId: item.physicalWarehouse_warehouse_id,
            address: item.physicalWarehouse_address,
            capacity: item.physicalWarehouse_capacity,
            warehouse: {
              warehouseId: item.warehouse_warehouse_id,
              name: item.warehouse_name,
              description: item.warehouse_description,
              type: item.warehouse_type
            }
          };
        });

        this.logger.log(`(Admin) Đã chuyển đổi ${items.length} bản ghi sang định dạng DTO`);
        if (items.length > 0) {
          this.logger.log(`(Admin) Mẫu dữ liệu đã chuyển đổi: ${JSON.stringify(items[0]).substring(0, 200)}...`);
        }
        return [items, total];
      } catch (error) {
        this.logger.error(`(Admin) Lỗi khi thực hiện truy vấn lấy dữ liệu: ${error.message}`);
        throw error;
      }
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi đếm tổng số bản ghi: ${error.message}`);
      throw error;
    }
  }
}