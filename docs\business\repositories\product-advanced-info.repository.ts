import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ProductAdvancedInfo } from '@modules/business/entities';
import { ProductTypeEnum } from '@modules/business/enums';

/**
 * Repository cho ProductAdvancedInfo
 * Xử lý các thao tác database liên quan đến thông tin nâng cao của sản phẩm
 */
@Injectable()
export class ProductAdvancedInfoRepository extends Repository<ProductAdvancedInfo> {
  private readonly logger = new Logger(ProductAdvancedInfoRepository.name);

  constructor(private dataSource: DataSource) {
    super(ProductAdvancedInfo, dataSource.createEntityManager());
  }

  /**
   * Tìm thông tin nâng cao theo ID sản phẩm
   * @param productId ID sản phẩm
   * @returns Thông tin nâng cao hoặc null
   */
  async findByProductId(productId: number): Promise<ProductAdvancedInfo | null> {
    try {
      this.logger.log(`Tìm thông tin nâng cao cho sản phẩm ID: ${productId}`);
      
      const result = await this.createQueryBuilder('pai')
        .where('pai.productId = :productId', { productId })
        .getOne();

      this.logger.log(`Tìm thấy thông tin nâng cao: ${result ? 'Có' : 'Không'}`);
      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm thông tin nâng cao cho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm thông tin nâng cao theo loại sản phẩm
   * @param productType Loại sản phẩm
   * @returns Danh sách thông tin nâng cao
   */
  async findByProductType(productType: ProductTypeEnum): Promise<ProductAdvancedInfo[]> {
    try {
      this.logger.log(`Tìm thông tin nâng cao theo loại sản phẩm: ${productType}`);
      
      const results = await this.createQueryBuilder('pai')
        .where('pai.productType = :productType', { productType })
        .getMany();

      this.logger.log(`Tìm thấy ${results.length} bản ghi thông tin nâng cao`);
      return results;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm thông tin nâng cao theo loại sản phẩm ${productType}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo hoặc cập nhật thông tin nâng cao
   * @param productAdvancedInfo Thông tin nâng cao cần lưu
   * @returns Thông tin nâng cao đã lưu
   */
  async createOrUpdate(productAdvancedInfo: Partial<ProductAdvancedInfo>): Promise<ProductAdvancedInfo> {
    try {
      this.logger.log(`Tạo/cập nhật thông tin nâng cao cho sản phẩm ID: ${productAdvancedInfo.productId}`);

      // Kiểm tra productId
      if (!productAdvancedInfo.productId) {
        throw new Error('ProductId là bắt buộc');
      }

      // Kiểm tra xem đã có thông tin nâng cao cho sản phẩm này chưa
      const existing = await this.findByProductId(productAdvancedInfo.productId);
      
      if (existing) {
        // Cập nhật thông tin hiện có
        Object.assign(existing, productAdvancedInfo);
        existing.updatedAt = Date.now();
        const result = await this.save(existing);
        this.logger.log(`Cập nhật thông tin nâng cao thành công cho sản phẩm ID: ${productAdvancedInfo.productId}`);
        return result;
      } else {
        // Tạo mới
        const newAdvancedInfo = this.create({
          ...productAdvancedInfo,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
        const result = await this.save(newAdvancedInfo);
        this.logger.log(`Tạo mới thông tin nâng cao thành công cho sản phẩm ID: ${productAdvancedInfo.productId}`);
        return result;
      }
    } catch (error) {
      this.logger.error(`Lỗi khi tạo/cập nhật thông tin nâng cao cho sản phẩm ${productAdvancedInfo.productId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa thông tin nâng cao theo ID sản phẩm
   * @param productId ID sản phẩm
   * @returns Kết quả xóa
   */
  async deleteByProductId(productId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa thông tin nâng cao cho sản phẩm ID: ${productId}`);
      
      const result = await this.createQueryBuilder()
        .delete()
        .from(ProductAdvancedInfo)
        .where('productId = :productId', { productId })
        .execute();

      const deleted = (result.affected ?? 0) > 0;
      this.logger.log(`Xóa thông tin nâng cao: ${deleted ? 'Thành công' : 'Không có dữ liệu để xóa'}`);
      return deleted;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin nâng cao cho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật số lượt mua
   * @param productId ID sản phẩm
   * @param increment Số lượng tăng thêm (mặc định là 1)
   * @returns Thông tin nâng cao đã cập nhật
   */
  async incrementPurchaseCount(productId: number, increment: number = 1): Promise<ProductAdvancedInfo | null> {
    try {
      this.logger.log(`Tăng số lượt mua cho sản phẩm ID: ${productId}, số lượng: ${increment}`);
      
      const advancedInfo = await this.findByProductId(productId);
      if (!advancedInfo) {
        this.logger.warn(`Không tìm thấy thông tin nâng cao cho sản phẩm ID: ${productId}`);
        return null;
      }

      advancedInfo.purchaseCount += increment;
      advancedInfo.updatedAt = Date.now();
      
      const result = await this.save(advancedInfo);
      this.logger.log(`Cập nhật số lượt mua thành công: ${result.purchaseCount}`);
      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật số lượt mua cho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
