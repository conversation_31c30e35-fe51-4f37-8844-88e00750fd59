import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserConvert, UserConvertCustomer } from '@modules/business/entities';
import { PaginatedResult } from '@common/response'; // Using user's path
import { QueryUserConvertDto as QueryUserConvertDtoUser } from '@modules/business/user/dto'; // Aliased user DTO
import { QueryUserConvertDto as QueryUserConvertDtoAdmin } from '@modules/business/admin/dto'; // Aliased admin DTO
import { AppException } from '@common/exceptions/app.exception'; // From user
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions'; // From user

/**
 * Interface for customer data from raw query (from user file)
 */
interface CustomerData {
  id: number;
  avatar: string;
  name: string;
  email: Record<string, string>;
  phone: string;
  platform: string;
  timezone: string;
  userId: number;
  agentId: string;
  metadata: Record<string, unknown>;
  createdAt: number;
  updatedAt: number;
}

/**
 * Repository xử lý các thao tác với bảng user_converts,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class UserConvertRepository extends Repository<UserConvert> {
  private readonly logger = new Logger(UserConvertRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserConvert, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder/truy vấn cơ bản cho bảng user_converts
   * (Identical in both files, one version kept)
   * @returns QueryBuilder cho bảng user_converts
   */
  private createBaseQuery(): SelectQueryBuilder<UserConvert> {
    return this.createQueryBuilder('convert');
  }

  // --- Methods from User File ---

  /**
   * Tìm bản ghi chuyển đổi theo ID với thông tin khách hàng (User context method)
   * @param id ID bản ghi chuyển đổi
   * @returns Bản ghi chuyển đổi với thông tin khách hàng hoặc null nếu không tìm thấy
   */
  async findByIdWithCustomer(id: number): Promise<UserConvert | null> {
    this.logger.log(`(User) Tìm bản ghi chuyển đổi với ID: ${id} kèm thông tin khách hàng`);

    const qb = this.createBaseQuery()
      .leftJoinAndSelect(
        UserConvertCustomer, // Using entity directly for type safety
        'customer',
        'convert.convertCustomerId = customer.id' // Adjusted alias to match TypeORM style better
      )
      .where('convert.id = :id', { id });

    // Using getOne() and TypeORM's relation loading is often cleaner
    // However, to preserve original raw query logic and mapping:
    const result = await qb.getRawOne();

    if (!result) {
      return null;
    }

    const convert = new UserConvert();
    convert.id = result.convert_id;
    convert.convertCustomerId = result.convert_convert_customer_id; // Check alias if using convert.customer relation
    convert.userId = result.convert_user_id;
    convert.conversionType = result.convert_conversion_type;
    convert.source = result.convert_source;
    convert.notes = result.convert_notes;
    convert.content = result.convert_content;
    convert.createdAt = result.convert_created_at ? Number(result.convert_created_at) : Date.now();
    convert.updatedAt = result.convert_updated_at ? Number(result.convert_updated_at) : Date.now();


    if (result.customer_id) {
      const customerData: CustomerData = {
        id: result.customer_id,
        avatar: result.customer_avatar,
        name: result.customer_name,
        email: typeof result.customer_email === 'string' ? JSON.parse(result.customer_email) : result.customer_email, // Handle potential JSON string
        phone: result.customer_phone,
        platform: result.customer_platform,
        timezone: result.customer_timezone,
        userId: result.customer_user_id,
        agentId: result.customer_agent_id,
        metadata: typeof result.customer_metadata === 'string' ? JSON.parse(result.customer_metadata) : result.customer_metadata, // Handle potential JSON string
        createdAt: result.customer_created_at, // Assuming these are epoch numbers or parsable date strings
        updatedAt: result.customer_updated_at
      };
      // This approach of manually attaching is okay but consider TypeORM relations for future
      (convert as any).convertCustomer = customerData;
    }

    return convert;
  }

  /**
   * Tìm bản ghi chuyển đổi theo ID (User context method)
   * @param id ID bản ghi chuyển đổi
   * @returns Bản ghi chuyển đổi hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<UserConvert | null> {
    this.logger.log(`(User) Tìm bản ghi chuyển đổi với ID: ${id}`);
    // This is similar to admin's findUserConvertById but kept distinct as per user's original file.
    const qb = this.createBaseQuery()
      .where('convert.id = :id', { id });
    return qb.getOne();
  }

  /**
   * Tìm danh sách bản ghi chuyển đổi với phân trang (User context method)
   * @param userId ID người dùng
   * @param queryDto Tham số truy vấn (from user DTO path)
   * @returns Danh sách bản ghi chuyển đổi với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserConvertDtoUser): Promise<PaginatedResult<UserConvert>> {
    try {
      const {
        page = 1,
        limit = 10,
        convertCustomerId,
        conversionType,
        source,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = queryDto;

      const skip = (page - 1) * limit;

      const query = this.createBaseQuery()
        .where('convert.userId = :userId', { userId });

      if (convertCustomerId) {
        query.andWhere('convert.convertCustomerId = :convertCustomerId', { convertCustomerId });
      }
      if (conversionType) {
        query.andWhere('convert.conversionType ILIKE :conversionType', { conversionType });
      }
      if (source) {
        query.andWhere('convert.source ILIKE :source', { source });
      }

      query.orderBy(`convert.${sortBy}`, sortDirection);
      query.skip(skip).take(limit);

      const [items, total] = await query.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`(User) Lỗi khi lấy danh sách bản ghi chuyển đổi: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_FIND_FAILED,
        `Lỗi khi lấy danh sách bản ghi chuyển đổi: ${error.message}`
      );
    }
  }

  // --- Methods from Admin File ---

  /**
   * Tìm bản ghi chuyển đổi khách hàng theo ID (Admin context method)
   * @param id ID của bản ghi chuyển đổi khách hàng
   * @returns Bản ghi chuyển đổi khách hàng hoặc null nếu không tìm thấy
   */
  async findUserConvertById(id: number): Promise<UserConvert | null> {
    this.logger.log(`(Admin) Tìm bản ghi chuyển đổi khách hàng với ID: ${id}`);
    return this.createBaseQuery()
      .where('convert.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm bản ghi chuyển đổi khách hàng theo ID với thông tin chi tiết khách hàng (Admin context method)
   * @param id ID của bản ghi chuyển đổi khách hàng
   * @returns Bản ghi chuyển đổi khách hàng với thông tin chi tiết khách hàng hoặc null nếu không tìm thấy
   */
  async findUserConvertByIdWithCustomer(id: number): Promise<any> { // Return type is 'any' as in original
    this.logger.log(`(Admin) Tìm bản ghi chuyển đổi khách hàng với ID: ${id} kèm thông tin chi tiết khách hàng`);

    try {
      const convert = await this.findUserConvertById(id); // Calls admin version
      if (!convert) {
        this.logger.log(`(Admin) Không tìm thấy bản ghi chuyển đổi với ID: ${id}`);
        return null;
      }
      this.logger.log(`(Admin) Đã tìm thấy bản ghi chuyển đổi với ID: ${id}`);

      if (convert.convertCustomerId) {
        this.logger.log(`(Admin) Tìm thông tin khách hàng với ID: ${convert.convertCustomerId}`);
        const customerQuery = this.dataSource
          .createQueryBuilder()
          .select('*') // Consider selecting specific columns
          .from(UserConvertCustomer, 'customer') // Using entity for 'from'
          .where('customer.id = :customerId', { customerId: convert.convertCustomerId });

        this.logger.log(`(Admin) SQL query: ${customerQuery.getSql()}`);
        const customer = await customerQuery.getRawOne();

        if (customer) {
          this.logger.log(`(Admin) Đã tìm thấy thông tin khách hàng với ID: ${convert.convertCustomerId}`);
          this.logger.log(`(Admin) Thông tin khách hàng: ${JSON.stringify(customer)}`);
          return {
            ...convert,
            customer: customer // Admin version nests the raw customer object
          };
        } else {
          this.logger.log(`(Admin) Không tìm thấy thông tin khách hàng với ID: ${convert.convertCustomerId}`);
        }
      }
      return convert;
    } catch (error) {
      this.logger.error(
        `(Admin) Lỗi khi tìm bản ghi chuyển đổi với thông tin khách hàng: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Tìm danh sách bản ghi chuyển đổi khách hàng với phân trang, tìm kiếm và lọc (Admin context method)
   * @param queryParams Tham số truy vấn (from admin DTO path)
   * @returns Danh sách bản ghi chuyển đổi khách hàng phân trang
   */
  async findUserConverts(queryParams: QueryUserConvertDtoAdmin): Promise<PaginatedResult<UserConvert>> {
    this.logger.log(`(Admin) Tìm kiếm bản ghi chuyển đổi với các tham số: ${JSON.stringify(queryParams)}`);

    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      convertCustomerId,
      userId,
      conversionType,
      source,
      createdAtFrom,
      createdAtTo
    } = queryParams;

    const skip = (page - 1) * limit;
    this.logger.log(`(Admin) Phân trang: skip ${skip}, limit ${limit}`);

    const queryBuilder = this.createBaseQuery();
    this.logger.log(`(Admin) Đã tạo query builder cơ bản`);

    if (search) {
      this.logger.log(`(Admin) Thêm điều kiện tìm kiếm với từ khóa: ${search}`);
      queryBuilder.andWhere('convert.notes ILIKE :search', { search: `%${search}%` });
    }
    if (convertCustomerId) {
      this.logger.log(`(Admin) Lọc theo ID khách hàng: ${convertCustomerId}`);
      queryBuilder.andWhere('convert.convertCustomerId = :convertCustomerId', { convertCustomerId });
    }
    if (userId) {
      this.logger.log(`(Admin) Lọc theo ID người dùng: ${userId}`);
      queryBuilder.andWhere('convert.userId = :userId', { userId });
    }
    if (conversionType) {
      this.logger.log(`(Admin) Lọc theo loại chuyển đổi: ${conversionType}`);
      queryBuilder.andWhere('convert.conversionType ILIKE :conversionType', { conversionType });
    }
    if (source) {
      this.logger.log(`(Admin) Lọc theo nguồn: ${source}`);
      queryBuilder.andWhere('convert.source ILIKE :source', { source });
    }
    if (createdAtFrom) {
      this.logger.log(`(Admin) Lọc theo thời gian tạo từ: ${createdAtFrom}`);
      queryBuilder.andWhere('convert.createdAt >= :createdAtFrom', { createdAtFrom: Number(createdAtFrom) }); // Sử dụng số thay vì Date object
    }
    if (createdAtTo) {
      this.logger.log(`(Admin) Lọc theo thời gian tạo đến: ${createdAtTo}`);
      queryBuilder.andWhere('convert.createdAt <= :createdAtTo', { createdAtTo: Number(createdAtTo) }); // Sử dụng số thay vì Date object
    }

    this.logger.log(`(Admin) Đang đếm tổng số bản ghi`);
    const totalItems = await queryBuilder.getCount();
    this.logger.log(`(Admin) Tổng số bản ghi: ${totalItems}`);

    this.logger.log(`(Admin) Sắp xếp theo ${sortBy} ${sortDirection}`);
    queryBuilder.orderBy(`convert.${sortBy}`, sortDirection).skip(skip).take(limit);

    this.logger.log(`(Admin) Đang thực hiện truy vấn`);
    const items = await queryBuilder.getMany();
    this.logger.log(`(Admin) Đã tìm thấy ${items.length}/${totalItems} bản ghi chuyển đổi khách hàng`);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page
      }
    };
  }
}