import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserShopInfo } from '../entities/user-shop-info.entity';

/**
 * Repository cho UserShopInfo
 */
@Injectable()
export class UserShopInfoRepository {
  private readonly logger = new Logger(UserShopInfoRepository.name);

  constructor(
    @InjectRepository(UserShopInfo)
    private readonly repository: Repository<UserShopInfo>,
  ) {}

  /**
   * Tìm thông tin shop theo userId
   * @param userId ID người dùng
   * @returns Thông tin shop hoặc null
   */
  async findByUserId(userId: number): Promise<UserShopInfo | null> {
    try {
      this.logger.log(`Tìm thông tin shop cho userId: ${userId}`);
      
      return await this.repository.findOne({
        where: { userId }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm thông tin shop cho userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Tạo thông tin shop mới
   * @param shopData Dữ liệu shop
   * @returns Thông tin shop đã tạo
   */
  async create(shopData: Partial<UserShopInfo>): Promise<UserShopInfo> {
    try {
      this.logger.log(`Tạo thông tin shop mới cho userId: ${shopData.userId}`);
      
      const shopInfo = this.repository.create({
        ...shopData,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      const savedShopInfo = await this.repository.save(shopInfo);
      this.logger.log(`Đã tạo thông tin shop với ID: ${savedShopInfo.id}`);
      
      return savedShopInfo;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo thông tin shop: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo thông tin shop: ${error.message}`);
    }
  }

  /**
   * Cập nhật thông tin shop
   * @param userId ID người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns Thông tin shop đã cập nhật
   */
  async updateByUserId(userId: number, updateData: Partial<UserShopInfo>): Promise<UserShopInfo> {
    try {
      this.logger.log(`Cập nhật thông tin shop cho userId: ${userId}`);
      
      const dataToUpdate = {
        ...updateData,
        updatedAt: Date.now()
      };

      await this.repository.update({ userId }, dataToUpdate);
      
      const updatedShopInfo = await this.findByUserId(userId);
      if (!updatedShopInfo) {
        throw new Error(`Không tìm thấy thông tin shop sau khi cập nhật cho userId: ${userId}`);
      }

      this.logger.log(`Đã cập nhật thông tin shop cho userId: ${userId}`);
      return updatedShopInfo;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi cập nhật thông tin shop cho userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Tạo hoặc cập nhật thông tin shop
   * @param userId ID người dùng
   * @param shopData Dữ liệu shop
   * @returns Thông tin shop
   */
  async upsert(userId: number, shopData: Partial<UserShopInfo>): Promise<UserShopInfo> {
    try {
      this.logger.log(`Upsert thông tin shop cho userId: ${userId}`);
      
      const existingShopInfo = await this.findByUserId(userId);
      
      if (existingShopInfo) {
        return await this.updateByUserId(userId, shopData);
      } else {
        return await this.create({ ...shopData, userId });
      }
    } catch (error) {
      this.logger.error(`Lỗi khi upsert thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi upsert thông tin shop cho userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Xóa thông tin shop
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  async deleteByUserId(userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa thông tin shop cho userId: ${userId}`);
      
      const result = await this.repository.delete({ userId });
      const deleted = !!(result.affected && result.affected > 0);

      if (deleted) {
        this.logger.log(`Đã xóa thông tin shop cho userId: ${userId}`);
      } else {
        this.logger.warn(`Không tìm thấy thông tin shop để xóa cho userId: ${userId}`);
      }

      return deleted;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa thông tin shop cho userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Tìm shop theo ID và userId
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Thông tin shop hoặc null
   */
  async findByIdAndUserId(shopId: number, userId: number): Promise<UserShopInfo | null> {
    try {
      this.logger.log(`Tìm shop với ID: ${shopId} cho userId: ${userId}`);

      return await this.repository.findOne({
        where: { id: shopId, userId }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm shop với ID ${shopId} cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm shop với ID ${shopId} cho userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách tất cả shop của user
   * @param userId ID người dùng
   * @returns Danh sách shop
   */
  async findAllByUserId(userId: number): Promise<UserShopInfo[]> {
    try {
      this.logger.log(`Lấy danh sách shop cho userId: ${userId}`);

      return await this.repository.find({
        where: { userId },
        order: { createdAt: 'DESC' }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách shop cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi lấy danh sách shop cho userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Cập nhật shop theo ID
   * @param shopId ID shop
   * @param userId ID người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns Thông tin shop đã cập nhật
   */
  async updateById(shopId: number, userId: number, updateData: Partial<UserShopInfo>): Promise<UserShopInfo> {
    try {
      this.logger.log(`Cập nhật shop với ID: ${shopId} cho userId: ${userId}`);

      const dataToUpdate = {
        ...updateData,
        updatedAt: Date.now()
      };

      await this.repository.update({ id: shopId, userId }, dataToUpdate);

      const updatedShopInfo = await this.findByIdAndUserId(shopId, userId);
      if (!updatedShopInfo) {
        throw new Error(`Không tìm thấy shop sau khi cập nhật với ID: ${shopId} cho userId: ${userId}`);
      }

      this.logger.log(`Đã cập nhật shop với ID: ${shopId} cho userId: ${userId}`);
      return updatedShopInfo;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật shop với ID ${shopId} cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi cập nhật shop với ID ${shopId} cho userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Xóa shop theo ID
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  async deleteById(shopId: number, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa shop với ID: ${shopId} cho userId: ${userId}`);

      const result = await this.repository.delete({ id: shopId, userId });
      const deleted = !!(result.affected && result.affected > 0);

      if (deleted) {
        this.logger.log(`Đã xóa shop với ID: ${shopId} cho userId: ${userId}`);
      } else {
        this.logger.warn(`Không tìm thấy shop để xóa với ID: ${shopId} cho userId: ${userId}`);
      }

      return deleted;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa shop với ID ${shopId} cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa shop với ID ${shopId} cho userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Kiểm tra thông tin shop có tồn tại không
   * @param userId ID người dùng
   * @returns True nếu tồn tại
   */
  async exists(userId: number): Promise<boolean> {
    try {
      this.logger.log(`Kiểm tra thông tin shop tồn tại cho userId: ${userId}`);

      const count = await this.repository.count({
        where: { userId }
      });

      return count > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi kiểm tra thông tin shop cho userId ${userId}: ${error.message}`);
    }
  }
}
