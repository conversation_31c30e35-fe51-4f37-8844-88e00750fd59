import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { WarehouseCustomField, CustomField, Warehouse } from '@modules/business/entities'; // Added Warehouse for admin joins
import { QueryWarehouseCustomFieldDto } from '@modules/business/admin/dto/warehouse'; // Admin DTO for pagination

/**
 * Repository xử lý truy vấn dữ liệu cho entity WarehouseCustomField,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class WarehouseCustomFieldRepository extends Repository<WarehouseCustomField> {
  private readonly logger = new Logger(WarehouseCustomFieldRepository.name);

  constructor(private dataSource: DataSource) {
    super(WarehouseCustomField, dataSource.createEntityManager());
  }

  // --- Base Query Builders ---

  /**
   * Tạo query builder cơ bản cho warehouse custom field (User context version)
   * @returns Query builder
   */
  private createBaseQuery_user(): SelectQueryBuilder<WarehouseCustomField> {
    return this.createQueryBuilder('warehouseCustomField');
  }

  /**
   * Tạo query builder cơ bản cho warehouse custom field (Admin context version)
   * @returns SelectQueryBuilder<WarehouseCustomField>
   */
  private createBaseQuery_admin(): SelectQueryBuilder<WarehouseCustomField> {
    this.logger.log('(Admin) Tạo query builder cơ bản cho warehouse custom field');
    const qb = this.createQueryBuilder('warehouseCustomField'); // Alias 'warehouseCustomField'
    this.logger.log(`(Admin) Đã tạo query builder với alias 'warehouseCustomField'`);
    return qb;
  }

  // --- Methods with same name, differentiated by suffix ---

  /**
   * Tìm trường tùy chỉnh của kho theo ID kho và ID trường (User context version)
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh hoặc null nếu không tìm thấy
   */
  async findByWarehouseIdAndFieldId_user(warehouseId: number, fieldId: number): Promise<WarehouseCustomField | null> {
    try {
      return await this.createBaseQuery_user()
        .where('warehouseCustomField.warehouseId = :warehouseId', { warehouseId })
        .andWhere('warehouseCustomField.fieldId = :fieldId', { fieldId })
        .getOne();
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tìm trường tùy chỉnh của kho theo warehouseId=${warehouseId} và fieldId=${fieldId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm trường tùy chỉnh của kho theo warehouseId=${warehouseId} và fieldId=${fieldId}: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm trường tùy chỉnh của kho theo ID kho và ID trường (Admin context version)
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Trường tùy chỉnh của kho
   */
  async findByWarehouseIdAndFieldId_admin(
    warehouseId: number,
    fieldId: number
  ): Promise<WarehouseCustomField | null> {
    this.logger.log(`(Admin) Tìm kiếm trường tùy chỉnh của kho với warehouseId: ${warehouseId} và fieldId: ${fieldId}`);
    const qb = this.createBaseQuery_admin();
    this.logger.log(`(Admin) Đã tạo query builder cơ bản`);
    this.logger.log(`(Admin) Thêm điều kiện lọc theo warehouseId: ${warehouseId} và fieldId: ${fieldId}`);
    qb.where('warehouseCustomField.warehouseId = :warehouseId', { warehouseId })
      .andWhere('warehouseCustomField.fieldId = :fieldId', { fieldId });

    const sqlQuery = qb.getQuery();
    const params = qb.getParameters();
    this.logger.log(`(Admin) Câu SQL để lấy trường tùy chỉnh: ${sqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn: ${JSON.stringify(params)}`);

    try {
      const result = await qb.getOne();
      if (result) {
        this.logger.log(`(Admin) Đã tìm thấy trường tùy chỉnh với warehouseId: ${warehouseId} và fieldId: ${fieldId}`);
        this.logger.log(`(Admin) Dữ liệu: ${JSON.stringify(result)}`);
      } else {
        this.logger.log(`(Admin) Không tìm thấy trường tùy chỉnh với warehouseId: ${warehouseId} và fieldId: ${fieldId}`);
      }
      return result;
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi thực hiện truy vấn lấy trường tùy chỉnh: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách trường tùy chỉnh của kho theo ID kho (User context version)
   * @param warehouseId ID của kho
   * @returns Danh sách trường tùy chỉnh
   */
  async findByWarehouseId_user(warehouseId: number): Promise<WarehouseCustomField[]> {
    try {
      return await this.createBaseQuery_user()
        .where('warehouseCustomField.warehouseId = :warehouseId', { warehouseId })
        .getMany();
    } catch (error) {
      this.logger.error(`(User) Lỗi khi lấy danh sách trường tùy chỉnh của kho theo warehouseId=${warehouseId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi lấy danh sách trường tùy chỉnh của kho theo warehouseId=${warehouseId}: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm danh sách trường tùy chỉnh của kho theo ID kho (Admin context version)
   * @param warehouseId ID của kho
   * @returns Danh sách trường tùy chỉnh của kho (custom structure)
   */
  async findByWarehouseId_admin(warehouseId: number): Promise<any[]> {
    this.logger.log(`(Admin) Tìm kiếm danh sách trường tùy chỉnh của kho với warehouseId: ${warehouseId}`);
    const qb = this.createBaseQuery_admin();
    this.logger.log(`(Admin) Đã tạo query builder cơ bản`);
    this.logger.log(`(Admin) Thêm join với bảng custom_fields`);
    // Admin joins with 'custom_fields' table directly, not the entity by relation path
    qb.leftJoin(CustomField, 'customField', 'customField.id = warehouseCustomField.fieldId');

    this.logger.log(`(Admin) Thêm điều kiện lọc theo warehouseId: ${warehouseId}`);
    qb.where('warehouseCustomField.warehouseId = :warehouseId', { warehouseId });
    this.logger.log(`(Admin) Chọn các trường cần lấy với alias rõ ràng`);
    qb.select([ // Uses entity property names that TypeORM maps to columns
      'warehouseCustomField.fieldId AS field_id', // Raw query aliases
      'customField.label AS label',
      'customField.type AS type',
      'warehouseCustomField.value AS value'
    ]);

    const sqlQuery = qb.getQuery();
    const params = qb.getParameters();
    this.logger.log(`(Admin) Câu SQL để lấy danh sách trường tùy chỉnh: ${sqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn: ${JSON.stringify(params)}`);

    try {
      const result = await qb.getRawMany();
      this.logger.log(`(Admin) Đã tìm thấy ${result.length} trường tùy chỉnh cho kho với warehouseId: ${warehouseId}`);
      if (result.length > 0) {
        this.logger.log(`(Admin) Mẫu dữ liệu thô đầu tiên: ${JSON.stringify(result[0]).substring(0, 200)}...`);
        this.logger.log(`(Admin) Cấu trúc dữ liệu thô chi tiết: ${JSON.stringify(Object.keys(result[0]))}`);
      }
      const formattedResult = result.map(item => ({
        fieldId: item.field_id, // Accessing by alias from raw query
        label: item.label,
        type: item.type,
        value: item.value
      }));
      this.logger.log(`(Admin) Đã chuyển đổi ${formattedResult.length} trường tùy chỉnh sang định dạng DTO`);
      return formattedResult;
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi thực hiện truy vấn lấy danh sách trường tùy chỉnh: ${error.message}`);
      throw error;
    }
  }

  // --- User specific or unique methods ---

  /**
   * Xóa trường tùy chỉnh của kho theo ID kho (User context)
   * @param warehouseId ID của kho
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByWarehouseId(warehouseId: number): Promise<number> {
    try {
      const result = await this.createBaseQuery_user()
        .delete()
        .where('warehouseCustomField.warehouseId = :warehouseId', { warehouseId })
        .execute();
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`(User) Lỗi khi xóa trường tùy chỉnh của kho theo warehouseId=${warehouseId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa trường tùy chỉnh của kho theo warehouseId=${warehouseId}: ${error.message}`);
    }
  }

  /**
   * Xóa trường tùy chỉnh của kho theo ID kho và ID trường (User context)
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Số lượng bản ghi đã xóa
   */
  async deleteByWarehouseIdAndFieldId(warehouseId: number, fieldId: number): Promise<number> {
    try {
      const result = await this.createBaseQuery_user()
        .delete()
        .where('warehouseCustomField.warehouseId = :warehouseId', { warehouseId })
        .andWhere('warehouseCustomField.fieldId = :fieldId', { fieldId })
        .execute();
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`(User) Lỗi khi xóa trường tùy chỉnh của kho theo warehouseId=${warehouseId} và fieldId=${fieldId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa trường tùy chỉnh của kho theo warehouseId=${warehouseId} và fieldId=${fieldId}: ${error.message}`);
    }
  }

  /**
   * Tạo trường tùy chỉnh mới cho kho (User context)
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @param value Giá trị của trường tùy chỉnh
   * @returns Trường tùy chỉnh đã tạo
   */
  async createCustomField(warehouseId: number, fieldId: number, value: any): Promise<WarehouseCustomField> {
    try {
      const customField = this.create({ warehouseId, fieldId, value });
      return await this.save(customField);
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tạo trường tùy chỉnh cho kho với warehouseId=${warehouseId} và fieldId=${fieldId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo trường tùy chỉnh cho kho với warehouseId=${warehouseId} và fieldId=${fieldId}: ${error.message}`);
    }
  }

  /**
   * Cập nhật trường tùy chỉnh của kho (User context)
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @param value Giá trị mới của trường tùy chỉnh
   * @returns Trường tùy chỉnh đã cập nhật
   */
  async updateCustomField(warehouseId: number, fieldId: number, value: any): Promise<WarehouseCustomField> {
    try {
      const customField = await this.findByWarehouseIdAndFieldId_user(warehouseId, fieldId); // Call user version
      if (!customField) {
        throw new Error(`Không tìm thấy trường tùy chỉnh với warehouseId=${warehouseId} và fieldId=${fieldId}`);
      }
      customField.value = value;
      return await this.save(customField);
    } catch (error) {
      this.logger.error(`(User) Lỗi khi cập nhật trường tùy chỉnh của kho với warehouseId=${warehouseId} và fieldId=${fieldId}: ${error.message}`, error.stack);
      // Preserving original error throwing style for user context
      if (error instanceof Error && error.message.startsWith('Không tìm thấy')) {
        throw error;
      }
      throw new Error(`Lỗi khi cập nhật trường tùy chỉnh của kho với warehouseId=${warehouseId} và fieldId=${fieldId}: ${error.message}`);
    }
  }

  // --- Admin specific or unique methods ---

  /**
   * Tìm kiếm trường tùy chỉnh của kho theo ID kho và ID trường với thông tin chi tiết (Admin context)
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Trường tùy chỉnh của kho với thông tin chi tiết (custom structure)
   */
  async findDetailByWarehouseIdAndFieldId(
    warehouseId: number,
    fieldId: number
  ): Promise<any | null> { // Return type any[] in original, but likely meant any or null
    this.logger.log(`(Admin) Tìm kiếm chi tiết trường tùy chỉnh của kho với warehouseId: ${warehouseId} và fieldId: ${fieldId}`);
    const qb = this.createBaseQuery_admin();
    this.logger.log(`(Admin) Đã tạo query builder cơ bản`);
    this.logger.log(`(Admin) Thêm join với bảng custom_fields`);
    qb.leftJoin(CustomField, 'customField', 'customField.id = warehouseCustomField.fieldId');
    this.logger.log(`(Admin) Thêm join với bảng warehouse`);
    qb.leftJoin(Warehouse, 'warehouse', 'warehouse.warehouseId = warehouseCustomField.warehouseId'); // Assuming Warehouse entity
    this.logger.log(`(Admin) Thêm điều kiện lọc theo warehouseId: ${warehouseId} và fieldId: ${fieldId}`);
    qb.where('warehouseCustomField.warehouseId = :warehouseId', { warehouseId })
      .andWhere('warehouseCustomField.fieldId = :fieldId', { fieldId });
    this.logger.log(`(Admin) Chọn các trường cần lấy với alias rõ ràng`);
    qb.select([ // Uses entity property names for select, TypeORM maps to columns, then aliases for raw query
      'warehouseCustomField.warehouseId AS warehouse_id',
      'warehouseCustomField.fieldId AS field_id',
      'warehouseCustomField.value AS value',
      'warehouse.name AS warehouse_name',
      'customField.label AS field_label',
      'customField.type AS type',
      'customField.required AS required',
      'customField.configJson AS config_json'
    ]);

    const sqlQuery = qb.getQuery();
    const params = qb.getParameters();
    this.logger.log(`(Admin) Câu SQL để lấy chi tiết trường tùy chỉnh: ${sqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn: ${JSON.stringify(params)}`);

    try {
      // Original used getRawMany(), if it's unique, getRawOne() is more appropriate.
      // Keeping getRawMany() and taking first element if exists to match original logic closely.
      const rawResult = await qb.getRawOne(); // Changed to getRawOne as it's detail for specific ids
      this.logger.log(`(Admin) Kết quả truy vấn: ${rawResult ? 1 : 0} bản ghi`);

      if (rawResult) { // If rawResult is not null
        this.logger.log(`(Admin) Đã tìm thấy trường tùy chỉnh với warehouseId: ${warehouseId} và fieldId: ${fieldId}`);
        this.logger.log(`(Admin) Mẫu dữ liệu thô: ${JSON.stringify(rawResult).substring(0, 200)}...`);
        // Original returned an array, now returns single object or null
        const formattedResult = {
          warehouseId: rawResult.warehouse_id,
          fieldId: rawResult.field_id,
          value: rawResult.value,
          warehouseName: rawResult.warehouse_name,
          fieldLabel: rawResult.field_label,
          type: rawResult.type,
          required: rawResult.required,
          configJson: rawResult.config_json
        };
        this.logger.log(`(Admin) Dữ liệu đã chuyển đổi: ${JSON.stringify(formattedResult).substring(0, 200)}...`);
        return formattedResult;
      } else {
        this.logger.log(`(Admin) Không tìm thấy trường tùy chỉnh với warehouseId: ${warehouseId} và fieldId: ${fieldId}`);
        return null;
      }
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi thực hiện truy vấn lấy chi tiết trường tùy chỉnh: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tìm kiếm danh sách trường tùy chỉnh của kho với phân trang (Admin context)
   * @param queryDto DTO truy vấn
   * @returns Danh sách trường tùy chỉnh của kho với phân trang (custom structure)
   */
  async findAllWithPagination(
    queryDto: QueryWarehouseCustomFieldDto // Admin DTO
  ): Promise<[any[], number]> {
    this.logger.log('(Admin) Tìm kiếm danh sách trường tùy chỉnh của kho với phân trang');
    const { page, limit, warehouseId, fieldId, sortBy, sortDirection } = queryDto;
    this.logger.log(`(Admin) Tham số truy vấn: page=${page}, limit=${limit}, warehouseId=${warehouseId}, fieldId=${fieldId}, sortBy=${sortBy}, sortDirection=${sortDirection}`);
    const skip = (page - 1) * limit;

    const qb = this.createBaseQuery_admin();
    this.logger.log(`(Admin) Thêm join với bảng custom_fields`);
    qb.leftJoin(CustomField, 'customField', 'customField.id = warehouseCustomField.fieldId');
    this.logger.log(`(Admin) Thêm join với bảng warehouse`);
    qb.leftJoin(Warehouse, 'warehouse', 'warehouse.warehouseId = warehouseCustomField.warehouseId');
    this.logger.log(`(Admin) Chọn các trường cần lấy với alias rõ ràng`);
    qb.select([
      'warehouseCustomField.warehouseId AS warehouse_id',
      'warehouseCustomField.fieldId AS field_id',
      'warehouseCustomField.value AS value',
      'warehouse.name AS warehouse_name',
      'customField.label AS field_label'
    ]);

    if (warehouseId) {
      qb.andWhere('warehouseCustomField.warehouseId = :warehouseId', { warehouseId });
    }
    if (fieldId) {
      qb.andWhere('warehouseCustomField.fieldId = :fieldId', { fieldId });
    }

    if (sortBy) {
      // For joined fields, need to use alias of joined table if sorting on them
      // e.g. if sortBy is 'warehouseName', it should be 'warehouse.name'
      // or if sortBy is 'fieldLabel', it should be 'customField.label'
      // The current admin logic for sortBy might not work correctly for joined fields.
      // This simplified version assumes sortBy refers to warehouseCustomField properties.
      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        qb.orderBy('warehouseCustomField.warehouseId', sortDirection);
      } else if (sortBy === 'warehouseName') {
        qb.orderBy('warehouse.name', sortDirection);
      } else if (sortBy === 'fieldLabel') {
        qb.orderBy('customField.label', sortDirection);
      }
      else {
        qb.orderBy(`warehouseCustomField.${sortBy}`, sortDirection);
      }
    } else {
      qb.orderBy('warehouseCustomField.warehouseId', 'DESC');
    }
    qb.offset(skip).limit(limit);

    const countQb = this.createBaseQuery_admin();
    // Apply same filters to count query
    if (warehouseId) { countQb.andWhere('warehouseCustomField.warehouseId = :warehouseId', { warehouseId }); }
    if (fieldId) { countQb.andWhere('warehouseCustomField.fieldId = :fieldId', { fieldId }); }
    // If joins are necessary for filtering in count query, they must be added here too.


    try {
      const total = await countQb.getCount();
      const rawItems = await qb.getRawMany();
      this.logger.log(`(Admin) Đã tìm thấy ${rawItems.length}/${total} trường tùy chỉnh của kho`);

      const items = rawItems.map(item => ({
        warehouseId: item.warehouse_id,
        fieldId: item.field_id,
        value: item.value,
        warehouseName: item.warehouse_name,
        fieldLabel: item.field_label
      }));
      return [items, total];
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi thực hiện truy vấn hoặc đếm: ${error.message}`);
      throw error;
    }
  }
}