import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServicesModule } from '@shared/services/services.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { IntegrationUserModule } from '@modules/integration/user/integration-user.module';
import {
  UserProduct,
  UserClassification,
  CustomField,
  PhysicalWarehouse,
  UserOrder,
  VirtualWarehouse,
  Warehouse,
  Inventory,
  UserConvert,
  UserConvertCustomer,
  UserAddress,
  ProductAdvancedInfo,
  UserShopInfo,

  CustomerFacebook,
  CustomerWeb,
  File,
  Folder
} from '../entities';
import { Product } from '@modules/marketplace/entities';
import { Agent } from '@modules/agent/entities/agent.entity';

import {
  UserProductRepository,
  UserOrderRepository,
  WarehouseRepository,
  PhysicalWarehouseRepository,
  VirtualWarehouseRepository,
  InventoryRepository,
  UserClassificationRepository,
  CustomFieldRepository,
  UserConvertRepository,
  UserConvertCustomerRepository,
  UserAddressRepository,
  ProductAdvancedInfoRepository,
  UserShopInfoRepository,

  CustomerFacebookRepository,
  CustomerWebRepository,
  FileRepository,
  FolderRepository,
  BusinessReportRepository
} from '../repositories';
import { ProductRepository } from '@modules/marketplace/repositories';

import {
  UserProductController,
  UserOrderController,
  UserOrderTrackingController,
  UserShopInfoController,
  UserWarehouseController,
  UserInventoryController,
  UserPhysicalWarehouseController,
  CustomFieldController,
  BusinessIntegrationController,
  ClassificationController,
  UserConvertController,
  UserConvertCustomerController,
  UserFileController,
  UserFolderController,
  UserVirtualWarehouseController,
  BusinessReportController,
} from './controllers';
import { GHTKShipmentController } from '@modules/business/user/controllers';
import { GHNShipmentController } from '@modules/business/user/controllers';
import { ShippingWebhookController } from './controllers/user-order-tracking.controller';

import {
  UserProductService,
  UserOrderService,
  UserWarehouseService,
  UserInventoryService,
  UserPhysicalWarehouseService,
  CustomFieldService,
  BusinessIntegrationService,
  ClassificationService,
  UserConvertService,
  UserConvertCustomerService,
  UserFileService,
  UserFolderService,
  UserVirtualWarehouseService,
  BusinessReportService,
  UserShopInfoService,
} from './services';
import { GHTKShipmentService } from '@modules/business/user/services';
import { GHNShipmentService } from './services/ghn-shipment.service';
import { GHTKConfigValidationHelper } from './helpers/ghtk-config-validation.helper';
import { GHNConfigValidationHelper } from './helpers/ghn-config-validation.helper';

import { ValidationHelper, UserProductHelper, BusinessReportHelper, MetadataHelper, ProductValidationHelper } from './helpers';
import { GHTKIntegrationHelper } from './helpers/ghtk-integration.helper';
import { BusinessIntegrationInterceptor } from './interceptors/business-integration.interceptor';

/**
 * Module quản lý chức năng business cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProduct,
      UserClassification,
      CustomField,
      PhysicalWarehouse,
      UserOrder,
      VirtualWarehouse,
      Warehouse,
      Inventory,
      UserConvert,
      UserConvertCustomer,
      UserAddress,
      ProductAdvancedInfo,
      UserShopInfo,

      CustomerFacebook,
      CustomerWeb,
      Product,
      Agent,
      File,
      Folder
    ]),
    ServicesModule,
    IntegrationUserModule
  ],
  controllers: [
    UserProductController,
    UserOrderController,
    UserOrderTrackingController,
    UserShopInfoController,
    ShippingWebhookController,
    UserWarehouseController,
    UserInventoryController,
    UserPhysicalWarehouseController,
    CustomFieldController,
    BusinessIntegrationController,
    ClassificationController,
    UserConvertController,
    UserConvertCustomerController,
    UserFileController,
    UserFolderController,
    UserVirtualWarehouseController,
    BusinessReportController,
    GHTKShipmentController,
    GHNShipmentController
  ],
  providers: [
    // Repositories
    UserProductRepository,
    UserOrderRepository,
    WarehouseRepository,
    PhysicalWarehouseRepository,
    VirtualWarehouseRepository,
    InventoryRepository,
    UserClassificationRepository,
    CustomFieldRepository,
    UserConvertRepository,
    UserConvertCustomerRepository,
    UserAddressRepository,
    ProductAdvancedInfoRepository,
    UserShopInfoRepository,

    CustomerFacebookRepository,
    CustomerWebRepository,
    ProductRepository,
    FileRepository,
    FolderRepository,
    BusinessReportRepository,

    // Helpers
    ValidationHelper,
    UserProductHelper,
    BusinessReportHelper,
    MetadataHelper,
    ProductValidationHelper,

    // Services
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    BusinessReportService,
    GHTKShipmentService,
    GHNShipmentService,
    UserShopInfoService,
    GHTKConfigValidationHelper,
    GHNConfigValidationHelper,
    GHTKIntegrationHelper,

    // Interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: BusinessIntegrationInterceptor
    }
  ],
  exports: [
    TypeOrmModule,
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    BusinessReportService,
    GHTKShipmentService,
    GHNShipmentService,
    UserProductRepository,
  ],
})
export class BusinessUserModule {}
