import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { Api<PERSON><PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse, ApiExtraModels } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators';
import { User } from '@modules/user/entities/user.entity';
import { BusinessReportService } from '@modules/business/user/services';
import { ApiResponseDto } from '@common/response/api-response-dto';

import {
  ReportOverviewQueryDto,
  ReportOverviewResponseDto,
  SalesChartQueryDto,
  SalesChartResponseDto,
  OrdersChartQueryDto,
  OrdersChartResponseDto,
  CustomersChartQueryDto,
  CustomersChartResponseDto,
  ProductsChartQueryDto,
  ProductsChartResponseDto,
  TopSellingProductsQueryDto,
  TopSellingProductsResponseDto,
  PotentialCustomersQueryDto,
  PotentialCustomersResponseDto
} from '../dto/report';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các endpoint báo cáo business cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_REPORT)
@Controller('user/business/reports')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  ReportOverviewResponseDto,
  SalesChartResponseDto,
  OrdersChartResponseDto,
  CustomersChartResponseDto,
  ProductsChartResponseDto,
  TopSellingProductsResponseDto,
  PotentialCustomersResponseDto
)
export class BusinessReportController {
  constructor(
    private readonly businessReportService: BusinessReportService,
  ) {}

  /**
   * Lấy dữ liệu tổng quan báo cáo
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy dữ liệu tổng quan báo cáo',
    description: 'API lấy dữ liệu tổng quan bao gồm tổng doanh thu, tổng đơn hàng, khách hàng mới và so sánh với kỳ trước'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu tổng quan thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/ReportOverviewResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ'
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực'
  })
  async getOverview(
    @CurrentUser() user: User,
    @Query() query: ReportOverviewQueryDto
  ): Promise<ApiResponseDto<ReportOverviewResponseDto>> {
    const data = await this.businessReportService.getReportOverview(user.id, query);
    return ApiResponseDto.success(data, 'Lấy dữ liệu tổng quan thành công');
  }

  /**
   * Lấy dữ liệu biểu đồ doanh thu
   */
  @Get('sales-chart')
  @ApiOperation({
    summary: 'Lấy dữ liệu biểu đồ doanh thu',
    description: 'API lấy dữ liệu biểu đồ doanh thu theo thời gian với các tùy chọn nhóm dữ liệu'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu biểu đồ doanh thu thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/SalesChartResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ'
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực'
  })
  async getSalesChart(
    @CurrentUser() user: User,
    @Query() query: SalesChartQueryDto
  ): Promise<ApiResponseDto<SalesChartResponseDto>> {
    const data = await this.businessReportService.getSalesChartData(user.id, query);
    return ApiResponseDto.success(data, 'Lấy dữ liệu biểu đồ doanh thu thành công');
  }

  /**
   * Lấy dữ liệu biểu đồ đơn hàng
   */
  @Get('orders-chart')
  @ApiOperation({
    summary: 'Lấy dữ liệu biểu đồ đơn hàng',
    description: 'API lấy dữ liệu biểu đồ đơn hàng theo trạng thái và thời gian'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu biểu đồ đơn hàng thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/OrdersChartResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ'
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực'
  })
  async getOrdersChart(
    @CurrentUser() user: User,
    @Query() query: OrdersChartQueryDto
  ): Promise<ApiResponseDto<OrdersChartResponseDto>> {
    const data = await this.businessReportService.getOrdersChartData(user.id, query);
    return ApiResponseDto.success(data, 'Lấy dữ liệu biểu đồ đơn hàng thành công');
  }

  /**
   * Lấy dữ liệu biểu đồ khách hàng
   */
  @Get('customers-chart')
  @ApiOperation({
    summary: 'Lấy dữ liệu biểu đồ khách hàng',
    description: 'API lấy dữ liệu biểu đồ khách hàng mới và tổng khách hàng theo thời gian'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu biểu đồ khách hàng thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/CustomersChartResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ'
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực'
  })
  async getCustomersChart(
    @CurrentUser() user: User,
    @Query() query: CustomersChartQueryDto
  ): Promise<ApiResponseDto<CustomersChartResponseDto>> {
    const data = await this.businessReportService.getCustomersChartData(user.id, query);
    return ApiResponseDto.success(data, 'Lấy dữ liệu biểu đồ khách hàng thành công');
  }

  /**
   * Lấy dữ liệu biểu đồ sản phẩm
   */
  @Get('products-chart')
  @ApiOperation({
    summary: 'Lấy dữ liệu biểu đồ sản phẩm',
    description: 'API lấy dữ liệu biểu đồ hiệu suất sản phẩm'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy dữ liệu biểu đồ sản phẩm thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/ProductsChartResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ'
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực'
  })
  async getProductsChart(
    @CurrentUser() user: User,
    @Query() query: ProductsChartQueryDto
  ): Promise<ApiResponseDto<ProductsChartResponseDto>> {
    const data = await this.businessReportService.getProductsChartData(user.id, query);
    return ApiResponseDto.success(data, 'Lấy dữ liệu biểu đồ sản phẩm thành công');
  }

  /**
   * Lấy danh sách sản phẩm bán chạy
   */
  @Get('top-selling-products')
  @ApiOperation({
    summary: 'Lấy danh sách sản phẩm bán chạy',
    description: 'API lấy danh sách sản phẩm bán chạy nhất theo doanh thu hoặc số lượng'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách sản phẩm bán chạy thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/TopSellingProductsResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ'
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực'
  })
  async getTopSellingProducts(
    @CurrentUser() user: User,
    @Query() query: TopSellingProductsQueryDto
  ): Promise<ApiResponseDto<TopSellingProductsResponseDto>> {
    const data = await this.businessReportService.getTopSellingProducts(user.id, query);
    return ApiResponseDto.success(data, 'Lấy danh sách sản phẩm bán chạy thành công');
  }

  /**
   * Lấy danh sách khách hàng tiềm năng
   */
  @Get('potential-customers')
  @ApiOperation({
    summary: 'Lấy danh sách khách hàng tiềm năng',
    description: 'API lấy danh sách khách hàng tiềm năng dựa trên điểm tiềm năng và hành vi mua hàng'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách khách hàng tiềm năng thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/PotentialCustomersResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số truy vấn không hợp lệ'
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa xác thực'
  })
  async getPotentialCustomers(
    @CurrentUser() user: User,
    @Query() query: PotentialCustomersQueryDto
  ): Promise<ApiResponseDto<PotentialCustomersResponseDto>> {
    const data = await this.businessReportService.getPotentialCustomers(user.id, query);
    return ApiResponseDto.success(data, 'Lấy danh sách khách hàng tiềm năng thành công');
  }
}
