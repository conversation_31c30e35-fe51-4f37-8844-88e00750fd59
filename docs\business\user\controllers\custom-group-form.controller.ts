import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiCreatedResponse, ApiExtraModels, ApiOkResponse, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CustomGroupFormService } from '@modules/business/user/services';
import {
  CreateCustomGroupFormDto,
  UpdateCustomGroupFormDto,
  CustomGroupFormCreatedResponseDto,
  QueryCustomGroupFormDto,
  CustomGroupFormListItemDto,
  CustomGroupFormResponseDto
} from '../dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';
import { SkipValidation } from '../decorators/skip-validation.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

/**
 * Controller xử lý các endpoint liên quan đến nhóm trường tùy chỉnh cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS)
@Controller('user/custom-group-forms')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, CustomGroupFormCreatedResponseDto, CustomGroupFormListItemDto, CustomGroupFormResponseDto, PaginatedResult, UpdateCustomGroupFormDto)
export class CustomGroupFormController {
  constructor(private readonly customGroupFormService: CustomGroupFormService) {}

  /**
   * Tạo nhóm trường tùy chỉnh mới
   * @param createDto DTO chứa thông tin tạo nhóm trường tùy chỉnh
   * @param userId ID của người dùng hiện tại
   * @SkipValidation Bỏ qua validate của DTO
   * @returns Thông tin nhóm trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo nhóm trường tùy chỉnh mới' })
  @ApiCreatedResponse({
    description: 'Nhóm trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CustomGroupFormCreatedResponseDto),
  })
  @ApiBody({
    type: CreateCustomGroupFormDto,
    description: 'Thông tin tạo nhóm trường tùy chỉnh',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.GROUP_FORM_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async create(
    @SkipValidation() body: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<CustomGroupFormCreatedResponseDto>> {
    // Tạo DTO thủ công từ request body
    const createDto: CreateCustomGroupFormDto = {
      label: body.label,
      productId: body.productId ? Number(body.productId) : undefined,
      userId: userId
    };

    const result = await this.customGroupFormService.create(createDto);
    return ApiResponseDto.created(result, 'Nhóm trường tùy chỉnh đã được tạo thành công');
  }


  /**
   * Lấy danh sách nhóm trường tùy chỉnh với các điều kiện lọc và phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @param userId
   * @returns Danh sách nhóm trường tùy chỉnh với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách nhóm trường tùy chỉnh' })
  @ApiOkResponse({
    description: 'Danh sách nhóm trường tùy chỉnh',
    schema: ApiResponseDto.getPaginatedSchema(CustomGroupFormListItemDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.GROUP_FORM_FIND_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async findAll(
    @Query() queryDto: QueryCustomGroupFormDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<PaginatedResult<CustomGroupFormListItemDto>>> {
    // Tạo một đối tượng mới chỉ với các trường cần thiết và thêm userId
    console.log(`userId được truyền vào: ${userId}`);
    const queryParams = {
      ...queryDto,
      userId // Tự động thêm ID của người dùng đăng nhập
    };
    console.log(`queryParams: ${JSON.stringify(queryParams)}`);

    const result = await this.customGroupFormService.findAll(queryParams);
    return ApiResponseDto.success(result, 'Lấy danh sách nhóm trường tùy chỉnh thành công');
  }

  /**
   * Lấy chi tiết nhóm trường tùy chỉnh theo ID
   * @param id ID của nhóm trường tùy chỉnh
   * @param userId
   * @returns Thông tin chi tiết nhóm trường tùy chỉnh với các trường con
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết nhóm trường tùy chỉnh' })
  @ApiParam({
    name: 'id',
    description: 'ID của nhóm trường tùy chỉnh cần lấy chi tiết',
    type: 'number',
    example: 1
  })
  @ApiOkResponse({
    description: 'Chi tiết nhóm trường tùy chỉnh',
    schema: ApiResponseDto.getSchema(CustomGroupFormResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.GROUP_FORM_NOT_FOUND,
    ErrorCode.NOT_FOUND,
  )
  async findById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<CustomGroupFormResponseDto>> {
    const result = await this.customGroupFormService.findById(id, userId);
    return ApiResponseDto.success(result, 'Lấy chi tiết nhóm trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật nhóm trường tùy chỉnh
   * @param {number} id - ID của nhóm trường tùy chỉnh cần cập nhật
   * @param requestBody
   * @param {number} userId - ID của người dùng hiện tại
   * @returns {Promise<ApiResponseDto<CustomGroupFormCreatedResponseDto>>} Thông tin nhóm trường tùy chỉnh đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật nhóm trường tùy chỉnh' })
  @ApiParam({
    name: 'id',
    description: 'ID của nhóm trường tùy chỉnh cần cập nhật',
    type: 'number',
    example: 1
  })
  @ApiOkResponse({
    description: 'Nhóm trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomGroupFormCreatedResponseDto),
  })
  @ApiBody({
    type: UpdateCustomGroupFormDto,
    description: 'Thông tin cập nhật nhóm trường tùy chỉnh',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.GROUP_FORM_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
    BUSINESS_ERROR_CODES.GROUP_FORM_NOT_FOUND,
    ErrorCode.NOT_FOUND,
  )
  async update(
    @Param('id', ParseIntPipe) id: number,
    @SkipValidation() requestBody: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<CustomGroupFormCreatedResponseDto>> {
    // Tạo DTO thủ công từ request body
    const updateDto: UpdateCustomGroupFormDto = {
      label: requestBody.label,
      userId: userId
    };

    const result = await this.customGroupFormService.update(id, updateDto);
    return ApiResponseDto.success(result, 'Nhóm trường tùy chỉnh đã được cập nhật thành công');
  }

  /**
   * Xóa nhóm trường tùy chỉnh
   * @param id ID của nhóm trường tùy chỉnh cần xóa
   * @param userId
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa nhóm trường tùy chỉnh' })
  @ApiParam({
    name: 'id',
    description: 'ID của nhóm trường tùy chỉnh cần xóa',
    type: 'number',
    example: 1
  })
  @ApiOkResponse({
    description: 'Nhóm trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.GROUP_FORM_DELETION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
    BUSINESS_ERROR_CODES.GROUP_FORM_NOT_FOUND,
    ErrorCode.NOT_FOUND,
  )
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.customGroupFormService.delete(id, userId);
    return ApiResponseDto.success(null, 'Nhóm trường tùy chỉnh đã được xóa thành công');
  }
}
