import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpStatus,
  SetMetadata
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { User } from '@modules/user/entities';
import { GHNShipmentService } from '@modules/business/user/services';
import { GHNConfigValidationHelper } from '../helpers/ghn-config-validation.helper';
import {
  CreateGHNOrderRequestDto,
  UpdateGHNOrderRequestDto,
  CalculateGHNFeeRequestDto,
  CancelGHNOrderRequestDto,
  PrintGHNOrderRequestDto,
  UpdateGHNCODRequestDto,
  GetGHNServicesRequestDto,
  CalculateGHNLeadTimeRequestDto,
  CreateGHNStoreRequestDto,
  GetGHNStationsRequestDto,
  CreateGHNTicketRequestDto,
  ReplyGHNTicketRequestDto,
  GHNWebhookDataDto
} from '../dto/ghn';
import {
  CreateGHNOrderResponseDto,
  CalculateGHNFeeResponseDto,
  GetGHNOrderInfoResponseDto,
  CancelGHNOrderResponseDto,
  GetGHNStoresResponseDto,
  CreateGHNStoreResponseDto,
  GetGHNAddressResponseDto,
  GetGHNServicesResponseDto,
  GetGHNPickShiftsResponseDto,
  CalculateGHNLeadTimeResponseDto,
  GetGHNStationsResponseDto,
  CreateGHNTicketResponseDto,
  GetGHNTicketsResponseDto,
  GetGHNOTPResponseDto,
  PrintGHNOrderResponseDto,
  GHNBaseResponseDto
} from '../dto/ghn/ghn-response.dto';

/**
 * Controller xử lý các API GHN (Giao Hàng Nhanh)
 */
@ApiTags('GHN Shipment')
@Controller('ghn')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class GHNShipmentController {
  private readonly logger = new Logger(GHNShipmentController.name);

  constructor(
    private readonly ghnService: GHNShipmentService,
    private readonly configValidationHelper: GHNConfigValidationHelper
  ) {}

  /**
   * Kiểm tra cấu hình GHN
   */
  @Get('config/validate')
  @ApiOperation({
    summary: 'Kiểm tra cấu hình GHN',
    description: 'Validate cấu hình GHN và trả về báo cáo chi tiết'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kiểm tra cấu hình thành công'
  })
  async validateConfig(@CurrentUser() user: User) {
    this.logger.log(`User ${user.id} kiểm tra cấu hình GHN`);

    const validation = this.configValidationHelper.validateGHNConfig();
    const envInfo = this.configValidationHelper.getEnvironmentInfo();
    const compatibility = this.configValidationHelper.validateEnvironmentCompatibility();
    const report = this.configValidationHelper.generateConfigReport();

    return {
      isValid: validation.isValid,
      errors: validation.errors,
      warnings: validation.warnings,
      environment: envInfo.nodeEnv,
      isTestMode: validation.config.isTestMode,
      compatibility: compatibility,
      report: report
    };
  }

  /**
   * Tạo đơn hàng GHN
   */
  @Post('orders')
  @ApiOperation({
    summary: 'Tạo đơn hàng GHN',
    description: 'Gửi thông tin đơn hàng lên hệ thống GHN để tạo vận đơn'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo đơn hàng GHN thành công',
    type: CreateGHNOrderResponseDto
  })
  async createOrder(
    @CurrentUser() user: User,
    @Body() createOrderDto: CreateGHNOrderRequestDto
  ) {
    this.logger.log(`User ${user.id} tạo đơn hàng GHN: ${createOrderDto.clientOrderCode}`);
    return await this.ghnService.createOrder(createOrderDto);
  }

  /**
   * Cập nhật đơn hàng GHN
   */
  @Post('orders/:orderCode/update')
  @ApiOperation({
    summary: 'Cập nhật đơn hàng GHN',
    description: 'Cập nhật thông tin đơn hàng đã tạo trên GHN'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật đơn hàng thành công',
    type: GHNBaseResponseDto
  })
  async updateOrder(
    @CurrentUser() user: User,
    @Param('orderCode') orderCode: string,
    @Body() updateOrderDto: UpdateGHNOrderRequestDto
  ) {
    this.logger.log(`User ${user.id} cập nhật đơn hàng GHN: ${orderCode}`);
    return await this.ghnService.updateOrder(orderCode, updateOrderDto);
  }

  /**
   * Hủy đơn hàng GHN
   */
  @Post('orders/cancel')
  @ApiOperation({
    summary: 'Hủy đơn hàng GHN',
    description: 'Hủy một hoặc nhiều đơn hàng đã được gửi lên GHN'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Hủy đơn hàng thành công',
    type: CancelGHNOrderResponseDto
  })
  async cancelOrder(
    @CurrentUser() user: User,
    @Body() cancelOrderDto: CancelGHNOrderRequestDto
  ) {
    this.logger.log(`User ${user.id} hủy đơn hàng GHN: ${cancelOrderDto.orderCodes.join(', ')}`);
    return await this.ghnService.cancelOrder(cancelOrderDto.orderCodes);
  }

  /**
   * Trả đơn hàng GHN
   */
  @Post('orders/return')
  @ApiOperation({
    summary: 'Trả đơn hàng GHN',
    description: 'Yêu cầu trả lại đơn hàng cho người gửi'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trả đơn hàng thành công',
    type: CancelGHNOrderResponseDto
  })
  async returnOrder(
    @CurrentUser() user: User,
    @Body() returnOrderDto: CancelGHNOrderRequestDto
  ) {
    this.logger.log(`User ${user.id} trả đơn hàng GHN: ${returnOrderDto.orderCodes.join(', ')}`);
    return await this.ghnService.returnOrder(returnOrderDto.orderCodes);
  }

  /**
   * Giao lại đơn hàng GHN
   */
  @Post('orders/delivery-again')
  @ApiOperation({
    summary: 'Giao lại đơn hàng GHN',
    description: 'Yêu cầu giao lại đơn hàng bị chuyển sang trạng thái storage'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Giao lại đơn hàng thành công',
    type: CancelGHNOrderResponseDto
  })
  async deliveryAgain(
    @CurrentUser() user: User,
    @Body() deliveryAgainDto: CancelGHNOrderRequestDto
  ) {
    this.logger.log(`User ${user.id} giao lại đơn hàng GHN: ${deliveryAgainDto.orderCodes.join(', ')}`);
    return await this.ghnService.deliveryAgain(deliveryAgainDto.orderCodes);
  }

  /**
   * Lấy thông tin đơn hàng GHN
   */
  @Get('orders/:orderCode')
  @ApiOperation({
    summary: 'Lấy thông tin đơn hàng GHN',
    description: 'Lấy thông tin chi tiết của một đơn hàng theo mã đơn GHN'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin đơn hàng thành công',
    type: GetGHNOrderInfoResponseDto
  })
  async getOrderInfo(
    @CurrentUser() user: User,
    @Param('orderCode') orderCode: string
  ) {
    this.logger.log(`User ${user.id} lấy thông tin đơn hàng GHN: ${orderCode}`);
    return await this.ghnService.getOrderInfo(orderCode);
  }

  /**
   * Lấy thông tin đơn hàng theo mã khách hàng
   */
  @Get('orders/client/:clientOrderCode')
  @ApiOperation({
    summary: 'Lấy thông tin đơn hàng theo mã khách hàng',
    description: 'Lấy thông tin chi tiết của một đơn hàng theo mã đơn do khách hàng cung cấp'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin đơn hàng thành công',
    type: GetGHNOrderInfoResponseDto
  })
  async getOrderInfoByClientCode(
    @CurrentUser() user: User,
    @Param('clientOrderCode') clientOrderCode: string
  ) {
    this.logger.log(`User ${user.id} lấy thông tin đơn hàng GHN theo mã KH: ${clientOrderCode}`);
    return await this.ghnService.getOrderInfoByClientCode(clientOrderCode);
  }

  /**
   * Xem trước đơn hàng GHN
   */
  @Post('orders/preview')
  @ApiOperation({
    summary: 'Xem trước đơn hàng GHN',
    description: 'Xem trước thông tin và phí của đơn hàng trước khi tạo chính thức'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xem trước đơn hàng thành công',
    type: CreateGHNOrderResponseDto
  })
  async previewOrder(
    @CurrentUser() user: User,
    @Body() previewOrderDto: Omit<CreateGHNOrderRequestDto, 'shopId'>
  ) {
    this.logger.log(`User ${user.id} xem trước đơn hàng GHN`);
    return await this.ghnService.previewOrder(previewOrderDto);
  }

  /**
   * Cập nhật COD đơn hàng GHN
   */
  @Post('orders/:orderCode/cod')
  @ApiOperation({
    summary: 'Cập nhật COD đơn hàng GHN',
    description: 'Cập nhật số tiền thu hộ (COD) của đơn hàng'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật COD thành công',
    type: GHNBaseResponseDto
  })
  async updateCOD(
    @CurrentUser() user: User,
    @Param('orderCode') orderCode: string,
    @Body() updateCODDto: UpdateGHNCODRequestDto
  ) {
    this.logger.log(`User ${user.id} cập nhật COD đơn hàng GHN: ${orderCode}`);
    return await this.ghnService.updateCOD(orderCode, updateCODDto.codAmount);
  }

  /**
   * In nhãn đơn hàng GHN
   */
  @Post('orders/print')
  @ApiOperation({
    summary: 'In nhãn đơn hàng GHN',
    description: 'Lấy token để in nhãn cho một hoặc nhiều đơn hàng'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'In nhãn đơn hàng thành công',
    type: PrintGHNOrderResponseDto
  })
  async printOrder(
    @CurrentUser() user: User,
    @Body() printOrderDto: PrintGHNOrderRequestDto
  ) {
    this.logger.log(`User ${user.id} in nhãn đơn hàng GHN: ${printOrderDto.orderCodes.join(', ')}`);
    return await this.ghnService.printOrder(printOrderDto.orderCodes);
  }

  /**
   * Tính phí vận chuyển GHN
   */
  @Post('calculate-fee')
  @ApiOperation({
    summary: 'Tính phí vận chuyển GHN',
    description: 'Tính toán chi phí vận chuyển dựa trên thông tin địa chỉ, khối lượng, kích thước'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tính phí vận chuyển thành công',
    type: CalculateGHNFeeResponseDto
  })
  async calculateFee(
    @CurrentUser() user: User,
    @Body() calculateFeeDto: CalculateGHNFeeRequestDto
  ) {
    this.logger.log(`User ${user.id} tính phí vận chuyển GHN từ ${calculateFeeDto.fromDistrictId} đến ${calculateFeeDto.toDistrictId}`);
    return await this.ghnService.calculateFee(calculateFeeDto);
  }

  /**
   * Lấy phí đơn hàng GHN
   */
  @Get('orders/:orderCode/fee')
  @ApiOperation({
    summary: 'Lấy phí đơn hàng GHN',
    description: 'Lấy thông tin chi tiết phí của một đơn hàng đã tạo'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy phí đơn hàng thành công'
  })
  async getOrderFee(
    @CurrentUser() user: User,
    @Param('orderCode') orderCode: string
  ) {
    this.logger.log(`User ${user.id} lấy phí đơn hàng GHN: ${orderCode}`);
    return await this.ghnService.getOrderFee(orderCode);
  }

  /**
   * Lấy danh sách dịch vụ GHN
   */
  @Get('services')
  @ApiOperation({
    summary: 'Lấy danh sách dịch vụ GHN',
    description: 'Lấy danh sách các dịch vụ vận chuyển có sẵn giữa hai địa điểm'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách dịch vụ thành công',
    type: GetGHNServicesResponseDto
  })
  async getServices(
    @CurrentUser() user: User,
    @Query() servicesQuery: GetGHNServicesRequestDto
  ) {
    this.logger.log(`User ${user.id} lấy danh sách dịch vụ GHN từ ${servicesQuery.fromDistrict} đến ${servicesQuery.toDistrict}`);
    return await this.ghnService.getServices(servicesQuery.fromDistrict, servicesQuery.toDistrict);
  }

  /**
   * Tính thời gian giao hàng GHN
   */
  @Post('leadtime')
  @ApiOperation({
    summary: 'Tính thời gian giao hàng GHN',
    description: 'Tính toán thời gian giao hàng dự kiến giữa hai địa điểm'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tính thời gian giao hàng thành công',
    type: CalculateGHNLeadTimeResponseDto
  })
  async getLeadTime(
    @CurrentUser() user: User,
    @Body() leadTimeDto: CalculateGHNLeadTimeRequestDto
  ) {
    this.logger.log(`User ${user.id} tính thời gian giao hàng GHN`);
    return await this.ghnService.getLeadTime(leadTimeDto);
  }

  /**
   * Lấy danh sách cửa hàng GHN
   */
  @Get('stores')
  @ApiOperation({
    summary: 'Lấy danh sách cửa hàng GHN',
    description: 'Lấy danh sách các cửa hàng đã đăng ký với GHN'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách cửa hàng thành công',
    type: GetGHNStoresResponseDto
  })
  async getStores(
    @CurrentUser() user: User,
    @Query() params?: any
  ) {
    this.logger.log(`User ${user.id} lấy danh sách cửa hàng GHN`);
    return await this.ghnService.getStores(params);
  }

  /**
   * Tạo cửa hàng GHN
   */
  @Post('stores')
  @ApiOperation({
    summary: 'Tạo cửa hàng GHN',
    description: 'Đăng ký cửa hàng mới với GHN'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo cửa hàng thành công',
    type: CreateGHNStoreResponseDto
  })
  async createStore(
    @CurrentUser() user: User,
    @Body() createStoreDto: CreateGHNStoreRequestDto
  ) {
    this.logger.log(`User ${user.id} tạo cửa hàng GHN: ${createStoreDto.name}`);
    return await this.ghnService.createStore(createStoreDto);
  }

  /**
   * Lấy danh sách tỉnh/thành phố GHN
   */
  @Get('address/provinces')
  @ApiOperation({
    summary: 'Lấy danh sách tỉnh/thành phố GHN',
    description: 'Lấy danh sách tất cả tỉnh/thành phố mà GHN hỗ trợ'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách tỉnh/thành phố thành công',
    type: GetGHNAddressResponseDto
  })
  async getProvinces(
    @CurrentUser() user: User
  ) {
    this.logger.log(`User ${user.id} lấy danh sách tỉnh/thành phố GHN`);
    return await this.ghnService.getProvinces();
  }

  /**
   * Lấy danh sách quận/huyện GHN
   */
  @Get('address/districts/:provinceId')
  @ApiOperation({
    summary: 'Lấy danh sách quận/huyện GHN',
    description: 'Lấy danh sách quận/huyện theo tỉnh/thành phố'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách quận/huyện thành công',
    type: GetGHNAddressResponseDto
  })
  async getDistricts(
    @CurrentUser() user: User,
    @Param('provinceId') provinceId: number
  ) {
    this.logger.log(`User ${user.id} lấy danh sách quận/huyện GHN cho tỉnh: ${provinceId}`);
    return await this.ghnService.getDistricts(provinceId);
  }

  /**
   * Lấy danh sách phường/xã GHN
   */
  @Get('address/wards/:districtId')
  @ApiOperation({
    summary: 'Lấy danh sách phường/xã GHN',
    description: 'Lấy danh sách phường/xã theo quận/huyện'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách phường/xã thành công',
    type: GetGHNAddressResponseDto
  })
  async getWards(
    @CurrentUser() user: User,
    @Param('districtId') districtId: number
  ) {
    this.logger.log(`User ${user.id} lấy danh sách phường/xã GHN cho quận: ${districtId}`);
    return await this.ghnService.getWards(districtId);
  }

  /**
   * Lấy danh sách bưu cục GHN
   */
  @Get('stations')
  @ApiOperation({
    summary: 'Lấy danh sách bưu cục GHN',
    description: 'Lấy danh sách bưu cục GHN gần địa chỉ cho trước'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách bưu cục thành công',
    type: GetGHNStationsResponseDto
  })
  async getStations(
    @CurrentUser() user: User,
    @Query() stationsQuery: GetGHNStationsRequestDto
  ) {
    this.logger.log(`User ${user.id} lấy danh sách bưu cục GHN`);
    return await this.ghnService.getStations(stationsQuery);
  }

  /**
   * Lấy danh sách ca lấy hàng GHN
   */
  @Get('pick-shifts')
  @ApiOperation({
    summary: 'Lấy danh sách ca lấy hàng GHN',
    description: 'Lấy danh sách các ca lấy hàng có sẵn'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách ca lấy hàng thành công',
    type: GetGHNPickShiftsResponseDto
  })
  async getPickShifts(
    @CurrentUser() user: User
  ) {
    this.logger.log(`User ${user.id} lấy danh sách ca lấy hàng GHN`);
    return await this.ghnService.getPickShifts();
  }

  /**
   * Tạo ticket hỗ trợ GHN
   */
  @Post('tickets')
  @ApiOperation({
    summary: 'Tạo ticket hỗ trợ GHN',
    description: 'Tạo ticket hỗ trợ cho đơn hàng gặp vấn đề'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo ticket hỗ trợ thành công',
    type: CreateGHNTicketResponseDto
  })
  async createTicket(
    @CurrentUser() user: User,
    @Body() createTicketDto: CreateGHNTicketRequestDto
  ) {
    this.logger.log(`User ${user.id} tạo ticket hỗ trợ GHN cho đơn hàng: ${createTicketDto.orderCode}`);
    return await this.ghnService.createTicket(createTicketDto);
  }

  /**
   * Phản hồi ticket GHN
   */
  @Post('tickets/:ticketId/reply')
  @ApiOperation({
    summary: 'Phản hồi ticket GHN',
    description: 'Gửi phản hồi cho ticket hỗ trợ đã tạo'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Phản hồi ticket thành công'
  })
  async replyTicket(
    @CurrentUser() user: User,
    @Param('ticketId') ticketId: number,
    @Body() replyTicketDto: ReplyGHNTicketRequestDto
  ) {
    this.logger.log(`User ${user.id} phản hồi ticket GHN: ${ticketId}`);
    return await this.ghnService.replyTicket(ticketId, replyTicketDto.description);
  }

  /**
   * Lấy danh sách ticket GHN
   */
  @Get('tickets')
  @ApiOperation({
    summary: 'Lấy danh sách ticket GHN',
    description: 'Lấy danh sách tất cả ticket hỗ trợ đã tạo'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách ticket thành công',
    type: GetGHNTicketsResponseDto
  })
  async getTickets(
    @CurrentUser() user: User
  ) {
    this.logger.log(`User ${user.id} lấy danh sách ticket GHN`);
    return await this.ghnService.getTickets();
  }

  /**
   * Lấy OTP cho đối tác GHN
   */
  @Post('affiliate/otp')
  @ApiOperation({
    summary: 'Lấy OTP cho đối tác GHN',
    description: 'Lấy mã OTP để đăng ký làm đối tác GHN'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy OTP thành công',
    type: GetGHNOTPResponseDto
  })
  async getOTP(
    @CurrentUser() user: User,
    @Body() otpRequest: { phone: string }
  ) {
    this.logger.log(`User ${user.id} lấy OTP cho đối tác GHN: ${otpRequest.phone}`);
    return await this.ghnService.getOTP(otpRequest.phone);
  }

  /**
   * Tạo cửa hàng bằng OTP
   */
  @Post('affiliate/stores')
  @ApiOperation({
    summary: 'Tạo cửa hàng bằng OTP',
    description: 'Tạo cửa hàng GHN bằng mã OTP đã nhận'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo cửa hàng bằng OTP thành công',
    type: CreateGHNStoreResponseDto
  })
  async createStoreByOTP(
    @CurrentUser() user: User,
    @Body() createStoreOTPDto: any
  ) {
    this.logger.log(`User ${user.id} tạo cửa hàng GHN bằng OTP: ${createStoreOTPDto.phone}`);
    return await this.ghnService.createStoreByOTP(createStoreOTPDto);
  }

  /**
   * Thêm nhân viên bằng OTP
   */
  @Post('affiliate/staff')
  @ApiOperation({
    summary: 'Thêm nhân viên bằng OTP',
    description: 'Thêm nhân viên vào cửa hàng GHN bằng mã OTP'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thêm nhân viên bằng OTP thành công',
    type: GHNBaseResponseDto
  })
  async addStaffByOTP(
    @CurrentUser() user: User,
    @Body() addStaffOTPDto: any
  ) {
    this.logger.log(`User ${user.id} thêm nhân viên GHN bằng OTP: ${addStaffOTPDto.phone}`);
    return await this.ghnService.addStaffByOTP(addStaffOTPDto);
  }

  /**
   * Webhook endpoint để nhận cập nhật từ GHN
   * Endpoint này không cần authentication vì được gọi từ GHN
   */
  @Post('webhook')
  @SetMetadata('skipAuth', true)
  @ApiOperation({
    summary: 'Webhook từ GHN',
    description: 'Endpoint để nhận thông báo cập nhật trạng thái đơn hàng từ GHN'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xử lý webhook thành công'
  })
  async handleWebhook(@Body() webhookData: GHNWebhookDataDto): Promise<{ success: boolean }> {
    this.logger.log(`Nhận webhook từ GHN cho đơn hàng: ${webhookData.OrderCode}`);

    await this.ghnService.handleWebhook(webhookData);

    return { success: true };
  }
}
