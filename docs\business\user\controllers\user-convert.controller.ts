import { <PERSON>, Get, HttpCode, HttpStatus, Logger, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserConvertService } from '@modules/business/user/services';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { QueryUserConvertDto, UserConvertListItemDto, UserConvertResponseDto, UserConvertCustomerDto } from '../dto';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * Controller x<PERSON> lý c<PERSON>c request liên quan đến chuyển đổi khách hàng
 */
@ApiTags(SWAGGER_API_TAGS.USER_CONVERT)
@Controller('user/converts')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, UserConvertResponseDto, UserConvertListItemDto, UserConvertCustomerDto, PaginatedResult)
export class UserConvertController {
  private readonly logger = new Logger(UserConvertController.name);

  constructor(private readonly userConvertService: UserConvertService) {}

  /**
   * Lấy danh sách bản ghi chuyển đổi của người dùng
   * @param queryDto DTO chứa các tham số truy vấn
   * @param user Thông tin người dùng từ JWT
   * @returns Danh sách bản ghi chuyển đổi với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy danh sách bản ghi chuyển đổi' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách bản ghi chuyển đổi',
    schema: ApiResponseDto.getPaginatedSchema(UserConvertListItemDto),
  })
  @ApiErrorResponse(BUSINESS_ERROR_CODES.CONVERT_FIND_FAILED)
  async getConverts(
    @Query() queryDto: QueryUserConvertDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<UserConvertListItemDto>>> {
    try {
      this.logger.log(`Lấy danh sách bản ghi chuyển đổi cho userId=${user.id}`);
      const converts = await this.userConvertService.findAll(user.id, queryDto);
      return ApiResponseDto.success(converts, 'Lấy danh sách bản ghi chuyển đổi thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách bản ghi chuyển đổi: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy chi tiết bản ghi chuyển đổi theo ID
   * @param id ID của bản ghi chuyển đổi
   * @param user Thông tin người dùng từ JWT
   * @returns Chi tiết bản ghi chuyển đổi với thông tin khách hàng
   */
  @Get('detail/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy chi tiết bản ghi chuyển đổi với thông tin khách hàng' })
  @ApiParam({ name: 'id', description: 'ID của bản ghi chuyển đổi', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết bản ghi chuyển đổi kèm thông tin khách hàng',
    schema: ApiResponseDto.getSchema(UserConvertResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.CONVERT_FIND_FAILED
  )
  async getConvertDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserConvertResponseDto>> {
    try {
      this.logger.log(`Lấy chi tiết bản ghi chuyển đổi id=${id} cho userId=${user.id}`);
      const convert = await this.userConvertService.findById(id, user.id);
      return ApiResponseDto.success(convert, 'Lấy chi tiết bản ghi chuyển đổi thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết bản ghi chuyển đổi: ${error.message}`, error.stack);
      throw error;
    }
  }
}