import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { UserVirtualWarehouseService } from '@modules/business/user/services';
import {
  CreateVirtualWarehouseDto,
  UpdateVirtualWarehouseDto,
  VirtualWarehouseResponseDto,
  QueryVirtualWarehouseDto,
} from '../dto/warehouse';
import { ApiResponseDto } from '@/common/response';
import { SwaggerApiTag } from '@common/swagger';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * Controller xử lý các endpoint liên quan đến kho ảo cho người dùng
 */
@Controller('user/virtual-warehouses')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SwaggerApiTag.USER_WAREHOUSE_VIRTUAL)
export class UserVirtualWarehouseController {
  constructor(private readonly userVirtualWarehouseService: UserVirtualWarehouseService) {}

  /**
   * Lấy danh sách kho ảo với phân trang và lọc
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách kho ảo với phân trang và lọc' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kho ảo với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(VirtualWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getVirtualWarehouses(
    @Query() queryDto: QueryVirtualWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    queryDto.userId = user.id;
    const virtualWarehouses = await this.userVirtualWarehouseService.getVirtualWarehouses(queryDto);
    return ApiResponseDto.success(virtualWarehouses, 'Lấy danh sách kho ảo thành công');
  }

  /**
   * Lấy thông tin kho ảo theo ID
   */
  @Get(':warehouseId')
  @ApiOperation({ summary: 'Lấy thông tin kho ảo theo ID' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của kho ảo',
    schema: ApiResponseDto.getSchema(VirtualWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getVirtualWarehouseById(
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
    @CurrentUser() user: JwtPayload
  ) {
    const virtualWarehouse = await this.userVirtualWarehouseService.getVirtualWarehouseById(warehouseId, user.id);
    return ApiResponseDto.success(virtualWarehouse, 'Lấy thông tin kho ảo thành công');
  }

  /**
   * Tạo mới kho ảo
   */
  @Post(':warehouseId')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Tạo mới kho ảo' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', type: 'number' })
  @ApiCreatedResponse({
    description: 'Tạo mới kho ảo thành công',
    type: () => ApiResponseDto.getSchema(VirtualWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
    BUSINESS_ERROR_CODES.WAREHOUSE_VALIDATION_FAILED,
    BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
    BUSINESS_ERROR_CODES.WAREHOUSE_ALREADY_EXISTS,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @ApiBody({ type: CreateVirtualWarehouseDto })
  async createVirtualWarehouse(
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
    @Body() createDto: CreateVirtualWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    createDto.userId = user.id;
    const virtualWarehouse = await this.userVirtualWarehouseService.createVirtualWarehouse(
      warehouseId,
      createDto,
    );
    return ApiResponseDto.created<VirtualWarehouseResponseDto>(
      virtualWarehouse,
      'Tạo mới kho ảo thành công',
    );
  }

  /**
   * Cập nhật kho ảo
   */
  @Put(':warehouseId')
  @ApiOperation({ summary: 'Cập nhật kho ảo' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', type: 'number' })
  @ApiOkResponse({
    description: 'Cập nhật kho ảo thành công',
    schema: ApiResponseDto.getSchema(VirtualWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @ApiBody({ type: UpdateVirtualWarehouseDto })
  async updateVirtualWarehouse(
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
    @Body() updateDto: UpdateVirtualWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    const virtualWarehouse = await this.userVirtualWarehouseService.updateVirtualWarehouse(
      warehouseId,
      updateDto,
      user.id
    );
    return ApiResponseDto.success<VirtualWarehouseResponseDto>(
      virtualWarehouse,
      'Cập nhật kho ảo thành công',
    );
  }

  /**
   * Xóa kho ảo
   */
  @Delete(':warehouseId')
  @ApiOperation({ summary: 'Xóa kho ảo' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', type: 'number' })
  @ApiOkResponse({
    description: 'Xóa kho ảo thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async deleteVirtualWarehouse(
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
    @CurrentUser() user: JwtPayload
  ) {
    await this.userVirtualWarehouseService.deleteVirtualWarehouse(warehouseId, user.id);
    return ApiResponseDto.success(null, 'Xóa kho ảo thành công');
  }
}
