import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  ParseIntPipe
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { UserWarehouseCustomFieldService } from '@modules/business/user/services';
import {
  CreateWarehouseCustomFieldDto,
  UpdateWarehouseCustomFieldDto,
  WarehouseCustomFieldResponseDto,
} from '../dto/warehouse';
import { ApiResponseDto } from '@/common/response';
import { SwaggerApiTag } from '@common/swagger';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

/**
 * <PERSON> x<PERSON> lý các request liên quan đến trường tùy chỉnh của kho
 */
@ApiTags(SwaggerApiTag.USER_WAREHOUSE_CUSTOM_FIELD)
@Controller('user/warehouses')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserWarehouseCustomFieldController {
  constructor(
    private readonly userWarehouseCustomFieldService: UserWarehouseCustomFieldService
  ) {}

  /**
   * Thêm trường tùy chỉnh cho kho
   */
  @Post(':id/custom-fields')
  @ApiOperation({ summary: 'Thêm trường tùy chỉnh cho kho' })
  @ApiParam({ name: 'id', description: 'ID của kho', type: Number })
  @ApiBody({ type: CreateWarehouseCustomFieldDto })
  @ApiResponse({
    status: 201,
    description: 'Trường tùy chỉnh đã được thêm thành công',
    schema: ApiResponseDto.getSchema(WarehouseCustomFieldResponseDto)
  })
  async addCustomField(
    @Param('id', ParseIntPipe) id: number,
    @Body() createDto: CreateWarehouseCustomFieldDto,
    @CurrentUser() user: JwtPayload
  ) {
    const customField = await this.userWarehouseCustomFieldService.addCustomField(id, createDto, user.id);
    return ApiResponseDto.success(customField, 'Thêm trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật trường tùy chỉnh của kho
   */
  @Put(':id/custom-fields/:fieldId')
  @ApiOperation({ summary: 'Cập nhật trường tùy chỉnh của kho' })
  @ApiParam({ name: 'id', description: 'ID của kho', type: Number })
  @ApiParam({ name: 'fieldId', description: 'ID của trường tùy chỉnh', type: Number })
  @ApiBody({ type: UpdateWarehouseCustomFieldDto })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(WarehouseCustomFieldResponseDto)
  })
  async updateCustomField(
    @Param('id', ParseIntPipe) id: number,
    @Param('fieldId', ParseIntPipe) fieldId: number,
    @Body() updateDto: UpdateWarehouseCustomFieldDto,
    @CurrentUser() user: JwtPayload
  ) {
    const customField = await this.userWarehouseCustomFieldService.updateCustomField(id, fieldId, updateDto, user.id);
    return ApiResponseDto.success(customField, 'Cập nhật trường tùy chỉnh thành công');
  }

  /**
   * Xóa trường tùy chỉnh của kho
   */
  @Delete(':id/custom-fields/:fieldId')
  @ApiOperation({ summary: 'Xóa trường tùy chỉnh của kho' })
  @ApiParam({ name: 'id', description: 'ID của kho', type: Number })
  @ApiParam({ name: 'fieldId', description: 'ID của trường tùy chỉnh', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  async deleteCustomField(
    @Param('id', ParseIntPipe) id: number,
    @Param('fieldId', ParseIntPipe) fieldId: number,
    @CurrentUser() user: JwtPayload
  ) {
    await this.userWarehouseCustomFieldService.deleteCustomField(id, fieldId, user.id);
    return ApiResponseDto.success(null, 'Xóa trường tùy chỉnh thành công');
  }

  /**
   * Lấy danh sách trường tùy chỉnh của kho
   */
  @Get(':id/custom-fields')
  @ApiOperation({ summary: 'Lấy danh sách trường tùy chỉnh của kho' })
  @ApiParam({ name: 'id', description: 'ID của kho', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Danh sách trường tùy chỉnh của kho',
    schema: ApiResponseDto.getSchema([WarehouseCustomFieldResponseDto])
  })
  async getCustomFields(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ) {
    const customFields = await this.userWarehouseCustomFieldService.getCustomFields(id, user.id);
    return ApiResponseDto.success(customFields, 'Lấy danh sách trường tùy chỉnh thành công');
  }

  /**
   * Lấy thông tin trường tùy chỉnh của kho theo ID
   */
  @Get(':id/custom-fields/:fieldId')
  @ApiOperation({ summary: 'Lấy thông tin trường tùy chỉnh của kho theo ID' })
  @ApiParam({ name: 'id', description: 'ID của kho', type: Number })
  @ApiParam({ name: 'fieldId', description: 'ID của trường tùy chỉnh', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trường tùy chỉnh của kho',
    schema: ApiResponseDto.getSchema(WarehouseCustomFieldResponseDto)
  })
  async getCustomFieldById(
    @Param('id', ParseIntPipe) id: number,
    @Param('fieldId', ParseIntPipe) fieldId: number,
    @CurrentUser() user: JwtPayload
  ) {
    const customField = await this.userWarehouseCustomFieldService.getCustomFieldById(id, fieldId, user.id);
    return ApiResponseDto.success(customField, 'Lấy thông tin trường tùy chỉnh thành công');
  }
}
