import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { UserWarehouseService } from '@modules/business/user/services';
import {
  CreateWarehouseDto,
  QueryWarehouseDto,
  UpdateWarehouseDto,
  WarehouseResponseDto,
} from '../dto/warehouse';
import { ApiResponseDto } from '@/common/response';
import { SwaggerApiTag } from '@common/swagger';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

/**
 * <PERSON> x<PERSON> lý các request liên quan đến kho của người dùng
 */
@ApiTags(SwaggerApiTag.USER_WAREHOUSE)
@Controller('user/warehouses')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserWarehouseController {
  constructor(private readonly userWarehouseService: UserWarehouseService) {}

  /**
   * Tạo kho mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo kho mới' })
  @ApiBody({ type: CreateWarehouseDto })
  @ApiResponse({
    status: 201,
    description: 'Kho đã được tạo thành công',
    schema: ApiResponseDto.getSchema(WarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
    BUSINESS_ERROR_CODES.WAREHOUSE_VALIDATION_FAILED
  )
  async createWarehouse(
    @Body() createWarehouseDto: CreateWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    createWarehouseDto.userId = user.id;
    const warehouse =
      await this.userWarehouseService.createWarehouse(createWarehouseDto);
    return ApiResponseDto.success(warehouse, 'Tạo kho thành công');
  }

  /**
   * Cập nhật thông tin kho
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin kho' })
  @ApiParam({ name: 'id', description: 'ID của kho', type: Number })
  @ApiBody({ type: UpdateWarehouseDto })
  @ApiResponse({
    status: 200,
    description: 'Kho đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(WarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED,
    BUSINESS_ERROR_CODES.WAREHOUSE_ACCESS_DENIED
  )
  async updateWarehouse(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateWarehouseDto: UpdateWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    const warehouse = await this.userWarehouseService.updateWarehouse(
      id,
      updateWarehouseDto,
      user.id
    );
    return ApiResponseDto.success(warehouse, 'Cập nhật kho thành công');
  }

  /**
   * Xóa kho
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa kho' })
  @ApiParam({ name: 'id', description: 'ID của kho', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Kho đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
    BUSINESS_ERROR_CODES.WAREHOUSE_ACCESS_DENIED
  )
  async deleteWarehouse(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ) {
    await this.userWarehouseService.deleteWarehouse(id, user.id);
    return ApiResponseDto.success(null, 'Xóa kho thành công');
  }

  /**
   * Lấy thông tin kho theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin kho theo ID' })
  @ApiParam({ name: 'id', description: 'ID của kho', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của kho',
    schema: ApiResponseDto.getSchema(WarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_FIND_FAILED,
    BUSINESS_ERROR_CODES.WAREHOUSE_ACCESS_DENIED
  )
  async getWarehouseById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ) {
    const warehouse = await this.userWarehouseService.getWarehouseById(id, user.id);
    return ApiResponseDto.success(warehouse, 'Lấy thông tin kho thành công');
  }

  /**
   * Lấy danh sách kho với phân trang và lọc
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách kho với phân trang và lọc' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kho với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(WarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_FIND_FAILED
  )
  async getWarehouses(
    @Query() queryDto: QueryWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    queryDto.userId = user.id;
    const warehouses = await this.userWarehouseService.getWarehouses(queryDto);
    return ApiResponseDto.success(warehouses, 'Lấy danh sách kho thành công');
  }
}
