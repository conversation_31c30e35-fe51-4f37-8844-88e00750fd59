# 📋 Bulk Create Convert Customer API

## 🎯 Tổng quan

API này cho phép tạo nhiều khách hàng chuyển đổi cùng lúc trong một request duy nhất, gi<PERSON>p tối ưu hóa hiệu suất và giảm số lượng request cần thiết.

## 🔗 Endpoint

```
POST /v1/user/convert-customers/bulk
```

## 🔐 Authentication

Yêu cầu JWT token trong header:
```
Authorization: Bearer <your-jwt-token>
```

## 📥 Request Body

### Schema

```typescript
{
  customers: CreateUserConvertCustomerDto[],  // Mảng khách hàng cần tạo (1-100 items)
  skipDuplicates?: boolean,                   // Bỏ qua trùng lặp (default: false)
  continueOnError?: boolean                   // Tiếp tục khi có lỗi (default: false)
}
```

### Ví dụ Request

```json
{
  "customers": [
    {
      "name": "<PERSON><PERSON><PERSON><PERSON>",
      "phone": "0912345678",
      "email": "nguy<PERSON><EMAIL>",
      "platform": "facebook",
      "timezone": "Asia/Ho_Chi_Minh",
      "tags": ["vip", "potential"],
      "customFields": [
        {
          "configId": "customer_age",
          "label": "Tuổi khách hàng",
          "type": "number",
          "required": false,
          "configJson": {}
        },
        {
          "configId": "customer_interest",
          "label": "Sở thích",
          "type": "text",
          "required": false,
          "configJson": {}
        }
      ]
    },
    {
      "name": "Trần Thị B",
      "phone": "0987654321",
      "email": "<EMAIL>",
      "platform": "web",
      "timezone": "Asia/Ho_Chi_Minh",
      "agentId": "550e8400-e29b-41d4-a716-446655440000",
      "tags": ["new", "interested"],
      "customFields": [
        {
          "configId": "customer_job",
          "label": "Nghề nghiệp",
          "type": "text",
          "required": false,
          "configJson": {}
        }
      ]
    }
  ],
  "skipDuplicates": true,
  "continueOnError": true
}
```

## 📤 Response

### Success Response (201 Created)

```json
{
  "success": true,
  "message": "Tạo bulk khách hàng chuyển đổi thành công",
  "data": {
    "totalRequested": 2,
    "successCount": 2,
    "errorCount": 0,
    "skippedCount": 0,
    "processingTimeMs": 1500,
    "results": [
      {
        "index": 0,
        "status": "success",
        "customer": {
          "id": 1,
          "name": "Nguyễn Văn A",
          "phone": "0912345678",
          "email": "<EMAIL>",
          "platform": "facebook",
          "timezone": "Asia/Ho_Chi_Minh",
          "userId": 123,
          "createdAt": 1641708800000,
          "updatedAt": 1641708800000
        },
        "customFields": [
          {
            "id": 1,
            "configId": "customer_age",
            "label": "Tuổi khách hàng",
            "type": "number",
            "required": false,
            "configJson": {},
            "userId": 123,
            "employeeId": null,
            "status": "APPROVED",
            "createAt": 1641708800000
          }
        ],
        "originalData": {
          "name": "Nguyễn Văn A",
          "phone": "0912345678",
          "email": "<EMAIL>"
        }
      },
      {
        "index": 1,
        "status": "success",
        "customer": {
          "id": 2,
          "name": "Trần Thị B",
          "phone": "0987654321",
          "email": "<EMAIL>",
          "platform": "web",
          "timezone": "Asia/Ho_Chi_Minh",
          "userId": 123,
          "agentId": "550e8400-e29b-41d4-a716-446655440000",
          "createdAt": 1641708800000,
          "updatedAt": 1641708800000
        },
        "originalData": {
          "name": "Trần Thị B",
          "phone": "0987654321",
          "email": "<EMAIL>"
        }
      }
    ]
  }
}
```

### Partial Success Response (207 Partial Content)

```json
{
  "success": true,
  "message": "Bulk create hoàn thành với một số lỗi",
  "data": {
    "totalRequested": 3,
    "successCount": 1,
    "errorCount": 1,
    "skippedCount": 1,
    "processingTimeMs": 2000,
    "results": [
      {
        "index": 0,
        "status": "success",
        "customer": { /* customer data */ },
        "originalData": { /* original request data */ }
      },
      {
        "index": 1,
        "status": "error",
        "message": "Tên khách hàng không được để trống",
        "errorCode": "VALIDATION_ERROR",
        "originalData": { /* original request data */ }
      },
      {
        "index": 2,
        "status": "skipped",
        "message": "Số điện thoại 0912345678 đã tồn tại trong hệ thống",
        "originalData": { /* original request data */ }
      }
    ]
  }
}
```

## ⚙️ Tham số cấu hình

### skipDuplicates (boolean, optional)

- **Default**: `false`
- **Mô tả**: Xử lý khi gặp số điện thoại trùng lặp
  - `true`: Bỏ qua khách hàng trùng lặp và tiếp tục xử lý
  - `false`: Dừng toàn bộ quá trình khi gặp trùng lặp

### continueOnError (boolean, optional)

- **Default**: `false`
- **Mô tả**: Xử lý khi gặp lỗi validation
  - `true`: Tiếp tục xử lý các khách hàng khác khi gặp lỗi
  - `false`: Dừng toàn bộ quá trình khi gặp lỗi đầu tiên

## 🚨 Error Codes

| Error Code | HTTP Status | Mô tả |
|------------|-------------|-------|
| `CONVERT_CUSTOMER_BULK_CREATION_FAILED` | 500 | Lỗi khi tạo bulk khách hàng |
| `CONVERT_CUSTOMER_BULK_VALIDATION_FAILED` | 400 | Lỗi validation dữ liệu bulk |
| `CONVERT_CUSTOMER_PHONE_DUPLICATE` | 409 | Số điện thoại đã tồn tại |
| `CONVERT_CUSTOMER_CREATION_FAILED` | 500 | Lỗi khi tạo khách hàng |

## 📏 Giới hạn

- **Số lượng tối đa**: 100 khách hàng mỗi request
- **Số lượng tối thiểu**: 1 khách hàng mỗi request
- **Timeout**: 30 giây
- **Rate limit**: 10 requests/phút

## 🔧 Custom Fields

API hỗ trợ tạo trường tùy chỉnh cho mỗi khách hàng trong bulk create:

### Cấu trúc Custom Field

```typescript
{
  configId: string,        // ID cấu hình (unique)
  label: string,           // Nhãn hiển thị
  type: string,            // Loại trường (text, number, email, select, etc.)
  required: boolean,       // Trường bắt buộc hay không
  configJson: object       // Cấu hình JSON chi tiết
}
```

### Các loại trường hỗ trợ

- `text`: Văn bản
- `number`: Số
- `email`: Email
- `phone`: Số điện thoại
- `select`: Lựa chọn đơn
- `multiselect`: Lựa chọn nhiều
- `date`: Ngày
- `datetime`: Ngày giờ
- `textarea`: Văn bản dài
- `checkbox`: Hộp kiểm
- `radio`: Nút radio

### Ví dụ Custom Fields (Đơn giản)

```json
{
  "customFields": [
    {
      "configId": "customer_age",
      "label": "Tuổi khách hàng",
      "type": "number",
      "required": false,
      "configJson": {}
    },
    {
      "configId": "customer_interest",
      "label": "Sở thích",
      "type": "text",
      "required": false,
      "configJson": {}
    },
    {
      "configId": "customer_notes",
      "label": "Ghi chú",
      "type": "textarea",
      "required": false,
      "configJson": {}
    }
  ]
}
```

### Ví dụ Custom Fields (Chi tiết - Tùy chọn)

```json
{
  "customFields": [
    {
      "configId": "customer_age",
      "label": "Tuổi khách hàng",
      "type": "number",
      "required": true,
      "configJson": {
        "min": 18,
        "max": 100,
        "placeholder": "Nhập tuổi",
        "validation": {
          "pattern": "^[0-9]+$"
        }
      }
    },
    {
      "configId": "customer_interest",
      "label": "Sở thích",
      "type": "multiselect",
      "required": false,
      "configJson": {
        "options": [
          { "value": "sports", "label": "Thể thao" },
          { "value": "music", "label": "Âm nhạc" },
          { "value": "travel", "label": "Du lịch" }
        ],
        "placeholder": "Chọn sở thích"
      }
    }
  ]
}
```

## 💡 Best Practices

### 1. Sử dụng skipDuplicates và continueOnError

```json
{
  "customers": [...],
  "skipDuplicates": true,
  "continueOnError": true
}
```

### 2. Xử lý kết quả

```javascript
const response = await fetch('/v1/user/convert-customers/bulk', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(bulkData)
});

const result = await response.json();

// Kiểm tra kết quả
result.data.results.forEach((item, index) => {
  switch (item.status) {
    case 'success':
      console.log(`Customer ${index} created with ID: ${item.customer.id}`);
      break;
    case 'error':
      console.error(`Customer ${index} failed: ${item.message}`);
      break;
    case 'skipped':
      console.warn(`Customer ${index} skipped: ${item.message}`);
      break;
  }
});
```

### 3. Batch processing cho số lượng lớn

```javascript
// Chia nhỏ danh sách lớn thành các batch 100 items
const batchSize = 100;
const customers = [...]; // Danh sách lớn

for (let i = 0; i < customers.length; i += batchSize) {
  const batch = customers.slice(i, i + batchSize);

  const result = await createBulkCustomers({
    customers: batch,
    skipDuplicates: true,
    continueOnError: true
  });

  // Xử lý kết quả batch
  console.log(`Batch ${Math.floor(i/batchSize) + 1}: ${result.data.successCount}/${batch.length} success`);
}
```

## 🔄 So sánh với Single Create

| Aspect | Single Create | Bulk Create |
|--------|---------------|-------------|
| **Performance** | 1 customer/request | Up to 100 customers/request |
| **Network calls** | N requests | 1 request |
| **Transaction** | Per customer | Per batch |
| **Error handling** | Immediate failure | Configurable (continue/stop) |
| **Duplicate handling** | Manual check | Built-in options |

## 📝 Changelog

- **v1.0.0**: Initial release của Bulk Create API
- Hỗ trợ tạo tối đa 100 khách hàng mỗi request
- Tích hợp skipDuplicates và continueOnError options
- Transaction-based processing để đảm bảo data consistency
