# 🧪 Bulk Create Convert Customer - Examples & Test Cases

## 📋 Ví dụ sử dụng cơ bản

### 1. <PERSON><PERSON><PERSON> <PERSON>hi<PERSON>u khách hàng thành công

```bash
curl -X POST "http://localhost:3000/v1/user/convert-customers/bulk" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customers": [
      {
        "name": "Nguyễn Văn A",
        "phone": "0912345678",
        "email": {"primary": "<EMAIL>"},
        "platform": "facebook",
        "timezone": "Asia/Ho_Chi_Minh",
        "tags": ["vip", "potential"]
      },
      {
        "name": "Trần Thị B", 
        "phone": "0987654321",
        "email": {"primary": "<EMAIL>"},
        "platform": "web",
        "timezone": "Asia/Ho_Chi_Minh"
      }
    ],
    "skipDuplicates": false,
    "continueOnError": false
  }'
```

### 2. Tạo với bỏ qua trùng lặp

```bash
curl -X POST "http://localhost:3000/v1/user/convert-customers/bulk" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customers": [
      {
        "name": "Nguyễn Văn A",
        "phone": "0912345678",
        "email": {"primary": "<EMAIL>"},
        "platform": "facebook"
      },
      {
        "name": "Nguyễn Văn A Duplicate",
        "phone": "0912345678",
        "email": {"primary": "<EMAIL>"},
        "platform": "web"
      },
      {
        "name": "Trần Thị B",
        "phone": "0987654321", 
        "email": {"primary": "<EMAIL>"},
        "platform": "web"
      }
    ],
    "skipDuplicates": true,
    "continueOnError": true
  }'
```

## 🧪 Test Cases

### Test Case 1: Tất cả thành công

**Request:**
```json
{
  "customers": [
    {
      "name": "Test User 1",
      "phone": "0901111111",
      "email": {"primary": "<EMAIL>"},
      "platform": "facebook"
    },
    {
      "name": "Test User 2", 
      "phone": "0902222222",
      "email": {"primary": "<EMAIL>"},
      "platform": "web"
    }
  ]
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Tạo bulk khách hàng chuyển đổi thành công",
  "data": {
    "totalRequested": 2,
    "successCount": 2,
    "errorCount": 0,
    "skippedCount": 0,
    "results": [
      {
        "index": 0,
        "status": "success",
        "customer": { /* customer data */ }
      },
      {
        "index": 1,
        "status": "success", 
        "customer": { /* customer data */ }
      }
    ]
  }
}
```

### Test Case 2: Có lỗi validation

**Request:**
```json
{
  "customers": [
    {
      "name": "",
      "phone": "invalid-phone",
      "email": "invalid-email"
    },
    {
      "name": "Valid User",
      "phone": "0901111111",
      "email": {"primary": "<EMAIL>"}
    }
  ],
  "continueOnError": true
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Bulk create hoàn thành với một số lỗi",
  "data": {
    "totalRequested": 2,
    "successCount": 1,
    "errorCount": 1,
    "skippedCount": 0,
    "results": [
      {
        "index": 0,
        "status": "error",
        "message": "Tên khách hàng không được để trống",
        "errorCode": "VALIDATION_ERROR"
      },
      {
        "index": 1,
        "status": "success",
        "customer": { /* customer data */ }
      }
    ]
  }
}
```

### Test Case 3: Trùng lặp số điện thoại

**Request:**
```json
{
  "customers": [
    {
      "name": "User 1",
      "phone": "0901111111",
      "email": {"primary": "<EMAIL>"}
    },
    {
      "name": "User 2",
      "phone": "0901111111",
      "email": {"primary": "<EMAIL>"}
    }
  ],
  "skipDuplicates": true
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Bulk create hoàn thành với một số lỗi",
  "data": {
    "totalRequested": 2,
    "successCount": 1,
    "errorCount": 0,
    "skippedCount": 1,
    "results": [
      {
        "index": 0,
        "status": "success",
        "customer": { /* customer data */ }
      },
      {
        "index": 1,
        "status": "skipped",
        "message": "Số điện thoại 0901111111 bị trùng lặp trong request"
      }
    ]
  }
}
```

### Test Case 4: Quá giới hạn số lượng

**Request:**
```json
{
  "customers": [
    /* 101 customers - exceeds limit of 100 */
  ]
}
```

**Expected Response:**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "customers",
      "message": "Không được tạo quá 100 khách hàng cùng lúc"
    }
  ]
}
```

## 🔧 JavaScript/TypeScript Examples

### Sử dụng với Fetch API

```javascript
async function createBulkCustomers(customers, options = {}) {
  const response = await fetch('/v1/user/convert-customers/bulk', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${getAuthToken()}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      customers,
      skipDuplicates: options.skipDuplicates || false,
      continueOnError: options.continueOnError || false
    })
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Sử dụng
const customers = [
  {
    name: "Nguyễn Văn A",
    phone: "0912345678",
    email: {"primary": "<EMAIL>"},
    platform: "facebook"
  },
  {
    name: "Trần Thị B",
    phone: "0987654321", 
    email: {"primary": "<EMAIL>"},
    platform: "web"
  }
];

try {
  const result = await createBulkCustomers(customers, {
    skipDuplicates: true,
    continueOnError: true
  });
  
  console.log(`Created ${result.data.successCount}/${result.data.totalRequested} customers`);
  
  // Xử lý kết quả chi tiết
  result.data.results.forEach((item, index) => {
    switch (item.status) {
      case 'success':
        console.log(`✅ Customer ${index}: Created with ID ${item.customer.id}`);
        break;
      case 'error':
        console.error(`❌ Customer ${index}: ${item.message}`);
        break;
      case 'skipped':
        console.warn(`⏭️ Customer ${index}: ${item.message}`);
        break;
    }
  });
} catch (error) {
  console.error('Bulk create failed:', error);
}
```

### Sử dụng với Axios

```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3000/v1',
  headers: {
    'Authorization': `Bearer ${getAuthToken()}`
  }
});

async function bulkCreateCustomers(data) {
  try {
    const response = await api.post('/user/convert-customers/bulk', data);
    return response.data;
  } catch (error) {
    if (error.response?.status === 207) {
      // Partial success - still return data
      return error.response.data;
    }
    throw error;
  }
}
```

## 📊 Performance Testing

### Batch Processing cho số lượng lớn

```javascript
async function processBulkCustomers(allCustomers, batchSize = 100) {
  const results = [];
  
  for (let i = 0; i < allCustomers.length; i += batchSize) {
    const batch = allCustomers.slice(i, i + batchSize);
    
    console.log(`Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(allCustomers.length/batchSize)}`);
    
    try {
      const result = await createBulkCustomers(batch, {
        skipDuplicates: true,
        continueOnError: true
      });
      
      results.push(result);
      
      // Delay giữa các batch để tránh rate limit
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`Batch ${Math.floor(i/batchSize) + 1} failed:`, error);
      results.push({ error: error.message, batch: i/batchSize + 1 });
    }
  }
  
  return results;
}
```

## 🚨 Error Handling Best Practices

```javascript
async function robustBulkCreate(customers) {
  try {
    const result = await createBulkCustomers(customers, {
      skipDuplicates: true,
      continueOnError: true
    });
    
    // Kiểm tra kết quả
    if (result.data.errorCount > 0) {
      console.warn(`${result.data.errorCount} customers failed to create`);
      
      // Log chi tiết các lỗi
      const errors = result.data.results
        .filter(r => r.status === 'error')
        .map(r => ({ index: r.index, message: r.message, data: r.originalData }));
      
      console.table(errors);
    }
    
    if (result.data.successCount === 0) {
      throw new Error('No customers were created successfully');
    }
    
    return result;
    
  } catch (error) {
    console.error('Bulk create completely failed:', error);
    
    // Fallback: thử tạo từng customer riêng lẻ
    console.log('Attempting individual creation as fallback...');
    return await fallbackIndividualCreate(customers);
  }
}

async function fallbackIndividualCreate(customers) {
  const results = [];
  
  for (let i = 0; i < customers.length; i++) {
    try {
      const customer = await createSingleCustomer(customers[i]);
      results.push({
        index: i,
        status: 'success',
        customer,
        originalData: customers[i]
      });
    } catch (error) {
      results.push({
        index: i,
        status: 'error',
        message: error.message,
        originalData: customers[i]
      });
    }
  }
  
  return {
    data: {
      totalRequested: customers.length,
      successCount: results.filter(r => r.status === 'success').length,
      errorCount: results.filter(r => r.status === 'error').length,
      skippedCount: 0,
      results
    }
  };
}
```
