import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả từng item trong bulk delete trường tùy chỉnh
 */
export class BulkDeleteCustomFieldResultItemDto {
  /**
   * ID trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID trường tùy chỉnh',
    example: 1,
  })
  customFieldId: number;

  /**
   * Trạng thái xử lý
   * - success: Xóa thành công
   * - error: Có lỗi xảy ra
   * @example "success"
   */
  @ApiProperty({
    description: 'Trạng thái xử lý',
    enum: ['success', 'error'],
    example: 'success',
  })
  status: 'success' | 'error';

  /**
   * Thông báo chi tiết
   * @example "Xóa trường tùy chỉnh thành công"
   */
  @ApiProperty({
    description: 'Thông báo chi tiết',
    example: 'Xóa trường tùy chỉnh thành công',
  })
  message: string;
}

/**
 * DTO cho phản hồi bulk delete trường tùy chỉnh
 */
export class BulkDeleteCustomFieldResponseDto {
  /**
   * Tổng số trường tùy chỉnh được yêu cầu xóa
   * @example 5
   */
  @ApiProperty({
    description: 'Tổng số trường tùy chỉnh được yêu cầu xóa',
    example: 5,
  })
  totalRequested: number;

  /**
   * Số trường tùy chỉnh xóa thành công
   * @example 3
   */
  @ApiProperty({
    description: 'Số trường tùy chỉnh xóa thành công',
    example: 3,
  })
  successCount: number;

  /**
   * Số trường tùy chỉnh xóa thất bại
   * @example 2
   */
  @ApiProperty({
    description: 'Số trường tùy chỉnh xóa thất bại',
    example: 2,
  })
  failureCount: number;

  /**
   * Kết quả chi tiết cho từng trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Kết quả chi tiết cho từng trường tùy chỉnh',
    type: [BulkDeleteCustomFieldResultItemDto],
  })
  results: BulkDeleteCustomFieldResultItemDto[];

  /**
   * Thông báo tổng quan
   * @example "Xóa thành công 3/5 trường tùy chỉnh"
   */
  @ApiProperty({
    description: 'Thông báo tổng quan',
    example: 'Xóa thành công 3/5 trường tùy chỉnh',
  })
  message: string;
}
