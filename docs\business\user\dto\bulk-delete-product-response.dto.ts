import { ApiProperty } from '@nestjs/swagger';

/**
 * D<PERSON> cho kết quả từng item trong bulk delete sản phẩm
 */
export class BulkDeleteProductResultItemDto {
  /**
   * ID sản phẩm
   * @example 1
   */
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1,
  })
  productId: number;

  /**
   * Trạng thái xử lý
   * - success: Xóa thành công
   * - error: Có lỗi xảy ra
   * @example "success"
   */
  @ApiProperty({
    description: 'Trạng thái xử lý',
    enum: ['success', 'error'],
    example: 'success',
  })
  status: 'success' | 'error';

  /**
   * Thông báo chi tiết
   * @example "Xóa sản phẩm thành công"
   */
  @ApiProperty({
    description: 'Thông báo chi tiết',
    example: 'Xóa sản phẩm thành công',
  })
  message: string;
}

/**
 * D<PERSON> cho phản hồi bulk delete sản phẩm
 */
export class BulkDeleteProductResponseDto {
  /**
   * Tổng số sản phẩm được yêu cầu xóa
   * @example 5
   */
  @ApiProperty({
    description: 'Tổng số sản phẩm được yêu cầu xóa',
    example: 5,
  })
  totalRequested: number;

  /**
   * Số sản phẩm xóa thành công
   * @example 3
   */
  @ApiProperty({
    description: 'Số sản phẩm xóa thành công',
    example: 3,
  })
  successCount: number;

  /**
   * Số sản phẩm xóa thất bại
   * @example 2
   */
  @ApiProperty({
    description: 'Số sản phẩm xóa thất bại',
    example: 2,
  })
  failureCount: number;

  /**
   * Kết quả chi tiết cho từng sản phẩm
   */
  @ApiProperty({
    description: 'Kết quả chi tiết cho từng sản phẩm',
    type: [BulkDeleteProductResultItemDto],
  })
  results: BulkDeleteProductResultItemDto[];

  /**
   * Thông báo tổng quan
   * @example "Xóa thành công 3/5 sản phẩm"
   */
  @ApiProperty({
    description: 'Thông báo tổng quan',
    example: 'Xóa thành công 3/5 sản phẩm',
  })
  message: string;
}
