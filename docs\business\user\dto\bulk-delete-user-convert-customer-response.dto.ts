import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả từng item trong bulk delete khách hàng chuyển đổi
 */
export class BulkDeleteUserConvertCustomerResultItemDto {
  /**
   * ID khách hàng chuyển đổi
   * @example 1
   */
  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: 1,
  })
  customerId: number;

  /**
   * Trạng thái xử lý
   * - success: Xóa thành công
   * - error: Có lỗi xảy ra
   * @example "success"
   */
  @ApiProperty({
    description: 'Trạng thái xử lý',
    enum: ['success', 'error'],
    example: 'success',
  })
  status: 'success' | 'error';

  /**
   * Thông báo chi tiết
   * @example "Xóa khách hàng chuyển đổi thành công"
   */
  @ApiProperty({
    description: 'Thông báo chi tiết',
    example: '<PERSON><PERSON><PERSON> khách hàng chuyển đổi thành công',
  })
  message: string;
}

/**
 * DTO cho phản hồi bulk delete khách hàng chuyển đổi
 */
export class BulkDeleteUserConvertCustomerResponseDto {
  /**
   * Tổng số khách hàng chuyển đổi được yêu cầu xóa
   * @example 5
   */
  @ApiProperty({
    description: 'Tổng số khách hàng chuyển đổi được yêu cầu xóa',
    example: 5,
  })
  totalRequested: number;

  /**
   * Số khách hàng chuyển đổi xóa thành công
   * @example 3
   */
  @ApiProperty({
    description: 'Số khách hàng chuyển đổi xóa thành công',
    example: 3,
  })
  successCount: number;

  /**
   * Số khách hàng chuyển đổi xóa thất bại
   * @example 2
   */
  @ApiProperty({
    description: 'Số khách hàng chuyển đổi xóa thất bại',
    example: 2,
  })
  failureCount: number;

  /**
   * Kết quả chi tiết cho từng khách hàng chuyển đổi
   */
  @ApiProperty({
    description: 'Kết quả chi tiết cho từng khách hàng chuyển đổi',
    type: [BulkDeleteUserConvertCustomerResultItemDto],
  })
  results: BulkDeleteUserConvertCustomerResultItemDto[];

  /**
   * Thông báo tổng quan
   * @example "Xóa thành công 3/5 khách hàng chuyển đổi"
   */
  @ApiProperty({
    description: 'Thông báo tổng quan',
    example: 'Xóa thành công 3/5 khách hàng chuyển đổi',
  })
  message: string;
}
