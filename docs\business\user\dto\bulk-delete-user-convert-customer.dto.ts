import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, ArrayUnique } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho yêu cầu xóa nhiều khách hàng chuyển đổi
 */
export class BulkDeleteUserConvertCustomerDto {
  /**
   * Danh sách ID khách hàng chuyển đổi cần xóa
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID khách hàng chuyển đổi cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'Danh sách ID khách hàng chuyển đổi phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một khách hàng chuyển đổi để xóa' })
  @ArrayUnique({ message: 'Danh sách ID khách hàng chuyển đổi không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID khách hàng chuyển đổi phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: 'Danh sách ID khách hàng chuyển đổi không được để trống' })
  customerIds: number[];
}
