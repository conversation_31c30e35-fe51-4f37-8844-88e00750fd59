import { ApiProperty } from '@nestjs/swagger';
import { UserConvertCustomerResponseDto } from './user-convert-customer-response.dto';


/**
 * DTO cho kết quả từng item trong bulk create
 */
export class BulkCreateResultItemDto {
  /**
   * Chỉ số của item trong mảng request (bắt đầu từ 0)
   * @example 0
   */
  @ApiProperty({
    description: 'Chỉ số của item trong mảng request (bắt đầu từ 0)',
    example: 0,
  })
  index: number;

  /**
   * Trạng thái xử lý
   * - success: Tạo thành công
   * - error: Có lỗi xảy ra
   * - skipped: Bỏ qua do trùng lặp
   * @example "success"
   */
  @ApiProperty({
    description: 'Trạng thái xử lý',
    enum: ['success', 'error', 'skipped'],
    example: 'success',
  })
  status: 'success' | 'error' | 'skipped';

  /**
   * Thông tin khách hàng đã được tạo (chỉ có khi status = success)
   * @example { "id": 1, "name": "Nguyễn Văn A", "phone": "0912345678" }
   */
  @ApiProperty({
    description: 'Thông tin khách hàng đã được tạo (chỉ có khi status = success)',
    type: UserConvertCustomerResponseDto,
    required: false,
  })
  customer?: UserConvertCustomerResponseDto;

  /**
   * Thông báo lỗi (chỉ có khi status = error hoặc skipped)
   * @example "Số điện thoại đã tồn tại trong hệ thống"
   */
  @ApiProperty({
    description: 'Thông báo lỗi (chỉ có khi status = error hoặc skipped)',
    example: 'Số điện thoại đã tồn tại trong hệ thống',
    required: false,
  })
  message?: string;

  /**
   * Mã lỗi chi tiết (chỉ có khi status = error)
   * @example "CONVERT_CUSTOMER_PHONE_DUPLICATE"
   */
  @ApiProperty({
    description: 'Mã lỗi chi tiết (chỉ có khi status = error)',
    example: 'CONVERT_CUSTOMER_PHONE_DUPLICATE',
    required: false,
  })
  errorCode?: string;



  /**
   * Dữ liệu gốc từ request
   * @example { "name": "Nguyễn Văn A", "phone": "0912345678" }
   */
  @ApiProperty({
    description: 'Dữ liệu gốc từ request',
    example: { name: 'Nguyễn Văn A', phone: '0912345678' },
  })
  originalData: any;
}

/**
 * DTO cho response của bulk create convert customers
 */
export class BulkUserConvertCustomerResponseDto {
  /**
   * Tổng số khách hàng trong request
   * @example 10
   */
  @ApiProperty({
    description: 'Tổng số khách hàng trong request',
    example: 10,
  })
  totalRequested: number;

  /**
   * Số khách hàng tạo thành công
   * @example 8
   */
  @ApiProperty({
    description: 'Số khách hàng tạo thành công',
    example: 8,
  })
  successCount: number;

  /**
   * Số khách hàng bị lỗi
   * @example 1
   */
  @ApiProperty({
    description: 'Số khách hàng bị lỗi',
    example: 1,
  })
  errorCount: number;

  /**
   * Số khách hàng bị bỏ qua
   * @example 1
   */
  @ApiProperty({
    description: 'Số khách hàng bị bỏ qua',
    example: 1,
  })
  skippedCount: number;

  /**
   * Chi tiết kết quả cho từng khách hàng
   */
  @ApiProperty({
    description: 'Chi tiết kết quả cho từng khách hàng',
    type: [BulkCreateResultItemDto],
    example: [
      {
        index: 0,
        status: 'success',
        customer: {
          id: 1,
          name: 'Nguyễn Văn A',
          phone: '0912345678',
          email: '<EMAIL>',
          platform: 'facebook',
          timezone: 'Asia/Ho_Chi_Minh',
          userId: 123,
          createdAt: 1641708800000,
          updatedAt: 1641708800000
        },

        originalData: {
          name: 'Nguyễn Văn A',
          phone: '0912345678',
          email: '<EMAIL>',
          platform: 'facebook',
          timezone: 'Asia/Ho_Chi_Minh',
          tags: ['vip', 'potential']
        },
      },
      {
        index: 1,
        status: 'error',
        message: 'Số điện thoại đã tồn tại trong hệ thống',
        errorCode: 'CONVERT_CUSTOMER_PHONE_DUPLICATE',
        originalData: {
          name: 'Trần Thị B',
          phone: '0987654321',
          email: '<EMAIL>',
          platform: 'web',
          timezone: 'Asia/Ho_Chi_Minh',
          tags: ['new']
        },
      },
    ],
  })
  results: BulkCreateResultItemDto[];

  /**
   * Thời gian xử lý (milliseconds)
   * @example 1500
   */
  @ApiProperty({
    description: 'Thời gian xử lý (milliseconds)',
    example: 1500,
  })
  processingTimeMs: number;
}
