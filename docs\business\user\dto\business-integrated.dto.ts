import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  ValidateNested
} from 'class-validator';
import {
  CreateCustomFieldDto,
  UpdateCustomFieldDto,
  // CreateCustomGroupFormDto, UpdateCustomGroupFormDto đã bị xóa
  BusinessCreateProductDto,
  BusinessUpdateProductDto
} from '../dto';
import { ApiProperty } from '@nestjs/swagger';
import { CreateClassificationDto, UpdateClassificationDto, ClassificationResponseDto } from './classification.dto';

/**
 * DTO cho tạo tích hợp business bao gồm sản phẩm, nhóm trường và các trường tùy chỉnh
 *
 * Lưu ý: Phân loại sản phẩm (classifications) nên được đặt trong thuộc tính product.classifications
 * thay vì ở cấp cao nhất của DTO này để tránh trùng lặp dữ liệu.
 */
export class CreateIntegratedBusinessDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm',
    type: () => BusinessCreateProductDto
  })
  @IsObject()
  @ValidateNested()
  @Type(() => BusinessCreateProductDto)
  product: BusinessCreateProductDto;

  // groupForm đã bị xóa - không còn sử dụng CustomGroupForm

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh',
    type: [CreateCustomFieldDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  customFields: CreateCustomFieldDto[];

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm (không khuyến khích sử dụng, nên đặt trong product.classifications)',
    type: [CreateClassificationDto],
    required: false,
    deprecated: true
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateClassificationDto)
  classifications?: CreateClassificationDto[];
}

/**
 * DTO cho thông tin trường tùy chỉnh khi cập nhật
 */
export class CustomFieldUpdateItem {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh (nếu cập nhật trường hiện có)',
    example: 56,
    required: false
  })
  @IsNumber()
  @IsOptional()
  id?: number;

  @ApiProperty({
    description: 'Thông tin cập nhật trường',
    type: () => UpdateCustomFieldDto
  })
  @IsObject()
  @ValidateNested()
  @Type(() => UpdateCustomFieldDto)
  field: UpdateCustomFieldDto;
}

/**
 * DTO cho cập nhật tích hợp business
 */
export class UpdateIntegratedBusinessDto {
  @ApiProperty({
    description: 'ID sản phẩm cần cập nhật',
    example: 123,
    required: false
  })
  @IsNumber()
  @IsOptional()
  productId?: number;

  @ApiProperty({
    description: 'Thông tin cập nhật sản phẩm',
    type: () => BusinessUpdateProductDto,
    required: false
  })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessUpdateProductDto)
  product?: BusinessUpdateProductDto;

  @ApiProperty({
    description: 'ID nhóm trường cần cập nhật',
    example: 45,
    required: false
  })
  @IsNumber()
  @IsOptional()
  groupFormId?: number;

  // groupForm đã bị xóa - không còn sử dụng CustomGroupForm
  /*
  @ApiProperty({
    description: 'Thông tin cập nhật nhóm trường',
    type: () => UpdateCustomGroupFormDto,
    required: false
  })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  groupForm?: UpdateCustomGroupFormDto;
  */

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh cần cập nhật hoặc thêm mới',
    type: [CustomFieldUpdateItem],
    required: false
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldUpdateItem)
  customFields?: CustomFieldUpdateItem[];

  @ApiProperty({
    description: 'Danh sách ID trường tùy chỉnh cần xóa',
    example: [57, 62],
    required: false,
    type: [Number]
  })
  @IsArray()
  @IsOptional()
  @IsNumber({}, { each: true })
  customFieldsToDelete?: number[];

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm cần cập nhật hoặc thêm mới',
    type: [UpdateClassificationDto],
    required: false
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateClassificationDto)
  classifications?: UpdateClassificationDto[];

  @ApiProperty({
    description: 'Danh sách ID phân loại sản phẩm cần xóa',
    example: [5, 8],
    required: false,
    type: [Number]
  })
  @IsArray()
  @IsOptional()
  @IsNumber({}, { each: true })
  classificationsToDelete?: number[];
}

/**
 * DTO cho xóa tích hợp business
 */
export class DeleteIntegratedBusinessDto {
  @ApiProperty({
    description: 'ID sản phẩm cần xóa',
    example: 123
  })
  @IsNumber()
  @IsNotEmpty()
  productId: number;
}

/**
 * DTO cho tạo tích hợp business không bao gồm phân loại sản phẩm
 */
export class CreateIntegratedBusinessWithoutClassificationDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm',
    type: () => BusinessCreateProductDto
  })
  @IsObject()
  @ValidateNested()
  @Type(() => BusinessCreateProductDto)
  product: BusinessCreateProductDto;

  // groupForm đã bị xóa - không còn sử dụng CustomGroupForm

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh',
    type: [CreateCustomFieldDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  customFields: CreateCustomFieldDto[];
}

/**
 * DTO cho phản hồi khi thao tác với tích hợp business
 */
export class IntegratedBusinessResponseDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm'
  })
  @IsObject()
  product: any;

  @ApiProperty({
    description: 'Thông tin nhóm trường',
    required: false
  })
  @IsObject()
  @IsOptional()
  // groupForm: any; // CustomGroupForm đã bị xóa

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh',
    type: [Object]
  })
  @IsArray()
  customFields: any[];

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [ClassificationResponseDto],
    required: false
  })
  @IsArray()
  @IsOptional()
  classifications?: ClassificationResponseDto[];
}

/**
 * DTO cho phản hồi khi thao tác với tích hợp business không bao gồm phân loại sản phẩm
 */
export class IntegratedBusinessWithoutClassificationResponseDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm'
  })
  @IsObject()
  product: any;

  @ApiProperty({
    description: 'Thông tin nhóm trường',
    required: false
  })
  @IsObject()
  @IsOptional()
  // groupForm: any; // CustomGroupForm đã bị xóa

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh',
    type: [Object]
  })
  @IsArray()
  customFields: any[];
}