import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';
import { OrderAddressDto } from './user-address.dto';

/**
 * DTO cho thông tin sản phẩm trong request tính phí vận chuyển
 */
export class ShippingProductDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm',
    example: 2
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  quantity: number;
}

/**
 * DTO cho request tính phí vận chuyển
 */
export class CalculateShippingFeeRequestDto {
  @ApiProperty({
    description: 'ID shop để lấy địa chỉ gửi',
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  shopId: number;

  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [ShippingProductDto],
    example: [
      {
        productId: 1,
        quantity: 2
      },
      {
        productId: 2,
        quantity: 1
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ShippingProductDto)
  products: ShippingProductDto[];

  @ApiProperty({
    description: 'ID khách hàng để lấy địa chỉ mặc định (nếu không có deliveryAddress)',
    example: 18,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  customerId?: number;

  @ApiProperty({
    description: 'Thông tin địa chỉ giao hàng (chọn có sẵn hoặc tạo mới). Nếu không truyền sẽ sử dụng địa chỉ của customer.',
    type: OrderAddressDto,
    required: false,
    examples: {
      'Sử dụng địa chỉ có sẵn': {
        summary: 'Chọn địa chỉ đã lưu trong hệ thống',
        value: {
          addressId: 1
        }
      },
      'Tạo địa chỉ mới': {
        summary: 'Tạo địa chỉ mới để tính phí',
        value: {
          newAddress: {
            recipientName: "Nguyễn Văn A",
            recipientPhone: "0912345678",
            address: "123 Đường ABC, Phường 1",
            province: "TP. Hồ Chí Minh",
            district: "Quận 1",
            ward: "Phường Bến Nghé",
            postalCode: "70000",
            isDefault: false,
            addressType: "home",
            note: "Gần chợ Bến Thành"
          }
        }
      }
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OrderAddressDto)
  deliveryAddress?: OrderAddressDto;

  @ApiProperty({
    description: 'Đơn vị vận chuyển ưu tiên',
    enum: ['GHN', 'GHTK'],
    example: 'GHN',
    examples: {
      'GHN': {
        value: 'GHN',
        description: 'Giao Hàng Nhanh - phù hợp cho nội thành và liên tỉnh'
      },
      'GHTK': {
        value: 'GHTK',
        description: 'Giao Hàng Tiết Kiệm - phù hợp cho các tỉnh xa'
      }
    }
  })
  @IsNotEmpty()
  @IsEnum(['GHN', 'GHTK'])
  preferredCarrier: 'GHN' | 'GHTK';
}

/**
 * DTO cho response tính phí vận chuyển
 */
export class CalculateShippingFeeResponseDto {
  @ApiProperty({
    description: 'Đơn vị vận chuyển được chọn',
    example: 'GHN'
  })
  carrier: string;

  @ApiProperty({
    description: 'Phí vận chuyển (VND)',
    example: 30000
  })
  fee: number;

  @ApiProperty({
    description: 'Loại dịch vụ',
    example: 'standard'
  })
  serviceType: string;

  @ApiProperty({
    description: 'Thời gian giao hàng dự kiến',
    example: '2-3 ngày',
    required: false
  })
  estimatedDeliveryTime?: string;
}
