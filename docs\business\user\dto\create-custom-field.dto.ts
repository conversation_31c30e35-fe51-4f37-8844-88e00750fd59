import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  IsEnum,
} from 'class-validator';
import { CustomFieldTypeEnum } from '@modules/business/enums';

/**
 * DTO cho tạo trường tùy chỉnh
 */
export class CreateCustomFieldDto {

  /**
   * ID cấu hình (bắt buộc nhập)
   * @example "custom-text-001"
   */
  @ApiProperty({
    description: 'ID cấu hình (bắt buộc nhập)',
    example: 'custom-text-001',
    required: true
  })
  @IsNotEmpty({ message: 'ID cấu hình không được để trống' })
  @IsString({ message: 'ID cấu hình phải là chuỗi' })
  configId: string;

  /**
   * Nhãn hiển thị
   * @example "Số điện thoại"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Số điện thoại',
  })
  @IsNotEmpty({ message: 'Nhãn hiển thị không được để trống' })
  @IsString({ message: 'Nhãn hiển thị phải là chuỗi' })
  @MaxLength(255, { message: 'Nhãn hiển thị không được vượt quá 255 ký tự' })
  label: string;

  /**
   * Loại trường
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
    enum: CustomFieldTypeEnum,
    enumName: 'CustomFieldTypeEnum',
  })
  @IsNotEmpty({ message: 'Loại trường không được để trống' })
  @IsEnum(CustomFieldTypeEnum, {
    message: `Loại trường phải là một trong các giá trị: ${Object.values(CustomFieldTypeEnum).join(', ')}`
  })
  type: CustomFieldTypeEnum;

  /**
   * Trường bắt buộc hay không
   * @example true
   */
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  @IsBoolean({ message: 'Trường bắt buộc phải là boolean' })
  required: boolean;

  /**
   * Cấu hình JSON
   * @example { "validation": { "pattern": "^[0-9]{10}$" }, "placeholder": "Nhập số điện thoại", "variant": "outlined", "size": "small" }
   */
  @ApiProperty({
    description: 'Cấu hình JSON',
    example: {
      validation: { pattern: '^[0-9]{10}$' },
      placeholder: 'Nhập số điện thoại',
      variant: 'outlined',
      size: 'small',
    },
  })
  @IsObject({ message: 'Cấu hình JSON phải là đối tượng' })
  @IsOptional()
  configJson: any = {};

  /**
   * ID nhân viên tạo
   * @example 1
   */
  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID nhân viên tạo phải là số' })
  employeeId?: number;

  /**
   * ID người dùng tạo
   * @example 1001
   */
  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1001,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng tạo phải là số' })
  userId?: number;

  /**
   * ID nhóm trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID nhóm trường tùy chỉnh',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID nhóm trường tùy chỉnh phải là số' })
  formGroupId?: number;

  /**
   * Cấu hình grid
   * @example { "i": "field1", "x": 0, "y": 0, "w": 6, "h": 2 }
   */
  @IsOptional()
  @IsObject({ message: 'Cấu hình grid phải là đối tượng' })
  grid?: any = { i: 'default', x: 0, y: 0, w: 6, h: 2 };

  /**
   * Giá trị mặc định
   * @example { "value": "example" }
   */
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: { value: 'example' },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Giá trị mặc định phải là đối tượng' })
  value?: { value: string } = { value: '' };

  /**
   * Tags để phân loại trường tùy chỉnh
   * @example ["product", "classification"]
   */
  @ApiProperty({
    description: 'Tags để phân loại trường tùy chỉnh',
    example: ['product', 'classification'],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[] = [];
}
