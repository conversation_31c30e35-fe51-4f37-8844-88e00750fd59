import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response khi lấy danh sách trường tùy chỉnh
 */
export class CustomFieldListItemDto {
  @Expose()
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
  })
  component: string;

  @Expose()
  @ApiProperty({
    description: 'ID cấu hình',
    example: 'text-input',
  })
  configId: string;

  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Họ và tên',
  })
  label: string;

  @Expose()
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  type: string;

  @Expose()
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  required: boolean;

  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON>u hình JSON',
    example: {
      validation: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z0-9 ]*$' },
      placeholder: 'Nhập họ và tên',
      variant: 'outlined',
      size: 'small',
    },
  })
  configJson: any;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1001,
    nullable: true,
  })
  userId: number | null;

  @Expose()
  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: null,
    nullable: true,
  })
  employeeId: number | null;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái của trường',
    example: 'PENDING',
  })
  status: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createAt: number;
}
