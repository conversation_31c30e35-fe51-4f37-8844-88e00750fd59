import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response khi lấy danh sách nhóm trường tùy chỉnh
 */
export class CustomGroupFormListItemDto {
  @Expose()
  @ApiProperty({
    description: 'ID của nhóm trường tùy chỉnh',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Thông tin cá nhân',
  })
  label: string;



  @Expose()
  @ApiProperty({
    description: 'ID sản phẩm liên kết',
    example: 201,
    nullable: true,
  })
  productId: number | null;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createAt: number;
}
