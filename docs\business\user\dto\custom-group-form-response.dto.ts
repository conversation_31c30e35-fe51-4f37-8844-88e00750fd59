import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho cấu hình grid của trường tùy chỉnh
 */
export class GridConfigDto {
  @Expose()
  @ApiProperty({
    description: 'ID của grid',
    example: 'text-input',
  })
  i: string;

  @Expose()
  @ApiProperty({
    description: 'Vị trí x',
    example: 0,
  })
  x: number;

  @Expose()
  @ApiProperty({
    description: 'Vị trí y',
    example: 0,
  })
  y: number;

  @Expose()
  @ApiProperty({
    description: 'Chiều rộng',
    example: 4,
  })
  w: number;

  @Expose()
  @ApiProperty({
    description: 'Chiều cao',
    example: 2,
  })
  h: number;
}

/**
 * DTO cho cấu hình validation của trường tùy chỉnh
 */
export class ValidationConfigDto {
  @Expose()
  @ApiProperty({
    description: 'Độ dài tối thiểu',
    example: 3,
    required: false,
  })
  minLength?: number;

  @Expose()
  @ApiProperty({
    description: 'Độ dài tối đa',
    example: 50,
    required: false,
  })
  maxLength?: number;

  @Expose()
  @ApiProperty({
    description: 'Pattern regex',
    example: '^[a-zA-Z0-9 ]*$',
    required: false,
  })
  pattern?: string;

  @Expose()
  @ApiProperty({
    description: 'Ngày tối thiểu',
    example: '1900-01-01',
    required: false,
  })
  minDate?: string;

  @Expose()
  @ApiProperty({
    description: 'Ngày tối đa',
    example: '2023-12-31',
    required: false,
  })
  maxDate?: string;
}

/**
 * DTO cho cấu hình của trường tùy chỉnh
 */
export class FieldConfigDto {
  @Expose()
  @ApiProperty({
    description: 'ID của cấu hình',
    example: 'text-input',
  })
  id: string;

  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Họ và tên',
  })
  label: string;

  @Expose()
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
    required: false,
  })
  type?: string;

  @Expose()
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  required: boolean;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình validation',
    type: ValidationConfigDto,
    required: false,
  })
  @Type(() => ValidationConfigDto)
  validation?: ValidationConfigDto;

  @Expose()
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: '',
    required: false,
  })
  defaultValue?: string;

  @Expose()
  @ApiProperty({
    description: 'Placeholder',
    example: 'Nhập họ và tên',
    required: false,
  })
  placeholder?: string;

  @Expose()
  @ApiProperty({
    description: 'Variant',
    example: 'outlined',
    required: false,
  })
  variant?: string;

  @Expose()
  @ApiProperty({
    description: 'Kích thước',
    example: 'small',
    required: false,
  })
  size?: string;

  @Expose()
  @ApiProperty({
    description: 'Danh sách tùy chọn',
    example: ['Việt Nam', 'Mỹ', 'Nhật Bản'],
    required: false,
  })
  options?: string[];
}

/**
 * DTO cho giá trị của trường tùy chỉnh
 */
export class FieldValueDto {
  @Expose()
  @ApiProperty({
    description: 'Giá trị của trường',
    example: 'Nguyễn Văn A',
  })
  value: string;
}

/**
 * DTO cho trường tùy chỉnh
 */
export class CustomFieldDto {
  @Expose()
  @ApiProperty({
    description: 'ID của trường',
    example: 1,
  })
  fieldId: number;

  @Expose()
  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
  })
  component: string;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình trường',
    type: FieldConfigDto,
  })
  @Type(() => FieldConfigDto)
  config: FieldConfigDto;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình grid',
    type: GridConfigDto,
  })
  @Type(() => GridConfigDto)
  grid: GridConfigDto;

  @Expose()
  @ApiProperty({
    description: 'Giá trị của trường',
    type: FieldValueDto,
  })
  @Type(() => FieldValueDto)
  value: FieldValueDto;
}

/**
 * DTO cho response khi lấy chi tiết nhóm trường tùy chỉnh
 */
export class CustomGroupFormResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của nhóm trường tùy chỉnh',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Thông tin cá nhân',
  })
  label: string;



  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createAt: number;

  @Expose()
  @ApiProperty({
    description: 'Danh sách trường trong nhóm',
    type: [CustomFieldDto],
  })
  @Type(() => CustomFieldDto)
  fields: CustomFieldDto[];
}
