import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString, MaxLength, IsNotEmpty, IsNumber } from 'class-validator';

/**
 * DTO cho thông tin khách hàng Facebook
 */
export class CustomerFacebookDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách hàng Facebook',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID phải là số' })
  id?: number;

  @Expose()
  @ApiProperty({
    description: 'ID riêng của khách hàng trên page',
    example: '12345678901234567890',
    maxLength: 20,
  })
  @IsNotEmpty({ message: 'Page scoped ID không được để trống' })
  @IsString({ message: 'Page scoped ID phải là chuỗi' })
  @MaxLength(20, { message: 'Page scoped ID không được vượt quá 20 ký tự' })
  pageScopedId: string;

  @Expose()
  @ApiProperty({
    description: 'ID trang Facebook',
    example: '123456789012345',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Page ID phải là chuỗi' })
  @MaxLength(255, { message: 'Page ID không được vượt quá 255 ký tự' })
  pageId?: string;

  @Expose()
  @ApiProperty({
    description: 'Tên người dùng',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Avatar phải là chuỗi' })
  @MaxLength(255, { message: 'Avatar không được vượt quá 255 ký tự' })
  avatar?: string;

  @Expose()
  @ApiProperty({
    description: 'Giới tính',
    example: 'male',
    maxLength: 45,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Giới tính phải là chuỗi' })
  @MaxLength(45, { message: 'Giới tính không được vượt quá 45 ký tự' })
  gender?: string;
}

/**
 * DTO response cho thông tin khách hàng Facebook
 */
export class CustomerFacebookResponseDto extends CustomerFacebookDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách hàng Facebook',
    example: 1,
  })
  declare id: number;

  @Expose()
  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: 123,
    required: false,
  })
  userConvertCustomerId?: number;
}
