import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString, IsNumber, MaxLength } from 'class-validator';

/**
 * DTO cho thông tin khách hàng Web
 */
export class CustomerWebDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách truy cập web',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID phải là số' })
  id?: number;

  @Expose()
  @ApiProperty({
    description: 'Tên miền truy cập',
    example: 'example.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Domain phải là chuỗi' })
  domain?: string;

  @Expose()
  @ApiProperty({
    description: 'Đường dẫn trang',
    example: '/products/123',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Path phải là chuỗi' })
  path?: string;

  @Expose()
  @ApiProperty({
    description: 'Thiết bị sử dụng',
    example: 'Desktop',
    maxLength: 200,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Device phải là chuỗi' })
  @MaxLength(200, { message: 'Device không được vượt quá 200 ký tự' })
  device?: string;

  @Expose()
  @ApiProperty({
    description: 'Hệ điều hành',
    example: 'Windows 10',
    maxLength: 200,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'OS phải là chuỗi' })
  @MaxLength(200, { message: 'OS không được vượt quá 200 ký tự' })
  os?: string;

  @Expose()
  @ApiProperty({
    description: 'Địa chỉ IP',
    example: '***********',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'IP phải là chuỗi' })
  @MaxLength(100, { message: 'IP không được vượt quá 100 ký tự' })
  ip?: string;

  @Expose()
  @ApiProperty({
    description: 'Trình duyệt',
    example: 'Chrome 91.0.4472.124',
    maxLength: 400,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Browser phải là chuỗi' })
  @MaxLength(400, { message: 'Browser không được vượt quá 400 ký tự' })
  browser?: string;

  @Expose()
  @ApiProperty({
    description: 'Thời điểm kết thúc phiên truy cập (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'End session unix phải là số' })
  endSessionUnix?: number;

  @Expose()
  @ApiProperty({
    description: 'Thời điểm bắt đầu phiên truy cập (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Start session unix phải là số' })
  startSessionUnix?: number;

  @Expose()
  @ApiProperty({
    description: 'Favicon của trang web',
    example: 'https://example.com/favicon.ico',
    maxLength: 500,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Favicon phải là chuỗi' })
  @MaxLength(500, { message: 'Favicon không được vượt quá 500 ký tự' })
  favicon?: string;
}

/**
 * DTO response cho thông tin khách hàng Web
 */
export class CustomerWebResponseDto extends CustomerWebDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách truy cập web',
    example: 1,
  })
  declare id: number;

  @Expose()
  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: 123,
    required: false,
  })
  userConvertCustomerId?: number;
}
