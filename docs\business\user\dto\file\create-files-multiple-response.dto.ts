import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho thông tin file đã tạo và URL tải lên
 */
export class FileCreationInfoDto {
  /**
   * ID của file đã tạo
   * @example 1
   */
  @ApiProperty({
    description: 'ID của file đã tạo',
    example: 1,
  })
  @Expose()
  id: number;

  /**
   * Tên file
   * @example "Tài liệu hướng dẫn.pdf"
   */
  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
  })
  @Expose()
  name: string;

  /**
   * URL ký sẵn để tải file lên
   * @example "https://storage.example.com/presigned-url?token=abc123"
   */
  @ApiProperty({
    description: 'URL ký sẵn để tải file lên',
    example: 'https://storage.example.com/presigned-url?token=abc123',
  })
  @Expose()
  uploadUrl: string;

  /**
   * Kh<PERSON>a lưu trữ của file trên hệ thống
   * @example "files/2023/05/document-1625097600000-abcdef123456.pdf"
   */
  @ApiProperty({
    description: 'Khóa lưu trữ của file trên hệ thống',
    example: 'files/2023/05/document-1625097600000-abcdef123456.pdf',
  })
  @Expose()
  storageKey: string;

  /**
   * URL xem file
   * @example "https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf"
   */
  @ApiProperty({
    description: 'URL xem file',
    example: 'https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf',
  })
  @Expose()
  viewUrl: string;

  /**
   * ID kho ảo chứa file
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho ảo chứa file',
    example: 1,
    nullable: true,
  })
  @Expose()
  warehouseId: number | null;

  /**
   * ID thư mục chứa file
   * @example 1
   */
  @ApiProperty({
    description: 'ID thư mục chứa file',
    example: 1,
  })
  @Expose()
  folderId: number;

  /**
   * Kích thước file (byte)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước file (byte)',
    example: 1024000,
  })
  @Expose()
  size: number;

  /**
   * Thời gian tạo (millis)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
  })
  @Expose()
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1625097600000,
  })
  @Expose()
  updatedAt: number;
}

/**
 * DTO cho kết quả tạo nhiều file
 */
export class CreateFilesMultipleResponseDto {
  /**
   * Danh sách thông tin các file đã tạo
   */
  @ApiProperty({
    description: 'Danh sách thông tin các file đã tạo',
    type: [FileCreationInfoDto],
  })
  @Expose()
  @Type(() => FileCreationInfoDto)
  files: FileCreationInfoDto[];

  /**
   * Số lượng file đã tạo thành công
   * @example 2
   */
  @ApiProperty({
    description: 'Số lượng file đã tạo thành công',
    example: 2,
  })
  @Expose()
  successCount: number;

  /**
   * Tổng số file được yêu cầu tạo
   * @example 2
   */
  @ApiProperty({
    description: 'Tổng số file được yêu cầu tạo',
    example: 2,
  })
  @Expose()
  totalCount: number;
}
