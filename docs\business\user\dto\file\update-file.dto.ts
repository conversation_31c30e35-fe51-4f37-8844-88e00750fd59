import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc cập nhật file
 */
export class UpdateFileDto {
  /**
   * Tên file
   * @example "Tài liệu hướng dẫn cập nhật.pdf"
   */
  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn cập nhật.pdf',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên file phải là chuỗi' })
  @MaxLength(255, { message: 'Tên file không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * ID thư mục chứa file
   * @example 2
   */
  @ApiProperty({
    description: 'ID thư mục chứa file',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID thư mục phải là số' })
  @Type(() => Number)
  folderId?: number;

  /**
   * Kích thước file (byte)
   * @example 2048000
   */
  @ApiProperty({
    description: 'Kích thước file (byte)',
    example: 2048000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Kích thước file phải là số' })
  @Type(() => Number)
  size?: number;

  /**
   * Có tạo URL để upload file mới hay không
   * @example true
   */
  @ApiProperty({
    description: 'Có tạo URL để upload file mới hay không',
    example: true,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường này phải là boolean' })
  @Type(() => Boolean)
  generateUploadUrl?: boolean;
}
