import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo thư mục mới
 */
export class CreateFolderDto {
  /**
   * Tên thư mục
   * @example "Tài liệu dự án"
   */
  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Tài liệu dự án',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên thư mục không được để trống' })
  @IsString({ message: 'Tên thư mục phải là chuỗi' })
  @MaxLength(255, { message: 'Tên thư mục không được vượt quá 255 ký tự' })
  name: string;

  /**
   * ID thư mục cha (null nếu là thư mục gốc)
   * @example 1
   */
  @ApiProperty({
    description: 'ID thư mục cha (null nếu là thư mục gốc)',
    example: 1,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID thư mục cha phải là số' })
  @Type(() => Number)
  parentId?: number | null;

  /**
   * ID kho ảo gốc
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho ảo gốc',
    example: 1,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho ảo gốc phải là số' })
  @Type(() => Number)
  root?: number | null;
}
