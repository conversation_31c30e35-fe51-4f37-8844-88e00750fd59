import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO để xóa nhiều thư mục
 */
export class DeleteFoldersDto {
  @ApiProperty({
    description: 'Danh sách ID của các thư mục cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'folderIds phải là một mảng' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON>i có ít nhất một folder ID' })
  @IsNumber({}, { each: true, message: 'Mỗi folder ID phải là số' })
  @IsNotEmpty({ each: true, message: 'Folder ID không được để trống' })
  @Type(() => Number)
  folderIds: number[];
}
