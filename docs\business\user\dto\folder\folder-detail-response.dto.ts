import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho thông tin người dùng sở hữu thư mục
 */
export class FolderUserDto {
  /**
   * ID của người dùng
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  id: number;

  /**
   * Tên đầy đủ của người dùng
   * @example "Nguyễn Văn A"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A',
  })
  fullName: string;

  /**
   * Email của người dùng
   * @example "<EMAIL>"
   */
  @Expose()
  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * <PERSON>ố điện thoại của người dùng
   * @example "0987654321"
   */
  @Expose()
  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0987654321',
  })
  phoneNumber: string;
}

/**
 * DTO cho thông tin thư mục cha
 */
export class ParentFolderDto {
  /**
   * ID của thư mục cha
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của thư mục cha',
    example: 1,
  })
  id: number;

  /**
   * Tên thư mục cha
   * @example "Thư mục cha"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên thư mục cha',
    example: 'Thư mục cha',
  })
  name: string;

  /**
   * Đường dẫn thư mục cha
   * @example "/Thư mục cha"
   */
  @Expose()
  @ApiProperty({
    description: 'Đường dẫn thư mục cha',
    example: '/Thư mục cha',
  })
  path: string;
}

/**
 * DTO cho thông tin kho ảo
 */
export class VirtualWarehouseDto {
  /**
   * ID của kho ảo
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của kho ảo',
    example: 1,
  })
  warehouseId: number;

  /**
   * Hệ thống liên kết
   * @example "SAP ERP"
   */
  @Expose()
  @ApiProperty({
    description: 'Hệ thống liên kết',
    example: 'SAP ERP',
    nullable: true,
  })
  associatedSystem: string | null;

  /**
   * Mục đích sử dụng
   * @example "Quản lý hàng hóa trực tuyến"
   */
  @Expose()
  @ApiProperty({
    description: 'Mục đích sử dụng',
    example: 'Quản lý hàng hóa trực tuyến',
    nullable: true,
  })
  purpose: string | null;
}

/**
 * DTO cho phản hồi thông tin chi tiết thư mục
 */
export class FolderDetailResponseDto {
  /**
   * ID của thư mục
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của thư mục',
    example: 1,
  })
  id: number;

  /**
   * Tên thư mục
   * @example "Tài liệu dự án"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Tài liệu dự án',
  })
  name: string;

  /**
   * Đường dẫn thư mục
   * @example "/Tài liệu/Dự án"
   */
  @Expose()
  @ApiProperty({
    description: 'Đường dẫn thư mục',
    example: '/Tài liệu/Dự án',
    nullable: true,
  })
  path: string | null;

  /**
   * Thời gian tạo (millis)
   * @example 1625097600000
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   * @example 1625097600000
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1625097600000,
  })
  updatedAt: number;

  /**
   * ID thư mục cha (null nếu là thư mục gốc)
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID thư mục cha (null nếu là thư mục gốc)',
    example: 1,
    nullable: true,
  })
  parentId: number | null;

  /**
   * Thông tin chi tiết về thư mục cha
   */
  @Expose()
  @Type(() => ParentFolderDto)
  @ApiProperty({
    description: 'Thông tin chi tiết về thư mục cha',
    type: ParentFolderDto,
    nullable: true,
  })
  parentFolder: ParentFolderDto | null;

  /**
   * ID người dùng sở hữu
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID người dùng sở hữu',
    example: 1,
  })
  userId: number;

  /**
   * Thông tin chi tiết về người dùng sở hữu
   */
  @Expose()
  @Type(() => FolderUserDto)
  @ApiProperty({
    description: 'Thông tin chi tiết về người dùng sở hữu',
    type: FolderUserDto,
    nullable: true,
  })
  user: FolderUserDto | null;

  /**
   * ID kho ảo gốc
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID kho ảo gốc',
    example: 1,
    nullable: true,
  })
  root: number | null;

  /**
   * Thông tin chi tiết về kho ảo
   */
  @Expose()
  @Type(() => VirtualWarehouseDto)
  @ApiProperty({
    description: 'Thông tin chi tiết về kho ảo',
    type: VirtualWarehouseDto,
    nullable: true,
  })
  virtualWarehouse: VirtualWarehouseDto | null;
}
