import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * DTO cho các tham số truy vấn danh sách thư mục
 */
export class QueryFolderDto extends QueryDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;

  /**
   * ID thư mục cha (null để lấy thư mục gốc)
   * @example 1
   */
  @ApiProperty({
    description: 'ID thư mục cha (null để lấy thư mục gốc)',
    example: 1,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID thư mục cha phải là số' })
  @Type(() => Number)
  parentId?: number | null;

  /**
   * Trường sắp xếp
   * @example "id"
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ['id', 'name', 'createdAt', 'updatedAt'],
    default: 'id',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'id';

  /**
   * Hướng sắp xếp
   * @example "ASC"
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.ASC;
}
