import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc cập nhật thư mục
 */
export class UpdateFolderDto {
  /**
   * Tên thư mục
   * @example "Tài liệu dự án (đã cập nhật)"
   */
  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Tài liệu dự án (đã cập nhật)',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên thư mục phải là chuỗi' })
  @MaxLength(255, { message: 'Tên thư mục không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * ID thư mục cha (null nếu là thư mục gốc)
   * @example 2
   */
  @ApiProperty({
    description: 'ID thư mục cha (null nếu là thư mục gốc)',
    example: 2,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID thư mục cha phải là số' })
  @Type(() => Number)
  parentId?: number | null;

  /**
   * ID kho ảo gốc
   * @example 2
   */
  @ApiProperty({
    description: 'ID kho ảo gốc',
    example: 2,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho ảo gốc phải là số' })
  @Type(() => Number)
  root?: number | null;
}
