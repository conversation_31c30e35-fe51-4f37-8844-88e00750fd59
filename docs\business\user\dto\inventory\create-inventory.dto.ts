import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, Min, ValidateIf, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo bản ghi tồn kho mới
 */
export class CreateInventoryDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;
  /**
   * ID sản phẩm
   * @example 1
   */
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID sản phẩm không được để trống' })
  @IsNumber({}, { message: 'ID sản phẩm phải là số' })
  @Type(() => Number)
  productId: number;

  /**
   * ID kho chứa sản phẩm
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho chứa sản phẩm',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID kho không được để trống' })
  @IsNumber({}, { message: 'ID kho phải là số' })
  @Type(() => Number)
  warehouseId: number;

  /**
   * Số lượng hiện tại trong kho (tự động tính toán)
   * @example 100
   */
  @ApiProperty({
    description: 'Số lượng hiện tại trong kho (tự động tính toán từ các số lượng thành phần)',
    example: 100,
    readOnly: true,
    required: false,
    writeOnly: false
  })
  @ValidateIf(() => false)
  currentQuantity?: number;

  /**
   * Tổng số lượng đã nhập vào kho (tự động tính toán)
   * @example 150
   */
  @ApiProperty({
    description: 'Tổng số lượng đã nhập vào kho (tự động tính toán, luôn lớn hơn hoặc bằng số lượng hiện tại)',
    example: 150,
    readOnly: true,
    required: false,
    writeOnly: false
  })
  @ValidateIf(() => false)
  totalQuantity?: number;

  /**
   * Số lượng sẵn sàng để bán hoặc sử dụng (đây cũng là tổng số lượng)
   * @example 90
   */
  @ApiProperty({
    description: 'Số lượng sẵn sàng để bán hoặc sử dụng (đây cũng là tổng số lượng)',
    example: 90,
    default: 0,
  })
  @IsNumber({}, { message: 'Số lượng sẵn sàng phải là số' })
  @Min(0, { message: 'Số lượng sẵn sàng không được nhỏ hơn 0' })
  @Type(() => Number)
  availableQuantity: number = 0;

  /**
   * Mã SKU (Stock Keeping Unit) của sản phẩm trong kho
   * @example "SKU-001"
   */
  @ApiProperty({
    description: 'Mã SKU (Stock Keeping Unit) của sản phẩm trong kho',
    example: 'SKU-001',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Mã SKU phải là chuỗi' })
  @MaxLength(100, { message: 'Mã SKU không được vượt quá 100 ký tự' })
  sku?: string;

  /**
   * Mã vạch (Barcode) của sản phẩm trong kho
   * @example "1234567890123"
   */
  @ApiProperty({
    description: 'Mã vạch (Barcode) của sản phẩm trong kho',
    example: '1234567890123',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Mã vạch phải là chuỗi' })
  @MaxLength(100, { message: 'Mã vạch không được vượt quá 100 ký tự' })
  barcode?: string;
}
