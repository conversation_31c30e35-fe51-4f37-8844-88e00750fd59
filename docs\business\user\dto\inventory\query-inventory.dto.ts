import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * DTO cho việc truy vấn danh sách tồn kho
 */
export class QueryInventoryDto extends QueryDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;
  /**
   * ID sản phẩm
   * @example 1
   */
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID sản phẩm phải là số' })
  @Type(() => Number)
  productId?: number;

  /**
   * ID kho
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho phải là số' })
  @Type(() => Number)
  warehouseId?: number;

  /**
   * Trường sắp xếp
   * @example "id"
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    example: 'id',
    required: false,
    enum: ['id', 'productId', 'warehouseId', 'currentQuantity', 'availableQuantity', 'lastUpdated'],
  })
  @IsOptional()
  @IsString({ message: 'Trường sắp xếp phải là chuỗi' })
  sortBy?: string = 'id';

  /**
   * Hướng sắp xếp
   * @example "ASC"
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    example: 'ASC',
    required: false,
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsEnum(SortDirection, { message: 'Hướng sắp xếp phải là ASC hoặc DESC' })
  sortDirection?: SortDirection = SortDirection.ASC;
}
