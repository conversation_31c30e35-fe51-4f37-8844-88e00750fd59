import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, ValidateIf } from 'class-validator';

/**
 * DTO cho trường metadata của khách hàng chuyển đổi
 *
 * **L<PERSON>u ý quan trọng:**
 * - Chỉ cần gửi những custom fields bạn muốn điền
 * - Không bắt buộc phải điền tất cả các trường required
 * - Validation chỉ áp dụng cho những trường được gửi lên
 * - Custom fields có thể được tạo bởi user hoặc employee
 */
export class MetadataFieldDto {
  /**
   * Config ID từ bảng custom_fields
   *
   * **Các configId có sẵn trong hệ thống:**
   * - day_of_birth: Ng<PERSON>y sinh (text, required)
   * - haianh: Thông tin bổ sung (text, optional)
   * - product_color: <PERSON><PERSON><PERSON> sắ<PERSON> sản phẩm (text với pattern ^[A-Z0-9]{10,15}$)
   * - model-number: Mã model (text với pattern ^[A-Z0-9]+$, required)
   * - storage-capacity: Dung lượng (select: 128GB, 256GB, 512GB, 1TB, required)
   * - color-option: Màu sắc (radio: Titan Tự Nhiên, Titan Đen, Titan Trắng, Titan Xanh, required)
   * - warranty-period: Thời gian bảo hành (select: 12 tháng, 24 tháng, 36 tháng, required)
   *
   * @example "day_of_birth"
   */
  @ApiProperty({
    description: 'Config ID từ bảng custom_fields. Xem danh sách configId có sẵn trong documentation.',
    example: 'day_of_birth',
    examples: {
      simple_text: {
        value: 'day_of_birth',
        description: 'Ngày sinh (text field)'
      },
      optional_text: {
        value: 'haianh',
        description: 'Thông tin bổ sung (optional text field)'
      },
      pattern_text: {
        value: 'product_color',
        description: 'Màu sắc sản phẩm (text với pattern validation)'
      },
      select_field: {
        value: 'storage-capacity',
        description: 'Dung lượng (select field với options)'
      }
    }
  })
  @IsNotEmpty({ message: 'Config ID không được để trống' })
  @IsString({ message: 'Config ID phải là chuỗi' })
  configId: string;

  /**
   * Giá trị của trường tùy chỉnh
   *
   * **Lưu ý về validation:**
   * - Giá trị phải tuân theo validation rules của custom field
   * - Text fields có thể có pattern, minLength, maxLength
   * - Select fields phải chọn từ danh sách options có sẵn
   * - Number fields phải là số hợp lệ
   *
   * @example "28/11/2003"
   */
  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh. Phải tuân theo validation rules của custom field.',
    example: '28/11/2003',
    examples: {
      date_text: {
        value: '28/11/2003',
        description: 'Ngày sinh (text format)'
      },
      simple_text: {
        value: 'Thông tin bổ sung',
        description: 'Text thông thường'
      },
      pattern_text: {
        value: 'RED12345678',
        description: 'Text với pattern ^[A-Z0-9]{10,15}$'
      },
      select_value: {
        value: '256GB',
        description: 'Giá trị từ select options'
      },
      radio_value: {
        value: 'Titan Đen',
        description: 'Giá trị từ radio options'
      }
    },
    oneOf: [
      { type: 'string' },
      { type: 'number' },
      { type: 'boolean' },
      { type: 'array', items: { type: 'string' } },
      { type: 'array', items: { type: 'number' } }
    ]
  })
  @IsOptional()
  value?: string | number | boolean | string[] | number[];
}

/**
 * DTO cho response metadata field với thông tin đầy đủ
 */
export class MetadataFieldResponseDto extends MetadataFieldDto {
  /**
   * Nhãn hiển thị từ custom field
   * @example "date_of_birth"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị từ custom field',
    example: 'date_of_birth',
  })
  @IsOptional()
  @IsString({ message: 'Label phải là chuỗi' })
  label?: string;

  /**
   * Loại trường từ custom field
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường từ custom field',
    example: 'text',
    enum: ['text', 'number', 'email', 'phone', 'select', 'multiselect', 'date', 'datetime', 'textarea', 'checkbox', 'radio']
  })
  @IsOptional()
  @IsString({ message: 'Type phải là chuỗi' })
  type?: string;

  /**
   * Có bắt buộc hay không
   * @example true
   */
  @ApiProperty({
    description: 'Có bắt buộc hay không',
    example: true,
  })
  @IsOptional()
  required?: boolean;
}
