import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho các tham số truy vấn danh sách nhóm trường tùy chỉnh
 */
export class QueryCustomGroupFormDto extends QueryDto {

  /**
   * ID sản phẩm để lọc
   * @example 201
   */
  @ApiProperty({
    description: 'ID sản phẩm để lọc',
    example: 201,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  productId?: number;

  /**
   * ID người dùng để lọc
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng để lọc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;
}
