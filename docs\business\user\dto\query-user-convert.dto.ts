import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp chuyển đổi khách hàng
 */
export enum UserConvertSortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  CONVERSION_TYPE = 'conversionType',
  SOURCE = 'source',
}

/**
 * DTO cho các tham số truy vấn danh sách chuyển đổi khách hàng
 */
export class QueryUserConvertDto {
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên một trang',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: 'ID khách hàng được chuyển đổi',
    example: 101,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  convertCustomerId?: number;

  @ApiProperty({
    description: 'Loại chuyển đổi',
    example: 'online',
    required: false,
  })
  @IsOptional()
  @IsString()
  conversionType?: string;

  @ApiProperty({
    description: 'Nguồn gốc chuyển đổi',
    example: 'website',
    required: false,
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: UserConvertSortField,
    default: UserConvertSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserConvertSortField)
  sortBy?: UserConvertSortField = UserConvertSortField.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
