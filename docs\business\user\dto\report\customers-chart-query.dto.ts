import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsDateString, Validate } from 'class-validator';
import { DateRangeValidator } from './report-overview-query.dto';
import { ChartGroupByEnum } from './sales-chart-query.dto';

/**
 * Enum cho phân khúc khách hàng
 */
export enum CustomerSegmentEnum {
  NEW = 'new',
  RETURNING = 'returning',
  VIP = 'vip'
}

/**
 * DTO cho query parameters của API biểu đồ khách hàng
 */
export class CustomersChartQueryDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày bắt đầu phải có định dạng YYYY-MM-DD' })
  startDate?: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày kết thúc phải có định dạng YYYY-MM-DD' })
  @Validate(DateRangeValidator)
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Cách nhóm dữ liệu theo thời gian',
    enum: ChartGroupByEnum,
    example: ChartGroupByEnum.MONTH,
    default: ChartGroupByEnum.MONTH,
  })
  @IsOptional()
  @IsEnum(ChartGroupByEnum, { message: 'Cách nhóm dữ liệu không hợp lệ' })
  groupBy?: ChartGroupByEnum = ChartGroupByEnum.MONTH;

  @ApiPropertyOptional({
    description: 'Lọc theo phân khúc khách hàng',
    enum: CustomerSegmentEnum,
    example: CustomerSegmentEnum.NEW,
  })
  @IsOptional()
  @IsEnum(CustomerSegmentEnum, { message: 'Phân khúc khách hàng không hợp lệ' })
  segment?: CustomerSegmentEnum;
}
