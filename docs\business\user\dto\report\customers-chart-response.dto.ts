import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho một điểm dữ liệu trong biểu đồ khách hàng
 */
export class CustomersChartDataPointDto {
  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kỳ (T1, T2, ... hoặc 01/2024, 02/2024, ...)',
    example: 'T1 2024',
  })
  period: string;

  @Expose()
  @ApiProperty({
    description: 'Ng<PERSON>y đại diện cho kỳ (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  date: string;

  @Expose()
  @ApiProperty({
    description: 'Số khách hàng mới',
    example: 15,
  })
  newCustomers: number;

  @Expose()
  @ApiProperty({
    description: 'Số khách hàng quay lại',
    example: 25,
  })
  returningCustomers: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số khách hàng',
    example: 40,
  })
  totalCustomers: number;

  @Expose()
  @ApiProperty({
    description: 'Số khách hàng VIP',
    example: 5,
  })
  vipCustomers: number;
}

/**
 * DTO cho tóm tắt dữ liệu khách hàng
 */
export class CustomersChartSummaryDto {
  @Expose()
  @ApiProperty({
    description: 'Tổng khách hàng mới',
    example: 120,
  })
  totalNewCustomers: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng khách hàng quay lại',
    example: 200,
  })
  totalReturningCustomers: number;

  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ giữ chân khách hàng (%)',
    example: 62.5,
  })
  customerRetentionRate: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian sống trung bình của khách hàng (ngày)',
    example: 180,
  })
  averageCustomerLifetime: number;
}

/**
 * DTO cho response API biểu đồ khách hàng
 */
export class CustomersChartResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Dữ liệu biểu đồ theo thời gian',
    type: [CustomersChartDataPointDto],
  })
  data: CustomersChartDataPointDto[];

  @Expose()
  @ApiProperty({
    description: 'Tóm tắt dữ liệu khách hàng',
    type: CustomersChartSummaryDto,
  })
  summary: CustomersChartSummaryDto;
}
