import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho một điểm dữ liệu trong biểu đồ đơn hàng
 */
export class OrdersChartDataPointDto {
  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kỳ (T1, T2, ... hoặc 01/2024, 02/2024, ...)',
    example: 'T1 2024',
  })
  period: string;

  @Expose()
  @ApiProperty({
    description: 'Ngày đại diện cho kỳ (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  date: string;

  @Expose()
  @ApiProperty({
    description: 'Tổng số đơn hàng',
    example: 50,
  })
  totalOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Số đơn hàng đang chờ xử lý',
    example: 5,
  })
  pendingOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Số đơn hàng đã xác nhận',
    example: 10,
  })
  confirmedOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Số đơn hàng đang vận chuyển',
    example: 15,
  })
  shippingOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Số đơn hàng đã giao',
    example: 18,
  })
  deliveredOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Số đơn hàng đã hủy',
    example: 2,
  })
  cancelledOrders: number;
}

/**
 * DTO cho phân tích trạng thái đơn hàng
 */
export class OrderStatusBreakdownDto {
  @Expose()
  @ApiProperty({
    description: 'Tổng đơn hàng đang chờ xử lý',
    example: 25,
  })
  pending: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng đơn hàng đã xác nhận',
    example: 50,
  })
  confirmed: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng đơn hàng đang vận chuyển',
    example: 75,
  })
  shipping: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng đơn hàng đã giao',
    example: 90,
  })
  delivered: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng đơn hàng đã hủy',
    example: 10,
  })
  cancelled: number;
}

/**
 * DTO cho response API biểu đồ đơn hàng
 */
export class OrdersChartResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Dữ liệu biểu đồ theo thời gian',
    type: [OrdersChartDataPointDto],
  })
  data: OrdersChartDataPointDto[];

  @Expose()
  @ApiProperty({
    description: 'Phân tích trạng thái đơn hàng',
    type: OrderStatusBreakdownDto,
  })
  statusBreakdown: OrderStatusBreakdownDto;
}
