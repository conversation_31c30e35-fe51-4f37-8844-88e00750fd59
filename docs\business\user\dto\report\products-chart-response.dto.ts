import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho một sản phẩm trong biểu đồ hiệu suất
 */
export class ProductsChartDataItemDto {
  @Expose()
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1,
  })
  productId: number;

  @Expose()
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: '<PERSON>o thun nam cao cấp',
  })
  productName: string;

  @Expose()
  @ApiProperty({
    description: 'Tên danh mục',
    example: 'Thời trang nam',
  })
  categoryName: string;

  @Expose()
  @ApiProperty({
    description: '<PERSON>anh thu từ sản phẩm',
    example: 5000000,
  })
  revenue: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đã bán',
    example: 50,
  })
  quantitySold: number;

  @Expose()
  @ApiProperty({
    description: 'Số đơn hàng chứa sản phẩm',
    example: 25,
  })
  ordersCount: number;

  @Expose()
  @ApiProperty({
    description: 'Giá trung bình',
    example: 100000,
  })
  averagePrice: number;

  @Expose()
  @ApiProperty({
    description: 'URL hình ảnh sản phẩm',
    example: 'https://example.com/product-image.jpg',
    nullable: true,
  })
  imageUrl?: string;
}

/**
 * DTO cho sản phẩm bán chạy nhất
 */
export class BestSellingProductDto {
  @Expose()
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam cao cấp',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Doanh thu',
    example: 5000000,
  })
  revenue: number;
}

/**
 * DTO cho tóm tắt dữ liệu sản phẩm
 */
export class ProductsChartSummaryDto {
  @Expose()
  @ApiProperty({
    description: 'Tổng số sản phẩm',
    example: 150,
  })
  totalProducts: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng doanh thu',
    example: 50000000,
  })
  totalRevenue: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số lượng đã bán',
    example: 500,
  })
  totalQuantitySold: number;

  @Expose()
  @ApiProperty({
    description: 'Sản phẩm bán chạy nhất',
    type: BestSellingProductDto,
  })
  bestSellingProduct: BestSellingProductDto;
}

/**
 * DTO cho response API biểu đồ sản phẩm
 */
export class ProductsChartResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Dữ liệu hiệu suất sản phẩm',
    type: [ProductsChartDataItemDto],
  })
  data: ProductsChartDataItemDto[];

  @Expose()
  @ApiProperty({
    description: 'Tóm tắt dữ liệu',
    type: ProductsChartSummaryDto,
  })
  summary: ProductsChartSummaryDto;
}
