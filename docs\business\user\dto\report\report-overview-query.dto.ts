import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsDateString, Validate } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho các khoảng thời gian báo cáo
 */
export enum ReportPeriodEnum {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year'
}

/**
 * Custom validator để kiểm tra endDate >= startDate
 */
export class DateRangeValidator {
  validate(endDate: string, args: any): boolean {
    const startDate = args.object.startDate;
    if (!startDate || !endDate) return true;
    return new Date(endDate) >= new Date(startDate);
  }

  defaultMessage(): string {
    return 'Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu';
  }
}

/**
 * DTO cho query parameters của API tổng quan báo cáo
 */
export class ReportOverviewQueryDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON>y bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày bắt đầu phải có định dạng YYYY-MM-DD' })
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Ngày kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày kết thúc phải có định dạng YYYY-MM-DD' })
  @Validate(DateRangeValidator)
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Khoảng thời gian báo cáo',
    enum: ReportPeriodEnum,
    example: ReportPeriodEnum.MONTH,
    default: ReportPeriodEnum.MONTH,
  })
  @IsOptional()
  @IsEnum(ReportPeriodEnum, { message: 'Khoảng thời gian không hợp lệ' })
  period?: ReportPeriodEnum = ReportPeriodEnum.MONTH;
}
