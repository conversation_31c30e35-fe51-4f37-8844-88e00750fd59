import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho một điểm dữ liệu trong biểu đồ doanh thu
 */
export class SalesChartDataPointDto {
  @Expose()
  @ApiProperty({
    description: 'T<PERSON><PERSON> kỳ (T1, T2, ... hoặc 01/2024, 02/2024, ...)',
    example: 'T1 2024',
  })
  period: string;

  @Expose()
  @ApiProperty({
    description: 'Ngày đại diện cho kỳ (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  date: string;

  @Expose()
  @ApiProperty({
    description: 'Doanh thu trong kỳ',
    example: 5000000,
  })
  revenue: number;

  @Expose()
  @ApiProperty({
    description: 'Số đơn hàng trong kỳ',
    example: 25,
  })
  orders: number;

  @Expose()
  @ApiProperty({
    description: '<PERSON>i<PERSON> trị đơn hàng trung bình',
    example: 200000,
  })
  averageOrderValue: number;
}

/**
 * DTO cho tóm tắt dữ liệu biểu đồ doanh thu
 */
export class SalesChartSummaryDto {
  @Expose()
  @ApiProperty({
    description: 'Tổng doanh thu',
    example: 20000000,
  })
  totalRevenue: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng đơn hàng',
    example: 100,
  })
  totalOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Giá trị đơn hàng trung bình',
    example: 200000,
  })
  averageOrderValue: number;

  @Expose()
  @ApiProperty({
    description: 'Doanh thu cao nhất trong một kỳ',
    example: 8000000,
  })
  highestRevenue: number;

  @Expose()
  @ApiProperty({
    description: 'Doanh thu thấp nhất trong một kỳ',
    example: 2000000,
  })
  lowestRevenue: number;
}

/**
 * DTO cho response API biểu đồ doanh thu
 */
export class SalesChartResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Dữ liệu biểu đồ theo thời gian',
    type: [SalesChartDataPointDto],
  })
  data: SalesChartDataPointDto[];

  @Expose()
  @ApiProperty({
    description: 'Tóm tắt dữ liệu',
    type: SalesChartSummaryDto,
  })
  summary: SalesChartSummaryDto;
}
