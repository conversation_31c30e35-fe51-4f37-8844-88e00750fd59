import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsDateString, Validate, <PERSON>, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { DateRangeValidator } from './report-overview-query.dto';

/**
 * DTO cho query parameters của API sản phẩm bán chạy
 */
export class TopSellingProductsQueryDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ng<PERSON>y bắt đầu phải có định dạng YYYY-MM-DD' })
  startDate?: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: '<PERSON><PERSON><PERSON> kết thúc phải có định dạng YYYY-MM-DD' })
  @Validate(DateRangeValidator)
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Số lượng sản phẩm tối đa trả về',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Limit phải là số' })
  @Min(1, { message: 'Limit phải lớn hơn 0' })
  @Max(100, { message: 'Limit không được vượt quá 100' })
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'ID danh mục sản phẩm',
    example: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID danh mục phải là số' })
  @Min(1, { message: 'ID danh mục phải lớn hơn 0' })
  @Type(() => Number)
  categoryId?: number;
}
