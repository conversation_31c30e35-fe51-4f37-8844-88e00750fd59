import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNumber,
  Min,
  IsArray,
  ValidateNested
} from 'class-validator';
import { ServicePackageDto } from './service-package.dto';

/**
 * DTO cho thông tin nâng cao của dịch vụ
 */
export class ServiceProductDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 25,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  purchaseCount: number;

  @ApiProperty({
    description: 'Danh sách gói dịch vụ',
    type: [ServicePackageDto],
    example: [
      {
        name: '<PERSON><PERSON><PERSON> tư vấn cơ bản',
        price: 1000000,
        startTime: 1704067200000,
        endTime: 1704153600000,
        timezone: 'Asia/Ho_Chi_Minh',
        description: 'G<PERSON>i tư vấn cơ bản bao gồm 3 buổi tư vấn online',
        quantity: 50,
        minQuantityPerPurchase: 1,
        maxQuantityPerPurchase: 3,
        status: 'PENDING',
        imagesMediaTypes: ['image/jpeg']
      }
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ServicePackageDto)
  servicePackages: ServicePackageDto[];
}
