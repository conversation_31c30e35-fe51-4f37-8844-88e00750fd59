import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsArray,
  MaxLength,
  IsEnum
} from 'class-validator';
import { EntityStatusEnum } from '@modules/business/enums';

/**
 * DTO cho loại vé sự kiện
 */
export class TicketTypeDto {
  @ApiProperty({
    description: 'Tên loại vé',
    example: 'Vé VIP',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Giá vé',
    example: 500000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  price: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu bán vé (timestamp)',
    example: 1704067200000,
  })
  @IsNumber()
  @Type(() => Number)
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc bán vé (timestamp)',
    example: 1704153600000,
  })
  @IsNumber()
  @Type(() => Number)
  endTime: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiProperty({
    description: 'Mô tả loại vé',
    example: 'Vé VIP bao gồm suất ăn và quà tặng',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Số lượng vé có sẵn',
    example: 100,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  quantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu trên 1 lần mua',
    example: 1,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Giới hạn số lượng tối đa trên 1 lần mua',
    example: 5,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  maxQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Trạng thái loại vé',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.PENDING,
  })
  @IsEnum(EntityStatusEnum)
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Danh sách loại media cho hình ảnh vé',
    type: [String],
    example: ['image/jpeg', 'image/png'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes?: string[];
}
