import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho thông tin tracking từ GHN
 */
export class GHNTrackingStatusDto {
  @ApiProperty({
    description: 'ID shop',
    example: 196768
  })
  @Expose()
  shop_id: number;

  @ApiProperty({
    description: 'ID client',
    example: 2509992
  })
  @Expose()
  client_id: number;

  @ApiProperty({
    description: 'Tên người gửi',
    example: 'Cửa hàng ABC'
  })
  @Expose()
  from_name: string;

  @ApiProperty({
    description: 'Số điện thoại người gửi',
    example: '0789282471'
  })
  @Expose()
  from_phone: string;

  @ApiProperty({
    description: 'Địa chỉ người gửi',
    example: 'Số 123, Đường ABC, Phường 1, Quận 1, TP.HCM'
  })
  @Expose()
  from_address: string;

  @ApiProperty({
    description: 'Tên người nhận',
    example: 'NGUYEN NGOC HAI ANH'
  })
  @Expose()
  to_name: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0793355880'
  })
  @Expose()
  to_phone: string;

  @ApiProperty({
    description: 'Địa chỉ người nhận',
    example: 'Mộ cổ - Nguyên Xá - Minh Khai - Bắc Từ Liêm'
  })
  @Expose()
  to_address: string;

  @ApiProperty({
    description: 'Trọng lượng (gram)',
    example: 1200
  })
  @Expose()
  weight: number;

  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 20
  })
  @Expose()
  length: number;

  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 15
  })
  @Expose()
  width: number;

  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 10
  })
  @Expose()
  height: number;

  @ApiProperty({
    description: 'Mã đơn hàng của client',
    example: 'ORDER_20_1749016810896'
  })
  @Expose()
  client_order_code: string;

  @ApiProperty({
    description: 'Mã đơn hàng GHN',
    example: 'LBK6X3'
  })
  @Expose()
  order_code: string;

  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    example: 'ready_to_pick',
    enum: ['ready_to_pick', 'picking', 'picked', 'storing', 'transporting', 'sorting', 'delivering', 'delivered', 'delivery_fail', 'waiting_to_return', 'return', 'cancel']
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'Nội dung đơn hàng',
    example: 'Túi xách nữ da thật [60] [2 cái]'
  })
  @Expose()
  content: string;

  @ApiProperty({
    description: 'Ghi chú đơn hàng',
    example: 'Giao hàng trong giờ hành chính'
  })
  @Expose()
  note: string;

  @ApiProperty({
    description: 'Thời gian lấy hàng',
    example: '2025-06-04T06:00:15.42Z'
  })
  @Expose()
  pickup_time: string;

  @ApiProperty({
    description: 'Thời gian dự kiến giao hàng',
    example: '2025-06-05T16:59:59Z'
  })
  @Expose()
  leadtime: string;

  @ApiProperty({
    description: 'Thời gian tạo đơn',
    example: '2025-06-04T06:00:14.139Z'
  })
  @Expose()
  created_date: string;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2025-06-04T06:00:16.789Z'
  })
  @Expose()
  updated_date: string;

  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Túi xách nữ da thật' },
        code: { type: 'string', example: '60' },
        quantity: { type: 'number', example: 2 },
        weight: { type: 'number', example: 600 },
        status: { type: 'string', example: 'ready_to_pick' },
        item_order_code: { type: 'string', example: 'LBK6X3_1' }
      }
    }
  })
  @Expose()
  items: any[];
}

/**
 * DTO cho response tracking đơn hàng
 */
export class TrackingResponseDto {
  @ApiProperty({
    description: 'ID đơn hàng',
    example: '20'
  })
  @Expose()
  orderId: string;

  @ApiProperty({
    description: 'Mã vận đơn',
    example: 'LBK6X3'
  })
  @Expose()
  trackingNumber: string;

  @ApiProperty({
    description: 'Đơn vị vận chuyển',
    example: 'GHN',
    enum: ['GHN', 'GHTK']
  })
  @Expose()
  carrier: string;

  @ApiProperty({
    description: 'Thông tin trạng thái chi tiết từ đơn vị vận chuyển',
    type: GHNTrackingStatusDto
  })
  @Expose()
  status: GHNTrackingStatusDto;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1749016943203
  })
  @Expose()
  lastUpdated: number;
}

/**
 * DTO cho API response tracking
 */
export class TrackingApiResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true
  })
  @Expose()
  success: boolean;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Lấy thông tin tracking thành công'
  })
  @Expose()
  message: string;

  @ApiProperty({
    description: 'Dữ liệu tracking',
    type: TrackingResponseDto
  })
  @Expose()
  data: TrackingResponseDto;
}
