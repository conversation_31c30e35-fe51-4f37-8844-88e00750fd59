import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUrl, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật liên kết mạng xã hội của khách hàng chuyển đổi
 */
export class UpdateCustomerSocialLinksDto {
  /**
   * Link Facebook của khách hàng
   * @example "https://facebook.com/user123"
   */
  @ApiProperty({
    description: 'Link Facebook của khách hàng',
    example: 'https://facebook.com/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Facebook phải là chuỗi' })
  @IsUrl({}, { message: 'Link Facebook phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Facebook không được vượt quá 500 ký tự' })
  facebookLink?: string;

  /**
   * Link Twitter của khách hàng
   * @example "https://twitter.com/user123"
   */
  @ApiProperty({
    description: 'Link Twitter của khách hàng',
    example: 'https://twitter.com/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Twitter phải là chuỗi' })
  @IsUrl({}, { message: 'Link Twitter phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Twitter không được vượt quá 500 ký tự' })
  twitterLink?: string;

  /**
   * Link LinkedIn của khách hàng
   * @example "https://linkedin.com/in/user123"
   */
  @ApiProperty({
    description: 'Link LinkedIn của khách hàng',
    example: 'https://linkedin.com/in/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link LinkedIn phải là chuỗi' })
  @IsUrl({}, { message: 'Link LinkedIn phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link LinkedIn không được vượt quá 500 ký tự' })
  linkedinLink?: string;

  /**
   * Link Zalo của khách hàng
   * @example "https://zalo.me/user123"
   */
  @ApiProperty({
    description: 'Link Zalo của khách hàng',
    example: 'https://zalo.me/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Zalo phải là chuỗi' })
  @IsUrl({}, { message: 'Link Zalo phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Zalo không được vượt quá 500 ký tự' })
  zaloLink?: string;

  /**
   * Link Website của khách hàng
   * @example "https://website.com"
   */
  @ApiProperty({
    description: 'Link Website của khách hàng',
    example: 'https://website.com',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Website phải là chuỗi' })
  @IsUrl({}, { message: 'Link Website phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Website không được vượt quá 500 ký tự' })
  websiteLink?: string;
}

/**
 * DTO response cho liên kết mạng xã hội của khách hàng chuyển đổi
 */
export class CustomerSocialLinksResponseDto {
  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Link Facebook của khách hàng',
    example: 'https://facebook.com/user123',
    nullable: true,
  })
  facebookLink: string | null;

  @ApiProperty({
    description: 'Link Twitter của khách hàng',
    example: 'https://twitter.com/user123',
    nullable: true,
  })
  twitterLink: string | null;

  @ApiProperty({
    description: 'Link LinkedIn của khách hàng',
    example: 'https://linkedin.com/in/user123',
    nullable: true,
  })
  linkedinLink: string | null;

  @ApiProperty({
    description: 'Link Zalo của khách hàng',
    example: 'https://zalo.me/user123',
    nullable: true,
  })
  zaloLink: string | null;

  @ApiProperty({
    description: 'Link Website của khách hàng',
    example: 'https://website.com',
    nullable: true,
  })
  websiteLink: string | null;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
