import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsIn,
  IsNumber,
  Min,
} from 'class-validator';

/**
 * DTO cho tạo địa chỉ mới
 */
export class CreateUserAddressDto {
  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn A',
  })
  @IsNotEmpty({ message: 'Tên người nhận không được để trống' })
  @IsString({ message: 'Tên người nhận phải là chuỗi' })
  @MaxLength(255, { message: 'Tên người nhận không được vượt quá 255 ký tự' })
  recipientName: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0912345678',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON> điện thoại không được để trống' })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @MaxLength(20, { message: 'Số điện thoại không được vượt quá 20 ký tự' })
  recipientPhone: string;

  @ApiProperty({
    description: 'Địa chỉ chi tiết',
    example: '123 Đường ABC, Phường 1',
  })
  @IsNotEmpty({ message: 'Địa chỉ không được để trống' })
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ không được vượt quá 500 ký tự' })
  address: string;

  @ApiProperty({
    description: 'Tỉnh/Thành phố',
    example: 'TP. Hồ Chí Minh',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tỉnh/Thành phố phải là chuỗi' })
  @MaxLength(100, { message: 'Tỉnh/Thành phố không được vượt quá 100 ký tự' })
  province?: string;

  @ApiProperty({
    description: 'Quận/Huyện',
    example: 'Quận 1',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Quận/Huyện phải là chuỗi' })
  @MaxLength(100, { message: 'Quận/Huyện không được vượt quá 100 ký tự' })
  district?: string;

  @ApiProperty({
    description: 'Phường/Xã',
    example: 'Phường Bến Nghé',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Phường/Xã phải là chuỗi' })
  @MaxLength(100, { message: 'Phường/Xã không được vượt quá 100 ký tự' })
  ward?: string;

  @ApiProperty({
    description: 'Thôn/Ấp/Xóm/Tổ (dùng cho GHTK)',
    example: 'Không rõ',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Thôn/Ấp/Xóm/Tổ phải là chuỗi' })
  @MaxLength(100, { message: 'Thôn/Ấp/Xóm/Tổ không được vượt quá 100 ký tự' })
  hamlet?: string;

  @ApiProperty({
    description: 'Mã bưu điện',
    example: '70000',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mã bưu điện phải là chuỗi' })
  @MaxLength(10, { message: 'Mã bưu điện không được vượt quá 10 ký tự' })
  postalCode?: string;

  @ApiProperty({
    description: 'Có phải địa chỉ mặc định không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường isDefault phải là boolean' })
  isDefault?: boolean;

  @ApiProperty({
    description: 'Loại địa chỉ',
    enum: ['home', 'office', 'other'],
    example: 'home',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Loại địa chỉ phải là chuỗi' })
  @IsIn(['home', 'office', 'other'], { message: 'Loại địa chỉ phải là home, office hoặc other' })
  addressType?: string;

  @ApiProperty({
    description: 'Ghi chú địa chỉ',
    example: 'Gần chợ Bến Thành',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú phải là chuỗi' })
  note?: string;
}

/**
 * DTO cho cập nhật địa chỉ
 */
export class UpdateUserAddressDto {
  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên người nhận phải là chuỗi' })
  @MaxLength(255, { message: 'Tên người nhận không được vượt quá 255 ký tự' })
  recipientName?: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0912345678',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @MaxLength(20, { message: 'Số điện thoại không được vượt quá 20 ký tự' })
  recipientPhone?: string;

  @ApiProperty({
    description: 'Địa chỉ chi tiết',
    example: '123 Đường ABC, Phường 1',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ không được vượt quá 500 ký tự' })
  address?: string;

  @ApiProperty({
    description: 'Tỉnh/Thành phố',
    example: 'TP. Hồ Chí Minh',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tỉnh/Thành phố phải là chuỗi' })
  @MaxLength(100, { message: 'Tỉnh/Thành phố không được vượt quá 100 ký tự' })
  province?: string;

  @ApiProperty({
    description: 'Quận/Huyện',
    example: 'Quận 1',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Quận/Huyện phải là chuỗi' })
  @MaxLength(100, { message: 'Quận/Huyện không được vượt quá 100 ký tự' })
  district?: string;

  @ApiProperty({
    description: 'Phường/Xã',
    example: 'Phường Bến Nghé',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Phường/Xã phải là chuỗi' })
  @MaxLength(100, { message: 'Phường/Xã không được vượt quá 100 ký tự' })
  ward?: string;

  @ApiProperty({
    description: 'Thôn/Ấp/Xóm/Tổ (dùng cho GHTK)',
    example: 'Không rõ',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Thôn/Ấp/Xóm/Tổ phải là chuỗi' })
  @MaxLength(100, { message: 'Thôn/Ấp/Xóm/Tổ không được vượt quá 100 ký tự' })
  hamlet?: string;

  @ApiProperty({
    description: 'Mã bưu điện',
    example: '70000',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mã bưu điện phải là chuỗi' })
  @MaxLength(10, { message: 'Mã bưu điện không được vượt quá 10 ký tự' })
  postalCode?: string;

  @ApiProperty({
    description: 'Có phải địa chỉ mặc định không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường isDefault phải là boolean' })
  isDefault?: boolean;

  @ApiProperty({
    description: 'Loại địa chỉ',
    enum: ['home', 'office', 'other'],
    example: 'home',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Loại địa chỉ phải là chuỗi' })
  @IsIn(['home', 'office', 'other'], { message: 'Loại địa chỉ phải là home, office hoặc other' })
  addressType?: string;

  @ApiProperty({
    description: 'Ghi chú địa chỉ',
    example: 'Gần chợ Bến Thành',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú phải là chuỗi' })
  note?: string;
}

/**
 * DTO cho response địa chỉ
 */
export class UserAddressResponseDto {
  @ApiProperty({
    description: 'ID địa chỉ',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn A',
  })
  recipientName: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0912345678',
  })
  recipientPhone: string;

  @ApiProperty({
    description: 'Địa chỉ chi tiết',
    example: '123 Đường ABC, Phường 1',
  })
  address: string;

  @ApiProperty({
    description: 'Tỉnh/Thành phố',
    example: 'TP. Hồ Chí Minh',
    required: false,
  })
  province?: string;

  @ApiProperty({
    description: 'Quận/Huyện',
    example: 'Quận 1',
    required: false,
  })
  district?: string;

  @ApiProperty({
    description: 'Phường/Xã',
    example: 'Phường Bến Nghé',
    required: false,
  })
  ward?: string;

  @ApiProperty({
    description: 'Thôn/Ấp/Xóm/Tổ (dùng cho GHTK)',
    example: 'Không rõ',
    required: false,
  })
  hamlet?: string;

  @ApiProperty({
    description: 'Mã bưu điện',
    example: '70000',
    required: false,
  })
  postalCode?: string;

  @ApiProperty({
    description: 'Có phải địa chỉ mặc định không',
    example: false,
  })
  isDefault: boolean;

  @ApiProperty({
    description: 'Loại địa chỉ',
    example: 'home',
  })
  addressType: string;

  @ApiProperty({
    description: 'Ghi chú địa chỉ',
    example: 'Gần chợ Bến Thành',
    required: false,
  })
  note?: string;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1641708800000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1641708800000,
  })
  updatedAt: number;
}

/**
 * DTO cho địa chỉ trong logistic info (có thể chọn có sẵn hoặc tạo mới)
 */
export class OrderAddressDto {
  @ApiProperty({
    description: 'ID địa chỉ có sẵn (nếu chọn địa chỉ có sẵn)',
    example: 1,
    required: false,
    examples: {
      'Chọn địa chỉ có sẵn': {
        summary: 'Sử dụng địa chỉ đã lưu',
        description: 'Chọn một địa chỉ đã được lưu trước đó trong hệ thống',
        value: 1
      }
    }
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID địa chỉ phải là số' })
  @Min(1, { message: 'ID địa chỉ phải lớn hơn 0' })
  addressId?: number;

  @ApiProperty({
    description: 'Thông tin địa chỉ mới (nếu tạo địa chỉ mới)',
    type: CreateUserAddressDto,
    required: false,
    examples: {
      'Tạo địa chỉ mới': {
        summary: 'Tạo địa chỉ mới cho đơn hàng',
        description: 'Tạo một địa chỉ mới và lưu vào hệ thống',
        value: {
          recipientName: "Nguyễn Văn A",
          recipientPhone: "0912345678",
          address: "123 Đường ABC, Phường 1",
          province: "TP. Hồ Chí Minh",
          district: "Quận 1",
          ward: "Phường Bến Nghé",
          postalCode: "70000",
          isDefault: false,
          addressType: "home",
          note: "Gần chợ Bến Thành"
        }
      }
    }
  })
  @IsOptional()
  @Type(() => CreateUserAddressDto)
  newAddress?: CreateUserAddressDto;
}
