import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { UserConvertCustomerResponseDto } from './user-convert-customer-response.dto';

/**
 * DTO cho response khi lấy chi tiết thông tin chuyển đổi khách hàng kèm thông tin khách hàng
 */
export class UserConvertDetailResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID bản ghi chuyển đổi',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'ID khách hàng được chuyển đổi',
    example: 101,
    nullable: true,
  })
  convertCustomerId: number;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng thực hiện chuyển đổi',
    example: 1001,
    nullable: true,
  })
  userId: number;

  @Expose()
  @ApiProperty({
    description: '<PERSON>ại chuyển đổi',
    example: 'online',
    nullable: true,
  })
  conversionType: string;

  @Expose()
  @ApiProperty({
    description: 'Nguồn gốc chuyển đổi',
    example: 'website',
    nullable: true,
  })
  source: string;

  @Expose()
  @ApiProperty({
    description: 'Ghi chú thêm về chuyển đổi',
    example: 'Khách hàng đăng ký qua form liên hệ',
    nullable: true,
  })
  notes: string;

  @Expose()
  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { campaign: 'summer_sale', referrer: 'google' },
    nullable: true,
  })
  content: any;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1741708800000,
  })
  updatedAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thông tin chi tiết của khách hàng được chuyển đổi',
    type: UserConvertCustomerResponseDto,
    nullable: true,
  })
  @Type(() => UserConvertCustomerResponseDto)
  customer?: UserConvertCustomerResponseDto;
}
