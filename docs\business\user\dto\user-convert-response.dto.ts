import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho thông tin khách hàng chuyển đổi
 */
export class UserConvertCustomerDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách hàng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string;

  @Expose()
  @ApiProperty({
    description: 'Tên khách hàng',
    example: '<PERSON><PERSON><PERSON>',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Email khách hàng',
    example: { primary: '<EMAIL>', secondary: '<EMAIL>' },
    nullable: true,
  })
  email: Record<string, string>;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại',
    example: '0901234567',
    nullable: true,
  })
  phone: string;

  @Expose()
  @ApiProperty({
    description: 'Nền tảng nguồn',
    example: 'Facebook',
    nullable: true,
  })
  platform: string;

  @Expose()
  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
    nullable: true,
  })
  timezone: string;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng sở hữu',
    example: 123,
    nullable: true,
  })
  userId: number;

  @Expose()
  @ApiProperty({
    description: 'ID agent hỗ trợ',
    example: '123e4567-e89b-12d3-a456-426614174000',
    nullable: true,
  })
  agentId: string;

  @Expose()
  @ApiProperty({
    description: 'Thông tin metadata',
    example: { customField1: 'value1', customField2: 'value2' },
    nullable: true,
  })
  metadata: Record<string, unknown>;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1741708800000,
  })
  updatedAt: number;
}

/**
 * DTO cho response khi lấy thông tin chuyển đổi khách hàng
 */
export class UserConvertResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID bản ghi chuyển đổi',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'ID khách hàng được chuyển đổi',
    example: 101,
    nullable: true,
  })
  convertCustomerId: number;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng thực hiện chuyển đổi',
    example: 1001,
    nullable: true,
  })
  userId: number;

  @Expose()
  @ApiProperty({
    description: 'Loại chuyển đổi',
    example: 'online',
    nullable: true,
  })
  conversionType: string;

  @Expose()
  @ApiProperty({
    description: 'Nguồn gốc chuyển đổi',
    example: 'website',
    nullable: true,
  })
  source: string;

  @Expose()
  @ApiProperty({
    description: 'Ghi chú thêm về chuyển đổi',
    example: 'Khách hàng đăng ký qua form liên hệ',
    nullable: true,
  })
  notes: string;

  @Expose()
  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { campaign: 'summer_sale', referrer: 'google' },
    nullable: true,
  })
  content: Record<string, unknown>;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1741708800000,
  })
  updatedAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thông tin khách hàng chuyển đổi',
    type: UserConvertCustomerDto,
    nullable: true,
  })
  convertCustomer: UserConvertCustomerDto;
}

/**
 * DTO cho danh sách chuyển đổi khách hàng
 */
export class UserConvertListItemDto {
  @Expose()
  @ApiProperty({
    description: 'ID bản ghi chuyển đổi',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'ID khách hàng được chuyển đổi',
    example: 101,
    nullable: true,
  })
  convertCustomerId: number;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng thực hiện chuyển đổi',
    example: 1001,
    nullable: true,
  })
  userId: number;

  @Expose()
  @ApiProperty({
    description: 'Loại chuyển đổi',
    example: 'online',
    nullable: true,
  })
  conversionType: string;

  @Expose()
  @ApiProperty({
    description: 'Nguồn gốc chuyển đổi',
    example: 'website',
    nullable: true,
  })
  source: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createdAt: number;
}
