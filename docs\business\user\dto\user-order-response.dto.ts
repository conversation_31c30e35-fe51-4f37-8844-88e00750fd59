import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { OrderStatusEnum, ShippingStatusEnum } from '../../enums';
import { UserConvertCustomerListItemDto } from './user-convert-customer-response.dto';

/**
 * DTO cho response khi lấy thông tin đơn hàng
 */
export class UserOrderResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'ID của khách hàng',
    example: 101,
    nullable: true,
  })
  userConvertCustomerId: number;

  @Expose()
  @ApiProperty({
    description: 'ID của người dùng sở hữu đơn hàng',
    example: 1001,
    nullable: true,
  })
  userId: number;

  @Expose()
  @ApiProperty({
    description: 'Thông tin sản phẩm',
    example: [
      {
        id: 1,
        name: '<PERSON><PERSON> thun nam',
        quantity: 2,
        price: 150000,
        totalPrice: 300000,
      },
    ],
    nullable: true,
  })
  productInfo: any;

  @Expose()
  @ApiProperty({
    description: 'Thông tin hóa đơn',
    example: {
      subtotal: 300000,
      tax: 30000,
      shipping: 20000,
      discount: 0,
      total: 350000,
      paymentMethod: 'COD',
    },
    nullable: true,
  })
  billInfo: any;

  @Expose()
  @ApiProperty({
    description: 'Đơn hàng có yêu cầu vận chuyển hay không',
    example: true,
  })
  hasShipping: boolean;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.PENDING,
    nullable: true,
  })
  shippingStatus: ShippingStatusEnum;

  @Expose()
  @ApiProperty({
    description: 'Thông tin vận chuyển chi tiết',
    example: {
      address: '123 Đường ABC, Quận 1, TP.HCM',
      receiver: 'Nguyễn Văn A',
      phone: '0912345678',
      carrier: 'GHN',
      trackingNumber: 'GHN123456789',
    },
    nullable: true,
  })
  logisticInfo: any;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo đơn hàng (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật đơn hàng (millis)',
    example: 1741708800000,
  })
  updatedAt: number;

  @Expose()
  @ApiProperty({
    description: 'Nguồn đơn hàng',
    example: 'website',
    nullable: true,
  })
  source: string;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    enum: OrderStatusEnum,
    example: OrderStatusEnum.PENDING,
    nullable: true,
  })
  orderStatus: OrderStatusEnum;
}

/**
 * DTO cho danh sách đơn hàng
 */
export class UserOrderListItemDto {
  @Expose()
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Thông tin đầy đủ của khách hàng chuyển đổi',
    type: UserConvertCustomerListItemDto,
    nullable: true,
  })
  @Type(() => UserConvertCustomerListItemDto)
  userConvertCustomer: UserConvertCustomerListItemDto;

  @Expose()
  @ApiProperty({
    description: 'Thông tin hóa đơn tóm tắt',
    example: {
      total: 350000,
      paymentMethod: 'COD',
    },
    nullable: true,
  })
  billInfo: any;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.PENDING,
    nullable: true,
  })
  shippingStatus: ShippingStatusEnum;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo đơn hàng (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Nguồn đơn hàng',
    example: 'website',
    nullable: true,
  })
  source: string;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    enum: OrderStatusEnum,
    example: OrderStatusEnum.PENDING,
    nullable: true,
  })
  orderStatus: OrderStatusEnum;
}
