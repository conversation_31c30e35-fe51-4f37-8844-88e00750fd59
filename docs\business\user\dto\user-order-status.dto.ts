import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho thống kê trạng thái đơn hàng
 */
export class OrderStatusStatsDto {
  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đang chờ xử lý',
    example: 5,
  })
  pending: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đã xác nhận',
    example: 10,
  })
  confirmed: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đang xử lý',
    example: 3,
  })
  processing: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đã hoàn thành',
    example: 25,
  })
  completed: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đã hủy',
    example: 2,
  })
  cancelled: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số đơn hàng (bao gồm cả đơn có và không có vận chuyển)',
    example: 45,
  })
  total: number;
}

/**
 * DTO cho thống kê trạng thái vận chuyển
 */
export class ShippingStatusStatsDto {
  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đang chờ xử lý',
    example: 8,
  })
  pending: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đang chuẩn bị',
    example: 2,
  })
  preparing: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đã giao cho đơn vị vận chuyển',
    example: 5,
  })
  shipped: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đang vận chuyển/giao hàng',
    example: 3,
  })
  inTransit: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đang phân loại',
    example: 1,
  })
  sorting: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đã giao thành công',
    example: 20,
  })
  delivered: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng giao thất bại',
    example: 1,
  })
  deliveryFailed: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đang trả lại',
    example: 1,
  })
  returning: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng đơn hàng đã hủy',
    example: 3,
  })
  cancelled: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số đơn hàng có vận chuyển',
    example: 45,
  })
  total: number;
}

/**
 * DTO cho response thống kê trạng thái đơn hàng và vận chuyển
 */
export class UserOrderStatusResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Thống kê trạng thái đơn hàng',
    type: OrderStatusStatsDto,
  })
  orderStatus: OrderStatusStatsDto;

  @Expose()
  @ApiProperty({
    description: 'Thống kê trạng thái vận chuyển',
    type: ShippingStatusStatsDto,
  })
  shippingStatus: ShippingStatusStatsDto;
}
