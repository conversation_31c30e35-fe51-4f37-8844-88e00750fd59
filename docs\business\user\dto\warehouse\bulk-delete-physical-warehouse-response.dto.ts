import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả từng item trong bulk delete kho vật lý
 */
export class BulkDeleteResultItemDto {
  /**
   * ID kho vật lý
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho vật lý',
    example: 1,
  })
  warehouseId: number;

  /**
   * Trạng thái xử lý
   * - success: Xóa thành công
   * - error: Có lỗi xảy ra
   * @example "success"
   */
  @ApiProperty({
    description: 'Trạng thái xử lý',
    enum: ['success', 'error'],
    example: 'success',
  })
  status: 'success' | 'error';

  /**
   * Thông báo chi tiết
   * @example "Xóa kho vật lý thành công"
   */
  @ApiProperty({
    description: 'Thông báo chi tiết',
    example: 'Xóa kho vật lý thành công',
  })
  message: string;
}

/**
 * DTO cho phản hồi bulk delete kho vật lý
 */
export class BulkDeletePhysicalWarehouseResponseDto {
  /**
   * Tổng số kho được yêu cầu xóa
   * @example 5
   */
  @ApiProperty({
    description: 'Tổng số kho được yêu cầu xóa',
    example: 5,
  })
  totalRequested: number;

  /**
   * Số kho xóa thành công
   * @example 3
   */
  @ApiProperty({
    description: 'Số kho xóa thành công',
    example: 3,
  })
  successCount: number;

  /**
   * Số kho xóa thất bại
   * @example 2
   */
  @ApiProperty({
    description: 'Số kho xóa thất bại',
    example: 2,
  })
  failureCount: number;

  /**
   * Kết quả chi tiết cho từng kho
   */
  @ApiProperty({
    description: 'Kết quả chi tiết cho từng kho',
    type: [BulkDeleteResultItemDto],
  })
  results: BulkDeleteResultItemDto[];

  /**
   * Thông báo tổng quan
   * @example "Xóa thành công 3/5 kho vật lý"
   */
  @ApiProperty({
    description: 'Thông báo tổng quan',
    example: 'Xóa thành công 3/5 kho vật lý',
  })
  message: string;
}
