import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo kho ảo
 */
export class CreateVirtualWarehouseDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;
  /**
   * <PERSON>ệ thống liên kết
   * @example "SAP ERP"
   */
  @ApiProperty({
    description: '<PERSON>ệ thống liên kết',
    example: 'SAP ERP',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Hệ thống liên kết phải là chuỗi' })
  @MaxLength(100, { message: '<PERSON><PERSON> thống liên kết không được vượt quá 100 ký tự' })
  associatedSystem?: string;

  /**
   * <PERSON><PERSON><PERSON> đích sử dụng
   * @example "Quản lý hàng hóa trực tuyến"
   */
  @ApiProperty({
    description: 'Mục đích sử dụng',
    example: 'Quản lý hàng hóa trực tuyến',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mục đích sử dụng phải là chuỗi' })
  purpose?: string;
}
