import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';

/**
 * DTO cho việc tạo kho mới
 */
export class CreateWarehouseDto {
  /**
   * ID người dùng (được tự động lấy từ token JWT)
   */
  @ApiHideProperty()
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;
  /**
   * Tên kho
   * @example "Kho hàng chính"
   */
  @ApiProperty({
    description: 'Tên kho',
    example: 'Kho hàng chính',
    maxLength: 100,
  })
  @IsNotEmpty({ message: 'Tên kho không được để trống' })
  @IsString({ message: 'Tên kho phải là chuỗi' })
  @MaxLength(100, { message: 'Tên kho không được vượt quá 100 ký tự' })
  name: string;

  /**
   * Mô tả kho
   * @example "Kho chứa các sản phẩm chính của công ty"
   */
  @ApiProperty({
    description: 'Mô tả kho',
    example: 'Kho chứa các sản phẩm chính của công ty',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả kho phải là chuỗi' })
  description?: string;

  /**
   * Loại kho
   * @example "PHYSICAL"
   */
  @ApiProperty({
    description: 'Loại kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.PHYSICAL,
    default: WarehouseTypeEnum.PHYSICAL,
  })
  @IsEnum(WarehouseTypeEnum, { message: 'Loại kho không hợp lệ' })
  type: WarehouseTypeEnum;

  /**
   * Danh sách custom fields cho kho
   * @example [{"customFieldId": 1, "value": {"value": "Giá trị mẫu"}}]
   */
  @ApiProperty({
    description: 'Danh sách custom fields cho kho',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];
}
