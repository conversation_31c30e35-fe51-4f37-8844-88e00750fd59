import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * DTO cho các tham số truy vấn danh sách kho
 */
export class QueryWarehouseDto extends QueryDto {
  /**
   * ID người dùng (được tự động lấy từ token JWT)
   */
  @ApiHideProperty()
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;
  /**
   * Lọc theo loại kho
   * @example "PHYSICAL"
   */
  @ApiProperty({
    description: 'Lọ<PERSON> theo loạ<PERSON> kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.PHYSICAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(WarehouseTypeEnum)
  @Type(() => String)
  type?: WarehouseTypeEnum;

  /**
   * Trường sắp xếp
   * @example "warehouseId"
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ['warehouseId', 'name', 'type'],
    default: 'warehouseId',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'warehouseId';

  /**
   * Hướng sắp xếp
   * @example "ASC"
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.ASC;


  @ApiProperty({
    description: 'Vị trí bắt đầu',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  offset?: number
}
