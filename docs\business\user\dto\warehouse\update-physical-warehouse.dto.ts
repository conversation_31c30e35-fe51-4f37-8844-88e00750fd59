import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc cập nhật kho vật lý
 */
export class UpdatePhysicalWarehouseDto {
  /**
   * Địa chỉ kho
   * @example "123 Đường XYZ, Quận 2, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ kho',
    example: '123 Đường XYZ, Quận 2, TP.HCM',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ kho phải là chuỗi' })
  @MaxLength(255, { message: 'Địa chỉ kho không được vượt quá 255 ký tự' })
  address?: string;

  /**
   * Sức chứa kho
   * @example 2000
   */
  @ApiProperty({
    description: 'Sức chứa kho',
    example: 2000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '<PERSON>ứ<PERSON> chứa kho phải là số' })
  @Type(() => Number)
  capacity?: number;
}
