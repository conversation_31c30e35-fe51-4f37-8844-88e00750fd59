import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật kho ảo
 */
export class UpdateVirtualWarehouseDto {
  /**
   * <PERSON><PERSON> thống liên kết
   * @example "Oracle ERP"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> thống liên kết',
    example: 'Oracle ERP',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Hệ thống liên kết phải là chuỗi' })
  @MaxLength(100, { message: '<PERSON><PERSON> thống liên kết không được vượt quá 100 ký tự' })
  associatedSystem?: string;

  /**
   * <PERSON><PERSON>c đích sử dụng
   * @example "Quản lý hàng hóa và đồng bộ với hệ thống trung tâm"
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> đích sử dụng',
    example: 'Quản lý hàng hóa và đồng bộ với hệ thống trung tâm',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mục đích sử dụng phải là chuỗi' })
  purpose?: string;
}
