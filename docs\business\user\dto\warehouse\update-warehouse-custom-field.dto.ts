import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject } from 'class-validator';

/**
 * DTO cho việc cập nhật trường tùy chỉnh của kho
 */
export class UpdateWarehouseCustomFieldDto {
  /**
   * Gi<PERSON> trị của trường tùy chỉnh
   * @example { "value": "Giá trị mẫu đã cập nhật" }
   */
  @ApiProperty({
    description: 'Gi<PERSON> trị của trường tùy chỉnh',
    example: { value: 'Giá trị mẫu đã cập nhật' },
  })
  @IsNotEmpty({ message: 'Gi<PERSON> trị trường tùy chỉnh không được để trống' })
  @IsObject({ message: 'Giá trị trường tùy chỉnh phải là đối tượng JSON' })
  value: any;
}
