import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { WarehouseCustomFieldResponseDto } from './warehouse-custom-field-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * DTO cho phản hồi thông tin kho ảo
 */
export class VirtualWarehouseResponseDto {
  /**
   * ID của kho
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của kho',
    example: 1,
  })
  warehouseId: number;

  /**
   * Tên kho
   * @example "Kho ảo ERP"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên kho',
    example: 'Kho ảo ERP',
  })
  name: string;

  /**
   * Mô tả kho
   * @example "Kho ảo đồng bộ với hệ thống ERP"
   */
  @Expose()
  @ApiProperty({
    description: 'Mô tả kho',
    example: '<PERSON>ho ảo đồng bộ với hệ thống ERP',
    required: false,
  })
  description: string | null;

  /**
   * <PERSON>ạ<PERSON> kho
   * @example "VIRTUAL"
   */
  @Expose()
  @ApiProperty({
    description: 'Loại kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.VIRTUAL,
  })
  type: WarehouseTypeEnum;

  /**
   * Hệ thống liên kết
   * @example "SAP ERP"
   */
  @Expose()
  @ApiProperty({
    description: 'Hệ thống liên kết',
    example: 'SAP ERP',
    required: false,
  })
  associatedSystem: string | null;

  /**
   * Mục đích sử dụng
   * @example "Quản lý hàng hóa trực tuyến"
   */
  @Expose()
  @ApiProperty({
    description: 'Mục đích sử dụng',
    example: 'Quản lý hàng hóa trực tuyến',
    required: false,
  })
  purpose: string | null;

  /**
   * Danh sách trường tùy chỉnh của kho
   */
  @Expose()
  @Type(() => WarehouseCustomFieldResponseDto)
  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh của kho',
    type: [WarehouseCustomFieldResponseDto],
    required: false,
  })
  customFields?: WarehouseCustomFieldResponseDto[];
}
