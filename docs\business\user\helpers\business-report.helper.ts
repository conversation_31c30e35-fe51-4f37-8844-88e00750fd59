import { Injectable } from '@nestjs/common';
import { 
  ReportPeriodEnum, 
  ChartGroupByEnum,
  ReportOverviewQueryDto,
  SalesChartQueryDto,
  OrdersChartQueryDto,
  CustomersChartQueryDto,
  ProductsChartQueryDto,
  TopSellingProductsQueryDto,
  PotentialCustomersQueryDto
} from '../dto/report';

/**
 * Helper xử lý logic nghiệp vụ cho báo cáo business
 */
@Injectable()
export class BusinessReportHelper {

  /**
   * Tính toán khoảng thời gian mặc định dựa trên period
   */
  getDefaultDateRange(period: ReportPeriodEnum): { startDate: string; endDate: string } {
    const now = new Date();
    const endDate = now.toISOString().split('T')[0];
    let startDate: string;

    switch (period) {
      case ReportPeriodEnum.DAY:
        // 30 ngày gần nhất
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        break;
      case ReportPeriodEnum.WEEK:
        // 12 tuần gần nhất
        startDate = new Date(now.getTime() - 12 * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        break;
      case ReportPeriodEnum.MONTH:
        // 12 tháng gần nhất
        const monthsAgo = new Date(now.getFullYear(), now.getMonth() - 12, 1);
        startDate = monthsAgo.toISOString().split('T')[0];
        break;
      case ReportPeriodEnum.QUARTER:
        // 8 quý gần nhất (2 năm)
        const quartersAgo = new Date(now.getFullYear() - 2, 0, 1);
        startDate = quartersAgo.toISOString().split('T')[0];
        break;
      case ReportPeriodEnum.YEAR:
        // 5 năm gần nhất
        const yearsAgo = new Date(now.getFullYear() - 5, 0, 1);
        startDate = yearsAgo.toISOString().split('T')[0];
        break;
      default:
        // Mặc định 12 tháng
        startDate = new Date(now.getFullYear(), now.getMonth() - 12, 1).toISOString().split('T')[0];
    }

    return { startDate, endDate };
  }

  /**
   * Tính toán khoảng thời gian kỳ trước để so sánh
   */
  getPreviousPeriodDateRange(startDate: string, endDate: string): { startDate: string; endDate: string } {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const duration = end.getTime() - start.getTime();

    const previousEnd = new Date(start.getTime() - 1);
    const previousStart = new Date(previousEnd.getTime() - duration);

    return {
      startDate: previousStart.toISOString().split('T')[0],
      endDate: previousEnd.toISOString().split('T')[0]
    };
  }

  /**
   * Tính toán tỷ lệ tăng trưởng
   */
  calculateGrowthRate(current: number, previous: number): number {
    if (previous === 0) {
      return current > 0 ? 100 : 0;
    }
    return ((current - previous) / previous) * 100;
  }

  /**
   * Xác định groupBy mặc định dựa trên khoảng thời gian
   */
  getDefaultGroupBy(startDate: string, endDate: string): ChartGroupByEnum {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff <= 7) {
      return ChartGroupByEnum.DAY;
    } else if (daysDiff <= 90) {
      return ChartGroupByEnum.WEEK;
    } else if (daysDiff <= 730) {
      return ChartGroupByEnum.MONTH;
    } else {
      return ChartGroupByEnum.QUARTER;
    }
  }

  /**
   * Validate date range
   */
  validateDateRange(startDate?: string, endDate?: string): void {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start > end) {
        throw new Error('Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc');
      }

      // Kiểm tra khoảng thời gian tối đa (2 năm)
      const maxDuration = 2 * 365 * 24 * 60 * 60 * 1000; // 2 years in milliseconds
      if (end.getTime() - start.getTime() > maxDuration) {
        throw new Error('Khoảng thời gian báo cáo không được vượt quá 2 năm');
      }
    }
  }

  /**
   * Format số tiền
   */
  formatCurrency(amount: number, currency: string = 'VND'): string {
    if (currency === 'VND') {
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(amount);
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }

  /**
   * Format phần trăm
   */
  formatPercentage(value: number, decimals: number = 1): string {
    return `${value.toFixed(decimals)}%`;
  }

  /**
   * Tạo period name cho biểu đồ
   */
  generatePeriodName(date: string, groupBy: ChartGroupByEnum): string {
    const dateObj = new Date(date);
    
    switch (groupBy) {
      case ChartGroupByEnum.DAY:
        return dateObj.toLocaleDateString('vi-VN');
      case ChartGroupByEnum.WEEK:
        const weekNumber = this.getWeekNumber(dateObj);
        return `Tuần ${weekNumber}/${dateObj.getFullYear()}`;
      case ChartGroupByEnum.MONTH:
        return `${(dateObj.getMonth() + 1).toString().padStart(2, '0')}/${dateObj.getFullYear()}`;
      case ChartGroupByEnum.QUARTER:
        const quarter = Math.floor(dateObj.getMonth() / 3) + 1;
        return `Quý ${quarter}/${dateObj.getFullYear()}`;
      default:
        return date;
    }
  }

  /**
   * Lấy số tuần trong năm
   */
  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  /**
   * Tính toán tỷ lệ giữ chân khách hàng
   */
  calculateRetentionRate(newCustomers: number, returningCustomers: number): number {
    const totalCustomers = newCustomers + returningCustomers;
    if (totalCustomers === 0) return 0;
    return (returningCustomers / totalCustomers) * 100;
  }

  /**
   * Tính toán thời gian sống trung bình của khách hàng
   */
  calculateAverageCustomerLifetime(customers: any[]): number {
    if (customers.length === 0) return 0;
    
    const totalLifetime = customers.reduce((sum, customer) => {
      const createdAt = new Date(customer.createdAt);
      const lastOrderDate = customer.lastOrderDate ? new Date(customer.lastOrderDate) : new Date();
      const lifetime = (lastOrderDate.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
      return sum + lifetime;
    }, 0);

    return Math.round(totalLifetime / customers.length);
  }

  /**
   * Tạo summary cho sales chart
   */
  createSalesChartSummary(data: any[]) {
    const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0);
    const totalOrders = data.reduce((sum, item) => sum + item.orders, 0);
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const revenues = data.map(item => item.revenue);
    const highestRevenue = Math.max(...revenues, 0);
    const lowestRevenue = Math.min(...revenues, 0);

    return {
      totalRevenue,
      totalOrders,
      averageOrderValue,
      highestRevenue,
      lowestRevenue
    };
  }

  /**
   * Tạo status breakdown cho orders chart
   */
  createOrdersStatusBreakdown(data: any[]) {
    return data.reduce((breakdown, item) => ({
      pending: breakdown.pending + item.pendingOrders,
      confirmed: breakdown.confirmed + item.confirmedOrders,
      shipping: breakdown.shipping + item.shippingOrders,
      delivered: breakdown.delivered + item.deliveredOrders,
      cancelled: breakdown.cancelled + item.cancelledOrders
    }), {
      pending: 0,
      confirmed: 0,
      shipping: 0,
      delivered: 0,
      cancelled: 0
    });
  }

  /**
   * Tạo summary cho customers chart
   */
  createCustomersChartSummary(data: any[]) {
    const totalNewCustomers = data.reduce((sum, item) => sum + item.newCustomers, 0);
    const totalReturningCustomers = data.reduce((sum, item) => sum + item.returningCustomers, 0);
    const customerRetentionRate = this.calculateRetentionRate(totalNewCustomers, totalReturningCustomers);
    
    return {
      totalNewCustomers,
      totalReturningCustomers,
      customerRetentionRate,
      averageCustomerLifetime: 180 // TODO: Calculate from actual data
    };
  }

  /**
   * Tạo summary cho products chart
   */
  createProductsChartSummary(data: any[]) {
    const totalProducts = data.length;
    const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0);
    const totalQuantitySold = data.reduce((sum, item) => sum + item.quantitySold, 0);
    const bestSellingProduct = data.length > 0 ? {
      id: data[0].productId,
      name: data[0].productName,
      revenue: data[0].revenue
    } : null;

    return {
      totalProducts,
      totalRevenue,
      totalQuantitySold,
      bestSellingProduct
    };
  }
}
