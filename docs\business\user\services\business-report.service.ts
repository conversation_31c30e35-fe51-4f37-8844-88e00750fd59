import { Injectable, Logger } from '@nestjs/common';
import { BusinessReportRepository } from '@modules/business/repositories/business-report.repository';
import { BusinessReportHelper } from '@modules/business/user/helpers';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { plainToInstance } from 'class-transformer';
import {
  ReportOverviewQueryDto,
  ReportOverviewResponseDto,
  ReportPeriodEnum,
  SalesChartQueryDto,
  SalesChartResponseDto,
  OrdersChartQueryDto,
  OrdersChartResponseDto,
  CustomersChartQueryDto,
  CustomersChartResponseDto,
  ProductsChartQueryDto,
  ProductsChartResponseDto,
  TopSellingProductsQueryDto,
  TopSellingProductsResponseDto,
  PotentialCustomersQueryDto,
  PotentialCustomersResponseDto
} from '../dto/report';

/**
 * Service xử lý logic nghiệp vụ cho báo cáo business
 */
@Injectable()
export class BusinessReportService {
  private readonly logger = new Logger(BusinessReportService.name);

  constructor(
    private readonly businessReportRepository: BusinessReportRepository,
    private readonly businessReportHelper: BusinessReportHelper,
  ) {}

  /**
   * Lấy dữ liệu tổng quan báo cáo
   */
  async getReportOverview(userId: number, query: ReportOverviewQueryDto): Promise<ReportOverviewResponseDto> {
    try {
      this.logger.log(`Lấy tổng quan báo cáo cho user ${userId} với query: ${JSON.stringify(query)}`);

      // Validate input
      this.businessReportHelper.validateDateRange(query.startDate, query.endDate);

      // Lấy khoảng thời gian mặc định nếu không có
      const dateRange = query.startDate && query.endDate 
        ? { startDate: query.startDate, endDate: query.endDate }
        : this.businessReportHelper.getDefaultDateRange(query.period || ReportPeriodEnum.MONTH);

      // Lấy dữ liệu hiện tại
      const currentData = await this.businessReportRepository.getReportOverview(userId, {
        ...query,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });

      // Lấy dữ liệu kỳ trước để so sánh
      const previousDateRange = this.businessReportHelper.getPreviousPeriodDateRange(
        dateRange.startDate, 
        dateRange.endDate
      );
      
      const previousData = await this.businessReportRepository.getReportOverview(userId, {
        ...query,
        startDate: previousDateRange.startDate,
        endDate: previousDateRange.endDate
      });

      // Tính toán tỷ lệ tăng trưởng
      const revenueGrowth = this.businessReportHelper.calculateGrowthRate(
        currentData.totalRevenue, 
        previousData.totalRevenue
      );
      const ordersGrowth = this.businessReportHelper.calculateGrowthRate(
        currentData.totalOrders, 
        previousData.totalOrders
      );
      const customersGrowth = this.businessReportHelper.calculateGrowthRate(
        currentData.newCustomers, 
        previousData.newCustomers
      );

      const result = {
        ...currentData,
        period: query.period,
        previousPeriod: {
          totalRevenue: previousData.totalRevenue,
          totalOrders: previousData.totalOrders,
          newCustomers: previousData.newCustomers,
          revenueGrowth,
          ordersGrowth,
          customersGrowth
        }
      };

      return plainToInstance(ReportOverviewResponseDto, result, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy tổng quan báo cáo: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_OVERVIEW_FAILED,
        `Lỗi khi lấy tổng quan báo cáo: ${error.message}`
      );
    }
  }

  /**
   * Lấy dữ liệu biểu đồ doanh thu
   */
  async getSalesChartData(userId: number, query: SalesChartQueryDto): Promise<SalesChartResponseDto> {
    try {
      this.logger.log(`Lấy dữ liệu biểu đồ doanh thu cho user ${userId} với query: ${JSON.stringify(query)}`);

      // Validate input
      this.businessReportHelper.validateDateRange(query.startDate, query.endDate);

      // Lấy dữ liệu từ repository
      const data = await this.businessReportRepository.getSalesChartData(userId, query);

      // Tạo summary
      const summary = this.businessReportHelper.createSalesChartSummary(data);

      const result = {
        data,
        summary
      };

      return plainToInstance(SalesChartResponseDto, result, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu biểu đồ doanh thu: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_SALES_CHART_FAILED,
        `Lỗi khi lấy dữ liệu biểu đồ doanh thu: ${error.message}`
      );
    }
  }

  /**
   * Lấy dữ liệu biểu đồ đơn hàng
   */
  async getOrdersChartData(userId: number, query: OrdersChartQueryDto): Promise<OrdersChartResponseDto> {
    try {
      this.logger.log(`Lấy dữ liệu biểu đồ đơn hàng cho user ${userId} với query: ${JSON.stringify(query)}`);

      // Validate input
      this.businessReportHelper.validateDateRange(query.startDate, query.endDate);

      // Lấy dữ liệu từ repository
      const data = await this.businessReportRepository.getOrdersChartData(userId, query);

      // Tạo status breakdown
      const statusBreakdown = this.businessReportHelper.createOrdersStatusBreakdown(data);

      const result = {
        data,
        statusBreakdown
      };

      return plainToInstance(OrdersChartResponseDto, result, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu biểu đồ đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_ORDERS_CHART_FAILED,
        `Lỗi khi lấy dữ liệu biểu đồ đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Lấy dữ liệu biểu đồ khách hàng
   */
  async getCustomersChartData(userId: number, query: CustomersChartQueryDto): Promise<CustomersChartResponseDto> {
    try {
      this.logger.log(`Lấy dữ liệu biểu đồ khách hàng cho user ${userId} với query: ${JSON.stringify(query)}`);

      // Validate input
      this.businessReportHelper.validateDateRange(query.startDate, query.endDate);

      // Lấy dữ liệu từ repository
      const data = await this.businessReportRepository.getCustomersChartData(userId, query);

      // Tạo summary
      const summary = this.businessReportHelper.createCustomersChartSummary(data);

      const result = {
        data,
        summary
      };

      return plainToInstance(CustomersChartResponseDto, result, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu biểu đồ khách hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_CUSTOMERS_CHART_FAILED,
        `Lỗi khi lấy dữ liệu biểu đồ khách hàng: ${error.message}`
      );
    }
  }

  /**
   * Lấy dữ liệu biểu đồ sản phẩm
   */
  async getProductsChartData(userId: number, query: ProductsChartQueryDto): Promise<ProductsChartResponseDto> {
    try {
      this.logger.log(`Lấy dữ liệu biểu đồ sản phẩm cho user ${userId} với query: ${JSON.stringify(query)}`);

      // Validate input
      this.businessReportHelper.validateDateRange(query.startDate, query.endDate);

      // Lấy dữ liệu từ repository (sử dụng top selling products logic)
      const data = await this.businessReportRepository.getTopSellingProducts(
        userId,
        query,
      );

      // Tạo summary
      const summary =
        this.businessReportHelper.createProductsChartSummary(data);

      const result = {
        data: data.map(
          (item: {
            productId: any;
            productName: any;
            categoryName: any;
            revenue: number;
            quantitySold: number;
            ordersCount: number;
            imageUrl: any;
          }) => {
            return {
              productId: item.productId,
              productName: item.productName,
              categoryName: item.categoryName,
              revenue: item.revenue,
              quantitySold: item.quantitySold,
              ordersCount: item.ordersCount,
              averagePrice:
                item.ordersCount > 0 ? item.revenue / item.quantitySold : 0,
              imageUrl: item.imageUrl,
            };
          },
        ),
        summary,
      };

      return plainToInstance(ProductsChartResponseDto, result, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu biểu đồ sản phẩm: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_PRODUCTS_CHART_FAILED,
        `Lỗi khi lấy dữ liệu biểu đồ sản phẩm: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách sản phẩm bán chạy
   */
  async getTopSellingProducts(userId: number, query: TopSellingProductsQueryDto): Promise<TopSellingProductsResponseDto> {
    try {
      this.logger.log(`Lấy danh sách sản phẩm bán chạy cho user ${userId} với query: ${JSON.stringify(query)}`);

      // Validate input
      this.businessReportHelper.validateDateRange(query.startDate, query.endDate);

      // Lấy dữ liệu từ repository
      const data = await this.businessReportRepository.getTopSellingProducts(userId, query);

      const period = query.startDate && query.endDate 
        ? `${query.startDate} đến ${query.endDate}`
        : 'Tất cả thời gian';

      const result = {
        data,
        meta: {
          totalItems: data.length,
          period
        }
      };

      return plainToInstance(TopSellingProductsResponseDto, result, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách sản phẩm bán chạy: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_TOP_SELLING_PRODUCTS_FAILED,
        `Lỗi khi lấy danh sách sản phẩm bán chạy: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách khách hàng tiềm năng
   */
  async getPotentialCustomers(userId: number, query: PotentialCustomersQueryDto): Promise<PotentialCustomersResponseDto> {
    try {
      this.logger.log(`Lấy danh sách khách hàng tiềm năng cho user ${userId} với query: ${JSON.stringify(query)}`);

      // Lấy dữ liệu từ repository
      const data = await this.businessReportRepository.getPotentialCustomers(userId, query);

      const averagePotentialScore = data.length > 0 
        ? data.reduce((sum, customer) => sum + customer.potentialScore, 0) / data.length 
        : 0;

      const result = {
        data,
        meta: {
          totalItems: data.length,
          averagePotentialScore: Math.round(averagePotentialScore * 10) / 10
        }
      };

      return plainToInstance(PotentialCustomersResponseDto, result, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách khách hàng tiềm năng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.REPORT_POTENTIAL_CUSTOMERS_FAILED,
        `Lỗi khi lấy danh sách khách hàng tiềm năng: ${error.message}`
      );
    }
  }
}
