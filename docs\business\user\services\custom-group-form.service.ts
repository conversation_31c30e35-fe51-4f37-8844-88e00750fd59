import { Injectable, Logger } from '@nestjs/common';
import { CustomGroupFormRepository, CustomGroupFormFieldRepository, CustomFieldRepository, UserProductRepository } from '@modules/business/repositories';
import {
  CreateCustomGroupFormDto,
  UpdateCustomGroupFormDto,
  CustomGroupFormCreatedResponseDto,
  QueryCustomGroupFormDto,
  CustomGroupFormListItemDto,
  CustomGroupFormResponseDto,
} from '../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import { CustomGroupForm } from '@modules/business/entities';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response/api-response-dto';
import { ValidationHelper } from '../helpers/validation.helper';
import { CustomFieldDto, GridConfigDto } from '@modules/business/user/dto/custom-group-form-response.dto';

/**
 * Service xử lý logic nghiệp vụ cho nhóm trường tùy chỉnh
 */
@Injectable()
export class CustomGroupFormService {
  private readonly logger = new Logger(CustomGroupFormService.name);

  constructor(
    private readonly customGroupFormRepository: CustomGroupFormRepository,
    private readonly customGroupFormFieldRepository: CustomGroupFormFieldRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo nhóm trường tùy chỉnh mới
   * @param createDto DTO chứa thông tin tạo nhóm trường tùy chỉnh, bao gồm:
   *   - label: Nhãn hiển thị của nhóm trường
   *   - userId: ID của người dùng tạo nhóm trường (tùy chọn nếu có employeeId)
   *   - employeeId: ID của nhân viên tạo nhóm trường (tùy chọn nếu có userId)
   *   - productId: ID của sản phẩm liên kết với nhóm trường (tùy chọn)
   * @returns Thông tin nhóm trường tùy chỉnh đã tạo
   */
  @Transactional()
  async create(createDto: CreateCustomGroupFormDto): Promise<CustomGroupFormCreatedResponseDto> {
    try {
      this.logger.log(`Tạo nhóm trường tùy chỉnh mới: ${JSON.stringify(createDto)}`);

      // Kiểm tra xem userId có được cung cấp không
      this.validationHelper.validateUserProvided(createDto.userId);

      // Kiểm tra nếu có productId thì product có tồn tại và không bị xóa không
      if (createDto.productId) {
        const product = await this.userProductRepository.findById(createDto.productId);
        this.validationHelper.validateProductExists(product, createDto.productId);
      }

      // Tạo entity mới
      const newGroupForm = new CustomGroupForm();
      newGroupForm.label = createDto.label;
      newGroupForm.userId = createDto.userId || null;
      newGroupForm.productId = createDto.productId || null;

      // Lưu vào database
      const savedGroupForm = await this.customGroupFormRepository.save(newGroupForm);

      // Chuyển đổi sang DTO response
      return plainToInstance(CustomGroupFormCreatedResponseDto, savedGroupForm, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo nhóm trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.GROUP_FORM_CREATION_FAILED,
        'Lỗi khi tạo nhóm trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy danh sách nhóm trường tùy chỉnh với các điều kiện lọc và phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách nhóm trường tùy chỉnh với phân trang
   */
  async findAll(queryDto: QueryCustomGroupFormDto): Promise<PaginatedResult<CustomGroupFormListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách nhóm trường tùy chỉnh với điều kiện: ${JSON.stringify(queryDto)}`);

      // Lấy danh sách nhóm trường tùy chỉnh từ repository
      const result = await this.customGroupFormRepository.findAll(queryDto);

      // Chuyển đổi sang DTO response
      const items = result.items.map(item =>
        plainToInstance(CustomGroupFormListItemDto, item, { excludeExtraneousValues: true })
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách nhóm trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      // Nếu lỗi liên quan đến trường sắp xếp không hợp lệ
      if (error.message && error.message.includes('Trường sắp xếp')) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          error.message,
          { sortBy: queryDto.sortBy }
        );
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.GROUP_FORM_FIND_FAILED,
        'Lỗi khi lấy danh sách nhóm trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy chi tiết nhóm trường tùy chỉnh theo ID
   * @param id ID của nhóm trường tùy chỉnh
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin chi tiết nhóm trường tùy chỉnh với các trường con
   */
  async findById(id: number, userId: number): Promise<CustomGroupFormResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết nhóm trường tùy chỉnh với ID: ${id}`);

      // Lấy thông tin nhóm trường tùy chỉnh theo ID và userId
      const existingGroupForm = await this.customGroupFormRepository.findByIdAndUserId(id, userId);

      // Kiểm tra xem nhóm trường tùy chỉnh có tồn tại không và thuộc về người dùng hiện tại
      this.validationHelper.validateGroupFormExists(existingGroupForm, id);

      // Sử dụng assertion để TypeScript biết rằng existingGroupForm không phải là null
      const groupForm = existingGroupForm as CustomGroupForm;

      // Lấy danh sách trường trong nhóm
      const groupFormFields = await this.customGroupFormFieldRepository.findByFormGroupId(id);

      // Tạo response DTO
      const responseDto = plainToInstance(CustomGroupFormResponseDto, groupForm, { excludeExtraneousValues: true });

      // Khởi tạo mảng fields
      const fields: CustomFieldDto[] = [];

      // Lấy thông tin chi tiết của từng trường
      for (const groupFormField of groupFormFields) {
        const customField = await this.customFieldRepository.findById(groupFormField.fieldId);

        if (customField) {
          // Tạo DTO cho trường tùy chỉnh
          const fieldDto = new CustomFieldDto();
          fieldDto.fieldId = customField.id;
          fieldDto.component = customField.component;
          // Ensure config has the correct structure
          fieldDto.config = customField.configJson || {
            id: customField.configId,
            label: customField.label,
            type: customField.type,
            required: customField.required
          };
          // Create a default grid config with required properties if none exists
          const gridConfig = groupFormField.gird || { i: 'default', x: 0, y: 0, w: 4, h: 2 };
          fieldDto.grid = gridConfig as GridConfigDto;
          // Ensure value has the correct structure
          fieldDto.value = {
            value: groupFormField.value?.value || ''
          };

          // Thêm vào danh sách trường
          fields.push(fieldDto);
        }
      }

      // Gán danh sách trường vào response
      responseDto.fields = fields;

      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết nhóm trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.GROUP_FORM_NOT_FOUND,
        'Lỗi khi lấy chi tiết nhóm trường tùy chỉnh',
      );
    }
  }

  /**
   * Cập nhật nhóm trường tùy chỉnh
   * @param id ID của nhóm trường tùy chỉnh cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật nhóm trường tùy chỉnh
   * @returns Thông tin nhóm trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async update(id: number, updateDto: UpdateCustomGroupFormDto): Promise<CustomGroupFormCreatedResponseDto> {
    try {
      this.logger.log(`Cập nhật nhóm trường tùy chỉnh với ID ${id}: ${JSON.stringify(updateDto)}`);

      // Kiểm tra xem userId có được cung cấp không
      this.validationHelper.validateUserProvided(updateDto.userId, true);

      // Kiểm tra xem nhóm trường tùy chỉnh có tồn tại không và thuộc về người dùng hiện tại
      const existingGroupForm = await this.customGroupFormRepository.findByIdAndUserId(id, updateDto.userId as number);
      this.validationHelper.validateGroupFormExists(existingGroupForm, id);



      // Cập nhật thông tin - Sau khi validateGroupFormExists, existingGroupForm không thể là null
      // Sử dụng assertion để TypeScript biết rằng existingGroupForm không phải là null
      const groupForm = existingGroupForm as CustomGroupForm;
      groupForm.label = updateDto.label;
      if (updateDto.userId !== undefined) {
        groupForm.userId = updateDto.userId;
      }



      // Lưu vào database
      const updatedGroupForm = await this.customGroupFormRepository.save(groupForm);

      // Chuyển đổi sang DTO response
      return plainToInstance(CustomGroupFormCreatedResponseDto, updatedGroupForm, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật nhóm trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.GROUP_FORM_UPDATE_FAILED,
        'Lỗi khi cập nhật nhóm trường tùy chỉnh',
      );
    }
  }

  /**
   * Xóa nhóm trường tùy chỉnh
   * @param id ID của nhóm trường tùy chỉnh cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo xóa thành công
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa nhóm trường tùy chỉnh với ID: ${id}`);

      // Kiểm tra xem nhóm trường tùy chỉnh có tồn tại không và thuộc về người dùng hiện tại
      const existingGroupForm = await this.customGroupFormRepository.findByIdAndUserId(id, userId);
      this.validationHelper.validateGroupFormExists(existingGroupForm, id);

      // Xóa các trường liên quan trong bảng custom_group_form_field
      await this.customGroupFormFieldRepository.delete({ formGroupId: id });

      // Xóa nhóm trường tùy chỉnh
      await this.customGroupFormRepository.delete(id);

      this.logger.log(`Đã xóa nhóm trường tùy chỉnh với ID: ${id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhóm trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.GROUP_FORM_DELETION_FAILED,
        'Lỗi khi xóa nhóm trường tùy chỉnh',
      );
    }
  }
}
