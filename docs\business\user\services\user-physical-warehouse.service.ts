import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PhysicalWarehouse } from '@modules/business/entities';
import { PhysicalWarehouseRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../helpers/validation.helper';
import { WarehouseTypeEnum } from '@modules/business/enums';
import {
  CreatePhysicalWarehouseDto,
  UpdatePhysicalWarehouseDto,
  PhysicalWarehouseResponseDto,
  QueryPhysicalWarehouseDto,
  BulkDeletePhysicalWarehouseDto,
  BulkDeletePhysicalWarehouseResponseDto,
} from '../dto/warehouse';
import { UserWarehouseService } from './user-warehouse.service';
import { PaginatedResult } from '@/common/response';

/**
 * Service xử lý logic nghiệp vụ cho kho vật lý của người dùng
 */
@Injectable()
export class UserPhysicalWarehouseService {
  private readonly logger = new Logger(UserPhysicalWarehouseService.name);

  constructor(
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly userWarehouseService: UserWarehouseService,
  ) {}

  /**
   * Tạo mới kho vật lý
   * @param createDto DTO chứa thông tin tạo kho vật lý mới
   * @returns Thông tin kho vật lý đã tạo
   */
  @Transactional()
  async createPhysicalWarehouse(
    createDto: CreatePhysicalWarehouseDto,
  ): Promise<PhysicalWarehouseResponseDto> {
    try {
      // Kiểm tra warehouse ID đã tồn tại chưa
      const warehouse = await this.validationHelper.validateWarehouseExists(
        createDto.warehouseId,
      );

      // Kiểm tra loại kho là kho vật lý
      if (warehouse.type !== WarehouseTypeEnum.PHYSICAL) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
          `Kho với ID ${createDto.warehouseId} không phải là kho vật lý`,
        );
      }

      // Tạo kho vật lý
      const physicalWarehouse = new PhysicalWarehouse();
      physicalWarehouse.warehouseId = createDto.warehouseId;
      physicalWarehouse.address = createDto.address;
      physicalWarehouse.capacity = createDto.capacity || 0;

      // Lưu kho vật lý vào cơ sở dữ liệu
      const savedPhysicalWarehouse =
        await this.physicalWarehouseRepository.createPhysicalWarehouse(
          physicalWarehouse,
        );

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(
        PhysicalWarehouseResponseDto,
        savedPhysicalWarehouse,
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo kho vật lý: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
        `Lỗi khi tạo kho vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin kho vật lý theo ID
   * @param warehouseId ID của kho
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của kho vật lý
   */
  async getPhysicalWarehouseById(
    warehouseId: number,
    userId: number,
  ): Promise<PhysicalWarehouseResponseDto> {
    try {
      // Kiểm tra kho tồn tại
      const warehouse =
        await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Kiểm tra loại kho là kho vật lý
      if (warehouse.type !== WarehouseTypeEnum.PHYSICAL) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
          `Kho với ID ${warehouseId} không phải là kho vật lý`,
        );
      }

      // Lấy thông tin đầy đủ của kho vật lý (kết hợp với thông tin kho chung và trường tùy chỉnh)
      const physicalWarehouseWithDetails =
        await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(
          warehouseId,
        );
      if (!physicalWarehouseWithDetails) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
          `Không tìm thấy thông tin kho vật lý với ID ${warehouseId}`,
        );
      }

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(
        PhysicalWarehouseResponseDto,
        physicalWarehouseWithDetails,
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin kho vật lý: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FETCH_FAILED,
        `Lỗi khi lấy thông tin kho vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin kho vật lý
   * @param warehouseId ID của kho
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng
   * @returns Thông tin kho vật lý đã cập nhật
   */
  @Transactional()
  async updatePhysicalWarehouse(
    warehouseId: number,
    updateDto: UpdatePhysicalWarehouseDto,
    userId: number,
  ): Promise<PhysicalWarehouseResponseDto> {
    try {
      // Kiểm tra kho tồn tại
      const warehouse =
        await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Kiểm tra loại kho là kho vật lý
      if (warehouse.type !== WarehouseTypeEnum.PHYSICAL) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
          `Kho với ID ${warehouseId} không phải là kho vật lý`,
        );
      }

      // Kiểm tra kho vật lý tồn tại
      const physicalWarehouse =
        await this.physicalWarehouseRepository.findByWarehouseId_user(warehouseId);
      if (!physicalWarehouse) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
          `Không tìm thấy thông tin kho vật lý với ID ${warehouseId}`,
        );
      }

      // Cập nhật thông tin kho vật lý
      const updatedPhysicalWarehouse =
        await this.physicalWarehouseRepository.updatePhysicalWarehouse(
          warehouseId,
          updateDto,
        );

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(
        PhysicalWarehouseResponseDto,
        updatedPhysicalWarehouse,
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật kho vật lý: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED,
        `Lỗi khi cập nhật kho vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Xóa kho vật lý
   * @param warehouseId ID của kho
   * @param userId ID của người dùng
   * @returns Thông báo kết quả xóa
   */
  @Transactional()
  async deletePhysicalWarehouse(
    warehouseId: number,
    userId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Kiểm tra kho tồn tại
      const warehouse =
        await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Kiểm tra loại kho là kho vật lý
      if (warehouse.type !== WarehouseTypeEnum.PHYSICAL) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
          `Kho với ID ${warehouseId} không phải là kho vật lý`,
        );
      }

      // Kiểm tra kho vật lý tồn tại
      const physicalWarehouse =
        await this.physicalWarehouseRepository.findByWarehouseId_user(warehouseId);
      if (!physicalWarehouse) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
          `Không tìm thấy thông tin kho vật lý với ID ${warehouseId}`,
        );
      }

      // Xóa kho vật lý
      const deletePhysicalResult =
        await this.physicalWarehouseRepository.deletePhysicalWarehouse(
          warehouseId,
        );
      if (!deletePhysicalResult) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
          `Không thể xóa kho vật lý với ID ${warehouseId}`,
        );
      }

      // Xóa kho chung thông qua UserWarehouseService
      await this.userWarehouseService.deleteWarehouse(warehouseId, userId);

      // Trả về kết quả
      return {
        success: true,
        message: 'Xóa kho vật lý thành công',
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa kho vật lý: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
        `Lỗi khi xóa kho vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách kho vật lý với phân trang và lọc
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách kho vật lý với phân trang
   */
  async getPhysicalWarehouses(
    queryDto: QueryPhysicalWarehouseDto,
  ): Promise<PaginatedResult<PhysicalWarehouseResponseDto>> {
    try {
      // Kiểm tra và xử lý trường sắp xếp
      const validSortFields = ['warehouseId', 'name', 'address', 'capacity', 'description', 'type'];
      if (queryDto.sortBy && !validSortFields.includes(queryDto.sortBy)) {
        // Nếu trường sắp xếp không hợp lệ, sử dụng trường mặc định
        queryDto.sortBy = 'warehouseId';
      }

      // Đảm bảo chỉ lấy kho của người dùng hiện tại
      if (!queryDto.userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_ACCESS_DENIED,
          `Không thể lấy danh sách kho vật lý: Thiếu thông tin người dùng`
        );
      }

      // Lấy danh sách kho vật lý từ repository
      const result = await this.physicalWarehouseRepository.findAll(queryDto);

      // Chuyển đổi các item sang DTO - Manual mapping để đảm bảo đúng
      const items = result.items.map((physicalWarehouse) => {
        const dto = new PhysicalWarehouseResponseDto();
        dto.id = physicalWarehouse.id;
        dto.warehouseId = physicalWarehouse.warehouseId;
        dto.name = physicalWarehouse.name;
        dto.description = physicalWarehouse.description;
        dto.type = physicalWarehouse.type;
        dto.address = physicalWarehouse.address;
        dto.capacity = physicalWarehouse.capacity;
        dto.customFields = physicalWarehouse.customFields || [];

        return dto;
      });

      // Trả về kết quả với phân trang
      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách kho vật lý: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FETCH_FAILED,
        `Lỗi khi lấy danh sách kho vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều kho vật lý
   * @param bulkDeleteDto DTO chứa danh sách ID kho cần xóa
   * @param userId ID của người dùng
   * @returns Kết quả xóa nhiều kho vật lý
   */
  @Transactional()
  async bulkDeletePhysicalWarehouses(
    bulkDeleteDto: BulkDeletePhysicalWarehouseDto,
    userId: number,
  ): Promise<BulkDeletePhysicalWarehouseResponseDto> {
    try {
      const { warehouseIds } = bulkDeleteDto;
      const results: any[] = [];
      let successCount = 0;
      let failureCount = 0;

      this.logger.log(
        `Bắt đầu xóa bulk ${warehouseIds.length} kho vật lý cho userId=${userId}`,
      );

      // Xử lý từng kho một để có thể báo cáo chi tiết
      for (const warehouseId of warehouseIds) {
        try {
          // Kiểm tra kho tồn tại
          const warehouse = await this.validationHelper.validateWarehouseExists(warehouseId);

          // Kiểm tra loại kho là kho vật lý
          if (warehouse.type !== WarehouseTypeEnum.PHYSICAL) {
            results.push({
              warehouseId,
              status: 'error',
              message: `Kho với ID ${warehouseId} không phải là kho vật lý`,
            });
            failureCount++;
            continue;
          }

          // Kiểm tra kho vật lý tồn tại
          const physicalWarehouse = await this.physicalWarehouseRepository.findByWarehouseId_user(warehouseId);
          if (!physicalWarehouse) {
            results.push({
              warehouseId,
              status: 'error',
              message: `Không tìm thấy thông tin kho vật lý với ID ${warehouseId}`,
            });
            failureCount++;
            continue;
          }

          // Xóa kho vật lý
          const deletePhysicalResult = await this.physicalWarehouseRepository.deletePhysicalWarehouse(warehouseId);
          if (!deletePhysicalResult) {
            results.push({
              warehouseId,
              status: 'error',
              message: `Không thể xóa kho vật lý với ID ${warehouseId}`,
            });
            failureCount++;
            continue;
          }

          // Xóa kho chung thông qua UserWarehouseService
          await this.userWarehouseService.deleteWarehouse(warehouseId, userId);

          results.push({
            warehouseId,
            status: 'success',
            message: 'Xóa kho vật lý thành công',
          });
          successCount++;

        } catch (error) {
          this.logger.error(
            `Lỗi khi xóa kho vật lý ${warehouseId}: ${error.message}`,
            error.stack,
          );

          results.push({
            warehouseId,
            status: 'error',
            message: error instanceof AppException ? error.message : `Lỗi khi xóa kho vật lý: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BulkDeletePhysicalWarehouseResponseDto = {
        totalRequested: warehouseIds.length,
        successCount,
        failureCount,
        results,
        message: `Xóa thành công ${successCount}/${warehouseIds.length} kho vật lý`,
      };

      this.logger.log(
        `Hoàn thành xóa bulk kho vật lý: ${successCount} thành công, ${failureCount} thất bại`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bulk kho vật lý: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
        `Lỗi khi xóa bulk kho vật lý: ${error.message}`,
      );
    }
  }
}
