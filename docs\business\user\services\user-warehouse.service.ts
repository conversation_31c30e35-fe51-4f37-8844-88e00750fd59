import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import {
  WarehouseRepository,
  PhysicalWarehouseRepository,
  VirtualWarehouseRepository,
  InventoryRepository,
  CustomFieldRepository
} from '../../repositories';
import { ValidationHelper } from '../helpers/validation.helper';
import { MetadataHelper } from '../helpers/metadata.helper';
import {
  CreateWarehouseDto,
  QueryWarehouseDto,
  UpdateWarehouseDto,
  WarehouseResponseDto,
} from '../dto/warehouse';
import { PaginatedResult } from '@/common/response';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * Service xử lý logic nghiệp vụ cho kho của người dùng
 */
@Injectable()
export class UserWarehouseService {
  private readonly logger = new Logger(UserWarehouseService.name);

  constructor(
    private readonly warehouseRepository: WarehouseRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly virtualWarehouseRepository: VirtualWarehouseRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly metadataHelper: MetadataHelper
  ) {}

  /**
   * Tạo kho mới
   * @param createWarehouseDto DTO chứa thông tin kho mới
   * @returns Thông tin kho đã tạo
   */
  @Transactional()
  async createWarehouse(createWarehouseDto: CreateWarehouseDto): Promise<WarehouseResponseDto> {
    try {
      // Kiểm tra dữ liệu đầu vào
      await this.validationHelper.validateCreateWarehouse(createWarehouseDto);

      // Xử lý custom fields nếu có
      let customFields: any[] = [];
      if (createWarehouseDto.customFields && createWarehouseDto.customFields.length > 0) {
        this.logger.log(`Xử lý ${createWarehouseDto.customFields.length} custom fields cho warehouse`);

        // Lấy danh sách ID custom fields
        const customFieldIds = this.metadataHelper.extractCustomFieldIds(createWarehouseDto.customFields);

        // Lấy thông tin chi tiết custom fields từ database
        customFields = await this.customFieldRepository.findByIds(customFieldIds);

        // Validate custom fields
        this.metadataHelper.validateCustomFieldInputs(createWarehouseDto.customFields, customFields);
      }

      // Tạo metadata cho warehouse
      const metadata = this.metadataHelper.buildMetadata(
        createWarehouseDto.customFields,
        customFields
      );

      // Tạo kho mới
      const warehouse = this.warehouseRepository.create({
        name: createWarehouseDto.name,
        description: createWarehouseDto.description,
        type: createWarehouseDto.type,
        metadata: metadata
      });

      // Lưu kho vào database
      const savedWarehouse = await this.warehouseRepository.save(warehouse);

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(WarehouseResponseDto, savedWarehouse, {
        excludeExtraneousValues: true
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
        `Lỗi khi tạo kho: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật thông tin kho
   * @param warehouseId ID của kho cần cập nhật
   * @param updateWarehouseDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng
   * @returns Thông tin kho đã cập nhật
   */
  @Transactional()
  async updateWarehouse(
    warehouseId: number,
    updateWarehouseDto: UpdateWarehouseDto,
    userId: number
  ): Promise<WarehouseResponseDto> {
    try {
      // Kiểm tra dữ liệu đầu vào
      await this.validationHelper.validateUpdateWarehouse(warehouseId, updateWarehouseDto);

      // Lấy thông tin kho hiện tại
      const warehouse = await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Xử lý custom fields nếu có
      if (updateWarehouseDto.customFields && updateWarehouseDto.customFields.length > 0) {
        this.logger.log(`Cập nhật ${updateWarehouseDto.customFields.length} custom fields cho warehouse`);

        // Lấy danh sách ID custom fields
        const customFieldIds = this.metadataHelper.extractCustomFieldIds(updateWarehouseDto.customFields);

        // Lấy thông tin chi tiết custom fields từ database
        const customFields = await this.customFieldRepository.findByIds(customFieldIds);

        // Validate custom fields
        this.metadataHelper.validateCustomFieldInputs(updateWarehouseDto.customFields, customFields);

        // Merge metadata mới với metadata hiện tại
        warehouse.metadata = this.metadataHelper.mergeMetadata(
          warehouse.metadata,
          updateWarehouseDto.customFields,
          customFields
        );
      }

      // Cập nhật thông tin kho
      if (updateWarehouseDto.name) {
        warehouse.name = updateWarehouseDto.name;
      }

      if (updateWarehouseDto.description !== undefined) {
        warehouse.description = updateWarehouseDto.description;
      }

      if (updateWarehouseDto.type) {
        warehouse.type = updateWarehouseDto.type;
      }

      // Lưu cập nhật vào database
      const updatedWarehouse = await this.warehouseRepository.save(warehouse);

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(WarehouseResponseDto, updatedWarehouse, {
        excludeExtraneousValues: true
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED,
        `Lỗi khi cập nhật kho: ${error.message}`
      );
    }
  }

  /**
   * Xóa kho
   * @param warehouseId ID của kho cần xóa
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteWarehouse(warehouseId: number, userId: number): Promise<void> {
    try {
      // Kiểm tra kho tồn tại
      const warehouse = await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Kiểm tra xem có inventory nào đang sử dụng warehouse này không
      const inventories = await this.inventoryRepository.find({
        where: { warehouseId }
      });

      if (inventories && inventories.length > 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_DELETION_FAILED,
          `Không thể xóa kho vì có ${inventories.length} sản phẩm đang được lưu trữ trong kho này`
        );
      }

      // Xóa warehouse_custom_field trước để tránh foreign key constraint
      try {
        await this.warehouseRepository.query(
          'DELETE FROM warehouse_custom_field WHERE warehouse_id = $1',
          [warehouseId]
        );
        this.logger.log(`Đã xóa các trường tùy chỉnh của kho ${warehouseId}`);
      } catch (customFieldError) {
        this.logger.warn(`Không thể xóa trường tùy chỉnh của kho ${warehouseId}: ${customFieldError.message}`);
        // Tiếp tục xóa kho ngay cả khi không xóa được custom fields
      }

      // Xóa physical_warehouse trước nếu là kho vật lý
      if (warehouse.type === WarehouseTypeEnum.PHYSICAL) {
        const physicalWarehouse = await this.physicalWarehouseRepository.findByWarehouseId_user(warehouseId);
        if (physicalWarehouse) {
          await this.physicalWarehouseRepository.delete({ warehouseId });
        }
      }

      // Xóa virtual_warehouse trước nếu là kho ảo
      if (warehouse.type === WarehouseTypeEnum.VIRTUAL) {
        const virtualWarehouse = await this.virtualWarehouseRepository.findByWarehouseId_user(warehouseId);
        if (virtualWarehouse) {
          await this.virtualWarehouseRepository.delete({ warehouseId });
        }
      }

      // Metadata sẽ được xóa cùng với warehouse, không cần xử lý riêng

      // Xóa kho
      await this.warehouseRepository.delete(warehouseId);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_DELETION_FAILED,
        `Lỗi khi xóa kho: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin kho theo ID
   * @param warehouseId ID của kho
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của kho
   */
  async getWarehouseById(warehouseId: number, userId: number): Promise<WarehouseResponseDto> {
    try {
      // Lấy thông tin kho thông qua validation helper
      // Validation helper sẽ gọi repository để kiểm tra và lấy thông tin kho
      const warehouse = await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(WarehouseResponseDto, warehouse, {
        excludeExtraneousValues: true
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FIND_FAILED,
        `Lỗi khi lấy thông tin kho: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách kho với phân trang và lọc
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách kho với phân trang
   */
  async getWarehouses(queryDto: QueryWarehouseDto): Promise<PaginatedResult<WarehouseResponseDto>> {
    try {
      // Kiểm tra và xử lý trường sắp xếp
      const validSortFields = ['warehouseId', 'name', 'type'];
      if (queryDto.sortBy && !validSortFields.includes(queryDto.sortBy)) {
        // Nếu trường sắp xếp không hợp lệ, sử dụng trường mặc định
        queryDto.sortBy = 'warehouseId';
      }

      // Đảm bảo chỉ lấy kho của người dùng hiện tại
      if (!queryDto.userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_ACCESS_DENIED,
          `Không thể lấy danh sách kho: Thiếu thông tin người dùng`
        );
      }

      // Lấy danh sách kho từ repository
      const result = await this.warehouseRepository.findAll(queryDto);

      // Chuyển đổi các item sang DTO
      const items = result.items.map(warehouse =>
        plainToInstance(WarehouseResponseDto, warehouse, {
          excludeExtraneousValues: true
        })
      );

      // Trả về kết quả với các item đã chuyển đổi
      return {
        items,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FIND_FAILED,
        `Lỗi khi lấy danh sách kho: ${error.message}`
      );
    }
  }


}
