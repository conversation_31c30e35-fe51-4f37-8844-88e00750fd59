import { Test, TestingModule } from '@nestjs/testing';
import { UserPhysicalWarehouseController } from '../../controllers/user-physical-warehouse.controller';
import { UserPhysicalWarehouseService } from '../../services/user-physical-warehouse.service';
import { BulkDeletePhysicalWarehouseDto, BulkDeletePhysicalWarehouseResponseDto } from '../../dto/warehouse';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

describe('UserPhysicalWarehouseController - Bulk Delete', () => {
  let controller: UserPhysicalWarehouseController;
  let service: UserPhysicalWarehouseService;

  const mockUser: JwtPayload = {
    id: 1,
    email: '<EMAIL>',
    iat: Date.now(),
    exp: Date.now() + 3600,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserPhysicalWarehouseController],
      providers: [
        {
          provide: UserPhysicalWarehouseService,
          useValue: {
            bulkDeletePhysicalWarehouses: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserPhysicalWarehouseController>(UserPhysicalWarehouseController);
    service = module.get<UserPhysicalWarehouseService>(UserPhysicalWarehouseService);
  });

  describe('bulkDeletePhysicalWarehouses', () => {
    it('nên xóa nhiều kho vật lý thành công', async () => {
      // Arrange
      const bulkDeleteDto: BulkDeletePhysicalWarehouseDto = {
        warehouseIds: [1, 2, 3],
      };

      const expectedResponse: BulkDeletePhysicalWarehouseResponseDto = {
        totalRequested: 3,
        successCount: 3,
        failureCount: 0,
        results: [
          { warehouseId: 1, status: 'success', message: 'Xóa kho vật lý thành công' },
          { warehouseId: 2, status: 'success', message: 'Xóa kho vật lý thành công' },
          { warehouseId: 3, status: 'success', message: 'Xóa kho vật lý thành công' },
        ],
        message: 'Xóa thành công 3/3 kho vật lý',
      };

      jest.spyOn(service, 'bulkDeletePhysicalWarehouses').mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.bulkDeletePhysicalWarehouses(bulkDeleteDto, mockUser);

      // Assert
      expect(service.bulkDeletePhysicalWarehouses).toHaveBeenCalledWith(bulkDeleteDto, mockUser.id);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe(expectedResponse.message);
    });

    it('nên xử lý trường hợp một số kho xóa thất bại', async () => {
      // Arrange
      const bulkDeleteDto: BulkDeletePhysicalWarehouseDto = {
        warehouseIds: [1, 2, 999],
      };

      const expectedResponse: BulkDeletePhysicalWarehouseResponseDto = {
        totalRequested: 3,
        successCount: 2,
        failureCount: 1,
        results: [
          { warehouseId: 1, status: 'success', message: 'Xóa kho vật lý thành công' },
          { warehouseId: 2, status: 'success', message: 'Xóa kho vật lý thành công' },
          { warehouseId: 999, status: 'error', message: 'Không tìm thấy kho với ID 999' },
        ],
        message: 'Xóa thành công 2/3 kho vật lý',
      };

      jest.spyOn(service, 'bulkDeletePhysicalWarehouses').mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.bulkDeletePhysicalWarehouses(bulkDeleteDto, mockUser);

      // Assert
      expect(service.bulkDeletePhysicalWarehouses).toHaveBeenCalledWith(bulkDeleteDto, mockUser.id);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe(expectedResponse.message);
    });

    it('nên ném lỗi khi service ném AppException', async () => {
      // Arrange
      const bulkDeleteDto: BulkDeletePhysicalWarehouseDto = {
        warehouseIds: [1, 2, 3],
      };

      const error = new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
        'Lỗi khi xóa bulk kho vật lý'
      );

      jest.spyOn(service, 'bulkDeletePhysicalWarehouses').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.bulkDeletePhysicalWarehouses(bulkDeleteDto, mockUser))
        .rejects.toThrow(AppException);
      expect(service.bulkDeletePhysicalWarehouses).toHaveBeenCalledWith(bulkDeleteDto, mockUser.id);
    });

    it('nên validate DTO đầu vào', async () => {
      // Arrange
      const invalidDto = {
        warehouseIds: [], // Empty array should fail validation
      } as BulkDeletePhysicalWarehouseDto;

      // Act & Assert
      // Note: Validation sẽ được xử lý bởi NestJS validation pipe
      // Test này chỉ để đảm bảo controller nhận đúng DTO type
      expect(invalidDto.warehouseIds).toEqual([]);
    });
  });
});
