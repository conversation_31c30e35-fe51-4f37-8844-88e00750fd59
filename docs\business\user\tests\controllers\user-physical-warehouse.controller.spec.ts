import { Test, TestingModule } from '@nestjs/testing';
import { UserPhysicalWarehouseController } from '../../controllers/user-physical-warehouse.controller';
import { UserPhysicalWarehouseService } from '../../services/user-physical-warehouse.service';
import { CreatePhysicalWarehouseDto, UpdatePhysicalWarehouseDto, QueryPhysicalWarehouseDto } from '../../dto/warehouse';
import { PhysicalWarehouseResponseDto } from '../../dto/warehouse/physical-warehouse-response.dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { PaginatedResult } from '@common/response';

describe('UserPhysicalWarehouseController', () => {
  let controller: UserPhysicalWarehouseController;
  let service: UserPhysicalWarehouseService;

  // Mock data
  const mockPhysicalWarehouseResponse: PhysicalWarehouseResponseDto = {
    warehouseId: 1,
    name: 'Kho hàng 1',
    description: 'Mô tả kho hàng 1',
    type: WarehouseTypeEnum.PHYSICAL,
    address: '123 Đường ABC, Quận 1, TP.HCM',
    capacity: 1000,
    customFields: [
      {
        warehouseId: 1,
        fieldId: 1,
        value: { value: 'Giá trị 1' },
      },
    ],
  };

  const mockPhysicalWarehouseResponseList: PhysicalWarehouseResponseDto[] = [
    mockPhysicalWarehouseResponse,
    {
      warehouseId: 2,
      name: 'Kho hàng 2',
      description: 'Mô tả kho hàng 2',
      type: WarehouseTypeEnum.PHYSICAL,
      address: '456 Đường XYZ, Quận 2, TP.HCM',
      capacity: 2000,
    },
  ];

  const mockPaginatedResult: PaginatedResult<PhysicalWarehouseResponseDto> = {
    items: mockPhysicalWarehouseResponseList,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserPhysicalWarehouseController],
      providers: [
        {
          provide: UserPhysicalWarehouseService,
          useValue: {
            createPhysicalWarehouse: jest.fn(),
            updatePhysicalWarehouse: jest.fn(),
            getPhysicalWarehouseById: jest.fn(),
            getPhysicalWarehouses: jest.fn(),
            deletePhysicalWarehouse: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserPhysicalWarehouseController>(UserPhysicalWarehouseController);
    service = module.get<UserPhysicalWarehouseService>(UserPhysicalWarehouseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPhysicalWarehouse', () => {
    it('nên tạo kho vật lý mới thành công', async () => {
      // Arrange
      const createDto: CreatePhysicalWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      };

      jest.spyOn(service, 'createPhysicalWarehouse').mockResolvedValue(mockPhysicalWarehouseResponse);

      // Act
      const result = await controller.createPhysicalWarehouse(createDto);

      // Assert
      expect(service.createPhysicalWarehouse).toHaveBeenCalledWith(createDto);
      expect(result.data).toEqual(mockPhysicalWarehouseResponse);
      expect(result.message).toBe('Tạo kho vật lý thành công');
    });

    it('nên ném lỗi khi tạo kho vật lý thất bại', async () => {
      // Arrange
      const createDto: CreatePhysicalWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.PHYSICAL_WAREHOUSE_CREATION_FAILED, 'Lỗi khi tạo kho vật lý');

      jest.spyOn(service, 'createPhysicalWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createPhysicalWarehouse(createDto)).rejects.toThrow(AppException);
      expect(service.createPhysicalWarehouse).toHaveBeenCalledWith(createDto);
    });
  });

  describe('updatePhysicalWarehouse', () => {
    it('nên cập nhật kho vật lý thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdatePhysicalWarehouseDto = {
        address: '123 Đường ABC (đã cập nhật), Quận 1, TP.HCM',
        capacity: 1500,
      };

      jest.spyOn(service, 'updatePhysicalWarehouse').mockResolvedValue(mockPhysicalWarehouseResponse);

      // Act
      const result = await controller.updatePhysicalWarehouse(warehouseId, updateDto);

      // Assert
      expect(service.updatePhysicalWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
      expect(result.data).toEqual(mockPhysicalWarehouseResponse);
      expect(result.message).toBe('Cập nhật kho vật lý thành công');
    });

    it('nên ném lỗi khi cập nhật kho vật lý thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdatePhysicalWarehouseDto = {
        address: '123 Đường ABC (đã cập nhật), Quận 1, TP.HCM',
        capacity: 1500,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.PHYSICAL_WAREHOUSE_UPDATE_FAILED, 'Lỗi khi cập nhật kho vật lý');

      jest.spyOn(service, 'updatePhysicalWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.updatePhysicalWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(service.updatePhysicalWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
    });
  });

  describe('getPhysicalWarehouseById', () => {
    it('nên lấy thông tin kho vật lý theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(service, 'getPhysicalWarehouseById').mockResolvedValue(mockPhysicalWarehouseResponse);

      // Act
      const result = await controller.getPhysicalWarehouseById(warehouseId);

      // Assert
      expect(service.getPhysicalWarehouseById).toHaveBeenCalledWith(warehouseId);
      expect(result.data).toEqual(mockPhysicalWarehouseResponse);
      expect(result.message).toBe('Lấy thông tin kho vật lý thành công');
    });

    it('nên ném lỗi khi lấy thông tin kho vật lý thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.PHYSICAL_WAREHOUSE_NOT_FOUND, 'Kho vật lý không tồn tại');

      jest.spyOn(service, 'getPhysicalWarehouseById').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getPhysicalWarehouseById(warehouseId)).rejects.toThrow(AppException);
      expect(service.getPhysicalWarehouseById).toHaveBeenCalledWith(warehouseId);
    });
  });

  describe('getPhysicalWarehouses', () => {
    it('nên lấy danh sách kho vật lý với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryPhysicalWarehouseDto = {
        page: 1,
        limit: 10,
        search: 'kho',
        sortBy: 'name',
        sortDirection: 'ASC',
      };

      jest.spyOn(service, 'getPhysicalWarehouses').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getPhysicalWarehouses(queryDto);

      // Assert
      expect(service.getPhysicalWarehouses).toHaveBeenCalledWith(queryDto);
      expect(result.data).toEqual(mockPaginatedResult);
      expect(result.message).toBe('Lấy danh sách kho vật lý thành công');
    });

    it('nên ném lỗi khi lấy danh sách kho vật lý thất bại', async () => {
      // Arrange
      const queryDto: QueryPhysicalWarehouseDto = {
        page: 1,
        limit: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.PHYSICAL_WAREHOUSE_FETCH_FAILED, 'Lỗi khi lấy danh sách kho vật lý');

      jest.spyOn(service, 'getPhysicalWarehouses').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getPhysicalWarehouses(queryDto)).rejects.toThrow(AppException);
      expect(service.getPhysicalWarehouses).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('deletePhysicalWarehouse', () => {
    it('nên xóa kho vật lý thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(service, 'deletePhysicalWarehouse').mockResolvedValue(undefined);

      // Act
      const result = await controller.deletePhysicalWarehouse(warehouseId);

      // Assert
      expect(service.deletePhysicalWarehouse).toHaveBeenCalledWith(warehouseId);
      expect(result.message).toBe('Xóa kho vật lý thành công');
    });

    it('nên ném lỗi khi xóa kho vật lý thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.PHYSICAL_WAREHOUSE_DELETE_FAILED, 'Lỗi khi xóa kho vật lý');

      jest.spyOn(service, 'deletePhysicalWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deletePhysicalWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(service.deletePhysicalWarehouse).toHaveBeenCalledWith(warehouseId);
    });
  });
});
