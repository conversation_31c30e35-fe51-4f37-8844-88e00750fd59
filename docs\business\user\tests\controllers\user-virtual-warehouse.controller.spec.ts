import { Test, TestingModule } from '@nestjs/testing';
import { UserVirtualWarehouseController } from '../../controllers/user-virtual-warehouse.controller';
import { UserVirtualWarehouseService } from '../../services/user-virtual-warehouse.service';
import { CreateVirtualWarehouseDto, UpdateVirtualWarehouseDto, QueryVirtualWarehouseDto } from '../../dto/warehouse';
import { VirtualWarehouseResponseDto } from '../../dto/warehouse/virtual-warehouse-response.dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { PaginatedResult } from '@common/response';

describe('UserVirtualWarehouseController', () => {
  let controller: UserVirtualWarehouseController;
  let service: UserVirtualWarehouseService;

  // Mock data
  const mockVirtualWarehouseResponse: VirtualWarehouseResponseDto = {
    warehouseId: 1,
    name: 'Kho ảo 1',
    description: 'Mô tả kho ảo 1',
    type: WarehouseTypeEnum.VIRTUAL,
    associatedSystem: 'SAP ERP',
    purpose: 'Quản lý hàng hóa trực tuyến',
    customFields: [
      {
        warehouseId: 1,
        fieldId: 1,
        value: { value: 'Giá trị 1' },
      },
    ],
  };

  const mockVirtualWarehouseResponseList: VirtualWarehouseResponseDto[] = [
    mockVirtualWarehouseResponse,
    {
      warehouseId: 2,
      name: 'Kho ảo 2',
      description: 'Mô tả kho ảo 2',
      type: WarehouseTypeEnum.VIRTUAL,
      associatedSystem: 'Oracle ERP',
      purpose: 'Quản lý hàng hóa offline',
    },
  ];

  const mockPaginatedResult: PaginatedResult<VirtualWarehouseResponseDto> = {
    items: mockVirtualWarehouseResponseList,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserVirtualWarehouseController],
      providers: [
        {
          provide: UserVirtualWarehouseService,
          useValue: {
            createVirtualWarehouse: jest.fn(),
            updateVirtualWarehouse: jest.fn(),
            getVirtualWarehouseById: jest.fn(),
            getVirtualWarehouses: jest.fn(),
            deleteVirtualWarehouse: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserVirtualWarehouseController>(UserVirtualWarehouseController);
    service = module.get<UserVirtualWarehouseService>(UserVirtualWarehouseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createVirtualWarehouse', () => {
    it('nên tạo kho ảo mới thành công', async () => {
      // Arrange
      const createDto: CreateVirtualWarehouseDto = {
        name: 'Kho ảo mới',
        description: 'Mô tả kho ảo mới',
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      };

      jest.spyOn(service, 'createVirtualWarehouse').mockResolvedValue(mockVirtualWarehouseResponse);

      // Act
      const result = await controller.createVirtualWarehouse(createDto);

      // Assert
      expect(service.createVirtualWarehouse).toHaveBeenCalledWith(createDto);
      expect(result.data).toEqual(mockVirtualWarehouseResponse);
      expect(result.message).toBe('Tạo kho ảo thành công');
    });

    it('nên ném lỗi khi tạo kho ảo thất bại', async () => {
      // Arrange
      const createDto: CreateVirtualWarehouseDto = {
        name: 'Kho ảo mới',
        description: 'Mô tả kho ảo mới',
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.VIRTUAL_WAREHOUSE_CREATION_FAILED, 'Lỗi khi tạo kho ảo');

      jest.spyOn(service, 'createVirtualWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createVirtualWarehouse(createDto)).rejects.toThrow(AppException);
      expect(service.createVirtualWarehouse).toHaveBeenCalledWith(createDto);
    });
  });

  describe('updateVirtualWarehouse', () => {
    it('nên cập nhật kho ảo thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateVirtualWarehouseDto = {
        associatedSystem: 'SAP ERP Updated',
        purpose: 'Quản lý hàng hóa trực tuyến (đã cập nhật)',
      };

      jest.spyOn(service, 'updateVirtualWarehouse').mockResolvedValue(mockVirtualWarehouseResponse);

      // Act
      const result = await controller.updateVirtualWarehouse(warehouseId, updateDto);

      // Assert
      expect(service.updateVirtualWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
      expect(result.data).toEqual(mockVirtualWarehouseResponse);
      expect(result.message).toBe('Cập nhật kho ảo thành công');
    });

    it('nên ném lỗi khi cập nhật kho ảo thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateVirtualWarehouseDto = {
        associatedSystem: 'SAP ERP Updated',
        purpose: 'Quản lý hàng hóa trực tuyến (đã cập nhật)',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.VIRTUAL_WAREHOUSE_UPDATE_FAILED, 'Lỗi khi cập nhật kho ảo');

      jest.spyOn(service, 'updateVirtualWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.updateVirtualWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(service.updateVirtualWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
    });
  });

  describe('getVirtualWarehouseById', () => {
    it('nên lấy thông tin kho ảo theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(service, 'getVirtualWarehouseById').mockResolvedValue(mockVirtualWarehouseResponse);

      // Act
      const result = await controller.getVirtualWarehouseById(warehouseId);

      // Assert
      expect(service.getVirtualWarehouseById).toHaveBeenCalledWith(warehouseId);
      expect(result.data).toEqual(mockVirtualWarehouseResponse);
      expect(result.message).toBe('Lấy thông tin kho ảo thành công');
    });

    it('nên ném lỗi khi lấy thông tin kho ảo thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.VIRTUAL_WAREHOUSE_NOT_FOUND, 'Kho ảo không tồn tại');

      jest.spyOn(service, 'getVirtualWarehouseById').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getVirtualWarehouseById(warehouseId)).rejects.toThrow(AppException);
      expect(service.getVirtualWarehouseById).toHaveBeenCalledWith(warehouseId);
    });
  });

  describe('getVirtualWarehouses', () => {
    it('nên lấy danh sách kho ảo với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryVirtualWarehouseDto = {
        page: 1,
        limit: 10,
        search: 'kho',
        sortBy: 'name',
        sortDirection: 'ASC',
      };

      jest.spyOn(service, 'getVirtualWarehouses').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getVirtualWarehouses(queryDto);

      // Assert
      expect(service.getVirtualWarehouses).toHaveBeenCalledWith(queryDto);
      expect(result.data).toEqual(mockPaginatedResult);
      expect(result.message).toBe('Lấy danh sách kho ảo thành công');
    });

    it('nên ném lỗi khi lấy danh sách kho ảo thất bại', async () => {
      // Arrange
      const queryDto: QueryVirtualWarehouseDto = {
        page: 1,
        limit: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.VIRTUAL_WAREHOUSE_FETCH_FAILED, 'Lỗi khi lấy danh sách kho ảo');

      jest.spyOn(service, 'getVirtualWarehouses').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getVirtualWarehouses(queryDto)).rejects.toThrow(AppException);
      expect(service.getVirtualWarehouses).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('deleteVirtualWarehouse', () => {
    it('nên xóa kho ảo thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(service, 'deleteVirtualWarehouse').mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteVirtualWarehouse(warehouseId);

      // Assert
      expect(service.deleteVirtualWarehouse).toHaveBeenCalledWith(warehouseId);
      expect(result.message).toBe('Xóa kho ảo thành công');
    });

    it('nên ném lỗi khi xóa kho ảo thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.VIRTUAL_WAREHOUSE_DELETE_FAILED, 'Lỗi khi xóa kho ảo');

      jest.spyOn(service, 'deleteVirtualWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deleteVirtualWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(service.deleteVirtualWarehouse).toHaveBeenCalledWith(warehouseId);
    });
  });
});
