import { Test, TestingModule } from '@nestjs/testing';
import { UserWarehouseCustomFieldController } from '../../controllers/user-warehouse-custom-field.controller';
import { UserWarehouseCustomFieldService } from '../../services/user-warehouse-custom-field.service';
import { CreateWarehouseCustomFieldDto, UpdateWarehouseCustomFieldDto } from '../../dto/warehouse';
import { WarehouseCustomFieldResponseDto } from '../../dto/warehouse/warehouse-custom-field-response.dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

describe('UserWarehouseCustomFieldController', () => {
  let controller: UserWarehouseCustomFieldController;
  let service: UserWarehouseCustomFieldService;

  // Mock data
  const mockCustomFieldResponse: WarehouseCustomFieldResponseDto = {
    warehouseId: 1,
    fieldId: 1,
    value: { value: 'Giá trị 1' },
  };

  const mockCustomFieldResponseList: WarehouseCustomFieldResponseDto[] = [
    mockCustomFieldResponse,
    {
      warehouseId: 1,
      fieldId: 2,
      value: { value: 'Giá trị 2' },
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserWarehouseCustomFieldController],
      providers: [
        {
          provide: UserWarehouseCustomFieldService,
          useValue: {
            createWarehouseCustomField: jest.fn(),
            updateWarehouseCustomField: jest.fn(),
            getWarehouseCustomFieldById: jest.fn(),
            getWarehouseCustomFields: jest.fn(),
            deleteWarehouseCustomField: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserWarehouseCustomFieldController>(UserWarehouseCustomFieldController);
    service = module.get<UserWarehouseCustomFieldService>(UserWarehouseCustomFieldService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createWarehouseCustomField', () => {
    it('nên tạo trường tùy chỉnh mới thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const createDto: CreateWarehouseCustomFieldDto = {
        fieldId: 3,
        value: { value: 'Giá trị mới' },
      };

      jest.spyOn(service, 'createWarehouseCustomField').mockResolvedValue(mockCustomFieldResponse);

      // Act
      const result = await controller.createWarehouseCustomField(warehouseId, createDto);

      // Assert
      expect(service.createWarehouseCustomField).toHaveBeenCalledWith(warehouseId, createDto);
      expect(result.data).toEqual(mockCustomFieldResponse);
      expect(result.message).toBe('Tạo trường tùy chỉnh cho kho thành công');
    });

    it('nên ném lỗi khi tạo trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const createDto: CreateWarehouseCustomFieldDto = {
        fieldId: 3,
        value: { value: 'Giá trị mới' },
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_CREATION_FAILED, 'Lỗi khi tạo trường tùy chỉnh');

      jest.spyOn(service, 'createWarehouseCustomField').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createWarehouseCustomField(warehouseId, createDto)).rejects.toThrow(AppException);
      expect(service.createWarehouseCustomField).toHaveBeenCalledWith(warehouseId, createDto);
    });
  });

  describe('updateWarehouseCustomField', () => {
    it('nên cập nhật trường tùy chỉnh thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const updateDto: UpdateWarehouseCustomFieldDto = {
        value: { value: 'Giá trị đã cập nhật' },
      };

      jest.spyOn(service, 'updateWarehouseCustomField').mockResolvedValue(mockCustomFieldResponse);

      // Act
      const result = await controller.updateWarehouseCustomField(warehouseId, fieldId, updateDto);

      // Assert
      expect(service.updateWarehouseCustomField).toHaveBeenCalledWith(warehouseId, fieldId, updateDto);
      expect(result.data).toEqual(mockCustomFieldResponse);
      expect(result.message).toBe('Cập nhật trường tùy chỉnh cho kho thành công');
    });

    it('nên ném lỗi khi cập nhật trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const updateDto: UpdateWarehouseCustomFieldDto = {
        value: { value: 'Giá trị đã cập nhật' },
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_UPDATE_FAILED, 'Lỗi khi cập nhật trường tùy chỉnh');

      jest.spyOn(service, 'updateWarehouseCustomField').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.updateWarehouseCustomField(warehouseId, fieldId, updateDto)).rejects.toThrow(AppException);
      expect(service.updateWarehouseCustomField).toHaveBeenCalledWith(warehouseId, fieldId, updateDto);
    });
  });

  describe('getWarehouseCustomFieldById', () => {
    it('nên lấy thông tin trường tùy chỉnh theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;

      jest.spyOn(service, 'getWarehouseCustomFieldById').mockResolvedValue(mockCustomFieldResponse);

      // Act
      const result = await controller.getWarehouseCustomFieldById(warehouseId, fieldId);

      // Assert
      expect(service.getWarehouseCustomFieldById).toHaveBeenCalledWith(warehouseId, fieldId);
      expect(result.data).toEqual(mockCustomFieldResponse);
      expect(result.message).toBe('Lấy thông tin trường tùy chỉnh của kho thành công');
    });

    it('nên ném lỗi khi lấy thông tin trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND, 'Trường tùy chỉnh không tồn tại');

      jest.spyOn(service, 'getWarehouseCustomFieldById').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getWarehouseCustomFieldById(warehouseId, fieldId)).rejects.toThrow(AppException);
      expect(service.getWarehouseCustomFieldById).toHaveBeenCalledWith(warehouseId, fieldId);
    });
  });

  describe('getWarehouseCustomFields', () => {
    it('nên lấy danh sách trường tùy chỉnh thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(service, 'getWarehouseCustomFields').mockResolvedValue(mockCustomFieldResponseList);

      // Act
      const result = await controller.getWarehouseCustomFields(warehouseId);

      // Assert
      expect(service.getWarehouseCustomFields).toHaveBeenCalledWith(warehouseId);
      expect(result.data).toEqual(mockCustomFieldResponseList);
      expect(result.message).toBe('Lấy danh sách trường tùy chỉnh của kho thành công');
    });

    it('nên ném lỗi khi lấy danh sách trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FETCH_FAILED, 'Lỗi khi lấy danh sách trường tùy chỉnh');

      jest.spyOn(service, 'getWarehouseCustomFields').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getWarehouseCustomFields(warehouseId)).rejects.toThrow(AppException);
      expect(service.getWarehouseCustomFields).toHaveBeenCalledWith(warehouseId);
    });
  });

  describe('deleteWarehouseCustomField', () => {
    it('nên xóa trường tùy chỉnh thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;

      jest.spyOn(service, 'deleteWarehouseCustomField').mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteWarehouseCustomField(warehouseId, fieldId);

      // Assert
      expect(service.deleteWarehouseCustomField).toHaveBeenCalledWith(warehouseId, fieldId);
      expect(result.message).toBe('Xóa trường tùy chỉnh của kho thành công');
    });

    it('nên ném lỗi khi xóa trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_DELETE_FAILED, 'Lỗi khi xóa trường tùy chỉnh');

      jest.spyOn(service, 'deleteWarehouseCustomField').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deleteWarehouseCustomField(warehouseId, fieldId)).rejects.toThrow(AppException);
      expect(service.deleteWarehouseCustomField).toHaveBeenCalledWith(warehouseId, fieldId);
    });
  });
});
