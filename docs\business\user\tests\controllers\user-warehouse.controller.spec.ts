import { Test, TestingModule } from '@nestjs/testing';
import { UserWarehouseController } from '../../controllers/user-warehouse.controller';
import { UserWarehouseService } from '../../services/user-warehouse.service';
import { CreateWarehouseDto, UpdateWarehouseDto, QueryWarehouseDto } from '../../dto/warehouse';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { PaginatedResult } from '@common/response';

describe('UserWarehouseController', () => {
  let controller: UserWarehouseController;
  let service: UserWarehouseService;

  // Mock data
  const mockWarehouseResponse: WarehouseResponseDto = {
    warehouseId: 1,
    name: 'Kho hàng 1',
    description: '<PERSON><PERSON> tả kho hàng 1',
    type: WarehouseTypeEnum.PHYSICAL,
  };

  const mockWarehouseResponseList: WarehouseResponseDto[] = [
    mockWarehouseResponse,
    {
      warehouseId: 2,
      name: 'Kho hàng 2',
      description: 'Mô tả kho hàng 2',
      type: WarehouseTypeEnum.VIRTUAL,
    },
  ];

  const mockPaginatedResult: PaginatedResult<WarehouseResponseDto> = {
    items: mockWarehouseResponseList,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserWarehouseController],
      providers: [
        {
          provide: UserWarehouseService,
          useValue: {
            createWarehouse: jest.fn(),
            updateWarehouse: jest.fn(),
            getWarehouseById: jest.fn(),
            getWarehouses: jest.fn(),
            deleteWarehouse: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserWarehouseController>(UserWarehouseController);
    service = module.get<UserWarehouseService>(UserWarehouseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createWarehouse', () => {
    it('nên tạo kho mới thành công', async () => {
      // Arrange
      const createDto: CreateWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      };

      jest.spyOn(service, 'createWarehouse').mockResolvedValue(mockWarehouseResponse);

      // Act
      const result = await controller.createWarehouse(createDto);

      // Assert
      expect(service.createWarehouse).toHaveBeenCalledWith(createDto);
      expect(result.data).toEqual(mockWarehouseResponse);
      expect(result.message).toBe('Tạo kho thành công');
    });

    it('nên ném lỗi khi tạo kho thất bại', async () => {
      // Arrange
      const createDto: CreateWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED, 'Lỗi khi tạo kho');

      jest.spyOn(service, 'createWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createWarehouse(createDto)).rejects.toThrow(AppException);
      expect(service.createWarehouse).toHaveBeenCalledWith(createDto);
    });
  });

  describe('updateWarehouse', () => {
    it('nên cập nhật kho thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateWarehouseDto = {
        name: 'Kho hàng đã cập nhật',
        description: 'Mô tả đã cập nhật',
      };

      jest.spyOn(service, 'updateWarehouse').mockResolvedValue(mockWarehouseResponse);

      // Act
      const result = await controller.updateWarehouse(warehouseId, updateDto);

      // Assert
      expect(service.updateWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
      expect(result.data).toEqual(mockWarehouseResponse);
      expect(result.message).toBe('Cập nhật kho thành công');
    });

    it('nên ném lỗi khi cập nhật kho thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateWarehouseDto = {
        name: 'Kho hàng đã cập nhật',
        description: 'Mô tả đã cập nhật',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED, 'Lỗi khi cập nhật kho');

      jest.spyOn(service, 'updateWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.updateWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(service.updateWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
    });
  });

  describe('getWarehouseById', () => {
    it('nên lấy thông tin kho theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(service, 'getWarehouseById').mockResolvedValue(mockWarehouseResponse);

      // Act
      const result = await controller.getWarehouseById(warehouseId);

      // Assert
      expect(service.getWarehouseById).toHaveBeenCalledWith(warehouseId);
      expect(result.data).toEqual(mockWarehouseResponse);
      expect(result.message).toBe('Lấy thông tin kho thành công');
    });

    it('nên ném lỗi khi lấy thông tin kho thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(service, 'getWarehouseById').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getWarehouseById(warehouseId)).rejects.toThrow(AppException);
      expect(service.getWarehouseById).toHaveBeenCalledWith(warehouseId);
    });
  });

  describe('getWarehouses', () => {
    it('nên lấy danh sách kho với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryWarehouseDto = {
        page: 1,
        limit: 10,
        type: WarehouseTypeEnum.PHYSICAL,
      };

      jest.spyOn(service, 'getWarehouses').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getWarehouses(queryDto);

      // Assert
      expect(service.getWarehouses).toHaveBeenCalledWith(queryDto);
      expect(result.data).toEqual(mockPaginatedResult);
      expect(result.message).toBe('Lấy danh sách kho thành công');
    });

    it('nên ném lỗi khi lấy danh sách kho thất bại', async () => {
      // Arrange
      const queryDto: QueryWarehouseDto = {
        page: 1,
        limit: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_FETCH_FAILED, 'Lỗi khi lấy danh sách kho');

      jest.spyOn(service, 'getWarehouses').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getWarehouses(queryDto)).rejects.toThrow(AppException);
      expect(service.getWarehouses).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('deleteWarehouse', () => {
    it('nên xóa kho thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(service, 'deleteWarehouse').mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteWarehouse(warehouseId);

      // Assert
      expect(service.deleteWarehouse).toHaveBeenCalledWith(warehouseId);
      expect(result.message).toBe('Xóa kho thành công');
    });

    it('nên ném lỗi khi xóa kho thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED, 'Lỗi khi xóa kho');

      jest.spyOn(service, 'deleteWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deleteWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(service.deleteWarehouse).toHaveBeenCalledWith(warehouseId);
    });
  });
});
