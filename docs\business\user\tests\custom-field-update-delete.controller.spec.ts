import { Test, TestingModule } from '@nestjs/testing';
import { CustomFieldController } from '../controllers';
import { CustomFieldService } from '../services';
import { UpdateCustomFieldDto, CustomFieldResponseDto, ComponentListResponseDto } from '../dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { EntityStatusEnum } from '@modules/business/enums';
import { JwtUtilService } from '@modules/auth/guards/jwt.util';

describe('CustomFieldController - Update, Delete, Components', () => {
  let controller: CustomFieldController;
  let service: CustomFieldService;

  const mockCustomFieldService = {
    update: jest.fn(),
    delete: jest.fn(),
    getComponents: jest.fn(),
  };

  const mockJwtUtilService = {
    verify: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomFieldController],
      providers: [
        {
          provide: CustomFieldService,
          useValue: mockCustomFieldService,
        },
        {
          provide: JwtUtilService,
          useValue: mockJwtUtilService,
        },
      ],
    }).compile();

    controller = module.get<CustomFieldController>(CustomFieldController);
    service = module.get<CustomFieldService>(CustomFieldService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('update', () => {
    it('should update a custom field and return success response', async () => {
      // Arrange
      const id = 1;
      const updateDto: UpdateCustomFieldDto = {
        label: 'Họ và tên đầy đủ',
        configJson: {
          validation: { minLength: 5, maxLength: 60, pattern: '^[a-zA-Z0-9 ]*$' },
          placeholder: 'Nhập họ và tên đầy đủ',
          variant: 'outlined',
          size: 'small',
        },
      };

      const expectedResponse: CustomFieldResponseDto = {
        id: 1,
        component: 'Text Input',
        configId: 'text-input',
        label: 'Họ và tên đầy đủ',
        type: 'text',
        required: true,
        configJson: {
          validation: { minLength: 5, maxLength: 60, pattern: '^[a-zA-Z0-9 ]*$' },
          placeholder: 'Nhập họ và tên đầy đủ',
          variant: 'outlined',
          size: 'small',
        },
        userId: 1001,
        employeeId: null,
        status: EntityStatusEnum.PENDING,
        createAt: 1741708800000,
      };

      mockCustomFieldService.update.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.update(id, updateDto);

      // Assert
      expect(service.update).toHaveBeenCalledWith(id, updateDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe('Cập nhật trường tùy chỉnh thành công');
    });
  });

  describe('delete', () => {
    it('should delete a custom field and return success response', async () => {
      // Arrange
      const id = 1;
      const userId = 1001;
      mockCustomFieldService.delete.mockResolvedValue(undefined);

      // Act
      const result = await controller.delete(id, userId);

      // Assert
      expect(service.delete).toHaveBeenCalledWith(id, userId);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toBeNull();
      expect(result.message).toBe('Xóa trường tùy chỉnh thành công');
    });
  });

  describe('getComponents', () => {
    it('should get components and return success response', async () => {
      // Arrange
      const userId = 1001;
      const employeeId = undefined;
      const currentUserId = 1001;

      const expectedResponse: ComponentListResponseDto = {
        data: [
          {
            component: 'Text Input',
            config: {
              id: 'text-input',
              label: 'Văn bản',
              type: 'text',
              required: false,
              validation: { minLength: 0, maxLength: 255 },
              placeholder: 'Nhập văn bản',
              variant: 'outlined',
              size: 'small',
            },
          },
          {
            component: 'Date Picker',
            config: {
              id: 'date-picker',
              label: 'Ngày tháng',
              type: 'date',
              required: false,
              validation: { minDate: '1900-01-01', maxDate: '2100-12-31' },
              variant: 'outlined',
              size: 'small',
            },
          },
        ],
        total: 2,
      };

      mockCustomFieldService.getComponents.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.getComponents(userId, employeeId, currentUserId);

      // Assert
      expect(service.getComponents).toHaveBeenCalledWith(userId, employeeId);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe('Lấy danh sách thành phần thành công');
    });

    it('should use currentUserId if userId and employeeId are not provided', async () => {
      // Arrange
      const userId = undefined;
      const employeeId = undefined;
      const currentUserId = 1001;

      const expectedResponse: ComponentListResponseDto = {
        data: [
          {
            component: 'Text Input',
            config: {
              id: 'text-input',
              label: 'Văn bản',
              type: 'text',
              required: false,
              validation: { minLength: 0, maxLength: 255 },
              placeholder: 'Nhập văn bản',
              variant: 'outlined',
              size: 'small',
            },
          },
        ],
        total: 1,
      };

      mockCustomFieldService.getComponents.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.getComponents(userId, employeeId, currentUserId);

      // Assert
      expect(service.getComponents).toHaveBeenCalledWith(currentUserId, employeeId);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe('Lấy danh sách thành phần thành công');
    });
  });
});
