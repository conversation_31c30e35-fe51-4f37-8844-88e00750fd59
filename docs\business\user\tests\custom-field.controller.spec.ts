import { Test, TestingModule } from '@nestjs/testing';
import { CustomFieldController } from '../controllers';
import { CustomFieldService } from '../services';
import { CreateCustomFieldDto, CustomFieldResponseDto } from '../dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { EntityStatusEnum } from '@modules/business/enums';
import { JwtUtilService } from '@modules/auth/guards/jwt.util';

describe('CustomFieldController', () => {
  let controller: CustomFieldController;
  let service: CustomFieldService;

  const mockCustomFieldService = {
    create: jest.fn(),
  };

  const mockJwtUtilService = {
    verify: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomFieldController],
      providers: [
        {
          provide: CustomFieldService,
          useValue: mockCustomFieldService,
        },
        {
          provide: JwtUtilService,
          useValue: mockJwtUtilService,
        },
      ],
    }).compile();

    controller = module.get<CustomFieldController>(CustomFieldController);
    service = module.get<CustomFieldService>(CustomFieldService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a custom field and return success response', async () => {
      // Arrange
      const createDto: CreateCustomFieldDto = {
        component: 'Text Input',
        configId: 'custom-text-001',
        label: 'Số điện thoại',
        type: 'text',
        required: true,
        configJson: {
          validation: { pattern: '^[0-9]{10}$' },
          placeholder: 'Nhập số điện thoại',
          variant: 'outlined',
          size: 'small',
        },
      };

      const user = { id: 1001 };

      const expectedResponse: CustomFieldResponseDto = {
        id: 2,
        component: 'Text Input',
        configId: 'custom-text-001',
        label: 'Số điện thoại',
        type: 'text',
        required: true,
        configJson: {
          validation: { pattern: '^[0-9]{10}$' },
          placeholder: 'Nhập số điện thoại',
          variant: 'outlined',
          size: 'small',
        },
        userId: 1001,
        employeeId: null,
        status: EntityStatusEnum.PENDING,
        createAt: 1741708800000,
      };

      mockCustomFieldService.create.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.create(createDto, user);

      // Assert
      expect(service.create).toHaveBeenCalledWith({
        ...createDto,
        userId: 1001,
      });
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe('Tạo trường tùy chỉnh thành công');
    });

    it('should use provided userId if available', async () => {
      // Arrange
      const createDto: CreateCustomFieldDto = {
        component: 'Text Input',
        configId: 'custom-text-002',
        label: 'Email',
        type: 'email',
        required: true,
        configJson: {
          validation: { pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' },
          placeholder: 'Nhập email',
          variant: 'outlined',
          size: 'small',
        },
        userId: 2001,
      };

      const user = { id: 1001 };

      const expectedResponse: CustomFieldResponseDto = {
        id: 3,
        component: 'Text Input',
        configId: 'custom-text-002',
        label: 'Email',
        type: 'email',
        required: true,
        configJson: {
          validation: { pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' },
          placeholder: 'Nhập email',
          variant: 'outlined',
          size: 'small',
        },
        userId: 2001,
        employeeId: null,
        status: EntityStatusEnum.PENDING,
        createAt: 1741708800000,
      };

      mockCustomFieldService.create.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.create(createDto, user);

      // Assert
      expect(service.create).toHaveBeenCalledWith(createDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe('Tạo trường tùy chỉnh thành công');
    });
  });
});
