import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { ClassificationPriceDto } from '../../dto/classification.dto';

describe('ClassificationPriceDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(ClassificationPriceDto, {
      listPrice: 200000,
      salePrice: 150000,
      currency: 'VND',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu listPrice', async () => {
    // Arrange
    const dto = plainToInstance(ClassificationPriceDto, {
      salePrice: 150000,
      currency: 'VND',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu currency', async () => {
    // Arrange
    const dto = plainToInstance(ClassificationPriceDto, {
      listPrice: 200000,
      salePrice: 150000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu salePrice', async () => {
    // Arrange
    const dto = plainToInstance(ClassificationPriceDto, {
      listPrice: 200000,
      currency: 'VND',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });
});
