import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateFileDto } from '../../dto/file/create-file.dto';

describe('CreateFileDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 'Tài liệu hướng dẫn.pdf',
      size: 1024000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bao gồm cả trường không bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: '<PERSON><PERSON><PERSON> liệu hướng dẫn.pdf',
      warehouseId: 1,
      folderId: 2,
      size: 1024000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ khi folderId là null', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 'Tài liệu hướng dẫn.pdf',
      warehouseId: 1,
      folderId: null,
      size: 1024000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu name', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      size: 1024000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi name không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 123,
      size: 1024000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi name vượt quá 255 ký tự', async () => {
    // Arrange
    const longName = 'a'.repeat(256);
    const dto = plainToInstance(CreateFileDto, {
      name: longName,
      size: 1024000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi thiếu size', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 'Tài liệu hướng dẫn.pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sizeErrors = errors.find(e => e.property === 'size');
    expect(sizeErrors).toBeDefined();
    expect(sizeErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi size không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 'Tài liệu hướng dẫn.pdf',
      size: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sizeErrors = errors.find(e => e.property === 'size');
    expect(sizeErrors).toBeDefined();
    expect(sizeErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi warehouseId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 'Tài liệu hướng dẫn.pdf',
      warehouseId: 'not-a-number',
      size: 1024000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const warehouseIdErrors = errors.find(e => e.property === 'warehouseId');
    expect(warehouseIdErrors).toBeDefined();
    expect(warehouseIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi folderId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 'Tài liệu hướng dẫn.pdf',
      folderId: 'not-a-number',
      size: 1024000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const folderIdErrors = errors.find(e => e.property === 'folderId');
    expect(folderIdErrors).toBeDefined();
    expect(folderIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 'Tài liệu hướng dẫn.pdf',
      warehouseId: '1', // String that should be converted to number
      folderId: '2',    // String that should be converted to number
      size: '1024000',  // String that should be converted to number
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.warehouseId).toBe('number');
    expect(typeof dto.folderId).toBe('number');
    expect(typeof dto.size).toBe('number');
    expect(dto.warehouseId).toBe(1);
    expect(dto.folderId).toBe(2);
    expect(dto.size).toBe(1024000);
  });

  it('nên bỏ qua các trường không được định nghĩa trong DTO', async () => {
    // Arrange
    const dto = plainToInstance(CreateFileDto, {
      name: 'Tài liệu hướng dẫn.pdf',
      size: 1024000,
      extraField1: 'should be ignored',
      extraField2: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect((dto as any).extraField1).toBeUndefined();
    expect((dto as any).extraField2).toBeUndefined();
  });
});