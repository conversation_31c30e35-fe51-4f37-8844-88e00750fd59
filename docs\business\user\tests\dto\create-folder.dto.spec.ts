import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateFolderDto } from '../../dto/folder/create-folder.dto';

describe('CreateFolderDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: 'Tài liệu dự án',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bao gồm cả trường không bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: '<PERSON>ài liệu dự án',
      parentId: 1,
      root: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ khi parentId là null', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: 'Tài liệu dự án',
      parentId: null,
      root: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ khi root là null', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: 'Tài liệu dự án',
      parentId: 1,
      root: null,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu name', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      parentId: 1,
      root: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi name không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: 123,
      parentId: 1,
      root: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi name vượt quá độ dài tối đa', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: 'a'.repeat(256), // 256 ký tự, vượt quá giới hạn 255
      parentId: 1,
      root: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi parentId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: 'Tài liệu dự án',
      parentId: 'not-a-number',
      root: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const parentIdErrors = errors.find(e => e.property === 'parentId');
    expect(parentIdErrors).toBeDefined();
    expect(parentIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi root không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: 'Tài liệu dự án',
      parentId: 1,
      root: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const rootErrors = errors.find(e => e.property === 'root');
    expect(rootErrors).toBeDefined();
    expect(rootErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(CreateFolderDto, {
      name: 'Tài liệu dự án',
      parentId: '1', // String that should be converted to number
      root: '2',     // String that should be converted to number
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.parentId).toBe('number');
    expect(typeof dto.root).toBe('number');
    expect(dto.parentId).toBe(1);
    expect(dto.root).toBe(2);
  });
});
