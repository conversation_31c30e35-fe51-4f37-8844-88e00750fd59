import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateInventoryDto } from '../../dto/inventory/create-inventory.dto';

describe('CreateInventoryDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.availableQuantity).toBe(0);
    expect(dto.reservedQuantity).toBe(0);
    expect(dto.defectiveQuantity).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bao gồm cả trường không bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 1,
      availableQuantity: 90,
      reservedQuantity: 5,
      defectiveQuantity: 5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.availableQuantity).toBe(90);
    expect(dto.reservedQuantity).toBe(5);
    expect(dto.defectiveQuantity).toBe(5);
  });

  it('nên thất bại khi thiếu productId', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      warehouseId: 1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const productIdErrors = errors.find(e => e.property === 'productId');
    expect(productIdErrors).toBeDefined();
    expect(productIdErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu warehouseId', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const warehouseIdErrors = errors.find(e => e.property === 'warehouseId');
    expect(warehouseIdErrors).toBeDefined();
    expect(warehouseIdErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi productId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 'not-a-number',
      warehouseId: 1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const productIdErrors = errors.find(e => e.property === 'productId');
    expect(productIdErrors).toBeDefined();
    expect(productIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi warehouseId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const warehouseIdErrors = errors.find(e => e.property === 'warehouseId');
    expect(warehouseIdErrors).toBeDefined();
    expect(warehouseIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi availableQuantity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 1,
      availableQuantity: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const availableQuantityErrors = errors.find(e => e.property === 'availableQuantity');
    expect(availableQuantityErrors).toBeDefined();
    expect(availableQuantityErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi availableQuantity nhỏ hơn 0', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 1,
      availableQuantity: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const availableQuantityErrors = errors.find(e => e.property === 'availableQuantity');
    expect(availableQuantityErrors).toBeDefined();
    expect(availableQuantityErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi reservedQuantity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 1,
      reservedQuantity: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const reservedQuantityErrors = errors.find(e => e.property === 'reservedQuantity');
    expect(reservedQuantityErrors).toBeDefined();
    expect(reservedQuantityErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi reservedQuantity nhỏ hơn 0', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 1,
      reservedQuantity: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const reservedQuantityErrors = errors.find(e => e.property === 'reservedQuantity');
    expect(reservedQuantityErrors).toBeDefined();
    expect(reservedQuantityErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi defectiveQuantity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 1,
      defectiveQuantity: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const defectiveQuantityErrors = errors.find(e => e.property === 'defectiveQuantity');
    expect(defectiveQuantityErrors).toBeDefined();
    expect(defectiveQuantityErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi defectiveQuantity nhỏ hơn 0', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: 1,
      warehouseId: 1,
      defectiveQuantity: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const defectiveQuantityErrors = errors.find(e => e.property === 'defectiveQuantity');
    expect(defectiveQuantityErrors).toBeDefined();
    expect(defectiveQuantityErrors?.constraints).toHaveProperty('min');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(CreateInventoryDto, {
      productId: '1',
      warehouseId: '1',
      availableQuantity: '90',
      reservedQuantity: '5',
      defectiveQuantity: '5',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.productId).toBe('number');
    expect(typeof dto.warehouseId).toBe('number');
    expect(typeof dto.availableQuantity).toBe('number');
    expect(typeof dto.reservedQuantity).toBe('number');
    expect(typeof dto.defectiveQuantity).toBe('number');
    expect(dto.productId).toBe(1);
    expect(dto.warehouseId).toBe(1);
    expect(dto.availableQuantity).toBe(90);
    expect(dto.reservedQuantity).toBe(5);
    expect(dto.defectiveQuantity).toBe(5);
  });
});
