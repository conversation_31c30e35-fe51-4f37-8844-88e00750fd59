import { plainToInstance } from 'class-transformer';
import { FolderResponseDto } from '../../dto/folder/folder-response.dto';

describe('FolderResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainData = {
      id: 1,
      name: 'Tài liệu dự án',
      parentId: 2,
      userId: 3,
      path: '/Tài liệu/Dự án',
      root: 4,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      extraField: 'should be excluded',
    };

    // Act
    const dto = plainToInstance(FolderResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(FolderResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('<PERSON>ài liệu dự án');
    expect(dto.parentId).toBe(2);
    expect(dto.userId).toBe(3);
    expect(dto.path).toBe('/Tài liệu/Dự án');
    expect(dto.root).toBe(4);
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625097600000);
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với parentId là null', () => {
    // Arrange
    const plainData = {
      id: 1,
      name: 'Tài liệu dự án',
      parentId: null,
      userId: 3,
      path: '/Tài liệu dự án',
      root: 4,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(FolderResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(FolderResponseDto);
    expect(dto.parentId).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với path là null', () => {
    // Arrange
    const plainData = {
      id: 1,
      name: 'Tài liệu dự án',
      parentId: 2,
      userId: 3,
      path: null,
      root: 4,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(FolderResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(FolderResponseDto);
    expect(dto.path).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với root là null', () => {
    // Arrange
    const plainData = {
      id: 1,
      name: 'Tài liệu dự án',
      parentId: 2,
      userId: 3,
      path: '/Tài liệu/Dự án',
      root: null,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(FolderResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(FolderResponseDto);
    expect(dto.root).toBeNull();
  });

  it('nên chuyển đổi một mảng các plain object sang mảng DTO', () => {
    // Arrange
    const plainDataArray = [
      {
        id: 1,
        name: 'Thư mục 1',
        parentId: null,
        userId: 3,
        path: '/Thư mục 1',
        root: 4,
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
      },
      {
        id: 2,
        name: 'Thư mục 2',
        parentId: 1,
        userId: 3,
        path: '/Thư mục 1/Thư mục 2',
        root: 4,
        createdAt: 1625097700000,
        updatedAt: 1625097700000,
      },
    ];

    // Act
    const dtoArray = plainToInstance(FolderResponseDto, plainDataArray, { excludeExtraneousValues: true });

    // Assert
    expect(Array.isArray(dtoArray)).toBe(true);
    expect(dtoArray.length).toBe(2);
    expect(dtoArray[0]).toBeInstanceOf(FolderResponseDto);
    expect(dtoArray[1]).toBeInstanceOf(FolderResponseDto);
    expect(dtoArray[0].id).toBe(1);
    expect(dtoArray[1].id).toBe(2);
    expect(dtoArray[0].name).toBe('Thư mục 1');
    expect(dtoArray[1].name).toBe('Thư mục 2');
    expect(dtoArray[0].parentId).toBeNull();
    expect(dtoArray[1].parentId).toBe(1);
  });
});
