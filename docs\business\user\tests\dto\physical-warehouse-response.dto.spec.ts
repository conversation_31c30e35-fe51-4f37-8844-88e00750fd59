import { plainToInstance } from 'class-transformer';
import { PhysicalWarehouseResponseDto } from '../../dto/warehouse/physical-warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { WarehouseCustomFieldResponseDto } from '../../dto/warehouse/warehouse-custom-field-response.dto';

describe('PhysicalWarehouseResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: '<PERSON><PERSON> hàng chính',
      description: '<PERSON>ho chứa các sản phẩm chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL,
      address: '123 Đường ABC, Quận 1, TP.HCM',
      capacity: 1000,
      customFields: [
        {
          warehouseId: 1,
          fieldId: 1,
          value: { value: 'Giá trị 1' },
        },
      ],
      extraField: 'should be excluded',
    };

    // Act
    const dto = plainToInstance(PhysicalWarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.name).toBe('Kho hàng chính');
    expect(dto.description).toBe('Kho chứa các sản phẩm chính của công ty');
    expect(dto.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dto.address).toBe('123 Đường ABC, Quận 1, TP.HCM');
    expect(dto.capacity).toBe(1000);
    expect(dto.customFields).toHaveLength(1);
    expect(dto.customFields[0]).toBeInstanceOf(WarehouseCustomFieldResponseDto);
    expect(dto.customFields[0].warehouseId).toBe(1);
    expect(dto.customFields[0].fieldId).toBe(1);
    expect(dto.customFields[0].value).toEqual({ value: 'Giá trị 1' });
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với capacity là null', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: 'Kho hàng chính',
      description: 'Kho chứa các sản phẩm chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL,
      address: '123 Đường ABC, Quận 1, TP.HCM',
      capacity: null,
    };

    // Act
    const dto = plainToInstance(PhysicalWarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseResponseDto);
    expect(dto.capacity).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với customFields là undefined', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: 'Kho hàng chính',
      description: 'Kho chứa các sản phẩm chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL,
      address: '123 Đường ABC, Quận 1, TP.HCM',
      capacity: 1000,
    };

    // Act
    const dto = plainToInstance(PhysicalWarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseResponseDto);
    expect(dto.customFields).toBeUndefined();
  });

  it('nên chuyển đổi một mảng các plain object sang mảng DTO', () => {
    // Arrange
    const plainDataArray = [
      {
        warehouseId: 1,
        name: 'Kho hàng 1',
        description: 'Mô tả kho hàng 1',
        type: WarehouseTypeEnum.PHYSICAL,
        address: '123 Đường ABC, Quận 1, TP.HCM',
        capacity: 1000,
      },
      {
        warehouseId: 2,
        name: 'Kho hàng 2',
        description: 'Mô tả kho hàng 2',
        type: WarehouseTypeEnum.PHYSICAL,
        address: '456 Đường XYZ, Quận 2, TP.HCM',
        capacity: 2000,
      },
    ];

    // Act
    const dtoArray = plainToInstance(PhysicalWarehouseResponseDto, plainDataArray, { excludeExtraneousValues: true });

    // Assert
    expect(Array.isArray(dtoArray)).toBe(true);
    expect(dtoArray.length).toBe(2);
    expect(dtoArray[0]).toBeInstanceOf(PhysicalWarehouseResponseDto);
    expect(dtoArray[1]).toBeInstanceOf(PhysicalWarehouseResponseDto);
    expect(dtoArray[0].warehouseId).toBe(1);
    expect(dtoArray[1].warehouseId).toBe(2);
    expect(dtoArray[0].name).toBe('Kho hàng 1');
    expect(dtoArray[1].name).toBe('Kho hàng 2');
    expect(dtoArray[0].address).toBe('123 Đường ABC, Quận 1, TP.HCM');
    expect(dtoArray[1].address).toBe('456 Đường XYZ, Quận 2, TP.HCM');
  });
});
