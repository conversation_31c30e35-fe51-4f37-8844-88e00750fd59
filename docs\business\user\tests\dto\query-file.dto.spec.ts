import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryFileDto } from '../../dto/file/query-file.dto';
import { SortDirection } from '@common/dto';

describe('QueryFileDto', () => {
  it('nên xác thực DTO hợp lệ với các giá trị mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.sortBy).toBe('id');
    expect(dto.sortDirection).toBe(SortDirection.ASC);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      page: 2,
      limit: 20,
      search: 'document',
      warehouseId: 1,
      folderId: 2,
      sortBy: 'name',
      sortDirection: SortDirection.DESC,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
    expect(dto.search).toBe('document');
    expect(dto.warehouseId).toBe(1);
    expect(dto.folderId).toBe(2);
    expect(dto.sortBy).toBe('name');
    expect(dto.sortDirection).toBe(SortDirection.DESC);
  });

  it('nên thất bại khi page không phải là số nguyên', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      page: 1.5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const pageErrors = errors.find(e => e.property === 'page');
    expect(pageErrors).toBeDefined();
    expect(pageErrors?.constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi page nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      page: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const pageErrors = errors.find(e => e.property === 'page');
    expect(pageErrors).toBeDefined();
    expect(pageErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi limit không phải là số nguyên', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      limit: 10.5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const limitErrors = errors.find(e => e.property === 'limit');
    expect(limitErrors).toBeDefined();
    expect(limitErrors?.constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi limit nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      limit: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const limitErrors = errors.find(e => e.property === 'limit');
    expect(limitErrors).toBeDefined();
    expect(limitErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi warehouseId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      warehouseId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const warehouseIdErrors = errors.find(e => e.property === 'warehouseId');
    expect(warehouseIdErrors).toBeDefined();
    expect(warehouseIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi folderId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      folderId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const folderIdErrors = errors.find(e => e.property === 'folderId');
    expect(folderIdErrors).toBeDefined();
    expect(folderIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi sortDirection không phải là giá trị hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      sortDirection: 'INVALID',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sortDirectionErrors = errors.find(e => e.property === 'sortDirection');
    expect(sortDirectionErrors).toBeDefined();
    expect(sortDirectionErrors?.constraints).toHaveProperty('isEnum');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      page: '2',
      limit: '20',
      warehouseId: '1',
      folderId: '2',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.page).toBe('number');
    expect(typeof dto.limit).toBe('number');
    expect(typeof dto.warehouseId).toBe('number');
    expect(typeof dto.folderId).toBe('number');
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
    expect(dto.warehouseId).toBe(1);
    expect(dto.folderId).toBe(2);
  });
});