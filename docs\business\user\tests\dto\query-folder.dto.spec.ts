import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryFolderDto } from '../../dto/folder/query-folder.dto';
import { SortDirection } from '@common/dto';

describe('QueryFolderDto', () => {
  it('nên xác thực DTO hợp lệ với các giá trị mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.sortBy).toBe('id');
    expect(dto.sortDirection).toBe(SortDirection.ASC);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      page: 2,
      limit: 20,
      search: 'folder',
      userId: 1,
      parentId: 2,
      root: 3,
      sortBy: 'name',
      sortDirection: SortDirection.DESC,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
    expect(dto.search).toBe('folder');
    expect(dto.userId).toBe(1);
    expect(dto.parentId).toBe(2);
    expect(dto.root).toBe(3);
    expect(dto.sortBy).toBe('name');
    expect(dto.sortDirection).toBe(SortDirection.DESC);
  });

  it('nên xác thực DTO hợp lệ khi parentId là null', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      parentId: null,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.parentId).toBeNull();
  });

  it('nên xác thực DTO hợp lệ khi root là null', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      root: null,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.root).toBeNull();
  });

  it('nên thất bại khi page không phải là số nguyên', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      page: 1.5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const pageErrors = errors.find(e => e.property === 'page');
    expect(pageErrors).toBeDefined();
    expect(pageErrors?.constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi page nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      page: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const pageErrors = errors.find(e => e.property === 'page');
    expect(pageErrors).toBeDefined();
    expect(pageErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi limit không phải là số nguyên', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      limit: 10.5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const limitErrors = errors.find(e => e.property === 'limit');
    expect(limitErrors).toBeDefined();
    expect(limitErrors?.constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi limit nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      limit: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const limitErrors = errors.find(e => e.property === 'limit');
    expect(limitErrors).toBeDefined();
    expect(limitErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi userId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      userId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const userIdErrors = errors.find(e => e.property === 'userId');
    expect(userIdErrors).toBeDefined();
    expect(userIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi parentId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      parentId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const parentIdErrors = errors.find(e => e.property === 'parentId');
    expect(parentIdErrors).toBeDefined();
    expect(parentIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi root không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      root: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const rootErrors = errors.find(e => e.property === 'root');
    expect(rootErrors).toBeDefined();
    expect(rootErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi sortDirection không phải là giá trị hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      sortDirection: 'INVALID',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sortDirectionErrors = errors.find(e => e.property === 'sortDirection');
    expect(sortDirectionErrors).toBeDefined();
    expect(sortDirectionErrors?.constraints).toHaveProperty('isEnum');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(QueryFolderDto, {
      page: '2',
      limit: '20',
      userId: '1',
      parentId: '2',
      root: '3',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.page).toBe('number');
    expect(typeof dto.limit).toBe('number');
    expect(typeof dto.userId).toBe('number');
    expect(typeof dto.parentId).toBe('number');
    expect(typeof dto.root).toBe('number');
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
    expect(dto.userId).toBe(1);
    expect(dto.parentId).toBe(2);
    expect(dto.root).toBe(3);
  });
});
