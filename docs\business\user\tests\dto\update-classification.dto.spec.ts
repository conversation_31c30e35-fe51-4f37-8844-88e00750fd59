import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateClassificationDto } from '../../dto/classification.dto';

describe('UpdateClassificationDto', () => {
  it('nên xác thực DTO hợp lệ khi không có trường nào được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với id (cập nhật classification hiện có)', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      id: 1,
      type: 'Màu sắc',
      price: {
        listPrice: 200000,
        salePrice: 150000,
        currency: 'VND',
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ khi không có id (tạo classification mới)', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      type: 'Kích thước',
      price: {
        listPrice: 300000,
        salePrice: 250000,
        currency: 'VND',
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với chỉ type (tạo classification mới)', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      type: 'Chất liệu',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với customFields', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      id: 2,
      type: 'Màu sắc',
      customFields: [
        {
          customFieldId: 1,
          value: { value: 'Đỏ' },
        },
        {
          customFieldId: 2,
          value: { value: 'Xanh' },
        },
      ],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi id không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      id: 'invalid',
      type: 'Màu sắc',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const idErrors = errors.find(e => e.property === 'id');
    expect(idErrors).toBeDefined();
    expect(idErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi type không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      id: 1,
      type: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const typeErrors = errors.find(e => e.property === 'type');
    expect(typeErrors).toBeDefined();
    expect(typeErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi price không phải là object', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      id: 1,
      type: 'Màu sắc',
      price: 'invalid',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const priceErrors = errors.find(e => e.property === 'price');
    expect(priceErrors).toBeDefined();
    expect(priceErrors?.constraints).toHaveProperty('isObject');
  });

  it('nên thất bại khi customFields không phải là mảng', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      id: 1,
      type: 'Màu sắc',
      customFields: 'invalid',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const customFieldsErrors = errors.find(e => e.property === 'customFields');
    expect(customFieldsErrors).toBeDefined();
    expect(customFieldsErrors?.constraints).toHaveProperty('isArray');
  });

  it('nên xác thực DTO hợp lệ với price có đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(UpdateClassificationDto, {
      id: 1,
      type: 'Màu sắc',
      price: {
        listPrice: 500000,
        salePrice: 400000,
        currency: 'VND',
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
