import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateFileDto } from '../../dto/file/update-file.dto';

describe('UpdateFileDto', () => {
  it('nên xác thực DTO hợp lệ khi không có trường nào được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {
      name: 'Tài liệu hướng dẫn cập nhật.pdf',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {
      name: 'Tài liệu hướng dẫn cập nhật.pdf',
      folderId: 2,
      size: 2048000,
      generateUploadUrl: true,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi name không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {
      name: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi name vượt quá 255 ký tự', async () => {
    // Arrange
    const longName = 'a'.repeat(256);
    const dto = plainToInstance(UpdateFileDto, {
      name: longName,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi folderId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {
      folderId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const folderIdErrors = errors.find(e => e.property === 'folderId');
    expect(folderIdErrors).toBeDefined();
    expect(folderIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi size không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {
      size: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sizeErrors = errors.find(e => e.property === 'size');
    expect(sizeErrors).toBeDefined();
    expect(sizeErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi generateUploadUrl không phải là boolean', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {
      generateUploadUrl: 'not-a-boolean',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const generateUploadUrlErrors = errors.find(e => e.property === 'generateUploadUrl');
    expect(generateUploadUrlErrors).toBeDefined();
    expect(generateUploadUrlErrors?.constraints).toHaveProperty('isBoolean');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {
      folderId: '2',
      size: '2048000',
      generateUploadUrl: 'true',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.folderId).toBe('number');
    expect(typeof dto.size).toBe('number');
    expect(typeof dto.generateUploadUrl).toBe('boolean');
    expect(dto.folderId).toBe(2);
    expect(dto.size).toBe(2048000);
    expect(dto.generateUploadUrl).toBe(true);
  });

  it('nên bỏ qua các trường không được định nghĩa trong DTO', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFileDto, {
      name: 'Tài liệu hướng dẫn cập nhật.pdf',
      extraField1: 'should be ignored',
      extraField2: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect((dto as any).extraField1).toBeUndefined();
    expect((dto as any).extraField2).toBeUndefined();
  });
});