import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdatePhysicalWarehouseDto } from '../../dto/warehouse/update-physical-warehouse.dto';

describe('UpdatePhysicalWarehouseDto', () => {
  it('nên xác thực DTO hợp lệ khi không có trường nào được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdatePhysicalWarehouseDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdatePhysicalWarehouseDto, {
      address: '123 Đường XYZ, Quận 2, TP.HCM',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với tất cả các trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdatePhysicalWarehouseDto, {
      address: '123 Đường XYZ, Quận 2, TP.HCM',
      capacity: 2000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi address không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdatePhysicalWarehouseDto, {
      address: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const addressErrors = errors.find(e => e.property === 'address');
    expect(addressErrors).toBeDefined();
    expect(addressErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi address vượt quá độ dài tối đa', async () => {
    // Arrange
    const dto = plainToInstance(UpdatePhysicalWarehouseDto, {
      address: 'a'.repeat(256), // 256 ký tự, vượt quá giới hạn 255
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const addressErrors = errors.find(e => e.property === 'address');
    expect(addressErrors).toBeDefined();
    expect(addressErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi capacity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdatePhysicalWarehouseDto, {
      capacity: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const capacityErrors = errors.find(e => e.property === 'capacity');
    expect(capacityErrors).toBeDefined();
    expect(capacityErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(UpdatePhysicalWarehouseDto, {
      capacity: '2000', // String that should be converted to number
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.capacity).toBe('number');
    expect(dto.capacity).toBe(2000);
  });
});
