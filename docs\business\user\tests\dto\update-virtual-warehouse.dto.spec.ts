import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateVirtualWarehouseDto } from '../../dto/warehouse/update-virtual-warehouse.dto';

describe('UpdateVirtualWarehouseDto', () => {
  it('nên xác thực DTO hợp lệ khi không có trường nào được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateVirtualWarehouseDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateVirtualWarehouseDto, {
      associatedSystem: 'Oracle ERP',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với tất cả các trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateVirtualWarehouseDto, {
      associatedSystem: 'Oracle ERP',
      purpose: 'Quản lý hàng hóa trực tuyến (đã cập nhật)',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi associatedSystem không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateVirtualWarehouseDto, {
      associatedSystem: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const associatedSystemErrors = errors.find(e => e.property === 'associatedSystem');
    expect(associatedSystemErrors).toBeDefined();
    expect(associatedSystemErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi associatedSystem vượt quá độ dài tối đa', async () => {
    // Arrange
    const dto = plainToInstance(UpdateVirtualWarehouseDto, {
      associatedSystem: 'a'.repeat(101), // 101 ký tự, vượt quá giới hạn 100
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const associatedSystemErrors = errors.find(e => e.property === 'associatedSystem');
    expect(associatedSystemErrors).toBeDefined();
    expect(associatedSystemErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi purpose không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateVirtualWarehouseDto, {
      purpose: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const purposeErrors = errors.find(e => e.property === 'purpose');
    expect(purposeErrors).toBeDefined();
    expect(purposeErrors?.constraints).toHaveProperty('isString');
  });
});
