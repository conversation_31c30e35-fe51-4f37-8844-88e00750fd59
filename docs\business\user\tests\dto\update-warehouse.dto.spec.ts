import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateWarehouseDto } from '../../dto/warehouse/update-warehouse.dto';

describe('UpdateWarehouseDto', () => {
  it('nên xác thực DTO hợp lệ khi không có trường nào được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseDto, {
      name: '<PERSON><PERSON> hàng chính (đã cập nhật)',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với tất cả các trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseDto, {
      name: 'Kho hàng chính (đã cập nhật)',
      description: 'Mô tả kho hàng đã cập nhật',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi name không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseDto, {
      name: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi name vượt quá độ dài tối đa', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseDto, {
      name: 'a'.repeat(101), // 101 ký tự, vượt quá giới hạn 100
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi description không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseDto, {
      description: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const descriptionErrors = errors.find(e => e.property === 'description');
    expect(descriptionErrors).toBeDefined();
    expect(descriptionErrors?.constraints).toHaveProperty('isString');
  });
});
