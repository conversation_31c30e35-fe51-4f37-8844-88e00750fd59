import { plainToInstance } from 'class-transformer';
import { VirtualWarehouseResponseDto } from '../../dto/warehouse/virtual-warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { WarehouseCustomFieldResponseDto } from '../../dto/warehouse/warehouse-custom-field-response.dto';

describe('VirtualWarehouseResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: 'Kho ảo 1',
      description: 'Mô tả kho ảo 1',
      type: WarehouseTypeEnum.VIRTUAL,
      associatedSystem: 'SAP ERP',
      purpose: 'Quản lý hàng hóa trực tuyến',
      customFields: [
        {
          warehouseId: 1,
          fieldId: 1,
          value: { value: 'Giá trị 1' },
        },
      ],
      extraField: 'should be excluded',
    };

    // Act
    const dto = plainToInstance(VirtualWarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.name).toBe('Kho ảo 1');
    expect(dto.description).toBe('Mô tả kho ảo 1');
    expect(dto.type).toBe(WarehouseTypeEnum.VIRTUAL);
    expect(dto.associatedSystem).toBe('SAP ERP');
    expect(dto.purpose).toBe('Quản lý hàng hóa trực tuyến');
    expect(dto.customFields).toHaveLength(1);
    expect(dto.customFields[0]).toBeInstanceOf(WarehouseCustomFieldResponseDto);
    expect(dto.customFields[0].warehouseId).toBe(1);
    expect(dto.customFields[0].fieldId).toBe(1);
    expect(dto.customFields[0].value).toEqual({ value: 'Giá trị 1' });
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với associatedSystem là null', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: 'Kho ảo 1',
      description: 'Mô tả kho ảo 1',
      type: WarehouseTypeEnum.VIRTUAL,
      associatedSystem: null,
      purpose: 'Quản lý hàng hóa trực tuyến',
    };

    // Act
    const dto = plainToInstance(VirtualWarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dto.associatedSystem).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với purpose là null', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: 'Kho ảo 1',
      description: 'Mô tả kho ảo 1',
      type: WarehouseTypeEnum.VIRTUAL,
      associatedSystem: 'SAP ERP',
      purpose: null,
    };

    // Act
    const dto = plainToInstance(VirtualWarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dto.purpose).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với customFields là undefined', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: 'Kho ảo 1',
      description: 'Mô tả kho ảo 1',
      type: WarehouseTypeEnum.VIRTUAL,
      associatedSystem: 'SAP ERP',
      purpose: 'Quản lý hàng hóa trực tuyến',
    };

    // Act
    const dto = plainToInstance(VirtualWarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dto.customFields).toBeUndefined();
  });

  it('nên chuyển đổi một mảng các plain object sang mảng DTO', () => {
    // Arrange
    const plainDataArray = [
      {
        warehouseId: 1,
        name: 'Kho ảo 1',
        description: 'Mô tả kho ảo 1',
        type: WarehouseTypeEnum.VIRTUAL,
        associatedSystem: 'SAP ERP',
        purpose: 'Quản lý hàng hóa trực tuyến',
      },
      {
        warehouseId: 2,
        name: 'Kho ảo 2',
        description: 'Mô tả kho ảo 2',
        type: WarehouseTypeEnum.VIRTUAL,
        associatedSystem: 'Oracle ERP',
        purpose: 'Quản lý hàng hóa offline',
      },
    ];

    // Act
    const dtoArray = plainToInstance(VirtualWarehouseResponseDto, plainDataArray, { excludeExtraneousValues: true });

    // Assert
    expect(Array.isArray(dtoArray)).toBe(true);
    expect(dtoArray.length).toBe(2);
    expect(dtoArray[0]).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dtoArray[1]).toBeInstanceOf(VirtualWarehouseResponseDto);
    expect(dtoArray[0].warehouseId).toBe(1);
    expect(dtoArray[1].warehouseId).toBe(2);
    expect(dtoArray[0].name).toBe('Kho ảo 1');
    expect(dtoArray[1].name).toBe('Kho ảo 2');
    expect(dtoArray[0].associatedSystem).toBe('SAP ERP');
    expect(dtoArray[1].associatedSystem).toBe('Oracle ERP');
  });
});
