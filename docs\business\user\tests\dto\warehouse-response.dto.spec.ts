import { plainToInstance } from 'class-transformer';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

describe('WarehouseResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: '<PERSON><PERSON> hàng chính',
      description: '<PERSON>ho hàng chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL,
      extraField: 'should be excluded',
    };

    // Act
    const dto = plainToInstance(WarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(WarehouseResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.name).toBe('<PERSON>ho hàng chính');
    expect(dto.description).toBe('<PERSON>ho hàng chính của công ty');
    expect(dto.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với description là null', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      name: 'Kho hàng chính',
      description: null,
      type: WarehouseTypeEnum.PHYSICAL,
    };

    // Act
    const dto = plainToInstance(WarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(WarehouseResponseDto);
    expect(dto.description).toBeNull();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với type là VIRTUAL', () => {
    // Arrange
    const plainData = {
      warehouseId: 2,
      name: 'Kho ảo',
      description: 'Kho ảo cho hệ thống',
      type: WarehouseTypeEnum.VIRTUAL,
    };

    // Act
    const dto = plainToInstance(WarehouseResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(WarehouseResponseDto);
    expect(dto.type).toBe(WarehouseTypeEnum.VIRTUAL);
  });

  it('nên chuyển đổi một mảng các plain object sang mảng DTO', () => {
    // Arrange
    const plainDataArray = [
      {
        warehouseId: 1,
        name: 'Kho hàng 1',
        description: 'Mô tả kho hàng 1',
        type: WarehouseTypeEnum.PHYSICAL,
      },
      {
        warehouseId: 2,
        name: 'Kho hàng 2',
        description: 'Mô tả kho hàng 2',
        type: WarehouseTypeEnum.VIRTUAL,
      },
    ];

    // Act
    const dtoArray = plainToInstance(WarehouseResponseDto, plainDataArray, { excludeExtraneousValues: true });

    // Assert
    expect(Array.isArray(dtoArray)).toBe(true);
    expect(dtoArray.length).toBe(2);
    expect(dtoArray[0]).toBeInstanceOf(WarehouseResponseDto);
    expect(dtoArray[1]).toBeInstanceOf(WarehouseResponseDto);
    expect(dtoArray[0].warehouseId).toBe(1);
    expect(dtoArray[1].warehouseId).toBe(2);
    expect(dtoArray[0].name).toBe('Kho hàng 1');
    expect(dtoArray[1].name).toBe('Kho hàng 2');
    expect(dtoArray[0].type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dtoArray[1].type).toBe(WarehouseTypeEnum.VIRTUAL);
  });
});
