import { Test, TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { FileRepository } from '../../../repositories';
import { File } from '../../../entities';

describe('FileRepository', () => {
  let repository: FileRepository;
  let dataSource: DataSource;

  // Mock data
  const mockFiles: File[] = [
    {
      id: 1,
      name: 'File 1.pdf',
      folderId: 1,
      storageKey: 'files/2023/05/file1.pdf',
      size: 1024,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      warehouseId: 1, // Trường này không có trong database, chỉ dùng trong code
    },
    {
      id: 2,
      name: 'File 2.docx',
      folderId: 1,
      storageKey: 'files/2023/05/file2.docx',
      size: 2048,
      createdAt: 1625097700000,
      updatedAt: 1625097700000,
      warehouseId: 1, // Trường này không có trong database, chỉ dùng trong code
    },
  ];

  // Mock query builder
  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn().mockImplementation(() => Promise.resolve([[], 0])),
    getMany: jest.fn().mockImplementation(() => Promise.resolve([])),
    getOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
    getCount: jest.fn().mockImplementation(() => Promise.resolve(0)),
    clone: jest.fn().mockReturnThis()
  };

  // Biến để lưu ID được truyền vào where
  let currentFileId: number | null = null;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FileRepository,
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
      ],
    }).compile();

    repository = module.get<FileRepository>(FileRepository);
    dataSource = module.get<DataSource>(DataSource);

    // Mock các phương thức của repository
    jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);
    jest.spyOn(repository, 'save').mockImplementation((file: File) => Promise.resolve({ ...file, id: 1 }));

    // Mock phương thức createBaseQuery (private method)
    jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue(mockQueryBuilder as any);

    // Ghi đè hàm where để lưu ID được truyền vào
    (mockQueryBuilder.where as jest.Mock).mockImplementation((condition: string, parameters: any) => {
      if (condition === 'file.id = :id' && parameters?.id) {
        currentFileId = parameters.id;
      }
      return mockQueryBuilder;
    });

    // Ghi đè hàm getOne để trả về file dựa trên ID đã lưu
    (mockQueryBuilder.getOne as jest.Mock).mockImplementation(() => {
      if (!currentFileId) return Promise.resolve(null);
      const file = mockFiles.find(f => f.id === currentFileId);
      return Promise.resolve(file || null);
    });

    jest.spyOn(repository, 'find').mockResolvedValue(mockFiles);
    jest.spyOn(repository, 'update').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
    jest.spyOn(repository, 'delete').mockResolvedValue({ affected: 1, raw: {} });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createFile', () => {
    it('nên tạo file mới thành công', async () => {
      // Arrange
      const newFile: File = {
        name: 'New File.pdf',
        folderId: 1,
        storageKey: 'files/2023/05/new-file.pdf',
        size: 1024,
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        warehouseId: 1, // Trường này không có trong database, chỉ dùng trong code
      } as File;

      // Act
      const result = await repository.createFile(newFile);

      // Assert
      expect(repository.save).toHaveBeenCalledWith(newFile);
      expect(result).toEqual({ ...newFile, id: 1 });
    });

    it('nên ném lỗi khi tạo file thất bại', async () => {
      // Arrange
      const newFile: File = {
        name: 'New File.pdf',
        folderId: 1,
        storageKey: 'files/2023/05/new-file.pdf',
        size: 1024,
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        warehouseId: 1, // Trường này không có trong database, chỉ dùng trong code
      } as File;

      jest.spyOn(repository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.createFile(newFile)).rejects.toThrow('Lỗi khi tạo file: Database error');
    });
  });

  describe('findById', () => {
    it('nên tìm file theo ID thành công', async () => {
      // Arrange
      const fileId = 1;
      currentFileId = null; // Reset ID

      // Act
      const result = await repository.findById(fileId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('file.id = :id', { id: fileId });
      expect(result).toEqual(mockFiles[0]);
    });

    it('nên trả về null khi không tìm thấy file', async () => {
      // Arrange
      const fileId = 999;
      currentFileId = null; // Reset ID
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValueOnce(null);

      // Act
      const result = await repository.findById(fileId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('file.id = :id', { id: fileId });
      expect(result).toBeNull();
    });

    it('nên ném lỗi khi tìm file thất bại', async () => {
      // Arrange
      const fileId = 1;
      currentFileId = null; // Reset ID
      (mockQueryBuilder.getOne as jest.Mock).mockRejectedValueOnce(new Error('Database error'));

      // Act & Assert
      await expect(repository.findById(fileId)).rejects.toThrow(`Lỗi khi tìm file theo ID ${fileId}: Database error`);
    });
  });

  describe('findByFolderId', () => {
    it('nên tìm danh sách file theo ID thư mục thành công', async () => {
      // Arrange
      const folderId = 1;
      jest.spyOn(mockQueryBuilder, 'getMany').mockResolvedValue(mockFiles);

      // Act
      const result = await repository.findByFolderId(folderId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('file.folderId = :folderId', { folderId });
      expect(result).toEqual(mockFiles);
    });

    it('nên ném lỗi khi tìm danh sách file theo ID thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      jest.spyOn(mockQueryBuilder, 'getMany').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.findByFolderId(folderId)).rejects.toThrow(
        `Lỗi khi tìm file theo ID thư mục ${folderId}: Database error`
      );
    });
  });

  describe('findAll', () => {
    it('nên tìm danh sách file với phân trang thành công', async () => {
      // Arrange
      const queryParams = {
        page: 1,
        limit: 10,
        search: 'file',
        folderId: 1,
        sortBy: 'name',
        sortDirection: 'ASC',
      };

      // Reset các mock
      (mockQueryBuilder.andWhere as jest.Mock).mockClear();
      (mockQueryBuilder.orderBy as jest.Mock).mockClear();
      (mockQueryBuilder.skip as jest.Mock).mockClear();
      (mockQueryBuilder.take as jest.Mock).mockClear();

      // Mock getCount và getMany
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValueOnce(mockFiles.length);
      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValueOnce(mockFiles);

      // Act
      const result = await repository.findAll(queryParams);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('file.name ILIKE :search', { search: '%file%' });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('file.folderId = :folderId', { folderId: 1 });
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('file.name', 'ASC');
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
      expect(result).toEqual({
        items: mockFiles,
        meta: {
          totalItems: mockFiles.length,
          itemCount: mockFiles.length,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      });
    });

    it('nên ném lỗi khi tìm danh sách file thất bại', async () => {
      // Arrange
      const queryParams = {
        page: 1,
        limit: 10,
      };

      // Mock getCount để ném lỗi
      (mockQueryBuilder.getCount as jest.Mock).mockRejectedValueOnce(new Error('Database error'));

      // Act & Assert
      await expect(repository.findAll(queryParams)).rejects.toThrow('Lỗi khi tìm kiếm file: Database error');
    });
  });

  describe('updateFile', () => {
    it('nên cập nhật file thành công', async () => {
      // Arrange
      const fileId = 1;
      const updateData = {
        name: 'Updated File.pdf',
      };

      // Mock findById to return a file after update
      jest.spyOn(repository, 'findById').mockResolvedValue({
        ...mockFiles[0],
        name: 'Updated File.pdf'
      });

      // Act
      const result = await repository.updateFile(fileId, updateData);

      // Assert
      expect(repository.update).toHaveBeenCalledWith({ id: fileId }, updateData);
      expect(result).toEqual({
        ...mockFiles[0],
        name: 'Updated File.pdf'
      });
    });

    it('nên ném lỗi khi cập nhật file thất bại', async () => {
      // Arrange
      const fileId = 1;
      const updateData = {
        name: 'Updated File.pdf',
      };
      jest.spyOn(repository, 'update').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.updateFile(fileId, updateData)).rejects.toThrow(
        `Lỗi khi cập nhật file với ID ${fileId}: Database error`
      );
    });
  });

  describe('deleteFile', () => {
    it('nên xóa file thành công', async () => {
      // Arrange
      const fileId = 1;

      // Act
      const result = await repository.deleteFile(fileId);

      // Assert
      expect(repository.delete).toHaveBeenCalledWith({ id: fileId });
      expect(result).toEqual(true);
    });

    it('nên ném lỗi khi xóa file thất bại', async () => {
      // Arrange
      const fileId = 1;
      jest.spyOn(repository, 'delete').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.deleteFile(fileId)).rejects.toThrow(`Lỗi khi xóa file với ID ${fileId}: Database error`);
    });
  });
});
