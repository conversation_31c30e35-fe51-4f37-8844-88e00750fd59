import { Test, TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { FolderRepository } from '../../../repositories';
import { Folder, VirtualWarehouse } from '../../../entities';
import { User } from '../../../../user/entities';

describe('FolderRepository', () => {
  let repository: FolderRepository;
  let dataSource: DataSource;

  // Mock data
  const mockFolders: Folder[] = [
    {
      id: 1,
      name: 'Thư mục 1',
      parentId: undefined as unknown as number, // TypeScript hack to handle null in database
      userId: 1,
      path: '/Thư mục 1',
      root: 1,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    },
    {
      id: 2,
      name: 'Th<PERSON> mục 2',
      parentId: 1,
      userId: 1,
      path: '/Thư mục 1/Th<PERSON> mục 2',
      root: 1,
      createdAt: 1625097700000,
      updatedAt: 1625097700000,
    },
  ];

  const mockUser: User = {
    id: 1,
    fullName: '<PERSON><PERSON><PERSON><PERSON>',
    email: 'nguy<PERSON><PERSON>@example.com',
    phoneNumber: '0123456789',
  } as User;

  const mockVirtualWarehouse: VirtualWarehouse = {
    warehouseId: 1,
    associatedSystem: 'System A',
    purpose: 'Purpose A',
  };

  // Mock query builder
  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
    getMany: jest.fn(),
    getOne: jest.fn(),
    getCount: jest.fn().mockResolvedValue(2),
    clone: jest.fn().mockReturnThis()
  };

  // Mock user repository query builder
  const mockUserQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    getOne: jest.fn().mockResolvedValue(mockUser),
  };

  // Mock virtual warehouse repository query builder
  const mockVirtualWarehouseQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    getOne: jest.fn().mockResolvedValue(mockVirtualWarehouse),
  };

  // Biến để lưu ID được truyền vào where
  let currentFolderId: number | null = null;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FolderRepository,
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
            getRepository: jest.fn().mockReturnValue({
              createQueryBuilder: jest.fn().mockImplementation((alias) => {
                if (alias === 'user') return mockUserQueryBuilder;
                if (alias === 'vw') return mockVirtualWarehouseQueryBuilder;
                return mockQueryBuilder;
              }),
            }),
          },
        },
      ],
    }).compile();

    repository = module.get<FolderRepository>(FolderRepository);
    dataSource = module.get<DataSource>(DataSource);

    // Mock các phương thức của repository
    jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);
    jest.spyOn(repository, 'save').mockImplementation((folder: Folder) => Promise.resolve({ ...folder, id: 1 }));

    // Mock phương thức createBaseQuery (private method)
    jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue(mockQueryBuilder as any);

    // Ghi đè hàm where để lưu ID được truyền vào
    (mockQueryBuilder.where as jest.Mock).mockImplementation((condition: string, parameters: any) => {
      if (condition === 'folder.id = :id' && parameters?.id) {
        currentFolderId = parameters.id;
      }
      return mockQueryBuilder;
    });

    // Ghi đè hàm getOne để trả về folder dựa trên ID đã lưu
    (mockQueryBuilder.getOne as jest.Mock).mockImplementation(() => {
      if (!currentFolderId) return Promise.resolve(null);
      const folder = mockFolders.find(f => f.id === currentFolderId);
      return Promise.resolve(folder || null);
    });

    jest.spyOn(repository, 'findOne').mockImplementation((options: any) => {
      const id = options.where?.id;
      const folder = mockFolders.find(f => f.id === id);
      return Promise.resolve(folder || null);
    });
    jest.spyOn(repository, 'find').mockResolvedValue(mockFolders);
    jest.spyOn(repository, 'update').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
    jest.spyOn(repository, 'delete').mockResolvedValue({ affected: 1, raw: {} });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createFolder', () => {
    it('nên tạo thư mục mới thành công', async () => {
      // Arrange
      const newFolder = {
        name: 'Thư mục mới',
        parentId: undefined as unknown as number, // TypeScript hack to handle null in database
        userId: 1,
        path: '/Thư mục mới',
        root: 1,
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
      } as Folder;

      // Act
      const result = await repository.createFolder(newFolder);

      // Assert
      expect(repository.save).toHaveBeenCalledWith(newFolder);
      expect(result).toEqual({ ...newFolder, id: 1 });
    });

    it('nên ném lỗi khi tạo thư mục thất bại', async () => {
      // Arrange
      const newFolder = {
        name: 'Thư mục mới',
        parentId: undefined as unknown as number, // TypeScript hack to handle null in database
        userId: 1,
        path: '/Thư mục mới',
        root: 1,
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
      } as Folder;

      jest.spyOn(repository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.createFolder(newFolder)).rejects.toThrow('Lỗi khi tạo thư mục: Database error');
    });
  });

  describe('findById', () => {
    it('nên tìm thư mục theo ID thành công', async () => {
      // Arrange
      const folderId = 1;
      currentFolderId = null; // Reset ID

      // Act
      const result = await repository.findById(folderId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('folder.id = :id', { id: folderId });
      expect(result).toEqual(mockFolders[0]);
    });

    it('nên trả về null khi không tìm thấy thư mục', async () => {
      // Arrange
      const folderId = 999;
      currentFolderId = null; // Reset ID
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValueOnce(null);

      // Act
      const result = await repository.findById(folderId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('folder.id = :id', { id: folderId });
      expect(result).toBeNull();
    });

    it('nên ném lỗi khi tìm thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      currentFolderId = null; // Reset ID
      (mockQueryBuilder.getOne as jest.Mock).mockRejectedValueOnce(new Error('Database error'));

      // Act & Assert
      await expect(repository.findById(folderId)).rejects.toThrow(`Lỗi khi tìm thư mục theo ID ${folderId}: Database error`);
    });
  });

  describe('findByIdWithDetails', () => {
    it('nên tìm thông tin chi tiết thư mục theo ID thành công', async () => {
      // Arrange
      const folderId = 1;
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(mockFolders[0]);

      // Act
      const result = await repository.findByIdWithDetails(folderId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('folder.id = :id', { id: folderId });
      expect(result).toEqual({
        ...mockFolders[0],
        parentFolder: null,
        user: mockUser,
        virtualWarehouse: mockVirtualWarehouse,
      });
    });

    it('nên ném lỗi khi tìm thông tin chi tiết thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      (mockQueryBuilder.getOne as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.findByIdWithDetails(folderId)).rejects.toThrow(
        `Lỗi khi tìm thư mục chi tiết theo ID ${folderId}: Database error`
      );
    });
  });

  describe('findByParentIdAndName', () => {
    it('nên tìm thư mục theo parentId và name thành công khi parentId không null', async () => {
      // Arrange
      const parentId = 1;
      const name = 'Thư mục 2';
      const userId = 1;
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(mockFolders[1]);

      // Act
      const result = await repository.findByParentIdAndName(parentId, name, userId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('folder.name = :name', { name });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('folder.userId = :userId', { userId });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('folder.parentId = :parentId', { parentId });
      expect(result).toEqual(mockFolders[1]);
    });

    it('nên tìm thư mục theo parentId và name thành công khi parentId là null', async () => {
      // Arrange
      const parentId = null;
      const name = 'Thư mục 1';
      const userId = 1;
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(mockFolders[0]);

      // Act
      const result = await repository.findByParentIdAndName(parentId, name, userId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('folder.name = :name', { name });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('folder.userId = :userId', { userId });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('folder.parentId IS NULL');
      expect(result).toEqual(mockFolders[0]);
    });

    it('nên ném lỗi khi tìm thư mục theo parentId và name thất bại', async () => {
      // Arrange
      const parentId = 1;
      const name = 'Thư mục 2';
      const userId = 1;
      (mockQueryBuilder.getOne as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.findByParentIdAndName(parentId, name, userId)).rejects.toThrow(
        `Lỗi khi tìm thư mục theo ID thư mục cha ${parentId} và tên ${name}: Database error`
      );
    });
  });

  describe('findRootFoldersByUserId', () => {
    it('nên tìm danh sách thư mục gốc theo userId thành công', async () => {
      // Arrange
      const userId = 1;
      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValue([mockFolders[0]]);

      // Act
      const result = await repository.findRootFoldersByUserId(userId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('folder.userId = :userId', { userId });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('folder.parentId IS NULL');
      expect(result).toEqual([mockFolders[0]]);
    });

    it('nên ném lỗi khi tìm danh sách thư mục gốc thất bại', async () => {
      // Arrange
      const userId = 1;
      (mockQueryBuilder.getMany as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.findRootFoldersByUserId(userId)).rejects.toThrow(
        `Lỗi khi tìm thư mục gốc theo ID người dùng ${userId}: Database error`
      );
    });
  });

  describe('findAll', () => {
    it('nên tìm danh sách thư mục với phân trang thành công', async () => {
      // Arrange
      const queryParams = {
        page: 1,
        limit: 10,
        search: 'thư mục',
        userId: 1,
        parentId: null,
        sortBy: 'name',
        sortDirection: 'ASC',
      };

      // Reset các mock
      (mockQueryBuilder.andWhere as jest.Mock).mockClear();
      (mockQueryBuilder.orderBy as jest.Mock).mockClear();
      (mockQueryBuilder.skip as jest.Mock).mockClear();
      (mockQueryBuilder.take as jest.Mock).mockClear();

      // Mock getCount và getMany
      (mockQueryBuilder.getCount as jest.Mock).mockResolvedValueOnce(mockFolders.length);
      (mockQueryBuilder.getMany as jest.Mock).mockResolvedValueOnce(mockFolders);

      // Act
      const result = await repository.findAll(queryParams);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('folder.name ILIKE :search', { search: '%thư mục%' });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('folder.userId = :userId', { userId: 1 });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('folder.parentId IS NULL');
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('folder.name', 'ASC');
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
      expect(result).toEqual({
        items: mockFolders,
        meta: {
          totalItems: mockFolders.length,
          itemCount: mockFolders.length,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      });
    });

    it('nên ném lỗi khi tìm danh sách thư mục thất bại', async () => {
      // Arrange
      const queryParams = {
        page: 1,
        limit: 10,
      };
      (mockQueryBuilder.getManyAndCount as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.findAll(queryParams)).rejects.toThrow('Lỗi khi tìm kiếm thư mục: Database error');
    });
  });

  describe('updateFolder', () => {
    it('nên cập nhật thư mục thành công', async () => {
      // Arrange
      const folderId = 1;
      const updateData = {
        name: 'Thư mục đã cập nhật',
      };

      // Mock findById để trả về folder sau khi cập nhật
      currentFolderId = null; // Reset ID
      (mockQueryBuilder.getOne as jest.Mock).mockImplementation(() => {
        if (currentFolderId === folderId) {
          return Promise.resolve(mockFolders[0]);
        }
        return Promise.resolve(null);
      });

      // Act
      const result = await repository.updateFolder(folderId, updateData);

      // Assert
      expect(repository.update).toHaveBeenCalledWith({ id: folderId }, updateData);
      expect(result).toEqual(mockFolders[0]);
    });

    it('nên ném lỗi khi cập nhật thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      const updateData = {
        name: 'Thư mục đã cập nhật',
      };
      jest.spyOn(repository, 'update').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.updateFolder(folderId, updateData)).rejects.toThrow(
        `Lỗi khi cập nhật thư mục với ID ${folderId}: Database error`
      );
    });
  });

  describe('deleteFolder', () => {
    it('nên xóa thư mục thành công', async () => {
      // Arrange
      const folderId = 1;

      // Mock delete để trả về kết quả xóa thành công
      jest.spyOn(repository, 'delete').mockResolvedValueOnce({ affected: 1, raw: {} });

      // Act
      const result = await repository.deleteFolder(folderId);

      // Assert
      expect(repository.delete).toHaveBeenCalledWith({ id: folderId });
      expect(result).toBe(true);
    });

    it('nên ném lỗi khi xóa thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      jest.spyOn(repository, 'delete').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.deleteFolder(folderId)).rejects.toThrow(
        `Lỗi khi xóa thư mục với ID ${folderId}: Database error`
      );
    });
  });
});
