import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { PhysicalWarehouseRepository } from '../../../repositories';
import { PhysicalWarehouse, Warehouse, WarehouseCustomField } from '../../../entities';
import { WarehouseTypeEnum } from '../../../enums';
import { QueryPhysicalWarehouseDto } from '../../dto/warehouse';
import { SortDirection } from '../../../../../common/dto';

describe('PhysicalWarehouseRepository', () => {
  let repository: PhysicalWarehouseRepository;
  let dataSource: DataSource;

  // Mock data
  const mockPhysicalWarehouses: PhysicalWarehouse[] = [
    {
      warehouseId: 1,
      address: '123 Đường ABC, Quận 1, TP.HCM',
      capacity: 1000,
    },
    {
      warehouseId: 2,
      address: '456 Đường XYZ, Quận 2, TP.HCM',
      capacity: 2000,
    },
  ];

  const mockWarehouses: Warehouse[] = [
    {
      warehouseId: 1,
      name: 'Kho hàng 1',
      description: 'Mô tả kho hàng 1',
      type: WarehouseTypeEnum.PHYSICAL,
    },
    {
      warehouseId: 2,
      name: 'Kho hàng 2',
      description: 'Mô tả kho hàng 2',
      type: WarehouseTypeEnum.PHYSICAL,
    },
  ];

  const mockCustomFields: WarehouseCustomField[] = [
    {
      warehouseId: 1,
      fieldId: 1,
      value: { value: 'Giá trị 1' },
    },
    {
      warehouseId: 1,
      fieldId: 2,
      value: { value: 'Giá trị 2' },
    },
  ];

  // Mock query builder
  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    innerJoin: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getRawOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
    getRawMany: jest.fn().mockImplementation(() => Promise.resolve([])),
    getCount: jest.fn().mockImplementation(() => Promise.resolve(0)),
    getManyAndCount: jest.fn().mockImplementation(() => Promise.resolve([[], 0])),
    getMany: jest.fn().mockImplementation(() => Promise.resolve([])),
    getOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
  } as unknown as SelectQueryBuilder<PhysicalWarehouse>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PhysicalWarehouseRepository,
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
      ],
    }).compile();

    repository = module.get<PhysicalWarehouseRepository>(PhysicalWarehouseRepository);
    dataSource = module.get<DataSource>(DataSource);

    // Mock các phương thức của repository
    jest.spyOn(repository, 'create').mockImplementation((data: any) => data as PhysicalWarehouse);
    jest.spyOn(repository, 'save').mockImplementation((physicalWarehouse: PhysicalWarehouse) => Promise.resolve(physicalWarehouse));
    jest.spyOn(repository, 'findOne').mockImplementation((options: any) => {
      const warehouseId = options.where?.warehouseId;
      const physicalWarehouse = mockPhysicalWarehouses.find(pw => pw.warehouseId === warehouseId);
      return Promise.resolve(physicalWarehouse || null);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPhysicalWarehouse', () => {
    it('nên tạo kho vật lý mới thành công', async () => {
      // Arrange
      const newPhysicalWarehouse: PhysicalWarehouse = {
        warehouseId: 3,
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      };

      // Act
      const result = await repository.createPhysicalWarehouse(newPhysicalWarehouse);

      // Assert
      expect(repository.save).toHaveBeenCalledWith(newPhysicalWarehouse);
      expect(result).toEqual(newPhysicalWarehouse);
    });

    it('nên ném lỗi khi tạo kho vật lý thất bại', async () => {
      // Arrange
      const newPhysicalWarehouse: PhysicalWarehouse = {
        warehouseId: 3,
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      };

      jest.spyOn(repository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.createPhysicalWarehouse(newPhysicalWarehouse)).rejects.toThrow(
        `Lỗi khi tạo kho vật lý: Database error`
      );
    });
  });



  describe('findByWarehouseIdWithDetails', () => {
    it('nên ném lỗi khi tìm kho vật lý với thông tin chi tiết thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      jest.spyOn(mockQueryBuilder, 'getRawOne').mockImplementation(() => Promise.reject(new Error('Database error')));

      // Act & Assert
      await expect(repository.findByWarehouseIdWithDetails(warehouseId)).rejects.toThrow(
        `Lỗi khi tìm kho vật lý với thông tin chi tiết theo ID ${warehouseId}: Database error`
      );
    });
  });

  describe('findByWarehouseId', () => {
    it('nên tìm kho vật lý theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;
      jest.spyOn(repository, 'findOne').mockResolvedValue(mockPhysicalWarehouses[0]);

      // Mock createBaseQuery để tránh lỗi
      const mockGetOne = jest.fn().mockResolvedValue(mockPhysicalWarehouses[0]);
      const mockWhere = jest.fn().mockReturnValue({ getOne: mockGetOne });
      jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue({ where: mockWhere });

      // Act
      const result = await repository.findByWarehouseId(warehouseId);

      // Assert
      expect(result).toEqual(mockPhysicalWarehouses[0]);
    });
  });

  describe('createBaseQuery', () => {
    it('nên tạo query builder cơ bản', () => {
      // Arrange
      jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder);

      // Act
      const result = (repository as any).createBaseQuery();

      // Assert
      expect(repository.createQueryBuilder).toHaveBeenCalledWith('physical_warehouse');
      expect(result).toBe(mockQueryBuilder);
    });
  });

  describe('updatePhysicalWarehouse', () => {
    it('nên cập nhật kho vật lý thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateData = {
        address: '123 Đường ABC (đã cập nhật), Quận 1, TP.HCM',
        capacity: 1500,
      };
      jest.spyOn(repository, 'update').mockResolvedValue({ affected: 1, raw: {} } as any);
      jest.spyOn(repository, 'findByWarehouseId').mockResolvedValue({
        ...mockPhysicalWarehouses[0],
        ...updateData
      });

      // Act
      const result = await repository.updatePhysicalWarehouse(warehouseId, updateData);

      // Assert
      expect(repository.update).toHaveBeenCalledWith({ warehouseId }, updateData);
      expect(result).toEqual({
        ...mockPhysicalWarehouses[0],
        ...updateData
      });
    });
  });

  describe('deletePhysicalWarehouse', () => {
    it('nên xóa kho vật lý thành công', async () => {
      // Arrange
      const warehouseId = 1;
      jest.spyOn(repository, 'delete').mockResolvedValue({ affected: 1, raw: {} } as any);

      // Act
      const result = await repository.deletePhysicalWarehouse(warehouseId);

      // Assert
      expect(repository.delete).toHaveBeenCalledWith({ warehouseId });
      expect(result).toBeTruthy();
    });
  });

  describe('findAll', () => {
    it('nên tìm danh sách kho vật lý với phân trang thành công', async () => {
      // Arrange
      const queryParams = {
        page: 1,
        limit: 10,
        sortBy: 'warehouseId',
        sortDirection: 'ASC',
      };

      // Mock các phương thức cần thiết
      jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder);
      jest.spyOn(mockQueryBuilder, 'getCount').mockResolvedValue(2);
      jest.spyOn(mockQueryBuilder, 'getMany').mockResolvedValue(mockPhysicalWarehouses);

      // Act
      const result = await repository.findAll(queryParams);

      // Assert
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('physical_warehouse.warehouseId', 'ASC');
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
      expect(result).toEqual({
        items: mockPhysicalWarehouses,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      });
    });
  });






});
