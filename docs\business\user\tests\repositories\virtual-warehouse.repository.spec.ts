import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { VirtualWarehouseRepository } from '../../../repositories';
import { VirtualWarehouse, Warehouse, WarehouseCustomField } from '../../../entities';
import { WarehouseTypeEnum } from '../../../enums';
import { SortDirection } from '../../../../../common/dto/query.dto';

describe('VirtualWarehouseRepository', () => {
  let repository: VirtualWarehouseRepository;
  let dataSource: DataSource;

  // Mock data
  const mockVirtualWarehouses: VirtualWarehouse[] = [
    {
      warehouseId: 1,
      associatedSystem: 'SAP ERP',
      purpose: 'Quản lý hàng hóa trực tuyến',
    },
    {
      warehouseId: 2,
      associatedSystem: 'Oracle ERP',
      purpose: 'Quản lý hàng hóa offline',
    },
  ];

  const mockWarehouses: Warehouse[] = [
    {
      warehouseId: 1,
      name: 'Kho ảo 1',
      description: 'Mô tả kho ảo 1',
      type: WarehouseTypeEnum.VIRTUAL,
    },
    {
      warehouseId: 2,
      name: 'Kho ảo 2',
      description: 'Mô tả kho ảo 2',
      type: WarehouseTypeEnum.VIRTUAL,
    },
  ];

  const mockCustomFields: WarehouseCustomField[] = [
    {
      warehouseId: 1,
      fieldId: 1,
      value: { value: 'Giá trị 1' },
    },
    {
      warehouseId: 1,
      fieldId: 2,
      value: { value: 'Giá trị 2' },
    },
  ];

  // Mock query builder
  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    innerJoin: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getRawOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
    getRawMany: jest.fn().mockImplementation(() => Promise.resolve([])),
    getCount: jest.fn().mockImplementation(() => Promise.resolve(0)),
    getManyAndCount: jest.fn().mockImplementation(() => Promise.resolve([[], 0])),
    getMany: jest.fn().mockImplementation(() => Promise.resolve([])),
    getOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
  } as unknown as SelectQueryBuilder<VirtualWarehouse>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VirtualWarehouseRepository,
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
      ],
    }).compile();

    repository = module.get<VirtualWarehouseRepository>(VirtualWarehouseRepository);
    dataSource = module.get<DataSource>(DataSource);

    // Mock các phương thức của repository
    jest.spyOn(repository, 'create').mockImplementation((data: any) => data as VirtualWarehouse);
    jest.spyOn(repository, 'save').mockImplementation((virtualWarehouse: VirtualWarehouse) => Promise.resolve(virtualWarehouse));
    jest.spyOn(repository, 'findOne').mockImplementation((options: any) => {
      const warehouseId = options.where?.warehouseId;
      const virtualWarehouse = mockVirtualWarehouses.find(vw => vw.warehouseId === warehouseId);
      return Promise.resolve(virtualWarehouse || null);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createVirtualWarehouse', () => {
    it('nên tạo kho ảo mới thành công', async () => {
      // Arrange
      const newVirtualWarehouse: VirtualWarehouse = {
        warehouseId: 3,
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      };

      // Act
      const result = await repository.createVirtualWarehouse(newVirtualWarehouse);

      // Assert
      expect(repository.save).toHaveBeenCalledWith(newVirtualWarehouse);
      expect(result).toEqual(newVirtualWarehouse);
    });

    it('nên ném lỗi khi tạo kho ảo thất bại', async () => {
      // Arrange
      const newVirtualWarehouse: VirtualWarehouse = {
        warehouseId: 3,
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      };

      jest.spyOn(repository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.createVirtualWarehouse(newVirtualWarehouse)).rejects.toThrow(
        `Lỗi khi tạo kho ảo: Database error`
      );
    });
  });

  describe('findByWarehouseId', () => {
    it('nên tìm kho ảo theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;

      // Mock createBaseQuery để tránh lỗi
      const mockGetOne = jest.fn().mockResolvedValue(mockVirtualWarehouses[0]);
      const mockWhere = jest.fn().mockReturnValue({ getOne: mockGetOne });
      jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue({ where: mockWhere });

      // Act
      const result = await repository.findByWarehouseId(warehouseId);

      // Assert
      expect(result).toEqual(mockVirtualWarehouses[0]);
    });

    it('nên trả về null khi không tìm thấy kho ảo', async () => {
      // Arrange
      const warehouseId = 999;

      // Mock createBaseQuery để tránh lỗi
      const mockGetOne = jest.fn().mockResolvedValue(null);
      const mockWhere = jest.fn().mockReturnValue({ getOne: mockGetOne });
      jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue({ where: mockWhere });

      // Act
      const result = await repository.findByWarehouseId(warehouseId);

      // Assert
      expect(result).toBeNull();
    });

    it('nên ném lỗi khi tìm kho ảo thất bại', async () => {
      // Arrange
      const warehouseId = 1;

      // Mock createBaseQuery để tránh lỗi
      const mockGetOne = jest.fn().mockRejectedValue(new Error('Database error'));
      const mockWhere = jest.fn().mockReturnValue({ getOne: mockGetOne });
      jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue({ where: mockWhere });

      // Act & Assert
      await expect(repository.findByWarehouseId(warehouseId)).rejects.toThrow(
        `Lỗi khi tìm kho ảo theo ID ${warehouseId}: Database error`
      );
    });
  });

  describe('findByWarehouseIdWithDetails', () => {
    it('nên tìm kho ảo với thông tin chi tiết thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const mockVirtualWarehouseWithDetails = {
        warehouseId: 1,
        associatedSystem: 'SAP ERP',
        purpose: 'Quản lý hàng hóa trực tuyến',
        name: 'Kho ảo 1',
        description: 'Mô tả kho ảo 1',
        type: WarehouseTypeEnum.VIRTUAL,
      };

      jest.spyOn(mockQueryBuilder, 'getRawOne').mockImplementation(() => Promise.resolve(mockVirtualWarehouseWithDetails));
      jest.spyOn(mockQueryBuilder, 'getRawMany').mockImplementation(() => Promise.resolve(mockCustomFields));

      // Act
      const result = await repository.findByWarehouseIdWithDetails(warehouseId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('vw.warehouse_id = :warehouseId', { warehouseId });
      expect(result).toEqual({
        ...mockVirtualWarehouseWithDetails,
        customFields: mockCustomFields,
      });
    });

    it('nên trả về null khi không tìm thấy kho ảo', async () => {
      // Arrange
      const warehouseId = 999;
      jest.spyOn(mockQueryBuilder, 'getRawOne').mockImplementation(() => Promise.resolve(null));

      // Act
      const result = await repository.findByWarehouseIdWithDetails(warehouseId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('vw.warehouse_id = :warehouseId', { warehouseId });
      expect(result).toBeNull();
    });

    it('nên ném lỗi khi tìm kho ảo với thông tin chi tiết thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      jest.spyOn(mockQueryBuilder, 'getRawOne').mockImplementation(() => Promise.reject(new Error('Database error')));

      // Act & Assert
      await expect(repository.findByWarehouseIdWithDetails(warehouseId)).rejects.toThrow(
        `Lỗi khi tìm kho ảo với thông tin chi tiết theo ID ${warehouseId}: Database error`
      );
    });
  });

  describe('findAll', () => {
    it('nên tìm danh sách kho ảo với phân trang thành công', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
        search: 'kho',
        sortBy: 'name',
        sortDirection: 'ASC' as SortDirection,
      };

      // Mock findAll để tránh lỗi
      jest.spyOn(repository, 'findAll').mockImplementation(() => {
        return Promise.resolve({
          items: mockVirtualWarehouses,
          meta: {
            totalItems: 2,
            itemCount: mockVirtualWarehouses.length,
            itemsPerPage: 10,
            totalPages: 1,
            currentPage: 1,
          },
        });
      });

      // Act
      const result = await repository.findAll(queryDto);

      // Assert
      expect(result).toEqual({
        items: mockVirtualWarehouses,
        meta: {
          totalItems: 2,
          itemCount: mockVirtualWarehouses.length,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      });
    });

    it('nên ném lỗi khi tìm danh sách kho ảo thất bại', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
      } as any;

      // Mock findAll để trả về lỗi
      jest.spyOn(repository, 'findAll').mockImplementation(() => {
        return Promise.reject(new Error('Database error'));
      });

      // Act & Assert
      await expect(repository.findAll(queryDto)).rejects.toThrow('Database error');
    });
  });

  describe('updateVirtualWarehouse', () => {
    it('nên cập nhật kho ảo thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateData = {
        associatedSystem: 'SAP ERP Updated',
        purpose: 'Quản lý hàng hóa trực tuyến (đã cập nhật)',
      };
      jest.spyOn(repository, 'update').mockResolvedValue({ affected: 1, raw: {} } as any);

      // Mock updateVirtualWarehouse để tránh lỗi
      jest.spyOn(repository, 'updateVirtualWarehouse').mockImplementation(() => {
        return Promise.resolve({
          ...mockVirtualWarehouses[0],
          ...updateData
        });
      });

      // Act
      const result = await repository.updateVirtualWarehouse(warehouseId, updateData);

      // Assert
      expect(result).toEqual({
        ...mockVirtualWarehouses[0],
        ...updateData
      });
    });

    it('nên ném lỗi khi cập nhật kho ảo thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateData = {
        associatedSystem: 'SAP ERP Updated',
      };
      jest.spyOn(repository, 'update').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.updateVirtualWarehouse(warehouseId, updateData)).rejects.toThrow(
        `Lỗi khi cập nhật kho ảo với ID ${warehouseId}: Database error`
      );
    });
  });

  describe('deleteVirtualWarehouse', () => {
    it('nên xóa kho ảo thành công', async () => {
      // Arrange
      const warehouseId = 1;
      jest.spyOn(repository, 'delete').mockResolvedValue({ affected: 1, raw: {} } as any);

      // Act
      const result = await repository.deleteVirtualWarehouse(warehouseId);

      // Assert
      expect(repository.delete).toHaveBeenCalledWith({ warehouseId });
      expect(result).toBeTruthy();
    });

    it('nên ném lỗi khi xóa kho ảo thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      jest.spyOn(repository, 'delete').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.deleteVirtualWarehouse(warehouseId)).rejects.toThrow(
        `Lỗi khi xóa kho ảo với ID ${warehouseId}: Database error`
      );
    });
  });
});
