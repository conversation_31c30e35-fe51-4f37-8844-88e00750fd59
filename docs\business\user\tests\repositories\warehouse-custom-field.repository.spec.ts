import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, DeleteResult, SelectQueryBuilder } from 'typeorm';
import { WarehouseCustomFieldRepository } from '../../../repositories';
import { WarehouseCustomField } from '../../../entities';

describe('WarehouseCustomFieldRepository', () => {
  let repository: WarehouseCustomFieldRepository;
  let dataSource: DataSource;

  // Mock data
  const mockCustomFields: WarehouseCustomField[] = [
    {
      warehouseId: 1,
      fieldId: 1,
      value: { value: 'Giá trị 1' },
    },
    {
      warehouseId: 1,
      fieldId: 2,
      value: { value: 'Giá trị 2' },
    },
    {
      warehouseId: 2,
      fieldId: 1,
      value: { value: 'Giá trị 3' },
    },
  ];

  // Mock query builder
  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
    getMany: jest.fn().mockImplementation(() => Promise.resolve([])),
  } as unknown as SelectQueryBuilder<WarehouseCustomField>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WarehouseCustomFieldRepository,
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
      ],
    }).compile();

    repository = module.get<WarehouseCustomFieldRepository>(WarehouseCustomFieldRepository);
    dataSource = module.get<DataSource>(DataSource);

    // Mock các phương thức của repository
    jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder);
    jest.spyOn(repository, 'create').mockImplementation((data: any) => data as WarehouseCustomField);
    jest.spyOn(repository, 'save').mockImplementation((customField: WarehouseCustomField) => Promise.resolve(customField));
    jest.spyOn(repository, 'findOne').mockImplementation((options: any) => {
      const warehouseId = options.where?.warehouseId;
      const fieldId = options.where?.fieldId;
      const customField = mockCustomFields.find(cf => cf.warehouseId === warehouseId && cf.fieldId === fieldId);
      return Promise.resolve(customField || null);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createBaseQuery', () => {
    it('nên tạo query builder cơ bản', () => {
      // Act
      const result = (repository as any).createBaseQuery();

      // Assert
      expect(repository.createQueryBuilder).toHaveBeenCalledWith('warehouseCustomField');
      expect(result).toBe(mockQueryBuilder);
    });
  });

  describe('findByWarehouseIdAndFieldId', () => {
    it('nên tìm trường tùy chỉnh theo warehouseId và fieldId thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.resolve(mockCustomFields[0]));

      // Act
      const result = await repository.findByWarehouseIdAndFieldId(warehouseId, fieldId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('warehouseCustomField.warehouseId = :warehouseId', { warehouseId });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('warehouseCustomField.fieldId = :fieldId', { fieldId });
      expect(result).toEqual(mockCustomFields[0]);
    });

    it('nên trả về null khi không tìm thấy trường tùy chỉnh', async () => {
      // Arrange
      const warehouseId = 999;
      const fieldId = 999;
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.resolve(null));

      // Act
      const result = await repository.findByWarehouseIdAndFieldId(warehouseId, fieldId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('warehouseCustomField.warehouseId = :warehouseId', { warehouseId });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('warehouseCustomField.fieldId = :fieldId', { fieldId });
      expect(result).toBeNull();
    });

    it('nên ném lỗi khi tìm trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.reject(new Error('Database error')));

      // Act & Assert
      await expect(repository.findByWarehouseIdAndFieldId(warehouseId, fieldId)).rejects.toThrow(
        `Lỗi khi tìm trường tùy chỉnh của kho theo warehouseId=${warehouseId} và fieldId=${fieldId}: Database error`
      );
    });
  });

  describe('findByWarehouseId', () => {
    it('nên tìm danh sách trường tùy chỉnh theo warehouseId thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const expectedCustomFields = mockCustomFields.filter(cf => cf.warehouseId === warehouseId);
      jest.spyOn(mockQueryBuilder, 'getMany').mockImplementation(() => Promise.resolve(expectedCustomFields));

      // Act
      const result = await repository.findByWarehouseId(warehouseId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('warehouseCustomField.warehouseId = :warehouseId', { warehouseId });
      expect(result).toEqual(expectedCustomFields);
    });

    it('nên trả về mảng rỗng khi không tìm thấy trường tùy chỉnh', async () => {
      // Arrange
      const warehouseId = 999;
      jest.spyOn(mockQueryBuilder, 'getMany').mockImplementation(() => Promise.resolve([]));

      // Act
      const result = await repository.findByWarehouseId(warehouseId);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('warehouseCustomField.warehouseId = :warehouseId', { warehouseId });
      expect(result).toEqual([]);
    });

    it('nên ném lỗi khi tìm danh sách trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      jest.spyOn(mockQueryBuilder, 'getMany').mockImplementation(() => Promise.reject(new Error('Database error')));

      // Act & Assert
      await expect(repository.findByWarehouseId(warehouseId)).rejects.toThrow(
        `Lỗi khi lấy danh sách trường tùy chỉnh của kho theo warehouseId=${warehouseId}: Database error`
      );
    });
  });

  describe('createCustomField', () => {
    it('nên tạo trường tùy chỉnh mới thành công', async () => {
      // Arrange
      const warehouseId = 2;
      const fieldId = 2;
      const value = { value: 'Giá trị mới' };
      const newCustomField = {
        warehouseId,
        fieldId,
        value,
      };

      // Act
      const result = await repository.createCustomField(warehouseId, fieldId, value);

      // Assert
      expect(repository.create).toHaveBeenCalledWith(newCustomField);
      expect(repository.save).toHaveBeenCalledWith(newCustomField);
      expect(result).toEqual(newCustomField);
    });

    it('nên ném lỗi khi tạo trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 2;
      const fieldId = 2;
      const value = { value: 'Giá trị mới' };
      jest.spyOn(repository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.createCustomField(warehouseId, fieldId, value)).rejects.toThrow(
        `Lỗi khi tạo trường tùy chỉnh cho kho với warehouseId=${warehouseId} và fieldId=${fieldId}: Database error`
      );
    });
  });

  describe('updateCustomField', () => {
    it('nên cập nhật trường tùy chỉnh thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const value = { value: 'Giá trị đã cập nhật' };
      const existingCustomField = { ...mockCustomFields[0] };
      const updatedCustomField = { ...existingCustomField, value };

      jest.spyOn(repository, 'findByWarehouseIdAndFieldId').mockResolvedValue(existingCustomField);

      // Act
      const result = await repository.updateCustomField(warehouseId, fieldId, value);

      // Assert
      expect(repository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
      expect(repository.save).toHaveBeenCalledWith(updatedCustomField);
      expect(result).toEqual(updatedCustomField);
    });

    it('nên ném lỗi khi không tìm thấy trường tùy chỉnh để cập nhật', async () => {
      // Arrange
      const warehouseId = 999;
      const fieldId = 999;
      const value = { value: 'Giá trị đã cập nhật' };
      jest.spyOn(repository, 'findByWarehouseIdAndFieldId').mockResolvedValue(null);

      // Act & Assert
      await expect(repository.updateCustomField(warehouseId, fieldId, value)).rejects.toThrow(
        `Không tìm thấy trường tùy chỉnh với warehouseId=${warehouseId} và fieldId=${fieldId}`
      );
    });

    it('nên ném lỗi khi cập nhật trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const value = { value: 'Giá trị đã cập nhật' };
      const existingCustomField = { ...mockCustomFields[0] };

      jest.spyOn(repository, 'findByWarehouseIdAndFieldId').mockResolvedValue(existingCustomField);
      jest.spyOn(repository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.updateCustomField(warehouseId, fieldId, value)).rejects.toThrow(
        `Lỗi khi cập nhật trường tùy chỉnh của kho với warehouseId=${warehouseId} và fieldId=${fieldId}: Database error`
      );
    });
  });

  describe('deleteByWarehouseIdAndFieldId', () => {
    it('nên xóa trường tùy chỉnh thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;

      // Mock createBaseQuery để tránh lỗi
      const mockExecute = jest.fn().mockResolvedValue({ affected: 1 });
      const mockAndWhere = jest.fn().mockReturnValue({ execute: mockExecute });
      const mockWhere = jest.fn().mockReturnValue({ andWhere: mockAndWhere });
      const mockDelete = jest.fn().mockReturnValue({ where: mockWhere });

      jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue({
        delete: mockDelete
      });

      // Act
      const result = await repository.deleteByWarehouseIdAndFieldId(warehouseId, fieldId);

      // Assert
      expect(mockDelete).toHaveBeenCalled();
      expect(mockWhere).toHaveBeenCalledWith('warehouseCustomField.warehouseId = :warehouseId', { warehouseId });
      expect(mockAndWhere).toHaveBeenCalledWith('warehouseCustomField.fieldId = :fieldId', { fieldId });
      expect(result).toBe(1);
    });

    it('nên ném lỗi khi xóa trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;

      // Mock createBaseQuery để gây ra lỗi
      jest.spyOn(repository as any, 'createBaseQuery').mockImplementation(() => {
        throw new Error('Database error');
      });

      // Act & Assert
      await expect(repository.deleteByWarehouseIdAndFieldId(warehouseId, fieldId)).rejects.toThrow(
        `Lỗi khi xóa trường tùy chỉnh của kho theo warehouseId=${warehouseId} và fieldId=${fieldId}: Database error`
      );
    });
  });
});
