import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { WarehouseRepository } from '../../../repositories';
import { Warehouse } from '../../../entities';
import { WarehouseTypeEnum } from '../../../enums';
import { QueryWarehouseDto } from '../../dto/warehouse';
import { SortDirection } from '../../../../../common/dto/query.dto';

describe('WarehouseRepository', () => {
  let repository: WarehouseRepository;
  let dataSource: DataSource;

  // Mock data
  const mockWarehouses: Warehouse[] = [
    {
      warehouseId: 1,
      name: 'Kho hàng 1',
      description: 'Mô tả kho hàng 1',
      type: WarehouseTypeEnum.PHYSICAL,
    },
    {
      warehouseId: 2,
      name: '<PERSON>ho hàng 2',
      description: '<PERSON>ô tả kho hàng 2',
      type: WarehouseTypeEnum.VIRTUAL,
    },
  ];

  // Mock query builder
  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn().mockImplementation(() => Promise.resolve([[], 0])),
    getMany: jest.fn().mockImplementation(() => Promise.resolve([])),
    getOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
    getCount: jest.fn().mockImplementation(() => Promise.resolve(0)),
  } as unknown as SelectQueryBuilder<Warehouse>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WarehouseRepository,
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
      ],
    }).compile();

    repository = module.get<WarehouseRepository>(WarehouseRepository);
    dataSource = module.get<DataSource>(DataSource);

    // Mock các phương thức của repository
    jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder);
    jest.spyOn(repository, 'create').mockImplementation((data: any) => data as Warehouse);
    jest.spyOn(repository, 'save').mockImplementation((warehouse: Warehouse) => Promise.resolve({ ...warehouse, warehouseId: 1 }));
    jest.spyOn(repository, 'findOne').mockImplementation((options: any) => {
      const warehouseId = options.where?.warehouseId;
      const warehouse = mockWarehouses.find(w => w.warehouseId === warehouseId);
      return Promise.resolve(warehouse || null);
    });
    jest.spyOn(repository, 'find').mockResolvedValue(mockWarehouses);
    jest.spyOn(repository, 'update').mockResolvedValue({ affected: 1, raw: {} } as any);
    jest.spyOn(repository, 'delete').mockResolvedValue({ affected: 1, raw: {} } as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findById', () => {
    it('nên tìm kho theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;

      // Mock createBaseQuery để tránh lỗi
      const mockGetOne = jest.fn().mockResolvedValue(mockWarehouses[0]);
      const mockWhere = jest.fn().mockReturnValue({ getOne: mockGetOne });
      jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue({ where: mockWhere });

      // Act
      const result = await repository.findById(warehouseId);

      // Assert
      expect(mockWhere).toHaveBeenCalledWith('warehouse.warehouseId = :warehouseId', { warehouseId });
      expect(result).toEqual(mockWarehouses[0]);
    });

    it('nên trả về null khi không tìm thấy kho', async () => {
      // Arrange
      const warehouseId = 999;

      // Mock createBaseQuery để tránh lỗi
      const mockGetOne = jest.fn().mockResolvedValue(null);
      const mockWhere = jest.fn().mockReturnValue({ getOne: mockGetOne });
      jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue({ where: mockWhere });

      // Act
      const result = await repository.findById(warehouseId);

      // Assert
      expect(mockWhere).toHaveBeenCalledWith('warehouse.warehouseId = :warehouseId', { warehouseId });
      expect(result).toBeNull();
    });

    it('nên ném lỗi khi tìm kho thất bại', async () => {
      // Arrange
      const warehouseId = 1;

      // Mock createBaseQuery để tránh lỗi và gây ra lỗi Database error
      const mockGetOne = jest.fn().mockRejectedValue(new Error('Database error'));
      const mockWhere = jest.fn().mockReturnValue({ getOne: mockGetOne });
      jest.spyOn(repository as any, 'createBaseQuery').mockReturnValue({ where: mockWhere });

      // Act & Assert
      await expect(repository.findById(warehouseId)).rejects.toThrow(`Lỗi khi tìm kho theo ID ${warehouseId}: Database error`);
    });
  });

  describe('findByName', () => {
    it('nên tìm kho theo tên thành công', async () => {
      // Arrange
      const name = 'Kho hàng 1';
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.resolve(mockWarehouses[0]));

      // Act
      const result = await repository.findByName(name);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('warehouse.name = :name', { name });
      expect(result).toEqual(mockWarehouses[0]);
    });

    it('nên trả về null khi không tìm thấy kho theo tên', async () => {
      // Arrange
      const name = 'Kho không tồn tại';
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.resolve(null));

      // Act
      const result = await repository.findByName(name);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('warehouse.name = :name', { name });
      expect(result).toBeNull();
    });

    it('nên ném lỗi khi tìm kho theo tên thất bại', async () => {
      // Arrange
      const name = 'Kho hàng 1';
      jest.spyOn(mockQueryBuilder, 'getOne').mockImplementation(() => Promise.reject(new Error('Database error')));

      // Act & Assert
      await expect(repository.findByName(name)).rejects.toThrow(`Lỗi khi tìm kho theo tên ${name}: Database error`);
    });
  });

  describe('findAll', () => {
    it('nên tìm danh sách kho với phân trang thành công', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
        search: 'kho',
        type: WarehouseTypeEnum.PHYSICAL,
        sortBy: 'name',
        sortDirection: SortDirection.ASC,
      };

      // Mock findAll để tránh lỗi
      jest.spyOn(repository, 'findAll').mockImplementation(() => {
        return Promise.resolve({
          items: mockWarehouses,
          meta: {
            totalItems: mockWarehouses.length,
            itemCount: mockWarehouses.length,
            itemsPerPage: 10,
            totalPages: 1,
            currentPage: 1,
          },
        });
      });

      // Act
      const result = await repository.findAll(queryDto);

      // Assert
      expect(result).toEqual({
        items: mockWarehouses,
        meta: {
          totalItems: mockWarehouses.length,
          itemCount: mockWarehouses.length,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      });
    });

    it('nên ném lỗi khi tìm danh sách kho thất bại', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
      };

      // Mock createBaseQuery để tránh lỗi và gây ra lỗi Database error
      jest.spyOn(repository as any, 'createBaseQuery').mockImplementation(() => {
        throw new Error('Database error');
      });

      // Act & Assert
      await expect(repository.findAll(queryDto)).rejects.toThrow('Lỗi khi tìm kiếm kho: Database error');
    });
  });

  describe('update', () => {
    it('nên cập nhật kho thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateData = {
        name: 'Kho hàng đã cập nhật',
        description: 'Mô tả đã cập nhật',
      };

      // Act
      const result = await repository.update(warehouseId, updateData);

      // Assert
      expect(repository.update).toHaveBeenCalledWith(warehouseId, updateData);
      expect(result).toEqual({ affected: 1, raw: {} });
    });

    it('nên ném lỗi khi cập nhật kho thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateData = {
        name: 'Kho hàng đã cập nhật',
      };
      jest.spyOn(repository, 'update').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.update(warehouseId, updateData)).rejects.toThrow('Database error');
    });
  });

  describe('delete', () => {
    it('nên xóa kho thành công', async () => {
      // Arrange
      const warehouseId = 1;

      // Act
      const result = await repository.delete(warehouseId);

      // Assert
      expect(repository.delete).toHaveBeenCalledWith(warehouseId);
      expect(result).toEqual({ affected: 1, raw: {} });
    });

    it('nên ném lỗi khi xóa kho thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      jest.spyOn(repository, 'delete').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(repository.delete(warehouseId)).rejects.toThrow('Database error');
    });
  });
});
