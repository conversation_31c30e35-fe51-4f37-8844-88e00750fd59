import { Test, TestingModule } from '@nestjs/testing';
import { UserFileService } from '../../services/user-file.service';
import { FileRepository } from '../../../repositories';
import { ValidationHelper } from '../../helpers/validation.helper';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CreateFileDto, UpdateFileDto, QueryFileDto } from '../../dto/file';
import { File } from '../../../entities';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';
import { plainToInstance } from 'class-transformer';
import { FileResponseDto } from '../../dto/file/file-response.dto';

describe('UserFileService', () => {
  let service: UserFileService;
  let fileRepository: FileRepository;
  let validationHelper: ValidationHelper;
  let s3Service: S3Service;
  let cdnService: CdnService;

  // Mock data
  const mockFiles: File[] = [
    {
      id: 1,
      name: 'File 1.pdf',
      folderId: 1,
      storageKey: 'files/2023/05/file1.pdf',
      size: 1024,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      warehouseId: 1, // Trường này không có trong database, chỉ dùng trong code
    },
    {
      id: 2,
      name: 'File 2.docx',
      folderId: 1,
      storageKey: 'files/2023/05/file2.docx',
      size: 2048,
      createdAt: 1625097700000,
      updatedAt: 1625097700000,
      warehouseId: 1, // Trường này không có trong database, chỉ dùng trong code
    },
  ];

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    role: 'user',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserFileService,
        {
          provide: FileRepository,
          useValue: {
            createFile: jest.fn(),
            findById: jest.fn(),
            findByFolderId: jest.fn(),
            findAll: jest.fn(),
            updateFile: jest.fn(),
            deleteFile: jest.fn(),
          },
        },
        {
          provide: ValidationHelper,
          useValue: {
            validateCreateFile: jest.fn(),
            validateFileExists: jest.fn(),
            validateUpdateFile: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            createPresignedWithID: jest.fn(),
          },
        },
        {
          provide: CdnService,
          useValue: {
            generateUrlView: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserFileService>(UserFileService);
    fileRepository = module.get<FileRepository>(FileRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
    s3Service = module.get<S3Service>(S3Service);
    cdnService = module.get<CdnService>(CdnService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createFile', () => {
    it('nên tạo file mới thành công', async () => {
      // Arrange
      const createDto: CreateFileDto = {
        name: 'New File.pdf',
        folderId: 1,
        size: 1024,
      };
      const warehouseId = 1;
      const s3Key = 'files/2023/05/new-file.pdf';
      const uploadUrl = 'https://s3.example.com/upload-url';
      const viewUrl = 'https://cdn.example.com/view-url';
      const savedFile = { ...createDto, id: 3, storageKey: s3Key, createdAt: 1625097800000, updatedAt: 1625097800000 };

      jest.spyOn(validationHelper, 'validateCreateFile').mockResolvedValue(undefined);
      jest.spyOn(fileRepository, 'createFile').mockResolvedValue(savedFile as File);
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue(uploadUrl);
      jest.spyOn(cdnService, 'generateUrlView').mockReturnValue(viewUrl);

      // Act
      const result = await service.createFile(createDto);

      // Assert
      expect(validationHelper.validateCreateFile).toHaveBeenCalledWith(createDto);
      expect(fileRepository.createFile).toHaveBeenCalled();
      expect(s3Service.createPresignedWithID).toHaveBeenCalled();
      expect(cdnService.generateUrlView).toHaveBeenCalled();
      expect(result).toBeInstanceOf(FileResponseDto);
      expect(result.id).toBe(3);
      expect(result.name).toBe('New File.pdf');
      expect(result.folderId).toBe(1);
      expect(result.warehouseId).toBe(1);
      expect(result.storageKey).toBe(s3Key);
      expect(result.size).toBe(1024);
      expect(result.viewUrl).toBe(viewUrl);
      expect(result.uploadUrl).toBe(uploadUrl);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const createDto: CreateFileDto = {
        name: 'New File.pdf',
        folderId: 1,
        size: 1024,
      };
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_CREATION_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateCreateFile').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createFile(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateFile).toHaveBeenCalledWith(createDto);
    });

    it('nên ném lỗi khi tạo file thất bại', async () => {
      // Arrange
      const createDto: CreateFileDto = {
        name: 'New File.pdf',
        folderId: 1,
        size: 1024,
      };
      const warehouseId = 1;
      const s3Key = 'files/2023/05/new-file.pdf';

      jest.spyOn(validationHelper, 'validateCreateFile').mockResolvedValue(undefined);
      jest.spyOn(fileRepository, 'createFile').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.createFile(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateFile).toHaveBeenCalledWith(createDto);
      expect(fileRepository.createFile).toHaveBeenCalled();
    });
  });

  describe('updateFile', () => {
    it('nên cập nhật file thành công', async () => {
      // Arrange
      const fileId = 1;
      const updateDto: UpdateFileDto = {
        name: 'Updated File.pdf',
      };
      const existingFile = { ...mockFiles[0], name: 'File 1.pdf' };
      const updatedFile = { ...existingFile, name: 'Updated File.pdf', updatedAt: 1625097900000 };
      const viewUrl = 'https://cdn.example.com/view-url';

      jest.spyOn(validationHelper, 'validateFileExists').mockResolvedValue(existingFile as File);
      jest.spyOn(validationHelper, 'validateUpdateFile').mockResolvedValue(undefined);
      jest.spyOn(fileRepository, 'updateFile').mockResolvedValue(updatedFile as File);
      jest.spyOn(fileRepository, 'findById').mockResolvedValue(updatedFile as File);
      jest.spyOn(cdnService, 'generateUrlView').mockReturnValue(viewUrl);

      // Act
      const result = await service.updateFile(fileId, updateDto);

      // Assert
      expect(validationHelper.validateFileExists).toHaveBeenCalledWith(fileId);
      expect(validationHelper.validateUpdateFile).toHaveBeenCalledWith(updateDto, existingFile);
      expect(fileRepository.updateFile).toHaveBeenCalledWith(fileId, { name: 'Updated File.pdf' });
      expect(fileRepository.findById).toHaveBeenCalledWith(fileId);
      expect(cdnService.generateUrlView).toHaveBeenCalled();
      expect(result).toBeInstanceOf(FileResponseDto);
      expect(result.id).toBe(1);
      expect(result.name).toBe('Updated File.pdf');
    });

    it('nên ném lỗi khi file không tồn tại', async () => {
      // Arrange
      const fileId = 999;
      const updateDto: UpdateFileDto = {
        name: 'Updated File.pdf',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_NOT_FOUND, 'File không tồn tại');

      jest.spyOn(validationHelper, 'validateFileExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateFile(fileId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateFileExists).toHaveBeenCalledWith(fileId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const fileId = 1;
      const updateDto: UpdateFileDto = {
        name: 'Updated File.pdf',
      };
      const existingFile = { ...mockFiles[0], name: 'File 1.pdf' };
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_UPDATE_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateFileExists').mockResolvedValue(existingFile as File);
      jest.spyOn(validationHelper, 'validateUpdateFile').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateFile(fileId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateFileExists).toHaveBeenCalledWith(fileId);
      expect(validationHelper.validateUpdateFile).toHaveBeenCalledWith(updateDto, existingFile);
    });
  });

  describe('getFileById', () => {
    it('nên lấy thông tin file theo ID thành công', async () => {
      // Arrange
      const fileId = 1;
      const viewUrl = 'https://cdn.example.com/view-url';

      jest.spyOn(validationHelper, 'validateFileExists').mockResolvedValue(mockFiles[0] as File);
      jest.spyOn(cdnService, 'generateUrlView').mockReturnValue(viewUrl);

      // Act
      const result = await service.getFileById(fileId);

      // Assert
      expect(validationHelper.validateFileExists).toHaveBeenCalledWith(fileId);
      expect(cdnService.generateUrlView).toHaveBeenCalled();
      expect(result).toBeInstanceOf(FileResponseDto);
      expect(result.id).toBe(1);
      expect(result.name).toBe('File 1.pdf');
      expect(result.viewUrl).toBe(viewUrl);
    });

    it('nên ném lỗi khi file không tồn tại', async () => {
      // Arrange
      const fileId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_NOT_FOUND, 'File không tồn tại');

      jest.spyOn(validationHelper, 'validateFileExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.getFileById(fileId)).rejects.toThrow(AppException);
      expect(validationHelper.validateFileExists).toHaveBeenCalledWith(fileId);
    });
  });

  describe('getFiles', () => {
    it('nên lấy danh sách file với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryFileDto = {
        page: 1,
        limit: 10,
        warehouseId: 1,
      };
      const viewUrl = 'https://cdn.example.com/view-url';
      const paginatedResult = {
        items: mockFiles,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(fileRepository, 'findAll').mockResolvedValue(paginatedResult);
      jest.spyOn(cdnService, 'generateUrlView').mockReturnValue(viewUrl);

      // Act
      const result = await service.getFiles(queryDto);

      // Assert
      expect(fileRepository.findAll).toHaveBeenCalledWith(queryDto);
      expect(cdnService.generateUrlView).toHaveBeenCalledTimes(2);
      expect(result.items.length).toBe(2);
      expect(result.items[0]).toBeInstanceOf(FileResponseDto);
      expect(result.items[0].id).toBe(1);
      expect(result.items[0].name).toBe('File 1.pdf');
      expect(result.items[0].viewUrl).toBe(viewUrl);
      expect(result.meta.totalItems).toBe(2);
    });

    it('nên ném lỗi khi lấy danh sách file thất bại', async () => {
      // Arrange
      const queryDto: QueryFileDto = {
        page: 1,
        limit: 10,
      };

      jest.spyOn(fileRepository, 'findAll').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getFiles(queryDto)).rejects.toThrow(AppException);
      expect(fileRepository.findAll).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('getFilesByFolderId', () => {
    it('nên lấy danh sách file theo ID thư mục thành công', async () => {
      // Arrange
      const folderId = 1;
      const viewUrl = 'https://cdn.example.com/view-url';

      jest.spyOn(fileRepository, 'findByFolderId').mockResolvedValue(mockFiles);
      jest.spyOn(cdnService, 'generateUrlView').mockReturnValue(viewUrl);

      // Act
      const result = await service.getFilesByFolderId(folderId);

      // Assert
      expect(fileRepository.findByFolderId).toHaveBeenCalledWith(folderId);
      expect(cdnService.generateUrlView).toHaveBeenCalledTimes(2);
      expect(result.length).toBe(2);
      expect(result[0]).toBeInstanceOf(FileResponseDto);
      expect(result[0].id).toBe(1);
      expect(result[0].name).toBe('File 1.pdf');
      expect(result[0].viewUrl).toBe(viewUrl);
    });

    it('nên ném lỗi khi lấy danh sách file theo ID thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(fileRepository, 'findByFolderId').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getFilesByFolderId(folderId)).rejects.toThrow(AppException);
      expect(fileRepository.findByFolderId).toHaveBeenCalledWith(folderId);
    });
  });

  describe('deleteFile', () => {
    it('nên xóa file thành công', async () => {
      // Arrange
      const fileId = 1;

      jest.spyOn(validationHelper, 'validateFileExists').mockResolvedValue(mockFiles[0] as File);
      jest.spyOn(fileRepository, 'deleteFile').mockResolvedValue(true);

      // Act
      await service.deleteFile(fileId);

      // Assert
      expect(validationHelper.validateFileExists).toHaveBeenCalledWith(fileId);
      expect(fileRepository.deleteFile).toHaveBeenCalledWith(fileId);
    });

    it('nên ném lỗi khi file không tồn tại', async () => {
      // Arrange
      const fileId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_NOT_FOUND, 'File không tồn tại');

      jest.spyOn(validationHelper, 'validateFileExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.deleteFile(fileId)).rejects.toThrow(AppException);
      expect(validationHelper.validateFileExists).toHaveBeenCalledWith(fileId);
    });

    it('nên ném lỗi khi xóa file thất bại', async () => {
      // Arrange
      const fileId = 1;

      jest.spyOn(validationHelper, 'validateFileExists').mockResolvedValue(mockFiles[0] as File);
      jest.spyOn(fileRepository, 'deleteFile').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.deleteFile(fileId)).rejects.toThrow(AppException);
      expect(validationHelper.validateFileExists).toHaveBeenCalledWith(fileId);
      expect(fileRepository.deleteFile).toHaveBeenCalledWith(fileId);
    });
  });
});
