import { Test, TestingModule } from '@nestjs/testing';
import { UserInventoryService } from '../../services/user-inventory.service';
import { InventoryRepository, WarehouseRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../../helpers/validation.helper';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CreateInventoryDto, UpdateInventoryDto, QueryInventoryDto } from '../../dto/inventory';
import { Inventory, Warehouse } from '@modules/business/entities';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { plainToInstance } from 'class-transformer';
import { InventoryResponseDto } from '../../dto/inventory/inventory-response.dto';

describe('UserInventoryService', () => {
  let service: UserInventoryService;
  let inventoryRepository: InventoryRepository;
  let warehouseRepository: WarehouseRepository;
  let validationHelper: ValidationHelper;

  // Mock data
  const mockInventories: Inventory[] = [
    {
      id: 1,
      productId: 1,
      warehouseId: 1,
      currentQuantity: 100,
      totalQuantity: 150,
      availableQuantity: 90,
      reservedQuantity: 5,
      defectiveQuantity: 5,
      lastUpdated: 1715270400000,
    },
    {
      id: 2,
      productId: 2,
      warehouseId: 1,
      currentQuantity: 200,
      totalQuantity: 250,
      availableQuantity: 180,
      reservedQuantity: 10,
      defectiveQuantity: 10,
      lastUpdated: 1715270500000,
    },
    {
      id: 3,
      productId: 1,
      warehouseId: 2,
      currentQuantity: 300,
      totalQuantity: 350,
      availableQuantity: 270,
      reservedQuantity: 15,
      defectiveQuantity: 15,
      lastUpdated: 1715270600000,
    },
  ];

  const mockWarehouses: Warehouse[] = [
    {
      warehouseId: 1,
      name: 'Kho hàng 1',
      description: 'Mô tả kho hàng 1',
      type: WarehouseTypeEnum.PHYSICAL,
    },
    {
      warehouseId: 2,
      name: 'Kho hàng 2',
      description: 'Mô tả kho hàng 2',
      type: WarehouseTypeEnum.PHYSICAL,
    },
  ];

  const mockInventoryWithWarehouse = {
    ...mockInventories[0],
    warehouse: mockWarehouses[0],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserInventoryService,
        {
          provide: InventoryRepository,
          useValue: {
            createInventory: jest.fn(),
            findById: jest.fn(),
            findByProductAndWarehouse: jest.fn(),
            findAll: jest.fn(),
            updateInventory: jest.fn(),
            deleteInventory: jest.fn(),
          },
        },
        {
          provide: WarehouseRepository,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: ValidationHelper,
          useValue: {
            validateCreateInventory: jest.fn(),
            validateInventoryExists: jest.fn(),
            validateUpdateInventory: jest.fn(),
            validateWarehouseExists: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserInventoryService>(UserInventoryService);
    inventoryRepository = module.get<InventoryRepository>(InventoryRepository);
    warehouseRepository = module.get<WarehouseRepository>(WarehouseRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createInventory', () => {
    it('nên tạo tồn kho mới thành công', async () => {
      // Arrange
      const createDto: CreateInventoryDto = {
        productId: 3,
        warehouseId: 1,
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
      };
      const existingWarehouse = mockWarehouses[0];
      const newInventory = {
        id: 4,
        productId: 3,
        warehouseId: 1,
        currentQuantity: 120,
        totalQuantity: 170,
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
        lastUpdated: expect.any(Number),
      };

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(validationHelper, 'validateCreateInventory').mockResolvedValue(undefined);
      jest.spyOn(inventoryRepository, 'createInventory').mockResolvedValue(newInventory as Inventory);
      jest.spyOn(inventoryRepository, 'findById').mockResolvedValue({ ...newInventory, warehouse: existingWarehouse } as any);

      // Act
      const result = await service.createInventory(createDto);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(createDto.warehouseId);
      expect(validationHelper.validateCreateInventory).toHaveBeenCalledWith(createDto);
      expect(inventoryRepository.createInventory).toHaveBeenCalledWith(expect.objectContaining({
        productId: 3,
        warehouseId: 1,
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
        currentQuantity: 120,
        totalQuantity: 170,
        lastUpdated: expect.any(Number),
      }));
      expect(inventoryRepository.findById).toHaveBeenCalledWith(4);
      expect(result).toBeInstanceOf(InventoryResponseDto);
      expect(result.id).toBe(4);
      expect(result.productId).toBe(3);
      expect(result.warehouseId).toBe(1);
      expect(result.availableQuantity).toBe(100);
      expect(result.reservedQuantity).toBe(10);
      expect(result.defectiveQuantity).toBe(10);
      expect(result.currentQuantity).toBe(120);
      expect(result.totalQuantity).toBe(170);
      expect(result.warehouse).toBeDefined();
      expect(result.warehouse.warehouseId).toBe(1);
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const createDto: CreateInventoryDto = {
        productId: 3,
        warehouseId: 999,
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createInventory(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(createDto.warehouseId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const createDto: CreateInventoryDto = {
        productId: 3,
        warehouseId: 1,
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
      };
      const existingWarehouse = mockWarehouses[0];
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(validationHelper, 'validateCreateInventory').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createInventory(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(createDto.warehouseId);
      expect(validationHelper.validateCreateInventory).toHaveBeenCalledWith(createDto);
    });
  });

  describe('updateInventory', () => {
    it('nên cập nhật tồn kho thành công', async () => {
      // Arrange
      const inventoryId = 1;
      const updateDto: UpdateInventoryDto = {
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
      };
      const existingInventory = mockInventories[0];
      const updatedInventory = {
        ...existingInventory,
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
        currentQuantity: 120,
        totalQuantity: 170,
        lastUpdated: expect.any(Number),
      };

      jest.spyOn(validationHelper, 'validateInventoryExists').mockResolvedValue(existingInventory);
      jest.spyOn(validationHelper, 'validateUpdateInventory').mockResolvedValue(undefined);
      jest.spyOn(inventoryRepository, 'updateInventory').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
      jest.spyOn(inventoryRepository, 'findById').mockResolvedValue({ ...updatedInventory, warehouse: mockWarehouses[0] } as any);

      // Act
      const result = await service.updateInventory(inventoryId, updateDto);

      // Assert
      expect(validationHelper.validateInventoryExists).toHaveBeenCalledWith(inventoryId);
      expect(validationHelper.validateUpdateInventory).toHaveBeenCalledWith(updateDto, existingInventory);
      expect(inventoryRepository.updateInventory).toHaveBeenCalledWith(inventoryId, expect.objectContaining({
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
        currentQuantity: 120,
        totalQuantity: 170,
        lastUpdated: expect.any(Number),
      }));
      expect(inventoryRepository.findById).toHaveBeenCalledWith(inventoryId);
      expect(result).toBeInstanceOf(InventoryResponseDto);
      expect(result.id).toBe(1);
      expect(result.availableQuantity).toBe(100);
      expect(result.reservedQuantity).toBe(10);
      expect(result.defectiveQuantity).toBe(10);
      expect(result.currentQuantity).toBe(120);
      expect(result.totalQuantity).toBe(170);
      expect(result.warehouse).toBeDefined();
      expect(result.warehouse.warehouseId).toBe(1);
    });

    it('nên ném lỗi khi tồn kho không tồn tại', async () => {
      // Arrange
      const inventoryId = 999;
      const updateDto: UpdateInventoryDto = {
        availableQuantity: 100,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND, 'Tồn kho không tồn tại');

      jest.spyOn(validationHelper, 'validateInventoryExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateInventory(inventoryId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateInventoryExists).toHaveBeenCalledWith(inventoryId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const inventoryId = 1;
      const updateDto: UpdateInventoryDto = {
        availableQuantity: 100,
      };
      const existingInventory = mockInventories[0];
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_UPDATE_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateInventoryExists').mockResolvedValue(existingInventory);
      jest.spyOn(validationHelper, 'validateUpdateInventory').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateInventory(inventoryId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateInventoryExists).toHaveBeenCalledWith(inventoryId);
      expect(validationHelper.validateUpdateInventory).toHaveBeenCalledWith(updateDto, existingInventory);
    });
  });

  describe('getInventoryById', () => {
    it('nên lấy thông tin tồn kho theo ID thành công', async () => {
      // Arrange
      const inventoryId = 1;

      jest.spyOn(inventoryRepository, 'findById').mockResolvedValue(mockInventoryWithWarehouse as any);

      // Act
      const result = await service.getInventoryById(inventoryId);

      // Assert
      expect(inventoryRepository.findById).toHaveBeenCalledWith(inventoryId);
      expect(result).toBeInstanceOf(InventoryResponseDto);
      expect(result.id).toBe(1);
      expect(result.productId).toBe(1);
      expect(result.warehouseId).toBe(1);
      expect(result.availableQuantity).toBe(90);
      expect(result.reservedQuantity).toBe(5);
      expect(result.defectiveQuantity).toBe(5);
      expect(result.currentQuantity).toBe(100);
      expect(result.totalQuantity).toBe(150);
      expect(result.warehouse).toBeDefined();
      expect(result.warehouse.warehouseId).toBe(1);
    });

    it('nên ném lỗi khi tồn kho không tồn tại', async () => {
      // Arrange
      const inventoryId = 999;

      jest.spyOn(inventoryRepository, 'findById').mockResolvedValue(null);

      // Act & Assert
      await expect(service.getInventoryById(inventoryId)).rejects.toThrow(AppException);
      expect(inventoryRepository.findById).toHaveBeenCalledWith(inventoryId);
    });

    it('nên ném lỗi khi lấy thông tin tồn kho thất bại', async () => {
      // Arrange
      const inventoryId = 1;

      jest.spyOn(inventoryRepository, 'findById').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getInventoryById(inventoryId)).rejects.toThrow(AppException);
      expect(inventoryRepository.findById).toHaveBeenCalledWith(inventoryId);
    });
  });

  describe('getInventoryByProductAndWarehouse', () => {
    it('nên lấy thông tin tồn kho theo productId và warehouseId thành công', async () => {
      // Arrange
      const productId = 1;
      const warehouseId = 1;

      jest.spyOn(inventoryRepository, 'findByProductAndWarehouse').mockResolvedValue(mockInventoryWithWarehouse as any);

      // Act
      const result = await service.getInventoryByProductAndWarehouse(productId, warehouseId);

      // Assert
      expect(inventoryRepository.findByProductAndWarehouse).toHaveBeenCalledWith(productId, warehouseId);
      expect(result).toBeInstanceOf(InventoryResponseDto);
      expect(result.productId).toBe(1);
      expect(result.warehouseId).toBe(1);
      expect(result.warehouse).toBeDefined();
      expect(result.warehouse.warehouseId).toBe(1);
    });

    it('nên ném lỗi khi tồn kho không tồn tại', async () => {
      // Arrange
      const productId = 999;
      const warehouseId = 999;

      jest.spyOn(inventoryRepository, 'findByProductAndWarehouse').mockResolvedValue(null);

      // Act & Assert
      await expect(service.getInventoryByProductAndWarehouse(productId, warehouseId)).rejects.toThrow(AppException);
      expect(inventoryRepository.findByProductAndWarehouse).toHaveBeenCalledWith(productId, warehouseId);
    });

    it('nên ném lỗi khi lấy thông tin tồn kho thất bại', async () => {
      // Arrange
      const productId = 1;
      const warehouseId = 1;

      jest.spyOn(inventoryRepository, 'findByProductAndWarehouse').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getInventoryByProductAndWarehouse(productId, warehouseId)).rejects.toThrow(AppException);
      expect(inventoryRepository.findByProductAndWarehouse).toHaveBeenCalledWith(productId, warehouseId);
    });
  });

  describe('getInventories', () => {
    it('nên lấy danh sách tồn kho với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryInventoryDto = {
        page: 1,
        limit: 10,
        productId: 1,
        warehouseId: 1,
        sortBy: 'currentQuantity',
        sortDirection: 'DESC',
      };
      const paginatedResult = {
        items: [mockInventoryWithWarehouse],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(inventoryRepository, 'findAll').mockResolvedValue(paginatedResult);

      // Act
      const result = await service.getInventories(queryDto);

      // Assert
      expect(inventoryRepository.findAll).toHaveBeenCalledWith(queryDto);
      expect(result.items.length).toBe(1);
      expect(result.items[0]).toBeInstanceOf(InventoryResponseDto);
      expect(result.items[0].id).toBe(1);
      expect(result.items[0].productId).toBe(1);
      expect(result.items[0].warehouseId).toBe(1);
      expect(result.meta.totalItems).toBe(1);
    });

    it('nên ném lỗi khi lấy danh sách tồn kho thất bại', async () => {
      // Arrange
      const queryDto: QueryInventoryDto = {
        page: 1,
        limit: 10,
      };

      jest.spyOn(inventoryRepository, 'findAll').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getInventories(queryDto)).rejects.toThrow(AppException);
      expect(inventoryRepository.findAll).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('deleteInventory', () => {
    it('nên xóa tồn kho thành công', async () => {
      // Arrange
      const inventoryId = 1;
      const existingInventory = mockInventories[0];

      jest.spyOn(validationHelper, 'validateInventoryExists').mockResolvedValue(existingInventory);
      jest.spyOn(inventoryRepository, 'deleteInventory').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

      // Act
      await service.deleteInventory(inventoryId);

      // Assert
      expect(validationHelper.validateInventoryExists).toHaveBeenCalledWith(inventoryId);
      expect(inventoryRepository.deleteInventory).toHaveBeenCalledWith(inventoryId);
    });

    it('nên ném lỗi khi tồn kho không tồn tại', async () => {
      // Arrange
      const inventoryId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND, 'Tồn kho không tồn tại');

      jest.spyOn(validationHelper, 'validateInventoryExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.deleteInventory(inventoryId)).rejects.toThrow(AppException);
      expect(validationHelper.validateInventoryExists).toHaveBeenCalledWith(inventoryId);
    });

    it('nên ném lỗi khi xóa tồn kho thất bại', async () => {
      // Arrange
      const inventoryId = 1;
      const existingInventory = mockInventories[0];

      jest.spyOn(validationHelper, 'validateInventoryExists').mockResolvedValue(existingInventory);
      jest.spyOn(inventoryRepository, 'deleteInventory').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.deleteInventory(inventoryId)).rejects.toThrow(AppException);
      expect(validationHelper.validateInventoryExists).toHaveBeenCalledWith(inventoryId);
      expect(inventoryRepository.deleteInventory).toHaveBeenCalledWith(inventoryId);
    });
  });
});
