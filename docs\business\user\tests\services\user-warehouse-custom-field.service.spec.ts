import { Test, TestingModule } from '@nestjs/testing';
import { UserWarehouseCustomFieldService } from '../../services/user-warehouse-custom-field.service';
import { WarehouseCustomFieldRepository, WarehouseRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../../helpers/validation.helper';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CreateWarehouseCustomFieldDto, UpdateWarehouseCustomFieldDto } from '../../dto/warehouse';
import { WarehouseCustomField, Warehouse } from '@modules/business/entities';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { plainToInstance } from 'class-transformer';
import { WarehouseCustomFieldResponseDto } from '../../dto/warehouse/warehouse-custom-field-response.dto';

describe('UserWarehouseCustomFieldService', () => {
  let service: UserWarehouseCustomFieldService;
  let warehouseCustomFieldRepository: WarehouseCustomFieldRepository;
  let warehouseRepository: WarehouseRepository;
  let validationHelper: ValidationHelper;

  // Mock data
  const mockCustomFields: WarehouseCustomField[] = [
    {
      warehouseId: 1,
      fieldId: 1,
      value: { value: 'Giá trị 1' },
    },
    {
      warehouseId: 1,
      fieldId: 2,
      value: { value: 'Giá trị 2' },
    },
    {
      warehouseId: 2,
      fieldId: 1,
      value: { value: 'Giá trị 3' },
    },
  ];

  const mockWarehouses: Warehouse[] = [
    {
      warehouseId: 1,
      name: 'Kho hàng 1',
      description: 'Mô tả kho hàng 1',
      type: WarehouseTypeEnum.PHYSICAL,
    },
    {
      warehouseId: 2,
      name: 'Kho hàng 2',
      description: 'Mô tả kho hàng 2',
      type: WarehouseTypeEnum.VIRTUAL,
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserWarehouseCustomFieldService,
        {
          provide: WarehouseCustomFieldRepository,
          useValue: {
            findByWarehouseIdAndFieldId: jest.fn(),
            findByWarehouseId: jest.fn(),
            createCustomField: jest.fn(),
            updateCustomField: jest.fn(),
            deleteCustomField: jest.fn(),
          },
        },
        {
          provide: WarehouseRepository,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: ValidationHelper,
          useValue: {
            validateWarehouseExists: jest.fn(),
            validateCreateWarehouseCustomField: jest.fn(),
            validateUpdateWarehouseCustomField: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserWarehouseCustomFieldService>(UserWarehouseCustomFieldService);
    warehouseCustomFieldRepository = module.get<WarehouseCustomFieldRepository>(WarehouseCustomFieldRepository);
    warehouseRepository = module.get<WarehouseRepository>(WarehouseRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createWarehouseCustomField', () => {
    it('nên tạo trường tùy chỉnh mới thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const createDto: CreateWarehouseCustomFieldDto = {
        fieldId: 3,
        value: { value: 'Giá trị mới' },
      };
      const existingWarehouse = mockWarehouses[0];
      const newCustomField = {
        warehouseId: 1,
        fieldId: 3,
        value: { value: 'Giá trị mới' },
      };

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(validationHelper, 'validateCreateWarehouseCustomField').mockResolvedValue(undefined);
      jest.spyOn(warehouseCustomFieldRepository, 'createCustomField').mockResolvedValue(newCustomField as WarehouseCustomField);

      // Act
      const result = await service.createWarehouseCustomField(warehouseId, createDto);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateCreateWarehouseCustomField).toHaveBeenCalledWith(warehouseId, createDto);
      expect(warehouseCustomFieldRepository.createCustomField).toHaveBeenCalledWith(warehouseId, createDto.fieldId, createDto.value);
      expect(result).toBeInstanceOf(WarehouseCustomFieldResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.fieldId).toBe(3);
      expect(result.value).toEqual({ value: 'Giá trị mới' });
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const createDto: CreateWarehouseCustomFieldDto = {
        fieldId: 3,
        value: { value: 'Giá trị mới' },
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createWarehouseCustomField(warehouseId, createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const createDto: CreateWarehouseCustomFieldDto = {
        fieldId: 3,
        value: { value: 'Giá trị mới' },
      };
      const existingWarehouse = mockWarehouses[0];
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_CREATION_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(validationHelper, 'validateCreateWarehouseCustomField').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createWarehouseCustomField(warehouseId, createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateCreateWarehouseCustomField).toHaveBeenCalledWith(warehouseId, createDto);
    });

    it('nên ném lỗi khi tạo trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const createDto: CreateWarehouseCustomFieldDto = {
        fieldId: 3,
        value: { value: 'Giá trị mới' },
      };
      const existingWarehouse = mockWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(validationHelper, 'validateCreateWarehouseCustomField').mockResolvedValue(undefined);
      jest.spyOn(warehouseCustomFieldRepository, 'createCustomField').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.createWarehouseCustomField(warehouseId, createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateCreateWarehouseCustomField).toHaveBeenCalledWith(warehouseId, createDto);
      expect(warehouseCustomFieldRepository.createCustomField).toHaveBeenCalledWith(warehouseId, createDto.fieldId, createDto.value);
    });
  });

  describe('updateWarehouseCustomField', () => {
    it('nên cập nhật trường tùy chỉnh thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const updateDto: UpdateWarehouseCustomFieldDto = {
        value: { value: 'Giá trị đã cập nhật' },
      };
      const existingWarehouse = mockWarehouses[0];
      const existingCustomField = mockCustomFields[0];
      const updatedCustomField = {
        ...existingCustomField,
        value: { value: 'Giá trị đã cập nhật' },
      };

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseIdAndFieldId').mockResolvedValue(existingCustomField);
      jest.spyOn(validationHelper, 'validateUpdateWarehouseCustomField').mockResolvedValue(undefined);
      jest.spyOn(warehouseCustomFieldRepository, 'updateCustomField').mockResolvedValue(updatedCustomField as WarehouseCustomField);

      // Act
      const result = await service.updateWarehouseCustomField(warehouseId, fieldId, updateDto);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
      expect(validationHelper.validateUpdateWarehouseCustomField).toHaveBeenCalledWith(updateDto, existingCustomField);
      expect(warehouseCustomFieldRepository.updateCustomField).toHaveBeenCalledWith(warehouseId, fieldId, updateDto.value);
      expect(result).toBeInstanceOf(WarehouseCustomFieldResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.fieldId).toBe(1);
      expect(result.value).toEqual({ value: 'Giá trị đã cập nhật' });
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const fieldId = 1;
      const updateDto: UpdateWarehouseCustomFieldDto = {
        value: { value: 'Giá trị đã cập nhật' },
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateWarehouseCustomField(warehouseId, fieldId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi trường tùy chỉnh không tồn tại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 999;
      const updateDto: UpdateWarehouseCustomFieldDto = {
        value: { value: 'Giá trị đã cập nhật' },
      };
      const existingWarehouse = mockWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseIdAndFieldId').mockResolvedValue(null);

      // Act & Assert
      await expect(service.updateWarehouseCustomField(warehouseId, fieldId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const updateDto: UpdateWarehouseCustomFieldDto = {
        value: { value: 'Giá trị đã cập nhật' },
      };
      const existingWarehouse = mockWarehouses[0];
      const existingCustomField = mockCustomFields[0];
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_UPDATE_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseIdAndFieldId').mockResolvedValue(existingCustomField);
      jest.spyOn(validationHelper, 'validateUpdateWarehouseCustomField').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateWarehouseCustomField(warehouseId, fieldId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
      expect(validationHelper.validateUpdateWarehouseCustomField).toHaveBeenCalledWith(updateDto, existingCustomField);
    });
  });

  describe('getWarehouseCustomFieldById', () => {
    it('nên lấy thông tin trường tùy chỉnh theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const existingWarehouse = mockWarehouses[0];
      const existingCustomField = mockCustomFields[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseIdAndFieldId').mockResolvedValue(existingCustomField);

      // Act
      const result = await service.getWarehouseCustomFieldById(warehouseId, fieldId);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
      expect(result).toBeInstanceOf(WarehouseCustomFieldResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.fieldId).toBe(1);
      expect(result.value).toEqual({ value: 'Giá trị 1' });
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const fieldId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.getWarehouseCustomFieldById(warehouseId, fieldId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi trường tùy chỉnh không tồn tại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 999;
      const existingWarehouse = mockWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseIdAndFieldId').mockResolvedValue(null);

      // Act & Assert
      await expect(service.getWarehouseCustomFieldById(warehouseId, fieldId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
    });
  });

  describe('getWarehouseCustomFields', () => {
    it('nên lấy danh sách trường tùy chỉnh theo warehouseId thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const existingWarehouse = mockWarehouses[0];
      const customFields = mockCustomFields.filter(cf => cf.warehouseId === warehouseId);

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseId').mockResolvedValue(customFields);

      // Act
      const result = await service.getWarehouseCustomFields(warehouseId);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(result.length).toBe(2);
      expect(result[0]).toBeInstanceOf(WarehouseCustomFieldResponseDto);
      expect(result[0].warehouseId).toBe(1);
      expect(result[0].fieldId).toBe(1);
      expect(result[0].value).toEqual({ value: 'Giá trị 1' });
      expect(result[1].warehouseId).toBe(1);
      expect(result[1].fieldId).toBe(2);
      expect(result[1].value).toEqual({ value: 'Giá trị 2' });
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.getWarehouseCustomFields(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên trả về mảng rỗng khi không có trường tùy chỉnh nào', async () => {
      // Arrange
      const warehouseId = 3;
      const existingWarehouse = {
        warehouseId: 3,
        name: 'Kho hàng 3',
        description: 'Mô tả kho hàng 3',
        type: WarehouseTypeEnum.PHYSICAL,
      };

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse as Warehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseId').mockResolvedValue([]);

      // Act
      const result = await service.getWarehouseCustomFields(warehouseId);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(result).toEqual([]);
    });
  });

  describe('deleteWarehouseCustomField', () => {
    it('nên xóa trường tùy chỉnh thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const existingWarehouse = mockWarehouses[0];
      const existingCustomField = mockCustomFields[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseIdAndFieldId').mockResolvedValue(existingCustomField);
      jest.spyOn(warehouseCustomFieldRepository, 'deleteCustomField').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

      // Act
      await service.deleteWarehouseCustomField(warehouseId, fieldId);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
      expect(warehouseCustomFieldRepository.deleteCustomField).toHaveBeenCalledWith(warehouseId, fieldId);
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const fieldId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.deleteWarehouseCustomField(warehouseId, fieldId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi trường tùy chỉnh không tồn tại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 999;
      const existingWarehouse = mockWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseIdAndFieldId').mockResolvedValue(null);

      // Act & Assert
      await expect(service.deleteWarehouseCustomField(warehouseId, fieldId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
    });

    it('nên ném lỗi khi xóa trường tùy chỉnh thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const fieldId = 1;
      const existingWarehouse = mockWarehouses[0];
      const existingCustomField = mockCustomFields[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(warehouseCustomFieldRepository, 'findByWarehouseIdAndFieldId').mockResolvedValue(existingCustomField);
      jest.spyOn(warehouseCustomFieldRepository, 'deleteCustomField').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.deleteWarehouseCustomField(warehouseId, fieldId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseCustomFieldRepository.findByWarehouseIdAndFieldId).toHaveBeenCalledWith(warehouseId, fieldId);
      expect(warehouseCustomFieldRepository.deleteCustomField).toHaveBeenCalledWith(warehouseId, fieldId);
    });
  });
});
