import { Test, TestingModule } from '@nestjs/testing';
import { UserWarehouseService } from '../../services/user-warehouse.service';
import { WarehouseRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../../helpers/validation.helper';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CreateWarehouseDto, UpdateWarehouseDto, QueryWarehouseDto } from '../../dto/warehouse';
import { Warehouse } from '@modules/business/entities';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { plainToInstance } from 'class-transformer';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';

describe('UserWarehouseService', () => {
  let service: UserWarehouseService;
  let warehouseRepository: WarehouseRepository;
  let validationHelper: ValidationHelper;

  // Mock data
  const mockWarehouses: Warehouse[] = [
    {
      warehouseId: 1,
      name: 'Kho hàng 1',
      description: 'Mô tả kho hàng 1',
      type: WarehouseTypeEnum.PHYSICAL,
    },
    {
      warehouseId: 2,
      name: 'Kho hàng 2',
      description: 'Mô tả kho hàng 2',
      type: WarehouseTypeEnum.VIRTUAL,
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserWarehouseService,
        {
          provide: WarehouseRepository,
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findById: jest.fn(),
            findByName: jest.fn(),
            findAll: jest.fn(),
            updateWarehouse: jest.fn(),
            deleteWarehouse: jest.fn(),
          },
        },
        {
          provide: ValidationHelper,
          useValue: {
            validateCreateWarehouse: jest.fn(),
            validateWarehouseExists: jest.fn(),
            validateUpdateWarehouse: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserWarehouseService>(UserWarehouseService);
    warehouseRepository = module.get<WarehouseRepository>(WarehouseRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createWarehouse', () => {
    it('nên tạo kho mới thành công', async () => {
      // Arrange
      const createDto: CreateWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      };
      const newWarehouse = {
        warehouseId: 3,
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      };

      jest.spyOn(validationHelper, 'validateCreateWarehouse').mockResolvedValue(undefined);
      jest.spyOn(warehouseRepository, 'create').mockReturnValue(createDto as Warehouse);
      jest.spyOn(warehouseRepository, 'save').mockResolvedValue(newWarehouse as Warehouse);

      // Act
      const result = await service.createWarehouse(createDto);

      // Assert
      expect(validationHelper.validateCreateWarehouse).toHaveBeenCalledWith(createDto);
      expect(warehouseRepository.create).toHaveBeenCalledWith({
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      });
      expect(warehouseRepository.save).toHaveBeenCalled();
      expect(result).toBeInstanceOf(WarehouseResponseDto);
      expect(result.warehouseId).toBe(3);
      expect(result.name).toBe('Kho hàng mới');
      expect(result.description).toBe('Mô tả kho hàng mới');
      expect(result.type).toBe(WarehouseTypeEnum.PHYSICAL);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const createDto: CreateWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateCreateWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createWarehouse(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateWarehouse).toHaveBeenCalledWith(createDto);
    });

    it('nên ném lỗi khi tạo kho thất bại', async () => {
      // Arrange
      const createDto: CreateWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      };

      jest.spyOn(validationHelper, 'validateCreateWarehouse').mockResolvedValue(undefined);
      jest.spyOn(warehouseRepository, 'create').mockReturnValue(createDto as Warehouse);
      jest.spyOn(warehouseRepository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.createWarehouse(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateWarehouse).toHaveBeenCalledWith(createDto);
      expect(warehouseRepository.create).toHaveBeenCalled();
      expect(warehouseRepository.save).toHaveBeenCalled();
    });
  });

  describe('updateWarehouse', () => {
    it('nên cập nhật kho thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateWarehouseDto = {
        name: 'Kho hàng đã cập nhật',
        description: 'Mô tả đã cập nhật',
      };
      const existingWarehouse = mockWarehouses[0];
      const updatedWarehouse = { ...existingWarehouse, name: 'Kho hàng đã cập nhật', description: 'Mô tả đã cập nhật' };

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(validationHelper, 'validateUpdateWarehouse').mockResolvedValue(undefined);
      jest.spyOn(warehouseRepository, 'updateWarehouse').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(updatedWarehouse);

      // Act
      const result = await service.updateWarehouse(warehouseId, updateDto);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateUpdateWarehouse).toHaveBeenCalledWith(updateDto, existingWarehouse);
      expect(warehouseRepository.updateWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
      expect(result).toBeInstanceOf(WarehouseResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.name).toBe('Kho hàng đã cập nhật');
      expect(result.description).toBe('Mô tả đã cập nhật');
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const updateDto: UpdateWarehouseDto = {
        name: 'Kho hàng đã cập nhật',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateWarehouseDto = {
        name: 'Kho hàng đã cập nhật',
      };
      const existingWarehouse = mockWarehouses[0];
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(validationHelper, 'validateUpdateWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateUpdateWarehouse).toHaveBeenCalledWith(updateDto, existingWarehouse);
    });
  });

  describe('getWarehouseById', () => {
    it('nên lấy thông tin kho theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(mockWarehouses[0]);

      // Act
      const result = await service.getWarehouseById(warehouseId);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(result).toBeInstanceOf(WarehouseResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.name).toBe('Kho hàng 1');
      expect(result.description).toBe('Mô tả kho hàng 1');
      expect(result.type).toBe(WarehouseTypeEnum.PHYSICAL);
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.getWarehouseById(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });
  });

  describe('getWarehouses', () => {
    it('nên lấy danh sách kho với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryWarehouseDto = {
        page: 1,
        limit: 10,
        type: WarehouseTypeEnum.PHYSICAL,
      };
      const paginatedResult = {
        items: [mockWarehouses[0]],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(warehouseRepository, 'findAll').mockResolvedValue(paginatedResult);

      // Act
      const result = await service.getWarehouses(queryDto);

      // Assert
      expect(warehouseRepository.findAll).toHaveBeenCalledWith(queryDto);
      expect(result.items.length).toBe(1);
      expect(result.items[0]).toBeInstanceOf(WarehouseResponseDto);
      expect(result.items[0].warehouseId).toBe(1);
      expect(result.items[0].name).toBe('Kho hàng 1');
      expect(result.items[0].type).toBe(WarehouseTypeEnum.PHYSICAL);
      expect(result.meta.totalItems).toBe(1);
    });

    it('nên ném lỗi khi lấy danh sách kho thất bại', async () => {
      // Arrange
      const queryDto: QueryWarehouseDto = {
        page: 1,
        limit: 10,
      };

      jest.spyOn(warehouseRepository, 'findAll').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getWarehouses(queryDto)).rejects.toThrow(AppException);
      expect(warehouseRepository.findAll).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('deleteWarehouse', () => {
    it('nên xóa kho thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(mockWarehouses[0]);
      jest.spyOn(warehouseRepository, 'deleteWarehouse').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

      // Act
      await service.deleteWarehouse(warehouseId);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseRepository.deleteWarehouse).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.deleteWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi xóa kho thất bại', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(mockWarehouses[0]);
      jest.spyOn(warehouseRepository, 'deleteWarehouse').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.deleteWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(warehouseRepository.deleteWarehouse).toHaveBeenCalledWith(warehouseId);
    });
  });
});
