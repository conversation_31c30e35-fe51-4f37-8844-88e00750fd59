# Hướng dẫn sử dụng các Component dùng chung

## Giới thiệu

Tài liệu này mô tả cách sử dụng các component dùng chung trong thư mục `src/shared/components/common`. <PERSON><PERSON><PERSON> là thư viện UI tự xây dựng, **không phải MUI UI**, vì vậy cần lưu ý sử dụng đúng các props và thuộc tính của từng component.

> **LƯU Ý QUAN TRỌNG**: Đ<PERSON><PERSON> là thư viện UI tự xây dựng, không phải MUI UI hay các thư viện UI phổ biến khác. Không sử dụng các props của MUI như `sx`, `variant="contained"`, v.v.

## Nguyên tắc chung

1. **Import component từ đường dẫn chung**:
   ```tsx
   import { But<PERSON>, Card, Typography } from '@/shared/components/common';
   ```

2. **Không nhầm lẫn với MUI UI**:
   - Các component này có API riêng, không tương thích với MUI
   - Không sử dụng các props của MUI như `sx`, `variant="contained"`, v.v.

3. **Sử dụng Icon component**:
   - Luôn sử dụng `<Icon name="icon-name" />` thay vì import trực tiếp từ thư viện bên ngoài
   - Không sử dụng `@heroicons/react/24/outline` hoặc các thư viện icon khác

## Các Component cơ bản

### Button

Button là component cơ bản để tạo nút bấm trong ứng dụng.

```tsx
<Button
  variant="primary" // primary, secondary, outline, success, warning, danger, ghost
  size="md" // sm, md, lg
  type="button" // button (default), submit, reset
  fullWidth={false}
  isLoading={false}
  leftIcon={<Icon name="plus" />}
  rightIcon={<Icon name="chevron-right" />}
  onClick={() => console.log('Button clicked')}
>
  Nút bấm
</Button>
```

**Props chính**:
- `variant`: Kiểu nút (primary, secondary, outline, success, warning, danger, ghost)
- `size`: Kích thước nút (sm, md, lg)
- `type`: Loại nút (button - mặc định, submit, reset). Mặc định là "button" để tránh submit form không mong muốn
- `fullWidth`: Nút chiếm toàn bộ chiều rộng container
- `isLoading`: Hiển thị trạng thái đang tải
- `leftIcon`: Icon bên trái nút
- `rightIcon`: Icon bên phải nút

**Lưu ý quan trọng**:
- Mặc định `type="button"` để tránh submit form không mong muốn
- Chỉ sử dụng `type="submit"` cho nút submit form chính

### Typography

Typography dùng để hiển thị văn bản với nhiều kiểu dáng khác nhau.

```tsx
<Typography
  variant="h1" // h1-h6, body1, body2, subtitle1, subtitle2, caption, overline, code
  align="left" // left, center, right, justify
  weight="normal" // thin, extralight, light, normal, medium, semibold, bold, extrabold, black
  color="primary" // default, primary, secondary, success, warning, danger, info, light, dark, muted, error
  transform="none" // none, uppercase, lowercase, capitalize
  underline={false}
  italic={false}
  truncate={false}
>
  Văn bản mẫu
</Typography>
```

**Props chính**:
- `variant`: Kiểu văn bản (h1-h6, body1, body2, subtitle1, subtitle2, caption, overline, code)
- `align`: Căn chỉnh văn bản (left, center, right, justify)
- `weight`: Độ đậm của văn bản
- `color`: Màu sắc văn bản
- `transform`: Biến đổi văn bản (none, uppercase, lowercase, capitalize)
- `fontSize`: Kích thước font chữ (có thể responsive)
- `truncate`: Cắt văn bản dài và hiển thị dấu "..."
- `lines`: Số dòng tối đa hiển thị khi truncate=true

### Icon

Icon hiển thị các biểu tượng SVG.

```tsx
<Icon
  name="user" // Tên icon
  size="md" // xs, sm, md, lg, xl
  color="primary" // Màu sắc (tùy chọn)
  fill={false} // Sử dụng fill thay vì stroke
/>
```

**Props chính**:
- `name`: Tên icon (user, home, settings, v.v.)
- `size`: Kích thước icon (xs, sm, md, lg, xl)
- `color`: Màu sắc icon
- `fill`: Sử dụng fill thay vì stroke

### Card

Card hiển thị nội dung trong một container có viền.

```tsx
<Card
  title="Tiêu đề card"
  subtitle="Tiêu đề phụ"
  footer={<Button>Hành động</Button>}
  variant="default" // default, bordered, elevated
  hoverable={true}
  loading={false}
  colorBorder="primary" // primary, success, warning, danger, info
  colorHeader="primary" // primary, success, warning, danger, info
  noPadding={false}
>
  Nội dung card
</Card>
```

**Props chính**:
- `title`: Tiêu đề card
- `subtitle`: Tiêu đề phụ
- `footer`: Nội dung footer
- `variant`: Kiểu card (default, bordered, elevated)
- `hoverable`: Hiệu ứng hover
- `loading`: Trạng thái loading
- `colorBorder`: Màu viền
- `colorHeader`: Màu nền header
- `noPadding`: Không sử dụng padding

## Form Components

### Input

Input là trường nhập liệu cơ bản.

```tsx
<Input
  type="text" // text, password, email, number, v.v.
  label="Tên người dùng"
  placeholder="Nhập tên người dùng"
  value={value}
  onChange={(e) => setValue(e.target.value)}
  error="Lỗi nếu có"
  helperText="Văn bản trợ giúp"
  leftIcon={<Icon name="user" />}
  rightIcon={<Icon name="eye" />}
  fullWidth={false}
  disabled={false}
/>
```

**Props chính**:
- `type`: Loại input (text, password, email, number, v.v.)
- `label`: Nhãn trường nhập liệu
- `placeholder`: Văn bản gợi ý
- `value`: Giá trị hiện tại
- `onChange`: Hàm xử lý khi giá trị thay đổi
- `error`: Thông báo lỗi
- `helperText`: Văn bản trợ giúp
- `leftIcon`: Icon bên trái
- `rightIcon`: Icon bên phải
- `fullWidth`: Input chiếm toàn bộ chiều rộng container
- `disabled`: Trạng thái vô hiệu hóa

### Select

Select cho phép người dùng chọn một hoặc nhiều tùy chọn từ danh sách.

```tsx
<Select
  value={value}
  onChange={(val) => setValue(val)}
  options={[
    { value: 'option1', label: 'Tùy chọn 1' },
    { value: 'option2', label: 'Tùy chọn 2' },
  ]}
  multiple={false}
  searchable={true}
  placeholder="Chọn một tùy chọn"
  label="Tùy chọn"
  disabled={false}
  loading={false}
  error="Lỗi nếu có"
  helperText="Văn bản trợ giúp"
  fullWidth={false}
/>
```

**Props chính**:
- `value`: Giá trị đã chọn
- `onChange`: Hàm xử lý khi giá trị thay đổi
- `options`: Danh sách tùy chọn
- `multiple`: Cho phép chọn nhiều
- `searchable`: Cho phép tìm kiếm
- `placeholder`: Văn bản gợi ý
- `label`: Nhãn trường chọn
- `disabled`: Trạng thái vô hiệu hóa
- `loading`: Trạng thái đang tải

### Checkbox

Checkbox cho phép người dùng chọn một hoặc nhiều tùy chọn.

```tsx
<Checkbox
  label="Đồng ý với điều khoản"
  checked={checked}
  onChange={(checked) => setChecked(checked)}
  value="agree"
  disabled={false}
  indeterminate={false}
  size="md" // sm, md, lg
  variant="default" // default, outlined, filled
  color="primary" // primary, success, warning, danger, info
/>
```

**Props chính**:
- `label`: Nhãn checkbox
- `checked`: Trạng thái đã chọn
- `onChange`: Hàm xử lý khi trạng thái thay đổi
- `value`: Giá trị của checkbox
- `disabled`: Trạng thái vô hiệu hóa
- `indeterminate`: Trạng thái không xác định
- `size`: Kích thước (sm, md, lg)
- `variant`: Kiểu checkbox (default, outlined, filled)
- `color`: Màu sắc (primary, success, warning, danger, info)

### Radio

Radio cho phép người dùng chọn một tùy chọn từ danh sách.

```tsx
<Radio
  label="Tùy chọn 1"
  checked={selected === 'option1'}
  onChange={() => setSelected('option1')}
  value="option1"
  disabled={false}
  size="md" // sm, md, lg
  variant="default" // default, outlined, filled
  color="primary" // primary, success, warning, danger, info
/>
```

**Props chính**:
- `label`: Nhãn radio
- `checked`: Trạng thái đã chọn
- `onChange`: Hàm xử lý khi trạng thái thay đổi
- `value`: Giá trị của radio
- `disabled`: Trạng thái vô hiệu hóa
- `size`: Kích thước (sm, md, lg)
- `variant`: Kiểu radio (default, outlined, filled)
- `color`: Màu sắc (primary, success, warning, danger, info)

### DatePicker

DatePicker cho phép người dùng chọn ngày tháng.

```tsx
<DatePicker
  label="Chọn ngày"
  value={date}
  onChange={(date) => setDate(date)}
  placeholder="DD/MM/YYYY"
  format="dd/MM/yyyy"
  minDate={new Date()}
  maxDate={new Date(2025, 0, 1)}
  disabled={false}
  readOnly={false}
  calendarIcon={<Icon name="calendar" />}
  iconOnly={false}
  noBorder={false}
/>
```

**Props chính**:
- `label`: Nhãn trường chọn ngày
- `value`: Giá trị ngày đã chọn
- `onChange`: Hàm xử lý khi ngày thay đổi
- `placeholder`: Văn bản gợi ý
- `format`: Định dạng ngày tháng
- `minDate`: Ngày tối thiểu có thể chọn
- `maxDate`: Ngày tối đa có thể chọn
- `disabled`: Trạng thái vô hiệu hóa
- `readOnly`: Trạng thái chỉ đọc
- `calendarIcon`: Icon lịch tùy chỉnh
- `iconOnly`: Chỉ hiển thị icon
- `noBorder`: Không hiển thị viền

## Layout Components

### Modal

Modal hiển thị nội dung trong cửa sổ popup.

```tsx
<Modal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  title="Tiêu đề modal"
  size="md" // sm, md, lg, xl
  closeOnClickOutside={true}
  closeOnEsc={true}
  footer={
    <div className="flex justify-end">
      <Button variant="outline" onClick={() => setIsOpen(false)} className="mr-2">
        Hủy
      </Button>
      <Button variant="primary" onClick={handleSubmit}>
        Xác nhận
      </Button>
    </div>
  }
>
  Nội dung modal
</Modal>
```

**Props chính**:
- `isOpen`: Trạng thái hiển thị modal
- `onClose`: Hàm xử lý khi đóng modal
- `title`: Tiêu đề modal
- `size`: Kích thước modal (sm, md, lg, xl)
- `closeOnClickOutside`: Đóng khi click bên ngoài
- `closeOnEsc`: Đóng khi nhấn phím Esc
- `footer`: Nội dung footer

### Table

Table hiển thị dữ liệu dạng bảng.

```tsx
<Table
  data={data}
  columns={[
    { title: 'ID', dataIndex: 'id', key: 'id', sortable: true },
    { title: 'Tên', dataIndex: 'name', key: 'name', render: (text) => <b>{text}</b> },
    {
      title: 'Hành động',
      key: 'actions',
      render: (_, record) => (
        <Button size="sm" onClick={() => handleEdit(record.id)}>Sửa</Button>
      )
    },
  ]}
  loading={false}
  sortable={true}
  selectable={false}
  expandable={false}
  pagination={true}
  size="md" // sm, md, lg
  bordered={false}
  striped={true}
  hoverable={true}
/>
```

**Props chính**:
- `data`: Dữ liệu hiển thị
- `columns`: Cấu trúc cột
- `loading`: Trạng thái đang tải
- `sortable`: Cho phép sắp xếp
- `selectable`: Cho phép chọn hàng
- `expandable`: Cho phép mở rộng hàng
- `pagination`: Phân trang
- `size`: Kích thước bảng (sm, md, lg)
- `bordered`: Hiển thị viền
- `striped`: Hiển thị màu nền xen kẽ
- `hoverable`: Hiệu ứng hover

## Lưu ý quan trọng

1. **Không nhầm lẫn với MUI UI**:
   - Không sử dụng `variant="contained"` cho Button (thay vào đó dùng `variant="primary"`)
   - Không sử dụng `sx` prop
   - Không sử dụng `ThemeProvider` của MUI

2. **Sử dụng Icon component**:
   - Luôn dùng `<Icon name="icon-name" />` thay vì import từ thư viện bên ngoài
   - Không dùng `import { UserIcon } from '@heroicons/react/24/outline'`

3. **Sử dụng Loading component**:
   - Dùng `<Loading />` thay vì `<Spinner />`

4. **Tận dụng các hook hỗ trợ**:
   - `useMediaQuery`, `useIsMobile`, `useIsTablet`, `useIsDesktop` cho responsive
   - `useResponsiveValue` để trả về giá trị khác nhau dựa trên breakpoint

5. **Tham khảo các trang demo**:
   - Xem các ví dụ trong thư mục `src/modules/components/pages` để hiểu cách sử dụng

## Component hiển thị và tương tác

### ResponsiveGrid

ResponsiveGrid tự động điều chỉnh số cột dựa trên kích thước màn hình và trạng thái chatpanel.

```tsx
<ResponsiveGrid
  maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 5 }}
  maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 4 }}
  gap={{ xs: 2, sm: 3, md: 4, lg: 5, xl: 6 }}
  onColumnsChange={(columns) => console.log(`Grid có ${columns} cột`)}
>
  <Card>Item 1</Card>
  <Card>Item 2</Card>
  <Card>Item 3</Card>
  <Card>Item 4</Card>
</ResponsiveGrid>
```

**Props chính**:
- `maxColumns`: Số cột tối đa cho mỗi breakpoint
- `maxColumnsWithChatPanel`: Số cột tối đa khi chatpanel mở
- `gap`: Khoảng cách giữa các phần tử
- `onColumnsChange`: Callback khi số cột thay đổi

### Chip

Chip hiển thị các thông tin nhỏ gọn, có thể chọn/bỏ chọn.

```tsx
<Chip
  variant="primary" // default, primary, secondary, success, warning, danger, info
  size="md" // sm, md, lg
  leftIconName="user"
  closable={true}
  onClose={() => console.log('Chip closed')}
  onClick={() => console.log('Chip clicked')}
  avatarSrc="/path/to/avatar.jpg"
  loading={false}
>
  Chip content
</Chip>
```

**Props chính**:
- `variant`: Kiểu chip
- `size`: Kích thước chip
- `leftIconName`: Tên icon bên trái
- `closable`: Có thể đóng
- `onClose`: Hàm xử lý khi đóng
- `onClick`: Hàm xử lý khi click
- `avatarSrc`: Đường dẫn avatar
- `loading`: Trạng thái đang tải

### Alert

Alert hiển thị thông báo quan trọng cho người dùng.

```tsx
<Alert
  type="info" // info, success, warning, error
  title="Thông báo"
  message="Đây là một thông báo quan trọng."
  description="Mô tả chi tiết về thông báo này."
  showIcon={true}
  closable={true}
  onClose={() => console.log('Alert closed')}
  banner={false}
  action={<Button size="sm">Hành động</Button>}
/>
```

**Props chính**:
- `type`: Loại thông báo (info, success, warning, error)
- `title`: Tiêu đề thông báo
- `message`: Nội dung thông báo
- `description`: Mô tả chi tiết
- `showIcon`: Hiển thị icon
- `closable`: Có thể đóng
- `onClose`: Hàm xử lý khi đóng
- `banner`: Hiển thị dạng banner
- `action`: Hành động tùy chỉnh

### Hidden

Hidden ẩn/hiện nội dung dựa trên breakpoint.

```tsx
<Hidden
  hideOn={['xs', 'sm']} // Ẩn trên xs và sm
  showOn={['lg', 'xl']} // Chỉ hiện trên lg và xl
  hideOnMobile={false}
  hideOnTablet={false}
  hideOnDesktop={false}
  hideOnPortrait={false}
  hideOnLandscape={false}
  useCSS={false} // Sử dụng CSS để ẩn thay vì không render
>
  Nội dung chỉ hiển thị trên desktop
</Hidden>
```

**Props chính**:
- `hideOn`: Ẩn trên các breakpoint này
- `showOn`: Hiện trên các breakpoint này
- `hideOnMobile`: Ẩn trên mobile
- `hideOnTablet`: Ẩn trên tablet
- `hideOnDesktop`: Ẩn trên desktop
- `hideOnPortrait`: Ẩn trên portrait
- `hideOnLandscape`: Ẩn trên landscape
- `useCSS`: Sử dụng CSS để ẩn thay vì không render

## Các hook hữu ích

### useMediaQuery

Hook để kiểm tra media query.

```tsx
import { useMediaQuery, useIsMobile, useIsTablet, useIsDesktop } from '@/shared/hooks/common';

// Kiểm tra media query tùy chỉnh
const isLargeScreen = useMediaQuery('(min-width: 1200px)');

// Hoặc sử dụng các hook tiện ích
const isMobile = useIsMobile(); // true nếu là mobile
const isTablet = useIsTablet(); // true nếu là tablet
const isDesktop = useIsDesktop(); // true nếu là desktop
```

### useResponsiveValue

Hook để trả về giá trị khác nhau dựa trên breakpoint.

```tsx
import { useResponsiveValue } from '@/shared/hooks/common';

// Trả về giá trị khác nhau dựa trên breakpoint
const columns = useResponsiveValue({
  xs: 1,
  sm: 2,
  md: 3,
  lg: 4,
  xl: 5,
});

// Sử dụng trong JSX
return <div className={`grid-cols-${columns}`}>...</div>;
```

## Ví dụ sử dụng nâng cao

### Form với validation

```tsx
import { Form, FormItem, Input, Button } from '@/shared/components/common';
import { z } from 'zod';
import { useRef } from 'react';

// Định nghĩa schema validation
const schema = z.object({
  username: z.string().min(3, 'Tên người dùng phải có ít nhất 3 ký tự'),
  email: z.string().email('Email không hợp lệ'),
  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
});

// Component form
const MyForm = () => {
  const formRef = useRef(null);

  const handleSubmit = (values) => {
    console.log('Form values:', values);
  };

  return (
    <Form ref={formRef} schema={schema} onSubmit={handleSubmit}>
      <FormItem name="username" label="Tên người dùng">
        <Input placeholder="Nhập tên người dùng" />
      </FormItem>

      <FormItem name="email" label="Email">
        <Input type="email" placeholder="Nhập email" />
      </FormItem>

      <FormItem name="password" label="Mật khẩu">
        <Input type="password" placeholder="Nhập mật khẩu" />
      </FormItem>

      <Button type="submit">Đăng ký</Button>
    </Form>
  );
};
```

### Bảng dữ liệu với phân trang và sắp xếp

```tsx
import { Table, Button, Icon } from '@/shared/components/common';
import { useState } from 'react';

const MyTable = () => {
  const [data, setData] = useState([
    { id: 1, name: 'Nguyễn Văn A', age: 30 },
    { id: 2, name: 'Trần Thị B', age: 25 },
    { id: 3, name: 'Lê Văn C', age: 35 },
  ]);

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sortable: true
    },
    {
      title: 'Tên',
      dataIndex: 'name',
      key: 'name',
      sortable: true,
      render: (text) => <span className="font-medium">{text}</span>
    },
    {
      title: 'Tuổi',
      dataIndex: 'age',
      key: 'age',
      sortable: true
    },
    {
      title: 'Hành động',
      key: 'actions',
      render: (_, record) => (
        <div className="flex space-x-2">
          <Button size="sm" leftIcon={<Icon name="pencil" />}>
            Sửa
          </Button>
          <Button size="sm" variant="danger" leftIcon={<Icon name="trash" />}>
            Xóa
          </Button>
        </div>
      )
    },
  ];

  const handleSortChange = (column, order) => {
    console.log(`Sắp xếp theo ${column} theo thứ tự ${order}`);
    // Gọi API hoặc sắp xếp dữ liệu ở đây
  };

  return (
    <Table
      data={data}
      columns={columns}
      sortable={true}
      onSortChange={handleSortChange}
      pagination={{
        total: 100,
        current: 1,
        pageSize: 10,
        onChange: (page) => console.log(`Chuyển đến trang ${page}`),
      }}
      loading={false}
      bordered={true}
      striped={true}
    />
  );
};
```

## Best Practices

### Tổ chức code

1. **Tách biệt logic và UI**:
   ```tsx
   // Tách logic ra khỏi component
   const useProductList = () => {
     const [products, setProducts] = useState([]);
     const [loading, setLoading] = useState(false);

     const fetchProducts = useCallback(async () => {
       setLoading(true);
       try {
         // Fetch data
         setProducts(data);
       } finally {
         setLoading(false);
       }
     }, []);

     return { products, loading, fetchProducts };
   };

   // Component chỉ xử lý UI
   const ProductList = () => {
     const { products, loading, fetchProducts } = useProductList();

     useEffect(() => {
       fetchProducts();
     }, [fetchProducts]);

     return (
       <Card title="Danh sách sản phẩm" loading={loading}>
         {products.map(product => (
           <div key={product.id}>{product.name}</div>
         ))}
       </Card>
     );
   };
   ```

2. **Sử dụng memo và callback**:
   ```tsx
   // Sử dụng useMemo cho các tính toán phức tạp
   const filteredProducts = useMemo(() => {
     return products.filter(p => p.price > minPrice);
   }, [products, minPrice]);

   // Sử dụng useCallback cho các hàm xử lý sự kiện
   const handleClick = useCallback(() => {
     console.log('Button clicked');
   }, []);
   ```

3. **Tạo component con cho UI phức tạp**:
   ```tsx
   const ProductCard = ({ product }) => (
     <Card hoverable>
       <Typography variant="h4">{product.name}</Typography>
       <Typography variant="body2">{product.description}</Typography>
       <Button>Xem chi tiết</Button>
     </Card>
   );

   const ProductList = ({ products }) => (
     <div className="grid grid-cols-3 gap-4">
       {products.map(product => (
         <ProductCard key={product.id} product={product} />
       ))}
     </div>
   );
   ```

### Responsive Design

1. **Sử dụng các hook responsive**:
   ```tsx
   const isMobile = useIsMobile();

   return (
     <div>
       {isMobile ? (
         <MobileView />
       ) : (
         <DesktopView />
       )}
     </div>
   );
   ```

2. **Sử dụng component Hidden**:
   ```tsx
   <div>
     <Hidden hideOnMobile>
       <DesktopNavigation />
     </Hidden>

     <Hidden hideOn={['md', 'lg', 'xl']}>
       <MobileNavigation />
     </Hidden>
   </div>
   ```

3. **Sử dụng useResponsiveValue**:
   ```tsx
   const padding = useResponsiveValue({
     xs: '1rem',
     sm: '1.5rem',
     md: '2rem',
     lg: '3rem',
   });

   return <div style={{ padding }}>Responsive padding</div>;
   ```

### Performance

1. **Tránh re-render không cần thiết**:
   ```tsx
   // Sử dụng React.memo cho component thuần UI
   const ProductItem = React.memo(({ name, price }) => (
     <div>
       <Typography>{name}</Typography>
       <Typography>{price}</Typography>
     </div>
   ));
   ```

2. **Lazy loading components**:
   ```tsx
   const HeavyComponent = React.lazy(() => import('./HeavyComponent'));

   const MyComponent = () => (
     <React.Suspense fallback={<Loading />}>
       <HeavyComponent />
     </React.Suspense>
   );
   ```

3. **Virtualize danh sách dài**:
   ```tsx
   import { useVirtualizer } from '@tanstack/react-virtual';

   const VirtualList = ({ items }) => {
     const parentRef = useRef(null);

     const virtualizer = useVirtualizer({
       count: items.length,
       getScrollElement: () => parentRef.current,
       estimateSize: () => 50,
     });

     return (
       <div ref={parentRef} style={{ height: '500px', overflow: 'auto' }}>
         <div style={{ height: `${virtualizer.getTotalSize()}px`, position: 'relative' }}>
           {virtualizer.getVirtualItems().map(virtualItem => (
             <div
               key={virtualItem.key}
               style={{
                 position: 'absolute',
                 top: 0,
                 left: 0,
                 width: '100%',
                 height: `${virtualItem.size}px`,
                 transform: `translateY(${virtualItem.start}px)`,
               }}
             >
               {items[virtualItem.index].name}
             </div>
           ))}
         </div>
       </div>
     );
   };
   ```

## Lưu ý quan trọng khi sử dụng component

1. **Không trộn lẫn các thư viện UI**:
   - Không sử dụng MUI UI cùng với thư viện component này
   - Không import component từ các thư viện khác như Ant Design, Chakra UI, v.v.

2. **Sử dụng đúng cách Icon**:
   ```tsx
   // ĐÚNG
   import { Icon } from '@/shared/components/common';
   <Icon name="user" />

   // SAI
   import { UserIcon } from '@heroicons/react/24/outline';
   <UserIcon />
   ```

3. **Sử dụng đúng cách Loading**:
   ```tsx
   // ĐÚNG
   import { Loading } from '@/shared/components/common';
   <Loading />

   // SAI
   import { Spinner } from 'some-library';
   <Spinner />
   ```

4. **Sử dụng Form với React Hook Form và Zod**:
   ```tsx
   // ĐÚNG
   import { Form, FormItem } from '@/shared/components/common';
   import { z } from 'zod';

   const schema = z.object({
     name: z.string().min(2),
   });

   <Form schema={schema} onSubmit={handleSubmit}>
     <FormItem name="name">
       <Input />
     </FormItem>
   </Form>

   // SAI - Không sử dụng các form component từ thư viện khác
   import { Form, Input } from 'antd';
   ```

5. **Sử dụng đúng cách Typography**:
   ```tsx
   // ĐÚNG
   import { Typography } from '@/shared/components/common';
   <Typography variant="h1">Tiêu đề</Typography>

   // SAI
   <h1 className="text-2xl font-bold">Tiêu đề</h1>
   ```

## Kết luận

Thư viện component này được xây dựng riêng cho dự án, không phải MUI UI hay các thư viện UI phổ biến khác. Vui lòng tuân thủ các quy tắc và hướng dẫn sử dụng để đảm bảo tính nhất quán trong toàn bộ ứng dụng.

Khi gặp vấn đề hoặc cần thêm thông tin về cách sử dụng component, hãy tham khảo:
1. Các ví dụ trong thư mục `src/modules/components/pages`
2. Mã nguồn của component trong `src/shared/components/common`
3. Các file README.md trong thư mục component (nếu có)
