# Tabs Component

Component Tabs cung cấp nhiều kiểu dáng và tùy chọn khác nhau để tạo giao diện tab trong ứng dụng.

## Tính năng

- **Nhiều kiểu dáng**: Default, Card, Underline, Pills, Segmented, Minimal, Bordered, Vertical Card, Icon Only
- **Hỗ trợ Icon**: Hiển thị icon bên cạnh label
- **Badge thông báo**: Hiển thị số lượng thông báo
- **Responsive**: Tối ưu cho mobile và desktop
- **Accessibility**: Hỗ trợ đầy đủ ARIA attributes
- **Animation**: Smooth transitions và indicator động

## Cách sử dụng cơ bản

```tsx
import { Tabs } from '@/shared/components/common';

const items = [
  {
    key: '1',
    label: 'Tab 1',
    children: <div>Nội dung Tab 1</div>,
  },
  {
    key: '2',
    label: 'Tab 2',
    children: <div>Nội dung Tab 2</div>,
  },
];

<Tabs items={items} />
```

## Props

### TabItem Interface

```tsx
interface TabItem {
  key: string;                    // Khóa duy nhất
  label: React.ReactNode;         // Tiêu đề tab
  children: React.ReactNode;      // Nội dung tab
  disabled?: boolean;             // Trạng thái disabled
  icon?: React.ReactNode;         // Icon hiển thị
  badge?: string | number;        // Badge thông báo
  badgeColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}
```

### TabsProps Interface

```tsx
interface TabsProps {
  items: TabItem[];                           // Danh sách tabs
  defaultActiveKey?: string;                  // Tab mặc định
  activeKey?: string;                         // Tab hiện tại (controlled)
  onChange?: (activeKey: string) => void;     // Callback khi thay đổi
  type?: 'default' | 'card' | 'underline' | 'pills' | 'segmented' | 'minimal' | 'bordered' | 'vertical-card' | 'icon-only';
  size?: 'sm' | 'md' | 'lg';                 // Kích thước
  position?: 'top' | 'bottom' | 'left' | 'right'; // Vị trí
  alignment?: 'start' | 'center' | 'end' | 'stretch'; // Căn chỉnh
  className?: string;                         // Class bổ sung
  contentClassName?: string;                  // Class cho content
}
```

## Các kiểu dáng

### 1. Default Tabs
```tsx
<Tabs items={items} />
```

### 2. Card Tabs
```tsx
<Tabs type="card" items={items} />
```

### 3. Underline Tabs
```tsx
<Tabs type="underline" items={items} />
```

### 4. Pills Tabs
```tsx
<Tabs type="pills" items={items} />
```

### 5. Segmented Control
```tsx
<Tabs type="segmented" items={items} />
```

### 6. Minimal Tabs
```tsx
<Tabs type="minimal" items={items} />
```

### 7. Bordered Tabs
```tsx
<Tabs type="bordered" items={items} />
```

### 8. Vertical Card Tabs
```tsx
<Tabs type="vertical-card" position="left" items={items} />
```

### 9. Icon Only Tabs
```tsx
<Tabs type="icon-only" items={iconItems} />
```

## Tabs với Icon và Badge

```tsx
const tabsWithIconAndBadge = [
  {
    key: '1',
    label: 'Tin nhắn',
    icon: <Icon name="chat" size="sm" />,
    badge: '5',
    badgeColor: 'danger',
    children: <div>Nội dung tin nhắn</div>,
  },
  {
    key: '2',
    label: 'Thông báo',
    icon: <Icon name="alert-circle" size="sm" />,
    badge: '12',
    badgeColor: 'warning',
    children: <div>Nội dung thông báo</div>,
  },
];

<Tabs items={tabsWithIconAndBadge} />
```

## Controlled Tabs

```tsx
const [activeTab, setActiveTab] = useState('1');

<Tabs 
  activeKey={activeTab} 
  onChange={setActiveTab} 
  items={items} 
/>
```

## Kích thước khác nhau

```tsx
<Tabs size="sm" items={items} />
<Tabs size="md" items={items} />
<Tabs size="lg" items={items} />
```

## Ví dụ thực tế

### Dashboard Navigation
```tsx
const dashboardTabs = [
  {
    key: 'overview',
    label: 'Tổng quan',
    icon: <Icon name="chart" size="sm" />,
    children: <DashboardOverview />,
  },
  {
    key: 'analytics',
    label: 'Phân tích',
    icon: <Icon name="chart" size="sm" />,
    badge: 'Mới',
    badgeColor: 'success',
    children: <Analytics />,
  },
];

<Tabs type="pills" items={dashboardTabs} />
```

### Settings Panel
```tsx
const settingsTabs = [
  {
    key: 'profile',
    label: 'Hồ sơ',
    icon: <Icon name="user" size="sm" />,
    children: <ProfileSettings />,
  },
  {
    key: 'security',
    label: 'Bảo mật',
    icon: <Icon name="lock" size="sm" />,
    children: <SecuritySettings />,
  },
];

<Tabs type="vertical-card" position="left" items={settingsTabs} />
```

### Mobile Navigation
```tsx
const mobileNavTabs = [
  {
    key: 'home',
    label: 'Trang chủ',
    icon: <Icon name="home" size="lg" />,
    children: <HomePage />,
  },
  {
    key: 'search',
    label: 'Tìm kiếm',
    icon: <Icon name="search" size="lg" />,
    children: <SearchPage />,
  },
];

<Tabs type="icon-only" items={mobileNavTabs} />
```

## Styling

Component sử dụng Tailwind CSS và hỗ trợ dark mode. Các class có thể được tùy chỉnh thông qua props `className` và `contentClassName`.

## Demo

Xem demo đầy đủ tại: `/components/tabs`

## Accessibility

- Sử dụng đúng ARIA roles và attributes
- Hỗ trợ keyboard navigation
- Screen reader friendly
- Focus management

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
