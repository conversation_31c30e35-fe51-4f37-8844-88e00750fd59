import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/shared/components/ui/tabs';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Input } from '@/shared/components/ui/input';
import { Textarea } from '@/shared/components/ui/textarea';
import { Separator } from '@/shared/components/ui/separator';
import { Label } from '@/shared/components/ui/label';
import { Slider } from "@/shared/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Switch } from "@/shared/components/ui/switch";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/shared/components/ui/accordion";
import { Badge } from "@/shared/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/shared/components/ui/dialog";
import { useToast } from "@/shared/hooks/use-toast";
import {
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  ArrowUp,
  Image as ImageIcon,
  Type,
  Square as ButtonIcon,
  Smartphone,
  Tablet,
  Monitor,
  Save,
  FileCode,
  UploadCloud,
  Eye,
  EyeOff,
  Settings,
  PanelTop,
  ChevronDown,
  Trash2,
  Clipboard,
  Undo2,
  Redo2,
  Layers,
  Layout,
  Columns,
  Link,
  Heading1,
  List,
  ListOrdered,
  ArrowUpDown,
  Plus,
  Minus,
  X,
  Check,
  Code,
  Maximize2,
  Minimize2,
  ChevronUp,
  Italic,
  Underline,
  PanelLeft as LayoutPanelLeft,
  PanelRight as LayoutPanelRight,
  Share2,
  Video,
  Timer
} from 'lucide-react';

// Dữ liệu mẫu mặc định
const initialEmail = {
  name: '',
  subject: '',
  preheader: '',
};

// Định nghĩa các mẫu email
const EMAIL_TEMPLATES = [
  {
    id: 'welcome-email',
    name: 'Email chào mừng',
    category: 'Marketing',
    description: 'Template email chào mừng người đăng ký mới',
    elements: [
      {
        id: `header-${Date.now()}`,
        type: 'header',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#ffffff" style="padding: 20px 0;">
                <img src="https://via.placeholder.com/200x60?text=Logo" alt="Logo" width="200" style="display: block; max-width: 100%;" />
              </td>
            </tr>
            <tr>
              <td align="center" bgcolor="#f8f9fa" style="padding: 10px 0; border-top: 1px solid #e5e7eb; border-bottom: 1px solid #e5e7eb;">
                <table border="0" cellspacing="0" cellpadding="0">
                  <tr>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Trang chủ</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Sản phẩm</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Liên hệ</a></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '0 0 20px 0',
        }
      },
      {
        id: `heading-${Date.now()}`,
        type: 'heading',
        content: 'Chào mừng bạn đến với dịch vụ của chúng tôi!',
        headingType: 'h1',
        style: {
          color: '#111111',
          fontSize: 28,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 20,
          paddingBottom: 20,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          fontFamily: 'Arial, sans-serif',
        }
      },
      {
        id: `text-${Date.now()}`,
        type: 'text',
        content: 'Cảm ơn bạn đã đăng ký dịch vụ của chúng tôi. Chúng tôi rất vui mừng được chào đón bạn vào cộng đồng của chúng tôi.',
        style: {
          color: '#333333',
          fontSize: 16,
          textAlign: 'center',
          paddingTop: 8,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `image-${Date.now()}`,
        type: 'image',
        src: 'https://via.placeholder.com/600x300?text=Welcome+Image',
        alt: 'Welcome Image',
        style: {
          width: '100%',
          maxWidth: '600px',
          height: 'auto',
          margin: '0 auto 20px auto',
          display: 'block',
        }
      },
      {
        id: `text-${Date.now() + 1}`,
        type: 'text',
        content: 'Dưới đây là một số thông tin hữu ích để bạn bắt đầu:',
        style: {
          color: '#333333',
          fontSize: 16,
          textAlign: 'left',
          paddingTop: 8,
          paddingBottom: 8,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `list-${Date.now()}`,
        type: 'list',
        content: '<ul><li>Hướng dẫn sử dụng dịch vụ</li><li>Các tính năng nổi bật</li><li>Cách liên hệ hỗ trợ</li></ul>',
        style: {
          color: '#333333',
          fontSize: 16,
          textAlign: 'left',
          paddingLeft: 32,
          paddingRight: 16,
          paddingTop: 8,
          paddingBottom: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `button-${Date.now()}`,
        type: 'button',
        text: 'Khám phá ngay',
        url: '#',
        style: {
          backgroundColor: '#0070f3',
          color: '#ffffff',
          padding: 12,
          borderRadius: 4,
          textAlign: 'center',
          fontWeight: 'bold',
          width: '200px',
          margin: '16px auto',
        }
      },
      {
        id: `footer-${Date.now()}`,
        type: 'footer',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#f8f9fa" style="padding: 30px 20px; border-top: 1px solid #e5e7eb;">
                <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                  <tr>
                    <td align="center" style="padding-bottom: 20px;">
                      <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=FB" alt="Facebook" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=TW" alt="Twitter" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=IG" alt="Instagram" width="32" height="32" style="display: block;" /></a>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="padding-bottom: 20px; color: #666; font-size: 14px; line-height: 1.5;">
                      Công ty TNHH ABC<br />
                      Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM<br />
                      Email: <EMAIL> | Điện thoại: (123) 456-7890
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color: #666; font-size: 12px; line-height: 1.5;">
                      © ${new Date().getFullYear()} Công ty ABC. Tất cả các quyền được bảo lưu.<br />
                      <a href="#" style="color: #666; text-decoration: underline;">Chính sách bảo mật</a> |
                      <a href="#" style="color: #666; text-decoration: underline;">Hủy đăng ký</a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '20px 0 0 0',
        }
      }
    ]
  },
  {
    id: 'newsletter',
    name: 'Bản tin định kỳ',
    category: 'Newsletter',
    description: 'Template email bản tin định kỳ với nhiều cột',
    elements: [
      {
        id: `header-${Date.now() + 100}`,
        type: 'header',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#ffffff" style="padding: 20px 0;">
                <img src="https://via.placeholder.com/200x60?text=Newsletter" alt="Logo" width="200" style="display: block; max-width: 100%;" />
              </td>
            </tr>
            <tr>
              <td align="center" bgcolor="#0070f3" style="padding: 10px 0; color: #ffffff;">
                <table border="0" cellspacing="0" cellpadding="0">
                  <tr>
                    <td style="padding: 0 15px;"><a href="#" style="color: #ffffff; text-decoration: none; font-size: 14px;">Tin mới</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #ffffff; text-decoration: none; font-size: 14px;">Sự kiện</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #ffffff; text-decoration: none; font-size: 14px;">Khuyến mãi</a></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '0 0 20px 0',
        }
      },
      {
        id: `heading-${Date.now() + 100}`,
        type: 'heading',
        content: 'Bản tin tháng 6/2023',
        headingType: 'h1',
        style: {
          color: '#111111',
          fontSize: 28,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 20,
          paddingBottom: 20,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          fontFamily: 'Arial, sans-serif',
        }
      },
      {
        id: `text-${Date.now() + 100}`,
        type: 'text',
        content: 'Chào mừng bạn đến với bản tin tháng 6. Dưới đây là những thông tin mới nhất và quan trọng nhất trong tháng.',
        style: {
          color: '#333333',
          fontSize: 16,
          textAlign: 'center',
          paddingTop: 8,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `2columns-${Date.now() + 100}`,
        type: '2columns',
        isContainer: true,
        columnCount: 2,
        columnGap: 20,
        columnPadding: 20,
        columnBgColor: '#f5f5f5',
        style: {
          width: '100%',
          padding: 0,
          marginBottom: 20,
        },
        children: [
          {
            id: `column-left-${Date.now() + 100}`,
            type: 'column',
            isContainer: true,
            columnPosition: 'left',
            style: {
              width: '100%',
              backgroundColor: '#f5f5f5',
              padding: 20,
              borderRadius: 4,
            },
            children: [
              {
                id: `heading-left-${Date.now() + 100}`,
                type: 'heading',
                content: 'Tin tức nổi bật',
                headingType: 'h2',
                parent: `column-left-${Date.now() + 100}`,
                style: {
                  color: '#111111',
                  fontSize: 22,
                  fontWeight: 'bold',
                  marginBottom: 16,
                  lineHeight: 1.2,
                }
              },
              {
                id: `image-left-${Date.now() + 100}`,
                type: 'image',
                src: 'https://via.placeholder.com/300x200?text=News',
                alt: 'Tin tức',
                parent: `column-left-${Date.now() + 100}`,
                style: {
                  width: '100%',
                  height: 'auto',
                  marginBottom: 16,
                  display: 'block',
                }
              },
              {
                id: `text-left-${Date.now() + 100}`,
                type: 'text',
                content: 'Sự kiện quan trọng đã diễn ra trong tháng qua. Đây là mô tả ngắn về sự kiện và tác động của nó.',
                parent: `column-left-${Date.now() + 100}`,
                style: {
                  color: '#333333',
                  fontSize: 16,
                  lineHeight: 1.5,
                  marginBottom: 16,
                }
              },
              {
                id: `button-left-${Date.now() + 100}`,
                type: 'button',
                text: 'Xem thêm',
                url: '#',
                parent: `column-left-${Date.now() + 100}`,
                style: {
                  backgroundColor: '#0070f3',
                  color: '#ffffff',
                  padding: 10,
                  borderRadius: 4,
                  textAlign: 'center',
                  fontWeight: 'bold',
                  display: 'inline-block',
                }
              }
            ]
          },
          {
            id: `column-right-${Date.now() + 100}`,
            type: 'column',
            isContainer: true,
            columnPosition: 'right',
            style: {
              width: '100%',
              backgroundColor: '#f5f5f5',
              padding: 20,
              borderRadius: 4,
            },
            children: [
              {
                id: `heading-right-${Date.now() + 100}`,
                type: 'heading',
                content: 'Sự kiện sắp tới',
                headingType: 'h2',
                parent: `column-right-${Date.now() + 100}`,
                style: {
                  color: '#111111',
                  fontSize: 22,
                  fontWeight: 'bold',
                  marginBottom: 16,
                  lineHeight: 1.2,
                }
              },
              {
                id: `image-right-${Date.now() + 100}`,
                type: 'image',
                src: 'https://via.placeholder.com/300x200?text=Events',
                alt: 'Sự kiện',
                parent: `column-right-${Date.now() + 100}`,
                style: {
                  width: '100%',
                  height: 'auto',
                  marginBottom: 16,
                  display: 'block',
                }
              },
              {
                id: `text-right-${Date.now() + 100}`,
                type: 'text',
                content: 'Các sự kiện sắp diễn ra trong tháng tới. Đừng bỏ lỡ những cơ hội tuyệt vời này.',
                parent: `column-right-${Date.now() + 100}`,
                style: {
                  color: '#333333',
                  fontSize: 16,
                  lineHeight: 1.5,
                  marginBottom: 16,
                }
              },
              {
                id: `button-right-${Date.now() + 100}`,
                type: 'button',
                text: 'Đăng ký',
                url: '#',
                parent: `column-right-${Date.now() + 100}`,
                style: {
                  backgroundColor: '#0070f3',
                  color: '#ffffff',
                  padding: 10,
                  borderRadius: 4,
                  textAlign: 'center',
                  fontWeight: 'bold',
                  display: 'inline-block',
                }
              }
            ]
          }
        ]
      },
      {
        id: `footer-${Date.now() + 100}`,
        type: 'footer',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#0070f3" style="padding: 30px 20px; color: #ffffff;">
                <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                  <tr>
                    <td align="center" style="padding-bottom: 20px;">
                      <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=FB" alt="Facebook" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=TW" alt="Twitter" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=IG" alt="Instagram" width="32" height="32" style="display: block;" /></a>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="padding-bottom: 20px; color: #ffffff; font-size: 14px; line-height: 1.5;">
                      Công ty TNHH ABC<br />
                      Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM<br />
                      Email: <EMAIL> | Điện thoại: (123) 456-7890
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color: #ffffff; font-size: 12px; line-height: 1.5;">
                      © ${new Date().getFullYear()} Công ty ABC. Tất cả các quyền được bảo lưu.<br />
                      <a href="#" style="color: #ffffff; text-decoration: underline;">Chính sách bảo mật</a> |
                      <a href="#" style="color: #ffffff; text-decoration: underline;">Hủy đăng ký</a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '20px 0 0 0',
        }
      }
    ]
  },
  {
    id: 'promotional',
    name: 'Email khuyến mãi',
    category: 'Marketing',
    description: 'Template email thông báo khuyến mãi, giảm giá',
    elements: [
      {
        id: `header-${Date.now() + 200}`,
        type: 'header',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#ffffff" style="padding: 20px 0;">
                <img src="https://via.placeholder.com/200x60?text=Sale" alt="Logo" width="200" style="display: block; max-width: 100%;" />
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '0 0 0 0',
        }
      },
      {
        id: `image-${Date.now() + 200}`,
        type: 'image',
        src: 'https://via.placeholder.com/600x300?text=SALE+50%',
        alt: 'Sale Banner',
        style: {
          width: '100%',
          maxWidth: '600px',
          height: 'auto',
          margin: '0 auto 0 auto',
          display: 'block',
        }
      },
      {
        id: `heading-${Date.now() + 200}`,
        type: 'heading',
        content: 'GIẢM GIÁ LỚN - CHỈ TRONG TUẦN NÀY!',
        headingType: 'h1',
        style: {
          color: '#e63946',
          fontSize: 28,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 20,
          paddingBottom: 10,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          fontFamily: 'Arial, sans-serif',
        }
      },
      {
        id: `text-${Date.now() + 200}`,
        type: 'text',
        content: 'Cơ hội cuối cùng để sở hữu sản phẩm với giá ưu đãi nhất. Giảm đến 50% cho tất cả các sản phẩm.',
        style: {
          color: '#333333',
          fontSize: 16,
          textAlign: 'center',
          paddingTop: 8,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `1column-${Date.now() + 200}`,
        type: '1column',
        isContainer: true,
        columnCount: 1,
        columnPadding: 20,
        columnBgColor: '#f9f9f9',
        style: {
          width: '100%',
          padding: 0,
          marginBottom: 20,
        },
        children: [
          {
            id: `text-col-${Date.now() + 200}`,
            type: 'text',
            content: '<div style="text-align: center; font-size: 24px; font-weight: bold; color: #e63946;">Mã giảm giá: SALE50</div>',
            parent: `1column-${Date.now() + 200}`,
            style: {
              padding: 20,
              backgroundColor: '#f9f9f9',
              borderRadius: 4,
              marginBottom: 20,
            }
          }
        ]
      },
      {
        id: `button-${Date.now() + 200}`,
        type: 'button',
        text: 'MUA NGAY',
        url: '#',
        style: {
          backgroundColor: '#e63946',
          color: '#ffffff',
          padding: 15,
          borderRadius: 4,
          textAlign: 'center',
          fontWeight: 'bold',
          fontSize: 18,
          width: '200px',
          margin: '16px auto 30px auto',
        }
      },
      {
        id: `text-${Date.now() + 201}`,
        type: 'text',
        content: 'Ưu đãi kết thúc vào ngày 30/06/2023. Áp dụng cho tất cả các sản phẩm trên website và tại cửa hàng.',
        style: {
          color: '#666666',
          fontSize: 14,
          textAlign: 'center',
          paddingTop: 0,
          paddingBottom: 20,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `footer-${Date.now() + 200}`,
        type: 'footer',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#333333" style="padding: 30px 20px; color: #ffffff;">
                <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                  <tr>
                    <td align="center" style="padding-bottom: 20px;">
                      <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=FB" alt="Facebook" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=TW" alt="Twitter" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=IG" alt="Instagram" width="32" height="32" style="display: block;" /></a>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color: #ffffff; font-size: 12px; line-height: 1.5;">
                      © ${new Date().getFullYear()} Công ty ABC. Tất cả các quyền được bảo lưu.<br />
                      <a href="#" style="color: #ffffff; text-decoration: underline;">Chính sách bảo mật</a> |
                      <a href="#" style="color: #ffffff; text-decoration: underline;">Hủy đăng ký</a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '20px 0 0 0',
        }
      }
    ]
  }
];

// Định nghĩa kiểu dữ liệu cho asset (hình ảnh)
interface Asset {
  id: string;
  type: 'image';
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  name?: string;
  size?: number;
  category?: string;
  selected?: boolean;
}

// Định nghĩa kiểu dữ liệu cho các phần tử email
interface EmailElement {
  id: string;
  type: string;
  content?: string;
  src?: string;
  url?: string;
  text?: string;
  alt?: string;
  children?: EmailElement[];
  parent?: string;
  draggable?: boolean;
  removable?: boolean;
  editable?: boolean;
  selectable?: boolean;
  hoverable?: boolean;
  copyable?: boolean;
  resizable?: boolean;
  style?: {
    // Typography
    color?: string;
    backgroundColor?: string;
    fontSize?: number;
    fontFamily?: string;
    fontWeight?: string | number;
    fontStyle?: string;
    textAlign?: string;
    lineHeight?: number | string;
    letterSpacing?: number | string;
    textDecoration?: string;
    textTransform?: string;

    // Spacing
    paddingTop?: number;
    paddingBottom?: number;
    paddingLeft?: number;
    paddingRight?: number;
    padding?: number | string;
    marginTop?: number;
    marginBottom?: number;
    marginLeft?: number;
    marginRight?: number;
    margin?: string | number;

    // Borders
    borderWidth?: number;
    borderStyle?: string;
    borderColor?: string;
    borderTop?: string;
    borderBottom?: string;
    borderLeft?: string;
    borderRight?: string;
    borderRadius?: number | string;

    // Dimensions
    width?: string | number;
    height?: string | number;
    minWidth?: string | number;
    maxWidth?: string | number;
    minHeight?: string | number;
    maxHeight?: string | number;

    // Display & Position
    display?: string;
    position?: string;
    top?: number | string;
    right?: number | string;
    bottom?: number | string;
    left?: number | string;
    zIndex?: number;
    float?: string;
    overflow?: string;

    // Flexbox
    flexDirection?: string;
    justifyContent?: string;
    alignItems?: string;
    flexWrap?: string;
    flex?: string | number;
    gap?: number | string;
  };
  attributes?: {
    [key: string]: string;
  };
  // Thuộc tính cho heading
  headingType?: string;

  // Thuộc tính cho columns
  isContainer?: boolean;
  columnCount?: number;
  columnGap?: number;
  columnPadding?: number;
  columnBgColor?: string;
  columnPosition?: 'left' | 'right' | 'center';
  selectedChildId?: string; // ID của phần tử con đang được chọn trong column

  [key: string]: any; // Cho phép các thuộc tính động
}

// Định nghĩa các loại phần tử có thể có
// type ElementType = 'text' | 'image' | 'button' | 'container' | 'divider' | 'spacer' | 'heading' | 'link' | 'list' | 'social' | 'video' | 'html' | 'header' | 'footer' | '1column' | '2columns';

// Props cho component EmailBuilderPage
interface EmailBuilderPageProps {
  initialValue?: string;
  onContentChange?: (htmlContent: string) => void;
  compactMode?: boolean;
}

// History management for undo/redo
interface HistoryState {
  emailElements: EmailElement[];
  selectedIndex: number | null;
}

export default function EmailBuilderPage({
  initialValue,
  onContentChange,
  compactMode = false
}: EmailBuilderPageProps = {}) {
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [selectedElement, setSelectedElement] = useState<EmailElement | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [emailData, setEmailData] = useState<any>(initialEmail);
  const [emailElements, setEmailElements] = useState<EmailElement[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [currentTab, setCurrentTab] = useState('blocks');
  const [openAccordions, setOpenAccordions] = useState<string[]>([]);

  // New state variables for enhanced functionality
  const [history, setHistory] = useState<HistoryState[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [leftPanelVisible, setLeftPanelVisible] = useState(true);
  const [rightPanelVisible, setRightPanelVisible] = useState(true);
  const [_, setStyleTab] = useState('typography');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [, setDraggedElement] = useState<EmailElement | null>(null);
  const [dragOverColumn, setDragOverColumn] = useState<string | null>(null);
  const [dragOverColumnPosition, setDragOverColumnPosition] = useState<'left' | 'right' | null>(null);
  const [showHtmlCode, setShowHtmlCode] = useState(false);
  const [codeTab, setCodeTab] = useState<'html' | 'css'>('html');
  const [cssCode, setCssCode] = useState<string>('');

  // Assets state variables
  const [assets, setAssets] = useState<Asset[]>([
    {
      id: 'asset-1',
      type: 'image',
      src: 'https://down-vn.img.susercontent.com/file/sg-11134301-7rdxk-m185t5goo0r255@resize_w900_nl.webp',
      alt: 'Sample Image 1',
      name: 'Sample Image 1',
      category: 'sample'
    },
    {
      id: 'asset-2',
      type: 'image',
      src: 'https://down-vn.img.susercontent.com/file/sg-11134301-7rdxk-m185t5goo0r255@resize_w900_nl.webp',
      alt: 'Sample Image 2',
      name: 'Sample Image 2',
      category: 'sample'
    },
    {
      id: 'asset-3',
      type: 'image',
      src: 'https://down-vn.img.susercontent.com/file/sg-11134301-7rdxk-m185t5goo0r255@resize_w900_nl.webp',
      alt: 'Sample Image 3',
      name: 'Sample Image 3',
      category: 'sample'
    },
    {
      id: 'asset-4',
      type: 'image',
      src: 'https://down-vn.img.susercontent.com/file/sg-11134301-7rdxk-m185t5goo0r255@resize_w900_nl.webp',
      alt: 'Sample Image 4',
      name: 'Sample Image 4',
      category: 'sample'
    }
  ]);
  const [showAssetUploadDialog, setShowAssetUploadDialog] = useState(false);
  const [uploadImageUrl, setUploadImageUrl] = useState('');
  const [uploadImageName, setUploadImageName] = useState('');
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Refs cho kéo thả
  const canvasRef = useRef<HTMLDivElement>(null);
  // Nạp dữ liệu từ initialValue nếu có
  useEffect(() => {
    if (initialValue && initialValue.trim() !== '' && emailElements.length === 0) {
      // Nếu có initialValue và không có phần tử nào, tạo một phần tử text mặc định
      const newElements = [{
        id: `content-${Date.now()}`,
        type: 'text',
        content: initialValue,
        style: {
          color: '#333333',
          textAlign: 'left',
          fontSize: 16,
          lineHeight: 1.5,
          padding: 16
        }
      }];

      setEmailElements(newElements);
      // Khởi tạo history
      setHistory([{ emailElements: newElements, selectedIndex: null }]);
      setHistoryIndex(0);
    }
  }, [initialValue, emailElements.length]);

  // Cập nhật nội dung HTML khi emailElements thay đổi
  useEffect(() => {
    if (onContentChange && emailElements.length > 0) {
      // Tạo HTML từ các phần tử
      let html = '';
      emailElements.forEach(element => {
        html += renderElementToHtml(element);
      });

      // Gọi callback
      onContentChange(html);
    }
  }, [emailElements, onContentChange]);

  // Thêm vào history khi có thay đổi
  const addToHistory = (elements: EmailElement[], index: number | null) => {
    // Cắt bỏ history sau vị trí hiện tại nếu đang ở giữa
    const newHistory = history.slice(0, historyIndex + 1);
    // Thêm trạng thái mới
    newHistory.push({ emailElements: [...elements], selectedIndex: index });
    // Cập nhật history và index
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Hiển thị/ẩn mã HTML
  const toggleHtmlCode = () => {
    // Nếu đang mở code viewer, đóng lại
    if (showHtmlCode) {
      setShowHtmlCode(false);
      return;
    }

    // Nếu đang đóng, mở lên và tạo CSS code
    const html = generateHTML();
    const css = extractCssFromHtml(html);
    setCssCode(css);
    setShowHtmlCode(true);
  };

  // Trích xuất CSS từ inline styles trong HTML
  const extractCssFromHtml = (html: string): string => {
    // Tạo một đối tượng để lưu trữ các selector và style tương ứng
    const cssRules: Record<string, Record<string, string>> = {};

    // Tạo một DOM parser để phân tích HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Lấy tất cả các phần tử có thuộc tính style
    const elementsWithStyle = doc.querySelectorAll('[style]');

    // Duyệt qua từng phần tử và trích xuất style
    elementsWithStyle.forEach((element, index) => {
      const style = element.getAttribute('style');
      if (style) {
        // Tạo một selector duy nhất cho phần tử này
        const tagName = element.tagName.toLowerCase();
        const className = element.className ? `.${element.className.replace(/\s+/g, '.')}` : '';
        const id = element.id ? `#${element.id}` : '';
        const selector = `${tagName}${id}${className}.element-${index}`;

        // Phân tích style thành các thuộc tính riêng lẻ
        const styleProps: Record<string, string> = {};
        style.split(';').forEach(prop => {
          const [name, value] = prop.split(':').map(s => s.trim());
          if (name && value) {
            styleProps[name] = value;
          }
        });

        // Lưu trữ selector và style
        cssRules[selector] = styleProps;
      }
    });

    // Chuyển đổi cssRules thành chuỗi CSS
    let cssString = '';
    Object.entries(cssRules).forEach(([selector, props]) => {
      cssString += `${selector} {\n`;
      Object.entries(props).forEach(([prop, value]) => {
        cssString += `  ${prop}: ${value};\n`;
      });
      cssString += '}\n\n';
    });

    // Thêm các style từ thẻ <style> trong HTML
    const styleElements = doc.querySelectorAll('style');
    styleElements.forEach(styleElement => {
      cssString += styleElement.textContent + '\n\n';
    });

    return cssString;
  };

  // Asset management functions
  const handleAssetSelect = (asset: Asset) => {
    // Toggle selection
    setAssets(assets.map(a => ({
      ...a,
      selected: a.id === asset.id ? !a.selected : false
    })));

    setSelectedAsset(asset.selected ? null : asset);
  };

  const handleAssetDelete = (assetId: string) => {
    if (confirm('Bạn có chắc chắn muốn xóa hình ảnh này?')) {
      setAssets(assets.filter(asset => asset.id !== assetId));
      if (selectedAsset && selectedAsset.id === assetId) {
        setSelectedAsset(null);
      }
    }
  };

  const handleAssetUpload = () => {
    if (uploadImageUrl) {
      const newAsset: Asset = {
        id: `asset-${Date.now()}`,
        type: 'image',
        src: uploadImageUrl,
        alt: uploadImageName || 'Uploaded Image',
        name: uploadImageName || 'Uploaded Image',
        category: 'uploaded'
      };

      setAssets([...assets, newAsset]);
      setUploadImageUrl('');
      setUploadImageName('');
      setShowAssetUploadDialog(false);

      toast({
        title: "Đã tải lên hình ảnh",
        description: "Hình ảnh đã được thêm vào thư viện.",
      });
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real application, you would upload the file to a server
      // and get back a URL. For this example, we'll use a placeholder.
      const fileName = file.name;
      const fileUrl = `https://via.placeholder.com/800x400?text=${encodeURIComponent(fileName)}`;

      setUploadImageUrl(fileUrl);
      setUploadImageName(fileName);
    }
  };

  const handleUseSelectedAsset = () => {
    if (selectedAsset) {
      if (selectedElement && selectedElement.type === 'image') {
        // Update the selected image element
        updateSelectedElement('src', selectedAsset.src);
        updateSelectedElement('alt', selectedAsset.alt || selectedAsset.name || 'Image');
      } else {
        // Add a new image element
        const newElement: EmailElement = {
          id: `image-${Date.now()}`,
          type: 'image',
          src: selectedAsset.src,
          alt: selectedAsset.alt || selectedAsset.name || 'Image',
          draggable: true,
          removable: true,
          editable: true,
          selectable: true,
          hoverable: true,
          copyable: true,
          style: {
            width: '100%',
            maxWidth: '600px',
            height: 'auto',
            margin: '0 auto 20px auto',
            display: 'block',
          }
        };

        const newElements = [...emailElements, newElement];
        setEmailElements(newElements);
        setSelectedElement(newElement);
        setSelectedIndex(newElements.length - 1);

        // Add to history
        addToHistory(newElements, newElements.length - 1);
      }
    }
  };

  // Thêm phần tử mới vào email
  const addNewElement = (type: string, parentId?: string) => {
    const id = `element-${Date.now()}`;
    let newElement: EmailElement = {
      id,
      type,
      draggable: true,
      removable: true,
      editable: true,
      selectable: true,
      hoverable: true,
      copyable: true,
      parent: parentId || '',
      style: {
        padding: 8,
        margin: 4,
      }
    };

    // Cấu hình mặc định cho từng loại phần tử
    switch (type) {
      case 'text':
        newElement.content = 'Nhấp đôi để chỉnh sửa văn bản này';
        newElement.style = {
          ...newElement.style,
          color: '#333333',
          fontSize: 16,
          textAlign: 'left',
          paddingTop: 8,
          paddingBottom: 8,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        };
        break;

      case 'heading':
        newElement.content = 'Tiêu đề mẫu';
        newElement.headingType = 'h2';
        newElement.style = {
          ...newElement.style,
          color: '#111111',
          fontSize: 24,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 16,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          marginBottom: 16,
          fontFamily: 'Arial, sans-serif',
        };
        break;

      case 'image':
        newElement.src = 'https://via.placeholder.com/600x200?text=Hình+ảnh+mẫu';
        newElement.alt = 'Hình ảnh mẫu';
        newElement.style = {
          ...newElement.style,
          width: '100%',
          paddingTop: 8,
          paddingBottom: 8,
          display: 'block',
        };
        break;

      case 'button':
        newElement.text = 'Nút nhấn';
        newElement.url = '#';
        newElement.style = {
          ...newElement.style,
          backgroundColor: '#0070f3',
          color: '#ffffff',
          padding: 12,
          borderRadius: 4,
          textAlign: 'center',
          fontWeight: 'bold',
          width: '200px',
          margin: '16px auto',
        };
        break;

      case 'divider':
        newElement.style = {
          ...newElement.style,
          borderTop: '1px solid #e5e7eb',
          margin: '16px 0',
          width: '100%',
        };
        break;

      case 'spacer':
        newElement.style = {
          ...newElement.style,
          height: 32,
          width: '100%',
        };
        break;

      case 'container':
        newElement.children = [];
        newElement.style = {
          ...newElement.style,
          padding: 16,
          backgroundColor: '#f9fafb',
          borderRadius: 4,
          width: '100%',
        };
        break;

      case 'link':
        newElement.text = 'Liên kết mẫu';
        newElement.url = '#';
        newElement.style = {
          ...newElement.style,
          color: '#0070f3',
          textDecoration: 'underline',
        };
        break;

      case 'list':
        newElement.content = '<ul><li>Mục danh sách 1</li><li>Mục danh sách 2</li><li>Mục danh sách 3</li></ul>';
        newElement.style = {
          ...newElement.style,
          paddingLeft: 32,
          lineHeight: 1.5,
        };
        break;

      case 'social':
        newElement.content = '<div style="display: flex; justify-content: center; gap: 16px;"><a href="#" style="color: #333;">Facebook</a><a href="#" style="color: #333;">Twitter</a><a href="#" style="color: #333;">Instagram</a></div>';
        newElement.style = {
          ...newElement.style,
          textAlign: 'center',
          padding: 16,
        };
        break;

      case 'html':
        newElement.content = '<div>Mã HTML tùy chỉnh</div>';
        break;

      case 'header':
        newElement.content = `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#ffffff" style="padding: 20px 0;">
                <img src="https://via.placeholder.com/200x60?text=Logo" alt="Logo" width="200" style="display: block; max-width: 100%;" />
              </td>
            </tr>
            <tr>
              <td align="center" bgcolor="#f8f9fa" style="padding: 10px 0; border-top: 1px solid #e5e7eb; border-bottom: 1px solid #e5e7eb;">
                <table border="0" cellspacing="0" cellpadding="0">
                  <tr>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Sản phẩm mới</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Khuyến mãi</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Tin tức</a></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `;
        newElement.style = {
          ...newElement.style,
          padding: 0,
          width: '100%',
          margin: '0 0 20px 0',
        };
        break;

      case 'footer':
        newElement.content = `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#f8f9fa" style="padding: 30px 20px; border-top: 1px solid #e5e7eb;">
                <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                  <tr>
                    <td align="center" style="padding-bottom: 20px;">
                      <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=FB" alt="Facebook" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=TW" alt="Twitter" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=IG" alt="Instagram" width="32" height="32" style="display: block;" /></a>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="padding-bottom: 20px; color: #666; font-size: 14px; line-height: 1.5;">
                      Công ty TNHH ABC<br />
                      Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM<br />
                      Email: <EMAIL> | Điện thoại: (123) 456-7890
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color: #666; font-size: 12px; line-height: 1.5;">
                      © ${new Date().getFullYear()} Công ty ABC. Tất cả các quyền được bảo lưu.<br />
                      <a href="#" style="color: #666; text-decoration: underline;">Chính sách bảo mật</a> |
                      <a href="#" style="color: #666; text-decoration: underline;">Hủy đăng ký</a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `;
        newElement.style = {
          ...newElement.style,
          padding: 0,
          width: '100%',
          margin: '20px 0 0 0',
        };
        break;

      case '1column':
        // Tạo một column container với khả năng chứa các phần tử con
        newElement.children = [];
        newElement.isContainer = true;
        newElement.columnCount = 1;
        newElement.columnGap = 20;
        newElement.columnPadding = 20;
        newElement.columnBgColor = '#f5f5f5';
        newElement.style = {
          ...newElement.style,
          width: '100%',
          padding: 0,
          marginBottom: 20,
        };

        // Thêm một phần tử text mặc định vào column
        const defaultTextFor1Column: EmailElement = {
          id: `text-${Date.now()}-1`,
          type: 'text',
          content: 'Đây là nội dung cột đơn. Nhấp đôi để chỉnh sửa nội dung này. Bạn có thể thêm văn bản, hình ảnh hoặc các thành phần khác vào đây.',
          parent: newElement.id,
          draggable: true,
          removable: true,
          editable: true,
          selectable: true,
          hoverable: true,
          copyable: true,
          style: {
            fontSize: 16,
            lineHeight: 1.5,
            color: '#333333',
            margin: 0,
          }
        };

        newElement.children.push(defaultTextFor1Column);
        break;

      case '2columns':
        // Tạo một column container với khả năng chứa các phần tử con
        newElement.children = [];
        newElement.isContainer = true;
        newElement.columnCount = 2;
        newElement.columnGap = 20;
        newElement.columnPadding = 20;
        newElement.columnBgColor = '#f5f5f5';
        newElement.style = {
          ...newElement.style,
          width: '100%',
          padding: 0,
          marginBottom: 20,
        };

        // Thêm phần tử text mặc định vào column trái
        const leftColumnContainer: EmailElement = {
          id: `column-left-${Date.now()}`,
          type: 'column',
          parent: newElement.id,
          children: [],
          isContainer: true,
          columnPosition: 'left',
          style: {
            width: '100%',
            backgroundColor: newElement.columnBgColor,
            padding: newElement.columnPadding,
            borderRadius: 4,
          }
        };

        // Thêm phần tử text mặc định vào column phải
        const rightColumnContainer: EmailElement = {
          id: `column-right-${Date.now()}`,
          type: 'column',
          parent: newElement.id,
          children: [],
          isContainer: true,
          columnPosition: 'right',
          style: {
            width: '100%',
            backgroundColor: newElement.columnBgColor,
            padding: newElement.columnPadding,
            borderRadius: 4,
          }
        };

        // Thêm cả hai column vào container chính
        newElement.children.push(leftColumnContainer);
        newElement.children.push(rightColumnContainer);
        break;
    }

    const newElements = [...emailElements, newElement];
    setEmailElements(newElements);

    // Chọn phần tử mới thêm
    setSelectedElement(newElement);
    setSelectedIndex(newElements.length - 1);

    // Thêm vào history
    addToHistory(newElements, newElements.length - 1);
  };

  // Chọn phần tử để chỉnh sửa
  const handleSelectElement = (element: EmailElement, index: number) => {
    // Nếu đang chọn phần tử khác, lưu trạng thái hiện tại vào history
    if (selectedIndex !== index && selectedIndex !== null) {
      addToHistory(emailElements, selectedIndex);
    }

    // Xử lý trường hợp phần tử nằm trong column (index = -1)
    if (index === -1) {
      // Tìm phần tử cha (column) của phần tử này
      const parentId = element.parent;
      if (parentId) {
        const parentIndex = emailElements.findIndex(el => el.id === parentId);
        if (parentIndex !== -1) {
          // Đánh dấu phần tử con được chọn trong column
          const parent = emailElements[parentIndex];
          if (parent && parent.selectedChildId !== element.id) {
            const updatedParent = { ...parent, selectedChildId: element.id };
            const newElements = [...emailElements];
            newElements[parentIndex] = updatedParent;
            setEmailElements(newElements);
          }
        }
      }
    } else {
      // Nếu chọn phần tử không phải là con của column, reset selectedChildId
      const updatedElements = emailElements.map(el => {
        if (el.selectedChildId) {
          return { ...el, selectedChildId: undefined };
        }
        return el;
      });
      setEmailElements(updatedElements as EmailElement[]);
    }

    setSelectedElement(element);
    setSelectedIndex(index);
    setStyleTab('typography'); // Reset về tab typography khi chọn phần tử mới
  };

  // Xử lý khi bắt đầu kéo phần tử
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, elementType: string) => {
    e.dataTransfer.setData('text/plain', elementType);
    e.dataTransfer.effectAllowed = 'copy';
    setDraggedElement({ type: elementType } as EmailElement);
  };

  // Áp dụng template email
  const applyEmailTemplate = (template: any) => {
    // Hiển thị xác nhận nếu đã có nội dung
    if (emailElements.length > 0) {
      if (!window.confirm('Bạn có chắc muốn áp dụng template này? Điều này sẽ thay thế tất cả nội dung hiện tại.')) {
        return;
      }
    }

    // Áp dụng template
    setEmailElements([...template.elements]);
    setSelectedElement(null);
    setSelectedIndex(null);

    // Cập nhật thông tin email
    setEmailData({
      ...emailData,
      name: template.name,
      subject: template.name
    });

    // Thêm vào history
    addToHistory([...template.elements], null);

    // Hiển thị thông báo
    toast({
      title: "Đã áp dụng template",
      description: `Template "${template.name}" đã được áp dụng thành công.`,
      duration: 3000,
    });
  };

  // Tạo HTML từ emailElements
  const generateHTML = () => {
    // Tạo phần đầu của HTML
    let html = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${emailData.subject || 'Email Template'}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      line-height: 1.5;
      color: #333333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
    }
    img {
      max-width: 100%;
      height: auto;
    }
    a {
      color: #0070f3;
      text-decoration: underline;
    }
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #0070f3;
      color: #ffffff;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
    }
    .column {
      padding: 10px;
    }
    .two-columns {
      display: table;
      width: 100%;
    }
    .two-columns .column {
      display: table-cell;
      width: 50%;
      vertical-align: top;
    }
    @media only screen and (max-width: 480px) {
      .two-columns .column {
        display: block;
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
`;

    // Tạo phần thân của HTML từ emailElements
    emailElements.forEach(element => {
      html += generateElementHTML(element);
    });

    // Tạo phần cuối của HTML
    html += `  </div>
</body>
</html>`;

    return html;
  };

  // Tạo HTML cho từng phần tử
  const generateElementHTML = (element: EmailElement): string => {
    let elementHtml = '';

    switch (element.type) {
      case 'text':
        elementHtml = `<div style="${generateStyleString(element.style)}">${element.content || 'Văn bản'}</div>`;
        break;

      case 'heading':
        const headingTag = element.headingType || 'h2';
        elementHtml = `<${headingTag} style="${generateStyleString(element.style)}">${element.content || 'Tiêu đề'}</${headingTag}>`;
        break;

      case 'image':
        elementHtml = `<div style="${generateStyleString(element.style)}">
  <img src="${element.src || 'https://via.placeholder.com/600x200?text=Hình+ảnh'}" alt="${element.alt || 'Hình ảnh'}" style="max-width: 100%; display: block;">
</div>`;
        break;

      case 'button':
        const buttonAlign = element.style?.textAlign || 'center';
        elementHtml = `<div style="text-align: ${buttonAlign};">
  <a href="${element.url || '#'}" style="background-color: ${element.style?.backgroundColor || '#0070f3'}; color: ${element.style?.color || '#ffffff'}; padding: ${element.style?.padding || '10px 20px'}; border-radius: ${element.style?.borderRadius || 4}px; border: none; display: inline-block; text-align: center; text-decoration: none; font-weight: ${element.style?.fontWeight || 'bold'}; ${element.style?.width ? `width: ${element.style.width};` : ''}">${element.text || 'Nút nhấn'}</a>
</div>`;
        break;

      case 'divider':
        elementHtml = `<hr style="${generateStyleString(element.style)}">`;
        break;

      case 'spacer':
        elementHtml = `<div style="${generateStyleString(element.style)}">&nbsp;</div>`;
        break;

      case 'link':
        elementHtml = `<a href="${element.url || '#'}" style="${generateStyleString(element.style)}">${element.text || 'Liên kết'}</a>`;
        break;

      case 'list':
        elementHtml = `<div style="${generateStyleString(element.style)}">${element.content || '<ul><li>Mục danh sách</li></ul>'}</div>`;
        break;

      case 'social':
        elementHtml = `<div style="${generateStyleString(element.style)}">${element.content || '<div style="text-align: center;"><a href="#" style="margin: 0 10px;"><img src="https://via.placeholder.com/30" alt="Facebook"></a><a href="#" style="margin: 0 10px;"><img src="https://via.placeholder.com/30" alt="Twitter"></a><a href="#" style="margin: 0 10px;"><img src="https://via.placeholder.com/30" alt="Instagram"></a></div>'}</div>`;
        break;

      case 'html':
        elementHtml = `<div style="${generateStyleString(element.style)}">${element.content || '<div>Mã HTML</div>'}</div>`;
        break;

      case 'header':
        elementHtml = `<div style="${generateStyleString(element.style)}" class="header">${element.content || '<div style="text-align: center; padding: 20px;"><img src="https://via.placeholder.com/200x50" alt="Logo"></div>'}</div>`;
        break;

      case 'footer':
        elementHtml = `<div style="${generateStyleString(element.style)}" class="footer">${element.content || '<div style="text-align: center; padding: 20px; font-size: 12px; color: #666;">© 2023 Công ty của bạn. Tất cả các quyền được bảo lưu.</div>'}</div>`;
        break;

      case '1column':
        elementHtml = `<div style="${generateStyleString(element.style)}" class="column">`;
        if (element.children && element.children.length > 0) {
          element.children.forEach(child => {
            elementHtml += generateElementHTML(child);
          });
        } else {
          elementHtml += '<div style="text-align: center; padding: 20px; color: #999;">Cột trống</div>';
        }
        elementHtml += `</div>`;
        break;

      case '2columns':
        elementHtml = `<div style="${generateStyleString(element.style)}" class="two-columns">`;

        // Tìm cột trái và phải
        const leftColumn = element.children?.find(child => child.columnPosition === 'left');
        const rightColumn = element.children?.find(child => child.columnPosition === 'right');

        // Tạo HTML cho cột trái
        elementHtml += `<div class="column" style="${generateStyleString(leftColumn?.style)}">`;
        if (leftColumn && leftColumn.children && leftColumn.children.length > 0) {
          leftColumn.children.forEach(child => {
            elementHtml += generateElementHTML(child);
          });
        } else {
          elementHtml += '<div style="text-align: center; padding: 20px; color: #999;">Cột trái trống</div>';
        }
        elementHtml += `</div>`;

        // Tạo HTML cho cột phải
        elementHtml += `<div class="column" style="${generateStyleString(rightColumn?.style)}">`;
        if (rightColumn && rightColumn.children && rightColumn.children.length > 0) {
          rightColumn.children.forEach(child => {
            elementHtml += generateElementHTML(child);
          });
        } else {
          elementHtml += '<div style="text-align: center; padding: 20px; color: #999;">Cột phải trống</div>';
        }
        elementHtml += `</div>`;

        elementHtml += `</div>`;
        break;

      default:
        elementHtml = `<div>${element.type}</div>`;
    }

    return elementHtml;
  };

  // Tạo chuỗi CSS từ đối tượng style
  const generateStyleString = (style?: Record<string, any>): string => {
    if (!style) return '';

    return Object.entries(style)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => {
        // Chuyển đổi camelCase sang kebab-case
        const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();

        // Xử lý các trường hợp đặc biệt
        if (typeof value === 'number' && !['zIndex', 'opacity', 'fontWeight', 'lineHeight', 'flex'].includes(key)) {
          return `${cssKey}: ${value}px;`;
        }

        return `${cssKey}: ${value};`;
      })
      .join(' ');
  };


  // Cập nhật thuộc tính của phần tử được chọn
  const updateSelectedElement = (property: string, value: any) => {
    if (selectedElement) {
      const updatedElement = { ...selectedElement };

      if (property.includes('.')) {
        const [parentProp, child] = property.split('.');
        if (parentProp) {
          if (!updatedElement[parentProp as keyof typeof updatedElement]) {
            updatedElement[parentProp as keyof typeof updatedElement] = {};
          } else {
            updatedElement[parentProp as keyof typeof updatedElement] = {
              ...updatedElement[parentProp as keyof typeof updatedElement]
            };
          }

          // Safely update the nested property
          const parentObj = updatedElement[parentProp as keyof typeof updatedElement] as Record<string, any>;
          if (parentObj && child) {
            parentObj[child] = value;
          }
        }
      } else {
        (updatedElement as any)[property] = value;
      }

      if (selectedIndex === -1) {
        const parentId = selectedElement.parent;
        if (parentId) {
          const parentIndex = emailElements.findIndex(el => el.id === parentId);
          if (parentIndex !== -1) {
            const parent = emailElements[parentIndex];

            if (parent && parent.type === '2columns' && parent.children) {
              const leftColumn = parent.children.find(child => child.columnPosition === 'left');
              const rightColumn = parent.children.find(child => child.columnPosition === 'right');

              if (leftColumn && leftColumn.children) {
                // Tìm phần tử trong cột trái
                const childIndex = leftColumn.children.findIndex(child => child.id === selectedElement.id);
                if (childIndex !== -1) {
                  // Cập nhật phần tử trong cột trái
                  const updatedLeftColumn = { ...leftColumn };
                  updatedLeftColumn.children = [...leftColumn.children];
                  updatedLeftColumn.children[childIndex] = updatedElement;

                  // Cập nhật cột trái trong 2columns
                  const updatedParent = { ...parent };
                  updatedParent.children = [...parent.children];
                  const leftColumnIndex = updatedParent.children.findIndex(child => child.id === leftColumn.id);
                  if (leftColumnIndex !== -1) {
                    updatedParent.children[leftColumnIndex] = updatedLeftColumn;
                  }

                  // Cập nhật 2columns trong emailElements
                  const newElements = [...emailElements];
                  newElements[parentIndex] = updatedParent as EmailElement;
                  setEmailElements(newElements);
                  setSelectedElement(updatedElement);

                  return;
                }
              }

              if (rightColumn && rightColumn.children && parent) {
                // Tìm phần tử trong cột phải
                const childIndex = rightColumn.children.findIndex(child => child.id === selectedElement.id);
                if (childIndex !== -1) {
                  // Cập nhật phần tử trong cột phải
                  const updatedRightColumn = { ...rightColumn };
                  updatedRightColumn.children = [...rightColumn.children];
                  updatedRightColumn.children[childIndex] = updatedElement;

                  // Cập nhật cột phải trong 2columns
                  const updatedParent = { ...parent };
                  updatedParent.children = [...parent.children];
                  const rightColumnIndex = updatedParent.children.findIndex(child => child.id === rightColumn.id);
                  if (rightColumnIndex !== -1) {
                    updatedParent.children[rightColumnIndex] = updatedRightColumn;
                  }

                  // Cập nhật 2columns trong emailElements
                  const newElements = [...emailElements];
                  newElements[parentIndex] = updatedParent as EmailElement;
                  setEmailElements(newElements);
                  setSelectedElement(updatedElement);


                  return;
                }
              }
            } else if (parent && parent.children) {
              // Đối với 1column hoặc các loại container khác
              const childIndex = parent.children.findIndex(child => child.id === selectedElement.id);
              if (childIndex !== -1) {
                // Cập nhật phần tử trong container
                const updatedParent = { ...parent };
                updatedParent.children = [...parent.children];
                updatedParent.children[childIndex] = updatedElement;

                // Cập nhật container trong emailElements
                const newElements = [...emailElements];
                newElements[parentIndex] = updatedParent as EmailElement;
                setEmailElements(newElements);
                setSelectedElement(updatedElement);

                return;
              }
            }
          }
        }
      } else if (selectedIndex !== null) {
        // Phần tử nằm trực tiếp trong emailElements
        const newElements = [...emailElements];
        newElements[selectedIndex] = updatedElement;
        setEmailElements(newElements);
        setSelectedElement(updatedElement);
      }
    }
  };

  // Thêm phần tử vào column
  const addElementToColumn = (columnId: string, elementType: string): string | null => {
    // Tìm column trong emailElements
    const columnIndex = emailElements.findIndex(el => el.id === columnId);
    if (columnIndex === -1) return null;

    const column = emailElements[columnIndex];

    if (column && column.type === '1column' && column.children !== undefined) {
      // Tạo phần tử mới
      const newChildId = `element-${Date.now()}`;
      const newChild: EmailElement = {
        id: newChildId,
        type: elementType,
        parent: columnId,
        draggable: true,
        removable: true,
        editable: true,
        selectable: true,
        hoverable: true,
        copyable: true,
        style: {}
      };

      // Cấu hình mặc định dựa trên loại phần tử
      switch (elementType) {
        case 'text':
          newChild.content = 'Nhấp đôi để chỉnh sửa văn bản này';
          newChild.style = {
            ...newChild.style,
            color: '#333333',
            fontSize: 16,
            lineHeight: 1.5,
            margin: '0 0 10px 0',
          };
          break;

        case 'image':
          newChild.src = 'https://via.placeholder.com/600x200?text=Hình+ảnh+mẫu';
          newChild.alt = 'Hình ảnh mẫu';
          newChild.style = {
            ...newChild.style,
            width: '100%',
            display: 'block',
            margin: '10px 0',
          };
          break;

        case 'button':
          newChild.text = 'Nút nhấn';
          newChild.url = '#';
          newChild.style = {
            ...newChild.style,
            backgroundColor: '#0070f3',
            color: '#ffffff',
            padding: 12,
            borderRadius: 4,
            textAlign: 'center',
            fontWeight: 'bold',
            margin: '10px 0',
            display: 'inline-block',
          };
          break;

        case 'heading':
          newChild.content = 'Tiêu đề mẫu';
          newChild.headingType = 'h2';
          newChild.style = {
            ...newChild.style,
            color: '#111111',
            fontSize: 24,
            fontWeight: 'bold',
            margin: '10px 0',
            lineHeight: 1.2,
          };
          break;

        case 'list':
          newChild.content = '<ul><li>Mục danh sách 1</li><li>Mục danh sách 2</li><li>Mục danh sách 3</li></ul>';
          newChild.style = {
            ...newChild.style,
            color: '#333333',
            fontSize: 16,
            lineHeight: 1.5,
            margin: '10px 0',
          };
          break;

        case 'divider':
          newChild.style = {
            ...newChild.style,
            borderTop: '1px solid #e0e0e0',
            margin: '20px 0',
            width: '100%',
          };
          break;

        case 'link':
          newChild.text = 'Liên kết mẫu';
          newChild.url = '#';
          newChild.style = {
            ...newChild.style,
            color: '#0070f3',
            textDecoration: 'underline',
            fontSize: 16,
          };
          break;
      }

      // Cập nhật column với phần tử mới
      const updatedColumn = { ...column };
      updatedColumn.children = [...(updatedColumn.children || []), newChild];

      // Cập nhật emailElements
      const newElements = [...emailElements];
      newElements[columnIndex] = updatedColumn;
      setEmailElements(newElements);

      // Thêm vào history
      addToHistory(newElements, columnIndex);

      // Tự động chọn phần tử mới được thêm vào
      setSelectedElement(newChild);
      setSelectedIndex(-1);

      return newChildId;
    }

    return null;
  };

  // Xóa phần tử đã chọn
  const deleteSelectedElement = () => {
    if (selectedIndex !== null) {
      // Lưu trạng thái trước khi xóa
      addToHistory(emailElements, selectedIndex);

      const newElements = [...emailElements];
      newElements.splice(selectedIndex, 1);
      setEmailElements(newElements);
      setSelectedElement(null);
      setSelectedIndex(null);
    }
  };

  // Di chuyển phần tử lên trên
  const moveElementUp = () => {
    if (selectedIndex !== null && selectedIndex > 0) {
      // Lưu trạng thái trước khi di chuyển
      addToHistory(emailElements, selectedIndex);

      const newElements = [...emailElements];
      const temp = newElements[selectedIndex];
      newElements[selectedIndex] = newElements[selectedIndex - 1] as EmailElement;
      newElements[selectedIndex - 1] = temp as EmailElement;

      setEmailElements(newElements);
      setSelectedIndex(selectedIndex - 1);
      setSelectedElement(temp as EmailElement);
    }
  };

  // Di chuyển phần tử xuống dưới
  const moveElementDown = () => {
    if (selectedIndex !== null && selectedIndex < emailElements.length - 1) {
      // Lưu trạng thái trước khi di chuyển
      addToHistory(emailElements, selectedIndex);

      const newElements = [...emailElements];
      const temp = newElements[selectedIndex];
      newElements[selectedIndex] = newElements[selectedIndex + 1] as EmailElement;
      newElements[selectedIndex + 1] = temp as EmailElement;

      setEmailElements(newElements);
      setSelectedIndex(selectedIndex + 1);
      setSelectedElement(temp as EmailElement);
    }
  };

  // Thêm các nút di chuyển lên/xuống vào giao diện
  const renderMoveButtons = (index: number) => {
    return (
      <div className="flex gap-1">
        {index > 0 && (
          <Button
            size="sm"
            variant="secondary"
            className="h-7 w-7 p-0 bg-white shadow-sm"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedIndex(index);
              setSelectedElement(emailElements[index] as EmailElement);
              moveElementUp();
            }}
          >
            <ChevronUp size={14} />
          </Button>
        )}
        {index < emailElements.length - 1 && (
          <Button
            size="sm"
            variant="secondary"
            className="h-7 w-7 p-0 bg-white shadow-sm"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedIndex(index);
              setSelectedElement(emailElements[index] as EmailElement);
              moveElementDown();
            }}
          >
            <ChevronDown size={14} />
          </Button>
        )}
      </div>
    );
  };

  // Hàm chuyển đổi phần tử thành HTML
  const renderElementToHtml = (element: EmailElement): string => {
    const style = element.style || {};
    // Chuyển đổi style object thành inline CSS
    const styleString = Object.entries(style)
      .map(([key, value]) => {
        // Bỏ qua nếu giá trị undefined
        if (value === undefined) return null;

        // Chuyển đổi camelCase thành kebab-case cho CSS
        const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();

        // Thêm đơn vị px cho giá trị số (ngoại trừ opacity, z-index, fontWeight, etc.)
        const skipPx = ['opacity', 'z-index', 'font-weight', 'line-height', 'zoom', 'font-size-adjust'];
        const needsPx = typeof value === 'number' && !skipPx.some(k => cssKey.includes(k));

        return `${cssKey}: ${value}${needsPx ? 'px' : ''}`;
      })
      .filter(Boolean)  // Loại bỏ các giá trị null/undefined
      .join('; ');

    switch (element.type) {
      case 'text':
        return `<div style="${styleString}">${element.content || ''}</div>`;

      case 'heading':
        const headingTag = element.headingType || 'h2';
        return `<${headingTag} style="${styleString}">${element.content || 'Heading'}</${headingTag}>`;

      case 'image':
        return `<div style="${styleString}">
          <img src="${element.src}"
            style="max-width: 100%; display: block; border: 0; outline: none;"
            width="${style.width || '100%'}"
            alt="${element.alt || 'Image'}"
          />
        </div>`;

      case 'button':
        // Cải thiện style cho button trong email
        const buttonStyle = `display: inline-block; text-decoration: none; background-color: ${style.backgroundColor || '#3b82f6'}; color: ${style.color || '#ffffff'}; padding: ${style.padding || '12px 20px'}; border-radius: ${style.borderRadius || '4px'}; text-align: center; font-weight: ${style.fontWeight || 'bold'};`;
        return `<div style="text-align: ${style.textAlign || 'center'}; margin: ${style.margin || '10px 0'};">
          <a href="${element.url || '#'}" target="_blank" style="${buttonStyle}">${element.text || 'Click here'}</a>
        </div>`;

      case 'divider':
        return `<hr style="${styleString}" />`;

      case 'spacer':
        return `<div style="${styleString}">&nbsp;</div>`;

      case 'container':
        let containerContent = '';
        if (element.children && element.children.length > 0) {
          element.children.forEach(child => {
            containerContent += renderElementToHtml(child);
          });
        }
        return `<div style="${styleString}">${containerContent}</div>`;

      case 'link':
        return `<a href="${element.url || '#'}" target="_blank" style="${styleString}">${element.text || 'Link'}</a>`;

      case 'list':
        return `<div style="${styleString}">${element.content || '<ul><li>List item</li></ul>'}</div>`;

      case 'social':
        return `<div style="${styleString}">${element.content || ''}</div>`;

      case 'html':
        return element.content || '';

      case 'header':
      case 'footer':
        return `<div style="${styleString}">${element.content || ''}</div>`;

      case '1column':
        if (element.children && element.children.length > 0) {
          let columnContent = '';
          element.children.forEach(child => {
            columnContent += renderElementToHtml(child);
          });

          return `
            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
              <tr>
                <td align="center" style="padding: 20px 0;">
                  <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                    <tr>
                      <td bgcolor="${element.columnBgColor || '#f5f5f5'}" style="padding: ${element.columnPadding || 20}px; border-radius: 4px;">
                        ${columnContent}
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          `;
        } else {
          return `
            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
              <tr>
                <td align="center" style="padding: 20px 0;">
                  <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                    <tr>
                      <td bgcolor="${element.columnBgColor || '#f5f5f5'}" style="padding: ${element.columnPadding || 20}px; border-radius: 4px;">
                        <p style="margin: 0; font-size: 16px; line-height: 1.5; color: #333333;">
                          Cột trống. Thêm nội dung vào đây.
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          `;
        }

      case '2columns':
        if (element.children && element.children.length > 0) {
          // Tìm column trái và phải
          const leftColumn = element.children.find(child => child.columnPosition === 'left');
          const rightColumn = element.children.find(child => child.columnPosition === 'right');

          // Render nội dung cho mỗi column
          let leftColumnContent = '';
          let rightColumnContent = '';

          if (leftColumn && leftColumn.children) {
            leftColumn.children.forEach(child => {
              leftColumnContent += renderElementToHtml(child);
            });
          }

          if (rightColumn && rightColumn.children) {
            rightColumn.children.forEach(child => {
              rightColumnContent += renderElementToHtml(child);
            });
          }

          return `
            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
              <tr>
                <td align="center" style="padding: 20px 0;">
                  <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                    <tr>
                      <td valign="top" style="width: 50%;">
                        <table border="0" cellspacing="0" cellpadding="0" width="100%">
                          <tr>
                            <td bgcolor="${element.columnBgColor || '#f5f5f5'}" style="padding: ${element.columnPadding || 20}px; border-radius: 4px; margin-right: 10px;">
                              ${leftColumnContent || '<p style="margin: 0;">Cột trống</p>'}
                            </td>
                          </tr>
                        </table>
                      </td>
                      <td width="${element.columnGap || 20}" style="font-size: 1px; line-height: 1px;">&nbsp;</td>
                      <td valign="top" style="width: 50%;">
                        <table border="0" cellspacing="0" cellpadding="0" width="100%">
                          <tr>
                            <td bgcolor="${element.columnBgColor || '#f5f5f5'}" style="padding: ${element.columnPadding || 20}px; border-radius: 4px; margin-left: 10px;">
                              ${rightColumnContent || '<p style="margin: 0;">Cột trống</p>'}
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          `;
        } else {
          return `
            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
              <tr>
                <td align="center" style="padding: 20px 0;">
                  <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                    <tr>
                      <td valign="top" style="width: 50%;">
                        <table border="0" cellspacing="0" cellpadding="0" width="100%">
                          <tr>
                            <td bgcolor="#f5f5f5" style="padding: 20px; border-radius: 4px; margin-right: 10px;">
                              <p style="margin: 0; font-size: 16px; line-height: 1.5; color: #333333;">
                                Cột trống. Thêm nội dung vào đây.
                              </p>
                            </td>
                          </tr>
                        </table>
                      </td>
                      <td width="20" style="font-size: 1px; line-height: 1px;">&nbsp;</td>
                      <td valign="top" style="width: 50%;">
                        <table border="0" cellspacing="0" cellpadding="0" width="100%">
                          <tr>
                            <td bgcolor="#f5f5f5" style="padding: 20px; border-radius: 4px; margin-left: 10px;">
                              <p style="margin: 0; font-size: 16px; line-height: 1.5; color: #333333;">
                                Cột trống. Thêm nội dung vào đây.
                              </p>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          `;
        }

      case 'column':
        if (element.children && element.children.length > 0) {
          let columnContent = '';
          element.children.forEach(child => {
            columnContent += renderElementToHtml(child);
          });
          return columnContent;
        } else {
          return '<p style="margin: 0;">Cột trống</p>';
        }

      default:
        return '';
    }
  };

  // Helper để hiển thị phần tử trong chế độ Design
  const renderDesignElement = (element: EmailElement) => {
    switch (element.type) {
      case 'text':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || 'Văn bản' }}
            onDoubleClick={(e) => {
              e.stopPropagation();
              // Tạo một textarea để chỉnh sửa nội dung
              const textContent = element.content || '';
              const textarea = document.createElement('textarea');
              textarea.value = textContent.replace(/<[^>]*>/g, ''); // Loại bỏ các thẻ HTML
              textarea.style.width = '100%';
              textarea.style.minHeight = '100px';
              textarea.style.padding = '8px';
              textarea.style.border = '1px solid #ccc';
              textarea.style.borderRadius = '4px';
              textarea.style.resize = 'vertical';

              // Thay thế phần tử hiện tại bằng textarea
              const target = e.currentTarget;
              target.innerHTML = '';
              target.appendChild(textarea);
              textarea.focus();

              // Xử lý khi blur (mất focus) khỏi textarea
              textarea.onblur = () => {
                const newContent = textarea.value;
                // Cập nhật nội dung của phần tử
                updateSelectedElement('content', newContent);
                // Khôi phục lại hiển thị
                target.innerHTML = newContent;
              };

              // Xử lý khi nhấn Enter
              textarea.onkeydown = (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  textarea.blur();
                }
              };
            }}
          />
        );

      case 'heading':
        const HeadingTag = element.headingType || 'h2';
        return React.createElement(HeadingTag, {
          style: element.style as React.CSSProperties,
          dangerouslySetInnerHTML: { __html: element.content || 'Tiêu đề' },
          onDoubleClick: (e: React.MouseEvent<HTMLElement>) => {
            e.stopPropagation();
            // Tạo một input để chỉnh sửa nội dung
            const textContent = element.content || '';
            const input = document.createElement('input');
            input.value = textContent.replace(/<[^>]*>/g, ''); // Loại bỏ các thẻ HTML
            input.style.width = '100%';
            input.style.padding = '8px';
            input.style.border = '1px solid #ccc';
            input.style.borderRadius = '4px';
            input.style.fontSize = `${element.style?.fontSize || 24}px`;
            input.style.fontWeight = element.style?.fontWeight as string || 'bold';

            // Thay thế phần tử hiện tại bằng input
            const target = e.currentTarget;
            target.innerHTML = '';
            target.appendChild(input);
            input.focus();

            // Xử lý khi blur (mất focus) khỏi input
            input.onblur = () => {
              const newContent = input.value;
              // Cập nhật nội dung của phần tử
              updateSelectedElement('content', newContent);
              // Khôi phục lại hiển thị
              target.innerHTML = newContent;
            };

            // Xử lý khi nhấn Enter
            input.onkeydown = (e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                input.blur();
              }
            };
          }
        });

      case 'image':
        return (
          <div style={element.style as React.CSSProperties}>
            <img
              src={element.src || 'https://via.placeholder.com/600x200?text=Hình+ảnh'}
              alt={element.alt || 'Hình ảnh'}
              style={{ maxWidth: '100%', display: 'block' }}
              onDoubleClick={(e) => {
                e.stopPropagation();
                // Hiển thị dialog để chỉnh sửa URL hình ảnh
                const imageUrl = element.src || '';
                const imageAlt = element.alt || '';

                // Tạo form để chỉnh sửa
                const form = document.createElement('div');
                form.style.display = 'flex';
                form.style.flexDirection = 'column';
                form.style.gap = '8px';
                form.style.padding = '8px';
                form.style.backgroundColor = '#fff';
                form.style.border = '1px solid #ccc';
                form.style.borderRadius = '4px';
                form.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                form.style.position = 'absolute';
                form.style.zIndex = '1000';
                form.style.width = '300px';

                // URL input
                const urlLabel = document.createElement('label');
                urlLabel.textContent = 'URL hình ảnh:';
                urlLabel.style.fontSize = '12px';
                urlLabel.style.fontWeight = 'bold';

                const urlInput = document.createElement('input');
                urlInput.value = imageUrl;
                urlInput.style.width = '100%';
                urlInput.style.padding = '6px';
                urlInput.style.border = '1px solid #ccc';
                urlInput.style.borderRadius = '4px';
                urlInput.style.marginBottom = '8px';

                // Alt text input
                const altLabel = document.createElement('label');
                altLabel.textContent = 'Văn bản thay thế (alt):';
                altLabel.style.fontSize = '12px';
                altLabel.style.fontWeight = 'bold';

                const altInput = document.createElement('input');
                altInput.value = imageAlt;
                altInput.style.width = '100%';
                altInput.style.padding = '6px';
                altInput.style.border = '1px solid #ccc';
                altInput.style.borderRadius = '4px';
                altInput.style.marginBottom = '8px';

                // Nút lưu
                const saveButton = document.createElement('button');
                saveButton.textContent = 'Cập nhật hình ảnh';
                saveButton.style.padding = '6px 12px';
                saveButton.style.backgroundColor = '#0070f3';
                saveButton.style.color = '#fff';
                saveButton.style.border = 'none';
                saveButton.style.borderRadius = '4px';
                saveButton.style.cursor = 'pointer';

                // Thêm các phần tử vào form
                form.appendChild(urlLabel);
                form.appendChild(urlInput);
                form.appendChild(altLabel);
                form.appendChild(altInput);
                form.appendChild(saveButton);

                // Thêm form vào body
                document.body.appendChild(form);

                // Định vị form gần vị trí của hình ảnh
                const rect = e.currentTarget.getBoundingClientRect();
                form.style.top = `${rect.top + window.scrollY}px`;
                form.style.left = `${rect.left + window.scrollX}px`;

                // Focus vào input
                urlInput.focus();

                // Xử lý khi nhấn nút lưu
                saveButton.onclick = () => {
                  const newUrl = urlInput.value;
                  const newAlt = altInput.value;

                  // Cập nhật thuộc tính của phần tử
                  updateSelectedElement('src', newUrl);
                  updateSelectedElement('alt', newAlt);

                  // Xóa form
                  document.body.removeChild(form);
                };

                // Xử lý khi nhấn Escape để đóng form
                const handleKeyDown = (e: KeyboardEvent) => {
                  if (e.key === 'Escape') {
                    document.body.removeChild(form);
                    document.removeEventListener('keydown', handleKeyDown);
                  }
                };

                document.addEventListener('keydown', handleKeyDown);

                // Xử lý khi click ra ngoài form
                const handleClickOutside = (e: MouseEvent) => {
                  if (!form.contains(e.target as Node)) {
                    document.body.removeChild(form);
                    document.removeEventListener('click', handleClickOutside);
                    document.removeEventListener('keydown', handleKeyDown);
                  }
                };

                // Đợi một chút trước khi thêm event listener để tránh trigger ngay lập tức
                setTimeout(() => {
                  document.addEventListener('click', handleClickOutside);
                }, 100);
              }}
            />
          </div>
        );

      case 'button':
        return (
          <div style={{ textAlign: element.style?.textAlign as React.CSSProperties['textAlign'] || 'center' }}>
            <button
              style={{
                backgroundColor: element.style?.backgroundColor,
                color: element.style?.color,
                padding: element.style?.padding,
                borderRadius: `${element.style?.borderRadius}px`,
                border: 'none',
                cursor: 'pointer',
                fontWeight: element.style?.fontWeight as React.CSSProperties['fontWeight'] || 'bold',
                display: 'inline-block',
                textAlign: 'center',
                textDecoration: 'none',
                width: element.style?.width as React.CSSProperties['width'],
              }}
              onDoubleClick={(e) => {
                e.stopPropagation();
                // Tạo một input để chỉnh sửa nội dung
                const textContent = element.text || '';
                const input = document.createElement('input');
                input.value = textContent;
                input.style.width = '100%';
                input.style.padding = '8px';
                input.style.border = '1px solid #ccc';
                input.style.borderRadius = '4px';
                input.style.color = '#000000'; // Đảm bảo text có thể đọc được

                // Thay thế phần tử hiện tại bằng input
                const target = e.currentTarget;
                target.innerHTML = '';
                target.appendChild(input);
                input.focus();

                // Xử lý khi blur (mất focus) khỏi input
                input.onblur = () => {
                  const newContent = input.value;
                  // Cập nhật nội dung của phần tử
                  updateSelectedElement('text', newContent);
                  // Khôi phục lại hiển thị
                  target.innerHTML = newContent;
                };

                // Xử lý khi nhấn Enter
                input.onkeydown = (e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    input.blur();
                  }
                };
              }}
            >
              {element.text || 'Nút nhấn'}
            </button>
          </div>
        );

      case 'divider':
        return (
          <hr style={element.style as React.CSSProperties} />
        );

      case 'spacer':
        return (
          <div style={element.style as React.CSSProperties}>&nbsp;</div>
        );

      case 'container':
        return (
          <div style={element.style as React.CSSProperties}>
            {element.children && element.children.length > 0 ? (
              element.children.map((child, idx) => (
                <div key={`container-child-${child.id}-${idx}`}>
                  {renderDesignElement(child)}
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-muted-foreground text-sm border border-dashed rounded">
                Container trống
              </div>
            )}
          </div>
        );

      case 'link':
        return (
          <a
            href="#"
            onClick={(e) => e.preventDefault()}
            style={{
              ...element.style as React.CSSProperties,
              cursor: 'pointer',
              textDecoration: element.style?.textDecoration || 'underline',
            }}
            onDoubleClick={(e) => {
              e.stopPropagation();
              // Tạo một form để chỉnh sửa cả text và URL
              const form = document.createElement('div');
              form.style.display = 'flex';
              form.style.flexDirection = 'column';
              form.style.gap = '8px';
              form.style.padding = '8px';
              form.style.backgroundColor = '#fff';
              form.style.border = '1px solid #ccc';
              form.style.borderRadius = '4px';
              form.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';

              // Input cho text
              const textLabel = document.createElement('label');
              textLabel.textContent = 'Văn bản liên kết:';
              textLabel.style.fontSize = '12px';
              textLabel.style.fontWeight = 'bold';

              const textInput = document.createElement('input');
              textInput.value = element.text || '';
              textInput.style.width = '100%';
              textInput.style.padding = '6px';
              textInput.style.border = '1px solid #ccc';
              textInput.style.borderRadius = '4px';
              textInput.style.marginBottom = '8px';

              // Input cho URL
              const urlLabel = document.createElement('label');
              urlLabel.textContent = 'URL:';
              urlLabel.style.fontSize = '12px';
              urlLabel.style.fontWeight = 'bold';

              const urlInput = document.createElement('input');
              urlInput.value = element.url || '#';
              urlInput.style.width = '100%';
              urlInput.style.padding = '6px';
              urlInput.style.border = '1px solid #ccc';
              urlInput.style.borderRadius = '4px';
              urlInput.style.marginBottom = '8px';

              // Nút lưu
              const saveButton = document.createElement('button');
              saveButton.textContent = 'Lưu';
              saveButton.style.padding = '6px 12px';
              saveButton.style.backgroundColor = '#0070f3';
              saveButton.style.color = '#fff';
              saveButton.style.border = 'none';
              saveButton.style.borderRadius = '4px';
              saveButton.style.cursor = 'pointer';

              // Thêm các phần tử vào form
              form.appendChild(textLabel);
              form.appendChild(textInput);
              form.appendChild(urlLabel);
              form.appendChild(urlInput);
              form.appendChild(saveButton);

              // Thay thế phần tử hiện tại bằng form
              const target = e.currentTarget;
              target.innerHTML = '';
              target.appendChild(form);
              textInput.focus();

              // Xử lý khi nhấn nút lưu
              saveButton.onclick = () => {
                const newText = textInput.value;
                const newUrl = urlInput.value;
                // Cập nhật nội dung của phần tử
                updateSelectedElement('text', newText);
                updateSelectedElement('url', newUrl);
                // Khôi phục lại hiển thị
                target.innerHTML = newText;
              };

              // Xử lý khi nhấn Enter
              const handleEnter = (e: KeyboardEvent) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  saveButton.click();
                }
              };

              textInput.onkeydown = handleEnter;
              urlInput.onkeydown = handleEnter;
            }}
          >
            {element.text || 'Liên kết'}
          </a>
        );

      case 'list':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '<ul><li>Mục danh sách</li></ul>' }}
            onDoubleClick={(e) => {
              e.stopPropagation();
              // Tạo một textarea để chỉnh sửa nội dung
              const textContent = element.content || '';
              const textarea = document.createElement('textarea');
              textarea.value = textContent;
              textarea.style.width = '100%';
              textarea.style.minHeight = '120px';
              textarea.style.padding = '8px';
              textarea.style.border = '1px solid #ccc';
              textarea.style.borderRadius = '4px';
              textarea.style.resize = 'vertical';

              // Thay thế phần tử hiện tại bằng textarea
              const target = e.currentTarget;
              target.innerHTML = '';
              target.appendChild(textarea);
              textarea.focus();

              // Xử lý khi blur (mất focus) khỏi textarea
              textarea.onblur = () => {
                const newContent = textarea.value;
                // Cập nhật nội dung của phần tử
                updateSelectedElement('content', newContent);
                // Khôi phục lại hiển thị
                target.innerHTML = newContent;
              };

              // Xử lý khi nhấn Ctrl+Enter
              textarea.onkeydown = (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                  e.preventDefault();
                  textarea.blur();
                }
              };
            }}
          />
        );

      case 'social':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '' }}
          />
        );

      case 'html':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '<div>Mã HTML</div>' }}
          />
        );

      case 'header':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '<div>Header</div>' }}
            className="border-b"
          />
        );

      case 'footer':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '<div>Footer</div>' }}
            className="border-t"
          />
        );

      case '1column':
        return (
          <div
            className="column-container border border-dashed rounded p-4 mb-4 relative group hover:border-primary"
            style={{
              ...element.style as React.CSSProperties,
              backgroundColor: '#fff',
            }}
          >
            <div className="column-controls absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10 bg-white/80 rounded shadow-sm flex gap-1 p-1">
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                title="Thêm phần tử"
                onClick={(e) => {
                  e.stopPropagation();
                  // Hiển thị menu để chọn loại phần tử muốn thêm
                  const showAddElementMenu = () => {
                    // Thêm phần tử text mặc định
                    addElementToColumn(element.id, 'text');
                  };

                  showAddElementMenu();
                }}
              >
                <Plus size={14} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                title="Xóa column"
                onClick={(e) => {
                  e.stopPropagation();
                  // Xác nhận xóa column
                  const newElements = emailElements.filter(el => el.id !== element.id);
                  setEmailElements(newElements);

                  // Nếu đang chọn column này, bỏ chọn
                  if (selectedElement && selectedElement.id === element.id) {
                    setSelectedElement(null);
                    setSelectedIndex(null);
                  }
                }}
              >
                <Trash2 size={14} />
              </Button>
            </div>
            <div
              className={`column-content p-4 rounded ${dragOverColumn === element.id ? 'bg-primary/10 border-2 border-primary border-dashed' : ''}`}
              style={{
                backgroundColor: dragOverColumn === element.id ? 'rgba(0, 112, 243, 0.1)' : (element.columnBgColor || '#f5f5f5'),
                padding: `${element.columnPadding || 20}px`,
                transition: 'background-color 0.2s',
              }}
              onDragOver={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setDragOverColumn(element.id);
                e.dataTransfer.dropEffect = 'copy';
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setDragOverColumn(null);
              }}
              onDrop={(e) => {
                e.preventDefault();
                e.stopPropagation();
                const elementType = e.dataTransfer.getData('text/plain');
                if (elementType) {
                  // Xóa nội dung mặc định nếu có
                  if (element.children && element.children.length > 0) {
                    // Nếu chỉ có phần tử mặc định (thường là text), xóa nó đi
                    if (element.children && element.children.length === 1 && element.children[0]?.type === 'text' &&
                      element.children[0].content && element.children[0].content.includes('Nhấp đôi để chỉnh sửa')) {
                      // Xóa phần tử mặc định
                      const updatedElement = { ...element };
                      updatedElement.children = [];

                      // Cập nhật element trong emailElements
                      const elementIndex = emailElements.findIndex(el => el.id === element.id);
                      if (elementIndex !== -1) {
                        const newElements = [...emailElements];
                        newElements[elementIndex] = updatedElement;
                        setEmailElements(newElements);
                      }
                    }
                  }
                  // Thêm phần tử mới
                  const newElementId = addElementToColumn(element.id, elementType);
                  setDragOverColumn(null);
                  // Tìm phần tử vừa thêm để chọn
                  if (newElementId) {
                    // Tìm phần tử trong children của column
                    const columnIndex = emailElements.findIndex(el => el.id === element.id);
                    if (columnIndex !== -1 && emailElements[columnIndex]?.children) {
                      const newElement = emailElements[columnIndex].children.find(child => child.id === newElementId);
                      if (newElement) {
                        // Chọn phần tử mới thêm để chỉnh sửa
                        setTimeout(() => {
                          handleSelectElement(newElement, -1); // -1 là index ảo, sẽ được xử lý trong hàm handleSelectElement
                        }, 100);
                      }
                    }
                  }
                }
              }}
            >
              {element.children && element.children.length > 0 ? (
                element.children.map((child, idx) => (
                  <div
                    key={`column-child-${child.id}-${idx}`}
                    className={`mb-2 last:mb-0 relative ${element.selectedChildId === child.id ? 'ring-2 ring-primary' : 'hover:ring-1 hover:ring-primary/50'}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectElement(child, -1);
                    }}
                  >
                    {renderDesignElement(child)}
                    {element.selectedChildId === child.id && (
                      <div className="absolute right-2 top-2 flex gap-1 bg-white/80 rounded p-1">
                        <Button
                          size="sm"
                          variant="secondary"
                          className="h-6 w-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Xóa phần tử này khỏi column
                            const updatedElement = { ...element };
                            updatedElement.children = element.children?.filter(c => c.id !== child.id) || [];

                            // Cập nhật element trong emailElements
                            const elementIndex = emailElements.findIndex(el => el.id === element.id);
                            if (elementIndex !== -1) {
                              const newElements = [...emailElements];
                              newElements[elementIndex] = updatedElement;
                              setEmailElements(newElements);
                              setSelectedElement(updatedElement);
                              setSelectedIndex(elementIndex);
                            }
                          }}
                        >
                          <Trash2 size={12} />
                        </Button>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center text-muted-foreground text-sm p-4 border border-dashed rounded">
                  Kéo thả hoặc nhấp vào nút + để thêm nội dung vào đây.
                </div>
              )}
            </div>
          </div>
        );

      case '2columns':
        // Tìm column trái và phải
        const leftColumn = element.children?.find(child => child.columnPosition === 'left');
        const rightColumn = element.children?.find(child => child.columnPosition === 'right');

        return (
          <div
            className="columns-container border border-dashed rounded p-4 mb-4 relative group hover:border-primary"
            style={{
              ...element.style as React.CSSProperties,
              backgroundColor: '#fff',
            }}
          >
            <div className="column-controls absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10 bg-white/80 rounded shadow-sm flex gap-1 p-1">
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                title="Thêm phần tử vào cột trái"
                onClick={(e) => {
                  e.stopPropagation();
                  // Tìm column trái
                  const leftColumn = element.children?.find(child => child.columnPosition === 'left');
                  if (leftColumn) {
                    // Tạo phần tử text mới
                    const newTextElement: EmailElement = {
                      id: `text-${Date.now()}`,
                      type: 'text',
                      content: 'Nhấp đôi để chỉnh sửa văn bản này',
                      parent: leftColumn.id,
                      draggable: true,
                      removable: true,
                      editable: true,
                      selectable: true,
                      hoverable: true,
                      copyable: true,
                      style: {
                        fontSize: 16,
                        lineHeight: 1.5,
                        color: '#333333',
                        margin: 0,
                      }
                    };

                    // Thêm vào children của leftColumn
                    const updatedLeftColumn = { ...leftColumn };
                    updatedLeftColumn.children = [...(updatedLeftColumn.children || []), newTextElement];
                    updatedLeftColumn.selectedChildId = newTextElement.id; // Chọn phần tử mới thêm

                    // Cập nhật element trong emailElements
                    const elementIndex = emailElements.findIndex(el => el.id === element.id);
                    if (elementIndex !== -1 && element.children) {
                      const leftColumnIndex = element.children.findIndex(c => c.id === leftColumn.id);
                      if (leftColumnIndex !== -1) {
                        const updatedElement = { ...element };
                        if (updatedElement.children) {
                          updatedElement.children[leftColumnIndex] = updatedLeftColumn;
                        }

                        const newElements = [...emailElements];
                        newElements[elementIndex] = updatedElement;
                        setEmailElements(newElements);

                        // Chọn phần tử mới thêm
                        setTimeout(() => {
                          handleSelectElement(newTextElement, -1);
                        }, 100);
                      }
                    }
                  }
                }}
              >
                <LayoutPanelLeft size={14} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                title="Thêm phần tử vào cột phải"
                onClick={(e) => {
                  e.stopPropagation();
                  // Tìm column phải
                  const rightColumn = element.children?.find(child => child.columnPosition === 'right');
                  if (rightColumn) {
                    // Tạo phần tử text mới
                    const newTextElement: EmailElement = {
                      id: `text-${Date.now()}`,
                      type: 'text',
                      content: 'Nhấp đôi để chỉnh sửa văn bản này',
                      parent: rightColumn.id,
                      draggable: true,
                      removable: true,
                      editable: true,
                      selectable: true,
                      hoverable: true,
                      copyable: true,
                      style: {
                        fontSize: 16,
                        lineHeight: 1.5,
                        color: '#333333',
                        margin: 0,
                      }
                    };

                    // Thêm vào children của rightColumn
                    const updatedRightColumn = { ...rightColumn };
                    updatedRightColumn.children = [...(updatedRightColumn.children || []), newTextElement];
                    updatedRightColumn.selectedChildId = newTextElement.id; // Chọn phần tử mới thêm

                    // Cập nhật element trong emailElements
                    const elementIndex = emailElements.findIndex(el => el.id === element.id);
                    if (elementIndex !== -1 && element.children) {
                      const rightColumnIndex = element.children.findIndex(c => c.id === rightColumn.id);
                      if (rightColumnIndex !== -1) {
                        const updatedElement = { ...element };
                        if (updatedElement.children) {
                          updatedElement.children[rightColumnIndex] = updatedRightColumn;
                        }

                        const newElements = [...emailElements];
                        newElements[elementIndex] = updatedElement;
                        setEmailElements(newElements);

                        // Chọn phần tử mới thêm
                        setTimeout(() => {
                          handleSelectElement(newTextElement, -1);
                        }, 100);
                      }
                    }
                  }
                }}
              >
                <LayoutPanelRight size={14} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                title="Xóa column"
                onClick={(e) => {
                  e.stopPropagation();
                  // Xác nhận xóa column
                  const newElements = emailElements.filter(el => el.id !== element.id);
                  setEmailElements(newElements);

                  // Nếu đang chọn column này, bỏ chọn
                  if (selectedElement && selectedElement.id === element.id) {
                    setSelectedElement(null);
                    setSelectedIndex(null);
                  }
                }}
              >
                <Trash2 size={14} />
              </Button>
            </div>
            <div className="columns-content flex gap-4">
              <div
                className={`column-left flex-1 p-4 rounded ${dragOverColumnPosition === 'left' && dragOverColumn === element.id ? 'bg-primary/10 border-2 border-primary border-dashed' : ''}`}
                style={{
                  backgroundColor: dragOverColumnPosition === 'left' && dragOverColumn === element.id ? 'rgba(0, 112, 243, 0.1)' : (element.columnBgColor || '#f5f5f5'),
                  padding: `${element.columnPadding || 20}px`,
                  transition: 'background-color 0.2s',
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  if (leftColumn) {
                    handleSelectElement(leftColumn, emailElements.findIndex(el => el.id === leftColumn.id));
                  }
                }}
                onDragOver={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setDragOverColumn(element.id);
                  setDragOverColumnPosition('left');
                  e.dataTransfer.dropEffect = 'copy';
                }}
                onDragLeave={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setDragOverColumn(null);
                  setDragOverColumnPosition(null);
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const elementType = e.dataTransfer.getData('text/plain');
                  if (elementType && leftColumn) {
                    // Xóa nội dung mặc định nếu có
                    if (leftColumn.children && leftColumn.children.length > 0) {
                      // Nếu chỉ có phần tử mặc định (thường là text), xóa nó đi
                      if (leftColumn.children.length === 1 && leftColumn.children[0]?.type === 'text' &&
                        leftColumn.children[0].content && leftColumn.children[0].content.includes('Nhấp đôi để chỉnh sửa')) {
                        // Xóa phần tử mặc định
                        const updatedLeftColumn = { ...leftColumn };
                        updatedLeftColumn.children = [];

                        // Cập nhật leftColumn trong element
                        const elementIndex = emailElements.findIndex(el => el.id === element.id);
                        if (elementIndex !== -1 && element.children) {
                          const leftColumnIndex = element.children.findIndex(c => c.id === leftColumn.id);
                          if (leftColumnIndex !== -1) {
                            const updatedElement = { ...element };
                            if (updatedElement.children) {
                              updatedElement.children[leftColumnIndex] = updatedLeftColumn;
                            }

                            const newElements = [...emailElements];
                            newElements[elementIndex] = updatedElement;
                            setEmailElements(newElements);

                            // Không thể cập nhật leftColumn trực tiếp vì nó là const
                            // Sử dụng updatedLeftColumn trong các bước tiếp theo
                          }
                        }
                      }
                    }

                    // Tạo phần tử mới dựa trên loại
                    const newElementId = `element-${Date.now()}`;
                    let newElement: EmailElement;

                    switch (elementType) {
                      case 'text':
                        newElement = {
                          id: newElementId,
                          type: 'text',
                          content: 'Nhấp đôi để chỉnh sửa văn bản này',
                          parent: leftColumn.id,
                          draggable: true,
                          removable: true,
                          editable: true,
                          selectable: true,
                          hoverable: true,
                          copyable: true,
                          style: {
                            fontSize: 16,
                            lineHeight: 1.5,
                            color: '#333333',
                            margin: 0,
                          }
                        };
                        break;

                      case 'image':
                        newElement = {
                          id: newElementId,
                          type: 'image',
                          src: 'https://via.placeholder.com/600x200?text=Hình+ảnh+mẫu',
                          alt: 'Hình ảnh mẫu',
                          parent: leftColumn.id,
                          draggable: true,
                          removable: true,
                          editable: true,
                          selectable: true,
                          hoverable: true,
                          copyable: true,
                          style: {
                            width: '100%',
                            display: 'block',
                            margin: '10px 0',
                          }
                        };
                        break;

                      case 'heading':
                        newElement = {
                          id: newElementId,
                          type: 'heading',
                          content: 'Tiêu đề mẫu',
                          headingType: 'h2',
                          parent: leftColumn.id,
                          draggable: true,
                          removable: true,
                          editable: true,
                          selectable: true,
                          hoverable: true,
                          copyable: true,
                          style: {
                            fontSize: 24,
                            fontWeight: 'bold',
                            color: '#111111',
                            margin: '10px 0',
                            lineHeight: 1.2,
                          }
                        };
                        break;

                      default:
                        newElement = {
                          id: newElementId,
                          type: elementType,
                          content: 'Nội dung mẫu',
                          parent: leftColumn.id,
                          draggable: true,
                          removable: true,
                          editable: true,
                          selectable: true,
                          hoverable: true,
                          copyable: true,
                          style: {
                            margin: '10px 0',
                          }
                        };
                    }

                    // Thêm vào children của leftColumn
                    const updatedLeftColumn = { ...leftColumn };
                    updatedLeftColumn.children = [...(updatedLeftColumn.children || []), newElement];
                    updatedLeftColumn.selectedChildId = newElement.id; // Chọn phần tử mới thêm

                    // Cập nhật element trong emailElements
                    const elementIndex = emailElements.findIndex(el => el.id === element.id);
                    if (elementIndex !== -1 && element.children) {
                      const leftColumnIndex = element.children.findIndex(c => c.id === leftColumn.id);
                      if (leftColumnIndex !== -1) {
                        const updatedElement = { ...element };
                        if (updatedElement.children) {
                          updatedElement.children[leftColumnIndex] = updatedLeftColumn;
                        }

                        const newElements = [...emailElements];
                        newElements[elementIndex] = updatedElement;
                        setEmailElements(newElements);

                        // Chọn phần tử mới thêm
                        setTimeout(() => {
                          handleSelectElement(newElement, -1);
                        }, 100);
                      }
                    }

                    setDragOverColumn(null);
                    setDragOverColumnPosition(null);
                  }
                }}
              >
                {leftColumn && leftColumn.children && leftColumn.children.length > 0 ? (
                  leftColumn.children.map((child, idx) => (
                    <div
                      key={`left-column-child-${child.id}-${idx}`}
                      className={`mb-2 last:mb-0 relative ${leftColumn && leftColumn.selectedChildId === child.id ? 'ring-2 ring-primary' : 'hover:ring-1 hover:ring-primary/50'}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectElement(child, -1);
                      }}
                    >
                      {renderDesignElement(child)}
                      {leftColumn && leftColumn.selectedChildId === child.id && (
                        <div className="absolute right-2 top-2 flex gap-1 bg-white/80 rounded p-1">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Xóa phần tử này khỏi column
                              if (leftColumn) {
                                const updatedLeftColumn = { ...leftColumn };
                                updatedLeftColumn.children = leftColumn.children?.filter(c => c.id !== child.id) || [];

                                // Cập nhật element trong emailElements
                                const elementIndex = emailElements.findIndex(el => el.id === element.id);
                                if (elementIndex !== -1) {
                                  // Tạo bản sao của element và cập nhật leftColumn
                                  const updatedElement = { ...element };
                                  const leftColumnIndex = updatedElement.children?.findIndex(c => c.id === leftColumn.id) || -1;
                                  if (leftColumnIndex !== -1 && updatedElement.children) {
                                    updatedElement.children[leftColumnIndex] = updatedLeftColumn;

                                    const newElements = [...emailElements];
                                    newElements[elementIndex] = updatedElement;
                                    setEmailElements(newElements);
                                    setSelectedElement(updatedElement);
                                    setSelectedIndex(elementIndex);
                                  }
                                }
                              }
                            }}
                          >
                            <Trash2 size={12} />
                          </Button>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center text-muted-foreground text-sm p-4 border border-dashed rounded">
                    Kéo thả hoặc nhấp vào nút + để thêm nội dung vào cột trái.
                  </div>
                )}
              </div>
              <div
                className={`column-right flex-1 p-4 rounded ${dragOverColumnPosition === 'right' && dragOverColumn === element.id ? 'bg-primary/10 border-2 border-primary border-dashed' : ''}`}
                style={{
                  backgroundColor: dragOverColumnPosition === 'right' && dragOverColumn === element.id ? 'rgba(0, 112, 243, 0.1)' : (element.columnBgColor || '#f5f5f5'),
                  padding: `${element.columnPadding || 20}px`,
                  transition: 'background-color 0.2s',
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  if (rightColumn) {
                    handleSelectElement(rightColumn, emailElements.findIndex(el => el.id === rightColumn.id));
                  }
                }}
                onDragOver={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setDragOverColumn(element.id);
                  setDragOverColumnPosition('right');
                  e.dataTransfer.dropEffect = 'copy';
                }}
                onDragLeave={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setDragOverColumn(null);
                  setDragOverColumnPosition(null);
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const elementType = e.dataTransfer.getData('text/plain');
                  if (elementType && rightColumn) {
                    // Tìm index của rightColumn trong emailElements
                    const rightColumnIndex = emailElements.findIndex(el => el.id === rightColumn.id);
                    if (rightColumnIndex !== -1) {
                      addElementToColumn(rightColumn.id, elementType);
                    } else {
                      // Nếu không tìm thấy trực tiếp, thêm vào children của 2columns
                      const columnIndex = emailElements.findIndex(el => el.id === element.id);
                      if (columnIndex !== -1 && rightColumn.children) {
                        // Tạo phần tử mới dựa trên loại
                        let newElement: EmailElement;

                        switch (elementType) {
                          case 'text':
                            newElement = {
                              id: `text-${Date.now()}`,
                              type: 'text',
                              content: 'Nhấp đôi để chỉnh sửa văn bản này',
                              parent: rightColumn.id,
                              draggable: true,
                              removable: true,
                              editable: true,
                              selectable: true,
                              hoverable: true,
                              copyable: true,
                              style: {
                                fontSize: 16,
                                lineHeight: 1.5,
                                color: '#333333',
                                margin: 0,
                              }
                            };
                            break;

                          case 'image':
                            newElement = {
                              id: `image-${Date.now()}`,
                              type: 'image',
                              src: 'https://via.placeholder.com/600x200?text=Hình+ảnh+mẫu',
                              alt: 'Hình ảnh mẫu',
                              parent: rightColumn.id,
                              draggable: true,
                              removable: true,
                              editable: true,
                              selectable: true,
                              hoverable: true,
                              copyable: true,
                              style: {
                                width: '100%',
                                display: 'block',
                                margin: '10px 0',
                              }
                            };
                            break;

                          case 'heading':
                            newElement = {
                              id: `heading-${Date.now()}`,
                              type: 'heading',
                              content: 'Tiêu đề mẫu',
                              headingType: 'h2',
                              parent: rightColumn.id,
                              draggable: true,
                              removable: true,
                              editable: true,
                              selectable: true,
                              hoverable: true,
                              copyable: true,
                              style: {
                                fontSize: 24,
                                fontWeight: 'bold',
                                color: '#111111',
                                margin: '10px 0',
                                lineHeight: 1.2,
                              }
                            };
                            break;

                          default:
                            newElement = {
                              id: `element-${Date.now()}`,
                              type: elementType,
                              content: 'Nội dung mẫu',
                              parent: rightColumn.id,
                              draggable: true,
                              removable: true,
                              editable: true,
                              selectable: true,
                              hoverable: true,
                              copyable: true,
                              style: {
                                margin: '10px 0',
                              }
                            };
                        }

                        // Thêm vào children của rightColumn
                        rightColumn.children.push(newElement);

                        // Cập nhật emailElements
                        const newElements = [...emailElements];
                        newElements[columnIndex] = { ...element };
                        setEmailElements(newElements);
                      }
                    }

                    setDragOverColumn(null);
                    setDragOverColumnPosition(null);
                  }
                }}
              >
                {rightColumn && rightColumn.children && rightColumn.children.length > 0 ? (
                  rightColumn.children.map((child, idx) => (
                    <div
                      key={`right-column-child-${child.id}-${idx}`}
                      className={`mb-2 last:mb-0 relative ${rightColumn && rightColumn.selectedChildId === child.id ? 'ring-2 ring-primary' : 'hover:ring-1 hover:ring-primary/50'}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectElement(child, -1); // -1 để đánh dấu đây là phần tử con trong column
                      }}
                    >
                      {renderDesignElement(child)}
                      {rightColumn && rightColumn.selectedChildId === child.id && (
                        <div className="absolute right-2 top-2 flex gap-1 bg-white/80 rounded p-1">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Xóa phần tử này khỏi column
                              if (rightColumn) {
                                const updatedRightColumn = { ...rightColumn };
                                updatedRightColumn.children = rightColumn.children?.filter(c => c.id !== child.id) || [];

                                // Cập nhật element trong emailElements
                                const elementIndex = emailElements.findIndex(el => el.id === element.id);
                                if (elementIndex !== -1) {
                                  // Tạo bản sao của element và cập nhật rightColumn
                                  const updatedElement = { ...element };
                                  const rightColumnIndex = updatedElement.children?.findIndex(c => c.id === rightColumn.id) || -1;
                                  if (rightColumnIndex !== -1 && updatedElement.children) {
                                    updatedElement.children[rightColumnIndex] = updatedRightColumn;

                                    const newElements = [...emailElements];
                                    newElements[elementIndex] = updatedElement;
                                    setEmailElements(newElements);
                                    setSelectedElement(updatedElement);
                                    setSelectedIndex(elementIndex);
                                  }
                                }
                              }
                            }}
                          >
                            <Trash2 size={12} />
                          </Button>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center text-muted-foreground text-sm p-4 border border-dashed rounded">
                    Kéo thả hoặc nhấp vào nút + để thêm nội dung vào cột phải.
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'column':
        return (
          <div>
            {element.children && element.children.length > 0 ? (
              element.children.map((child, idx) => (
                <div
                  key={`column-child-${child.id}-${idx}`}
                  className="mb-2 last:mb-0"
                >
                  {renderDesignElement(child)}
                </div>
              ))
            ) : (
              <div className="text-center text-muted-foreground text-sm p-2">
                Cột trống
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="p-4 text-center text-muted-foreground border border-dashed rounded">
            Phần tử không hỗ trợ
          </div>
        );
    }
  };

  return (
    <div className={`flex ${compactMode ? 'h-full' : 'h-screen'} flex-col bg-muted/20`}>
      {/* Header - Hiển thị khi không ở chế độ compact */}
      {!compactMode && (
        <div className="border-b bg-card px-4 py-2 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h1 className="text-xl font-semibold">Email Template Builder</h1>
            <div className="flex items-center ml-4">
              <Input
                className="w-64 h-8"
                placeholder="Tên template"
                value={emailData.name}
                onChange={(e) => setEmailData({ ...emailData, name: e.target.value })}
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* History controls */}
            <div className="flex items-center border rounded-md overflow-hidden mr-2">
              <Button
                size="sm"
                variant="ghost"
                className="px-2 h-8"
                disabled={historyIndex <= 0}
                onClick={() => {
                  if (historyIndex > 0) {
                    const newIndex = historyIndex - 1;
                    const historyItem = history[newIndex];
                    setEmailElements(historyItem?.emailElements || []);
                    setSelectedIndex(historyItem?.selectedIndex || null);
                    setSelectedElement(historyItem && historyItem.selectedIndex !== null ? historyItem.emailElements[historyItem.selectedIndex] || null : null);
                    setHistoryIndex(newIndex);
                  }
                }}
                title="Hoàn tác"
              >
                <Undo2 size={16} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="px-2 h-8"
                disabled={historyIndex >= history.length - 1}
                onClick={() => {
                    if (historyIndex < history.length - 1) {
                        const newIndex = historyIndex + 1;
                        const historyItem = history[newIndex];
                      
                        setEmailElements(historyItem?.emailElements || []);
                        setSelectedIndex(historyItem?.selectedIndex ?? null);
                      
                        let selectedElement: EmailElement | null = null;
                        if (
                          historyItem &&
                          historyItem.selectedIndex !== null &&
                          historyItem.emailElements &&
                          historyItem.selectedIndex < historyItem.emailElements.length
                        ) {
                          selectedElement = historyItem.emailElements[historyItem.selectedIndex] ?? null;
                        }
                      
                        setSelectedElement(selectedElement);
                        setHistoryIndex(newIndex);
                      }
                      
                }}
                title="Làm lại"
              >
                <Redo2 size={16} />
              </Button>
            </div>

            {/* Panel visibility controls */}
            <div className="flex items-center border rounded-md overflow-hidden mr-2">
              <Button
                size="sm"
                variant="ghost"
                className={`px-2 h-8 ${!leftPanelVisible ? 'text-muted-foreground' : ''}`}
                onClick={() => setLeftPanelVisible(!leftPanelVisible)}
                title={leftPanelVisible ? "Ẩn panel trái" : "Hiện panel trái"}
              >
                <LayoutPanelLeft size={16} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className={`px-2 h-8 ${!rightPanelVisible ? 'text-muted-foreground' : ''}`}
                onClick={() => setRightPanelVisible(!rightPanelVisible)}
                title={rightPanelVisible ? "Ẩn panel phải" : "Hiện panel phải"}
              >
                <LayoutPanelRight size={16} />
              </Button>
            </div>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowPreview(!showPreview)}
              className="h-8"
            >
              {showPreview ? <Settings size={16} className="mr-1" /> : <Eye size={16} className="mr-1" />}
              {showPreview ? 'Chỉnh sửa' : 'Xem trước'}
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={toggleHtmlCode}
              className="h-8"
            >
              <Code size={16} className="mr-1" />
              Xem code
            </Button>

            <Button
              size="sm"
              variant="ghost"
              className="h-8"
              onClick={() => {
                setIsFullscreen(!isFullscreen);

                // Xử lý fullscreen mode
                if (!isFullscreen) {
                  const element = document.documentElement;
                  if (element.requestFullscreen) {
                    element.requestFullscreen();
                  }
                } else {
                  if (document.exitFullscreen) {
                    document.exitFullscreen();
                  }
                }
              }}
            >
              {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
            </Button>

            <Separator orientation="vertical" className="h-8 mx-1" />

            <Button size="sm" onClick={() => {
              // Hiển thị dialog lưu template
              const templateName = prompt("Nhập tên template để lưu:", emailData.name || "Template mới");
              if (templateName) {
                // Cập nhật tên template
                setEmailData({ ...emailData, name: templateName });

                // Lưu template (trong thực tế, cần lưu vào database)
                toast({
                  title: "Đã lưu template",
                  description: `Template "${templateName}" đã được lưu thành công.`,
                  duration: 3000,
                });
              }
            }}>
              <Save size={16} className="mr-1" />
              Lưu template
            </Button>

            <Popover>
              <PopoverTrigger asChild>
                <Button size="sm" variant="outline">
                  <FileCode size={16} className="mr-1" />
                  Xuất
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-56 p-2">
                <div className="space-y-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => {
                      // Tạo HTML từ emailElements
                      const htmlContent = generateHTML();

                      // Sử dụng Clipboard API để sao chép
                      navigator.clipboard.writeText(htmlContent).then(() => {
                        toast({
                          title: "Đã sao chép HTML",
                          description: "Mã HTML đã được sao chép vào clipboard.",
                          duration: 3000,
                        });
                      });
                    }}
                  >
                    <Clipboard size={14} className="mr-2" />
                    Sao chép HTML
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => {
                      // Tạo HTML từ emailElements
                      const htmlContent = generateHTML();

                      // Tạo một blob và tạo URL để download
                      const blob = new Blob([htmlContent], { type: 'text/html' });
                      const url = URL.createObjectURL(blob);

                      // Tạo một element a tạm thời để download
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `${emailData.name || 'email-template'}.html`;
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);

                      toast({
                        title: "Đã tải xuống HTML",
                        description: "File HTML đã được tải xuống.",
                        duration: 3000,
                      });
                    }}
                  >
                    <UploadCloud size={14} className="mr-2" />
                    Tải xuống HTML
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => {
                      // Tạo dữ liệu dự án GrapesJS
                      const projectData = {
                        id: `project-${Date.now()}`,
                        name: emailData.name || 'Email Template',
                        createdAt: new Date().toISOString(),
                        html: generateHTML(),
                        css: extractCssFromHtml(generateHTML()),
                        components: JSON.stringify(emailElements),
                        assets: JSON.stringify(assets),
                        metadata: {
                          subject: emailData.subject || '',
                          preheader: emailData.preheader || '',
                          version: '1.0.0',
                          type: 'email',
                          framework: 'grapesjs'
                        }
                      };

                      // Chuyển đổi thành chuỗi JSON
                      const projectJson = JSON.stringify(projectData, null, 2);

                      // Tạo một blob và tạo URL để download
                      const blob = new Blob([projectJson], { type: 'application/json' });
                      const url = URL.createObjectURL(blob);

                      // Tạo một element a tạm thời để download
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `${emailData.name || 'email-template'}.grapesjs`;
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);

                      toast({
                        title: "Đã lưu dự án",
                        description: "Dự án đã được lưu dưới dạng file .grapesjs",
                        duration: 3000,
                      });
                    }}
                  >
                    <Save size={14} className="mr-2" />
                    Lưu dự án
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      )}

      {/* Header cho chế độ compact */}
      {compactMode && (
        <div className="border-b bg-card px-4 py-2 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h2 className="font-medium">Trình soạn thảo nội dung</h2>
          </div>

          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowPreview(!showPreview)}
            >
              {showPreview ? <Settings size={16} className="mr-1" /> : <Eye size={16} className="mr-1" />}
              {showPreview ? 'Chỉnh sửa' : 'Xem trước'}
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={toggleHtmlCode}
            >
              <Code size={16} className="mr-1" />
              Xem code
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                // Tạo dữ liệu dự án GrapesJS
                const projectData = {
                  id: `project-${Date.now()}`,
                  name: emailData.name || 'Email Template',
                  createdAt: new Date().toISOString(),
                  html: generateHTML(),
                  css: extractCssFromHtml(generateHTML()),
                  components: JSON.stringify(emailElements),
                  assets: JSON.stringify(assets),
                  metadata: {
                    subject: emailData.subject || '',
                    preheader: emailData.preheader || '',
                    version: '1.0.0',
                    type: 'email',
                    framework: 'grapesjs'
                  }
                };

                // Chuyển đổi thành chuỗi JSON
                const projectJson = JSON.stringify(projectData, null, 2);

                // Tạo một blob và tạo URL để download
                const blob = new Blob([projectJson], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                // Tạo một element a tạm thời để download
                const a = document.createElement('a');
                a.href = url;
                a.download = `${emailData.name || 'email-template'}.grapesjs`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                toast({
                  title: "Đã lưu dự án",
                  description: "Dự án đã được lưu dưới dạng file .grapesjs",
                  duration: 3000,
                });
              }}
            >
              <Save size={16} className="mr-1" />
              Lưu dự án
            </Button>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left sidebar - Block library */}
        {!showPreview && leftPanelVisible && (
          <div className={`${compactMode ? 'w-64' : 'w-72'} border-r bg-card overflow-auto`}>
            <Tabs defaultValue="blocks" value={currentTab} onValueChange={setCurrentTab}>
              <TabsList className="w-full">
                <TabsTrigger value="blocks" className="flex-1">Thư viện</TabsTrigger>
                <TabsTrigger value="assets" className="flex-1">Assets</TabsTrigger>
                <TabsTrigger value="layers" className="flex-1">Layers</TabsTrigger>
                <TabsTrigger value="templates" className="flex-1">Template</TabsTrigger>
              </TabsList>

              <TabsContent value="blocks" className="p-0 m-0">
                <div className="p-4">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium mb-2">Cơ bản</h3>
                      <div className="grid grid-cols-2 gap-2">
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('text')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'text')}
                        >
                          <Type size={20} />
                          <span className="text-xs">Văn bản</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('heading')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'heading')}
                        >
                          <Heading1 size={20} />
                          <span className="text-xs">Tiêu đề</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('image')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'image')}
                        >
                          <ImageIcon size={20} />
                          <span className="text-xs">Hình ảnh</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('button')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'button')}
                        >
                          <ButtonIcon size={20} />
                          <span className="text-xs">Nút bấm</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('list')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'list')}
                        >
                          <List size={20} />
                          <span className="text-xs">Danh sách</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('divider')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'divider')}
                        >
                          <Minus size={20} />
                          <span className="text-xs">Đường kẻ</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('link')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'link')}
                        >
                          <Link size={20} />
                          <span className="text-xs">Liên kết</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('spacer')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'spacer')}
                        >
                          <ArrowUpDown size={20} />
                          <span className="text-xs">Khoảng cách</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-2">Bố cục</h3>
                      <div className="grid grid-cols-2 gap-2">
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('1column')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, '1column')}
                        >
                          <Columns size={20} />
                          <span className="text-xs">1 Cột</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('2columns')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, '2columns')}
                        >
                          <Columns size={20} />
                          <span className="text-xs">2 Cột</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('header')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'header')}
                        >
                          <PanelTop size={20} />
                          <span className="text-xs">Header</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('footer')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'footer')}
                        >
                          <Layout size={20} />
                          <span className="text-xs">Footer</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-2">Nâng cao</h3>
                      <div className="grid grid-cols-2 gap-2">
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('social')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'social')}
                        >
                          <Share2 size={20} />
                          <span className="text-xs">Mạng xã hội</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('video')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'video')}
                        >
                          <Video size={20} />
                          <span className="text-xs">Video</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('html')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'html')}
                        >
                          <Code size={20} />
                          <span className="text-xs">HTML</span>
                        </div>
                        <div
                          className="p-2 border rounded-md bg-card hover:bg-accent hover:text-accent-foreground flex flex-col items-center text-center gap-1 cursor-pointer"
                          onClick={() => addNewElement('countdown')}
                          draggable
                          onDragStart={(e) => handleDragStart(e, 'countdown')}
                        >
                          <Timer size={20} />
                          <span className="text-xs">Đếm ngược</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="assets" className="p-0 m-0">
                <div className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">Thư viện hình ảnh</h3>
                      <Dialog open={showAssetUploadDialog} onOpenChange={setShowAssetUploadDialog}>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" className="h-8">
                            <UploadCloud size={14} className="mr-1" />
                            <span className="text-xs">Tải lên</span>
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Tải hình ảnh lên</DialogTitle>
                            <DialogDescription>
                              Nhập URL hình ảnh hoặc tải lên từ máy tính của bạn.
                            </DialogDescription>
                          </DialogHeader>

                          <div className="space-y-4 py-4">
                            <div className="space-y-2">
                              <Label htmlFor="image-url">URL hình ảnh</Label>
                              <Input
                                id="image-url"
                                placeholder="https://example.com/image.jpg"
                                value={uploadImageUrl}
                                onChange={(e) => setUploadImageUrl(e.target.value)}
                              />
                            </div>

                            <div className="text-center text-sm text-muted-foreground">
                              HOẶC
                            </div>

                            <div className="space-y-2">
                              <Label>Tên hình ảnh</Label>
                              <Input
                                placeholder="Tên hình ảnh"
                                value={uploadImageName}
                                onChange={(e) => setUploadImageName(e.target.value)}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label>Tải ảnh từ máy tính</Label>
                              <div
                                className="border-2 border-dashed rounded-md p-6 text-center cursor-pointer hover:bg-muted/50"
                                onClick={() => fileInputRef.current?.click()}
                              >
                                <UploadCloud className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                                <p className="text-sm text-muted-foreground">
                                  Kéo và thả hoặc nhấp để tải ảnh
                                </p>
                                <input
                                  ref={fileInputRef}
                                  type="file"
                                  accept="image/*"
                                  className="hidden"
                                  onChange={handleFileUpload}
                                />
                              </div>
                            </div>
                          </div>

                          <DialogFooter>
                            <Button variant="outline" onClick={() => setShowAssetUploadDialog(false)}>
                              Hủy
                            </Button>
                            <Button
                              onClick={handleAssetUpload}
                              disabled={!uploadImageUrl}
                            >
                              Thêm ảnh
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      {assets.map((asset) => (
                        <div
                          key={asset.id}
                          className={`relative border rounded-md overflow-hidden group cursor-pointer ${asset.selected ? 'ring-2 ring-primary' : ''}`}
                          onClick={() => handleAssetSelect(asset)}
                        >
                          <img
                            src={asset.src}
                            alt={asset.alt || asset.name || 'Image'}
                            className="w-full h-24 object-cover"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                            {asset.selected && (
                              <div className="absolute top-1 right-1 bg-primary text-white rounded-full p-1">
                                <Check size={12} />
                              </div>
                            )}
                            <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-1 text-xs truncate">
                              {asset.name || 'Image'}
                            </div>
                            <div className="hidden group-hover:flex gap-1 absolute top-1 right-1">
                              <Button
                                variant="destructive"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleAssetDelete(asset.id);
                                }}
                              >
                                <Trash2 size={12} />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {assets.length === 0 && (
                      <div className="text-center p-4 border border-dashed rounded-md">
                        <ImageIcon className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">
                          Chưa có hình ảnh nào trong thư viện
                        </p>
                      </div>
                    )}

                    {selectedAsset && (
                      <div className="mt-4">
                        <Button
                          className="w-full"
                          onClick={handleUseSelectedAsset}
                        >
                          {selectedElement && selectedElement.type === 'image'
                            ? 'Áp dụng cho hình ảnh đã chọn'
                            : 'Thêm hình ảnh vào email'}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="layers" className="p-0 m-0">
                <div className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">Cấu trúc trang</h3>
                      <Button variant="ghost" size="sm" className="h-7 px-2">
                        <Layers size={14} className="mr-1" />
                        <span className="text-xs">Mở rộng tất cả</span>
                      </Button>
                    </div>

                    <div className="border rounded-md">
                      {emailElements.length === 0 ? (
                        <div className="p-4 text-center text-muted-foreground text-sm">
                          Chưa có phần tử nào trong trang
                        </div>
                      ) : (
                        <div className="divide-y">
                          {emailElements.map((element, index) => (
                            <div
                              key={`layer-${element.id}`}
                              className={`p-2 hover:bg-accent/10 cursor-pointer ${selectedIndex === index ? 'bg-accent/20' : ''}`}
                              onClick={() => handleSelectElement(element, index)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <div className="flex items-center">
                                    {element.type === '1column' || element.type === '2columns' ? (
                                      <ChevronDown size={14} className="text-muted-foreground" />
                                    ) : (
                                      <div className="w-3.5"></div>
                                    )}

                                    <span className="ml-1 text-xs font-medium">
                                      {element.type === 'text' ? 'Văn bản' :
                                        element.type === 'heading' ? 'Tiêu đề' :
                                          element.type === 'image' ? 'Hình ảnh' :
                                            element.type === 'button' ? 'Nút bấm' :
                                              element.type === 'list' ? 'Danh sách' :
                                                element.type === 'divider' ? 'Đường kẻ' :
                                                  element.type === 'link' ? 'Liên kết' :
                                                    element.type === 'spacer' ? 'Khoảng cách' :
                                                      element.type === '1column' ? 'Cột đơn' :
                                                        element.type === '2columns' ? 'Hai cột' :
                                                          element.type === 'header' ? 'Header' :
                                                            element.type === 'footer' ? 'Footer' :
                                                              element.type}
                                    </span>
                                  </div>
                                </div>

                                <div className="flex items-center gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // Toggle visibility
                                      const updatedElement = { ...element };
                                      updatedElement.style = { ...updatedElement.style };
                                      updatedElement.style.display = updatedElement.style?.display === 'none' ? 'block' : 'none';

                                      const newElements = [...emailElements];
                                      newElements[index] = updatedElement;
                                      setEmailElements(newElements);
                                      setSelectedElement(updatedElement);
                                    }}
                                  >
                                    {element.style?.display === 'none' ? (
                                      <EyeOff size={14} />
                                    ) : (
                                      <Eye size={14} />
                                    )}
                                  </Button>

                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedIndex(index);
                                      setSelectedElement(element);
                                      deleteSelectedElement();
                                    }}
                                  >
                                    <Trash2 size={14} />
                                  </Button>
                                </div>
                              </div>

                              {/* Hiển thị các phần tử con nếu là column */}
                              {(element.type === '1column' || element.type === '2columns') && element.children && element.children.length > 0 && (
                                <div className="pl-6 mt-1 space-y-1">
                                  {element.children.map((child, childIndex) => (
                                    <div
                                      key={`child-${child.id}`}
                                      className={`p-1 hover:bg-accent/10 cursor-pointer rounded-sm ${selectedElement?.id === child.id ? 'bg-accent/20' : ''}`}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleSelectElement(child, -1);
                                      }}
                                    >
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-1">
                                          <span className="text-xs">
                                            {child.type === 'text' ? 'Văn bản' :
                                              child.type === 'heading' ? 'Tiêu đề' :
                                                child.type === 'image' ? 'Hình ảnh' :
                                                  child.type === 'button' ? 'Nút bấm' :
                                                    child.type === 'list' ? 'Danh sách' :
                                                      child.type === 'divider' ? 'Đường kẻ' :
                                                        child.type === 'link' ? 'Liên kết' :
                                                          child.type}
                                          </span>
                                        </div>

                                        <div className="flex items-center gap-1">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-5 w-5 p-0"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              // Toggle visibility
                                              const updatedChild = { ...child };
                                              updatedChild.style = { ...updatedChild.style };
                                              updatedChild.style.display = updatedChild.style?.display === 'none' ? 'block' : 'none';

                                              // Cập nhật phần tử con trong column
                                              const updatedElement = { ...element };
                                              updatedElement.children = [...(element.children ?? [])];
                                              updatedElement.children[childIndex] = updatedChild;

                                              const newElements = [...emailElements];
                                              newElements[index] = updatedElement;
                                              setEmailElements(newElements);

                                              if (selectedElement?.id === child.id) {
                                                setSelectedElement(updatedChild);
                                              }
                                            }}
                                          >
                                            {child.style?.display === 'none' ? (
                                              <EyeOff size={12} />
                                            ) : (
                                              <Eye size={12} />
                                            )}
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="templates" className="p-0 m-0">
                <div className="p-4">
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">Template có sẵn</h3>
                    {EMAIL_TEMPLATES.map((template) => (
                      <Card
                        key={template.id}
                        className="overflow-hidden cursor-pointer hover:shadow-md hover:border-primary"
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between mb-1">
                            <div className="font-medium text-sm">{template.name}</div>
                            <span className="text-xs px-2 py-0.5 bg-muted rounded-full">{template.category}</span>
                          </div>
                          <div className="text-xs text-muted-foreground">{template.description}</div>
                          <div className="mt-2 flex justify-end">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-7 text-xs"
                              onClick={() => applyEmailTemplate(template)}
                            >
                              Sử dụng
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}


                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Main canvas */}
        <div className="flex-1 flex flex-col">
          {/* Canvas area for editing */}
          <div className="flex-1 overflow-auto bg-muted relative">
            {/* Device frame */}
            <div className="flex justify-center items-start py-8">
              <div
                className={`
                  relative transition-all duration-300 ease-in-out
                  ${viewMode === 'desktop' ? 'w-[600px]' : viewMode === 'tablet' ? 'w-[480px] border-8 border-gray-800 rounded-xl' : 'w-[360px] border-[12px] border-gray-800 rounded-[24px]'}
                `}
              >
                {/* Device top bar (for mobile and tablet) */}
                {viewMode !== 'desktop' && (
                  <div className="absolute top-0 left-0 right-0 h-4 bg-gray-800 -mt-4 flex justify-center items-center">
                    <div className="w-16 h-1 bg-gray-600 rounded-full"></div>
                  </div>
                )}

                {/* Email canvas */}
                <div
                  className={`bg-white shadow-sm min-h-[800px] transition-all overflow-hidden`}
                  ref={canvasRef}
                >
                  {/* Preview mode */}
                  {showPreview ? (
                    <div
                      className="p-4"
                      dangerouslySetInnerHTML={{ __html: generateHTML() }}
                    />
                  ) : (
                    /* Edit mode */
                    <div className="p-4">
                      {emailElements.length === 0 ? (
                        <div className="border-2 border-dashed border-muted-foreground/20 rounded-md p-8 text-center text-muted-foreground">
                          <ImageIcon className="mx-auto h-12 w-12 mb-2 text-muted-foreground/60" />
                          <h3 className="text-lg font-medium mb-1">Chọn các thành phần từ thư viện</h3>
                          <p className="text-sm">Hoặc chọn một template có sẵn</p>
                        </div>
                      ) : (
                        emailElements.map((element, index) => (
                          <div
                            key={`design-${element.id}`}
                            className={`group relative ${selectedIndex === index ? 'ring-2 ring-primary' : 'hover:ring-1 hover:ring-primary/50'}`}
                            onClick={() => handleSelectElement(element, index)}
                          >
                            {renderDesignElement(element)}

                            {/* Element controls */}
                            <div className={`absolute right-2 top-2 flex gap-1 ${selectedIndex === index ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
                              {renderMoveButtons(index)}
                              <Button
                                size="sm"
                                variant="secondary"
                                className="h-7 w-7 p-0 bg-white shadow-sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedIndex(index);
                                  setSelectedElement(element);
                                  deleteSelectedElement();
                                }}
                              >
                                <Trash2 size={14} />
                              </Button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  )}
                </div>

                {/* Device bottom bar (for mobile) */}
                {viewMode === 'mobile' && (
                  <div className="absolute bottom-0 left-0 right-0 h-4 bg-gray-800 -mb-4 flex justify-center items-center">
                    <div className="w-8 h-1 bg-gray-600 rounded-full"></div>
                  </div>
                )}
              </div>
            </div>

            {/* Responsive controls */}
            <div className="absolute bottom-4 right-4 bg-white rounded-full shadow-md p-1 flex items-center">
              <Button
                size="sm"
                variant="ghost"
                className={`rounded-full ${viewMode === 'mobile' ? 'bg-accent text-accent-foreground' : ''}`}
                onClick={() => setViewMode('mobile')}
                title="Mobile"
              >
                <Smartphone size={16} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className={`rounded-full ${viewMode === 'tablet' ? 'bg-accent text-accent-foreground' : ''}`}
                onClick={() => setViewMode('tablet')}
                title="Tablet"
              >
                <Tablet size={16} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className={`rounded-full ${viewMode === 'desktop' ? 'bg-accent text-accent-foreground' : ''}`}
                onClick={() => setViewMode('desktop')}
                title="Desktop"
              >
                <Monitor size={16} />
              </Button>
            </div>
          </div>
        </div>

        {/* Right sidebar - Properties panel */}
        {!showPreview && rightPanelVisible && selectedElement && (
          <div className="w-72 border-l bg-card overflow-auto">
            <div className="p-4">
              <h3 className="text-sm font-medium mb-2 flex items-center justify-between">
                <span>Thuộc tính phần tử</span>
                <Badge variant="outline" className="text-xs font-normal">
                  {selectedElement.type === 'text' ? 'Văn bản' :
                    selectedElement.type === 'heading' ? 'Tiêu đề' :
                      selectedElement.type === 'image' ? 'Hình ảnh' :
                        selectedElement.type === 'button' ? 'Nút bấm' :
                          selectedElement.type === 'list' ? 'Danh sách' :
                            selectedElement.type === 'divider' ? 'Đường kẻ' :
                              selectedElement.type === 'link' ? 'Liên kết' :
                                selectedElement.type === 'spacer' ? 'Khoảng cách' :
                                  selectedElement.type === '1column' ? 'Cột đơn' :
                                    selectedElement.type === '2columns' ? 'Hai cột' :
                                      selectedElement.type}
                </Badge>
              </h3>
              <div className="text-xs text-muted-foreground mb-4 flex items-center justify-between">
                <span>{selectedIndex === -1 ? 'Phần tử trong cột' : 'Phần tử chính'}</span>
                {selectedIndex === -1 && selectedElement.parent && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => {
                      // Tìm column chứa phần tử
                      const parentId = selectedElement?.parent;
                      const parentIndex = emailElements.findIndex(el => el.id === parentId);
                      const parentElement = emailElements[parentIndex];
                        if (parentIndex !== -1 && parentElement) {
                        handleSelectElement(parentElement, parentIndex);
                        }
                    }}
                  >
                    <ArrowUp size={12} className="mr-1" />
                    Chọn cột
                  </Button>
                )}
              </div>

              {selectedElement.type === 'text' && (
                <div className="space-y-4">
                  <Accordion
                    type="multiple"
                    value={openAccordions}
                    onValueChange={setOpenAccordions}
                  >
                    <AccordionItem value="content">
                      <AccordionTrigger className="text-sm font-medium">Nội dung</AccordionTrigger>
                      <AccordionContent className="accordion-content">
                        <div className="space-y-2">
                          <Textarea
                            placeholder="Nhập nội dung văn bản"
                            value={selectedElement.content || ''}
                            onChange={(e) => updateSelectedElement('content', e.target.value)}
                            className="min-h-[100px]"
                          />
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="typography">
                      <AccordionTrigger className="text-sm font-medium">Typography</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Font Family</Label>
                            <Select
                              value={selectedElement.style?.fontFamily || 'Arial, sans-serif'}
                              onValueChange={(value) => updateSelectedElement('style.fontFamily', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn font" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                                <SelectItem value="'Helvetica Neue', Helvetica, Arial, sans-serif">Helvetica</SelectItem>
                                <SelectItem value="'Times New Roman', Times, serif">Times New Roman</SelectItem>
                                <SelectItem value="Georgia, serif">Georgia</SelectItem>
                                <SelectItem value="'Courier New', Courier, monospace">Courier New</SelectItem>
                                <SelectItem value="Tahoma, Geneva, sans-serif">Tahoma</SelectItem>
                                <SelectItem value="Verdana, Geneva, sans-serif">Verdana</SelectItem>
                                <SelectItem value="'Trebuchet MS', Helvetica, sans-serif">Trebuchet MS</SelectItem>
                                <SelectItem value="'Segoe UI', Tahoma, Geneva, sans-serif">Segoe UI</SelectItem>
                                <SelectItem value="Roboto, Arial, sans-serif">Roboto</SelectItem>
                                <SelectItem value="'Open Sans', Arial, sans-serif">Open Sans</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="grid grid-cols-2 gap-2">
                            <div className="space-y-2">
                              <Label>Kích thước</Label>
                              <div className="flex items-center gap-2">
                                <Slider
                                  value={[selectedElement.style?.fontSize as number || 16]}
                                  min={8}
                                  max={72}
                                  step={1}
                                  onValueChange={(value) => updateSelectedElement('style.fontSize', value[0])}
                                />
                                <span className="w-8 text-center">{selectedElement.style?.fontSize || 16}px</span>
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label>Line Height</Label>
                              <div className="flex items-center gap-2">
                                <Slider
                                  value={[parseFloat(String(selectedElement.style?.lineHeight ?? 1.5)) * 10]}
                                  min={10}
                                  max={30}
                                  step={1}
                                  onValueChange={(value) => updateSelectedElement('style.lineHeight', (value[0] ?? 0) / 10)}
                                />
                                <span className="w-8 text-center">{(selectedElement.style?.lineHeight || 1.5)}</span>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Độ đậm</Label>
                            <Select
                              value={String(selectedElement.style?.fontWeight || 'normal')}
                              onValueChange={(value) => updateSelectedElement('style.fontWeight', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn độ đậm" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="normal">Normal</SelectItem>
                                <SelectItem value="bold">Bold</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                                <SelectItem value="200">200</SelectItem>
                                <SelectItem value="300">300</SelectItem>
                                <SelectItem value="400">400</SelectItem>
                                <SelectItem value="500">500</SelectItem>
                                <SelectItem value="600">600</SelectItem>
                                <SelectItem value="700">700</SelectItem>
                                <SelectItem value="800">800</SelectItem>
                                <SelectItem value="900">900</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Màu chữ</Label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={selectedElement.style?.color || '#000000'}
                                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                                className="w-8 h-8 rounded"
                              />
                              <Input
                                value={selectedElement.style?.color || '#000000'}
                                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Canh lề</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant={selectedElement.style?.textAlign === 'left' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'left')}
                              >
                                <AlignLeft size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textAlign === 'center' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'center')}
                              >
                                <AlignCenter size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textAlign === 'right' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'right')}
                              >
                                <AlignRight size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textAlign === 'justify' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'justify')}
                              >
                                <AlignJustify size={16} />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Định dạng</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant={selectedElement.style?.fontStyle === 'italic' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.fontStyle', selectedElement.style?.fontStyle === 'italic' ? 'normal' : 'italic')}
                              >
                                <Italic size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textDecoration === 'underline' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textDecoration', selectedElement.style?.textDecoration === 'underline' ? 'none' : 'underline')}
                              >
                                <Underline size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textTransform === 'uppercase' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textTransform', selectedElement.style?.textTransform === 'uppercase' ? 'none' : 'uppercase')}
                              >
                                <Type size={16} />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Letter Spacing</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseFloat(String(selectedElement.style?.letterSpacing || 0)) * 10]}
                                min={-5}
                                max={20}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.letterSpacing', (value[0] ?? 0) / 10 + 'px')}
                              />
                              <span className="w-12 text-center">{parseFloat(String(selectedElement.style?.letterSpacing || 0))}px</span>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="spacing">
                      <AccordionTrigger className="text-sm font-medium">Spacing</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Padding</Label>
                            <div className="grid grid-cols-2 gap-2">
                              <div>
                                <Label className="text-xs">Top</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.paddingTop as number || 8]}
                                    min={0}
                                    max={100}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.paddingTop', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.paddingTop || 8}px</span>
                                </div>
                              </div>
                              <div>
                                <Label className="text-xs">Bottom</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.paddingBottom as number || 8]}
                                    min={0}
                                    max={100}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.paddingBottom', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.paddingBottom || 8}px</span>
                                </div>
                              </div>
                              <div>
                                <Label className="text-xs">Left</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.paddingLeft as number || 16]}
                                    min={0}
                                    max={100}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.paddingLeft', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.paddingLeft || 16}px</span>
                                </div>
                              </div>
                              <div>
                                <Label className="text-xs">Right</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.paddingRight as number || 16]}
                                    min={0}
                                    max={100}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.paddingRight', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.paddingRight || 16}px</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Margin</Label>
                            <div className="grid grid-cols-2 gap-2">
                              <div>
                                <Label className="text-xs">Top</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.marginTop as number || 0]}
                                    min={0}
                                    max={100}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.marginTop', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.marginTop || 0}px</span>
                                </div>
                              </div>
                              <div>
                                <Label className="text-xs">Bottom</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.marginBottom as number || 0]}
                                    min={0}
                                    max={100}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.marginBottom', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.marginBottom || 0}px</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="background">
                      <AccordionTrigger className="text-sm font-medium">Background</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Màu nền</Label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={selectedElement.style?.backgroundColor || '#ffffff'}
                                onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                                className="w-8 h-8 rounded"
                              />
                              <Input
                                value={selectedElement.style?.backgroundColor || '#ffffff'}
                                onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Switch
                              id="transparent-bg"
                              checked={!selectedElement.style?.backgroundColor || selectedElement.style?.backgroundColor === 'transparent'}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  updateSelectedElement('style.backgroundColor', 'transparent');
                                } else {
                                  updateSelectedElement('style.backgroundColor', '#ffffff');
                                }
                              }}
                            />
                            <Label htmlFor="transparent-bg">Nền trong suốt</Label>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="border">
                      <AccordionTrigger className="text-sm font-medium">Border</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Kiểu viền</Label>
                            <Select
                              value={selectedElement.style?.borderStyle || 'none'}
                              onValueChange={(value) => updateSelectedElement('style.borderStyle', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn kiểu viền" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Không viền</SelectItem>
                                <SelectItem value="solid">Nét liền</SelectItem>
                                <SelectItem value="dashed">Nét đứt</SelectItem>
                                <SelectItem value="dotted">Chấm</SelectItem>
                                <SelectItem value="double">Nét đôi</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {selectedElement.style?.borderStyle && selectedElement.style?.borderStyle !== 'none' && (
                            <>
                              <div className="space-y-2">
                                <Label>Độ dày viền</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.borderWidth as number || 1]}
                                    min={1}
                                    max={10}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.borderWidth', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.borderWidth || 1}px</span>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <Label>Màu viền</Label>
                                <div className="flex items-center gap-2">
                                  <input
                                    type="color"
                                    value={selectedElement.style?.borderColor || '#000000'}
                                    onChange={(e) => updateSelectedElement('style.borderColor', e.target.value)}
                                    className="w-8 h-8 rounded"
                                  />
                                  <Input
                                    value={selectedElement.style?.borderColor || '#000000'}
                                    onChange={(e) => updateSelectedElement('style.borderColor', e.target.value)}
                                    className="flex-1"
                                  />
                                </div>
                              </div>

                              <div className="space-y-2">
                                <Label>Bo góc</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.borderRadius as number || 0]}
                                    min={0}
                                    max={50}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.borderRadius', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.borderRadius || 0}px</span>
                                </div>
                              </div>
                            </>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}

              {selectedElement.type === 'image' && (
                <div className="space-y-4">
                  <Accordion type="single" collapsible defaultValue="content">
                    <AccordionItem value="content">
                      <AccordionTrigger className="text-sm font-medium">Nội dung</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>URL hình ảnh</Label>
                            <Input
                              placeholder="https://example.com/image.jpg"
                              value={selectedElement.src || ''}
                              onChange={(e) => updateSelectedElement('src', e.target.value)}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Văn bản thay thế (alt)</Label>
                            <Input
                              placeholder="Mô tả hình ảnh"
                              value={selectedElement.alt || ''}
                              onChange={(e) => updateSelectedElement('alt', e.target.value)}
                            />
                          </div>

                          <div className="flex items-center justify-between mb-2">
                            <div className="text-xs text-muted-foreground">
                              {selectedIndex === -1 ? 'Hình ảnh trong cột' : 'Hình ảnh chính'}
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-6 px-2 text-xs"
                            >
                              Cập nhật
                            </Button>
                          </div>

                          <div className="flex gap-2 mb-2">
                            <Button
                              variant="outline"
                              className="flex-1"
                              onClick={() => {
                                setCurrentTab('assets');
                                if (leftPanelVisible === false) {
                                  setLeftPanelVisible(true);
                                }
                              }}
                            >
                              <UploadCloud size={16} className="mr-2" />
                              Chọn từ thư viện
                            </Button>
                            {selectedIndex === -1 && (
                              <Button
                                variant="outline"
                                className="flex-1"
                                onClick={() => {
                                  // Tìm column chứa phần tử
                                  const parentId = selectedElement?.parent;
                                  const parentIndex = emailElements.findIndex(el => el.id === parentId);
                                  const parentElement = emailElements[parentIndex];
                                  if (parentIndex !== -1 && parentElement) {
                                    handleSelectElement(parentElement, parentIndex);
                                  }
                                }}
                              >
                                <ArrowUp size={16} className="mr-2" />
                                Chọn cột
                              </Button>
                            )}
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="dimensions">
                      <AccordionTrigger className="text-sm font-medium">Kích thước</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Chiều rộng</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseInt(String(selectedElement.style?.width || '100').replace('%', ''))]}
                                min={10}
                                max={100}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.width', `${value[0]}%`)}
                              />
                              <span className="w-12 text-center">{selectedElement.style?.width || '100%'}</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Căn chỉnh</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant={selectedElement.style?.margin === '0 auto 0 0' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.margin', '0 auto 0 0')}
                              >
                                <AlignLeft size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.margin === '0 auto' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.margin', '0 auto')}
                              >
                                <AlignCenter size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.margin === '0 0 0 auto' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.margin', '0 0 0 auto')}
                              >
                                <AlignRight size={16} />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="spacing">
                      <AccordionTrigger className="text-sm font-medium">Khoảng cách</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Margin trên</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.style?.marginTop as number || 10]}
                                min={0}
                                max={100}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.marginTop', value[0])}
                              />
                              <span className="w-8 text-center">{selectedElement.style?.marginTop || 10}px</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Margin dưới</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.style?.marginBottom as number || 10]}
                                min={0}
                                max={100}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.marginBottom', value[0])}
                              />
                              <span className="w-8 text-center">{selectedElement.style?.marginBottom || 10}px</span>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="border">
                      <AccordionTrigger className="text-sm font-medium">Viền</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Kiểu viền</Label>
                            <Select
                              value={selectedElement.style?.borderStyle || 'none'}
                              onValueChange={(value) => updateSelectedElement('style.borderStyle', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn kiểu viền" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Không viền</SelectItem>
                                <SelectItem value="solid">Nét liền</SelectItem>
                                <SelectItem value="dashed">Nét đứt</SelectItem>
                                <SelectItem value="dotted">Chấm</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {selectedElement.style?.borderStyle && selectedElement.style.borderStyle !== 'none' && (
                            <>
                              <div className="space-y-2">
                                <Label>Độ dày viền</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.borderWidth as number || 1]}
                                    min={1}
                                    max={10}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.borderWidth', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.borderWidth || 1}px</span>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <Label>Màu viền</Label>
                                <div className="flex items-center gap-2">
                                  <input
                                    type="color"
                                    value={selectedElement.style?.borderColor || '#000000'}
                                    onChange={(e) => updateSelectedElement('style.borderColor', e.target.value)}
                                    className="w-8 h-8 rounded"
                                  />
                                  <Input
                                    value={selectedElement.style?.borderColor || '#000000'}
                                    onChange={(e) => updateSelectedElement('style.borderColor', e.target.value)}
                                    className="flex-1"
                                  />
                                </div>
                              </div>
                            </>
                          )}

                          <div className="space-y-2">
                            <Label>Bo góc</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseInt(String(selectedElement.style?.borderRadius || '0').replace('px', ''))]
                                }
                                min={0}
                                max={50}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.borderRadius', `${value[0]}px`)}
                              />
                              <span className="w-12 text-center">{selectedElement.style?.borderRadius || '0px'}</span>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}

              {selectedElement.type === 'button' && (
                <div className="space-y-4">
                  <Accordion type="single" collapsible defaultValue="content">
                    <AccordionItem value="content">
                      <AccordionTrigger className="text-sm font-medium">Nội dung</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Văn bản nút</Label>
                            <Input
                              placeholder="Nhấn vào đây"
                              value={selectedElement.text || ''}
                              onChange={(e) => updateSelectedElement('text', e.target.value)}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Liên kết URL</Label>
                            <Input
                              placeholder="https://example.com"
                              value={selectedElement.url || ''}
                              onChange={(e) => updateSelectedElement('url', e.target.value)}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="text-xs text-muted-foreground">
                              {selectedIndex === -1 ? 'Nút trong cột' : 'Nút chính'}
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-6 px-2 text-xs"
                            >
                              Cập nhật
                            </Button>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="style">
                      <AccordionTrigger className="text-sm font-medium">Kiểu dáng</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Màu nền</Label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={selectedElement.style?.backgroundColor || '#3b82f6'}
                                onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                                className="w-8 h-8 rounded"
                              />
                              <Input
                                value={selectedElement.style?.backgroundColor || '#3b82f6'}
                                onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Màu chữ</Label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={selectedElement.style?.color || '#ffffff'}
                                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                                className="w-8 h-8 rounded"
                              />
                              <Input
                                value={selectedElement.style?.color || '#ffffff'}
                                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Bo góc</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.style?.borderRadius as number || 4]}
                                min={0}
                                max={20}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.borderRadius', value[0])}
                              />
                              <span className="w-8 text-center">{selectedElement.style?.borderRadius || 4}px</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Padding</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseInt(String(selectedElement.style?.padding || '8').replace('px', ''))]
                                }
                                min={4}
                                max={24}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.padding', `${value[0]}px`)}
                              />
                              <span className="w-12 text-center">{selectedElement.style?.padding || '8px'}</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Độ đậm</Label>
                            <Select
                              value={String(selectedElement.style?.fontWeight || 'bold')}
                              onValueChange={(value) => updateSelectedElement('style.fontWeight', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn độ đậm" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="normal">Normal</SelectItem>
                                <SelectItem value="bold">Bold</SelectItem>
                                <SelectItem value="600">Semi Bold</SelectItem>
                                <SelectItem value="800">Extra Bold</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="alignment">
                      <AccordionTrigger className="text-sm font-medium">Căn chỉnh</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Căn chỉnh</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant={selectedElement.style?.textAlign === 'left' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'left')}
                              >
                                <AlignLeft size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textAlign === 'center' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'center')}
                              >
                                <AlignCenter size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textAlign === 'right' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'right')}
                              >
                                <AlignRight size={16} />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Chiều rộng</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseInt(String(selectedElement.style?.width || '100').replace('%', ''))]
                                }
                                min={20}
                                max={100}
                                step={5}
                                onValueChange={(value) => updateSelectedElement('style.width', `${value[0]}%`)}
                              />
                              <span className="w-12 text-center">{selectedElement.style?.width || '100%'}</span>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}

              {selectedElement.type === 'heading' && (
                <div className="space-y-4">
                  <Accordion type="single" collapsible defaultValue="content">
                    <AccordionItem value="content">
                      <AccordionTrigger className="text-sm font-medium">Nội dung</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2">
                          <Textarea
                            placeholder="Nhập nội dung tiêu đề"
                            value={selectedElement.content || ''}
                            onChange={(e) => updateSelectedElement('content', e.target.value)}
                            className="min-h-[80px]"
                          />
                          <div className="flex items-center justify-between">
                            <div className="text-xs text-muted-foreground">
                              {selectedIndex === -1 ? 'Tiêu đề trong cột' : 'Tiêu đề chính'}
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-6 px-2 text-xs"
                            >
                              Cập nhật
                            </Button>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="typography">
                      <AccordionTrigger className="text-sm font-medium">Typography</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Loại tiêu đề</Label>
                            <Select
                              value={selectedElement.headingType || 'h2'}
                              onValueChange={(value) => updateSelectedElement('headingType', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn loại tiêu đề" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="h1">H1</SelectItem>
                                <SelectItem value="h2">H2</SelectItem>
                                <SelectItem value="h3">H3</SelectItem>
                                <SelectItem value="h4">H4</SelectItem>
                                <SelectItem value="h5">H5</SelectItem>
                                <SelectItem value="h6">H6</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Font Family</Label>
                            <Select
                              value={selectedElement.style?.fontFamily || 'Arial, sans-serif'}
                              onValueChange={(value) => updateSelectedElement('style.fontFamily', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn font" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                                <SelectItem value="'Helvetica Neue', Helvetica, Arial, sans-serif">Helvetica</SelectItem>
                                <SelectItem value="'Times New Roman', Times, serif">Times New Roman</SelectItem>
                                <SelectItem value="Georgia, serif">Georgia</SelectItem>
                                <SelectItem value="'Courier New', Courier, monospace">Courier New</SelectItem>
                                <SelectItem value="Tahoma, Geneva, sans-serif">Tahoma</SelectItem>
                                <SelectItem value="Verdana, Geneva, sans-serif">Verdana</SelectItem>
                                <SelectItem value="'Trebuchet MS', Helvetica, sans-serif">Trebuchet MS</SelectItem>
                                <SelectItem value="'Segoe UI', Tahoma, Geneva, sans-serif">Segoe UI</SelectItem>
                                <SelectItem value="Roboto, Arial, sans-serif">Roboto</SelectItem>
                                <SelectItem value="'Open Sans', Arial, sans-serif">Open Sans</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Kích thước</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.style?.fontSize as number || 24]}
                                min={16}
                                max={72}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.fontSize', value[0])}
                              />
                              <span className="w-8 text-center">{selectedElement.style?.fontSize || 24}px</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Độ đậm</Label>
                            <Select
                              value={String(selectedElement.style?.fontWeight || 'bold')}
                              onValueChange={(value) => updateSelectedElement('style.fontWeight', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn độ đậm" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="normal">Normal</SelectItem>
                                <SelectItem value="bold">Bold</SelectItem>
                                <SelectItem value="600">Semi Bold</SelectItem>
                                <SelectItem value="800">Extra Bold</SelectItem>
                                <SelectItem value="900">Black</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Màu chữ</Label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={selectedElement.style?.color || '#111111'}
                                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                                className="w-8 h-8 rounded"
                              />
                              <Input
                                value={selectedElement.style?.color || '#111111'}
                                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Canh lề</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant={selectedElement.style?.textAlign === 'left' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'left')}
                              >
                                <AlignLeft size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textAlign === 'center' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'center')}
                              >
                                <AlignCenter size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textAlign === 'right' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textAlign', 'right')}
                              >
                                <AlignRight size={16} />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Định dạng</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant={selectedElement.style?.fontStyle === 'italic' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.fontStyle', selectedElement.style?.fontStyle === 'italic' ? 'normal' : 'italic')}
                              >
                                <Italic size={16} />
                              </Button>
                              <Button
                                variant={selectedElement.style?.textTransform === 'uppercase' ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => updateSelectedElement('style.textTransform', selectedElement.style?.textTransform === 'uppercase' ? 'none' : 'uppercase')}
                              >
                                <Type size={16} />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Letter Spacing</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseFloat(String(selectedElement.style?.letterSpacing || 0)) * 10]}
                                min={-5}
                                max={20}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.letterSpacing', (value[0] ?? 0) / 10 + 'px')}
                              />
                              <span className="w-12 text-center">{parseFloat(String(selectedElement.style?.letterSpacing || 0))}px</span>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="spacing">
                      <AccordionTrigger className="text-sm font-medium">Spacing</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Margin</Label>
                            <div className="grid grid-cols-2 gap-2">
                              <div>
                                <Label className="text-xs">Top</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.marginTop as number || 0]}
                                    min={0}
                                    max={100}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.marginTop', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.marginTop || 0}px</span>
                                </div>
                              </div>
                              <div>
                                <Label className="text-xs">Bottom</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.marginBottom as number || 16]}
                                    min={0}
                                    max={100}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.marginBottom', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.marginBottom || 16}px</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}

              {selectedElement.type === 'list' && (
                <div className="space-y-4">
                  <Accordion type="single" collapsible defaultValue="content">
                    <AccordionItem value="content">
                      <AccordionTrigger className="text-sm font-medium">Nội dung</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Nội dung danh sách</Label>
                            <Textarea
                              placeholder="Nhập nội dung danh sách"
                              value={selectedElement.content || '<ul><li>Mục danh sách 1</li><li>Mục danh sách 2</li><li>Mục danh sách 3</li></ul>'}
                              onChange={(e) => updateSelectedElement('content', e.target.value)}
                              className="min-h-[120px]"
                            />
                            <div className="text-xs text-muted-foreground">
                              Mỗi mục danh sách nên được đặt trong thẻ &lt;li&gt;...&lt;/li&gt;
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Loại danh sách</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant={selectedElement.content?.includes('<ul>') ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => {
                                  const content = selectedElement.content || '';
                                  const newContent = content.replace(/<ol>/g, '<ul>').replace(/<\/ol>/g, '</ul>');
                                  updateSelectedElement('content', newContent);
                                }}
                              >
                                <List size={16} className="mr-2" />
                                Không thứ tự
                              </Button>
                              <Button
                                variant={selectedElement.content?.includes('<ol>') ? 'default' : 'outline'}
                                size="sm"
                                className="flex-1"
                                onClick={() => {
                                  const content = selectedElement.content || '';
                                  const newContent = content.replace(/<ul>/g, '<ol>').replace(/<\/ul>/g, '</ol>');
                                  updateSelectedElement('content', newContent);
                                }}
                              >
                                <ListOrdered size={16} className="mr-2" />
                                Có thứ tự
                              </Button>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="text-xs text-muted-foreground">
                              {selectedIndex === -1 ? 'Danh sách trong cột' : 'Danh sách chính'}
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-6 px-2 text-xs"
                            >
                              Cập nhật
                            </Button>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="style">
                      <AccordionTrigger className="text-sm font-medium">Kiểu dáng</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Màu chữ</Label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={selectedElement.style?.color || '#333333'}
                                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                                className="w-8 h-8 rounded"
                              />
                              <Input
                                value={selectedElement.style?.color || '#333333'}
                                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Kích thước chữ</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.style?.fontSize as number || 16]}
                                min={12}
                                max={24}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.fontSize', value[0])}
                              />
                              <span className="w-8 text-center">{selectedElement.style?.fontSize || 16}px</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Khoảng cách dòng</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseFloat(String(selectedElement.style?.lineHeight || 1.5)) * 10]}
                                min={10}
                                max={30}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.lineHeight', (value[0] ?? 0) / 10)}
                              />
                              <span className="w-8 text-center">{(selectedElement.style?.lineHeight || 1.5)}</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Khoảng cách giữa các mục</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseInt(String(selectedElement.style?.gap || '8').replace('px', ''))]
                                }
                                min={0}
                                max={20}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.gap', `${value[0]}px`)}
                              />
                              <span className="w-12 text-center">{selectedElement.style?.gap || '8px'}</span>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="spacing">
                      <AccordionTrigger className="text-sm font-medium">Khoảng cách</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Margin trên</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.style?.marginTop as number || 10]}
                                min={0}
                                max={40}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.marginTop', value[0])}
                              />
                              <span className="w-8 text-center">{selectedElement.style?.marginTop || 10}px</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Margin dưới</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.style?.marginBottom as number || 10]}
                                min={0}
                                max={40}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.marginBottom', value[0])}
                              />
                              <span className="w-8 text-center">{selectedElement.style?.marginBottom || 10}px</span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Padding</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[parseInt(String(selectedElement.style?.padding || '0').replace('px', ''))]
                                }
                                min={0}
                                max={40}
                                step={1}
                                onValueChange={(value) => updateSelectedElement('style.padding', `${value[0]}px`)}
                              />
                              <span className="w-12 text-center">{selectedElement.style?.padding || '0px'}</span>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}

              {selectedElement.type === 'divider' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Kiểu đường kẻ</Label>
                    <Select
                      value={selectedElement.style?.borderStyle || 'solid'}
                      onValueChange={(value) => updateSelectedElement('style.borderStyle', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn kiểu đường kẻ" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="solid">Nét liền</SelectItem>
                        <SelectItem value="dashed">Nét đứt</SelectItem>
                        <SelectItem value="dotted">Chấm</SelectItem>
                        <SelectItem value="double">Nét đôi</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Độ dày</Label>
                    <div className="flex items-center gap-2">
                      <Slider
                        value={[selectedElement.style?.borderWidth as number || 1]}
                        min={1}
                        max={10}
                        step={1}
                        onValueChange={(value) => updateSelectedElement('style.borderWidth', value[0])}
                      />
                      <span className="w-8 text-center">{selectedElement.style?.borderWidth || 1}px</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Màu đường kẻ</Label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        value={selectedElement.style?.borderColor || '#e5e7eb'}
                        onChange={(e) => updateSelectedElement('style.borderColor', e.target.value)}
                        className="w-8 h-8 rounded"
                      />
                      <Input
                        value={selectedElement.style?.borderColor || '#e5e7eb'}
                        onChange={(e) => updateSelectedElement('style.borderColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Khoảng cách</Label>
                    <div className="flex items-center gap-2">
                      <Slider
                        value={[
                            (() => {
                              const margin = selectedElement.style?.margin;
                              if (typeof margin === 'number') return margin;
                              if (typeof margin === 'string') {
                                const first = margin.split(' ')[0];
                                const parsed = parseInt(first || '16', 10);
                                return isNaN(parsed) ? 16 : parsed;
                              }
                              return 16;
                            })()
                          ]}
                        min={0}
                        max={50}
                        step={1}
                        onValueChange={(value) => updateSelectedElement('style.margin', `${value[0]}px 0`)}
                      />
                      <span className="w-8 text-center">
                        {
                            (() => {
                            const margin = selectedElement.style?.margin;
                            // If margin is a number, use it directly
                            if (typeof margin === 'number') return `${margin}px`;
                            // If margin is a string, try to parse the first value
                            if (typeof margin === 'string') {
                                const first = margin.split(' ')[0];
                                const parsed = parseInt(first || '0', 10);
                                return isNaN(parsed) ? '16px' : `${parsed}px`;
                            }
                            // Default fallback
                            return '16px';
                            })()
                        }
                        </span>
                    </div>
                  </div>
                </div>
              )}

              {selectedElement.type === 'link' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Văn bản liên kết</Label>
                    <Input
                      placeholder="Văn bản hiển thị"
                      value={selectedElement.text || ''}
                      onChange={(e) => updateSelectedElement('text', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>URL đích</Label>
                    <Input
                      placeholder="https://example.com"
                      value={selectedElement.url || ''}
                      onChange={(e) => updateSelectedElement('url', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Màu liên kết</Label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        value={selectedElement.style?.color || '#0070f3'}
                        onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                        className="w-8 h-8 rounded"
                      />
                      <Input
                        value={selectedElement.style?.color || '#0070f3'}
                        onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Kiểu gạch chân</Label>
                    <Select
                      value={selectedElement.style?.textDecoration || 'underline'}
                      onValueChange={(value) => updateSelectedElement('style.textDecoration', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn kiểu gạch chân" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="underline">Gạch chân</SelectItem>
                        <SelectItem value="none">Không gạch chân</SelectItem>
                        <SelectItem value="overline">Gạch trên</SelectItem>
                        <SelectItem value="line-through">Gạch ngang</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {selectedElement.type === 'spacer' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Chiều cao</Label>
                    <div className="flex items-center gap-2">
                      <Slider
                        value={[selectedElement.style?.height as number || 32]}
                        min={4}
                        max={100}
                        step={4}
                        onValueChange={(value) => updateSelectedElement('style.height', value[0])}
                      />
                      <span className="w-8 text-center">{selectedElement.style?.height || 32}px</span>
                    </div>
                  </div>
                </div>
              )}

              {(selectedElement.type === '1column' || selectedElement.type === '2columns') && (
                <div className="space-y-4">
                  <Accordion type="single" collapsible defaultValue="layout">
                    <AccordionItem value="layout">
                      <AccordionTrigger className="text-sm font-medium">Layout</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Màu nền cột</Label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={selectedElement.columnBgColor || '#f5f5f5'}
                                onChange={(e) => {
                                  updateSelectedElement('columnBgColor', e.target.value);
                                }}
                                className="w-8 h-8 rounded"
                              />
                              <Input
                                value={selectedElement.columnBgColor || '#f5f5f5'}
                                onChange={(e) => {
                                  updateSelectedElement('columnBgColor', e.target.value);
                                }}
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Khoảng đệm cột</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.columnPadding || 20]}
                                min={0}
                                max={40}
                                step={4}
                                onValueChange={(value) => {
                                  updateSelectedElement('columnPadding', value[0]);
                                }}
                              />
                              <span className="w-8 text-center">{selectedElement.columnPadding || 20}px</span>
                            </div>
                          </div>

                          {selectedElement.type === '2columns' && (
                            <div className="space-y-2">
                              <Label>Khoảng cách giữa các cột</Label>
                              <div className="flex items-center gap-2">
                                <Slider
                                  value={[selectedElement.columnGap || 20]}
                                  min={0}
                                  max={40}
                                  step={4}
                                  onValueChange={(value) => {
                                    updateSelectedElement('columnGap', value[0]);
                                  }}
                                />
                                <span className="w-8 text-center">{selectedElement.columnGap || 20}px</span>
                              </div>
                            </div>
                          )}

                          <div className="space-y-2">
                            <Label>Bo góc</Label>
                            <div className="flex items-center gap-2">
                              <Slider
                                value={[selectedElement.children?.[0]?.style?.borderRadius as number || 4]}
                                min={0}
                                max={20}
                                step={1}
                                onValueChange={(value) => {
                                  // Cập nhật borderRadius cho tất cả các cột
                                  if (selectedElement.children) {
                                    const updatedChildren = selectedElement.children.map(child => {
                                      if (child.type === 'column') {
                                        return {
                                          ...child,
                                          style: {
                                            ...child.style,
                                            borderRadius: value[0]
                                          }
                                        };
                                      }
                                      return child;
                                    });

                                    updateSelectedElement('children', updatedChildren);
                                  }
                                }}
                              />
                              <span className="w-8 text-center">{selectedElement.children?.[0]?.style?.borderRadius || 4}px</span>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="spacing">
                      <AccordionTrigger className="text-sm font-medium">Spacing</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Margin</Label>
                            <div className="grid grid-cols-2 gap-2">
                              <div>
                                <Label className="text-xs">Top</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.marginTop as number || 0]}
                                    min={0}
                                    max={50}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.marginTop', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.marginTop || 0}px</span>
                                </div>
                              </div>
                              <div>
                                <Label className="text-xs">Bottom</Label>
                                <div className="flex items-center gap-2">
                                  <Slider
                                    value={[selectedElement.style?.marginBottom as number || 20]}
                                    min={0}
                                    max={50}
                                    step={1}
                                    onValueChange={(value) => updateSelectedElement('style.marginBottom', value[0])}
                                  />
                                  <span className="w-8 text-center">{selectedElement.style?.marginBottom || 20}px</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="content">
                      <AccordionTrigger className="text-sm font-medium">Thêm nội dung</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label>Thêm phần tử vào cột</Label>
                            <div className="grid grid-cols-2 gap-2">
                              {selectedElement.type === '1column' ? (
                                <>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      const columnIndex = emailElements.findIndex(el => el.id === selectedElement.id);
                                      if (columnIndex !== -1) {
                                        addElementToColumn(selectedElement.id, 'text');
                                      }
                                    }}
                                  >
                                    <Type size={14} className="mr-2" />
                                    Thêm Text
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      const columnIndex = emailElements.findIndex(el => el.id === selectedElement.id);
                                      if (columnIndex !== -1) {
                                        addElementToColumn(selectedElement.id, 'image');
                                      }
                                    }}
                                  >
                                    <ImageIcon size={14} className="mr-2" />
                                    Thêm Ảnh
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      const columnIndex = emailElements.findIndex(el => el.id === selectedElement.id);
                                      if (columnIndex !== -1) {
                                        addElementToColumn(selectedElement.id, 'button');
                                      }
                                    }}
                                  >
                                    <ButtonIcon size={14} className="mr-2" />
                                    Thêm Nút
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      const columnIndex = emailElements.findIndex(el => el.id === selectedElement.id);
                                      if (columnIndex !== -1) {
                                        addElementToColumn(selectedElement.id, 'heading');
                                      }
                                    }}
                                  >
                                    <Heading1 size={14} className="mr-2" />
                                    Thêm Tiêu đề
                                  </Button>
                                </>
                              ) : (
                                <>
                                  <div className="col-span-2 mb-2">
                                    <Label>Chọn cột:</Label>
                                    <div className="flex gap-2 mt-1">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex-1"
                                      >
                                        Cột trái
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex-1"
                                      >
                                        Cột phải
                                      </Button>
                                    </div>
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      // Tìm column trái
                                      const leftColumn = selectedElement.children?.find(child => child.columnPosition === 'left');
                                      if (leftColumn) {
                                        const columnIndex = emailElements.findIndex(el => el.id === selectedElement.id);
                                        if (columnIndex !== -1 && leftColumn.children) {
                                          const newTextElement: EmailElement = {
                                            id: `text-${Date.now()}`,
                                            type: 'text',
                                            content: 'Nhấp đôi để chỉnh sửa văn bản này',
                                            parent: leftColumn.id,
                                            draggable: true,
                                            removable: true,
                                            editable: true,
                                            selectable: true,
                                            hoverable: true,
                                            copyable: true,
                                            style: {
                                              fontSize: 16,
                                              lineHeight: 1.5,
                                              color: '#333333',
                                              margin: 0,
                                            }
                                          };

                                          leftColumn.children.push(newTextElement);

                                          const newElements = [...emailElements];
                                          newElements[columnIndex] = { ...selectedElement };
                                          setEmailElements(newElements);
                                        }
                                      }
                                    }}
                                  >
                                    <Type size={14} className="mr-2" />
                                    Thêm Text
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      // Tìm column trái
                                      const leftColumn = selectedElement.children?.find(child => child.columnPosition === 'left');
                                      if (leftColumn) {
                                        const columnIndex = emailElements.findIndex(el => el.id === selectedElement.id);
                                        if (columnIndex !== -1 && leftColumn.children) {
                                          const newImageElement: EmailElement = {
                                            id: `image-${Date.now()}`,
                                            type: 'image',
                                            src: 'https://via.placeholder.com/600x200?text=Hình+ảnh+mẫu',
                                            alt: 'Hình ảnh mẫu',
                                            parent: leftColumn.id,
                                            draggable: true,
                                            removable: true,
                                            editable: true,
                                            selectable: true,
                                            hoverable: true,
                                            copyable: true,
                                            style: {
                                              width: '100%',
                                              display: 'block',
                                              margin: '10px 0',
                                            }
                                          };

                                          leftColumn.children.push(newImageElement);

                                          const newElements = [...emailElements];
                                          newElements[columnIndex] = { ...selectedElement };
                                          setEmailElements(newElements);
                                        }
                                      }
                                    }}
                                  >
                                    <ImageIcon size={14} className="mr-2" />
                                    Thêm Ảnh
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}

              {(selectedElement.type === 'header' || selectedElement.type === 'footer') && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Nội dung HTML</Label>
                    <Textarea
                      placeholder="Nhập nội dung HTML"
                      value={selectedElement.content || ''}
                      onChange={(e) => updateSelectedElement('content', e.target.value)}
                      className="min-h-[200px] font-mono text-xs"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Màu nền</Label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        value={selectedElement.style?.backgroundColor || '#f8f9fa'}
                        onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                        className="w-8 h-8 rounded"
                      />
                      <Input
                        value={selectedElement.style?.backgroundColor || '#f8f9fa'}
                        onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Khoảng đệm</Label>
                    <div className="flex items-center gap-2">
                      <Slider
                        value={[parseInt(String(selectedElement.style?.padding || 16))]}
                        min={0}
                        max={48}
                        step={4}
                        onValueChange={(value) => updateSelectedElement('style.padding', value[0])}
                      />
                      <span className="w-8 text-center">{selectedElement.style?.padding || 16}px</span>
                    </div>
                  </div>

                  {selectedElement.type === 'header' && (
                    <>
                      <div className="space-y-2">
                        <Label>URL Logo</Label>
                        <Input
                          placeholder="https://example.com/logo.png"
                          value={selectedElement['logoUrl'] || ''}
                          onChange={(e) => {
                            updateSelectedElement('logoUrl', e.target.value);
                            // Cập nhật URL trong nội dung HTML
                            const content = selectedElement.content || '';
                            const newContent = content.replace(
                              /(src=")[^"]*(".*alt="Logo")/,
                              `$1${e.target.value}$2`
                            );
                            updateSelectedElement('content', newContent);
                          }}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Màu nền menu</Label>
                        <div className="flex items-center gap-2">
                          <input
                            type="color"
                            value={selectedElement['menuBgColor'] || '#f8f9fa'}
                            onChange={(e) => {
                              updateSelectedElement('menuBgColor', e.target.value);
                              // Cập nhật màu nền trong nội dung HTML
                              const content = selectedElement.content || '';
                              const newContent = content.replace(
                                /(bgcolor=")[^"]*(".*border-top)/,
                                `$1${e.target.value}$2`
                              );
                              updateSelectedElement('content', newContent);
                            }}
                            className="w-8 h-8 rounded"
                          />
                          <Input
                            value={selectedElement['menuBgColor'] || '#f8f9fa'}
                            onChange={(e) => {
                              updateSelectedElement('menuBgColor', e.target.value);
                              // Cập nhật màu nền trong nội dung HTML
                              const content = selectedElement.content || '';
                              const newContent = content.replace(
                                /(bgcolor=")[^"]*(".*border-top)/,
                                `$1${e.target.value}$2`
                              );
                              updateSelectedElement('content', newContent);
                            }}
                            className="flex-1"
                          />
                        </div>
                      </div>
                    </>
                  )}

                  {selectedElement.type === 'footer' && (
                    <>
                      <div className="space-y-2">
                        <Label>Thông tin công ty</Label>
                        <Textarea
                          placeholder="Tên công ty, địa chỉ, liên hệ..."
                          value={selectedElement['companyInfo'] || 'Công ty TNHH ABC\nĐịa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM\nEmail: <EMAIL> | Điện thoại: (123) 456-7890'}
                          onChange={(e) => {
                            updateSelectedElement('companyInfo', e.target.value);
                            // Cập nhật thông tin công ty trong nội dung HTML
                            const content = selectedElement.content || '';
                            const newContent = content.replace(
                              /(style="padding-bottom: 20px; color: #666; font-size: 14px; line-height: 1.5;">)[\s\S]*?(<\/td>)/,
                              `$1${e.target.value.replace(/\n/g, '<br />')}$2`
                            );
                            updateSelectedElement('content', newContent);
                          }}
                          className="min-h-[100px]"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Hiển thị mạng xã hội</Label>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="show-social"
                            checked={selectedElement['showSocial'] !== false}
                            onCheckedChange={(checked) => {
                              updateSelectedElement('showSocial', checked);
                              // Ẩn/hiện phần mạng xã hội trong nội dung HTML
                              const content = selectedElement.content || '';
                              if (checked) {
                                // Hiển thị mạng xã hội (mặc định đã có)
                              } else {
                                // Ẩn phần mạng xã hội
                                const newContent = content.replace(
                                  /<tr>[\s\S]*?<td align="center" style="padding-bottom: 20px;">[\s\S]*?<\/td>[\s\S]*?<\/tr>/,
                                  ''
                                );
                                updateSelectedElement('content', newContent);
                              }
                            }}
                          />
                          <Label htmlFor="show-social">Hiển thị biểu tượng mạng xã hội</Label>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Code Viewer Modal */}
      {showHtmlCode && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-card rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Mã nguồn</h2>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant={codeTab === 'html' ? 'default' : 'outline'}
                  onClick={() => setCodeTab('html')}
                >
                  HTML
                </Button>
                <Button
                  size="sm"
                  variant={codeTab === 'css' ? 'default' : 'outline'}
                  onClick={() => setCodeTab('css')}
                >
                  CSS
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="ml-4"
                  onClick={() => {
                    // Sao chép mã vào clipboard
                    const content = codeTab === 'html' ? generateHTML() : cssCode;
                    navigator.clipboard.writeText(content).then(() => {
                      toast({
                        title: "Đã sao chép",
                        description: `Mã ${codeTab.toUpperCase()} đã được sao chép vào clipboard.`,
                        duration: 3000,
                      });
                    });
                  }}
                >
                  <Clipboard size={16} className="mr-2" />
                  Sao chép
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowHtmlCode(false)}
                >
                  <X size={16} />
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-auto p-4 bg-muted/30">
              <pre className="text-sm font-mono whitespace-pre-wrap bg-muted p-4 rounded-md overflow-auto max-h-[70vh]">
                {codeTab === 'html' ? (
                  <code>{generateHTML()}</code>
                ) : (
                  <code>{cssCode}</code>
                )}
              </pre>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}