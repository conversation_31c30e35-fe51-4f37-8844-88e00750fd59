# Kế hoạch khắc phục lỗi màn hình trắng sau khi build

## 1. Phân tích vấn đề

### 1.1. Tổng quan lỗi
- **Lỗi 1**: `Uncaught TypeError: Cannot set properties of undefined (setting 'Children')` - Lỗi liên quan đến React
- **Lỗi 2**: `Failed to load resource: the server responded with a status of 404 (Not Found)` - Lỗi tài nguyên không tìm thấy

### 1.2. Nguyên nhân tiềm ẩn
- Sự khác biệt giữa môi trường development và production
- Vấn đề với cấu hình Vite khi build
- Đường dẫn tương đối không được xử lý đúng trong bản build
- Vấn đề với các thư viện React hoặc cách import

## 2. Kế hoạch kiểm tra và khắc phục

### 2.1. <PERSON>ểm tra cấu trúc dự án (<PERSON>ày 1)
- [ ] Kiểm tra cấu trúc thư mục và tệp tin
- [ ] Xác minh các đường dẫn tương đối trong mã nguồn
- [ ] Kiểm tra cấu hình Vite và các plugin

### 2.2. Kiểm tra mã nguồn React (Ngày 1)
- [ ] Kiểm tra các component React có sử dụng React.Children
- [ ] Xác minh cách import React trong các file
- [ ] Kiểm tra các component có sử dụng React.lazy hoặc Suspense

### 2.3. Khắc phục lỗi React Children (Ngày 2)
- [ ] Cập nhật file main.tsx để đảm bảo React được import đúng cách
- [ ] Kiểm tra phiên bản React và các thư viện liên quan
- [ ] Cập nhật các component có sử dụng React.Children

### 2.4. Khắc phục lỗi tài nguyên không tìm thấy (Ngày 2)
- [ ] Tạo thư mục public nếu chưa có
- [ ] Di chuyển các tài nguyên tĩnh vào thư mục public
- [ ] Cập nhật đường dẫn trong index.html

### 2.5. Cập nhật cấu hình Vite (Ngày 3)
- [ ] Cập nhật cấu hình build trong vite.config.ts
- [ ] Điều chỉnh cấu hình rollupOptions
- [ ] Cập nhật cấu hình ESLint để không dừng quá trình build khi có lỗi

### 2.6. Kiểm tra và tối ưu hóa (Ngày 3)
- [ ] Thực hiện build thử nghiệm
- [ ] Kiểm tra kết quả build bằng trình duyệt
- [ ] Sử dụng công cụ phân tích bundle để tìm vấn đề

## 3. Giải pháp cụ thể

### 3.1. Cập nhật file main.tsx
```tsx
// Đảm bảo React được import đầu tiên
import React, { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

// Import TanStack Query sau React
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Import styles
import './index.css';
import '@/shared/styles/scrollbar.css';

// Import app và query client
import App from './App.tsx';
import { queryClient } from '@/shared/api';
```

### 3.2. Cập nhật index.html
```html
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="./favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RedAI Frontend</title>
    <script src="https://www.google.com/recaptcha/enterprise.js" async defer></script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
```

### 3.3. Cập nhật vite.config.ts
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
// @ts-ignore
import eslint from 'vite-plugin-eslint';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import checker from 'vite-plugin-checker';

export default defineConfig({
  server: {
    host: true,
    cors: true,
    proxy: {},
    strictPort: false,
    allowedHosts: ['localhost', '127.0.0.1', 'v2.redai.vn', '*************'],
  },
  preview: {
    host: true,
    port: 5173,
    strictPort: false,
    allowedHosts: ['localhost', '127.0.0.1', 'v2.redai.vn', '*************'],
  },
  build: {
    chunkSizeWarningLimit: 1000,
    minify: true,
    sourcemap: true,
    reportCompressedSize: true,
    rollupOptions: {
      output: {
        manualChunks: id => {
          // Đảm bảo React được tải trước
          if (id.includes('node_modules/react') || id.includes('node_modules/react-dom')) {
            return 'react-core';
          }

          // Các thư viện React khác và React Query
          if (
            id.includes('node_modules/react-router') ||
            id.includes('node_modules/react-redux') ||
            id.includes('node_modules/@tanstack/react-query')
          ) {
            return 'react-vendor';
          }

          // Tách các thư viện form
          if (
            id.includes('node_modules/react-hook-form') ||
            id.includes('node_modules/@hookform')
          ) {
            return 'form-libs';
          }
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  plugins: [
    react(),
    eslint({
      failOnError: false, // Không dừng quá trình build nếu có lỗi ESLint
      failOnWarning: false,
      include: ['src/**/*.ts', 'src/**/*.tsx'],
    }),
    checker({
      typescript: {
        tsconfigPath: './tsconfig.app.json',
        root: './',
        buildMode: true,
      },
      overlay: {
        initialIsOpen: true,
        position: 'tl',
      },
      enableBuild: true,
      terminal: true,
    }),
    visualizer({
      open: true,
      filename: 'dist/stats.html',
      gzipSize: true,
      brotliSize: true,
    }),
  ],
});
```

### 3.4. Cập nhật package.json
```json
{
  "scripts": {
    "build": "vite build",
    "build:staging": "vite build --mode staging",
    "build:production": "vite build --mode production",
    "build:testing": "vite build --mode testing",
    "build:localhost": "vite build --mode localhost",
    "preview": "vite preview"
  }
}
```

## 4. Kiểm tra và xác nhận (Ngày 4)

### 4.1. Kiểm tra build
- [ ] Thực hiện build với lệnh `npm run build`
- [ ] Chạy preview với lệnh `npm run preview`
- [ ] Kiểm tra console trình duyệt để xác nhận không còn lỗi

### 4.2. Kiểm tra tài nguyên
- [ ] Xác minh tất cả tài nguyên (hình ảnh, font, v.v.) được tải đúng
- [ ] Kiểm tra hiệu suất tải trang

### 4.3. Kiểm tra chức năng
- [ ] Kiểm tra tất cả các chức năng chính của ứng dụng
- [ ] Xác minh các tương tác người dùng hoạt động bình thường

## 5. Tài liệu hóa và phòng ngừa (Ngày 5)

### 5.1. Tài liệu hóa giải pháp
- [ ] Ghi lại các thay đổi đã thực hiện
- [ ] Cập nhật tài liệu dự án với các thực hành tốt nhất

### 5.2. Phòng ngừa vấn đề tương tự
- [ ] Thiết lập quy trình kiểm tra build trước khi triển khai
- [ ] Cập nhật quy trình CI/CD để phát hiện sớm các vấn đề tương tự
- [ ] Đào tạo team về các vấn đề tiềm ẩn khi build React với Vite

## 6. Theo dõi và cải tiến liên tục

### 6.1. Theo dõi
- [ ] Theo dõi hiệu suất ứng dụng sau khi triển khai
- [ ] Giám sát lỗi console trong môi trường production

### 6.2. Cải tiến
- [ ] Xem xét cải thiện quy trình build
- [ ] Tối ưu hóa kích thước bundle
- [ ] Cập nhật các thư viện và dependencies thường xuyên