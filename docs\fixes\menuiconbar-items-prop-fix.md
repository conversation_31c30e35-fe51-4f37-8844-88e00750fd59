# MenuIconBar Items Property Fix

## Vấn đề

Component `MenuIconBar` bị lỗi TypeScript vì thiếu thuộc tính `items` trong interface `MenuIconBarProps`. Nhiều file đang sử dụng `items={dataTable.menuItems}` nhưng bị lỗi:

```
Property 'items' does not exist on type 'IntrinsicAttributes & MenuIconBarProps'
```

## Nguyên nhân

- Interface `MenuIconBarProps` không có thuộc tính `items: ModernMenuItem[]`
- Component không có logic để hiển thị và xử lý menu items
- Documentation và example code có sử dụng `items` nhưng implementation thiếu

## Giải pháp

### 1. Cập nhật Interface

Thêm thuộc tính `items` vào `MenuIconBarProps`:

```typescript
interface MenuIconBarProps {
  // ... existing props
  /**
   * Danh sách các menu items để hiển thị trong filter menu
   */
  items?: ModernMenuItem[];
  // ... other props
}
```

### 2. Import Dependencies

Thêm import cho `ModernMenuTrigger` và `ModernMenuItem`:

```typescript
import {
  IconCard,
  Tooltip,
  SearchBar,
  Checkbox,
  ModernMenuTrigger,
} from '@/shared/components/common';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu/ModernMenu';
```

### 3. Implement Filter Menu Logic

Thêm logic hiển thị filter menu trong component:

```typescript
{/* Filter Menu Items */}
{items && items.length > 0 && (
  <ModernMenuTrigger
    items={items.map(item => ({
      label: item.label,
      icon: item.icon,
      onClick: item.onClick,
      disabled: item.disabled,
      divider: item.divider,
    }))}
    triggerIcon="filter"
    triggerVariant="default"
    placement="bottom"
    width="200px"
  />
)}
```

### 4. Sửa lỗi TypeScript

Sửa lỗi translation function:

```typescript
// Before
if (columnLabelMap[column.id]) {
  return t(columnLabelMap[column.id], columnLabelMap[column.id]);
}

// After  
const labelKey = columnLabelMap[column.id];
if (labelKey) {
  return t(labelKey, labelKey);
}
```

## Files Changed

- `src/modules/components/menu-bar/MenuIconBar.tsx` - Main component fix
- `src/modules/components/menu-bar/MenuIconBar.test.tsx` - Added test file

## Usage

Bây giờ component có thể được sử dụng với `items` prop:

```typescript
<MenuIconBar
  onSearch={dataTable.tableData.handleSearch}
  onAdd={handleAdd}
  items={dataTable.menuItems}
  onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
  columns={dataTable.columnVisibility.visibleColumns}
  showDateFilter={true}
  showColumnFilter={true}
/>
```

## Testing

- ✅ TypeScript compilation passes
- ✅ Build successful
- ✅ All existing usage patterns work
- ✅ New filter menu functionality works
- ✅ Unit tests added

## Impact

- Sửa được tất cả lỗi TypeScript liên quan đến `items` prop
- Không breaking changes cho existing code
- Thêm functionality mới cho filter menu
- Tương thích với `useDataTable` hook pattern
