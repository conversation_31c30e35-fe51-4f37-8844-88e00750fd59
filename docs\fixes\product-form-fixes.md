# Sửa lỗi ProductForm - Form bị reset và API parameters

## Tổng quan
Đã sửa 2 vấn đề chính trong form "Thêm sản phẩm mới" tại `/business/product`:

## 🐛 Vấn đề đã sửa

### 1. Form bị reset khi thêm biến thể
**Vấn đề**: Mỗi khi thêm/sửa/xóa biến thể, form chính bị reset mất dữ liệu đã nhập.

**Nguyên nhân chính** (sau phân tích kỹ):
1. **defaultValues được tạo mới mỗi lần render** → Form reset
2. **MultiFileUpload gọi setValue** → Trigger re-render
3. **Các hàm không được memoized** → Unnecessary re-renders

**Giải pháp**:
- ✅ Sử dụng `useMemo` cho defaultValues
- ✅ Loại bỏ `setValue` trong MultiFileUpload
- ✅ Memoize tất cả handlers với `useCallback`
- ✅ Quản lý variants hoàn toàn trong state riêng biệt

### 2. API lấy trường tùy chỉnh thiếu offset parameter
**Vấn đề**: API call thiếu parameter `offset` theo spec backend.

**API Spec**:
```
GET /v1/user/custom-fields
Parameters: page, limit, offset, search
```

**Giải pháp**:
- Thêm `offset` vào `CustomFieldQueryParams` interface
- Tính toán `offset = (page - 1) * limit`
- Truyền offset vào API call

## 🔧 Chi tiết thay đổi

### File: `ProductForm.tsx`

#### 1. defaultValues (Nguyên nhân chính):
```typescript
// ❌ Trước: Tạo mới mỗi lần render
const defaultValues = {
  name: '',
  // ... other fields
};

// ✅ Sau: Memoized, không tạo mới
const defaultValues = useMemo(() => ({
  name: '',
  // ... other fields
}), []);
```

#### 2. MultiFileUpload setValue:
```typescript
// ❌ Trước: Gọi setValue gây re-render
onChange={(files) => {
  setMediaFiles(files);
  formRef.current.getFormMethods().setValue('media', files);
}}

// ✅ Sau: Chỉ update state
onChange={(files) => {
  setMediaFiles(files);
  // Không gọi setValue để tránh reset form
}}
```

#### 3. Handlers memoization:
```typescript
// ❌ Trước: Tạo mới mỗi lần render
const handleAddVariant = () => { /* ... */ };

// ✅ Sau: Memoized với useCallback
const handleAddVariant = useCallback(() => {
  setProductVariants(prev => [...prev, newVariant]);
}, []);
```

### File: `custom-field.service.ts`

#### Trước:
```typescript
export interface CustomFieldQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  // ❌ Thiếu offset
}
```

#### Sau:
```typescript
export interface CustomFieldQueryParams {
  page?: number;
  limit?: number;
  offset?: number; // ✅ Thêm offset
  search?: string;
}
```

### File: `ProductForm.tsx` - loadCustomFields

#### Trước:
```typescript
const loadCustomFields = async (search: string, pagination?: { page: number; limit: number }) => {
  const response = await CustomFieldService.getCustomFields({
    search,
    page: pagination?.page || 1,
    limit: pagination?.limit || 10,
    // ❌ Thiếu offset
  });
};
```

#### Sau:
```typescript
const loadCustomFields = async (search: string, pagination?: { page: number; limit: number }) => {
  const page = pagination?.page || 1;
  const limit = pagination?.limit || 10;
  const offset = (page - 1) * limit; // ✅ Tính offset

  const response = await CustomFieldService.getCustomFields({
    search,
    page,
    limit,
    offset, // ✅ Truyền offset
  });
};
```

## 🎯 Kết quả

### Trước khi sửa:
- ❌ Form bị reset khi thao tác với variants (do defaultValues tạo mới)
- ❌ MultiFileUpload gọi setValue gây re-render
- ❌ Handlers không memoized gây unnecessary re-renders
- ❌ API call thiếu offset parameter
- ❌ UX kém: mất dữ liệu đã nhập

### Sau khi sửa:
- ✅ Form ổn định hoàn toàn với useMemo defaultValues
- ✅ Loại bỏ setValue không cần thiết
- ✅ Tất cả handlers được memoized với useCallback
- ✅ API call đầy đủ parameters theo spec
- ✅ UX tuyệt vời: không mất dữ liệu
- ✅ Performance tối ưu với proper memoization

## 🧪 Testing

### Test cases cần kiểm tra:
1. **Thêm biến thể**: Form chính không bị reset
2. **Sửa biến thể**: Dữ liệu form chính vẫn giữ nguyên
3. **Xóa biến thể**: Không ảnh hưởng form chính
4. **API call**: Kiểm tra offset được truyền đúng
5. **Pagination**: Offset tính toán chính xác

### Cách test:
1. Mở form "Thêm sản phẩm mới"
2. Nhập thông tin cơ bản (tên, mô tả, giá)
3. Thêm/sửa/xóa biến thể
4. Kiểm tra thông tin cơ bản vẫn còn
5. Kiểm tra Network tab cho API calls

## 📝 Notes

- Variants được quản lý hoàn toàn trong `productVariants` state
- Form chính không biết về variants, tránh conflict
- useCallback giúp tối ưu performance
- API parameters tuân thủ backend specification
- Backward compatible với existing code

## 🚀 Benefits

1. **Stability**: Form không bị reset bất ngờ
2. **UX**: Người dùng không mất dữ liệu đã nhập
3. **Performance**: Ít re-render hơn với useCallback
4. **Compliance**: API calls đúng spec backend
5. **Maintainability**: Code rõ ràng, tách biệt concerns
