# Fix: Product Form Shipment Config Validation Issue

## Vấn đề

Trong form tạo sản phẩm mới ở trang `/business/product`, ph<PERSON><PERSON> "C<PERSON>u hình vận chuyển" không bắt buộc nhưng khi người dùng bấm "Tạo sản phẩm", form lại focus vào phần Cấu hình vận chuyển và không cho phép tạo sản phẩm.

## Nguyên nhân

Schema validation trong Zod đang mong đợi các trường trong `shipmentConfig` là kiểu `number`, nhưng form đang gửi dữ liệu dưới dạng `string` (từ input type="number"). Khi form submit với các trường rỗng, Zod validation sẽ fail vì không thể convert string rỗng thành number.

## Giải pháp

### 1. Sửa `shipmentConfigSchema`

Cập nhật schema để có thể xử lý cả string và number input, và transform string thành number hoặc undefined:

```typescript
export const shipmentConfigSchema = z.object({
  lengthCm: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    })
    .refine((val) => val === undefined || val >= 0, {
      message: 'Chiều dài phải lớn hơn hoặc bằng 0',
    }),
  // Tương tự cho widthCm, heightCm, weightGram
});
```

### 2. Sửa `hasPriceSchema`

Cập nhật schema giá để có thể xử lý string input từ form:

```typescript
export const hasPriceSchema = z.object({
  listPrice: z
    .union([z.string(), z.number()])
    .transform((val) => {
      if (val === '' || val === null || val === undefined) return 0;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? 0 : num;
    })
    .refine((val) => val >= 0, {
      message: 'Giá niêm yết phải lớn hơn hoặc bằng 0',
    }),
  // Tương tự cho salePrice
});
```

## Files đã thay đổi

1. `src/modules/business/schemas/product.schema.ts`
2. `src/modules/admin/business/schemas/product.schema.ts`

## Kết quả

- ✅ Form có thể submit thành công khi phần "Cấu hình vận chuyển" để trống
- ✅ Form vẫn validate đúng khi có giá trị âm
- ✅ Form có thể xử lý cả string và number input
- ✅ Build và lint pass thành công
- ✅ TypeScript check pass

## Test Cases

### Trường hợp 1: Cấu hình vận chuyển để trống
- Input: `{ lengthCm: '', widthCm: '', heightCm: '', weightGram: '' }`
- Expected: Transform thành `{ lengthCm: undefined, widthCm: undefined, heightCm: undefined, weightGram: undefined }`
- Result: ✅ Pass

### Trường hợp 2: Cấu hình vận chuyển có giá trị hợp lệ
- Input: `{ lengthCm: '10', widthCm: '20', heightCm: '30', weightGram: '500' }`
- Expected: Transform thành `{ lengthCm: 10, widthCm: 20, heightCm: 30, weightGram: 500 }`
- Result: ✅ Pass

### Trường hợp 3: Cấu hình vận chuyển có giá trị âm
- Input: `{ lengthCm: '-10', widthCm: '20', heightCm: '30', weightGram: '500' }`
- Expected: Validation error
- Result: ✅ Fail as expected

### Trường hợp 4: Giá sản phẩm để trống
- Input: `{ listPrice: '', salePrice: '', currency: 'VND' }`
- Expected: Transform thành `{ listPrice: 0, salePrice: 0, currency: 'VND' }`
- Result: ✅ Pass

## Lưu ý

- Schema hiện tại sẽ transform string rỗng thành `undefined` cho shipmentConfig và `0` cho price fields
- Validation vẫn đảm bảo không có giá trị âm
- Các trường optional vẫn hoạt động đúng như mong đợi
