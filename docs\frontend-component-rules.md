# Quy tắc phát triển Frontend cho RedAI

## Component bắt buộc

### Typography, Table, Form
**BẮTBUỘC**: Dùng `Typography` thay `<p>`, `<h1>`, `<span>`. Form phải dùng `Form`, `FormItem`, `Input`, `useFormErrors`.

```tsx
// Typography
import { Typography } from '@/shared/components/common';
<Typography variant="h1">Tiêu đề</Typography>

// Table
import { Table } from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
const dataTable = useDataTable(useDataTableConfig({ columns }));
<Table columns={dataTable.columnVisibility.visibleTableColumns} data={data?.items || []} />

// Form
import { Form, FormItem, Input } from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
const { formRef, setFormErrors } = useFormErrors<FormValues>();
<Form ref={formRef} onSubmit={handleSubmit}>
  <FormItem label="Email" name="email" required>
    <Input type="email" value={email} onChange={handleChange} />
  </FormItem>
</Form>

// Other components
import { SlideInForm, ResponsiveGrid } from '@/shared/components/common';
import { MenuIconBar } from '@/modules/components/menu-bar';
import { ActiveFilters } from '@/modules/components/filters';
```

## Layout & Styling & TypeScript

### Layout Rules
- **Không tiêu đề trang**, **Fullwidth** (`className="w-full"`), **Theme** (`bg-background text-foreground`)
- **I18n**: `useTranslation` với colon syntax: `t('user:title')` ✅, `t('user.title')` ❌
- **Responsive**: Dùng `ResponsiveGrid` thay CSS Grid

```tsx
const { t } = useTranslation(['common', 'user']);
<ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
  {items.map(item => <Card key={item.id}>{item.content}</Card>)}
</ResponsiveGrid>
```

### TypeScript & ESLint
**BẮTBUỘC**: Cấm `any` type, `npm run lint` pass trước commit.

```tsx
// ✅ Đúng
interface UserData { id: string; name: string; }
const handleSubmit = (data: UserData) => {};

// ❌ Sai
const handleSubmit = (data: any) => {};
```

## Export Index.ts & API Pattern

### Export thứ tự chuẩn
```typescript
// 1. Types & constants
export * from './types';
export { MODULE_QUERY_KEYS } from './constants';

// 2. API & services
export * from './api';
export * from './services';

// 3. Hooks & components
export * from './hooks';
export { default as ComponentName } from './components/ComponentName';
```

### API 3-Layer Pattern
```typescript
// api/user.api.ts - Raw API
export const getUsers = async (params?: UserQueryDto) =>
  apiClient.get('/user/users', { params });

// services/user.service.ts - Business logic
export const getUsersWithBusinessLogic = async (params?: UserQueryDto) => {
  const defaultParams = { page: 1, limit: 10, ...params };
  return getUsers(defaultParams);
};

// hooks/useUser.ts - React Query
export const useUsers = (params?: UserQueryDto) =>
  useQuery({
    queryKey: USER_QUERY_KEYS.LIST(params || {}),
    queryFn: () => getUsersWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000,
  });

// Query Keys
export const USER_QUERY_KEYS = {
  ALL: ['users'] as const,
  LIST: (params: UserQueryDto) => [...USER_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: string) => [...USER_QUERY_KEYS.ALL, 'detail', id] as const,
};
```

### Import Paths
```tsx
// ✅ Đúng
import { apiClient } from '@/shared/api/axios';
import { useQuery, useMutation } from '@/shared/api';
import { USER_QUERY_KEYS } from '@/modules/user';

// ❌ Sai
import { useQuery } from '@tanstack/react-query';
```

## Mẫu trang chuẩn

```tsx
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, Card } from '@/shared/components/common';
import { MenuIconBar } from '@/modules/components/menu-bar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

const UserListPage: React.FC = () => {
  const { t } = useTranslation(['common', 'user']);
  const columns = useMemo(() => [
    { title: t('user:id'), dataIndex: 'id', sortable: true },
    { title: t('user:name'), dataIndex: 'name' },
  ], [t]);

  const dataTable = useDataTable(useDataTableConfig({ columns }));
  const { data, isLoading } = useUsers(dataTable.queryParams);

  return (
    <div>
      <MenuIconBar onSearch={dataTable.tableData.handleSearch} onAdd={() => {}} />
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.items || []}
          loading={isLoading}
          pagination={dataTable.pagination}
        />
      </Card>
    </div>
  );
};
```

### Module Index.ts Pattern
```typescript
// 1. Types & constants
export * from './types';
export { MODULE_QUERY_KEYS } from './constants';

// 2. Schemas (nếu có)
export * from './schemas';

// 3. API & services
export * from './api';
export * from './services';

// 4. Hooks
export * from './hooks';

// 5. Components & pages (default export)
export { default as ComponentName } from './components/ComponentName';
export { default as PageName } from './pages/PageName';

// 6. Routes
export { default as moduleRoutes } from './routers/moduleRoutes';

// Tránh xung đột tên - dùng alias
export type { AdminUser, AdminUserListResponse } from './types/admin.types';
export { UserSchema, ApiResponseSchema as UserApiResponseSchema } from './schemas/user.schema';
```

## Advanced Patterns (Đã có ở trên - tham khảo API 3-Layer Pattern)

### Naming Conventions
```typescript
// API: verb + noun
export const getUsers = async () => {};
export const createUser = async () => {};

// Services: verb + noun + WithBusinessLogic
export const getUsersWithBusinessLogic = async () => {};
export const createUserWithValidation = async () => {};

// Hooks: use + verb + noun
export const useUsers = () => {};
export const useCreateUser = () => {};

// Query keys: UPPERCASE_WITH_UNDERSCORE
export const USER_QUERY_KEYS = {
  ALL: ['users'] as const,
  LIST: (params) => [...] as const,
  DETAIL: (id) => [...] as const,
};
```

## ESLint Compliance và TypeScript

### 1. Bắt buộc tuân thủ ESLint
**BẮTBUỘC**: Tất cả code phải pass ESLint rules. Chạy `npm run lint` trước khi commit.

```bash
# Kiểm tra ESLint
npm run lint

# Tự động fix các lỗi có thể sửa được
npm run lint:fix

# Kiểm tra TypeScript types
npm run type-check
```

### 2. Cấm sử dụng 'any' type
**BẮTBUỘC**: Không được sử dụng `any` type. Phải định nghĩa type cụ thể.

```typescript
// ❌ Sai - sử dụng any
const handleSubmit = (data: any) => {
  console.log(data.name); // Không type-safe
};

const apiResponse: any = await fetchData();
const users: any[] = apiResponse.result;

// ✅ Đúng - định nghĩa type cụ thể
interface FormData {
  name: string;
  email: string;
  age: number;
}

const handleSubmit = (data: FormData) => {
  console.log(data.name); // Type-safe
};

interface ApiResponse<T> {
  code: number;
  message: string;
  result: T;
}

const apiResponse: ApiResponse<UserDto[]> = await fetchData();
const users: UserDto[] = apiResponse.result;
```

### 3. Proper Type Definitions
```typescript
// ✅ Đúng - Interface cho props
interface UserCardProps {
  user: UserDto;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  className?: string;
}

// ✅ Đúng - Generic types
interface ApiService<T, K = string> {
  get: (id: K) => Promise<ApiResponse<T>>;
  create: (data: Omit<T, 'id'>) => Promise<ApiResponse<T>>;
  update: (id: K, data: Partial<T>) => Promise<ApiResponse<T>>;
  delete: (id: K) => Promise<ApiResponse<void>>;
}

// ✅ Đúng - Union types thay vì any
type Status = 'loading' | 'success' | 'error' | 'idle';
type Theme = 'light' | 'dark' | 'auto';

// ✅ Đúng - Utility types
type CreateUserDto = Omit<UserDto, 'id' | 'createdAt' | 'updatedAt'>;
type UpdateUserDto = Partial<Pick<UserDto, 'name' | 'email' | 'avatar'>>;
```

### 4. Event Handlers Type Safety
```typescript
// ❌ Sai - any event
const handleClick = (event: any) => {
  event.preventDefault();
};

const handleChange = (event: any) => {
  setValue(event.target.value);
};

// ✅ Đúng - specific event types
const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
  event.preventDefault();
};

const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  setValue(event.target.value);
};

const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
  event.preventDefault();
  // Handle form submission
};
```

### 5. API Response Types
```typescript
// ❌ Sai - any response
const fetchUsers = async (): Promise<any> => {
  const response = await apiClient.get('/users');
  return response.data;
};

// ✅ Đúng - typed response
interface UserListResponse {
  items: UserDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

const fetchUsers = async (params?: UserQueryDto): Promise<ApiResponse<UserListResponse>> => {
  return apiClient.get<UserListResponse>('/users', { params });
};
```

### 6. Hook Return Types
```typescript
// ❌ Sai - any return
const useUserData = (id: string): any => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  return { user, loading, setUser };
};

// ✅ Đúng - typed return
interface UseUserDataReturn {
  user: UserDto | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

const useUserData = (id: string): UseUserDataReturn => {
  const [user, setUser] = useState<UserDto | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const refetch = useCallback(() => {
    // Refetch logic
  }, [id]);

  return { user, loading, error, refetch };
};
```

### 7. Component Props với Generic
```typescript
// ✅ Đúng - Generic component
interface TableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  onRowClick?: (row: T) => void;
  loading?: boolean;
}

function Table<T extends Record<string, unknown>>({
  data,
  columns,
  onRowClick,
  loading = false,
}: TableProps<T>): JSX.Element {
  return (
    <div>
      {data.map((row, index) => (
        <div key={index} onClick={() => onRowClick?.(row)}>
          {/* Render row */}
        </div>
      ))}
    </div>
  );
}

// Sử dụng
<Table<UserDto>
  data={users}
  columns={userColumns}
  onRowClick={(user) => console.log(user.name)} // Type-safe
/>
```

### 8. Async/Await Error Handling
```typescript
// ❌ Sai - any error
const fetchData = async () => {
  try {
    const response = await apiClient.get('/data');
    return response.data;
  } catch (error: any) {
    console.error(error.message);
    throw error;
  }
};

// ✅ Đúng - typed error handling
interface ApiError {
  code: number;
  message: string;
  details?: string;
}

const fetchData = async (): Promise<DataDto[]> => {
  try {
    const response = await apiClient.get<DataDto[]>('/data');
    return response.result;
  } catch (error) {
    if (error instanceof Error) {
      console.error('Network error:', error.message);
    } else {
      console.error('Unknown error:', error);
    }
    throw error;
  }
};
```

### 9. Form Validation Types
```typescript
// ✅ Đúng - Form validation với types
interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

const LoginForm: React.FC = () => {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const validateForm = (data: LoginFormData): FormErrors => {
    const newErrors: FormErrors = {};

    if (!data.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(data.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!data.password) {
      newErrors.password = 'Password is required';
    } else if (data.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    return newErrors;
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const validationErrors = validateForm(formData);

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Submit form
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
};
```

### 10. ESLint Rules Checklist
**Bắt buộc tuân thủ các rules sau:**

```typescript
// ✅ No unused variables
const usedVariable = 'value';
console.log(usedVariable);

// ✅ No unused imports
import { useState, useEffect } from 'react'; // Cả hai đều được sử dụng

// ✅ Consistent naming
const userName = 'john'; // camelCase
const USER_CONSTANT = 'ADMIN'; // UPPER_CASE for constants
const ComponentName = () => {}; // PascalCase for components

// ✅ Proper dependency arrays
useEffect(() => {
  fetchData(userId);
}, [userId]); // Include all dependencies

// ✅ No console.log in production
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info');
}

// ✅ Proper async/await
const fetchData = async (): Promise<void> => {
  try {
    await apiCall();
  } catch (error) {
    handleError(error);
  }
};

// ✅ Accessibility rules
<button
  type="button"
  aria-label="Close dialog"
  onClick={handleClose}
>
  <Icon name="close" />
</button>

// ✅ React hooks rules
const MyComponent: React.FC = () => {
  // Hooks always at top level
  const [state, setState] = useState<string>('');
  const data = useQuery(/* ... */);

  // No hooks in conditions
  if (condition) {
    // ❌ Don't put hooks here
  }

  return <div>{state}</div>;
};
```

### 11. Type Assertion Guidelines
```typescript
// ❌ Sai - type assertion với any
const element = document.getElementById('myId') as any;
const data = response.data as any;

// ✅ Đúng - specific type assertion
const element = document.getElementById('myId') as HTMLInputElement;
const data = response.data as UserDto[];

// ✅ Đúng - type guards
function isUserDto(obj: unknown): obj is UserDto {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    'email' in obj
  );
}

if (isUserDto(data)) {
  // data is now typed as UserDto
  console.log(data.name);
}
```

### 12. Pre-commit Checklist
**Trước khi commit, đảm bảo:**

```bash
# 1. ESLint pass
npm run lint
# Kết quả: ✓ No ESLint errors

# 2. TypeScript compilation
npm run type-check
# Kết quả: ✓ No TypeScript errors

# 3. Tests pass
npm run test
# Kết quả: ✓ All tests passing

# 4. Build successful
npm run build
# Kết quả: ✓ Build completed successfully
```

**Nếu có lỗi ESLint:**
1. Đọc error message cẩn thận
2. Fix theo đúng rule
3. Không sử dụng `// eslint-disable` trừ khi thực sự cần thiết
4. Không commit code có ESLint errors

## Form Components và Validation

### 1. Bắt buộc sử dụng Form Components
**BẮTBUỘC**: Khi làm form phải sử dụng các component: `Form`, `FormItem`, `Input` và hook `useFormErrors`.

```tsx
import { Form, FormItem, Input, Button } from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';

interface LoginFormValues {
  email: string;
  password: string;
}

const LoginForm: React.FC = () => {
  const { formRef, setFormErrors } = useFormErrors<LoginFormValues>();
  const [formData, setFormData] = useState<LoginFormValues>({
    email: '',
    password: '',
  });

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate form
      const errors = validateForm(formData);
      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }

      // Submit form
      await submitLogin(formData);
    } catch (error) {
      // Handle API errors
      if (error instanceof ApiError) {
        setFormErrors({
          email: error.message,
        });
      }
    }
  };

  return (
    <Form ref={formRef} onSubmit={handleSubmit}>
      <FormItem label="Email" name="email" required>
        <Input
          type="email"
          value={formData.email}
          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
          placeholder="Nhập email của bạn"
        />
      </FormItem>

      <FormItem label="Mật khẩu" name="password" required>
        <Input
          type="password"
          value={formData.password}
          onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
          placeholder="Nhập mật khẩu"
        />
      </FormItem>

      <Button type="submit" variant="primary" fullWidth>
        Đăng nhập
      </Button>
    </Form>
  );
};
```

### 2. useFormErrors Hook Pattern
**BẮTBUỘC**: Sử dụng `useFormErrors` hook để quản lý lỗi form.

```tsx
// ✅ Đúng - sử dụng useFormErrors với generic type
const { formRef, setFormErrors } = useFormErrors<LoginFormValues>();

// Set lỗi cho specific field
setFormErrors({
  email: 'Email không hợp lệ',
  password: 'Mật khẩu phải có ít nhất 6 ký tự',
});

// Set lỗi cho một field
setFormErrors({
  email: 'Email đã tồn tại',
});

// Clear tất cả lỗi
setFormErrors({});
```

### 3. Form Validation Pattern
```tsx
interface FormErrors {
  email?: string;
  password?: string;
  confirmPassword?: string;
  general?: string;
}

const validateForm = (data: LoginFormValues): FormErrors => {
  const errors: FormErrors = {};

  // Email validation
  if (!data.email) {
    errors.email = 'Email là bắt buộc';
  } else if (!/\S+@\S+\.\S+/.test(data.email)) {
    errors.email = 'Email không hợp lệ';
  }

  // Password validation
  if (!data.password) {
    errors.password = 'Mật khẩu là bắt buộc';
  } else if (data.password.length < 6) {
    errors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
  }

  return errors;
};
```

### 4. FormItem Props
```tsx
// FormItem với các props chuẩn
<FormItem
  label="Tên đăng nhập"     // Label hiển thị
  name="username"           // Name để mapping với useFormErrors
  required={true}           // Hiển thị dấu * bắt buộc
  help="Tên đăng nhập phải có ít nhất 3 ký tự" // Text hướng dẫn
  className="mb-4"          // Custom styling
>
  <Input
    type="text"
    value={formData.username}
    onChange={handleChange}
    placeholder="Nhập tên đăng nhập"
  />
</FormItem>
```

### 5. Input Component Types
```tsx
// Text input
<Input
  type="text"
  value={value}
  onChange={onChange}
  placeholder="Placeholder text"
  disabled={false}
  readOnly={false}
/>

// Email input
<Input
  type="email"
  value={email}
  onChange={handleEmailChange}
  placeholder="<EMAIL>"
/>

// Password input
<Input
  type="password"
  value={password}
  onChange={handlePasswordChange}
  placeholder="Nhập mật khẩu"
  showPasswordToggle={true}
/>

// Number input
<Input
  type="number"
  value={age}
  onChange={handleAgeChange}
  min={0}
  max={100}
/>
```

### 6. Complex Form Example
```tsx
interface RegisterFormValues {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  agreeToTerms: boolean;
}

const RegisterForm: React.FC = () => {
  const { formRef, setFormErrors } = useFormErrors<RegisterFormValues>();
  const [formData, setFormData] = useState<RegisterFormValues>({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    agreeToTerms: false,
  });

  const validateForm = (data: RegisterFormValues): Partial<RegisterFormValues> => {
    const errors: Partial<RegisterFormValues> = {};

    if (!data.email) {
      errors.email = 'Email là bắt buộc';
    }

    if (!data.password) {
      errors.password = 'Mật khẩu là bắt buộc';
    } else if (data.password.length < 8) {
      errors.password = 'Mật khẩu phải có ít nhất 8 ký tự';
    }

    if (data.password !== data.confirmPassword) {
      errors.confirmPassword = 'Mật khẩu xác nhận không khớp';
    }

    if (!data.firstName) {
      errors.firstName = 'Họ là bắt buộc';
    }

    if (!data.lastName) {
      errors.lastName = 'Tên là bắt buộc';
    }

    return errors;
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Client-side validation
    const errors = validateForm(formData);
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      await registerUser(formData);
      // Handle success
    } catch (error) {
      // Handle API errors
      if (error instanceof ApiError) {
        setFormErrors({
          email: error.message,
        });
      }
    }
  };

  const handleInputChange = (field: keyof RegisterFormValues) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.type === 'checkbox'
        ? event.target.checked
        : event.target.value;

      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    };

  return (
    <Form ref={formRef} onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormItem label="Họ" name="firstName" required>
          <Input
            type="text"
            value={formData.firstName}
            onChange={handleInputChange('firstName')}
            placeholder="Nhập họ"
          />
        </FormItem>

        <FormItem label="Tên" name="lastName" required>
          <Input
            type="text"
            value={formData.lastName}
            onChange={handleInputChange('lastName')}
            placeholder="Nhập tên"
          />
        </FormItem>
      </div>

      <FormItem label="Email" name="email" required>
        <Input
          type="email"
          value={formData.email}
          onChange={handleInputChange('email')}
          placeholder="<EMAIL>"
        />
      </FormItem>

      <FormItem label="Số điện thoại" name="phoneNumber">
        <Input
          type="tel"
          value={formData.phoneNumber}
          onChange={handleInputChange('phoneNumber')}
          placeholder="0123456789"
        />
      </FormItem>

      <FormItem label="Mật khẩu" name="password" required>
        <Input
          type="password"
          value={formData.password}
          onChange={handleInputChange('password')}
          placeholder="Nhập mật khẩu"
          showPasswordToggle
        />
      </FormItem>

      <FormItem label="Xác nhận mật khẩu" name="confirmPassword" required>
        <Input
          type="password"
          value={formData.confirmPassword}
          onChange={handleInputChange('confirmPassword')}
          placeholder="Nhập lại mật khẩu"
        />
      </FormItem>

      <FormItem name="agreeToTerms">
        <Checkbox
          checked={formData.agreeToTerms}
          onChange={handleInputChange('agreeToTerms')}
        >
          Tôi đồng ý với <Link href="/terms">Điều khoản sử dụng</Link>
        </Checkbox>
      </FormItem>

      <Button
        type="submit"
        variant="primary"
        fullWidth
        disabled={!formData.agreeToTerms}
      >
        Đăng ký
      </Button>
    </Form>
  );
};
```

### 7. API Error Handling trong Form
```tsx
const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
  event.preventDefault();

  try {
    await submitForm(formData);
  } catch (error) {
    if (error instanceof ApiError) {
      // Map API errors to form fields
      const apiErrors: Partial<FormValues> = {};

      if (error.code === 'EMAIL_EXISTS') {
        apiErrors.email = 'Email này đã được sử dụng';
      } else if (error.code === 'WEAK_PASSWORD') {
        apiErrors.password = 'Mật khẩu quá yếu';
      } else {
        // General error
        apiErrors.general = error.message;
      }

      setFormErrors(apiErrors);
    }
  }
};
```

### 8. Form Import Paths
**BẮTBUỘC**: Sử dụng import paths chuẩn cho form components.

```tsx
// ✅ Đúng
import { Form, FormItem, Input, Button, Checkbox } from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';

// ❌ Sai
import Form from '../../../shared/components/common/Form/Form';
import FormItem from '../../../shared/components/common/FormItem/FormItem';
```

### 9. Form TypeScript Types
```tsx
// Form values interface
interface FormValues {
  [key: string]: string | number | boolean | Date | null | undefined;
}

// Form errors type
type FormErrors<T> = Partial<Record<keyof T, string>>;

// Form component props
interface FormProps {
  onSubmit: (event: React.FormEvent<HTMLFormElement>) => void;
  children: React.ReactNode;
  className?: string;
}

// FormItem props
interface FormItemProps {
  label?: string;
  name: string;
  required?: boolean;
  help?: string;
  children: React.ReactNode;
  className?: string;
}
```
