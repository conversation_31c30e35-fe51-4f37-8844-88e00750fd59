# Quy Tắc <PERSON>t <PERSON>n Frontend Cho RedAI

## 1. Component B<PERSON>t <PERSON>

- **Typography**: Dùng `Typography` thay `<p>`, `<h1>`, `<span>`.
  ```tsx
  import { Typography } from '@/shared/components/common';
  <Typography variant="h1">Tiêu đ<PERSON></Typography>
  ```
- **Table**: Dùng `Table` với `useDataTable` và `useDataTableConfig`.
  ```tsx
  import { Table } from '@/shared/components/common';
  import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
  const dataTable = useDataTable(useDataTableConfig({ columns }));
  <Table columns={dataTable.columnVisibility.visibleTableColumns} data={data?.items || []} />
  ```
- **Form**: Dùng `Form`, `FormItem`, `Input`, và `useFormErrors`.
  ```tsx
  import { Form, FormItem, Input } from '@/shared/components/common';
  import { useFormErrors } from '@/shared/hooks/form';
  const { formRef, setFormErrors } = useFormErrors<FormValues>();
  <Form ref={formRef} onSubmit={handleSubmit}>
    <FormItem label="Email" name="email" required>
      <Input type="email" value={email} onChange={handleChange} />
    </FormItem>
  </Form>
  ```

## 2. Layout & Styling

- **Layout**: Không tiêu đề trang, fullwidth (`className="w-full"`), theme (`bg-background text-foreground`).
- **I18n**: Dùng `useTranslation` với cú pháp colon (`t('user:title')` ✅, `t('user.title')` ❌).
- **Responsive**: Dùng `ResponsiveGrid` thay CSS Grid.
  ```tsx
  import { ResponsiveGrid } from '@/shared/components/common';
  <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
    {items.map(item => <Card key={item.id}>{item.content}</Card>)}
  </ResponsiveGrid>
  ```

## 3. TypeScript & ESLint

- **TypeScript**: Cấm `any`, dùng `interface` cho props, `type` cho union.
  ```tsx
  interface UserData { id: string; name: string; }
  const handleSubmit = (data: UserData) => {};
  ```
- **ESLint**: Code phải pass `npm run lint` trước commit.

## 4. Export & API Pattern

### Export Index.ts
```typescript
export * from './types';
export { MODULE_QUERY_KEYS } from './constants';
export * from './api';
export * from './services';
export * from './hooks';
export { default as ComponentName } from './components/ComponentName';
```

### API 3-Layer Pattern
- **API Layer**: Gọi API thô.
  ```typescript
  export const getUsers = async (params?: UserQueryDto) => apiClient.get('/user/users', { params });
  ```
- **Services Layer**: Xử lý logic nghiệp vụ.
  ```typescript
  export const getUsersWithBusinessLogic = async (params?: UserQueryDto) => {
    const defaultParams = { page: 1, limit: 10, ...params };
    if (defaultParams.limit > 100) throw new Error('Limit cannot exceed 100');
    return getUsers(defaultParams);
  };
  ```
- **Hooks Layer**: Dùng TanStack Query.
  ```typescript
  export const useUsers = (params?: UserQueryDto) =>
    useQuery({
      queryKey: USER_QUERY_KEYS.LIST(params || {}),
      queryFn: () => getUsersWithBusinessLogic(params),
      staleTime: 5 * 60 * 1000,
    });
  ```
- **Query Keys**:
  ```typescript
  export const USER_QUERY_KEYS = {
    ALL: ['users'] as const,
    LIST: (params: UserQueryDto) => [...USER_QUERY_KEYS.ALL, 'list', params] as const,
  };
  ```

## 5. Mẫu Trang Chuẩn
```tsx
import { Table, Card } from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useUsers } from '@/modules/user';

const UserListPage: React.FC = () => {
  const { t } = useTranslation(['common', 'user']);
  const columns = useMemo(() => [
    { title: t('user:id'), dataIndex: 'id', sortable: true },
    { title: t('user:name'), dataIndex: 'name' },
  ], [t]);

  const dataTable = useDataTable(useDataTableConfig({ columns }));
  const { data, isLoading } = useUsers(dataTable.queryParams);

  return (
    <div className="w-full bg-background text-foreground">
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.items || []}
          loading={isLoading}
          pagination={dataTable.pagination}
        />
      </Card>
    </div>
  );
};

export default UserListPage;
```

## 6. Form & Validation
- **Form**: Dùng `Form`, `FormItem`, `Input`, `useFormErrors`.
  ```tsx
  import { Form, FormItem, Input, Button } from '@/shared/components/common';
  import { useFormErrors } from '@/shared/hooks/form';

  interface FormValues { email: string; password: string; }

  const LoginForm: React.FC = () => {
    const { formRef, setFormErrors } = useFormErrors<FormValues>();
    const [formData, setFormData] = useState<FormValues>({ email: '', password: '' });

    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
      event.preventDefault();
      const errors: Partial<FormValues> = {};
      if (!formData.email.includes('@')) errors.email = 'Email không hợp lệ';
      if (formData.password.length < 8) errors.password = 'Mật khẩu phải có ít nhất 8 ký tự';
      if (Object.keys(errors).length) setFormErrors(errors);
    };

    return (
      <Form ref={formRef} onSubmit={handleSubmit}>
        <FormItem label="Email" name="email" required>
          <Input
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          />
        </FormItem>
        <FormItem label="Mật khẩu" name="password" required>
          <Input
            type="password"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
          />
        </FormItem>
        <Button type="submit" variant="primary">Đăng nhập</Button>
      </Form>
    );
  };
  ```

## 7. ESLint & TypeScript
- **ESLint**: Pass `npm run lint` trước commit.
- **TypeScript**: Cấm `any`, dùng type cụ thể.
  ```typescript
  interface User { id: string; name: string; }
  const getUser = (user: User) => user.id;
  ```