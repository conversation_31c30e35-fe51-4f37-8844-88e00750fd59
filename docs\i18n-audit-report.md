# i18n Audit Report - Admin Data Module

Generated on: 2025-05-29T08:35:20.479Z

## 📊 Summary

- **Total Files Scanned**: 46
- **Files with Translations**: 13
- **Files with Hardcoded Text**: 19
- **Total Translation Calls**: 166
- **Total Hardcoded Strings**: 171
- **Total Issues**: 4

## 🔍 Namespace Usage

- **admin**: 8 files
- **common**: 7 files
- **data**: 4 files

## ⚠️ Issues Found

1. **missing_use_translation**: Translation calls found but no useTranslation hook
2. **missing_use_translation**: Translation calls found but no useTranslation hook
3. **missing_use_translation**: Translation calls found but no useTranslation hook
4. **missing_use_translation**: Translation calls found but no useTranslation hook

## 📁 File Details

### src\modules\admin\data\components\filters\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\components\filters\MediaFilter.tsx

**useTranslation Calls**: 1
- Line 24: `useTranslation(['admin', 'common'])` (namespaces: admin, common)

**Translation Calls**: 7
- Line 63: `common:all` (fallback: "Tất cả")
- Line 64: `admin:data.media.status.APPROVED` (fallback: "Hoạt động")
- Line 65: `admin:data.media.status.PENDING` (fallback: "Đang xử lý")
- Line 66: `admin:data.media.status.DRAFT` (fallback: "Nháp")
- Line 74: `common:search` (fallback: "Tìm kiếm")
... and 2 more

**Hardcoded Strings**: 3
- Line 70: "mb-4 p-4"
- Line 71: "grid grid-cols-1 md:grid-cols-3 gap-4"
- Line 99: "flex items-center"


**Issues**: 0


### src\modules\admin\data\components\forms\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\components\forms\KnowledgeFileCreateForm.tsx

**useTranslation Calls**: 1
- Line 34: `useTranslation(['admin', 'data'])` (namespaces: admin, data)

**Translation Calls**: 5
- Line 76: `data:knowledgeFiles.uploadFiles` (fallback: "Upload Knowledge Files")
- Line 124: `data:knowledgeFiles.selectedFiles` (fallback: "Selected Files")
- Line 189: `common:cancel` (fallback: "Cancel")
- Line 198: `data.common.uploading` (fallback: "Uploading...")
- Line 199: `data.common.upload` (fallback: "Upload")


**Hardcoded Strings**: 23
- Line 50: "Please select at least one file to upload"
- Line 73: "p-6 bg-white dark:bg-gray-800"
- Line 75: "mb-4 dark:text-primary-400"
... and 20 more

**Issues**: 0


### src\modules\admin\data\components\forms\KnowledgeFileDetailView.tsx

**useTranslation Calls**: 1
- Line 34: `useTranslation(['admin', 'data', 'common'])` (namespaces: admin, data, common)

**Translation Calls**: 8
- Line 58: `admin:data.knowledgeFiles.viewFile` (fallback: "View Knowledge File")
- Line 68: `admin:data.knowledgeFiles.fileName` (fallback: "File name")
- Line 80: `admin:data.knowledgeFiles.fileSize` (fallback: "Size")
- Line 92: `admin:data.knowledgeFiles.fileType` (fallback: "File type")
- Line 104: `admin:data.knowledgeFiles.vectorStore` (fallback: "Vector Store")
... and 3 more

**Hardcoded Strings**: 17
- Line 55: "p-6 bg-white dark:bg-gray-800"
- Line 57: "mb-4 dark:text-primary-400"
- Line 61: "bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"
... and 14 more

**Issues**: 0


### src\modules\admin\data\components\forms\MediaDetailView.tsx

**useTranslation Calls**: 1
- Line 17: `useTranslation(['admin', 'common'])` (namespaces: admin, common)

**Translation Calls**: 8
- Line 38: `admin:data.media.viewMedia` (fallback: "Chi tiết media")
- Line 55: `admin:data.media.name` (fallback: "Tên file")
- Line 64: `admin:data.media.author` (fallback: "Người tạo")
- Line 73: `admin:data.media.description` (fallback: "Mô tả")
- Line 80: `admin:data.media.storageKey` (fallback: "Khóa lưu trữ")
... and 3 more

**Hardcoded Strings**: 5
- Line 47: "max-w-full h-auto max-h-64 object-contain"
- Line 52: "grid grid-cols-2 gap-4"
- Line 90: "flex flex-wrap gap-2 mt-1"
... and 2 more

**Issues**: 0


### src\modules\admin\data\components\forms\URLForm.tsx

**useTranslation Calls**: 0


**Translation Calls**: 12
- Line 68: `,`
- Line 80: `data:url.form.url`
- Line 92: `data:url.form.title`
- Line 104: `data:url.form.description`
- Line 116: `data:url.form.type`
... and 7 more

**Hardcoded Strings**: 9
- Line 86: "w-full p-2 border rounded"
- Line 98: "w-full p-2 border rounded"
- Line 109: "w-full p-2 border rounded"
... and 6 more

**Issues**: 1
- missing_use_translation: Translation calls found but no useTranslation hook

### src\modules\admin\data\components\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\components\tables\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\components\tables\MediaTable.tsx

**useTranslation Calls**: 1
- Line 47: `useTranslation(['admin', 'common'])` (namespaces: admin, common)

**Translation Calls**: 9
- Line 54: `admin:data.media.table.name` (fallback: "Tên file")
- Line 61: `admin:data.media.table.description` (fallback: "Mô tả")
- Line 67: `admin:data.media.table.size` (fallback: "Kích thước")
- Line 77: `admin:data.media.table.author` (fallback: "Người tạo")
- Line 91: `admin:data.media.table.createdAt` (fallback: "Ngày tạo")
... and 4 more

**Hardcoded Strings**: 2
- Line 82: "flex flex-col"
- Line 84: "text-xs text-gray-500"


**Issues**: 0


### src\modules\admin\data\hooks\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\hooks\useAdminDataCounts.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 3
- Line 73: "Knowledge Files Data:"
- Line 74: "Knowledge Files Meta:"
- Line 75: "Knowledge Files Items:"


**Issues**: 0


### src\modules\admin\data\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\knowledge-files\hooks\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\knowledge-files\hooks\useKnowledgeFile.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 1
- Line 51: "Lỗi khi tạo file tri thức:"


**Issues**: 0


### src\modules\admin\data\knowledge-files\hooks\useVectorStore.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\knowledge-files\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\knowledge-files\schemas\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\knowledge-files\schemas\knowledge-file.schema.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 6
- Line 9: "Tên file không được để trống"
- Line 11: "Dung lượng file phải lớn hơn hoặc bằng 0"
- Line 14: "URL tải xuống không hợp lệ"
... and 3 more

**Issues**: 0


### src\modules\admin\data\knowledge-files\schemas\vector-store.schema.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 4
- Line 9: "Tên vector store không được để trống"
- Line 10: "Dung lượng phải lớn hơn hoặc bằng 0"
- Line 32: "Tên vector store không được để trống"
... and 1 more

**Issues**: 0


### src\modules\admin\data\knowledge-files\services\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\knowledge-files\services\knowledge-file.service.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 3
- Line 40: "Error fetching knowledge files:"
- Line 59: "Error creating knowledge files:"
- Line 81: "`Error deleting knowledge files:`"


**Issues**: 0


### src\modules\admin\data\knowledge-files\services\vector-store.service.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 6
- Line 40: "Error fetching vector stores:"
- Line 57: "Error creating vector store:"
- Line 79: "`Error fetching files for vector store with ID ${vectorStoreId}:`"
... and 3 more

**Issues**: 0


### src\modules\admin\data\knowledge-files\types\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\knowledge-files\types\knowledge-file.types.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\knowledge-files\types\vector-store.types.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\locales\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\media\hooks\useMedia.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\media\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\media\schemas\media.schema.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\media\services\media.service.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\media\types\media.types.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\pages\AdminDataManagementPage.tsx

**useTranslation Calls**: 1
- Line 10: `useTranslation(['data', 'common', 'admin'])` (namespaces: data, common, admin)

**Translation Calls**: 4
- Line 21: `admin:data.media.title` (fallback: "Quản lý Media")
- Line 32: `admin:data.url.title` (fallback: "Quản lý URL")
- Line 43: `admin:data.knowledgeFiles.title` (fallback: "Quản lý File Tri Thức")
- Line 54: `admin:data.vectorStore.title` (fallback: "Quản lý Vector Store")


**Hardcoded Strings**: 4
- Line 24: "Quản lý tập trung các tệp tin media như hình ảnh, video, âm thanh và tài liệu trong hệ thống."
- Line 35: "Quản lý các URL và nội dung được crawl từ các trang web để sử dụng trong hệ thống."
- Line 46: "Quản lý các tệp tin tri thức được sử dụng cho AI và vector store."
... and 1 more

**Issues**: 0


### src\modules\admin\data\pages\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\pages\KnowledgeFilesPage.tsx

**useTranslation Calls**: 1
- Line 48: `useTranslation(['data'])` (namespaces: data)

**Translation Calls**: 19
- Line 92: `data:knowledgeFiles.fileName` (fallback: "File name")
- Line 99: `data:knowledgeFiles.fileType` (fallback: "Type")
- Line 111: `data:knowledgeFiles.fileSize` (fallback: "Size")
- Line 119: `data:knowledgeFiles.vectorStore` (fallback: "Vector Store")
- Line 126: `data:knowledgeFiles.createdAt` (fallback: "Created at")
... and 14 more

**Hardcoded Strings**: 10
- Line 104: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
- Line 285: "Error deleting file:"
- Line 299: "Please select at least one file to delete"
... and 7 more

**Issues**: 0


### src\modules\admin\data\pages\MediaPage.tsx

**useTranslation Calls**: 1
- Line 33: `useTranslation(['admin', 'common'])` (namespaces: admin, common)

**Translation Calls**: 26
- Line 80: `admin:data.media.table.name` (fallback: "Tên")
- Line 91: `admin:data.media.table.description` (fallback: "Mô tả")
- Line 97: `admin:data.media.table.size` (fallback: "Kích thước")
- Line 115: `admin:data.media.table.author` (fallback: "Người tạo")
- Line 126: `admin:data.media.table.createdAt` (fallback: "Ngày tạo")
... and 21 more

**Hardcoded Strings**: 11
- Line 84: "flex items-center"
- Line 118: "flex flex-col"
- Line 120: "text-xs text-gray-500"
... and 8 more

**Issues**: 0


### src\modules\admin\data\pages\URLPage.tsx

**useTranslation Calls**: 1
- Line 37: `useTranslation(['admin', 'common'])` (namespaces: admin, common)

**Translation Calls**: 24
- Line 313: `admin:data.url.table.url` (fallback: "URL")
- Line 318: `common.copy` (fallback: "Sao chép")
- Line 333: `admin:data.url.table.title` (fallback: "Tiêu đề")
- Line 340: `admin:data.url.table.type` (fallback: "Loại")
- Line 352: `admin:data.url.table.tags` (fallback: "Tags")
... and 19 more

**Hardcoded Strings**: 24
- Line 128: "Error creating URL:"
- Line 156: "Error updating URL:"
- Line 215: "Error deleting URLs:"
... and 21 more

**Issues**: 0


### src\modules\admin\data\pages\VectorStorePage.tsx

**useTranslation Calls**: 0


**Translation Calls**: 37
- Line 62: `admin.data.vectorStore.nameRequired` (fallback: "Vector store name is required")
- Line 73: `admin.data.vectorStore.viewVectorStore` (fallback: "View Vector Store")
- Line 74: `admin.data.vectorStore.createVectorStore` (fallback: "Create New Vector Store")
- Line 87: `admin.data.vectorStore.namePlaceholder` (fallback: "Enter vector store name")
- Line 95: `admin.data.vectorStore.nameHint` (fallback: "Choose a descriptive name for your vector store. This will help you identify it later.")
... and 32 more

**Hardcoded Strings**: 25
- Line 70: "space-y-4 p-6 bg-white dark:bg-gray-800"
- Line 71: "mb-4  dark:text-primary-400"
- Line 91: "mt-1 text-sm text-red-500"
... and 22 more

**Issues**: 1
- missing_use_translation: Translation calls found but no useTranslation hook

### src\modules\admin\data\routers\adminDataRoutes.tsx

**useTranslation Calls**: 0


**Translation Calls**: 5
- Line 6: `../pages/AdminDataManagementPage`
- Line 7: `@/modules/admin/data/pages/MediaPage`
- Line 8: `@/modules/admin/data/pages/URLPage`
- Line 9: `@/modules/admin/data/pages/KnowledgeFilesPage`
- Line 10: `@/modules/admin/data/pages/VectorStorePage`


**Hardcoded Strings**: 5
- Line 16: "Admin Data Management"
- Line 26: "Admin Media Management"
- Line 36: "Admin URL Management"
... and 2 more

**Issues**: 1
- missing_use_translation: Translation calls found but no useTranslation hook

### src\modules\admin\data\url\hooks\useUrlQuery.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\url\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\url\schemas\url.schema.ts

**useTranslation Calls**: 0


**Translation Calls**: 2
- Line 51: `createdAt`
- Line 52: `DESC`


**Hardcoded Strings**: 10
- Line 8: "URL không hợp lệ"
- Line 9: "Tiêu đề không được để trống"
- Line 10: "Nội dung không được để trống"
... and 7 more

**Issues**: 1
- missing_use_translation: Translation calls found but no useTranslation hook

### src\modules\admin\data\url\services\url.service.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\url\types\url.types.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\utils\formatUtils.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


### src\modules\admin\data\utils\index.ts

**useTranslation Calls**: 0


**Translation Calls**: 0



**Hardcoded Strings**: 0



**Issues**: 0


---
*Generated by i18n-audit tool*
