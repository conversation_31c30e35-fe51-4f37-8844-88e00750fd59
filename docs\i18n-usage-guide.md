# Hướng dẫn sử dụng đa ngôn ngữ (i18n) trong RedAI Frontend

## C<PERSON><PERSON> sử dụng đúng

### 1. <PERSON>hi sử dụng useTranslation với namespace

```typescript
// ✅ ĐÚNG
const { t } = useTranslation('contract');

// Sử dụng dấu : để truy cập key con
{t('contract:types.selectType')}
{t('contract:types.business')}
{t('contract:actions.next')}
{t('contract:businessInfo.title')}
{t('contract:businessInfo.placeholders.companyName')}
```

### 2. <PERSON>hi sử dụng useTranslation không có namespace

```typescript
// ✅ ĐÚNG
const { t } = useTranslation();

// Sử dụng dấu . để truy cập key
{t('contract.types.selectType')}
{t('contract.types.business')}
{t('contract.actions.next')}
{t('contract.businessInfo.title')}
```

### 3. <PERSON><PERSON><PERSON> trúc file translation

```json
// public/locales/vi/contract.json
{
  "types": {
    "selectType": "Chọn loại hợp đồng",
    "selectTypeDescription": "Vui lòng chọn loại hợp đồng phù hợp",
    "business": "Doanh nghiệp",
    "personal": "Cá nhân"
  },
  "actions": {
    "next": "Tiếp tục",
    "previous": "Quay lại",
    "submit": "Gửi"
  },
  "terms": {
    "title": "Điều khoản và điều kiện",
    "content": "Nội dung điều khoản...",
    "accept": "Tôi đồng ý với các điều khoản",
    "mustAccept": "Bạn phải đồng ý với điều khoản để tiếp tục"
  },
  "businessInfo": {
    "title": "Thông tin doanh nghiệp",
    "companyName": "Tên công ty",
    "taxCode": "Mã số thuế",
    "companyEmail": "Email công ty",
    "companyPhone": "Số điện thoại công ty",
    "companyAddress": "Địa chỉ công ty",
    "representative": "Người đại diện",
    "position": "Chức vụ",
    "placeholders": {
      "companyName": "Nhập tên công ty",
      "taxCode": "Nhập mã số thuế",
      "companyEmail": "Nhập email công ty",
      "companyPhone": "Nhập số điện thoại công ty",
      "companyAddress": "Nhập địa chỉ công ty",
      "representative": "Nhập tên người đại diện",
      "position": "Nhập chức vụ"
    }
  },
  "personalInfo": {
    "title": "Thông tin cá nhân",
    "fullName": "Họ và tên",
    "dateOfBirth": "Ngày sinh",
    "idNumber": "Số CCCD/CMND",
    "idIssuedDate": "Ngày cấp",
    "idIssuedPlace": "Nơi cấp",
    "phone": "Số điện thoại",
    "address": "Địa chỉ",
    "taxCode": "Mã số thuế (tùy chọn)",
    "placeholders": {
      "fullName": "Nhập họ và tên",
      "dateOfBirth": "Chọn ngày sinh",
      "idNumber": "Nhập số CCCD/CMND",
      "idIssuedDate": "Chọn ngày cấp",
      "idIssuedPlace": "Nhập nơi cấp",
      "phone": "Nhập số điện thoại",
      "address": "Nhập địa chỉ",
      "taxCode": "Nhập mã số thuế"
    }
  }
}
```

## Quy tắc quan trọng

### ✅ ĐÚNG
- `useTranslation('namespace')` → `t('namespace:key.subkey')`
- `useTranslation()` → `t('namespace.key.subkey')`

### ❌ SAI
- `useTranslation('namespace')` → `t('namespace.key.subkey')` ❌
- Trộn lẫn dấu `:` và `.` ❌

## Ví dụ thực tế

```typescript
// File: ContractTypeSelector.tsx
import { useTranslation } from 'react-i18next';

const ContractTypeSelector = () => {
  const { t } = useTranslation('contract'); // Có namespace

  return (
    <div>
      <h2>{t('contract:types.selectType')}</h2>
      <p>{t('contract:types.selectTypeDescription')}</p>
      <button>{t('contract:types.business')}</button>
      <button>{t('contract:types.personal')}</button>
      <button>{t('contract:actions.next')}</button>
    </div>
  );
};
```

## Lưu ý
- Luôn kiểm tra cú pháp trước khi commit
- Đảm bảo file translation có đầy đủ key
- Sử dụng TypeScript để type-safe translation keys nếu có thể
