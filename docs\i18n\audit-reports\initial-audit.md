# Initial i18n Audit Report - Admin Data Module

**Generated**: 2025-05-29T08:35:20.479Z  
**Scope**: src/modules/admin/data  
**Tool**: tools/i18n-audit.cjs

## 📊 Executive Summary

The initial audit of the admin data module revealed significant i18n inconsistencies that require immediate attention. While the module has basic i18n infrastructure in place, there are critical issues with namespace usage, missing hooks, and extensive hardcoded text.

### Key Metrics
- **Total Files Scanned**: 46
- **Files with Translations**: 13 (28%)
- **Files with Hardcoded Text**: 19 (41%)
- **Total Translation Calls**: 166
- **Total Hardcoded Strings**: 171
- **Critical Issues**: 4

## ⚠️ Critical Issues Requiring Immediate Fix

### 1. Missing useTranslation Hooks
**Impact**: High - Translation calls will fail at runtime

| File | Issue | Translation Calls |
|------|-------|------------------|
| URLForm.tsx | Missing useTranslation hook | 12 calls |
| VectorStorePage.tsx | Missing useTranslation hook | 37 calls |
| adminDataRoutes.tsx | Missing useTranslation hook | 5 calls |
| url.schema.ts | Missing useTranslation hook | 2 calls |

### 2. Namespace Inconsistencies
**Impact**: Medium - Inconsistent developer experience

| Pattern | Files | Usage |
|---------|-------|-------|
| `['admin', 'common']` | 8 | Most common |
| `['data', 'common', 'admin']` | 1 | AdminDataManagementPage |
| `['data']` | 1 | KnowledgeFilesPage |
| `['admin', 'data', 'common']` | 1 | KnowledgeFileDetailView |
| `['admin', 'data']` | 1 | KnowledgeFileCreateForm |

## 📁 File-by-File Analysis

### High Priority Files (Pages)

#### AdminDataManagementPage.tsx
- **Status**: ⚠️ Needs standardization
- **useTranslation**: `['data', 'common', 'admin']` (non-standard)
- **Translation Calls**: 4
- **Hardcoded Strings**: 4 (descriptions in ModuleCard)
- **Action Required**: Standardize namespace, translate descriptions

#### MediaPage.tsx
- **Status**: ✅ Good pattern
- **useTranslation**: `['admin', 'common']` (standard)
- **Translation Calls**: 26
- **Hardcoded Strings**: 11 (mostly CSS classes)
- **Action Required**: Minor cleanup of hardcoded text

#### KnowledgeFilesPage.tsx
- **Status**: ⚠️ Needs standardization
- **useTranslation**: `['data']` (non-standard)
- **Translation Calls**: 19
- **Hardcoded Strings**: 10
- **Action Required**: Change to standard namespace pattern

#### URLPage.tsx
- **Status**: ✅ Good pattern
- **useTranslation**: `['admin', 'common']` (standard)
- **Translation Calls**: 24
- **Hardcoded Strings**: 24 (error messages, CSS classes)
- **Action Required**: Translate error messages

#### VectorStorePage.tsx
- **Status**: 🚨 Critical issue
- **useTranslation**: Missing!
- **Translation Calls**: 37
- **Hardcoded Strings**: 25
- **Action Required**: Add useTranslation hook, fix all translations

### Medium Priority Files (Components)

#### Forms Directory
- **KnowledgeFileCreateForm.tsx**: Non-standard namespace `['admin', 'data']`
- **KnowledgeFileDetailView.tsx**: Non-standard namespace `['admin', 'data', 'common']`
- **MediaDetailView.tsx**: Standard pattern ✅
- **URLForm.tsx**: 🚨 Missing useTranslation hook

#### Other Components
- **MediaFilter.tsx**: Standard pattern ✅
- **MediaTable.tsx**: Standard pattern ✅

### Low Priority Files (Infrastructure)

#### Schema Files
- **knowledge-file.schema.ts**: 6 hardcoded validation messages
- **vector-store.schema.ts**: 4 hardcoded validation messages
- **url.schema.ts**: 10 hardcoded validation messages + missing useTranslation

#### Service Files
- **knowledge-file.service.ts**: 3 hardcoded error messages
- **vector-store.service.ts**: 6 hardcoded error messages
- **useKnowledgeFile.ts**: 1 hardcoded error message

## 🎯 Recommended Action Plan

### Phase 1: Critical Fixes (Day 1)
1. **Add missing useTranslation hooks**:
   - VectorStorePage.tsx
   - URLForm.tsx
   - adminDataRoutes.tsx
   - url.schema.ts

2. **Standardize namespace patterns**:
   - Change all to `['admin', 'common']`
   - Update translation keys to use `admin:data.xxx` pattern

### Phase 2: Translation Cleanup (Day 2)
1. **Translate hardcoded UI text**:
   - AdminDataManagementPage descriptions
   - Error messages in pages
   - Validation messages in schemas

2. **Update translation files**:
   - Add missing keys to vi.json, en.json, zh.json
   - Ensure consistency across languages

### Phase 3: Validation (Day 3)
1. **Re-run audit tool**
2. **Test language switching**
3. **Verify no runtime errors**

## 📈 Success Metrics

### Target Goals
- **Critical Issues**: 0 (currently 4)
- **Namespace Consistency**: 100% (currently ~60%)
- **Hardcoded Text**: <10 strings (currently 171)
- **Translation Coverage**: 100% of UI text

### Quality Gates
- [ ] All components use standard namespace pattern
- [ ] No missing useTranslation hooks
- [ ] All UI text is translatable
- [ ] Language switching works without errors
- [ ] Validation messages are localized

## 🔗 Related Documents

- [Namespace Standards](../guidelines/namespace-standards.md)
- [Full Audit Report](../../i18n-audit-report.md)
- [Task Master Plan](../i18n-admin-data-taskmaster-plan.md)

---

*This report will be updated as fixes are implemented.*
