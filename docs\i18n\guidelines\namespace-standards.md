# Namespace Standards for Admin Data Module

## 🎯 Overview

This document defines the standardized namespace usage patterns for the admin data module i18n implementation.

## 📋 Current Analysis

Based on the audit findings, we have identified inconsistent namespace usage:

### Current Namespace Usage
- **admin**: 8 files
- **common**: 7 files  
- **data**: 4 files

### Issues Identified
1. **Inconsistent patterns**: Some components use `['admin', 'common']`, others use `['data', 'common', 'admin']`
2. **Namespace conflicts**: Both user data module and admin data module use 'data' namespace
3. **Missing useTranslation**: 4 files have translation calls without useTranslation hook

## ✅ Recommended Standard

### Primary Pattern for Admin Data Module
```tsx
const { t } = useTranslation(['admin', 'common']);
```

### Translation Key Pattern
```tsx
// For admin data specific content
t('admin:data.media.title', 'Default fallback')

// For common UI elements
t('common:save', 'Save')
t('common:cancel', 'Cancel')
```

### Namespace Hierarchy
```
admin:
  data:
    media:
      title: "Media Management"
      table:
        name: "File Name"
        size: "Size"
    knowledgeFiles:
      title: "Knowledge Files"
    url:
      title: "URL Management"
    vectorStore:
      title: "Vector Store"

common:
  save: "Save"
  cancel: "Cancel"
  delete: "Delete"
  edit: "Edit"
  view: "View"
```

## 🔧 Implementation Rules

### 1. useTranslation Hook Usage
```tsx
// ✅ Correct - Admin data components
const { t } = useTranslation(['admin', 'common']);

// ❌ Incorrect - Inconsistent namespace order
const { t } = useTranslation(['data', 'common', 'admin']);

// ❌ Incorrect - Missing useTranslation when using t()
// Component uses t() but no useTranslation hook
```

### 2. Translation Key Naming
```tsx
// ✅ Correct - Hierarchical structure
t('admin:data.media.table.name')

// ✅ Correct - With fallback
t('admin:data.media.title', 'Media Management')

// ❌ Incorrect - Flat structure
t('admin:mediaTitle')
```

### 3. Fallback Strategy
```tsx
// ✅ Correct - Always provide meaningful fallbacks
t('admin:data.media.deleteSuccess', 'Media deleted successfully')

// ❌ Incorrect - No fallback
t('admin:data.media.deleteSuccess')
```

## 📁 File-Specific Guidelines

### Pages (src/modules/admin/data/pages/)
- **Primary namespace**: `admin`
- **Secondary namespace**: `common`
- **Pattern**: `useTranslation(['admin', 'common'])`

### Components (src/modules/admin/data/components/)
- **Primary namespace**: `admin`
- **Secondary namespace**: `common`
- **Pattern**: `useTranslation(['admin', 'common'])`

### Forms (src/modules/admin/data/components/forms/)
- **Primary namespace**: `admin`
- **Secondary namespace**: `common`
- **Pattern**: `useTranslation(['admin', 'common'])`
- **Validation**: Use `admin:validation.xxx` for form-specific validation messages

## 🚫 Anti-Patterns to Avoid

### 1. Namespace Conflicts
```tsx
// ❌ Avoid - Conflicts with user data module
const { t } = useTranslation(['data']);
t('data:media.title'); // Conflicts with user module
```

### 2. Inconsistent Patterns
```tsx
// ❌ Avoid - Different patterns in same module
// File 1:
const { t } = useTranslation(['admin', 'common']);

// File 2:
const { t } = useTranslation(['data', 'admin']);
```

### 3. Missing useTranslation
```tsx
// ❌ Avoid - Using t() without useTranslation
function Component() {
  return <div>{t('admin:data.title')}</div>; // Error!
}
```

## 🔄 Migration Strategy

### Phase 1: Fix Critical Issues
1. Add missing useTranslation hooks
2. Standardize namespace arrays to `['admin', 'common']`

### Phase 2: Update Translation Keys
1. Ensure all keys use `admin:data.xxx` pattern
2. Add missing fallback text

### Phase 3: Validation
1. Run audit tool to verify compliance
2. Test language switching functionality

## ✅ Compliance Checklist

- [ ] All components use `useTranslation(['admin', 'common'])`
- [ ] All translation keys follow `admin:data.xxx` pattern
- [ ] All translation calls have meaningful fallbacks
- [ ] No hardcoded text in UI components
- [ ] Validation messages use proper namespace
- [ ] Error messages are translatable

---

*Last updated: 2025-05-29*
