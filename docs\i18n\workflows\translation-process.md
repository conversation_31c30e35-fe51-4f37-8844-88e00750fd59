# Translation Process Guide

## 🎯 Overview

This guide outlines the step-by-step process for adding, updating, and maintaining translations in the admin data module.

## 📋 Prerequisites

- Understanding of [Namespace Standards](../guidelines/namespace-standards.md)
- Access to translation files in `src/modules/admin/data/locales/`
- Basic knowledge of react-i18next

## 🔄 Adding New Translations

### Step 1: Identify Translation Needs
```tsx
// ❌ Before - Hardcoded text
<button>Save Changes</button>

// ✅ After - Translatable
<button>{t('common:save', 'Save')}</button>
```

### Step 2: Add useTranslation Hook
```tsx
import { useTranslation } from 'react-i18next';

const MyComponent: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  
  return (
    <div>
      <h1>{t('admin:data.media.title', 'Media Management')}</h1>
    </div>
  );
};
```

### Step 3: Update Translation Files

#### Vietnamese (vi.json)
```json
{
  "data": {
    "media": {
      "title": "Quản lý Media",
      "description": "Quản lý tập trung các tệp tin media"
    }
  }
}
```

#### English (en.json)
```json
{
  "data": {
    "media": {
      "title": "Media Management",
      "description": "Centralized management of media files"
    }
  }
}
```

#### Chinese (zh.json)
```json
{
  "data": {
    "media": {
      "title": "媒体管理",
      "description": "集中管理媒体文件"
    }
  }
}
```

### Step 4: Test Translations
1. Run the application
2. Switch between languages
3. Verify text displays correctly
4. Check for layout issues with different text lengths

## 🔧 Common Patterns

### 1. Simple Text Translation
```tsx
// Basic usage
{t('admin:data.media.title', 'Media Management')}

// With common namespace
{t('common:save', 'Save')}
```

### 2. Interpolation
```tsx
// Component
{t('admin:data.media.selectedItems', 'Selected {{count}} items', { count: 5 })}

// Translation file
{
  "selectedItems": "Đã chọn {{count}} mục"
}
```

### 3. Pluralization
```tsx
// Component
{t('admin:data.media.fileCount', '{{count}} file', { count: files.length })}

// Translation file (vi.json)
{
  "fileCount": "{{count}} tệp tin",
  "fileCount_plural": "{{count}} tệp tin"
}
```

### 4. Conditional Text
```tsx
// Component
{status === 'active' 
  ? t('common:status.active', 'Active')
  : t('common:status.inactive', 'Inactive')
}
```

### 5. Form Validation
```tsx
// Schema with translations
const schema = useMemo(() => z.object({
  name: z.string().min(1, t('admin:validation.required', 'This field is required'))
}), [t]);
```

## 📁 File Organization

### Translation File Structure
```
src/modules/admin/data/locales/
├── vi.json     # Vietnamese translations
├── en.json     # English translations
├── zh.json     # Chinese translations
└── index.ts    # Export configuration
```

### Key Hierarchy
```json
{
  "data": {
    "title": "System Data Management",
    "media": {
      "title": "Media Management",
      "table": {
        "name": "File Name",
        "size": "Size"
      },
      "messages": {
        "deleteSuccess": "Media deleted successfully"
      }
    },
    "knowledgeFiles": {
      "title": "Knowledge Files",
      "form": {
        "upload": "Upload Files"
      }
    }
  }
}
```

## ✅ Best Practices

### 1. Always Provide Fallbacks
```tsx
// ✅ Good - With fallback
{t('admin:data.media.title', 'Media Management')}

// ❌ Bad - No fallback
{t('admin:data.media.title')}
```

### 2. Use Meaningful Keys
```tsx
// ✅ Good - Descriptive key
{t('admin:data.media.confirmDeleteMessage', 'Are you sure?')}

// ❌ Bad - Generic key
{t('admin:data.media.msg1', 'Are you sure?')}
```

### 3. Group Related Keys
```json
{
  "media": {
    "table": {
      "name": "Name",
      "size": "Size"
    },
    "form": {
      "upload": "Upload",
      "cancel": "Cancel"
    }
  }
}
```

### 4. Consistent Namespace Usage
```tsx
// ✅ Consistent pattern
const { t } = useTranslation(['admin', 'common']);
```

## 🧪 Testing Checklist

### Before Committing
- [ ] All new text uses translation functions
- [ ] Translation keys exist in all language files
- [ ] Fallback text is meaningful
- [ ] Language switching works correctly
- [ ] No console errors related to missing keys
- [ ] UI layout works with different text lengths

### Testing Commands
```bash
# Run audit tool
node tools/i18n-audit.cjs

# Check for missing keys (if available)
npm run i18n:check

# Test build
npm run build
```

## 🚨 Common Issues and Solutions

### Issue 1: Missing useTranslation Hook
```tsx
// ❌ Error
function Component() {
  return <div>{t('key')}</div>; // t is not defined
}

// ✅ Fix
import { useTranslation } from 'react-i18next';

function Component() {
  const { t } = useTranslation(['admin', 'common']);
  return <div>{t('admin:data.title', 'Title')}</div>;
}
```

### Issue 2: Key Not Found
```tsx
// Check translation files for the key
// Ensure key exists in all language files
// Verify namespace is correct
```

### Issue 3: Layout Breaking
```tsx
// Use CSS to handle varying text lengths
.button {
  min-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
```

## 📞 Getting Help

- Check [Troubleshooting Guide](../reference/troubleshooting.md)
- Review [Namespace Standards](../guidelines/namespace-standards.md)
- Run audit tool: `node tools/i18n-audit.cjs`

---

*Last updated: 2025-05-29*
