# Module Marketplace Admin

## Tổng quan

Module Marketplace Admin cung cấp các chức năng quản lý chợ sản phẩm dành cho admin. Module này cho phép admin quản lý sản phẩm, giỏ hàng, đơn hàng và các hoạt động liên quan đến marketplace.

## Thành phần

- **Controllers**: <PERSON><PERSON> lý các request từ client
  - `ProductAdminController`: Quản lý sản phẩm
  - `CartAdminController`: Quản lý giỏ hàng
  - `OrderAdminController`: Quản lý đơn hàng

- **Services**: Xử lý logic nghiệp vụ
  - `ProductAdminService`: Xử lý logic liên quan đến sản phẩm
  - `CartAdminService`: Xử lý logic liên quan đến giỏ hàng
  - `OrderAdminService`: Xử lý logic liên quan đến đơn hàng

- **DTOs**: <PERSON><PERSON><PERSON> tượng truyền dữ liệu
  - Request DTOs: `QueryProductDto`, `UpdateProductStatusDto`, `CartQueryDto`, `OrderQueryDto`...
  - Response DTOs: `ProductResponseDto`, `CartAdminResponseDto`, `OrderResponseDto`...

## API Endpoints

### Quản lý sản phẩm

- `GET /admin/marketplace/products` - Lấy danh sách sản phẩm
- `GET /admin/marketplace/products/:id` - Lấy thông tin chi tiết sản phẩm
- `PUT /admin/marketplace/products/:id/status` - Cập nhật trạng thái sản phẩm
- `PUT /admin/marketplace/products/:id` - Cập nhật thông tin sản phẩm
- `POST /admin/marketplace/products/presigned-urls` - Tạo presigned URLs để upload file

### Quản lý giỏ hàng

- `GET /admin/marketplace/cart/all` - Lấy danh sách giỏ hàng của tất cả người dùng
- `GET /admin/marketplace/cart/:id` - Lấy thông tin chi tiết giỏ hàng

### Quản lý đơn hàng

- `GET /admin/marketplace/orders` - Lấy danh sách đơn hàng
- `GET /admin/marketplace/orders/:id` - Lấy thông tin chi tiết đơn hàng

## Cách sử dụng

### Lấy danh sách sản phẩm

```typescript
// Trong một service khác
import { ProductAdminService } from '@modules/marketplace/admin/services';

@Injectable()
export class SomeService {
  constructor(private readonly productAdminService: ProductAdminService) {}

  async getAllProducts(employeeId: number) {
    const queryDto = new QueryProductDto();
    queryDto.page = 1;
    queryDto.limit = 10;
    queryDto.sortBy = 'createdAt';
    queryDto.sortDirection = 'DESC';
    
    const products = await this.productAdminService.getProducts(employeeId, queryDto);
    return products;
  }
}
```

### Cập nhật trạng thái sản phẩm

```typescript
// Trong một service khác
import { ProductAdminService } from '@modules/marketplace/admin/services';
import { UpdateProductStatusDto } from '@modules/marketplace/admin/dto';
import { ProductStatus } from '@modules/marketplace/enums';

@Injectable()
export class SomeService {
  constructor(private readonly productAdminService: ProductAdminService) {}

  async approveProduct(employeeId: number, productId: number) {
    const updateDto = new UpdateProductStatusDto();
    updateDto.status = ProductStatus.APPROVED;
    
    const product = await this.productAdminService.updateProductStatus(employeeId, productId, updateDto);
    return product;
  }
}
```

## Testing

Module này có các bài test đầy đủ cho controllers và services. Để chạy các bài test:

```bash
# Chạy tất cả tests
npm run test:marketplace-admin

# Chạy test với coverage
npx jest --config=src/modules/marketplace/admin/test/jest.config.ts --coverage
```

## Mã lỗi

| Mã lỗi | Mô tả | HTTP Status |
|--------|-------|-------------|
| 30001 | Sản phẩm không tồn tại | 404 |
| 30002 | Không có quyền truy cập sản phẩm | 403 |
| 30003 | Giỏ hàng không tồn tại | 404 |
| 30004 | Đơn hàng không tồn tại | 404 |
| 30005 | Lỗi khi xử lý sản phẩm | 500 |
| 30006 | Lỗi khi xử lý giỏ hàng | 500 |
| 30007 | Lỗi khi xử lý đơn hàng | 500 |
