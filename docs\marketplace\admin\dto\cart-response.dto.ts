import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin người dùng trong response giỏ hàng
 */
export class CartUserInfoDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Tên người dùng',
    example: 'Nguyễn Văn A',
  })
  name: string;

  @ApiProperty({
    description: 'Email người dùng',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Avatar của người dùng',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;
}

/**
 * DTO cho thông tin sản phẩm trong giỏ hàng
 */
export class CartItemDto {
  @ApiProperty({
    description: 'ID của cart item',
    example: 789,
  })
  id: number;

  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  productId: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Laptop XYZ',
  })
  productName: string;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
  })
  discountedPrice: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 2,
  })
  quantity: number;

  @ApiProperty({
    description: 'Tên người bán',
    example: 'John Doe',
  })
  sellerName: string;

  @ApiProperty({
    description: 'Thời gian thêm vào giỏ hàng',
    example: 1625097600000,
  })
  createdAt: number;
}

/**
 * DTO cho response trả về thông tin giỏ hàng
 */
export class CartAdminResponseDto {
  @ApiProperty({
    description: 'ID của giỏ hàng',
    example: 456,
  })
  id: number;

  @ApiProperty({
    description: 'Thông tin người dùng',
    type: CartUserInfoDto,
  })
  user: CartUserInfoDto;

  @ApiProperty({
    description: 'Danh sách sản phẩm trong giỏ hàng',
    type: [CartItemDto],
  })
  items: CartItemDto[];

  @ApiProperty({
    description: 'Tổng giá trị giỏ hàng',
    example: 2000,
  })
  totalValue: number;

  @ApiProperty({
    description: 'Thời gian tạo giỏ hàng',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật giỏ hàng',
    example: 1625184000000,
  })
  updatedAt: number;
}
