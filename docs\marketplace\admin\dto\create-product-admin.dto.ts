import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsArray, 
  IsEnum, 
  IsNotEmpty, 
  IsNumber, 
  IsOptional, 
  IsString, 
  IsUUID, 
  MaxLength, 
  Min, 
  MinLength 
} from 'class-validator';
import { ProductCategory } from '@modules/marketplace/enums';

/**
 * DTO cho việc tạo sản phẩm mới bởi admin
 */
export class CreateProductAdminDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'AI Chatbot Template',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(500)
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Mẫu chatbot AI hỗ trợ khách hàng tự động',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> niêm yết',
    example: 1200,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  listedPrice: number;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  discountedPrice: number;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductCategory,
    example: ProductCategory.KNOWLEDGE_FILE,
  })
  @IsEnum(ProductCategory)
  category: ProductCategory;

  @ApiProperty({
    description: 'Danh sách MIME types của ảnh sản phẩm',
    type: [String],
    example: ['image/jpeg', 'image/png'],
  })
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes: string[];

  @ApiProperty({
    description: 'MIME type của tài liệu hướng dẫn sử dụng',
    example: 'application/pdf',
    required: false,
  })
  @IsOptional()
  @IsString()
  userManualMediaType?: string;

  @ApiProperty({
    description: 'MIME type của tài liệu chi tiết sản phẩm',
    example: 'application/pdf',
    required: false,
  })
  @IsOptional()
  @IsString()
  detailMediaType?: string;

  @ApiProperty({
    description: 'ID nguồn của sản phẩm (UUID)',
    example: '34f5c7ef-649a-46e2-a399-34fc7c197032',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  sourceId?: string;
}
