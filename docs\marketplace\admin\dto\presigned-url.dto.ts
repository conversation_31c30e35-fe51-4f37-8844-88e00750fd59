import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin URL ký sẵn cho ảnh
 */
export class PresignedUrlImageDto {
  @ApiProperty({
    description: 'Chỉ số ảnh',
    example: 1,
  })
  index: number;

  @ApiProperty({
    description: 'URL ký sẵn để upload',
    example: 'https://hnssbfc.com/key1',
  })
  uploadUrl: string;
}

/**
 * DTO cho các URL ký sẵn trả về khi cập nhật sản phẩm
 */
export class PresignedUrlsDto {
  @ApiProperty({
    description: 'Danh sách URL ký sẵn cho ảnh',
    type: [PresignedUrlImageDto],
  })
  presignedUrlImage: PresignedUrlImageDto[];

  @ApiProperty({
    description: 'URL ký sẵn cho chi tiết sản phẩm',
    example: 'https://hnssbfc.com/key_detail',
    nullable: true,
  })
  presignedUrlDetail: string | null;

  @ApiProperty({
    description: 'URL ký sẵn cho hướng dẫn sử dụng',
    example: 'https://hnssbfc.com/key_manual',
    nullable: true,
  })
  presignedUrlUserManual: string | null;

  @ApiProperty({
    description: 'Thông báo lỗi khi đăng bán sản phẩm (nếu có)',
    example: 'Không thể đăng bán sản phẩm: Thiếu thông tin bắt buộc',
    nullable: true,
    required: false,
  })
  publishError?: string;
}
