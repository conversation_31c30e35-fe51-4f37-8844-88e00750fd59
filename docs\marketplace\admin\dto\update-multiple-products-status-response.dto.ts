import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin lỗi khi cập nhật trạng thái sản phẩm
 */
export class ProductStatusUpdateErrorDto {
  @ApiProperty({
    description: 'ID của sản phẩm gặp lỗi khi cập nhật',
    example: 42,
  })
  id: number;

  @ApiProperty({
    description: 'Lý do lỗi',
    example: 'Sản phẩm không tồn tại',
  })
  reason: string;
}

/**
 * DTO phản hồi cho việc cập nhật trạng thái nhiều sản phẩm
 */
export class UpdateMultipleProductsStatusResponseDto {
  @ApiProperty({
    description: 'Danh sách ID sản phẩm đã cập nhật thành công',
    type: [Number],
    example: [1, 2, 3],
  })
  successIds: number[];

  @ApiProperty({
    description: 'Danh sách sản phẩm gặp lỗi khi cập nhật',
    type: [ProductStatusUpdateErrorDto],
    example: [
      {
        id: 4,
        reason: 'Sản phẩm không tồn tại',
      },
      {
        id: 5,
        reason: 'Không đủ quyền để cập nhật trạng thái sản phẩm',
      },
    ],
  })
  failedIds: ProductStatusUpdateErrorDto[];
}
