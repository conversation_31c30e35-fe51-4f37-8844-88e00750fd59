import { ApiProperty } from '@nestjs/swagger';
import { ProductDetailResponseDto } from './product-detail-response.dto';
import { PresignedUrlImageDto } from './presigned-url.dto';

/**
 * DTO cho response khi cập nhật sản phẩm
 */
export class UpdateProductResponseDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm đã cập nhật',
    type: ProductDetailResponseDto,
  })
  product: ProductDetailResponseDto;

  @ApiProperty({
    description: 'Danh sách URL ký sẵn cho ảnh',
    type: [PresignedUrlImageDto],
  })
  presignedUrlImage: PresignedUrlImageDto[];

  @ApiProperty({
    description: 'URL ký sẵn cho chi tiết sản phẩm',
    example: 'https://example.com/upload/detail.pdf',
    nullable: true,
  })
  presignedUrlDetail: string | null;

  @ApiProperty({
    description: 'URL ký sẵn cho hướng dẫn sử dụng',
    example: 'https://example.com/upload/manual.pdf',
    nullable: true,
  })
  presignedUrlUserManual: string | null;

  @ApiProperty({
    description: 'Thông báo lỗi khi đăng bán sản phẩm (nếu có)',
    example: 'Không thể đăng bán sản phẩm: Thiếu thông tin bắt buộc',
    nullable: true,
    required: false,
  })
  publishError?: string;
}
