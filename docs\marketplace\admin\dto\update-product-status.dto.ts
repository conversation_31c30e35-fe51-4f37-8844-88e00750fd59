import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { ProductStatus } from '@modules/marketplace/enums';

/**
 * DTO cho việc cập nhật trạng thái sản phẩm
 */
export class UpdateProductStatusDto {
  @ApiProperty({
    description: 'Trạng thái mới của sản phẩm',
    enum: ProductStatus,
    example: ProductStatus.APPROVED,
  })
  @IsNotEmpty()
  @IsEnum(ProductStatus)
  status: ProductStatus;
}
