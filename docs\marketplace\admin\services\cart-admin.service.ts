import { Injectable, Logger } from '@nestjs/common';
import { CartRepository } from '@modules/marketplace/repositories';
import { CartHelper } from '@modules/marketplace/helpers';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import {
  CartQueryDto,
  CartAdminResponseDto,
} from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Service xử lý logic liên quan đến giỏ hàng cho admin
 */
@Injectable()
export class CartAdminService {
  private readonly logger = new Logger(CartAdminService.name);

  constructor(
    private readonly cartRepository: CartRepository,
    private readonly cartHelper: CartHelper
  ) {}

  /**
   * Lấy danh sách giỏ hàng của tất cả người dùng với phân trang, tì<PERSON> kiếm, lọ<PERSON> và sắp xếp
   * @param employeeId ID của nhân viên
   * @param queryDto Tham số truy vấn
   * @returns Danh sách giỏ hàng với phân trang
   */
  async getCarts(
    _employeeId: number,
    queryDto: CartQueryDto
  ): Promise<PaginatedResult<CartAdminResponseDto>> {
    try {
      // Lấy danh sách giỏ hàng từ repository
      const cartsResult = await this.cartRepository.findAdminCarts(queryDto);

      // Nếu không có giỏ hàng nào, trả về kết quả trống
      if (cartsResult.items.length === 0) {
        return {
          items: [],
          meta: cartsResult.meta
        };
      }

      // Chuyển đổi sang DTO - sử dụng Promise.all vì mapToCartAdminResponseDto là async
      const cartDtos = await Promise.all(cartsResult.items.map(async (cart) => {
        try {
          return await this.cartHelper.mapToCartAdminResponseDto(cart);
        } catch (dtoError) {
          this.logger.error(`Error mapping cart ${cart.id} to DTO: ${dtoError.message}`, dtoError.stack);
          // Ném lỗi để xử lý ở catch block bên ngoài
          throw new AppException(
            MARKETPLACE_ERROR_CODES.CART_RETRIEVAL_FAILED,
            `Lỗi khi chuyển đổi giỏ hàng: ${dtoError.message}`
          );
        }
      }));

      // Trả về kết quả phân trang
      return {
        items: cartDtos,
        meta: cartsResult.meta
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting carts: ${error.message}`, error.stack);
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        'Lỗi khi lấy danh sách giỏ hàng'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết giỏ hàng theo ID
   * @param employeeId ID của nhân viên
   * @param cartId ID của giỏ hàng
   * @returns Thông tin chi tiết giỏ hàng
   */
  async getCartById(
    _employeeId: number,
    cartId: number
  ): Promise<CartAdminResponseDto> {
    try {
      // Lấy thông tin giỏ hàng từ repository
      const cart = await this.cartRepository.findById(cartId);

      // Kiểm tra giỏ hàng tồn tại
      if (!cart) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.CART_NOT_FOUND,
          `Giỏ hàng với ID ${cartId} không tồn tại`
        );
      }

      // Chuyển đổi sang DTO - sử dụng await vì mapToCartAdminResponseDto là async
      return await this.cartHelper.mapToCartAdminResponseDto(cart);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting cart by ID: ${error.message}`, error.stack);
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        `Không thể lấy thông tin chi tiết giỏ hàng: ${error.message}`
      );
    }
  }
}
