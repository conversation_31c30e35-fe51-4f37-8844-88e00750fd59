import { Injectable, Logger } from '@nestjs/common';
import { MarketOrderRepository } from '@modules/marketplace/repositories';
import { OrderHelper } from '@modules/marketplace/helpers';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { OrderQueryDto } from '../dto/order-query.dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { OrderResponseDto } from '../dto/order-response.dto';

/**
 * Service xử lý logic liên quan đến đơn hàng cho admin
 */
@Injectable()
export class OrderAdminService {
  private readonly logger = new Logger(OrderAdminService.name);

  constructor(
    private readonly marketOrderRepository: MarketOrderRepository,
    private readonly orderHelper: OrderHelper
  ) {}

  /**
   * L<PERSON><PERSON> danh sách lịch sử mua hàng của tất cả người dùng với phân trang, t<PERSON><PERSON> kiếm, lọ<PERSON> và sắp xếp
   * @param employeeId ID của nhân viên
   * @param queryDto DTO truy vấn
   * @returns Danh sách lịch sử mua hàng phân trang
   */
  /**
   * Lấy danh sách lịch sử mua hàng của tất cả người dùng với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employeeId ID của nhân viên
   * @param queryDto DTO truy vấn
   * @returns Danh sách lịch sử mua hàng phân trang
   */
  async getOrders(
    _employeeId: number,
    queryDto: OrderQueryDto
  ): Promise<PaginatedResult<OrderResponseDto>> {
    try {
      // Kiểm tra dữ liệu trong bảng trước
      const tableData = await this.marketOrderRepository.checkTableData();
      this.logger.debug(`Table data check: ${JSON.stringify(tableData)}`);

      // Lấy danh sách đơn hàng từ repository
      const ordersResult = await this.marketOrderRepository.findAdminOrders(queryDto);

      // Nếu không có đơn hàng nào, trả về kết quả trống
      if (ordersResult.items.length === 0) {
        return {
          items: [],
          meta: ordersResult.meta
        };
      }

      // Chuyển đổi sang DTO - sử dụng Promise.all vì mapToOrderResponseDto là async
      const orderDtos = await Promise.all(ordersResult.items.map(async (order) => {
        try {
          return await this.orderHelper.mapToOrderResponseDto(order);
        } catch (dtoError) {
          this.logger.error(`Error mapping order ${order.id} to DTO: ${dtoError.message}`, dtoError.stack);
          // Ném lỗi để xử lý ở catch block bên ngoài
          throw new AppException(
            MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
            `Lỗi khi chuyển đổi đơn hàng: ${dtoError.message}`
          );
        }
      }));

      // Trả về kết quả phân trang
      return {
        items: orderDtos,
        meta: ordersResult.meta
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting orders: ${error.message}`, error.stack);
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        'Lỗi khi lấy danh sách đơn hàng'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết đơn hàng theo ID
   * @param employeeId ID của nhân viên
   * @param orderId ID của đơn hàng
   * @returns Thông tin chi tiết đơn hàng
   */
  async getOrderById(
    _employeeId: number,
    orderId: number
  ): Promise<OrderResponseDto> {
    try {
      // Lấy thông tin đơn hàng từ repository
      const order = await this.marketOrderRepository.findOrderDetailById(orderId);

      // Kiểm tra đơn hàng tồn tại
      if (!order) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.ORDER_NOT_FOUND,
          `Đơn hàng với ID ${orderId} không tồn tại`
        );
      }

      // Chuyển đổi sang DTO - sử dụng await vì mapToOrderResponseDto là async
      return await this.orderHelper.mapToOrderResponseDto(order);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting order by ID: ${error.message}`, error.stack);
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        'Lỗi khi lấy thông tin chi tiết đơn hàng'
      );
    }
  }
}
