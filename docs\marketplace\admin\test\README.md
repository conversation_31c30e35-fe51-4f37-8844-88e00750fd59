# Tests cho Module Marketplace Admin

## C<PERSON>u trúc thư mục

```
test/
├── __mocks__/                # Mock data và services
│   ├── cart.mock.ts          # Mock data cho giỏ hàng
│   ├── order.mock.ts         # Mock data cho đơn hàng
│   ├── product.mock.ts       # Mock data cho sản phẩm
│   ├── repository.mock.ts    # Mock cho Repository
│   └── service.mock.ts       # Mock cho Service
├── controllers/              # Tests cho controllers
│   ├── cart-admin.controller.spec.ts    # Test cho CartAdminController
│   ├── order-admin.controller.spec.ts   # Test cho OrderAdminController
│   └── product-admin.controller.spec.ts # Test cho ProductAdminController
├── services/                 # Tests cho services
│   ├── cart-admin.service.spec.ts       # Test cho CartAdminService
│   ├── order-admin.service.spec.ts      # Test cho OrderAdminService
│   └── product-admin.service.spec.ts    # Test cho ProductAdminService
├── jest.config.ts            # <PERSON><PERSON><PERSON> hình <PERSON> cho module marketplace admin
├── run-tests.js              # Script chạy test
└── README.md                 # Tài liệu hướng dẫn
```

## Cách chạy tests

```bash
# Chạy tất cả tests
npm run test:marketplace-admin

# Chạy test cho một file cụ thể
npx jest src/modules/marketplace/admin/test/controllers/cart-admin.controller.spec.ts --config=src/modules/marketplace/admin/test/jest.config.ts

# Chạy test với coverage
npx jest --config=src/modules/marketplace/admin/test/jest.config.ts --coverage

# Sử dụng script run-tests.js
node src/modules/marketplace/admin/test/run-tests.js                  # Chạy tất cả các test
node src/modules/marketplace/admin/test/run-tests.js --coverage       # Chạy tất cả các test với coverage
node src/modules/marketplace/admin/test/run-tests.js --dir controllers # Chạy tất cả các test trong thư mục controllers
node src/modules/marketplace/admin/test/run-tests.js --file controllers/cart-admin.controller.spec.ts # Chạy một file test cụ thể
```

## Quy ước viết test

1. Mỗi controller và service cần có ít nhất một file test tương ứng
2. Sử dụng mock cho các dependencies để đảm bảo unit test độc lập
3. Đảm bảo test coverage cho các trường hợp thành công và thất bại
4. Sử dụng các mocks trong thư mục `__mocks__` để đảm bảo tính nhất quán

## Danh sách các test

### Controllers
- **CartAdminController**: Test các API liên quan đến giỏ hàng
  - `getAllCarts`: Lấy danh sách giỏ hàng
  - `getCartById`: Lấy thông tin chi tiết giỏ hàng

- **ProductAdminController**: Test các API liên quan đến sản phẩm
  - `getProducts`: Lấy danh sách sản phẩm
  - `getProductById`: Lấy thông tin chi tiết sản phẩm
  - `updateProductStatus`: Cập nhật trạng thái sản phẩm

- **OrderAdminController**: Test các API liên quan đến đơn hàng
  - `getOrders`: Lấy danh sách đơn hàng
  - `getOrderById`: Lấy thông tin chi tiết đơn hàng

### Services
- **CartAdminService**: Test các phương thức xử lý logic giỏ hàng
  - `getCarts`: Lấy danh sách giỏ hàng
  - `getCartById`: Lấy thông tin chi tiết giỏ hàng

- **ProductAdminService**: Test các phương thức xử lý logic sản phẩm
  - `getProducts`: Lấy danh sách sản phẩm
  - `getProductById`: Lấy thông tin chi tiết sản phẩm
  - `updateProductStatus`: Cập nhật trạng thái sản phẩm

- **OrderAdminService**: Test các phương thức xử lý logic đơn hàng
  - `getOrders`: Lấy danh sách đơn hàng
  - `getOrderById`: Lấy thông tin chi tiết đơn hàng
