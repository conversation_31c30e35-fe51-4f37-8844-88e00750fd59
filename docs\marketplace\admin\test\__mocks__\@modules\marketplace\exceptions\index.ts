import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

export const MARKETPLACE_ERROR_CODES = {
  PRODUCT_NOT_FOUND: new ErrorCode(
    12001,
    'Sản phẩm không tồn tại',
    HttpStatus.NOT_FOUND
  ),
  INVALID_STATUS: new ErrorCode(
    12003,
    'Trạng thái sản phẩm không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),
  INVALID_PRICE: new ErrorCode(
    12003,
    '<PERSON><PERSON><PERSON> không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),
  PRODUCT_FETCH_ERROR: new ErrorCode(
    12007,
    'Lỗi khi lấy thông tin sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  PRODUCT_STATUS_CHANGE_FAILED: new ErrorCode(
    12033,
    'Thay đổi trạng thái sản phẩm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  PRODUCT_CREATION_FAILED: new ErrorCode(
    12008,
    'Tạo sản phẩm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  PRODUCT_UPDATE_FAILED: new ErrorCode(
    12011,
    'Cập nhật sản phẩm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  PRODUCT_DELETED: new ErrorCode(
    12012,
    'Sản phẩm đã bị xóa',
    HttpStatus.BAD_REQUEST
  ),
  MISSING_REQUIRED_FIELDS: new ErrorCode(
    12009,
    'Thiếu thông tin bắt buộc',
    HttpStatus.BAD_REQUEST
  ),
  FILE_UPLOAD_FAILED: new ErrorCode(
    12010,
    'Tải lên tệp thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  ORDER_NOT_FOUND: new ErrorCode(
    12002,
    'Đơn hàng không tồn tại',
    HttpStatus.NOT_FOUND
  ),
  CART_NOT_FOUND: new ErrorCode(
    12004,
    'Giỏ hàng không tồn tại',
    HttpStatus.NOT_FOUND
  ),
  CART_RETRIEVAL_FAILED: new ErrorCode(
    12005,
    'Lỗi khi lấy thông tin giỏ hàng',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  GENERAL_ERROR: new ErrorCode(
    12099,
    'Lỗi hệ thống',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

};
