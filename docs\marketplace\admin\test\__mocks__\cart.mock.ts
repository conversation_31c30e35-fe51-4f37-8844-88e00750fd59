import { CartAdminResponseDto } from '@modules/marketplace/admin/dto';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Mock data cho giỏ hàng
 */
export const mockCart: any = {
  id: 1,
  userId: 1,
  'user.id': 1,
  'user.full_name': '<PERSON>uy<PERSON><PERSON>n <PERSON>',
  'user.email': '<EMAIL>',
  'user.avatar': 'avatar.jpg',
  'cartItems.id': 1,
  'cartItems.product_id': 1,
  'cartItems.quantity': 2,
  'product.id': 1,
  'product.name': 'Sản phẩm 1',
  'product.description': '<PERSON>ô tả sản phẩm 1',
  'product.listed_price': 1000,
  'product.discounted_price': 800,
  'product.category': 'AGENT',
  'product.status': 'APPROVED',
  'product.user_id': 2,
  'product.user.id': 2,
  'product.user.full_name': '<PERSON><PERSON><PERSON><PERSON> bán',
  'product.user.email': '<EMAIL>',
  'employee.id': null,
  'employee.full_name': null,
  'employee.email': null,
  'product.images': [{ key: 'image1.jpg', position: 0 }],
  'product.createdAt': 1625097600000,
  'product.updatedAt': 1625097600000,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho danh sách giỏ hàng
 */
export const mockCarts: any[] = [
  mockCart,
  {
    id: 2,
    userId: 2,
    'user.id': 2,
    'user.full_name': 'Nguyễn Văn B',
    'user.email': '<EMAIL>',
    'user.avatar': 'avatar2.jpg',
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  },
];

/**
 * Mock data cho CartAdminResponseDto
 */
export const mockCartResponseDto: CartAdminResponseDto = {
  id: 1,
  user: {
    id: 1,
    name: 'Nguyễn Văn A',
    email: '<EMAIL>',
    avatar: 'avatar.jpg',
  },
  items: [
    {
      id: 1,
      productId: 1,
      productName: 'Sản phẩm 1',
      discountedPrice: 800,
      sellerName: 'Người bán',
      createdAt: 1625097600000,
      quantity: 2,
    },
  ],
  totalValue: 1600,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho PaginatedResult<CartAdminResponseDto>
 */
export const mockPaginatedCartResponseDto: PaginatedResult<CartAdminResponseDto> = {
  items: [mockCartResponseDto],
  meta: {
    totalItems: 1,
    itemCount: 1,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};
