import { MarketOrder, MarketOrderLine } from '@modules/marketplace/entities';
import { MarketOrderExtended } from '@modules/marketplace/interfaces';
import { User } from '@modules/user/entities';
import { OrderResponseDto } from '@modules/marketplace/admin/dto';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Mock data cho đơn hàng
 */
export const mockOrder: MarketOrderExtended = {
  id: 1,
  userId: 1,
  totalPoint: 1600,
  user: {
    id: 1,
    fullName: 'Nguyễn Văn A',
    email: '<EMAIL>',
  } as User,
  orderLines: [
    {
      id: 1,
      productId: 1,
      point: 800,
      productName: 'Sản phẩm 1',
      platformFeePercent: 5.0,
      sellerReceivePrice: 760,
      quantity: 2,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      product: {
        id: 1,
        name: 'Sản phẩm 1',
        description: '<PERSON><PERSON> tả sản phẩm 1',
        listedPrice: 1000,
        discountedPrice: 800,
        category: 'AGENT',
        status: 'APPROVED',
        userId: 2,
        user: {
          id: 2,
          fullName: 'Ngườ<PERSON> bán',
          email: '<EMAIL>',
        } as User,
        employee: {} as any,
        images: [{ key: 'image1.jpg', position: 0 }],
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
      },
      order: {} as any,
    } as unknown as MarketOrderLine,
  ],
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho danh sách đơn hàng
 */
export const mockOrders: MarketOrderExtended[] = [
  mockOrder,
  {
    id: 2,
    userId: 2,
    totalPoint: 2000,
    user: {
      id: 2,
      fullName: 'Nguyễn Văn B',
      email: '<EMAIL>',
    } as User,
    orderLines: [],
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  } as MarketOrderExtended,
];

/**
 * Mock data cho OrderResponseDto
 */
export const mockOrderResponseDto: OrderResponseDto = {
  id: 1,
  user: {
    id: 1,
    name: 'Nguyễn Văn A',
    email: '<EMAIL>',
    avatar: null,
  },
  orderLines: [
    {
      id: 1,
      productId: 1,
      productName: 'Sản phẩm 1',
      point: 800,
      quantity: 2,
      platformFeePercent: 5.0,
    },
  ],
  totalAmount: 1600,
  createdAt: 1625097600000,
};

/**
 * Mock data cho PaginatedResult<OrderResponseDto>
 */
export const mockPaginatedOrderResponseDto: PaginatedResult<OrderResponseDto> = {
  items: [mockOrderResponseDto],
  meta: {
    totalItems: 1,
    itemCount: 1,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};
