import { ProductStatus, ProductCategory } from '@modules/marketplace/enums';
import { ProductResponseDto, ProductDetailResponseDto } from '@modules/marketplace/admin/dto';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Mock data cho sản phẩm
 */
export const mockProduct: any = {
  id: 1,
  name: 'Sản phẩm test',
  description: 'Mô tả sản phẩm test',
  listedPrice: 1000,
  discountedPrice: 800,
  category: ProductCategory.AGENT,
  status: ProductStatus.APPROVED,
  userId: 1,
  employeeId: null,
  'user.id': 1,
  'user.full_name': 'Người dùng',
  'user.email': '<EMAIL>',
  employee: {} as any,
  images: [
    { key: 'image1.jpg', position: 0 },
    { key: 'image2.jpg', position: 1 },
  ],
  userManual: 'manual.pdf',
  detail: 'detail.pdf',
  createdAt: 1625097600000,
  updatedAt: 1625097600000,

  sourceId: 'source-123',
};

/**
 * Mock data cho sản phẩm của admin
 */
export const mockAdminProduct: any = {
  id: 2,
  name: 'Sản phẩm admin',
  description: 'Mô tả sản phẩm admin',
  listedPrice: 2000,
  discountedPrice: 1800,
  category: ProductCategory.FUNCTION,
  status: ProductStatus.APPROVED,
  userId: null,
  employeeId: 1,
  'user.id': null,
  'employee.id': 1,
  'employee.full_name': 'Admin',
  'employee.email': '<EMAIL>',
  images: [
    { key: 'admin-image1.jpg', position: 0 },
  ],
  userManual: 'admin-manual.pdf',
  detail: 'admin-detail.pdf',
  createdAt: 1625097600000,
  updatedAt: 1625097600000,

  sourceId: 'admin-source-123',
};

/**
 * Mock data cho danh sách sản phẩm
 */
export const mockProducts: any[] = [
  mockProduct,
  mockAdminProduct,
];

/**
 * Mock data cho ProductResponseDto
 */
export const mockProductResponseDto: ProductResponseDto = {
  id: 1,
  name: 'Sản phẩm test',
  description: 'Mô tả sản phẩm test',
  listedPrice: 1000,
  discountedPrice: 800,
  category: ProductCategory.AGENT,
  status: ProductStatus.APPROVED,
  seller: {
    id: 1,
    name: 'Người dùng',
    avatar: null,
    type: 'user',
  },
  images: ['https://example.com/image1.jpg'],
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  soldCount: 25,
  canPurchase: true,
};

/**
 * Mock data cho ProductDetailResponseDto
 */
export const mockProductDetailResponseDto: ProductDetailResponseDto = {
  id: 1,
  name: 'Sản phẩm test',
  description: 'Mô tả sản phẩm test',
  listedPrice: 1000,
  discountedPrice: 800,
  category: ProductCategory.AGENT,
  status: ProductStatus.APPROVED,
  seller: {
    id: 1,
    name: 'Người dùng',
    avatar: null,
    type: 'user',
  },
  images: [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg',
  ],
  userManual: 'https://example.com/manual.pdf',
  detail: 'https://example.com/detail.pdf',
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  sourceId: 'source-123',
  soldCount: 25,
  canPurchase: true,
};

/**
 * Mock data cho PaginatedResult<ProductResponseDto>
 */
export const mockPaginatedProductResponseDto: PaginatedResult<ProductResponseDto> = {
  items: [mockProductResponseDto],
  meta: {
    totalItems: 1,
    itemCount: 1,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};

/**
 * Mock data cho kết quả tạo sản phẩm
 */
export const mockCreateProductResult = {
  product: mockProductDetailResponseDto,
  uploadUrls: {
    productId: '1',
    imagesUploadUrls: [
      {
        url: 'https://example.com/upload/image1.jpg',
        key: 'marketplace/IMAGE/2023/01/01/product-image-0-123456789-1',
        index: 0
      }
    ],
    userManualUploadUrl: 'https://example.com/upload/manual.pdf',
    detailUploadUrl: 'https://example.com/upload/detail.pdf'
  }
};
