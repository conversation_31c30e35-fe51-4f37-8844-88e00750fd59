import { SelectQueryBuilder } from 'typeorm';

/**
 * Mock cho SelectQueryBuilder
 */
export class MockSelectQueryBuilder<T> {
  private aliasName: string;

  constructor(aliasName: string) {
    this.aliasName = aliasName;
  }

  select(): this {
    return this;
  }

  addSelect(): this {
    return this;
  }

  where(): this {
    return this;
  }

  andWhere(): this {
    return this;
  }

  orWhere(): this {
    return this;
  }

  leftJoin(): this {
    return this;
  }

  leftJoinAndSelect(): this {
    return this;
  }

  innerJoin(): this {
    return this;
  }

  innerJoinAndSelect(): this {
    return this;
  }

  orderBy(): this {
    return this;
  }

  addOrderBy(): this {
    return this;
  }

  skip(): this {
    return this;
  }

  take(): this {
    return this;
  }

  getOne(): Promise<T | null> {
    return Promise.resolve(null);
  }

  getMany(): Promise<T[]> {
    return Promise.resolve([]);
  }

  getManyAndCount(): Promise<[T[], number]> {
    return Promise.resolve([[], 0]);
  }

  getCount(): Promise<number> {
    return Promise.resolve(0);
  }
}

/**
 * Mock cho Repository
 */
export class MockRepository<T extends object> {
  createQueryBuilder(alias: string): SelectQueryBuilder<T> {
    return new MockSelectQueryBuilder<T>(alias) as unknown as SelectQueryBuilder<T>;
  }

  findOne(): Promise<T | null> {
    return Promise.resolve(null);
  }

  find(): Promise<T[]> {
    return Promise.resolve([]);
  }

  save(entity: T): Promise<T> {
    return Promise.resolve(entity);
  }

  create(entityLike: Partial<T>): T {
    return entityLike as T;
  }

  delete(): Promise<any> {
    return Promise.resolve({ affected: 1 });
  }

  update(): Promise<any> {
    return Promise.resolve({ affected: 1 });
  }
}
