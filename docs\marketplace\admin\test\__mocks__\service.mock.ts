import { CartAdminService, ProductAdminService, OrderAdminService } from '@modules/marketplace/admin/services';
import { mockCartResponseDto, mockPaginatedCartResponseDto } from './cart.mock';
import { mockProductDetailResponseDto, mockPaginatedProductResponseDto } from './product.mock';
import { mockOrderResponseDto, mockPaginatedOrderResponseDto } from './order.mock';
import {
  CartQueryDto,
  QueryProductDto,
  OrderQueryDto,
  UpdateProductStatusDto,
  UpdateMultipleProductsStatusDto,
} from '@modules/marketplace/admin/dto';

/**
 * Mock cho CartAdminService
 */
export const mockCartAdminService: Partial<CartAdminService> = {
  getCarts: jest.fn().mockImplementation((_employeeId: number, _queryDto: CartQueryDto) => {
    return Promise.resolve(mockPaginatedCartResponseDto);
  }),

  getCartById: jest.fn().mockImplementation((_employeeId: number, cartId: number) => {
    if (cartId === 1) {
      return Promise.resolve(mockCartResponseDto);
    }
    throw new Error('Cart not found');
  }),
};

/**
 * Mock cho ProductAdminService
 */
export const mockProductAdminService: Partial<ProductAdminService> = {
  getProducts: jest.fn().mockImplementation((_employeeId: number, _queryDto: QueryProductDto) => {
    return Promise.resolve(mockPaginatedProductResponseDto);
  }),

  getProductById: jest.fn().mockImplementation((_employeeId: number, productId: number) => {
    if (productId === 1) {
      return Promise.resolve(mockProductDetailResponseDto);
    }
    throw new Error('Product not found');
  }),

  updateMultipleProductsStatus: jest.fn().mockImplementation((_employeeId: number, _updateDto: any) => {
    return Promise.resolve({
      successIds: [1, 2],
      failedIds: [
        {
          id: 3,
          reason: 'Sản phẩm không tồn tại'
        }
      ]
    });
  }),
};

/**
 * Mock cho OrderAdminService
 */
export const mockOrderAdminService: Partial<OrderAdminService> = {
  getOrders: jest.fn().mockImplementation((_employeeId: number, _queryDto: OrderQueryDto) => {
    return Promise.resolve(mockPaginatedOrderResponseDto);
  }),

  getOrderById: jest.fn().mockImplementation((_employeeId: number, orderId: number) => {
    if (orderId === 1) {
      return Promise.resolve(mockOrderResponseDto);
    }
    throw new Error('Order not found');
  }),
};
