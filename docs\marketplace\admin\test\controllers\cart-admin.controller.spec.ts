import { Test, TestingModule } from '@nestjs/testing';
import { CartAdminController } from '@modules/marketplace/admin/controllers/cart-admin.controller';
import { CartAdminService } from '@modules/marketplace/admin/services/cart-admin.service';
import { mockCartAdminService } from '../__mocks__/service.mock';
import { mockCartResponseDto, mockPaginatedCartResponseDto } from '../__mocks__/cart.mock';
import { CartQueryDto } from '@modules/marketplace/admin/dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

describe('Controller quản lý giỏ hàng (Admin)', () => {
  let controller: CartAdminController;
  let service: CartAdminService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CartAdminController],
      providers: [
        {
          provide: CartAdminService,
          useValue: mockCartAdminService,
        },
      ],
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(PermissionsGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<CartAdminController>(CartAdminController);
    service = module.get<CartAdminService>(CartAdminService);
  });

  it('phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('lấy tất cả giỏ hàng', () => {
    it('phải trả về danh sách giỏ hàng có phân trang', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new CartQueryDto();
      queryDto.page = 1;
      queryDto.limit = 10;

      jest.spyOn(service, 'getCarts').mockResolvedValue(mockPaginatedCartResponseDto);

      // Act
      const result = await controller.getAllCarts(employeeId, queryDto);

      // Assert
      expect(service.getCarts).toHaveBeenCalledWith(employeeId, queryDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockPaginatedCartResponseDto);
      expect(result.message).toBe('Lấy danh sách giỏ hàng thành công');
    });

    it('phải xử lý các tham số tìm kiếm và lọc', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new CartQueryDto();
      queryDto.page = 1;
      queryDto.limit = 10;
      queryDto.search = 'test';
      queryDto.userId = 2;

      jest.spyOn(service, 'getCarts').mockResolvedValue(mockPaginatedCartResponseDto);

      // Act
      const result = await controller.getAllCarts(employeeId, queryDto);

      // Assert
      expect(service.getCarts).toHaveBeenCalledWith(employeeId, expect.objectContaining({
        search: 'test',
        userId: 2
      }));
      expect(result.result).toEqual(mockPaginatedCartResponseDto);
    });
  });

  describe('lấy giỏ hàng theo ID', () => {
    it('phải trả về thông tin chi tiết giỏ hàng theo ID', async () => {
      // Arrange
      const employeeId = 1;
      const cartId = 1;

      jest.spyOn(service, 'getCartById').mockResolvedValue(mockCartResponseDto);

      // Act
      const result = await controller.getCartById(employeeId, cartId);

      // Assert
      expect(service.getCartById).toHaveBeenCalledWith(employeeId, cartId);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockCartResponseDto);
      expect(result.message).toBe('Lấy thông tin chi tiết giỏ hàng thành công');
    });

    it('phải xử lý lỗi khi không tìm thấy giỏ hàng', async () => {
      // Arrange
      const employeeId = 1;
      const cartId = 999;

      jest.spyOn(service, 'getCartById').mockRejectedValue(new Error('Cart not found'));

      // Act & Assert
      try {
        await controller.getCartById(employeeId, cartId);
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error.message).toBe('Cart not found');
      }
    });
  });
});
