import { Test, TestingModule } from '@nestjs/testing';
import { OrderAdminController } from '@modules/marketplace/admin/controllers/order-admin.controller';
import { OrderAdminService } from '@modules/marketplace/admin/services/order-admin.service';
import { mockOrderResponseDto, mockPaginatedOrderResponseDto } from '../__mocks__/order.mock';
import { OrderQueryDto } from '@modules/marketplace/admin/dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';


describe('Controller quản lý đơn hàng (Admin)', () => {
  let controller: OrderAdminController;
  let service: OrderAdminService;

  const mockOrderAdminService = {
    getOrders: jest.fn(),
    getOrderById: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrderAdminController],
      providers: [
        {
          provide: OrderAdminService,
          useValue: mockOrderAdminService,
        },
      ],
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(PermissionsGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<OrderAdminController>(OrderAdminController);
    service = module.get<OrderAdminService>(OrderAdminService);
  });

  it('phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('lấy danh sách đơn hàng', () => {
    it('phải trả về danh sách đơn hàng có phân trang', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new OrderQueryDto();
      queryDto.page = 1;
      queryDto.limit = 10;

      jest.spyOn(service, 'getOrders').mockResolvedValue(mockPaginatedOrderResponseDto);

      // Act
      const result = await controller.getOrders(employeeId, queryDto);

      // Assert
      expect(service.getOrders).toHaveBeenCalledWith(employeeId, queryDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockPaginatedOrderResponseDto);
      expect(result.message).toBe('Lấy danh sách đơn hàng thành công');
    });

    it('phải xử lý các tham số tìm kiếm và lọc', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new OrderQueryDto();
      queryDto.page = 1;
      queryDto.limit = 10;
      queryDto.search = 'test';
      // Không sử dụng status vì OrderQueryDto không có trường này
      queryDto.userId = 1;

      jest.spyOn(service, 'getOrders').mockResolvedValue(mockPaginatedOrderResponseDto);

      // Act
      const result = await controller.getOrders(employeeId, queryDto);

      // Assert
      expect(service.getOrders).toHaveBeenCalledWith(employeeId, expect.objectContaining({
        search: 'test',

        userId: 1
      }));
      expect(result.result).toEqual(mockPaginatedOrderResponseDto);
    });
  });

  describe('lấy đơn hàng theo ID', () => {
    it('phải trả về thông tin chi tiết đơn hàng theo ID', async () => {
      // Arrange
      const employeeId = 1;
      const orderId = 1;

      jest.spyOn(service, 'getOrderById').mockResolvedValue(mockOrderResponseDto);

      // Act
      const result = await controller.getOrderById(employeeId, orderId);

      // Assert
      expect(service.getOrderById).toHaveBeenCalledWith(employeeId, orderId);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockOrderResponseDto);
      expect(result.message).toBe('Lấy thông tin chi tiết đơn hàng thành công');
    });

    it('phải xử lý lỗi khi không tìm thấy đơn hàng', async () => {
      // Arrange
      const employeeId = 1;
      const orderId = 999;

      jest.spyOn(service, 'getOrderById').mockRejectedValue(new Error('Order not found'));

      // Act & Assert
      try {
        await controller.getOrderById(employeeId, orderId);
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error.message).toBe('Order not found');
      }
    });
  });
});
