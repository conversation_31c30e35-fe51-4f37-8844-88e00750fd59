import { Test, TestingModule } from '@nestjs/testing';
import { ProductAdminController } from '@modules/marketplace/admin/controllers/product-admin.controller';
import { ProductAdminService } from '@modules/marketplace/admin/services/product-admin.service';
import { mockPaginatedProductResponseDto, mockProductDetailResponseDto, mockCreateProductResult } from '../__mocks__/product.mock';
import { QueryProductDto, UpdateProductStatusDto, CreateProductAdminDto } from '@modules/marketplace/admin/dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';
import { AppException } from '../__mocks__/@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '../__mocks__/@modules/marketplace/exceptions';

describe('Controller quản lý sản phẩm (Admin)', () => {
  let controller: ProductAdminController;
  let service: ProductAdminService;

  const mockProductAdminService = {
    getProducts: jest.fn(),
    getProductById: jest.fn(),
    updateProductStatus: jest.fn(),
    createPresignedUrls: jest.fn(),
    updateProduct: jest.fn(),
    createProduct: jest.fn(),
    publishProduct: jest.fn(),
    deleteProduct: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductAdminController],
      providers: [
        {
          provide: ProductAdminService,
          useValue: mockProductAdminService,
        },
      ],
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(PermissionsGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<ProductAdminController>(ProductAdminController);
    service = module.get<ProductAdminService>(ProductAdminService);
  });

  it('phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('lấy tất cả sản phẩm', () => {
    it('phải trả về danh sách sản phẩm có phân trang', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new QueryProductDto();
      queryDto.page = 1;
      queryDto.limit = 10;

      jest.spyOn(service, 'getProducts').mockResolvedValue(mockPaginatedProductResponseDto);

      // Act
      const result = await controller.getAllProducts(employeeId, queryDto);

      // Assert
      expect(service.getProducts).toHaveBeenCalledWith(employeeId, queryDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockPaginatedProductResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý các tham số tìm kiếm và lọc', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new QueryProductDto();
      queryDto.page = 1;
      queryDto.limit = 10;
      queryDto.search = 'test';
      queryDto.status = ProductStatus.APPROVED;

      jest.spyOn(service, 'getProducts').mockResolvedValue(mockPaginatedProductResponseDto);

      // Act
      const result = await controller.getAllProducts(employeeId, queryDto);

      // Assert
      expect(service.getProducts).toHaveBeenCalledWith(employeeId, expect.objectContaining({
        search: 'test',
        status: ProductStatus.APPROVED
      }));
      expect(result.result).toEqual(mockPaginatedProductResponseDto);
    });
  });

  describe('lấy sản phẩm theo ID', () => {
    it('phải trả về thông tin chi tiết sản phẩm theo ID', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 1;

      jest.spyOn(service, 'getProductById').mockResolvedValue(mockProductDetailResponseDto);

      // Act
      const result = await controller.getProductById(employeeId, productId);

      // Assert
      expect(service.getProductById).toHaveBeenCalledWith(employeeId, productId);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockProductDetailResponseDto);
      expect(result.message).toBe('Success');
    });

    it('phải xử lý lỗi khi không tìm thấy sản phẩm', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 999;

      jest.spyOn(service, 'getProductById').mockRejectedValue(new Error('Product not found'));

      // Act & Assert
      try {
        await controller.getProductById(employeeId, productId);
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error.message).toBe('Product not found');
      }
    });
  });

  describe('cập nhật trạng thái sản phẩm', () => {
    it('phải cập nhật trạng thái sản phẩm thành công', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 1;
      const updateDto = new UpdateProductStatusDto();
      updateDto.status = ProductStatus.APPROVED;

      jest.spyOn(service, 'updateProductStatus').mockResolvedValue(mockProductDetailResponseDto);

      // Act
      const result = await controller.updateProductStatus(employeeId, productId, updateDto);

      // Assert
      expect(service.updateProductStatus).toHaveBeenCalledWith(employeeId, productId, updateDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockProductDetailResponseDto);
      expect(result.message).toBe('Success');
    });
  });

  describe('tạo sản phẩm mới', () => {
    it('phải tạo sản phẩm mới thành công', async () => {
      // Arrange
      const employeeId = 1;
      const createDto = new CreateProductAdminDto();
      createDto.name = 'Sản phẩm test';
      createDto.description = 'Mô tả sản phẩm test';
      createDto.listedPrice = 1000;
      createDto.discountedPrice = 800;
      createDto.category = ProductCategory.KNOWLEDGE_FILE;
      createDto.imagesMediaTypes = ['image/jpeg'];

      jest.spyOn(service, 'createProduct').mockResolvedValue(mockCreateProductResult);

      // Act
      const result = await controller.createProduct(employeeId, createDto);

      // Assert
      expect(service.createProduct).toHaveBeenCalledWith(employeeId, createDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockCreateProductResult);
      expect(result.message).toBe('Created Successfully');
    });

    it('phải trả về lỗi khi giá niêm yết nhỏ hơn giá sau giảm', async () => {
      // Arrange
      const employeeId = 1;
      const createDto = new CreateProductAdminDto();
      createDto.name = 'Sản phẩm test';
      createDto.description = 'Mô tả sản phẩm test';
      createDto.listedPrice = 800; // Giá niêm yết nhỏ hơn giá sau giảm
      createDto.discountedPrice = 1000;
      createDto.category = ProductCategory.KNOWLEDGE_FILE;
      createDto.imagesMediaTypes = ['image/jpeg'];

      const mockError = new AppException(
        MARKETPLACE_ERROR_CODES.INVALID_PRICE,
        'Giá niêm yết phải lớn hơn hoặc bằng giá sau giảm'
      );

      jest.spyOn(service, 'createProduct').mockRejectedValue(mockError);

      // Act & Assert
      await expect(controller.createProduct(employeeId, createDto)).rejects.toThrow(AppException);
      expect(service.createProduct).toHaveBeenCalledWith(employeeId, createDto);
    });
  });
});
