import { plainToClass, plainToInstance } from 'class-transformer';
import { CartAdminResponseDto, CartUserInfoDto, CartItemDto } from '../../dto/cart-response.dto';

describe('CartResponseDto', () => {
  describe('CartUserInfoDto', () => {
    it('phải chuyển đổi dữ liệu người dùng thành DTO hợp lệ', () => {
      // Arrange
      const userData = {
        id: 123,
        name: '<PERSON><PERSON>ễ<PERSON>ăn <PERSON>',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg',
      };

      // Act
      const userDto = plainToInstance(CartUserInfoDto, userData);

      // Assert
      expect(userDto).toBeInstanceOf(CartUserInfoDto);
      expect(userDto.id).toBe(123);
      expect(userDto.name).toBe('<PERSON><PERSON><PERSON><PERSON>n <PERSON>');
      expect(userDto.email).toBe('<EMAIL>');
      expect(userDto.avatar).toBe('https://example.com/avatar.jpg');
    });

    it('phải chấp nhận avatar là null', () => {
      // Arrange
      const userData = {
        id: 123,
        name: 'Nguyễn Văn A',
        email: '<EMAIL>',
        avatar: null,
      };

      // Act
      const userDto = plainToInstance(CartUserInfoDto, userData);

      // Assert
      expect(userDto.avatar).toBeNull();
    });
  });

  describe('CartItemDto', () => {
    it('phải chuyển đổi dữ liệu sản phẩm trong giỏ hàng thành DTO hợp lệ', () => {
      // Arrange
      const itemData = {
        id: 789,
        productId: 123,
        productName: 'Laptop XYZ',
        discountedPrice: 1000,
        quantity: 2,
        sellerName: 'John Doe',
        createdAt: 1625097600000,
      };

      // Act
      const itemDto = plainToInstance(CartItemDto, itemData);

      // Assert
      expect(itemDto).toBeInstanceOf(CartItemDto);
      expect(itemDto.id).toBe(789);
      expect(itemDto.productId).toBe(123);
      expect(itemDto.productName).toBe('Laptop XYZ');
      expect(itemDto.discountedPrice).toBe(1000);
      expect(itemDto.quantity).toBe(2);
      expect(itemDto.sellerName).toBe('John Doe');
      expect(itemDto.createdAt).toBe(1625097600000);
    });
  });

  describe('CartAdminResponseDto', () => {
    it('phải chuyển đổi dữ liệu giỏ hàng thành DTO hợp lệ', () => {
      // Arrange
      const cartData = {
        id: 456,
        user: {
          id: 123,
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          avatar: 'https://example.com/avatar.jpg',
        },
        items: [
          {
            id: 789,
            productId: 123,
            productName: 'Laptop XYZ',
            discountedPrice: 1000,
            quantity: 2,
            sellerName: 'John Doe',
            createdAt: 1625097600000,
          },
          {
            id: 790,
            productId: 124,
            productName: 'Smartphone ABC',
            discountedPrice: 500,
            quantity: 1,
            sellerName: 'Jane Smith',
            createdAt: 1625097700000,
          },
        ],
        totalValue: 2500,
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
      };

      // Act
      const cartDto = plainToInstance(CartAdminResponseDto, cartData);

      // Assert
      expect(cartDto).toBeInstanceOf(CartAdminResponseDto);
      expect(cartDto.id).toBe(456);
      expect(cartDto.user.id).toBe(123);
      expect(cartDto.items).toHaveLength(2);
      expect(cartDto.items[0].productName).toBe('Laptop XYZ');
      expect(cartDto.items[1].productName).toBe('Smartphone ABC');
      expect(cartDto.totalValue).toBe(2500);
      expect(cartDto.createdAt).toBe(1625097600000);
      expect(cartDto.updatedAt).toBe(1625184000000);
    });

    it('phải chuyển đổi dữ liệu giỏ hàng không có sản phẩm thành DTO hợp lệ', () => {
      // Arrange
      const cartData = {
        id: 456,
        user: {
          id: 123,
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          avatar: null,
        },
        items: [],
        totalValue: 0,
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
      };

      // Act
      const cartDto = plainToInstance(CartAdminResponseDto, cartData);

      // Assert
      expect(cartDto).toBeInstanceOf(CartAdminResponseDto);
      expect(cartDto.id).toBe(456);
      expect(cartDto.items).toHaveLength(0);
      expect(cartDto.totalValue).toBe(0);
    });
  });
});
