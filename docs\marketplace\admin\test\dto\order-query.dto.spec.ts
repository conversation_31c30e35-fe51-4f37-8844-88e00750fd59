import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { OrderQueryDto, OrderSortField } from '../../dto/order-query.dto';
import { SortDirection } from '@/common/dto';

describe('OrderQueryDto', () => {
  it('phải xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(OrderQueryDto, {
      page: 1,
      limit: 10,
      search: 'order123',
      userId: 123,
      sortBy: OrderSortField.TOTAL_AMOUNT,
      sortDirection: SortDirection.ASC,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải xác thực DTO hợp lệ với các trường tùy chọn bị bỏ qua', async () => {
    // Arrange
    const dto = plainToInstance(OrderQueryDto, {
      page: 1,
      limit: 10,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải sử dụng giá trị mặc định cho sortBy và sortDirection khi không được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(OrderQueryDto, {
      page: 1,
      limit: 10,
    });

    // Assert
    expect(dto.sortBy).toBe(OrderSortField.CREATED_AT);
    expect(dto.sortDirection).toBe(SortDirection.DESC);
  });

  it('phải thất bại khi userId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(OrderQueryDto, {
      page: 1,
      limit: 10,
      userId: 'not-a-number', // Không phải số
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('phải thất bại khi trường sắp xếp không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(OrderQueryDto, {
      page: 1,
      limit: 10,
      sortBy: 'INVALID_SORT_FIELD', // Trường sắp xếp không hợp lệ
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('phải thất bại khi hướng sắp xếp không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(OrderQueryDto, {
      page: 1,
      limit: 10,
      sortDirection: 'INVALID_DIRECTION', // Hướng sắp xếp không hợp lệ
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('phải thất bại khi page không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(OrderQueryDto, {
      page: 'not-a-number', // Không phải số
      limit: 10,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isInt');
  });

  it('phải thất bại khi limit không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(OrderQueryDto, {
      page: 1,
      limit: 'not-a-number', // Không phải số
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isInt');
  });
});
