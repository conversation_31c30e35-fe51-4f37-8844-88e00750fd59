import { plainToClass, plainToInstance } from 'class-transformer';
import { OrderResponseDto, OrderUserInfoDto, OrderLineDto } from '../../dto/order-response.dto';

describe('OrderResponseDto', () => {
  describe('OrderUserInfoDto', () => {
    it('phải chuyển đổi dữ liệu người dùng thành DTO hợp lệ', () => {
      // Arrange
      const userData = {
        id: 123,
        name: '<PERSON>uy<PERSON><PERSON>ăn <PERSON>',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg',
      };

      // Act
      const userDto = plainToInstance(OrderUserInfoDto, userData);

      // Assert
      expect(userDto).toBeInstanceOf(OrderUserInfoDto);
      expect(userDto.id).toBe(123);
      expect(userDto.name).toBe('Nguy<PERSON><PERSON>n <PERSON>');
      expect(userDto.email).toBe('<EMAIL>');
      expect(userDto.avatar).toBe('https://example.com/avatar.jpg');
    });

    it('phải chấp nhận avatar là null', () => {
      // Arrange
      const userData = {
        id: 123,
        name: 'Nguyễn Văn A',
        email: '<EMAIL>',
        avatar: null,
      };

      // Act
      const userDto = plainToInstance(OrderUserInfoDto, userData);

      // Assert
      expect(userDto.avatar).toBeNull();
    });
  });

  describe('OrderLineDto', () => {
    it('phải chuyển đổi dữ liệu chi tiết đơn hàng thành DTO hợp lệ', () => {
      // Arrange
      const orderLineData = {
        id: 789,
        productId: 123,
        productName: 'Laptop XYZ',
        point: 1000,
        quantity: 2,
        platformFeePercent: 5.0,
      };

      // Act
      const orderLineDto = plainToInstance(OrderLineDto, orderLineData);

      // Assert
      expect(orderLineDto).toBeInstanceOf(OrderLineDto);
      expect(orderLineDto.id).toBe(789);
      expect(orderLineDto.productId).toBe(123);
      expect(orderLineDto.productName).toBe('Laptop XYZ');
      expect(orderLineDto.point).toBe(1000);
      expect(orderLineDto.quantity).toBe(2);
      expect(orderLineDto.platformFeePercent).toBe(5.0);
    });
  });

  describe('OrderResponseDto', () => {
    it('phải chuyển đổi dữ liệu đơn hàng thành DTO hợp lệ', () => {
      // Arrange
      const orderData = {
        id: 456,
        user: {
          id: 123,
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          avatar: 'https://example.com/avatar.jpg',
        },
        orderLines: [
          {
            id: 789,
            productId: 123,
            productName: 'Laptop XYZ',
            point: 1000,
            quantity: 2,
            platformFeePercent: 5.0,
          },
          {
            id: 790,
            productId: 124,
            productName: 'Smartphone ABC',
            point: 500,
            quantity: 1,
            platformFeePercent: 5.0,
          },
        ],
        totalAmount: 2500,
        createdAt: 1625097600000,
      };

      // Act
      const orderDto = plainToInstance(OrderResponseDto, orderData);

      // Assert
      expect(orderDto).toBeInstanceOf(OrderResponseDto);
      expect(orderDto.id).toBe(456);
      expect(orderDto.user.id).toBe(123);
      expect(orderDto.orderLines).toHaveLength(2);
      expect(orderDto.orderLines[0].productName).toBe('Laptop XYZ');
      expect(orderDto.orderLines[1].productName).toBe('Smartphone ABC');
      expect(orderDto.totalAmount).toBe(2500);
      expect(orderDto.createdAt).toBe(1625097600000);
    });

    it('phải chuyển đổi dữ liệu đơn hàng không có chi tiết thành DTO hợp lệ', () => {
      // Arrange
      const orderData = {
        id: 456,
        user: {
          id: 123,
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          avatar: null,
        },
        orderLines: [],
        totalAmount: 0,
        createdAt: 1625097600000,
      };

      // Act
      const orderDto = plainToInstance(OrderResponseDto, orderData);

      // Assert
      expect(orderDto).toBeInstanceOf(OrderResponseDto);
      expect(orderDto.id).toBe(456);
      expect(orderDto.orderLines).toHaveLength(0);
      expect(orderDto.totalAmount).toBe(0);
    });
  });
});
