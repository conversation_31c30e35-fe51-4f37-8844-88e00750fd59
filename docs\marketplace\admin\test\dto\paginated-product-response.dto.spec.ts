import { plainToClass, plainToInstance } from 'class-transformer';
import { PaginatedProductResponseDto } from '../../dto/paginated-product-response.dto';
import { ProductDetailResponseDto } from '../../dto/product-detail-response.dto';
import { ProductStatus } from '@modules/marketplace/enums';

describe('PaginatedProductResponseDto', () => {
  it('phải chuyển đổi dữ liệu danh sách sản phẩm có phân trang thành DTO hợp lệ', () => {
    // Arrange
    const mockProducts = [
      {
        id: 123,
        name: 'AI Chatbot Template',
        description: 'Mẫu chatbot AI hỗ trợ khách hàng tự động',
        listedPrice: 1200,
        discountedPrice: 1000,
        category: 'KNOWLEDGE_FILE',
        status: ProductStatus.APPROVED,
        images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        userManual: 'https://example.com/manual.pdf',
        detail: 'https://example.com/detail.pdf',
      },
      {
        id: 124,
        name: 'AI Assistant Template',
        description: 'Mẫu trợ lý AI đa năng',
        listedPrice: 1500,
        discountedPrice: 1200,
        category: 'AGENT',
        status: ProductStatus.APPROVED,
        images: ['https://example.com/image3.jpg'],
        createdAt: 1625184000000,
        updatedAt: 1625184000000,
        userManual: 'https://example.com/manual2.pdf',
        detail: 'https://example.com/detail2.pdf',
      },
    ];

    const paginationData = {
      items: mockProducts,
      meta: {
        totalItems: 100,
        itemCount: 2,
        itemsPerPage: 10,
        totalPages: 10,
        currentPage: 1,
      },
    };

    // Act
    const paginatedDto = plainToInstance(PaginatedProductResponseDto, paginationData);

    // Assert
    expect(paginatedDto).toBeInstanceOf(PaginatedProductResponseDto);
    expect(paginatedDto.items).toHaveLength(2);
    expect(paginatedDto.meta.totalItems).toBe(100);
    expect(paginatedDto.meta.itemCount).toBe(2);
    expect(paginatedDto.meta.itemsPerPage).toBe(10);
    expect(paginatedDto.meta.totalPages).toBe(10);
    expect(paginatedDto.meta.currentPage).toBe(1);
  });

  it('phải chuyển đổi dữ liệu danh sách sản phẩm trống thành DTO hợp lệ', () => {
    // Arrange
    const paginationData = {
      items: [],
      meta: {
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      },
    };

    // Act
    const paginatedDto = plainToInstance(PaginatedProductResponseDto, paginationData);

    // Assert
    expect(paginatedDto).toBeInstanceOf(PaginatedProductResponseDto);
    expect(paginatedDto.items).toHaveLength(0);
    expect(paginatedDto.meta.totalItems).toBe(0);
    expect(paginatedDto.meta.totalPages).toBe(0);
  });

  it('phải chuyển đổi dữ liệu danh sách sản phẩm với trang cuối cùng thành DTO hợp lệ', () => {
    // Arrange
    const mockProducts = [
      {
        id: 199,
        name: 'Last Product',
        description: 'Sản phẩm cuối cùng',
        listedPrice: 1000,
        discountedPrice: 800,
        category: 'KNOWLEDGE_FILE',
        status: ProductStatus.APPROVED,
        images: ['https://example.com/image-last.jpg'],
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        userManual: null,
        detail: null,
      },
    ];

    const paginationData = {
      items: mockProducts,
      meta: {
        totalItems: 101,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 11,
        currentPage: 11,
      },
    };

    // Act
    const paginatedDto = plainToInstance(PaginatedProductResponseDto, paginationData);

    // Assert
    expect(paginatedDto).toBeInstanceOf(PaginatedProductResponseDto);
    expect(paginatedDto.items).toHaveLength(1);
    expect(paginatedDto.meta.totalItems).toBe(101);
    expect(paginatedDto.meta.currentPage).toBe(11);
    expect(paginatedDto.meta.totalPages).toBe(11);
  });
});
