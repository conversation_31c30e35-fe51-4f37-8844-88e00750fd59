import { plainToClass, plainToInstance } from 'class-transformer';
import { PresignedUrlsDto, PresignedUrlImageDto } from '../../dto/presigned-url.dto';

describe('PresignedUrlDto', () => {
  describe('PresignedUrlImageDto', () => {
    it('phải chuyển đổi dữ liệu URL ký sẵn cho ảnh thành DTO hợp lệ', () => {
      // Arrange
      const imageUrlData = {
        index: 1,
        uploadUrl: 'https://hnssbfc.com/key1',
      };

      // Act
      const imageUrlDto = plainToInstance(PresignedUrlImageDto, imageUrlData);

      // Assert
      expect(imageUrlDto).toBeInstanceOf(PresignedUrlImageDto);
      expect(imageUrlDto.index).toBe(1);
      expect(imageUrlDto.uploadUrl).toBe('https://hnssbfc.com/key1');
    });
  });

  describe('PresignedUrlsDto', () => {
    it('phải chuyển đổi dữ liệu URL ký sẵn thành DTO hợp lệ với đầy đủ thông tin', () => {
      // Arrange
      const presignedUrlsData = {
        presignedUrlImage: [
          {
            index: 0,
            uploadUrl: 'https://hnssbfc.com/key0',
          },
          {
            index: 1,
            uploadUrl: 'https://hnssbfc.com/key1',
          },
        ],
        presignedUrlDetail: 'https://hnssbfc.com/key_detail',
        presignedUrlUserManual: 'https://hnssbfc.com/key_manual',
      };

      // Act
      const presignedUrlsDto = plainToInstance(PresignedUrlsDto, presignedUrlsData);

      // Assert
      expect(presignedUrlsDto).toBeInstanceOf(PresignedUrlsDto);
      expect(presignedUrlsDto.presignedUrlImage).toHaveLength(2);
      expect(presignedUrlsDto.presignedUrlImage[0].index).toBe(0);
      expect(presignedUrlsDto.presignedUrlImage[1].uploadUrl).toBe('https://hnssbfc.com/key1');
      expect(presignedUrlsDto.presignedUrlDetail).toBe('https://hnssbfc.com/key_detail');
      expect(presignedUrlsDto.presignedUrlUserManual).toBe('https://hnssbfc.com/key_manual');
      expect(presignedUrlsDto.publishError).toBeUndefined();
    });

    it('phải chuyển đổi dữ liệu URL ký sẵn thành DTO hợp lệ với các trường null', () => {
      // Arrange
      const presignedUrlsData = {
        presignedUrlImage: [],
        presignedUrlDetail: null,
        presignedUrlUserManual: null,
      };

      // Act
      const presignedUrlsDto = plainToInstance(PresignedUrlsDto, presignedUrlsData);

      // Assert
      expect(presignedUrlsDto).toBeInstanceOf(PresignedUrlsDto);
      expect(presignedUrlsDto.presignedUrlImage).toHaveLength(0);
      expect(presignedUrlsDto.presignedUrlDetail).toBeNull();
      expect(presignedUrlsDto.presignedUrlUserManual).toBeNull();
    });

    it('phải chuyển đổi dữ liệu URL ký sẵn thành DTO hợp lệ với thông báo lỗi đăng bán', () => {
      // Arrange
      const presignedUrlsData = {
        presignedUrlImage: [
          {
            index: 0,
            uploadUrl: 'https://hnssbfc.com/key0',
          },
        ],
        presignedUrlDetail: 'https://hnssbfc.com/key_detail',
        presignedUrlUserManual: 'https://hnssbfc.com/key_manual',
        publishError: 'Không thể đăng bán sản phẩm: Thiếu thông tin bắt buộc',
      };

      // Act
      const presignedUrlsDto = plainToInstance(PresignedUrlsDto, presignedUrlsData);

      // Assert
      expect(presignedUrlsDto).toBeInstanceOf(PresignedUrlsDto);
      expect(presignedUrlsDto.presignedUrlImage).toHaveLength(1);
      expect(presignedUrlsDto.publishError).toBe('Không thể đăng bán sản phẩm: Thiếu thông tin bắt buộc');
    });
  });
});
