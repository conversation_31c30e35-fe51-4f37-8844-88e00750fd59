import { plainToClass, plainToInstance } from 'class-transformer';
import { ProductDetailResponseDto } from '../../dto/product-detail-response.dto';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';

describe('ProductDetailResponseDto', () => {
  it('phải chuyển đổi dữ liệu chi tiết sản phẩm thành DTO hợp lệ với đầy đủ thông tin', () => {
    // Arrange
    const productDetailData = {
      id: 123,
      name: 'AI Chatbot Template',
      description: 'A ready-to-use chatbot template for customer service',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.AGENT,
      status: ProductStatus.APPROVED,
      images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
      seller: {
        id: 123,
        name: '<PERSON>uy<PERSON><PERSON>ăn <PERSON>',
        avatar: 'https://example.com/avatar.jpg',
        type: 'user',
      },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      userManual: 'https://example.com/manual.pdf',
      detail: 'https://example.com/detail.pdf',
      sourceId: 'src_456',
    };

    // Act
    const productDetailDto = plainToInstance(ProductDetailResponseDto, productDetailData);

    // Assert
    expect(productDetailDto).toBeInstanceOf(ProductDetailResponseDto);
    expect(productDetailDto.id).toBe(123);
    expect(productDetailDto.name).toBe('AI Chatbot Template');
    expect(productDetailDto.description).toBe('A ready-to-use chatbot template for customer service');
    expect(productDetailDto.listedPrice).toBe(1200);
    expect(productDetailDto.discountedPrice).toBe(1000);
    expect(productDetailDto.category).toBe(ProductCategory.AGENT);
    expect(productDetailDto.status).toBe(ProductStatus.APPROVED);
    expect(productDetailDto.images).toHaveLength(2);
    expect(productDetailDto.seller.name).toBe('Nguyễn Văn A');
    expect(productDetailDto.userManual).toBe('https://example.com/manual.pdf');
    expect(productDetailDto.detail).toBe('https://example.com/detail.pdf');
    expect(productDetailDto.sourceId).toBe('src_456');
  });

  it('phải chuyển đổi dữ liệu chi tiết sản phẩm thành DTO hợp lệ với các trường tùy chọn là null', () => {
    // Arrange
    const productDetailData = {
      id: 123,
      name: 'AI Chatbot Template',
      description: 'A ready-to-use chatbot template for customer service',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.AGENT,
      status: ProductStatus.DRAFT,
      images: ['https://example.com/image1.jpg'],
      seller: {
        id: 456,
        name: 'Admin User',
        avatar: null,
        type: 'employee',
      },
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      userManual: null,
      detail: null,
      sourceId: null,
    };

    // Act
    const productDetailDto = plainToInstance(ProductDetailResponseDto, productDetailData);

    // Assert
    expect(productDetailDto).toBeInstanceOf(ProductDetailResponseDto);
    expect(productDetailDto.userManual).toBeNull();
    expect(productDetailDto.detail).toBeNull();
    expect(productDetailDto.sourceId).toBeNull();
  });

  it('phải kế thừa đúng các thuộc tính từ ProductResponseDto', () => {
    // Arrange
    const productDetailData = {
      id: 123,
      name: 'AI Chatbot Template',
      description: 'A ready-to-use chatbot template for customer service',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.AGENT,
      status: ProductStatus.APPROVED,
      images: ['https://example.com/image1.jpg'],
      seller: {
        id: 123,
        name: 'Nguyễn Văn A',
        avatar: 'https://example.com/avatar.jpg',
        type: 'user',
      },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      userManual: 'https://example.com/manual.pdf',
      detail: 'https://example.com/detail.pdf',
      sourceId: 'src_456',
    };

    // Act
    const productDetailDto = plainToInstance(ProductDetailResponseDto, productDetailData);

    // Assert
    // Kiểm tra các thuộc tính kế thừa từ ProductResponseDto
    expect(productDetailDto.id).toBe(123);
    expect(productDetailDto.name).toBe('AI Chatbot Template');
    expect(productDetailDto.description).toBe('A ready-to-use chatbot template for customer service');
    expect(productDetailDto.listedPrice).toBe(1200);
    expect(productDetailDto.discountedPrice).toBe(1000);
    expect(productDetailDto.category).toBe(ProductCategory.AGENT);
    expect(productDetailDto.status).toBe(ProductStatus.APPROVED);
    expect(productDetailDto.images).toHaveLength(1);
    expect(productDetailDto.seller).toBeDefined();
    expect(productDetailDto.createdAt).toBe(1625097600000);
    expect(productDetailDto.updatedAt).toBe(1625184000000);

    // Kiểm tra các thuộc tính mới trong ProductDetailResponseDto
    expect(productDetailDto.userManual).toBe('https://example.com/manual.pdf');
    expect(productDetailDto.detail).toBe('https://example.com/detail.pdf');
    expect(productDetailDto.sourceId).toBe('src_456');
  });
});
