import { plainToClass, plainToInstance } from 'class-transformer';
import { ProductResponseDto, SellerInfoDto } from '../../dto/product-response.dto';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';

describe('ProductResponseDto', () => {
  describe('SellerInfoDto', () => {
    it('phải chuyển đổi dữ liệu người bán thành DTO hợp lệ với đầy đủ thông tin', () => {
      // Arrange
      const sellerData = {
        id: 123,
        name: 'Nguyễn Văn A',
        avatar: 'https://example.com/avatar.jpg',
        type: 'user',
      };

      // Act
      const sellerDto = plainToInstance(SellerInfoDto, sellerData);

      // Assert
      expect(sellerDto).toBeInstanceOf(SellerInfoDto);
      expect(sellerDto.id).toBe(123);
      expect(sellerDto.name).toBe('<PERSON><PERSON><PERSON><PERSON>');
      expect(sellerDto.avatar).toBe('https://example.com/avatar.jpg');
      expect(sellerDto.type).toBe('user');
    });

    it('phải chuyển đổi dữ liệu người bán thành DTO hợp lệ với avatar là null', () => {
      // Arrange
      const sellerData = {
        id: 123,
        name: 'Nguyễn Văn A',
        avatar: null,
        type: 'employee',
      };

      // Act
      const sellerDto = plainToInstance(SellerInfoDto, sellerData);

      // Assert
      expect(sellerDto).toBeInstanceOf(SellerInfoDto);
      expect(sellerDto.avatar).toBeNull();
      expect(sellerDto.type).toBe('employee');
    });
  });

  describe('ProductResponseDto', () => {
    it('phải chuyển đổi dữ liệu sản phẩm thành DTO hợp lệ với đầy đủ thông tin', () => {
      // Arrange
      const productData = {
        id: 123,
        name: 'AI Chatbot Template',
        description: 'A ready-to-use chatbot template for customer service',
        listedPrice: 1200,
        discountedPrice: 1000,
        category: ProductCategory.AGENT,
        status: ProductStatus.APPROVED,
        images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        seller: {
          id: 123,
          name: 'Nguyễn Văn A',
          avatar: 'https://example.com/avatar.jpg',
          type: 'user',
        },
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
      };

      // Act
      const productDto = plainToInstance(ProductResponseDto, productData);

      // Assert
      expect(productDto).toBeInstanceOf(ProductResponseDto);
      expect(productDto.id).toBe(123);
      expect(productDto.name).toBe('AI Chatbot Template');
      expect(productDto.description).toBe('A ready-to-use chatbot template for customer service');
      expect(productDto.listedPrice).toBe(1200);
      expect(productDto.discountedPrice).toBe(1000);
      expect(productDto.category).toBe(ProductCategory.AGENT);
      expect(productDto.status).toBe(ProductStatus.APPROVED);
      expect(productDto.images).toHaveLength(2);
      expect(productDto.images[0]).toBe('https://example.com/image1.jpg');
      expect(productDto.seller.name).toBe('Nguyễn Văn A');
      expect(productDto.createdAt).toBe(1625097600000);
      expect(productDto.updatedAt).toBe(1625184000000);
    });

    it('phải chuyển đổi dữ liệu sản phẩm thành DTO hợp lệ với danh sách ảnh trống', () => {
      // Arrange
      const productData = {
        id: 123,
        name: 'AI Chatbot Template',
        description: 'A ready-to-use chatbot template for customer service',
        listedPrice: 1200,
        discountedPrice: 1000,
        category: ProductCategory.AGENT,
        status: ProductStatus.DRAFT,
        images: [],
        seller: {
          id: 456,
          name: 'Admin User',
          avatar: null,
          type: 'employee',
        },
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
      };

      // Act
      const productDto = plainToInstance(ProductResponseDto, productData);

      // Assert
      expect(productDto).toBeInstanceOf(ProductResponseDto);
      expect(productDto.images).toHaveLength(0);
      expect(productDto.status).toBe(ProductStatus.DRAFT);
      expect(productDto.seller.type).toBe('employee');
    });

    it('phải chuyển đổi dữ liệu sản phẩm với các trạng thái khác nhau', () => {
      // Arrange
      const statuses = [
        ProductStatus.DRAFT,
        ProductStatus.PENDING,
        ProductStatus.APPROVED,
        ProductStatus.REJECTED,
      ];

      // Act & Assert
      statuses.forEach(status => {
        const productData = {
          id: 123,
          name: 'AI Chatbot Template',
          description: 'A ready-to-use chatbot template for customer service',
          listedPrice: 1200,
          discountedPrice: 1000,
          category: ProductCategory.AGENT,
          status: status,
          images: ['https://example.com/image1.jpg'],
          seller: {
            id: 123,
            name: 'Nguyễn Văn A',
            avatar: 'https://example.com/avatar.jpg',
            type: 'user',
          },
          createdAt: 1625097600000,
          updatedAt: 1625184000000,
        };

        const productDto = plainToInstance(ProductResponseDto, productData);
        expect(productDto.status).toBe(status);
      });
    });
  });
});
