import { plainToInstance } from 'class-transformer';
import { UpdateMultipleProductsStatusResponseDto, ProductStatusUpdateErrorDto } from '../../dto/update-multiple-products-status-response.dto';

describe('UpdateMultipleProductsStatusResponseDto', () => {
  describe('ProductStatusUpdateErrorDto', () => {
    it('phải chuyển đổi dữ liệu lỗi cập nhật trạng thái thành DTO hợp lệ', () => {
      // Arrange
      const errorData = {
        id: 42,
        reason: 'Sản phẩm không tồn tại',
      };

      // Act
      const errorDto = plainToInstance(ProductStatusUpdateErrorDto, errorData);

      // Assert
      expect(errorDto).toBeInstanceOf(ProductStatusUpdateErrorDto);
      expect(errorDto.id).toBe(42);
      expect(errorDto.reason).toBe('<PERSON><PERSON>n phẩm không tồn tại');
    });
  });

  describe('UpdateMultipleProductsStatusResponseDto', () => {
    it('phải chuyển đổi dữ liệu phản hồi cập nhật trạng thái nhiều sản phẩm thành DTO hợp lệ', () => {
      // Arrange
      const responseData = {
        successIds: [1, 2, 3],
        failedIds: [
          {
            id: 4,
            reason: 'Sản phẩm không tồn tại',
          },
          {
            id: 5,
            reason: 'Không đủ quyền để cập nhật trạng thái sản phẩm',
          },
        ],
      };

      // Act
      const responseDto = plainToInstance(UpdateMultipleProductsStatusResponseDto, responseData);

      // Assert
      expect(responseDto).toBeInstanceOf(UpdateMultipleProductsStatusResponseDto);
      expect(responseDto.successIds).toHaveLength(3);
      expect(responseDto.successIds).toEqual([1, 2, 3]);
      expect(responseDto.failedIds).toHaveLength(2);
      expect(responseDto.failedIds[0].id).toBe(4);
      expect(responseDto.failedIds[0].reason).toBe('Sản phẩm không tồn tại');
      expect(responseDto.failedIds[1].id).toBe(5);
      expect(responseDto.failedIds[1].reason).toBe('Không đủ quyền để cập nhật trạng thái sản phẩm');
    });

    it('phải chuyển đổi dữ liệu phản hồi khi tất cả sản phẩm đều cập nhật thành công', () => {
      // Arrange
      const responseData = {
        successIds: [1, 2, 3, 4, 5],
        failedIds: [],
      };

      // Act
      const responseDto = plainToInstance(UpdateMultipleProductsStatusResponseDto, responseData);

      // Assert
      expect(responseDto).toBeInstanceOf(UpdateMultipleProductsStatusResponseDto);
      expect(responseDto.successIds).toHaveLength(5);
      expect(responseDto.successIds).toEqual([1, 2, 3, 4, 5]);
      expect(responseDto.failedIds).toHaveLength(0);
    });

    it('phải chuyển đổi dữ liệu phản hồi khi tất cả sản phẩm đều cập nhật thất bại', () => {
      // Arrange
      const responseData = {
        successIds: [],
        failedIds: [
          {
            id: 1,
            reason: 'Lỗi khi cập nhật trạng thái: Không thể chuyển từ APPROVED sang DRAFT',
          },
          {
            id: 2,
            reason: 'Lỗi khi cập nhật trạng thái: Không thể chuyển từ APPROVED sang DRAFT',
          },
        ],
      };

      // Act
      const responseDto = plainToInstance(UpdateMultipleProductsStatusResponseDto, responseData);

      // Assert
      expect(responseDto).toBeInstanceOf(UpdateMultipleProductsStatusResponseDto);
      expect(responseDto.successIds).toHaveLength(0);
      expect(responseDto.failedIds).toHaveLength(2);
      expect(responseDto.failedIds[0].id).toBe(1);
      expect(responseDto.failedIds[0].reason).toContain('Không thể chuyển từ APPROVED sang DRAFT');
    });
  });
});
