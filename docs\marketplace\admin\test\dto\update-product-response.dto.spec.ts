import { plainToClass, plainToInstance } from 'class-transformer';
import { UpdateProductResponseDto } from '../../dto/update-product-response.dto';
import { ProductDetailResponseDto } from '../../dto/product-detail-response.dto';
import { PresignedUrlImageDto } from '../../dto/presigned-url.dto';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';

describe('UpdateProductResponseDto', () => {
  it('phải chuyển đổi dữ liệu response cập nhật sản phẩm thành DTO hợp lệ với đầy đủ thông tin', () => {
    // Arrange
    const mockProductDetail = {
      id: 123,
      name: 'AI Chatbot Template',
      description: 'A ready-to-use chatbot template for customer service',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.AGENT,
      status: ProductStatus.APPROVED,
      images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
      seller: {
        id: 123,
        name: '<PERSON><PERSON><PERSON><PERSON>',
        avatar: 'https://example.com/avatar.jpg',
        type: 'user',
      },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      userManual: 'https://example.com/manual.pdf',
      detail: 'https://example.com/detail.pdf',
      sourceId: 'src_456',
    };

    const responseData = {
      product: mockProductDetail,
      presignedUrlImage: [
        {
          index: 0,
          uploadUrl: 'https://example.com/upload/image1.jpg',
        },
        {
          index: 1,
          uploadUrl: 'https://example.com/upload/image2.jpg',
        },
      ],
      presignedUrlDetail: 'https://example.com/upload/detail.pdf',
      presignedUrlUserManual: 'https://example.com/upload/manual.pdf',
    };

    // Act
    const responseDto = plainToInstance(UpdateProductResponseDto, responseData);

    // Assert
    expect(responseDto).toBeInstanceOf(UpdateProductResponseDto);
    expect(responseDto.product).toBeDefined();
    expect(responseDto.product.id).toBe(123);
    expect(responseDto.product.name).toBe('AI Chatbot Template');
    expect(responseDto.presignedUrlImage).toHaveLength(2);
    expect(responseDto.presignedUrlImage[0].index).toBe(0);
    expect(responseDto.presignedUrlImage[1].uploadUrl).toBe('https://example.com/upload/image2.jpg');
    expect(responseDto.presignedUrlDetail).toBe('https://example.com/upload/detail.pdf');
    expect(responseDto.presignedUrlUserManual).toBe('https://example.com/upload/manual.pdf');
    expect(responseDto.publishError).toBeUndefined();
  });

  it('phải chuyển đổi dữ liệu response cập nhật sản phẩm thành DTO hợp lệ với các trường tùy chọn là null', () => {
    // Arrange
    const mockProductDetail = {
      id: 123,
      name: 'AI Chatbot Template',
      description: 'A ready-to-use chatbot template for customer service',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.AGENT,
      status: ProductStatus.DRAFT,
      images: ['https://example.com/image1.jpg'],
      seller: {
        id: 456,
        name: 'Admin User',
        avatar: null,
        type: 'employee',
      },
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      userManual: null,
      detail: null,
      sourceId: null,
    };

    const responseData = {
      product: mockProductDetail,
      presignedUrlImage: [],
      presignedUrlDetail: null,
      presignedUrlUserManual: null,
    };

    // Act
    const responseDto = plainToInstance(UpdateProductResponseDto, responseData);

    // Assert
    expect(responseDto).toBeInstanceOf(UpdateProductResponseDto);
    expect(responseDto.product).toBeDefined();
    expect(responseDto.presignedUrlImage).toHaveLength(0);
    expect(responseDto.presignedUrlDetail).toBeNull();
    expect(responseDto.presignedUrlUserManual).toBeNull();
  });

  it('phải chuyển đổi dữ liệu response cập nhật sản phẩm thành DTO hợp lệ với thông báo lỗi đăng bán', () => {
    // Arrange
    const mockProductDetail = {
      id: 123,
      name: 'AI Chatbot Template',
      description: 'A ready-to-use chatbot template for customer service',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.AGENT,
      status: ProductStatus.DRAFT,
      images: ['https://example.com/image1.jpg'],
      seller: {
        id: 123,
        name: 'Nguyễn Văn A',
        avatar: 'https://example.com/avatar.jpg',
        type: 'user',
      },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      userManual: 'https://example.com/manual.pdf',
      detail: 'https://example.com/detail.pdf',
      sourceId: 'src_456',
    };

    const responseData = {
      product: mockProductDetail,
      presignedUrlImage: [
        {
          index: 0,
          uploadUrl: 'https://example.com/upload/image1.jpg',
        },
      ],
      presignedUrlDetail: 'https://example.com/upload/detail.pdf',
      presignedUrlUserManual: 'https://example.com/upload/manual.pdf',
      publishError: 'Không thể đăng bán sản phẩm: Thiếu thông tin bắt buộc',
    };

    // Act
    const responseDto = plainToInstance(UpdateProductResponseDto, responseData);

    // Assert
    expect(responseDto).toBeInstanceOf(UpdateProductResponseDto);
    expect(responseDto.product).toBeDefined();
    expect(responseDto.presignedUrlImage).toHaveLength(1);
    expect(responseDto.publishError).toBe('Không thể đăng bán sản phẩm: Thiếu thông tin bắt buộc');
  });
});
