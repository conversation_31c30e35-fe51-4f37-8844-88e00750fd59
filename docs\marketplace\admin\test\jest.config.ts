module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../../../..',
  testRegex: 'src/modules/marketplace/admin/test/.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['src/modules/marketplace/admin/**/*.(t|j)s'],
  coverageDirectory: './coverage/marketplace-admin',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@utils/(.*)$': '<rootDir>/src/shared/utils/$1',
    '^@database/(.*)$': '<rootDir>/src/database/$1',
    '^@config$': '<rootDir>/src/config',
    '^@dto/(.*)$': '<rootDir>/src/common/dto/$1',
    '^typeorm-transactional$': '<rootDir>/src/modules/marketplace/admin/test/__mocks__/typeorm-transactional.ts',
    '^@modules/marketplace/exceptions$': '<rootDir>/src/modules/marketplace/admin/test/__mocks__/@modules/marketplace/exceptions/index.ts',
    '^@common/exceptions$': '<rootDir>/src/modules/marketplace/admin/test/__mocks__/@common/exceptions/index.ts',
  },
  setupFilesAfterEnv: ['<rootDir>/src/jest-setup.ts'],
  passWithNoTests: true,
};
