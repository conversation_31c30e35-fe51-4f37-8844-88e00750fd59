/**
 * <PERSON>ript để chạy tất cả các test cho module marketplace admin
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// <PERSON><PERSON>u sắc cho output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

/**
 * Log với màu sắc
 * @param {string} message Thông điệ<PERSON> cần log
 * @param {string} color <PERSON><PERSON><PERSON> sắc (từ object colors)
 */
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

/**
 * Chạy một lệnh shell
 * @param {string} command Lệnh cần chạy
 * @returns {Object} Kết quả chạy lệnh
 */
function runCommand(command) {
  try {
    const output = execSync(command, { encoding: 'utf8' });
    return { success: true, output };
  } catch (error) {
    return { success: false, output: error.stdout };
  }
}

/**
 * Chạy tất cả các bài test trong một thư mục
 * @param {string} directory Thư mục cần chạy test
 * @returns {boolean} Kết quả chạy test
 */
function runAllTestsInDirectory(directory) {
  if (!fs.existsSync(directory)) {
    log(`Thư mục "${directory}" không tồn tại.`, colors.yellow);
    return false;
  }

  log(`\n${colors.bright}${colors.blue}Chạy tất cả bài test trong thư mục ${path.basename(directory)}...${colors.reset}`);
  
  const command = `npx jest ${directory} --config=src/modules/marketplace/admin/test/jest.config.ts --passWithNoTests`;
  const { success, output } = runCommand(command);
  
  if (success) {
    log(`${colors.green}✓ Tất cả test trong thư mục thành công${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}✗ Có test thất bại trong thư mục${colors.reset}`);
    log(output);
    return false;
  }
}

/**
 * Chạy một file test cụ thể
 * @param {string} filePath Đường dẫn đến file test
 * @returns {boolean} Kết quả chạy test
 */
function runSingleTest(filePath) {
  if (!fs.existsSync(filePath)) {
    log(`File "${filePath}" không tồn tại.`, colors.yellow);
    return false;
  }

  log(`\n${colors.bright}${colors.blue}Chạy test cho file ${path.basename(filePath)}...${colors.reset}`);
  
  const command = `npx jest ${filePath} --config=src/modules/marketplace/admin/test/jest.config.ts`;
  const { success, output } = runCommand(command);
  
  if (success) {
    log(`${colors.green}✓ Test thành công${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}✗ Test thất bại${colors.reset}`);
    log(output);
    return false;
  }
}

/**
 * Chạy tất cả các bài test
 * @returns {boolean} Kết quả chạy test
 */
function runAllTests() {
  log(`\n${colors.bright}${colors.magenta}Chạy tất cả bài test cho module marketplace admin...${colors.reset}`);
  
  const command = `npx jest --config=src/modules/marketplace/admin/test/jest.config.ts --passWithNoTests`;
  const { success, output } = runCommand(command);
  
  if (success) {
    log(`${colors.green}✓ Tất cả test thành công${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}✗ Có test thất bại${colors.reset}`);
    log(output);
    return false;
  }
}

/**
 * Chạy tất cả các bài test với coverage
 * @returns {boolean} Kết quả chạy test
 */
function runAllTestsWithCoverage() {
  log(`\n${colors.bright}${colors.magenta}Chạy tất cả bài test với coverage...${colors.reset}`);
  
  const command = `npx jest --config=src/modules/marketplace/admin/test/jest.config.ts --coverage --passWithNoTests`;
  const { success, output } = runCommand(command);
  
  if (success) {
    log(`${colors.green}✓ Tất cả test thành công${colors.reset}`);
    log(`${colors.cyan}Báo cáo coverage đã được tạo tại ./coverage/marketplace-admin/lcov-report/index.html${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}✗ Có test thất bại${colors.reset}`);
    log(output);
    return false;
  }
}

// Xử lý tham số dòng lệnh
const args = process.argv.slice(2);
if (args.length === 0) {
  // Không có tham số, chạy tất cả các test
  runAllTests();
} else if (args[0] === '--coverage') {
  // Chạy với coverage
  runAllTestsWithCoverage();
} else if (args[0] === '--dir' && args[1]) {
  // Chạy test trong một thư mục cụ thể
  const directory = path.join(__dirname, args[1]);
  runAllTestsInDirectory(directory);
} else if (args[0] === '--file' && args[1]) {
  // Chạy một file test cụ thể
  const filePath = path.join(__dirname, args[1]);
  runSingleTest(filePath);
} else {
  // Hiển thị hướng dẫn sử dụng
  log(`${colors.bright}${colors.yellow}Cách sử dụng:${colors.reset}`);
  log(`  node run-tests.js                  - Chạy tất cả các test`);
  log(`  node run-tests.js --coverage       - Chạy tất cả các test với coverage`);
  log(`  node run-tests.js --dir controllers - Chạy tất cả các test trong thư mục controllers`);
  log(`  node run-tests.js --file controllers/cart-admin.controller.spec.ts - Chạy một file test cụ thể`);
}
