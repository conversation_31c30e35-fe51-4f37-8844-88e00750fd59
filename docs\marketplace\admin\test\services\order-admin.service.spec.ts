import { Test, TestingModule } from '@nestjs/testing';
import { OrderAdminService } from '@modules/marketplace/admin/services/order-admin.service';
import { MarketOrderRepository } from '@modules/marketplace/repositories';
import { OrderHelper } from '@modules/marketplace/helpers';
import { mockOrder, mockOrders, mockOrderResponseDto, mockPaginatedOrderResponseDto } from '../__mocks__/order.mock';
import { OrderQueryDto } from '@modules/marketplace/admin/dto';
import { AppException, ErrorCode } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';

describe('Dịch vụ quản lý đơn hàng (Admin)', () => {
  let service: OrderAdminService;
  let orderRepository: MarketOrderRepository;
  let orderHelper: OrderHelper;

  const mockOrderRepository = {
    findById: jest.fn(),
    findAdminOrders: jest.fn(),
    findOrderDetailById: jest.fn(),
    checkTableData: jest.fn(),
  };

  const mockOrderHelper = {
    mapToOrderAdminResponseDto: jest.fn(),
    mapToOrderResponseDto: jest.fn().mockImplementation(async () => mockOrderResponseDto),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderAdminService,
        {
          provide: MarketOrderRepository,
          useValue: mockOrderRepository,
        },
        {
          provide: OrderHelper,
          useValue: mockOrderHelper,
        },
      ],
    }).compile();

    service = module.get<OrderAdminService>(OrderAdminService);
    orderRepository = module.get<MarketOrderRepository>(MarketOrderRepository);
    orderHelper = module.get<OrderHelper>(OrderHelper);
  });

  it('phải được định nghĩa', () => {
    expect(service).toBeDefined();
  });

  describe('lấy danh sách đơn hàng', () => {
    it('phải trả về danh sách đơn hàng có phân trang', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new OrderQueryDto();

      const mockPaginatedResult: PaginatedResult<any> = {
        items: mockOrders,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(orderRepository, 'checkTableData').mockResolvedValue({ count: 10 });
      jest.spyOn(orderRepository, 'findAdminOrders').mockResolvedValue(mockPaginatedResult);
      jest.spyOn(orderHelper, 'mapToOrderResponseDto').mockResolvedValue(mockOrderResponseDto);

      // Act
      const result = await service.getOrders(employeeId, queryDto);

      // Assert
      expect(orderRepository.findAdminOrders).toHaveBeenCalledWith(queryDto);
      expect(orderHelper.mapToOrderResponseDto).toHaveBeenCalledTimes(mockOrders.length);
      expect(result.items.length).toBe(mockOrders.length);
      expect(result.meta).toEqual(mockPaginatedResult.meta);
    });

    it('phải trả về mảng rỗng khi không tìm thấy đơn hàng nào', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new OrderQueryDto();

      const emptyPaginatedResult: PaginatedResult<any> = {
        items: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: 10,
          totalPages: 0,
          currentPage: 1,
        },
      };

      jest.spyOn(orderRepository, 'checkTableData').mockResolvedValue({ count: 0 });
      jest.spyOn(orderRepository, 'findAdminOrders').mockResolvedValue(emptyPaginatedResult);

      // Act
      const result = await service.getOrders(employeeId, queryDto);

      // Assert
      expect(orderRepository.findAdminOrders).toHaveBeenCalledWith(queryDto);
      expect(result.items).toEqual([]);
      expect(result.meta).toEqual(emptyPaginatedResult.meta);
    });

    it('phải xử lý lỗi trong quá trình chuyển đổi dữ liệu đơn hàng', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new OrderQueryDto();

      const mockPaginatedResult: PaginatedResult<any> = {
        items: mockOrders,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(orderRepository, 'checkTableData').mockResolvedValue({ count: 10 });
      jest.spyOn(orderRepository, 'findAdminOrders').mockResolvedValue(mockPaginatedResult);

      // First call succeeds, second call throws error
      jest.spyOn(orderHelper, 'mapToOrderResponseDto')
        .mockResolvedValueOnce(mockOrderResponseDto)
        .mockImplementationOnce(() => {
          throw new Error('Mapping error');
        });

      // Act & Assert
      try {
        await service.getOrders(employeeId, queryDto);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.GENERAL_ERROR);
        expect(error.message).toContain('Lỗi khi chuyển đổi đơn hàng');
      }
    });
  });

  describe('lấy thông tin chi tiết đơn hàng theo ID', () => {
    it('phải trả về thông tin chi tiết đơn hàng theo ID', async () => {
      // Arrange
      const employeeId = 1;
      const orderId = 1;

      jest.spyOn(orderRepository, 'findOrderDetailById').mockResolvedValue(mockOrder);
      jest.spyOn(orderHelper, 'mapToOrderResponseDto').mockResolvedValue(mockOrderResponseDto);

      // Act
      const result = await service.getOrderById(employeeId, orderId);

      // Assert
      expect(orderRepository.findOrderDetailById).toHaveBeenCalledWith(orderId);
      expect(orderHelper.mapToOrderResponseDto).toHaveBeenCalledWith(mockOrder);
      expect(result).toEqual(mockOrderResponseDto);
    });

    it('phải ném AppException khi không tìm thấy đơn hàng', async () => {
      // Arrange
      const employeeId = 1;
      const orderId = 999;

      jest.spyOn(orderRepository, 'findOrderDetailById').mockResolvedValue(null);

      // Act & Assert
      try {
        await service.getOrderById(employeeId, orderId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.ORDER_NOT_FOUND);
      }
    });

    it('phải xử lý và ném lại AppException', async () => {
      // Arrange
      const employeeId = 1;
      const orderId = 1;

      const appException = new AppException(
        MARKETPLACE_ERROR_CODES.ORDER_NOT_FOUND,
        'Order not found'
      );

      jest.spyOn(orderRepository, 'findOrderDetailById').mockRejectedValue(appException);

      // Act & Assert
      try {
        await service.getOrderById(employeeId, orderId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.ORDER_NOT_FOUND);
      }
    });

    it('phải bọc các lỗi khác trong AppException', async () => {
      // Arrange
      const employeeId = 1;
      const orderId = 1;

      jest.spyOn(orderRepository, 'findOrderDetailById').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      try {
        await service.getOrderById(employeeId, orderId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.GENERAL_ERROR);
      }
    });
  });
});
