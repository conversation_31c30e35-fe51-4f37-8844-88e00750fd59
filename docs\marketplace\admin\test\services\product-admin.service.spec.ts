import { Test, TestingModule } from '@nestjs/testing';
import { ProductAdminService } from '@modules/marketplace/admin/services/product-admin.service';
import { ProductRepository } from '@modules/marketplace/repositories';
import { ProductHelper, ValidationHelper, MediaHelper } from '@modules/marketplace/helpers';
import { S3Service } from '@shared/services/s3.service';
import { SqlHelper } from '@common/helpers/sql.helper';

import { mockProduct, mockProducts, mockProductResponseDto, mockProductDetailResponseDto } from '../__mocks__/product.mock';
import { QueryProductDto, UpdateProductStatusDto } from '@modules/marketplace/admin/dto';
import { AppException, ErrorCode } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { ProductStatus } from '@modules/marketplace/enums';

describe('<PERSON>ịch vụ quản lý sản phẩm (Admin)', () => {
  let service: ProductAdminService;
  let productRepository: ProductRepository;
  let productHelper: ProductHelper;
  let validationHelper: ValidationHelper;
  let mediaHelper: MediaHelper;
  let s3Service: S3Service;
  let sqlHelper: SqlHelper;


  const mockProductRepository = {
    findById: jest.fn(),
    findByIdForAdmin: jest.fn(),
    findAdminProducts: jest.fn(),
    save: jest.fn(),
    updateProductStatusWithRelatedTasks: jest.fn(),
  };

  const mockProductHelper = {
    mapToAdminProductResponseDto: jest.fn(),
    mapToAdminProductDetailResponseDto: jest.fn(),
  };

  const mockValidationHelper = {
    validateProductStatus: jest.fn(),
    validateProductStatusTransition: jest.fn(),
  };

  const mockS3Service = {
    createPresignedWithID: jest.fn(),
    getDownloadUrl: jest.fn(),
  };

  const mockSqlHelper = {
    getPaginatedData: jest.fn(),
  };

  const mockMediaHelper = {
    getImageTypeFromMimeString: jest.fn(),
  };


  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductAdminService,
        {
          provide: ProductRepository,
          useValue: mockProductRepository,
        },
        {
          provide: ProductHelper,
          useValue: mockProductHelper,
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
        {
          provide: SqlHelper,
          useValue: mockSqlHelper,
        },
        {
          provide: MediaHelper,
          useValue: mockMediaHelper,
        },
      ],
    }).compile();

    service = module.get<ProductAdminService>(ProductAdminService);
    productRepository = module.get<ProductRepository>(ProductRepository);
    productHelper = module.get<ProductHelper>(ProductHelper);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
    mediaHelper = module.get<MediaHelper>(MediaHelper);
    s3Service = module.get<S3Service>(S3Service);
    sqlHelper = module.get<SqlHelper>(SqlHelper);

  });

  it('phải được định nghĩa', () => {
    expect(service).toBeDefined();
  });

  describe('lấy danh sách sản phẩm', () => {
    it('phải trả về danh sách sản phẩm có phân trang', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new QueryProductDto();

      const mockPaginatedResult: PaginatedResult<any> = {
        items: mockProducts,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(productRepository, 'findAdminProducts').mockResolvedValue(mockPaginatedResult);
      jest.spyOn(productHelper, 'mapToAdminProductResponseDto').mockReturnValue(mockProductResponseDto);

      // Act
      const result = await service.getProducts(employeeId, queryDto);

      // Assert
      expect(productRepository.findAdminProducts).toHaveBeenCalledWith(queryDto);
      expect(productHelper.mapToAdminProductResponseDto).toHaveBeenCalledTimes(mockProducts.length);
      expect(result.items.length).toBe(mockProducts.length);
      expect(result.meta).toEqual(mockPaginatedResult.meta);
    });

    it('phải trả về mảng rỗng khi không tìm thấy sản phẩm nào', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new QueryProductDto();

      const emptyPaginatedResult: PaginatedResult<any> = {
        items: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: 10,
          totalPages: 0,
          currentPage: 1,
        },
      };

      jest.spyOn(productRepository, 'findAdminProducts').mockResolvedValue(emptyPaginatedResult);

      // Act
      const result = await service.getProducts(employeeId, queryDto);

      // Assert
      expect(productRepository.findAdminProducts).toHaveBeenCalledWith(queryDto);
      expect(result.items).toEqual([]);
      expect(result.meta).toEqual(emptyPaginatedResult.meta);
    });

    it('phải xử lý lỗi trong quá trình chuyển đổi dữ liệu sản phẩm', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new QueryProductDto();

      const mockPaginatedResult: PaginatedResult<any> = {
        items: mockProducts,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(productRepository, 'findAdminProducts').mockResolvedValue(mockPaginatedResult);

      // First call succeeds, second call throws error
      jest.spyOn(productHelper, 'mapToAdminProductResponseDto')
        .mockReturnValueOnce(mockProductResponseDto)
        .mockImplementationOnce(() => {
          throw new Error('Mapping error');
        });

      // Act & Assert
      try {
        await service.getProducts(employeeId, queryDto);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR);
        expect(error.message).toContain('Lỗi khi chuyển đổi sản phẩm');
      }
    });
  });

  describe('lấy thông tin chi tiết sản phẩm theo ID', () => {
    it('phải trả về thông tin chi tiết sản phẩm theo ID', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 1;

      jest.spyOn(productRepository, 'findByIdForAdmin').mockResolvedValue(mockProduct);
      jest.spyOn(productHelper, 'mapToAdminProductDetailResponseDto').mockReturnValue(mockProductDetailResponseDto);
      jest.spyOn(s3Service, 'getDownloadUrl').mockImplementation(() => Promise.resolve('https://example.com/image.jpg'));

      // Act
      const result = await service.getProductById(employeeId, productId);

      // Assert
      expect(productRepository.findByIdForAdmin).toHaveBeenCalledWith(productId);
      expect(productHelper.mapToAdminProductDetailResponseDto).toHaveBeenCalledWith(mockProduct);
      expect(result).toEqual(mockProductDetailResponseDto);
    });

    it('phải ném AppException khi không tìm thấy sản phẩm', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 999;

      jest.spyOn(productRepository, 'findByIdForAdmin').mockResolvedValue(null);

      // Act & Assert
      try {
        await service.getProductById(employeeId, productId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND);
      }
    });

    it('phải xử lý và ném lại AppException', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 1;

      const appException = new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
        'Product not found'
      );

      jest.spyOn(productRepository, 'findByIdForAdmin').mockRejectedValue(appException);

      // Act & Assert
      try {
        await service.getProductById(employeeId, productId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND);
      }
    });

    it('phải bọc các lỗi khác trong AppException', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 1;

      jest.spyOn(productRepository, 'findByIdForAdmin').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      try {
        await service.getProductById(employeeId, productId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR);
      }
    });
  });

  describe('cập nhật trạng thái sản phẩm', () => {
    it('phải cập nhật trạng thái sản phẩm thành công', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 1;
      const updateDto = new UpdateProductStatusDto();
      updateDto.status = ProductStatus.APPROVED;

      const updatedProduct = { ...mockProduct, status: ProductStatus.APPROVED };

      jest.spyOn(productRepository, 'findById').mockResolvedValue(mockProduct);
      jest.spyOn(validationHelper, 'validateProductStatusTransition').mockReturnValue(undefined);
      jest.spyOn(productRepository, 'updateProductStatusWithRelatedTasks').mockResolvedValue(updatedProduct);
      jest.spyOn(productHelper, 'mapToAdminProductDetailResponseDto').mockReturnValue(mockProductDetailResponseDto);

      // Act
      const result = await service.updateProductStatus(employeeId, productId, updateDto);

      // Assert
      expect(productRepository.findById).toHaveBeenCalledWith(productId);
      expect(validationHelper.validateProductStatusTransition).toHaveBeenCalledWith(
        mockProduct,
        employeeId,
        updateDto.status
      );
      expect(productRepository.updateProductStatusWithRelatedTasks).toHaveBeenCalled();
      expect(productHelper.mapToAdminProductDetailResponseDto).toHaveBeenCalledWith(updatedProduct);
      expect(result).toEqual(mockProductDetailResponseDto);
    });

    it('phải ném AppException khi không tìm thấy sản phẩm', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 999;
      const updateDto = new UpdateProductStatusDto();
      updateDto.status = ProductStatus.APPROVED;

      jest.spyOn(productRepository, 'findById').mockResolvedValue(null);

      // Act & Assert
      try {
        await service.updateProductStatus(employeeId, productId, updateDto);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND);
      }
    });

    it('phải ném AppException khi thay đổi trạng thái không hợp lệ', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 1;
      const updateDto = new UpdateProductStatusDto();
      updateDto.status = ProductStatus.APPROVED;

      jest.spyOn(productRepository, 'findById').mockResolvedValue(mockProduct);
      jest.spyOn(validationHelper, 'validateProductStatusTransition').mockImplementation(() => {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.INVALID_STATUS,
          'Invalid status change'
        );
      });

      // Act & Assert
      try {
        await service.updateProductStatus(employeeId, productId, updateDto);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.INVALID_STATUS);
      }
    });

    it('phải bọc các lỗi khác trong AppException', async () => {
      // Arrange
      const employeeId = 1;
      const productId = 1;
      const updateDto = new UpdateProductStatusDto();
      updateDto.status = ProductStatus.APPROVED;

      jest.spyOn(productRepository, 'findById').mockResolvedValue(mockProduct);
      jest.spyOn(validationHelper, 'validateProductStatusTransition').mockReturnValue(undefined);
      jest.spyOn(productRepository, 'updateProductStatusWithRelatedTasks').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      try {
        await service.updateProductStatus(employeeId, productId, updateDto);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED);
      }
    });
  });
});
