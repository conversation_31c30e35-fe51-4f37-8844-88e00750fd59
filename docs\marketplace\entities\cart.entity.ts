import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng carts trong cơ sở dữ liệu
 * Giỏ hàng
 */
@Entity('carts')
export class Cart {
  /**
   * Mã định danh giỏ hàng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Mã người dùng
   */
  @Column({ name: 'user_id' })
  user_id: number;

  /**
   * <PERSON>as cho user_id để tương thích với code cũ
   * @deprecated Sử dụng user_id thay thế
   */
  get userId(): number {
    return this.user_id;
  }

  set userId(value: number) {
    this.user_id = value;
  }

  /**
   * Thời gian tạo
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  created_at: number;

  /**
   * <PERSON><PERSON> cho created_at để tương thích với code cũ
   * @deprecated Sử dụng created_at thay thế
   */
  get createdAt(): number {
    return this.created_at;
  }

  set createdAt(value: number) {
    this.created_at = value;
  }

  /**
   * Thời gian cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  updated_at: number;

  /**
   * Alias cho updated_at để tương thích với code cũ
   * @deprecated Sử dụng updated_at thay thế
   */
  get updatedAt(): number {
    return this.updated_at;
  }

  set updatedAt(value: number) {
    this.updated_at = value;
  }

  /**
   * Danh sách các mục trong giỏ hàng
   * Thuộc tính này không được lưu trong cơ sở dữ liệu
   * Được sử dụng để lưu trữ kết quả join từ CartItem
   */
  cartItems?: any[];
}
