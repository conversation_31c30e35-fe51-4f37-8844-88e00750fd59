import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng market_order_line trong cơ sở dữ liệu
 * Chi tiết đơn hàng trong chợ
 */
@Entity('market_order_line')
export class MarketOrderLine {
  /**
   * Mã định danh chi tiết đơn hàng
   */
  @PrimaryGeneratedColumn('identity', { name: 'id' })
  id: number;

  /**
   * Mã sản phẩm
   */
  @Column({ name: 'product_id', type: 'bigint' })
  productId: number;

  /**
   * Mã đơn hàng
   */
  @Column({ name: 'order_id', type: 'bigint' })
  orderId: number;

  /**
   * Số point của sản phẩm
   */
  @Column({ name: 'point', type: 'bigint' })
  point: number;

  /**
   * Tên sản phẩm
   */
  @Column({ name: 'product_name', length: 500 })
  productName: string;

  /**
   * Phần trăm phí sàn
   */
  @Column({ name: 'platform_fee_percent', type: 'double precision' })
  platformFeePercent: number;

  /**
   * <PERSON>i<PERSON> người bán nhận được sau khi trừ phí sàn
   */
  @Column({ name: 'seller_receive_price', type: 'bigint' })
  sellerReceivePrice: number;

  /**
   * Số lượng
   */
  @Column({ name: 'quantity' })
  quantity: number;

  /**
   * Thời gian tạo
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  updatedAt: number;

  /**
   * Thông tin đơn hàng dạng JSON
   * Chứa thông tin về sản phẩm, người bán, v.v.
   */
  @Column({
    name: 'order_info',
    type: 'jsonb',
    default: () => "'{}'"
  })
  orderInfo: Record<string, any>;

  /**
   * Thông tin sản phẩm
   * Thuộc tính này không được lưu trong cơ sở dữ liệu
   * Được sử dụng để lưu trữ kết quả join từ Product
   */
  product?: any;

  /**
   * Thông tin đơn hàng
   * Thuộc tính này không được lưu trong cơ sở dữ liệu
   * Được sử dụng để lưu trữ kết quả join từ MarketOrder
   */
  order?: any;
}
