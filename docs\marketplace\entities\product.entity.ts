import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProductCategory, ProductStatus } from '../enums';

/**
 * Bảng sản phẩm trong chợ
 */
@Entity('products')
export class Product {
  /** Mã định danh sản phẩm */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /** Loại sản phẩm (product_category) */
  @Column({
    name: 'category',
    type: 'enum',
    enum: ProductCategory,
    nullable: true,
  })
  category: ProductCategory | null;

  /** Tên sản phẩm */
  @Column({
    name: 'name',
    type: 'varchar',
    length: 500,
    comment: 'Tên sản phẩm',
  })
  name: string;

  /** <PERSON>ô tả */
  @Column({
    name: 'description',
    type: 'text',
    comment: 'Mô tả'
  })
  description: string;

  /** Giá point niêm yết */
  @Column({
    name: 'listed_price',
    type: 'bigint',
    default: 0,
    nullable: false,
    comment: 'Giá point niêm yết',
  })
  listedPrice: number;

  /** Giá point sau giảm (giá tính tiền) */
  @Column({
    name: 'discounted_price',
    type: 'bigint',
    default: 0,
    nullable: false,
    comment: 'Giá point sau giảm (giá tính tiền)',
  })
  discountedPrice: number;

  /** Ảnh sản phẩm (mảng đường dẫn và vị trí) */
  @Column({
    name: 'images',
    type: 'jsonb',
    default: () => "'[]'::jsonb",
    nullable: false,
  })
  images: Array<{ key: string; position: number }>;

  /** Hướng dẫn sử dụng */
  @Column({
    name: 'user_manual',
    type: 'varchar',
    length: 100,
    nullable: true
  })
  userManual: string | null;

  /** Thông tin chi tiết */
  @Column({
    name: 'detail',
    type: 'varchar',
    length: 100,
    nullable: true
  })
  detail: string | null;

  /** Trạng thái sản phẩm (enum product_status) */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ProductStatus,
    default: ProductStatus.DRAFT,
    nullable: false,
  })
  status: ProductStatus;

  /** Thời gian tạo */
  @Column({
    name: 'created_at',
    type: 'bigint',
    comment: 'Thời gian tạo',
    nullable: true,
  })
  createdAt: number;

  /** Thời gian cập nhật */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    comment: 'Thời gian cập nhật',
    nullable: true,
  })
  updatedAt: number;

  /** Nhân viên phụ trách (FK employees.id) */
  @Column({
    name: 'employee_id',
    type: 'integer',
    nullable: true
  })
  employeeId: number | null;

  /** Người dùng tạo sản phẩm (FK users.id) */
  @Column({
    name: 'user_id',
    type: 'integer',
    nullable: true
  })
  userId: number | null;

  /** id của tài nguyên gốc */
  @Column({
    name: 'source_id',
    type: 'uuid',
    nullable: true,
    comment: 'id của tài nguyên gốc',
  })
  sourceId: string | null;

  // Các trường không lưu trong database, chỉ dùng để chứa dữ liệu từ query
  /** Tổng số lượng đã bán (từ market_order_line) */
  soldCount?: number;

  /** Có thể mua sản phẩm này không */
  canPurchase?: boolean;
}
