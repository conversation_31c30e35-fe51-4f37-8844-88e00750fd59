import { Injectable, Logger } from '@nestjs/common';
import { MarketOrder, MarketOrderLine } from '../entities';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { MarketOrderLineRepository } from '../repositories/market-order-line.repository';
import { User } from '@modules/user/entities';

/**
 * Helper class cho việc xử lý và chuyển đổi dữ liệu đơn hàng
 */
@Injectable()
export class OrderHelper {
  private readonly logger = new Logger(OrderHelper.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly orderLineRepository: MarketOrderLineRepository
  ) {}
  /**
   * Trích xuất danh sách OrderLine từ kết quả join thủ công
   * @param order Order với các trường đã được join
   * @returns Danh sách OrderLine đã được xử lý
   */
  private extractOrderLines(order: any): MarketOrderLine[] {
    const orderLines: MarketOrderLine[] = [];

    // Kiểm tra xem có dữ liệu orderLines từ join không
    if (!order['orderLines.id']) {
      return orderLines;
    }

    // Nếu chỉ có một orderLine
    if (!Array.isArray(order['orderLines.id'])) {
      const orderLine = {
        id: order['orderLines.id'],
        orderId: order.id,
        productId: order['orderLines.product_id'],
        quantity: order['orderLines.quantity'],
        point: order['orderLines.point'],
        platformFeePercent: order['orderLines.platformFeePercent'],
        sellerReceivePrice: order['orderLines.sellerReceivePrice'],
        product: {
          id: order['product.id'],
          name: order['product.name'],
          description: order['product.description'],
          listedPrice: order['product.listed_price'],
          discountedPrice: order['product.discounted_price'],
          images: order['product.images'],
          status: order['product.status'],
          user: {
            id: order['user.id'],
            fullName: order['user.full_name'],
            email: order['user.email'],
            avatar: order['user.avatar']
          },
          employee: {
            id: order['employee.id'],
            fullName: order['employee.full_name'],
            email: order['employee.email'],
            avatar: order['employee.avatar']
          }
        }
      } as unknown as MarketOrderLine;

      orderLines.push(orderLine);
    }
    // Nếu có nhiều orderLine
    else {
      for (let i = 0; i < order['orderLines.id'].length; i++) {
        const orderLine = {
          id: order['orderLines.id'][i],
          orderId: order.id,
          productId: order['orderLines.product_id'][i],
          quantity: order['orderLines.quantity'][i],
          point: order['orderLines.point'][i],
          platformFeePercent: order['orderLines.platformFeePercent']?.[i],
          sellerReceivePrice: order['orderLines.sellerReceivePrice']?.[i],
          product: {
            id: order['product.id'][i],
            name: order['product.name'][i],
            description: order['product.description'][i],
            listedPrice: order['product.listed_price'][i],
            discountedPrice: order['product.discounted_price'][i],
            images: order['product.images'][i],
            status: order['product.status'][i],
            user: {
              id: order['user.id']?.[i],
              fullName: order['user.full_name']?.[i],
              email: order['user.email']?.[i],
              avatar: order['user.avatar']?.[i]
            },
            employee: {
              id: order['employee.id']?.[i],
              fullName: order['employee.full_name']?.[i],
              email: order['employee.email']?.[i],
              avatar: order['employee.avatar']?.[i]
            }
          }
        } as unknown as MarketOrderLine;

        orderLines.push(orderLine);
      }
    }

    return orderLines;
  }
  /**
   * Chuyển đổi entity MarketOrder thành DTO cho admin
   * @param order Entity MarketOrder với các OrderLine đã được join
   * @returns Đối tượng DTO cho admin
   */
  async mapToOrderResponseDto(order: MarketOrder): Promise<any> {
    if (!order) {
      throw new Error('Cannot map null order to DTO');
    }

    this.logger.debug(`Mapping order to DTO: ${JSON.stringify(order)}`);

    // Lấy thông tin chi tiết đơn hàng trực tiếp từ repository
    this.logger.debug(`Fetching order lines for order ID: ${order.id}`);
    const orderLinesFromRepo = await this.orderLineRepository.findByOrderId(order.id);
    this.logger.debug(`Found ${orderLinesFromRepo.length} order lines from repository`);

    // Map order lines to DTOs
    const items = orderLinesFromRepo.map(line => this.mapToOrderLineResponseDto(line)).filter(item => item !== null) || [];
    this.logger.debug(`Mapped ${items.length} order lines to DTOs`);

    // Lấy thông tin người dùng trực tiếp từ UserRepository
    const userId = order.userId;
    this.logger.debug(`Fetching user information for userId: ${userId}`);

    // Lấy thông tin người dùng từ repository
    let userInfo: User | null = null;
    try {
      userInfo = await this.userRepository.findById(userId);
      this.logger.debug(`User info from repository: ${userInfo ? JSON.stringify(userInfo) : 'null'}`);
    } catch (error) {
      this.logger.error(`Error fetching user info: ${error.message}`, error.stack);
    }

    // Tạo thông tin người dùng cho DTO
    const user = {
      id: userId,
      name: userInfo?.fullName || 'Unknown',
      email: userInfo?.email || '<EMAIL>',
      avatar: userInfo?.avatar || null
    };

    // Log user info for debugging
    this.logger.debug(`Final user info: id=${user.id}, name=${user.name}, email=${user.email}`);

    return {
      id: order.id,
      user,
      orderLines: items,
      totalAmount: order.totalPoint,
      createdAt: order.createdAt,
    };
  }

  /**
   * Chuyển đổi entity MarketOrder thành DTO cho user
   * @param order Entity MarketOrder với các OrderLine đã được join
   * @returns Đối tượng DTO cho user
   */
  mapToUserOrderResponseDto(order: MarketOrder): any {
    if (!order) {
      throw new Error('Cannot map null order to DTO');
    }

    // Lấy danh sách order lines từ kết quả join thủ công
    const orderLines = this.extractOrderLines(order);

    const items = orderLines.map(line => this.mapToUserOrderLineResponseDto(line)) || [];

    return {
      id: order.id,
      totalPoint: order.totalPoint,
      items,
      createdAt: order.createdAt,
    };
  }

  /**
   * Chuyển đổi entity MarketOrderLine thành DTO cho admin
   * @param orderLine Entity MarketOrderLine
   * @returns Đối tượng DTO cho admin
   */
  mapToOrderLineResponseDto(orderLine: any): any {
    if (!orderLine) {
      this.logger.warn('Cannot map null order line to DTO');
      return null;
    }

    if (!orderLine.product) {
      this.logger.warn(`Order line ID ${orderLine.id} has no product information`);
      return {
        id: orderLine.id,
        productId: orderLine.productId,
        productName: orderLine.productName || 'Unknown Product',
        quantity: orderLine.quantity || 0,
        point: orderLine.point || 0,
        seller: {
          id: 0,
          name: 'Unknown',
          type: 'user',
        }
      };
    }

    const product = orderLine.product;
    this.logger.debug(`Mapping product: ${JSON.stringify(product)}`);

    let seller = {
      id: 0,
      name: 'Unknown',
      type: 'user',
    };

    // Kiểm tra dữ liệu user từ join thủ công
    if (product.user && product.user.id) {
      seller = {
        id: product.user.id,
        name: product.user.fullName || product.user.email || 'Unknown',
        type: 'user',
      };
    }
    // Kiểm tra dữ liệu employee từ join thủ công
    else if (product.employee && product.employee.id) {
      seller = {
        id: product.employee.id,
        name: product.employee.fullName || product.employee.email || 'Unknown',
        type: 'employee',
      };
    }

    return {
      id: orderLine.id,
      productId: product.id,
      productName: product.name,
      point: orderLine.point,
      quantity: orderLine.quantity,
      platformFeePercent: orderLine.platformFeePercent,
      sellerReceivePrice: orderLine.sellerReceivePrice,
      seller,
    };
  }

  /**
   * Chuyển đổi entity MarketOrderLine thành DTO cho user
   * @param orderLine Entity MarketOrderLine
   * @returns Đối tượng DTO cho user
   */
  mapToUserOrderLineResponseDto(orderLine: any): any {
    if (!orderLine || !orderLine.product) {
      throw new Error('Cannot map null order line to DTO');
    }

    const product = orderLine.product;
    let sellerName = 'Unknown';

    // Kiểm tra dữ liệu user từ join thủ công
    if (product.user && product.user.fullName) {
      sellerName = product.user.fullName || product.user.email || 'Unknown';
    }
    // Kiểm tra dữ liệu employee từ join thủ công
    else if (product.employee && product.employee.fullName) {
      sellerName = product.employee.fullName || product.employee.email || 'Unknown';
    }

    return {
      id: orderLine.id,
      productId: product.id,
      productName: product.name,
      point: orderLine.point,
      quantity: orderLine.quantity,
      sellerName,
    };
  }
}
