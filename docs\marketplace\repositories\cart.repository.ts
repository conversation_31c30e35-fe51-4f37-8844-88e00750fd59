import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Cart } from '../entities/cart.entity';
import { CartItem } from '../entities/cart-item.entity';
import { Product } from '../entities/product.entity';
import { ProductStatus } from '../enums/product-status.enum';
import { QueryCartDto } from '../user/dto';
import { User } from '@modules/user/entities';
import { Employee } from '@modules/employee/entities';
import { CartQueryDto, CartSortField } from '../admin/dto';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Kho lưu trữ tùy chỉnh cho giỏ hàng
 */
@Injectable()
export class CartRepository extends Repository<Cart> {
  private readonly logger = new Logger(CartRepository.name);

  constructor(
    private dataSource: DataSource
  ) {
    super(Cart, dataSource.createEntityManager());
  }

  /**
   * Tạo truy vấn cơ bản cho giỏ hàng
   * @returns QueryBuilder cho giỏ hàng
   */
  private createBaseQuery(): SelectQueryBuilder<Cart> {
    return this.createQueryBuilder('cart');
  }

  /**
   * Tìm giỏ hàng theo ID người dùng
   * Chỉ lấy các sản phẩm có trạng thái APPROVED
   * @param userId ID người dùng
   * @returns Giỏ hàng hoặc null
   */
  async findByUserId(userId: number): Promise<Cart | null> {
    try {
      // Tìm giỏ hàng với các trường created_at và updated_at
      const cart = await this.createBaseQuery()
        .select(['cart.id', 'cart.user_id', 'cart.created_at', 'cart.updated_at'])
        .where('cart.user_id = :userId', { userId })
        .getOne();

      if (!cart) {
        return null;
      }

      // Tìm các mục trong giỏ hàng với thông tin sản phẩm
      const cartItemsQuery = this.dataSource
        .createQueryBuilder()
        .select('cartItems.id', 'cartItems_id')
        .addSelect('cartItems.quantity', 'cartItems_quantity')
        .addSelect('cartItems.product_id', 'cartItems_product_id')
        .addSelect('product.id', 'product_id')
        .addSelect('product.name', 'product_name')
        .addSelect('product.description', 'product_description')
        .addSelect('product.listed_price', 'product_listed_price')
        .addSelect('product.discounted_price', 'product_discounted_price')
        .addSelect('product.images', 'product_images')
        .addSelect('product.status', 'product_status')
        .addSelect('user.id', 'user_id')
        .addSelect('user.full_name', 'user_full_name')
        .addSelect('user.email', 'user_email')
        .addSelect('user.avatar', 'user_avatar')
        .addSelect('employee.id', 'employee_id')
        .addSelect('employee.full_name', 'employee_full_name')
        .addSelect('employee.email', 'employee_email')
        .from('cart_items', 'cartItems')
        .leftJoin('products', 'product', 'product.id = cartItems.product_id')
        .leftJoin('users', 'user', 'user.id = product.user_id')
        .leftJoin('employees', 'employee', 'employee.id = product.employee_id')
        .where('cartItems.cart_id = :cartId', { cartId: cart.id })
        .andWhere('product.status = :status', { status: ProductStatus.APPROVED });

      const cartItems = await cartItemsQuery.getRawMany();

      // Gán kết quả vào cart
      if (cartItems && cartItems.length > 0) {
        cart.cartItems = cartItems;
      } else {
        cart.cartItems = [];
      }

      // Log thông tin để debug
      this.logger.debug(`Cart found for user ${userId}: ID=${cart.id}, created_at=${cart.created_at}, updated_at=${cart.updated_at}`);

      return cart;
    } catch (error) {
      this.logger.error(`Error finding cart by user ID ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm hoặc tạo giỏ hàng cho người dùng
   * @param userId ID người dùng
   * @returns Giỏ hàng
   */
  async findOrCreateCart(userId: number): Promise<Cart> {
    try {
      let cart = await this.findOne({ where: { user_id: userId } });

      if (!cart) {
        // Tạo giỏ hàng mới với thời gian hiện tại
        const now = Date.now();
        cart = this.create({
          user_id: userId,
          created_at: now,
          updated_at: now
        });
        await this.save(cart);
      }

      return cart;
    } catch (error) {
      this.logger.error(`Error finding or creating cart for user ID ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm giỏ hàng theo ID người dùng với các bộ lọc
   * Chỉ lấy các sản phẩm có trạng thái APPROVED
   * @param userId ID người dùng
   * @param queryDto DTO truy vấn
   * @returns Giỏ hàng với các sản phẩm đã lọc
   */
  async findCartWithFilters(userId: number, queryDto: QueryCartDto): Promise<Cart | null> {
    try {
      const { search, sortBy, sortDirection } = queryDto;

      // Tìm giỏ hàng với các trường created_at và updated_at
      const cart = await this.createBaseQuery()
        .select(['cart.id', 'cart.user_id', 'cart.created_at', 'cart.updated_at'])
        .where('cart.user_id = :userId', { userId })
        .getOne();

      if (!cart) {
        return null;
      }

      // Tìm các mục trong giỏ hàng với thông tin sản phẩm và bộ lọc
      const cartItemsQuery = this.dataSource
        .createQueryBuilder()
        .select('cartItems.id', 'cartItems_id')
        .addSelect('cartItems.quantity', 'cartItems_quantity')
        .addSelect('cartItems.product_id', 'cartItems_product_id')
        .addSelect('product.id', 'product_id')
        .addSelect('product.name', 'product_name')
        .addSelect('product.description', 'product_description')
        .addSelect('product.listed_price', 'product_listed_price')
        .addSelect('product.discounted_price', 'product_discounted_price')
        .addSelect('product.images', 'product_images')
        .addSelect('product.status', 'product_status')
        .addSelect('user.id', 'user_id')
        .addSelect('user.full_name', 'user_full_name')
        .addSelect('user.email', 'user_email')
        .addSelect('user.avatar', 'user_avatar')
        .addSelect('employee.id', 'employee_id')
        .addSelect('employee.full_name', 'employee_full_name')
        .addSelect('employee.email', 'employee_email')
        .from('cart_items', 'cartItems')
        .leftJoin('products', 'product', 'product.id = cartItems.product_id')
        .leftJoin('users', 'user', 'user.id = product.user_id')
        .leftJoin('employees', 'employee', 'employee.id = product.employee_id')
        .where('cartItems.cart_id = :cartId', { cartId: cart.id })
        .andWhere('product.status = :status', { status: ProductStatus.APPROVED });

      // Áp dụng bộ lọc tìm kiếm
      if (search) {
        cartItemsQuery.andWhere('LOWER(product.name) LIKE LOWER(:search)', { search: `%${search}%` });
      }

      // Sắp xếp
      switch (sortBy) {
        case 'productName':
          cartItemsQuery.orderBy('product.name', sortDirection);
          break;
        case 'discountedPrice':
          cartItemsQuery.orderBy('product.discounted_price', sortDirection);
          break;
        case 'quantity':
          cartItemsQuery.orderBy('cartItems.quantity', sortDirection);
          break;
        case 'createdAt':
        default:
          // Sắp xếp theo thời gian tạo của sản phẩm
          cartItemsQuery.orderBy('product.created_at', sortDirection);
          break;
      }

      const cartItems = await cartItemsQuery.getRawMany();

      // Gán kết quả vào cart
      if (cartItems && cartItems.length > 0) {
        cart.cartItems = cartItems;
      } else {
        cart.cartItems = [];
      }

      return cart;
    } catch (error) {
      this.logger.error(`Error finding cart with filters for user ID ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa giỏ hàng
   * @param cartId ID giỏ hàng
   * @returns true nếu xóa thành công
   */
  async removeCart(cartId: number): Promise<boolean> {
    try {
      this.logger.debug(`Xóa giỏ hàng với ID: ${cartId}`);
      const result = await this.delete(cartId);
      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;
      this.logger.debug(`Kết quả xóa giỏ hàng với ID ${cartId}: ${success ? 'thành công' : 'không thành công'}`);
      return success;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa giỏ hàng với ID ${cartId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Tính tổng giá trị giỏ hàng
   * @param userId ID người dùng
   * @returns Tổng giá trị giỏ hàng
   */
  async calculateCartTotal(userId: number): Promise<number> {
    const result = await this.dataSource
      .createQueryBuilder()
      .select('SUM(product.discounted_price * cart_items.quantity)', 'total')
      .from('carts', 'cart')
      .leftJoin('cart_items', 'cart_items', 'cart_items.cart_id = cart.id')
      .leftJoin('products', 'product', 'cart_items.product_id = product.id')
      .where('cart.user_id = :userId', { userId })
      .andWhere('product.status = :status', { status: ProductStatus.APPROVED })
      .getRawOne();

    return result?.total ? Number(result.total) : 0;
  }

  /**
   * Tìm giỏ hàng theo ID
   * @param id ID giỏ hàng
   * @returns Giỏ hàng hoặc null
   */
  async findById(id: number): Promise<Cart | null> {
    try {
      // Tạo truy vấn cơ bản
      const query = this.createBaseQuery();

      // Thêm điều kiện tìm kiếm theo ID
      query.where('cart.id = :id', { id });

      // Thêm các join với điều kiện rõ ràng
      query.leftJoin('users', 'cartUser', 'cartUser.id = cart.user_id');
      query.leftJoin('cart_items', 'cartItems', 'cartItems.cart_id = cart.id');
      query.leftJoin('products', 'product', 'product.id = cartItems.product_id');
      query.leftJoin('users', 'user', 'user.id = product.user_id');
      query.leftJoin('employees', 'employee', 'employee.id = product.employee_id');

      // Sử dụng alias rõ ràng cho các trường để tránh nhầm lẫn
      query.addSelect('cartUser.id', 'cartUser_id');
      query.addSelect('cartUser.full_name', 'cartUser_full_name');
      query.addSelect('cartUser.email', 'cartUser_email');
      query.addSelect('cartUser.avatar', 'cartUser_avatar');

      // Các trường khác
      query.addSelect([
        'cartItems.id', 'cartItems.product_id', 'cartItems.quantity',
        'product.id', 'product.name', 'product.description', 'product.listed_price', 'product.discounted_price', 'product.images', 'product.status',
        'user.id', 'user.full_name', 'user.email', 'user.avatar',
        'employee.id', 'employee.full_name', 'employee.email'
      ]);

      // Thực hiện truy vấn
      const cart = await query.getOne();

      // Log thông tin để debug
      if (cart) {
        this.logger.debug(`Found cart with ID ${id}, User ID: ${cart.userId}`);
        // Log các trường của cart để debug
        this.logger.debug(`Cart keys: ${Object.keys(cart).join(', ')}`);
        // Log thông tin người dùng để debug - sử dụng bracket notation để tránh lỗi TypeScript
        this.logger.debug(`User info: cartUser_id=${cart['cartUser_id']}, cartUser_full_name=${cart['cartUser_full_name']}, cartUser_email=${cart['cartUser_email']}`);
        // Không thể truy cập cart.cartItems vì đã loại bỏ quan hệ
        this.logger.debug(`Cart found successfully`);
      } else {
        this.logger.debug(`No cart found with ID ${id}`);
      }

      return cart;
    } catch (error) {
      this.logger.error(`Error finding cart by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm danh sách giỏ hàng cho admin với phân trang, tìm kiếm, lọc và sắp xếp
   * @param queryDto DTO truy vấn từ admin
   * @returns Danh sách giỏ hàng phân trang
   */
  async findAdminCarts(queryDto: CartQueryDto): Promise<PaginatedResult<Cart>> {
    try {
      const { page = 1, limit = 10, search, userId, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;
      const skip = (page - 1) * limit;

      // Tạo truy vấn cơ bản
      const query = this.createBaseQuery();

      // Thêm các join với điều kiện rõ ràng
      query.leftJoin('users', 'cartUser', 'cartUser.id = cart.user_id');

      // Chỉ join cartItems nếu cần thiết
      query.leftJoin('cart_items', 'cartItems', 'cartItems.cart_id = cart.id');

      // Chỉ join product và các bảng liên quan nếu có cartItems
      query.leftJoin('products', 'product', 'product.id = cartItems.product_id');
      query.leftJoin('users', 'user', 'user.id = product.user_id');
      query.leftJoin('employees', 'employee', 'employee.id = product.employee_id');

      // Sử dụng alias rõ ràng cho các trường để tránh nhầm lẫn
      query.addSelect('cartUser.id', 'cartUser_id');
      query.addSelect('cartUser.full_name', 'cartUser_full_name');
      query.addSelect('cartUser.email', 'cartUser_email');
      query.addSelect('cartUser.avatar', 'cartUser_avatar');

      // Các trường khác
      query.addSelect([
        'cartItems.id', 'cartItems.product_id', 'cartItems.quantity',
        'product.id', 'product.name', 'product.description', 'product.listed_price', 'product.discounted_price', 'product.images', 'product.status',
        'user.id', 'user.full_name', 'user.email', 'user.avatar',
        'employee.id', 'employee.full_name', 'employee.email'
      ]);

      // Áp dụng bộ lọc
      if (userId) {
        query.andWhere('cart.user_id = :userId', { userId });
      }

      // Áp dụng tìm kiếm
      if (search) {
        query.andWhere(
          '(LOWER(cartUser.full_name) LIKE LOWER(:search) OR LOWER(cartUser.email) LIKE LOWER(:search))',
          { search: `%${search}%` }
        );
      }

      // Sắp xếp
      if (sortBy === CartSortField.UPDATED_AT) {
        query.orderBy('cart.updated_at', sortDirection);
      } else if (sortBy === CartSortField.TOTAL_VALUE) {
        // Sắp xếp theo tổng giá trị - có thể cần tính toán
        query.orderBy('cart.created_at', sortDirection); // Fallback tạm thời
      } else if (sortBy === CartSortField.NAME || sortBy === CartSortField.PRICE) {
        // Các trường này có thể không áp dụng trực tiếp cho cart
        query.orderBy('cart.created_at', sortDirection); // Fallback tạm thời
      } else {
        // Mặc định sắp xếp theo createdAt
        query.orderBy('cart.created_at', sortDirection);
      }

      // Thực hiện phân trang
      query.skip(skip).take(limit);

      // Lấy kết quả và tổng số
      // Tạo một truy vấn đếm riêng biệt để đảm bảo chính xác
      const countQuery = this.createBaseQuery();

      // Áp dụng các điều kiện lọc tương tự như truy vấn chính
      if (userId) {
        countQuery.andWhere('cart.user_id = :userId', { userId });
      }

      if (search) {
        // Để đếm chính xác, chúng ta cần join với user
        countQuery.leftJoin('users', 'countUser', 'countUser.id = cart.user_id');
        countQuery.andWhere(
          '(LOWER(countUser.full_name) LIKE LOWER(:search) OR LOWER(countUser.email) LIKE LOWER(:search))',
          { search: `%${search}%` }
        );
      }

      // Thực hiện truy vấn
      const [items, total] = await Promise.all([
        query.getMany(),
        countQuery.getCount(),
      ]);

      // Log chi tiết để debug
      this.logger.debug(`Found ${items.length} carts out of ${total} total`);

      // Log SQL query để debug
      this.logger.debug(`SQL Query: ${query.getSql()}`);

      // Log các tham số truy vấn
      this.logger.debug(`Query parameters: ${JSON.stringify(query.getParameters())}`);

      // Log thông tin chi tiết về các item tìm thấy
      if (items.length > 0) {
        this.logger.debug(`First cart ID: ${items[0].id}, User ID: ${items[0].userId}`);
        // Log các trường của cart để debug
        this.logger.debug(`First cart keys: ${Object.keys(items[0]).join(', ')}`);
        // Log thông tin người dùng để debug - sử dụng bracket notation để tránh lỗi TypeScript
        this.logger.debug(`User info: cartUser_id=${items[0]['cartUser_id']}, cartUser_full_name=${items[0]['cartUser_full_name']}, cartUser_email=${items[0]['cartUser_email']}`);
        // Không thể truy cập cart.cartItems vì đã loại bỏ quan hệ
        this.logger.debug(`Cart found successfully`);
      }

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding admin carts: ${error.message}`, error.stack);
      throw error;
    }
  }
}
