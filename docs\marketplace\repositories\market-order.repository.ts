import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { MarketOrder, MarketOrderLine } from '@modules/marketplace/entities';
import { Product } from '../entities/product.entity';
import { QueryPurchaseHistoryDto } from '../user/dto';
import { OrderQueryDto } from '../admin/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { User } from '@modules/user/entities';
import { Employee } from '@modules/employee/entities';

/**
 * <PERSON>ho lưu trữ tùy chỉnh cho đơn hàng
 */
@Injectable()
export class MarketOrderRepository extends Repository<MarketOrder> {
  private readonly logger = new Logger(MarketOrderRepository.name);

  constructor(private dataSource: DataSource) {
    super(MarketOrder, dataSource.createEntityManager());
  }

  /**
   * Tạo truy vấn cơ bản cho đơn hàng
   * @returns QueryBuilder cho đơn hàng
   */
  private createBaseQuery(): SelectQueryBuilder<MarketOrder> {
    return this.createQueryBuilder('order');
  }

  /**
   * Tìm đơn hàng theo ID
   * @param id ID đơn hàng
   * @returns Đơn hàng hoặc null
   */
  async findById(id: number): Promise<MarketOrder | null> {
    return this.createBaseQuery()
      .where('order.id = :id', { id })
      .getOne();
  }

  /**
   * Kiểm tra xem bảng có dữ liệu không và trả về thông tin cơ bản
   * Phương thức này hữu ích cho việc debug
   * @returns Thông tin cơ bản về dữ liệu trong bảng
   */
  async checkTableData(): Promise<{ count: number; firstRecord?: any }> {
    try {
      // Đếm tổng số bản ghi
      const count = await this.createBaseQuery().getCount();

      // Nếu có dữ liệu, lấy bản ghi đầu tiên
      let firstRecord: any = null;
      if (count > 0) {
        try {
          firstRecord = await this.createBaseQuery()
            .select(['order.id', 'order.userId', 'order.totalPoint', 'order.createdAt'])
            .orderBy('order.id', 'ASC')
            .limit(1)
            .getRawOne();
        } catch (err) {
          this.logger.error(`Error getting first record: ${err.message}`);
          // Nếu có lỗi, vẫn giữ firstRecord là null
        }
      }

      this.logger.debug(`Table market_order has ${count} records`);
      if (firstRecord) {
        this.logger.debug(`First record: ${JSON.stringify(firstRecord)}`);
      }

      return { count, firstRecord };
    } catch (error) {
      this.logger.error(`Error checking table data: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm đơn hàng theo ID với chi tiết đơn hàng
   * @param id ID đơn hàng
   * @returns Đơn hàng với chi tiết hoặc null
   */
  async findByIdWithDetails(id: number): Promise<MarketOrder | null> {
    // Tìm đơn hàng
    return this.createBaseQuery()
      .leftJoin('market_order_line', 'orderLines', 'orderLines.order_id = order.id')
      .leftJoin('products', 'product', 'product.id = orderLines.product_id')
      .addSelect([
        'orderLines.id', 'orderLines.product_id', 'orderLines.quantity', 'orderLines.point',
        'product.id', 'product.name', 'product.description', 'product.listed_price',
        'product.discounted_price', 'product.images', 'product.status'
      ])
      .where('order.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm lịch sử mua hàng của người dùng
   * @param userId ID người dùng
   * @param queryDto DTO truy vấn
   * @returns Danh sách đơn hàng phân trang
   */
  async findPurchaseHistory(
    userId: number,
    queryDto: QueryPurchaseHistoryDto,
  ): Promise<PaginatedResult<MarketOrder>> {
    const { page, limit, sortDirection } = queryDto;
    const skip = (page - 1) * limit;

    this.logger.log(`Finding purchase history for user ${userId}`);

    // Tìm đơn hàng của người dùng
    const query = this.createBaseQuery()
      .where('order.userId = :userId', { userId })
      .leftJoin('market_order_line', 'orderLines', 'orderLines.order_id = order.id')
      .leftJoin('products', 'product', 'product.id = orderLines.product_id')
      .addSelect([
        'orderLines.id', 'orderLines.product_id', 'orderLines.quantity', 'orderLines.point',
        'product.id', 'product.name', 'product.description', 'product.listed_price',
        'product.discounted_price', 'product.images', 'product.status'
      ])
      .orderBy('order.createdAt', sortDirection)
      .skip(skip)
      .take(limit);

    const [orders, totalItems] = await query.getManyAndCount();

    if (orders.length > 0) {
      this.logger.log(`Found ${orders.length} orders for user ${userId}`);

      try {
        // Lấy danh sách ID đơn hàng
        const orderIds = orders.map(order => order.id);

        // Tìm chi tiết đơn hàng
        const orderLinesQuery = this.dataSource
          .createQueryBuilder()
          .select('ol')
          .from(MarketOrderLine, 'ol')
          .where('ol.order_id IN (:...orderIds)', { orderIds })
          .addSelect(['ol.id', 'ol.product_id', 'ol.quantity', 'ol.point', 'ol.product_name',
                     'ol.platform_fee_percent', 'ol.seller_receive_price', 'ol.created_at', 'ol.order_info']);

        const orderLines = await orderLinesQuery.getMany();
        this.logger.log(`Found ${orderLines.length} order lines for orders ${orderIds.join(', ')}`);

        // Log thông tin order_info để debug
        if (orderLines.length > 0) {
          orderLines.forEach(line => {
            if (line.orderInfo) {
              this.logger.debug(`Order line ${line.id} has order_info: ${typeof line.orderInfo === 'string' ? line.orderInfo : JSON.stringify(line.orderInfo)}`);

              // Kiểm tra xem order_info có phải là chuỗi không
              if (typeof line.orderInfo === 'string') {
                try {
                  const orderInfoObj = JSON.parse(line.orderInfo);
                  this.logger.debug(`Parsed order_info: ${JSON.stringify(orderInfoObj)}`);

                  // Cập nhật trường orderInfo
                  line.orderInfo = orderInfoObj;
                } catch (error) {
                  this.logger.error(`Failed to parse order_info: ${error.message}`);
                }
              }
            } else {
              this.logger.debug(`Order line ${line.id} does not have order_info`);
            }
          });
        }

        if (orderLines.length > 0) {
          // Lấy danh sách ID sản phẩm
          const productIds = [...new Set(orderLines.map(line => line.productId))];

          // Tìm thông tin sản phẩm và người bán
          const productsQuery = this.dataSource
            .createQueryBuilder()
            .select('p')
            .from(Product, 'p')
            .leftJoin(User, 'u', 'u.id = p.user_id')
            .leftJoin(Employee, 'e', 'e.id = p.employee_id')
            .where('p.id IN (:...productIds)', { productIds });

          // Lấy danh sách userId và employeeId từ orderLines
          const userIds: number[] = [];
          const employeeIds: number[] = [];

          // Lấy thông tin từ order_info
          orderLines.forEach(line => {
            if (line.orderInfo) {
              let orderInfoObj = line.orderInfo;
              if (typeof orderInfoObj === 'string') {
                try {
                  orderInfoObj = JSON.parse(orderInfoObj);
                } catch (error) {
                  this.logger.error(`Failed to parse order_info: ${error.message}`);
                }
              }

              // Trường hợp 1: order_info.product.seller
              if (orderInfoObj.product && orderInfoObj.product.seller) {
                const seller = orderInfoObj.product.seller;
                if (seller.type === 'user' && seller.id && typeof seller.id === 'number') {
                  userIds.push(seller.id);
                } else if (seller.type === 'admin' && seller.id && typeof seller.id === 'number') {
                  employeeIds.push(seller.id);
                }
              }

              // Trường hợp 2: order_info.seller
              if (orderInfoObj.seller) {
                const seller = orderInfoObj.seller;
                if (seller.type === 'user' && seller.id && typeof seller.id === 'number') {
                  userIds.push(seller.id);
                } else if (seller.type === 'admin' && seller.id && typeof seller.id === 'number') {
                  employeeIds.push(seller.id);
                }
              }
            }
          });

          // Thêm các trường cần thiết
          productsQuery
            .addSelect(['p.id', 'p.name', 'p.description', 'p.userId', 'p.employeeId'])
            .addSelect(['u.id', 'u.email', 'u.fullName', 'u.avatar', 'u.phoneNumber'])
            .addSelect(['e.id', 'e.email', 'e.fullName', 'e.avatar', 'e.phoneNumber']);

          this.logger.debug('SQL Query for products: ' + productsQuery.getSql());

          // Lấy thông tin người dùng và nhân viên từ userIds và employeeIds
          if (userIds.length > 0 || employeeIds.length > 0) {
            // Đảm bảo userIds và employeeIds không rỗng khi truyền vào truy vấn
            if (userIds.length === 0) userIds.push(0);
            if (employeeIds.length === 0) employeeIds.push(0);
            this.logger.debug(`Found ${userIds.length} userIds and ${employeeIds.length} employeeIds in order_info`);

            // Lấy thông tin người dùng (chỉ khi có userIds thật sự)
            if (userIds.length > 0 && userIds[0] !== 0) {
              const usersQuery = this.dataSource
                .createQueryBuilder()
                .select('u')
                .from(User, 'u')
                .where('u.id IN (:...userIds)', { userIds })
                .addSelect(['u.id', 'u.email', 'u.fullName', 'u.avatar', 'u.phoneNumber']);

              const users = await usersQuery.getMany();
              this.logger.debug(`Found ${users.length} users from userIds`);

              // Gán thông tin người dùng vào orderLines
              orderLines.forEach(line => {
                if (line.orderInfo) {
                  let orderInfoObj = line.orderInfo;
                  if (typeof orderInfoObj === 'string') {
                    try {
                      orderInfoObj = JSON.parse(orderInfoObj);
                    } catch (error) {
                      return;
                    }
                  }

                  let sellerId = null;

                  // Trường hợp 1: order_info.product.seller
                  if (orderInfoObj.product && orderInfoObj.product.seller && orderInfoObj.product.seller.type === 'user') {
                    sellerId = orderInfoObj.product.seller.id;
                  }
                  // Trường hợp 2: order_info.seller
                  else if (orderInfoObj.seller && orderInfoObj.seller.type === 'user') {
                    sellerId = orderInfoObj.seller.id;
                  }

                  if (sellerId) {
                    const user = users.find(u => u.id === sellerId);
                    if (user) {
                      if (!line.product) {
                        line.product = {};
                      }
                      line.product.user = user;
                    }
                  }
                }
              });
            }

            // Lấy thông tin nhân viên (chỉ khi có employeeIds thật sự)
            if (employeeIds.length > 0 && employeeIds[0] !== 0) {
              const employeesQuery = this.dataSource
                .createQueryBuilder()
                .select('e')
                .from(Employee, 'e')
                .where('e.id IN (:...employeeIds)', { employeeIds })
                .addSelect(['e.id', 'e.email', 'e.fullName', 'e.avatar', 'e.phoneNumber']);

              const employees = await employeesQuery.getMany();
              this.logger.debug(`Found ${employees.length} employees from employeeIds`);

              // Gán thông tin nhân viên vào orderLines
              orderLines.forEach(line => {
                if (line.orderInfo) {
                  let orderInfoObj = line.orderInfo;
                  if (typeof orderInfoObj === 'string') {
                    try {
                      orderInfoObj = JSON.parse(orderInfoObj);
                    } catch (error) {
                      return;
                    }
                  }

                  let sellerId = null;

                  // Trường hợp 1: order_info.product.seller
                  if (orderInfoObj.product && orderInfoObj.product.seller && orderInfoObj.product.seller.type === 'admin') {
                    sellerId = orderInfoObj.product.seller.id;
                  }
                  // Trường hợp 2: order_info.seller
                  else if (orderInfoObj.seller && orderInfoObj.seller.type === 'admin') {
                    sellerId = orderInfoObj.seller.id;
                  }

                  if (sellerId) {
                    const employee = employees.find(e => e.id === sellerId);
                    if (employee) {
                      if (!line.product) {
                        line.product = {};
                      }
                      line.product.employee = employee;
                    }
                  }
                }
              });
            }
          }

          const products = await productsQuery.getMany();

          // Gán thông tin sản phẩm vào chi tiết đơn hàng
          const productMap = new Map();
          products.forEach(product => {
            this.logger.debug(`Product ${product.id} info: ${JSON.stringify(product)}`);
            productMap.set(product.id, product);
          });

          // Thu thập danh sách employeeId và userId từ sản phẩm
          const productEmployeeIds: number[] = [];
          const productUserIds: number[] = [];

          orderLines.forEach(line => {
            if (productMap.has(line.productId)) {
              line.product = productMap.get(line.productId);

              // Kiểm tra xem có thông tin user hoặc employee không
              if (line.product.user) {
                this.logger.debug(`Product ${line.productId} has user info: ${JSON.stringify(line.product.user)}`);
              } else if (line.product.employee) {
                this.logger.debug(`Product ${line.productId} has employee info: ${JSON.stringify(line.product.employee)}`);
              } else {
                this.logger.debug(`Product ${line.productId} has no user or employee info`);

                // Thu thập employeeId và userId từ sản phẩm
                if (line.product.employeeId && typeof line.product.employeeId === 'number') {
                  productEmployeeIds.push(line.product.employeeId);
                } else if (line.product.userId && typeof line.product.userId === 'number') {
                  productUserIds.push(line.product.userId);
                }
              }
            } else {
              this.logger.debug(`No product found for product ID ${line.productId}`);
            }
          });

          // Lấy thông tin nhân viên và người dùng từ productEmployeeIds và productUserIds
          if (productEmployeeIds.length > 0 || productUserIds.length > 0) {
            this.logger.debug(`Found ${productEmployeeIds.length} productEmployeeIds and ${productUserIds.length} productUserIds from products`);

            // Lấy thông tin nhân viên
            if (productEmployeeIds.length > 0) {
              const employeesQuery = this.dataSource
                .createQueryBuilder()
                .select('e')
                .from(Employee, 'e')
                .where('e.id IN (:...employeeIds)', { employeeIds: productEmployeeIds })
                .addSelect(['e.id', 'e.email', 'e.fullName', 'e.avatar', 'e.phoneNumber']);

              const employees = await employeesQuery.getMany();
              this.logger.debug(`Found ${employees.length} employees from productEmployeeIds`);

              // Gán thông tin nhân viên vào sản phẩm
              orderLines.forEach(line => {
                if (line.product && line.product.employeeId) {
                  const employee = employees.find(e => e.id === line.product.employeeId);
                  if (employee) {
                    line.product.employee = employee;
                    this.logger.debug(`Assigned employee ${employee.id} to product ${line.productId}`);
                  }
                }
              });
            }

            // Lấy thông tin người dùng
            if (productUserIds.length > 0) {
              const usersQuery = this.dataSource
                .createQueryBuilder()
                .select('u')
                .from(User, 'u')
                .where('u.id IN (:...userIds)', { userIds: productUserIds })
                .addSelect(['u.id', 'u.email', 'u.fullName', 'u.avatar', 'u.phoneNumber']);

              const users = await usersQuery.getMany();
              this.logger.debug(`Found ${users.length} users from productUserIds`);

              // Gán thông tin người dùng vào sản phẩm
              orderLines.forEach(line => {
                if (line.product && line.product.userId) {
                  const user = users.find(u => u.id === line.product.userId);
                  if (user) {
                    line.product.user = user;
                    this.logger.debug(`Assigned user ${user.id} to product ${line.productId}`);
                  }
                }
              });
            }
          }
        }

        // Gán chi tiết đơn hàng vào đơn hàng
        for (const order of orders) {
          order.orderLines = orderLines.filter(ol => ol.orderId === order.id);
        }
      } catch (error) {
        this.logger.error(`Error fetching order lines: ${error.message}`, error.stack);
        for (const order of orders) {
          order.orderLines = [];
        }
      }
    }

    return {
      items: orders,
      meta: {
        totalItems,
        itemCount: orders.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tạo đơn hàng mới từ giỏ hàng
   * @param userId ID người dùng
   * @param totalPoint Tổng điểm thanh toán
   * @returns Đơn hàng đã tạo
   */
  async createOrder(userId: number, totalPoint: number): Promise<MarketOrder> {
    const order = this.create({
      userId,
      totalPoint,
      createdAt: Math.floor(Date.now()),
      updatedAt: Math.floor(Date.now()),
    });

    return this.save(order);
  }

  /**
   * Tạo đơn hàng mới với chi tiết đơn hàng
   * @param userId ID người dùng
   * @param totalPoint Tổng điểm thanh toán
   * @param products Danh sách sản phẩm
   * @param platformFeePercent Phần trăm phí sàn (mặc định: 5%)
   * @returns Đơn hàng đã tạo với chi tiết
   */
  @Transactional()
  async createOrderWithLines(
    userId: number,
    totalPoint: number,
    products: Product[],
    platformFeePercent: number = 5.0
  ): Promise<MarketOrder> {
    try {
      this.logger.debug(`Tạo đơn hàng mới cho user ${userId} với ${products.length} sản phẩm, tổng ${totalPoint} điểm`);

      // 1. Tạo đơn hàng
      const order = await this.createOrder(userId, totalPoint);
      this.logger.debug(`Đã tạo đơn hàng ID: ${order.id}`);

      // 2. Tạo chi tiết đơn hàng
      const now = Math.floor(Date.now());
      const orderLines: Partial<MarketOrderLine>[] = [];

      for (const product of products) {
        // Tính toán phí sàn và số tiền người bán nhận được
        const productPrice = Number(product.discountedPrice);
        const platformFee = Math.floor(productPrice * (platformFeePercent / 100));
        const sellerReceivePrice = productPrice - platformFee;

        // Tạo chi tiết đơn hàng
        orderLines.push({
          orderId: order.id,
          productId: product.id,
          point: productPrice,
          productName: product.name,
          platformFeePercent,
          sellerReceivePrice,
          quantity: 1,
          createdAt: now,
          updatedAt: now,
          // Lưu thông tin chi tiết sản phẩm vào orderInfo
          orderInfo: {
            name: product.name,
            description: product.description,
            category: product.category,
            discountedPrice: product.discountedPrice,
            listedPrice: product.listedPrice
          }
        });
      }

      // 3. Lưu chi tiết đơn hàng
      const marketOrderLineRepository = this.dataSource.getRepository(MarketOrderLine);
      await marketOrderLineRepository.save(orderLines);

      this.logger.debug(`Đã tạo ${orderLines.length} chi tiết đơn hàng cho đơn hàng ID: ${order.id}`);
      this.logger.debug(`Thông tin orderInfo đã được lưu: ${JSON.stringify(orderLines[0]?.orderInfo)}`);

      // 4. Trả về đơn hàng đã tạo
      return order;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm đơn hàng theo ID với đầy đủ thông tin chi tiết
   * @param id ID đơn hàng
   * @returns Đơn hàng với đầy đủ thông tin chi tiết hoặc null
   */
  async findOrderDetailById(id: number): Promise<MarketOrder | null> {
    try {
      // Tạo truy vấn cơ bản
      const query = this.createBaseQuery();

      // Thêm điều kiện tìm kiếm theo ID
      query.where('order.id = :id', { id });

      // Thêm các join với điều kiện rõ ràng
      // Join với user trước để đảm bảo lấy được thông tin người dùng
      query.leftJoin('users', 'orderUser', 'orderUser.id = order.user_id');

      // Sau đó join với các bảng khác
      query.leftJoin('market_order_line', 'orderLines', 'orderLines.order_id = order.id');
      query.leftJoin('products', 'product', 'product.id = orderLines.product_id');
      query.leftJoin('users', 'user', 'user.id = product.user_id');
      query.leftJoin('employees', 'employee', 'employee.id = product.employee_id');

      query.addSelect([
        'orderUser.id', 'orderUser.full_name', 'orderUser.email', 'orderUser.avatar',
        'orderLines.id', 'orderLines.product_id', 'orderLines.quantity', 'orderLines.point',
        'product.id', 'product.name', 'product.description', 'product.listed_price',
        'product.discounted_price', 'product.images', 'product.status',
        'user.id', 'user.full_name', 'user.email', 'user.avatar',
        'employee.id', 'employee.full_name', 'employee.email'
      ]);

      // Thực hiện truy vấn
      const order = await query.getOne();

      // Log thông tin để debug
      if (order) {
        this.logger.debug(`Found order with ID ${id}, User ID: ${order.userId}`);
        // Không thể truy cập orderLines vì đã loại bỏ quan hệ
        this.logger.debug(`Order found successfully`);
      } else {
        this.logger.debug(`No order found with ID ${id}`);

        // Kiểm tra xem ID có tồn tại trong bảng không
        const exists = await this.createBaseQuery()
          .where('order.id = :id', { id })
          .getCount();

        this.logger.debug(`Order with ID ${id} exists in database: ${exists > 0}`);
      }

      return order;
    } catch (error) {
      this.logger.error(`Error finding order by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm danh sách đơn hàng cho admin với phân trang, tìm kiếm, lọc và sắp xếp
   * @param queryDto DTO truy vấn từ admin
   * @returns Danh sách đơn hàng phân trang
   */
  async findAdminOrders(queryDto: OrderQueryDto): Promise<PaginatedResult<MarketOrder>> {
    try {
      const { page = 1, limit = 10, search, userId, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;
      const skip = (page - 1) * limit;

      // Tạo truy vấn cơ bản
      const query = this.createBaseQuery();

      // Thêm các join với điều kiện rõ ràng
      // Join với user trước để đảm bảo lấy được thông tin người dùng
      query.leftJoin('users', 'orderUser', 'orderUser.id = order.user_id');

      // Sau đó join với các bảng khác
      query.leftJoin('market_order_line', 'orderLines', 'orderLines.order_id = order.id');
      query.leftJoin('products', 'product', 'product.id = orderLines.product_id');
      query.leftJoin('users', 'user', 'user.id = product.user_id');
      query.leftJoin('employees', 'employee', 'employee.id = product.employee_id');

      query.addSelect([
        'orderUser.id', 'orderUser.full_name', 'orderUser.email', 'orderUser.avatar',
        'orderLines.id', 'orderLines.product_id', 'orderLines.quantity', 'orderLines.point',
        'product.id', 'product.name', 'product.description', 'product.listed_price',
        'product.discounted_price', 'product.images', 'product.status',
        'user.id', 'user.full_name', 'user.email', 'user.avatar',
        'employee.id', 'employee.full_name', 'employee.email'
      ]);

      // Áp dụng bộ lọc
      if (userId) {
        query.andWhere('order.userId = :userId', { userId });
      }

      // Không cần lọc theo status vì đã loại bỏ trường này

      // Áp dụng tìm kiếm
      if (search) {
        query.andWhere(
          '(LOWER(orderUser.full_name) LIKE LOWER(:search) OR LOWER(orderUser.email) LIKE LOWER(:search))',
          { search: `%${search}%` }
        );
      }

      // Sắp xếp
      if (sortBy === 'totalAmount') {
        query.orderBy('order.totalPoint', sortDirection);
      } else {
        // Mặc định sắp xếp theo createdAt
        query.orderBy('order.createdAt', sortDirection);
      }

      // Thực hiện phân trang
      query.skip(skip).take(limit);

      // Lấy kết quả và tổng số
      // Tạo một truy vấn đếm riêng biệt để đảm bảo chính xác
      const countQuery = this.createBaseQuery();

      // Áp dụng các điều kiện lọc tương tự như truy vấn chính
      if (userId) {
        countQuery.andWhere('order.userId = :userId', { userId });
      }

      if (search) {
        // Để đếm chính xác, chúng ta cần join với user
        countQuery.leftJoin('users', 'countUser', 'countUser.id = order.user_id');
        countQuery.andWhere(
          '(LOWER(countUser.full_name) LIKE LOWER(:search) OR LOWER(countUser.email) LIKE LOWER(:search))',
          { search: `%${search}%` }
        );
      }

      // Thực hiện truy vấn
      const [items, total] = await Promise.all([
        query.getMany(),
        countQuery.getCount(),
      ]);

      // Log chi tiết để debug
      this.logger.debug(`Found ${items.length} orders out of ${total} total`);

      // Log SQL query để debug
      this.logger.debug(`SQL Query: ${query.getSql()}`);

      // Log các tham số truy vấn
      this.logger.debug(`Query parameters: ${JSON.stringify(query.getParameters())}`);

      // Log thông tin chi tiết về các item tìm thấy
      if (items.length > 0) {
        this.logger.debug(`First order ID: ${items[0].id}, User ID: ${items[0].userId}`);
        // Không thể truy cập orderLines vì đã loại bỏ quan hệ
        this.logger.debug(`Orders found successfully`);
      } else {
        this.logger.debug('No orders found. Checking if table has data...');

        // Thực hiện truy vấn đơn giản để kiểm tra xem bảng có dữ liệu không
        const basicCount = await this.createBaseQuery().getCount();
        this.logger.debug(`Basic count of orders in database: ${basicCount}`);
      }

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding admin orders: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm danh sách đơn hàng của người dùng
   * @param queryDto DTO truy vấn
   * @param userId ID người dùng
   * @returns Danh sách đơn hàng phân trang
   */
  async findUserOrders(
    queryDto: QueryPurchaseHistoryDto,
    userId: number
  ): Promise<PaginatedResult<MarketOrder>> {
    try {
      const { page = 1, limit = 10, sortDirection = 'DESC' } = queryDto;
      const skip = (page - 1) * limit;

      const query = this.createBaseQuery()
        .leftJoin('market_order_line', 'orderLines', 'orderLines.order_id = order.id')
        .leftJoin('products', 'product', 'product.id = orderLines.product_id')
        .leftJoin('users', 'user', 'user.id = product.user_id')
        .leftJoin('employees', 'employee', 'employee.id = product.employee_id')
        .addSelect([
          'orderLines.id', 'orderLines.product_id', 'orderLines.quantity', 'orderLines.point',
          'product.id', 'product.name', 'product.description', 'product.listed_price',
          'product.discounted_price', 'product.images', 'product.status',
          'user.id', 'user.full_name', 'user.email', 'user.avatar',
          'employee.id', 'employee.full_name', 'employee.email'
        ])
        .where('order.userId = :userId', { userId });

      // Mặc định sắp xếp theo createdAt
      query.orderBy('order.createdAt', sortDirection);

      // Thực hiện phân trang
      query.skip(skip).take(limit);

      // Lấy kết quả và tổng số
      const [items, total] = await Promise.all([
        query.getMany(),
        this.createBaseQuery()
          .where('order.userId = :userId', { userId })
          .getCount(),
      ]);

      this.logger.debug(`Found ${items.length} orders out of ${total} total for user ${userId}`);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding user orders: ${error.message}`, error.stack);
      throw error;
    }
  }
}
