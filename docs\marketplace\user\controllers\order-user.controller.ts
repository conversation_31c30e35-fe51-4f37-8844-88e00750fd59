import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiResponse, ApiTags, getSchemaPath } from '@nestjs/swagger';
import { SwaggerApiTag } from '@common/swagger/swagger.tags';
import { OrderUserService } from '../services/order-user.service';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { CombinedPurchaseHistoryResponseDto, PurchaseHistoryItemDto, QueryPurchaseHistoryDto } from '../dto';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';

@ApiTags(SwaggerApiTag.USER_MARKETPLACE_ORDERS)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@ApiExtraModels(ApiResponseDto, PurchaseHistoryItemDto, CombinedPurchaseHistoryResponseDto)
@Controller('user/marketplace/orders')
export class OrderUserController {
  constructor(private readonly orderUserService: OrderUserService) {}

  /**
   * Lấy lịch sử mua hàng của người dùng
   * @param user Thông tin người dùng từ JWT
   * @param queryDto Tham số truy vấn
   * @returns Lịch sử mua hàng với phân trang
   */
  @Get('purchase-history')
  @ApiOperation({ summary: 'Lấy lịch sử mua hàng của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử mua hàng thành công',
    schema: ApiResponseDto.getSchema(CombinedPurchaseHistoryResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PURCHASE_HISTORY_ERROR
  )
  async getPurchaseHistory(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: QueryPurchaseHistoryDto
  ): Promise<ApiResponseDto<CombinedPurchaseHistoryResponseDto>> {
    const purchaseHistory = await this.orderUserService.getPurchaseHistory(user.id, queryDto);
    return ApiResponseDto.success(purchaseHistory, 'Lấy lịch sử mua hàng thành công');
  }
}
