import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin sản phẩm trong giỏ hàng
 */
export class CartItemResponseDto {
  @ApiProperty({
    description: 'ID của cart item',
    example: 789,
  })
  cartItemId: number;

  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  productId: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Laptop XYZ',
  })
  productName: string;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
  })
  discountedPrice: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 2,
  })
  quantity: number;

  @ApiProperty({
    description: 'Tên người bán',
    example: 'John Doe',
  })
  sellerName: string;

  @ApiProperty({
    description: 'Thời gian thêm vào giỏ hàng',
    example: 1625097600000,
  })
  createdAt: number;
}

/**
 * DTO cho response trả về thông tin giỏ hàng
 */
export class CartResponseDto {
  @ApiProperty({
    description: '<PERSON>h sách sản phẩm trong giỏ hàng',
    type: [CartItemResponseDto],
  })
  items: CartItemResponseDto[];

  @ApiProperty({
    description: 'Tổng giá trị giỏ hàng',
    example: 2000,
  })
  totalValue: number;

  @ApiProperty({
    description: 'Thời gian tạo giỏ hàng',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật giỏ hàng',
    example: 1625097600000,
  })
  updatedAt: number;
}
