import { PaginatedResult } from '@common/response/api-response-dto';
import { PurchaseHistoryItemDto } from './purchase-history-response.dto';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response trả về lịch sử mua hàng có phân trang
 */
export class CombinedPurchaseHistoryResponseDto implements PaginatedResult<PurchaseHistoryItemDto> {
  @ApiProperty({ type: [PurchaseHistoryItemDto] })
  items: PurchaseHistoryItemDto[];

  @ApiProperty()
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
