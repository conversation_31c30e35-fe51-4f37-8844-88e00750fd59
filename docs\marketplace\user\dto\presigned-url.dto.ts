import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin URL ký sẵn cho ảnh
 */
export class PresignedUrlImageDto {
  @ApiProperty({
    description: 'Chỉ số ảnh',
    example: 1,
  })
  index: number;

  @ApiProperty({
    description: 'URL ký sẵn để upload',
    example: 'https://hnssbfc.com/key1',
  })
  uploadUrl: string;
}

/**
 * DTO cho thông tin URL ký sẵn đơn lẻ
 */
export class PresignedUrlDto {
  @ApiProperty({
    description: 'URL ký sẵn để upload',
    example: 'https://cdn.redai.vn/marketplace/DOCUMENT/2025/05/product-detail-1746350313354-1746350313354-b9e0b3d7-b5c4-4000-b184-12248ed75794',
  })
  url: string;

  @ApiProperty({
    description: 'Khóa S3 của tệp',
    example: 'marketplace/DOCUMENT/2025/05/product-detail-1746350313354-1746350313354-b9e0b3d7-b5c4-4000-b184-12248ed75794',
  })
  key: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của URL (timestamp)',
    example: 1746350313354,
  })
  expiresAt: number;
}

/**
 * DTO cho các URL ký sẵn trả về khi cập nhật sản phẩm
 */
export class PresignedUrlsDto {
  @ApiProperty({
    description: 'Danh sách URL ký sẵn cho ảnh',
    type: [PresignedUrlImageDto],
  })
  presignedUrlImage: PresignedUrlImageDto[];

  @ApiProperty({
    description: 'URL ký sẵn cho chi tiết sản phẩm',
    example: 'https://hnssbfc.com/key_detail',
    nullable: true,
  })
  presignedUrlDetail: string | null;

  @ApiProperty({
    description: 'URL ký sẵn cho hướng dẫn sử dụng',
    example: 'https://hnssbfc.com/key_manual',
    nullable: true,
  })
  presignedUrlUserManual: string | null;
}
