import { ApiProperty } from '@nestjs/swagger';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';
import { SellerInfoDto } from './product-response.dto';

/**
 * DTO cho response trả về chi tiết sản phẩm
 */
export class ProductDetailResponseDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'AI Chatbot Template',
  })
  name: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả sản phẩm',
    example: 'A ready-to-use chatbot template for customer service',
  })
  description: string;

  @ApiProperty({
    description: '<PERSON>h sách ảnh sản phẩm',
    type: 'array',
    example: [
      {
        key: 'marketplace/IMAGE/2025/05/product-image-0-1746348513323-1746348513323-b9e0b3d7-b5c4-4000-b184-12248ed75794',
        position: 0,
        url: 'https://cdn.redai.vn/marketplace/IMAGE/2025/05/product-image-0-1746348513323-1746348513323-b9e0b3d7-b5c4-4000-b184-12248ed75794'
      }
    ],
  })
  images: Array<{ key: string; position: number; url: string }>;

  @ApiProperty({
    description: 'Giá niêm yết',
    example: 1200,
  })
  listedPrice: number;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
  })
  discountedPrice: number;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductCategory,
    example: ProductCategory.AGENT,
  })
  category: ProductCategory;

  @ApiProperty({
    description: 'Hướng dẫn sử dụng',
    example: 'https://example.com/manual.pdf',
    nullable: true,
  })
  userManual: string | null;

  @ApiProperty({
    description: 'Thông tin chi tiết',
    example: 'https://example.com/detail.pdf',
    nullable: true,
  })
  detail: string | null;

  @ApiProperty({
    description: 'ID nguồn sản phẩm',
    example: 'src_456',
    nullable: true,
  })
  sourceId: string | null;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1625184000000,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Thông tin người bán',
    type: SellerInfoDto,
  })
  seller: SellerInfoDto;

  @ApiProperty({
    description: 'Tổng số lượng đã bán',
    example: 25,
  })
  soldCount: number;

  @ApiProperty({
    description: 'Có thể mua sản phẩm này không',
    example: true,
  })
  canPurchase: boolean;
}

/**
 * DTO cho response trả về chi tiết sản phẩm của người dùng
 * Mở rộng từ ProductDetailResponseDto và thêm trạng thái
 */
export class UserProductDetailResponseDto extends ProductDetailResponseDto {
  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: ProductStatus,
    example: ProductStatus.APPROVED,
  })
  status: ProductStatus;
}
