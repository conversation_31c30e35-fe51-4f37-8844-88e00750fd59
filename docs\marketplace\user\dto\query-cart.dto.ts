import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp giỏ hàng
 */
export enum CartSortField {
  PRODUCT_NAME = 'productName',
  PRICE = 'discountedPrice',
  QUANTITY = 'quantity',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho các tham số truy vấn giỏ hàng
 */
export class QueryCartDto extends QueryDto {
  @ApiProperty({
    description: 'Trường sắp xếp',
    required: false,
    enum: CartSortField,
    default: CartSortField.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(CartSortField)
  sortBy: CartSortField = CartSortField.CREATED_AT;
}
