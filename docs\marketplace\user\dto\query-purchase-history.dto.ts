import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp lịch sử mua hàng
 */
export enum PurchaseHistorySortField {
  PRODUCT_NAME = 'productName',
  PRICE = 'discountedPrice',
  QUANTITY = 'quantity',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho các tham số truy vấn lịch sử mua hàng
 */
export class QueryPurchaseHistoryDto extends QueryDto {
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: PurchaseHistorySortField,
    example: PurchaseHistorySortField.CREATED_AT,
    default: PurchaseHistorySortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(PurchaseHistorySortField)
  sortBy: PurchaseHistorySortField = PurchaseHistorySortField.CREATED_AT;
}
