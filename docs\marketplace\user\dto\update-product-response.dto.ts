import { ApiProperty } from '@nestjs/swagger';
import { UserProductDetailResponseDto } from './product-detail-response.dto';
import { PresignedUrlImageDto } from './presigned-url.dto';

/**
 * DTO cho phản hồi khi cập nhật sản phẩm
 */
export class UpdateProductResponseDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm đã cập nhật',
    type: UserProductDetailResponseDto,
  })
  product: UserProductDetailResponseDto;

  @ApiProperty({
    description: 'Danh sách URL ký sẵn cho ảnh mới',
    type: [PresignedUrlImageDto],
    nullable: true,
  })
  presignedUrlImage: PresignedUrlImageDto[];

  @ApiProperty({
    description: 'URL ký sẵn cho chi tiết sản phẩm',
    example: 'https://hnssbfc.com/key_detail',
    nullable: true,
  })
  presignedUrlDetail: string | null;

  @ApiProperty({
    description: 'URL ký sẵn cho hướng dẫn sử dụng',
    example: 'https://hnssbfc.com/key_manual',
    nullable: true,
  })
  presignedUrlUserManual: string | null;
}
