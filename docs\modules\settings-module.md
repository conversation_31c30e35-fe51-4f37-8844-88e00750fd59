# Settings Module Documentation

## Tổng quan

Module Settings cung cấp trang `/settings` để người dùng cấu hình các thiết lập ứng dụng bao gồm:
- **Theme Settings**: C<PERSON>u hình giao diện (light/dark/custom)
- **Timezone Settings**: Cài đặt múi giờ
- **ChatPanel Keywords**: Quản lý từ khóa chuyển trang trong ChatPanel

## Cấu trúc Module

```
src/modules/settings/
├── components/
│   ├── ThemeSettings.tsx          # Component cài đặt theme
│   ├── TimezoneSettings.tsx       # Component cài đặt múi giờ
│   └── ChatKeywordSettings.tsx    # Component quản lý từ khóa ChatPanel
├── hooks/
│   └── useChatKeywords.ts         # Hook kết hợp default và custom keywords
├── locales/
│   ├── vi.json                    # Translation tiếng Việt
│   ├── en.json                    # Translation tiếng Anh
│   ├── zh.json                    # Translation tiếng Trung
│   └── index.ts                   # Export translations
├── pages/
│   └── SettingsPage.tsx           # Trang chính settings
├── routers/
│   └── settingsRoutes.tsx         # Routes configuration
├── store/
│   └── settingsSlice.ts           # Redux slice
├── types/
│   └── index.ts                   # Type definitions
└── index.ts                       # Module exports
```

## Tính năng chính

### 1. Theme Settings
- Chuyển đổi giữa Light, Dark, Custom theme
- Tích hợp với existing ThemeContext
- Preview theme với color swatches
- Reset về theme mặc định

### 2. Timezone Settings
- Danh sách múi giờ phổ biến
- Preview thời gian hiện tại theo múi giờ đã chọn
- Sử dụng múi giờ hệ thống
- Format thời gian với Intl.DateTimeFormat

### 3. ChatPanel Keywords Management
- Quản lý từ khóa điều hướng cho ChatPanel
- Bật/tắt từ khóa mặc định
- Thêm từ khóa tùy chỉnh
- Mô tả cho từng từ khóa
- Reset về danh sách mặc định

## Redux Store Integration

### State Structure
```typescript
interface SettingsState {
  timezone: string;
  chatKeywords: ChatKeyword[];
  customKeywords: ChatKeyword[];
  isLoading: boolean;
  error: string | null;
}
```

### Actions
- `setTimezone(timezone: string)`
- `addChatKeyword(keyword: ChatKeyword)`
- `updateChatKeyword({ id, updates })`
- `removeChatKeyword(id: string)`
- `toggleChatKeyword(id: string)`
- `resetChatKeywords()`

### Persistence
Settings được lưu với Redux Persist:
- `timezone`: Múi giờ đã chọn
- `chatKeywords`: Danh sách từ khóa mặc định (có thể bị disable)
- `customKeywords`: Từ khóa tùy chỉnh của người dùng

## ChatPanel Integration

### Hook useChatKeywords
```typescript
const useChatKeywords = (authType: AuthType): ModernMenuItem[]
```

Hook này:
1. Lấy default menu items từ `getMenuItemsByAuthType()`
2. Kết hợp với settings từ Redux store
3. Override keywords cho existing items
4. Thêm custom keywords
5. Filter theo enabled status
6. Sắp xếp: default items trước, custom items sau

### ChatInput Integration
ChatInput đã được cập nhật để sử dụng `useChatKeywords` thay vì hardcode menu items.

## Internationalization

Hỗ trợ 3 ngôn ngữ:
- **Tiếng Việt** (vi): Ngôn ngữ mặc định
- **English** (en): Ngôn ngữ phụ
- **中文** (zh): Ngôn ngữ bổ sung

Translation keys được tổ chức theo namespace `settings.*`

## Routing

### Protected Route
```typescript
{
  path: '/settings',
  element: (
    <ProtectedRoute type="PROTECT">
      <MainLayout>
        <SettingsPage />
      </MainLayout>
    </ProtectedRoute>
  ),
}
```

### Navigation
- Direct URL: `/settings`
- ChatPanel keywords: "settings", "cài đặt", "设置"

## Usage Examples

### Accessing Settings in Components
```typescript
import { useSelector, useDispatch } from 'react-redux';
import { setTimezone, addChatKeyword } from '@/modules/settings';

const MyComponent = () => {
  const { timezone, chatKeywords } = useSelector((state: RootState) => state.settings);
  const dispatch = useDispatch();

  const handleTimezoneChange = (newTimezone: string) => {
    dispatch(setTimezone(newTimezone));
  };
};
```

### Using Chat Keywords Hook
```typescript
import { useChatKeywords } from '@/modules/settings';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';

const ChatComponent = () => {
  const { authType } = useAuthCommon();
  const menuItems = useChatKeywords(authType);
  
  // menuItems now includes both default and custom keywords
};
```

## Best Practices

1. **Settings Persistence**: Tất cả settings được persist tự động
2. **Type Safety**: Sử dụng TypeScript interfaces cho tất cả data structures
3. **Internationalization**: Luôn sử dụng translation keys thay vì hardcode text
4. **Validation**: Form validation với react-hook-form + zod
5. **Performance**: useMemo trong hooks để tránh re-computation không cần thiết

## Future Enhancements

- Sync settings với server khi user đăng nhập
- Export/Import settings configuration
- Advanced theme customization
- More timezone options và auto-detection
- Bulk operations cho chat keywords
- Settings backup và restore
