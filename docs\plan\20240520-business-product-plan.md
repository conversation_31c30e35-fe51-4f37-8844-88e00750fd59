# Kế hoạch phát triển module Business/Product

## Phân tích hiện trạng

### Giao diện hiện tại
- Form tạo sản phẩm đã được cập nhật với các tính năng:
  - Trường đơn vị tiền tệ (currency) đã được chuyển thành Select với 3 lựa chọn: VND, EUR, USD
  - Đã thêm chức năng upload media (ảnh/video)
  - Đ<PERSON> thêm khối cấu hình vận chuyển (shipmentConfig)
  - Đ<PERSON> cải thiện phần quản lý biến thể sản phẩm (variants)
  - <PERSON><PERSON> cải thiện hiển thị ảnh sản phẩm với cơ chế cuộn ngang

### Vấn đề hiện tại
- Hiển thị ảnh sản phẩm bị vỡ layout khi đóng chat-panel, nhưng hoạt động bình thường khi mở chat-panel
- Cần kiểm tra kết nối API và xử lý dữ liệu

## Kế hoạch phát triển

### 1. Sửa lỗi hiển thị ảnh sản phẩm
- **Vấn đề**: Khi đóng chat-panel, phần hiển thị ảnh bị vỡ layout
- **Nguyên nhân có thể**:
  - Chiều cao container không đủ
  - Cơ chế responsive chưa hoạt động đúng
  - Vấn đề với overflow hoặc layout tổng thể
- **Giải pháp**:
  - Sử dụng chiều cao cố định cho container
  - Đảm bảo overflow được xử lý đúng
  - Kiểm tra lại layout tổng thể

### 2. Kiểm tra kết nối API
- **Hiện trạng**:
  - API tạo sản phẩm đã được triển khai
  - API upload media đã được triển khai
  - Chưa rõ API quản lý biến thể sản phẩm
- **Cần kiểm tra**:
  - Xác nhận API tạo sản phẩm hoạt động đúng với các trường mới
  - Kiểm tra API upload media và xử lý lỗi
  - Xác nhận API quản lý biến thể sản phẩm

### 3. Hoàn thiện giao diện
- **Cần cải thiện**:
  - Thêm validation cho các trường mới
  - Cải thiện UX khi thêm/xóa biến thể
  - Thêm thông báo thành công/thất bại
  - Thêm loading state khi submit form

### 4. Kiểm thử
- **Kịch bản kiểm thử**:
  - Tạo sản phẩm với đầy đủ thông tin
  - Tạo sản phẩm với biến thể
  - Upload nhiều ảnh/video
  - Kiểm tra responsive trên các kích thước màn hình
  - Kiểm tra khi chat-panel mở/đóng

## Chi tiết triển khai

### 1. Sửa lỗi hiển thị ảnh sản phẩm
```tsx
// Sử dụng chiều cao cố định và xử lý overflow
<div className="w-full h-32 overflow-hidden">
  <div className="flex flex-nowrap overflow-x-auto overflow-y-hidden pb-2 space-x-3 h-full">
    {/* Nội dung ảnh */}
  </div>
</div>
```

### 2. Cải thiện validation
```tsx
// Thêm validation cho các trường mới
const createProductSchema = z.object({
  // Các trường hiện tại
  shipmentConfig: z.object({
    lengthCm: z.number().min(0).optional(),
    widthCm: z.number().min(0).optional(),
    heightCm: z.number().min(0).optional(),
    weightGram: z.number().min(0).optional(),
  }).optional(),
  variants: z.array(
    z.object({
      name: z.string().min(1, "Tên biến thể không được để trống"),
      listPrice: z.number().min(0, "Giá niêm yết phải lớn hơn hoặc bằng 0"),
      salePrice: z.number().min(0, "Giá bán phải lớn hơn hoặc bằng 0"),
      currency: z.string(),
      customFields: z.array(/* ... */),
    })
  ).optional(),
});
```

### 3. Cải thiện UX khi thêm/xóa biến thể
```tsx
// Thêm animation khi thêm/xóa biến thể
<AnimatePresence>
  {variants.map((variant) => (
    <motion.div
      key={variant.id}
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: "auto" }}
      exit={{ opacity: 0, height: 0 }}
    >
      {/* Nội dung biến thể */}
    </motion.div>
  ))}
</AnimatePresence>
```

### 4. Thêm loading state và thông báo
```tsx
// Thêm loading state và thông báo
const handleSubmit = async (values) => {
  try {
    setIsSubmitting(true);
    await createProduct(values);
    NotificationUtil.success({
      message: "Tạo sản phẩm thành công",
    });
    // Reset form hoặc chuyển hướng
  } catch (error) {
    NotificationUtil.error({
      message: "Tạo sản phẩm thất bại",
    });
  } finally {
    setIsSubmitting(false);
  }
};
```

## Lịch trình

1. **Ngày 1**: Sửa lỗi hiển thị ảnh sản phẩm và kiểm tra kết nối API
2. **Ngày 2**: Hoàn thiện giao diện và thêm validation
3. **Ngày 3**: Kiểm thử và sửa lỗi
4. **Ngày 4**: Hoàn thiện và triển khai

## Kết luận

Module Business/Product đã được cập nhật với nhiều tính năng mới, nhưng vẫn còn một số vấn đề cần giải quyết. Kế hoạch này đề xuất các bước cụ thể để hoàn thiện module, từ sửa lỗi hiển thị ảnh sản phẩm đến kiểm tra kết nối API và cải thiện giao diện người dùng.
