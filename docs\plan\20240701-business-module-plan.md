# Kế hoạch triển khai các yêu cầu cho module Business

## Yêu cầu

1. C<PERSON><PERSON> nhật `ProductEditForm.tsx` để thêm component tags từ `FileForm.tsx`
2. Chỉnh sửa các thuộc tính để full width cho dễ nhìn
3. X<PERSON> lý chức năng xóa trong `activeFilters` trong module business/product
4. Triển khai API cho trang `/business/custom-field` dựa trên các API từ backend
5. Triển khai API cho trang `/business/inventory` dựa trên các API từ backend
6. Triển khai API cho trang `/business/conversion` dựa trên các API từ backend

## Chi tiết triển khai

### 1. Cập nhật ProductEditForm.tsx

- Thêm state `tempTags` để quản lý tags
- Thêm component FormItem cho tags tương tự như trong FileForm.tsx
- Cập nhật logic xử lý tags trong form

### 2. Chỉnh sửa thuộc tính full width

- Thêm thuộc tính `fullWidth` cho các Input, Textarea, Select trong ProductEditForm.tsx

### 3. Xử lý chức năng xóa trong activeFilters

- Kiểm tra và cập nhật hàm `handleClearAll` trong `useActiveFilters` hook
- Đảm bảo các hàm xóa filter được gọi đúng cách trong ProductsPage.tsx

### 4. Triển khai API cho trang /business/custom-field

#### API cần triển khai:
- Lấy danh sách nhóm trường tùy chỉnh (GET /user/custom-group-forms)
- Tạo nhóm trường tùy chỉnh mới (POST /user/custom-group-forms)
- Cập nhật nhóm trường tùy chỉnh (PUT /user/custom-group-forms/:id)
- Xóa nhóm trường tùy chỉnh (DELETE /user/custom-group-forms/:id)
- Lấy chi tiết nhóm trường tùy chỉnh (GET /user/custom-group-forms/:id)
- Lấy danh sách trường tùy chỉnh (GET /user/custom-fields)
- Tạo trường tùy chỉnh mới (POST /user/custom-fields)
- Cập nhật trường tùy chỉnh (PUT /user/custom-fields/:id)
- Xóa trường tùy chỉnh (DELETE /user/custom-fields/:id)
- Lấy chi tiết trường tùy chỉnh (GET /user/custom-fields/:id)

#### Các file cần tạo/cập nhật:
- Tạo types cho custom-field và custom-group-form
- Tạo service cho custom-field và custom-group-form
- Tạo hooks sử dụng TanStack Query
- Cập nhật UI để sử dụng các hooks mới

### 5. Triển khai API cho trang /business/inventory

#### API cần triển khai:
- Lấy danh sách kho (GET /user/warehouses)
- Tạo kho mới (POST /user/warehouses)
- Cập nhật kho (PUT /user/warehouses/:id)
- Xóa kho (DELETE /user/warehouses/:id)
- Lấy chi tiết kho (GET /user/warehouses/:id)
- Lấy danh sách trường tùy chỉnh của kho (GET /user/warehouses/:id/custom-fields)
- Thêm trường tùy chỉnh cho kho (POST /user/warehouses/:id/custom-fields)
- Cập nhật trường tùy chỉnh của kho (PUT /user/warehouses/:id/custom-fields/:fieldId)
- Xóa trường tùy chỉnh của kho (DELETE /user/warehouses/:id/custom-fields/:fieldId)
- Lấy chi tiết trường tùy chỉnh của kho (GET /user/warehouses/:id/custom-fields/:fieldId)

#### Các file cần tạo/cập nhật:
- Tạo types cho warehouse và warehouse-custom-field
- Tạo service cho warehouse và warehouse-custom-field
- Tạo hooks sử dụng TanStack Query
- Cập nhật UI để sử dụng các hooks mới

### 6. Triển khai API cho trang /business/conversion

#### API cần triển khai:
- Lấy danh sách bản ghi chuyển đổi (GET /user/converts)
- Lấy chi tiết bản ghi chuyển đổi (GET /user/converts/detail/:id)

#### Các file cần tạo/cập nhật:
- Tạo types cho convert
- Tạo service cho convert
- Tạo hooks sử dụng TanStack Query
- Cập nhật UI để sử dụng các hooks mới

## Thứ tự triển khai

1. Cập nhật ProductEditForm.tsx (thêm component tags và full width)
2. Xử lý chức năng xóa trong activeFilters
3. Triển khai API cho trang /business/custom-field
4. Triển khai API cho trang /business/inventory
5. Triển khai API cho trang /business/conversion

## Lưu ý

- Đảm bảo tuân thủ các quy tắc TypeScript, tránh sử dụng `any`
- Sử dụng TanStack Query cho tất cả các API calls
- Đảm bảo xử lý lỗi đầy đủ
- Đảm bảo UI responsive và thân thiện với người dùng
