# Kế hoạch nâng cấp các trang trong module Business

## Mục tiêu

1. Sửa lỗi bản dịch đa ngôn ngữ trong ProductsPage
2. Tạo hàm định dạng ngày tháng chung cho timestamp
3. Thêm Chip cho trường priceType trong ProductsPage
4. Thêm trường tùy chỉnh vào form tạo sản phẩm
5. Chuyển đổi trang OrderPage theo phong cách ProductsPage
6. Chuyển đổi trang CustomFieldPage theo phong cách ProductsPage
7. Chuyển đổi trang ConversionPage theo phong cách ProductsPage

## Chi tiết triển khai

### 1. Sửa lỗi bản dịch đa ngôn ngữ trong ProductsPage

- Kiểm tra và cập nhật các khóa dịch trong file `src\modules\business\locales\vi.json`
- Kiểm tra và cập nhật các khóa dịch trong file `src\modules\business\locales\en.json`
- <PERSON><PERSON>m tra và cập nhật các khóa dịch trong file `src\modules\business\locales\zh.json`
- Đảm bảo các khóa dịch được sử dụng đúng trong ProductsPage

### 2. Tạo hàm định dạng ngày tháng chung cho timestamp

- Tạo hàm `formatTimestamp` trong `src\shared\utils\date.ts`
- Hàm này sẽ xử lý timestamp dạng số (như 1746884479893)
- Thêm hàm kiểm tra timestamp hợp lệ
- Cập nhật ProductsPage để sử dụng hàm này cho cột createdAt

### 3. Thêm Chip cho trường priceType trong ProductsPage

- Sử dụng component Chip từ `src\shared\components\common\Chip\Chip.tsx`
- Cập nhật cột priceType trong bảng sản phẩm để hiển thị Chip với màu sắc tương ứng
- Đảm bảo hiển thị đúng theo ngôn ngữ hiện tại

### 4. Thêm trường tùy chỉnh vào form tạo sản phẩm

- Tạo service và hook cho custom-field:
  - `src\modules\business\services\custom-field.service.ts`
  - `src\modules\business\hooks\useCustomFieldQuery.ts`
- Tạo service và hook cho custom-group-form:
  - `src\modules\business\services\custom-group-form.service.ts`
  - `src\modules\business\hooks\useCustomGroupFormQuery.ts`
- Cập nhật ProductForm để thêm phần chọn trường tùy chỉnh
- Cập nhật các types và schema liên quan
- Thêm các khóa dịch cần thiết

### 5. Chuyển đổi trang OrderPage theo phong cách ProductsPage

- Tạo service và hook cho order:
  - `src\modules\business\services\order.service.ts`
  - `src\modules\business\hooks\useOrderQuery.ts`
- Tạo hoặc cập nhật OrderPage theo cấu trúc của ProductsPage
- Tạo form tạo/chỉnh sửa đơn hàng
- Thêm các khóa dịch cần thiết

### 6. Chuyển đổi trang CustomFieldPage theo phong cách ProductsPage

- Tạo hoặc cập nhật CustomFieldPage theo cấu trúc của ProductsPage
- Tạo form tạo/chỉnh sửa trường tùy chỉnh
- Tạo hoặc cập nhật CustomGroupFormPage theo cấu trúc của ProductsPage
- Tạo form tạo/chỉnh sửa nhóm trường tùy chỉnh
- Thêm các khóa dịch cần thiết

### 7. Chuyển đổi trang ConversionPage theo phong cách ProductsPage

- Tạo service và hook cho conversion:
  - `src\modules\business\services\conversion.service.ts`
  - `src\modules\business\hooks\useConversionQuery.ts`
- Tạo hoặc cập nhật ConversionPage theo cấu trúc của ProductsPage
- Tạo form tạo/chỉnh sửa conversion
- Thêm các khóa dịch cần thiết

## Tiêu chuẩn triển khai

- Tuân thủ eslint và TypeScript (không sử dụng any)
- Đảm bảo responsive design
- Sử dụng theme và các component dùng chung của hệ thống
- Hỗ trợ đa ngôn ngữ (vi, en, zh)
- Sử dụng TanStack Query cho quản lý dữ liệu
- Sử dụng apiRequest thay vì fetch hoặc axios trực tiếp
