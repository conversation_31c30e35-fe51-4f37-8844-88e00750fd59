# Kế hoạch Chuẩn hóa Toàn bộ Module Marketing

## Tổng quan

Sau khi rà soát tất cả các trang trong `src/modules/marketing/pages`, tôi đã phát hiện tình trạng không nhất quán về việc sử dụng component chung, theme, và đa ngôn ngữ. Kế hoạch này sẽ chuẩn hóa toàn bộ module marketing.

## Phân tích tình trạng hiện tại

### ✅ Các trang đã chuẩn (sử dụng component chung):
1. **AudiencePage.tsx** - ✅ Đã sử dụng Table, MenuIconBar, ActiveFilters, hooks chung
2. **CustomFieldsPage.tsx** - ✅ Đã sử dụng Table, MenuIconBar, ActiveFilters, hooks chung  
3. **SegmentPage.tsx** - ✅ Đã sử dụng Table, MenuIconBar, ActiveFilters, hooks chung
4. **TagManagementPage.tsx** - ✅ Đã sử dụng Table, MenuIconBar, ActiveFilters, hooks chung
5. **TemplateEmailPage.tsx** - ✅ Đã sử dụng Table, MenuIconBar, ActiveFilters, hooks chung
6. **ZaloAccountsPage.tsx** - ✅ Vừa refactor xong

### ⚠️ Các trang cần chuẩn hóa:
1. **MarketingDashboardPage.tsx** - Cần chuẩn hóa theme, đa ngôn ngữ
2. **CampaignPage.tsx** - Cần kiểm tra và chuẩn hóa
3. **ReportsPage.tsx** - Cần kiểm tra và chuẩn hóa
4. **MarketingPage.tsx** - Cần kiểm tra và chuẩn hóa
5. **MarketingTestPage.tsx** - Cần kiểm tra và chuẩn hóa
6. **ZaloMarketingPage.tsx** - Cần kiểm tra và chuẩn hóa
7. **GoogleAdsPage.tsx** - Cần kiểm tra và chuẩn hóa
8. **email/EmailOverviewPage.tsx** - Cần kiểm tra và chuẩn hóa
9. **email/EmailTemplatesPage.tsx** - Cần kiểm tra và chuẩn hóa
10. **google-ads/GoogleAdsPage.tsx** - Cần kiểm tra và chuẩn hóa
11. **zalo/ZaloFollowersPage.tsx** - Cần refactor
12. **zalo/ZaloOverviewPage.tsx** - Cần refactor
13. **zalo/ZaloZnsPage.tsx** - Cần refactor

### 🔧 Các trang skeleton (đã OK):
1. **SmsMarketingPage.tsx** - ✅ Skeleton đơn giản, đã dùng component chung
2. **FacebookAdsPage.tsx** - ✅ Skeleton đơn giản, đã dùng component chung

## Kế hoạch thực hiện

### Phase 1: Hoàn thiện Zalo Pages (Tiếp tục từ kế hoạch trước)
- [x] ZaloAccountsPage.tsx - ✅ Đã hoàn thành
- [ ] ZaloFollowersPage.tsx - Refactor sử dụng Table, MenuIconBar, ActiveFilters
- [ ] ZaloOverviewPage.tsx - Refactor sử dụng Card, stats components
- [ ] ZaloZnsPage.tsx - Refactor sử dụng Table, MenuIconBar, ActiveFilters

### Phase 2: Chuẩn hóa Dashboard và Overview Pages
- [ ] MarketingDashboardPage.tsx - Chuẩn hóa theme, đa ngôn ngữ
- [ ] ZaloMarketingPage.tsx - Kiểm tra và chuẩn hóa
- [ ] email/EmailOverviewPage.tsx - Kiểm tra và chuẩn hóa

### Phase 3: Chuẩn hóa Campaign và Reports Pages  
- [ ] CampaignPage.tsx - Kiểm tra và refactor nếu cần
- [ ] ReportsPage.tsx - Kiểm tra và refactor nếu cần
- [ ] MarketingPage.tsx - Kiểm tra và chuẩn hóa
- [ ] MarketingTestPage.tsx - Kiểm tra và chuẩn hóa

### Phase 4: Chuẩn hóa Email và Google Ads Pages
- [ ] email/EmailTemplatesPage.tsx - Kiểm tra và chuẩn hóa
- [ ] GoogleAdsPage.tsx - Kiểm tra và chuẩn hóa  
- [ ] google-ads/GoogleAdsPage.tsx - Kiểm tra và chuẩn hóa

### Phase 5: Kiểm tra tổng thể và tối ưu
- [ ] Kiểm tra tất cả imports
- [ ] Đảm bảo TypeScript compliance
- [ ] Kiểm tra theme consistency
- [ ] Kiểm tra đa ngôn ngữ đầy đủ
- [ ] Chạy build và test

## Tiêu chí chuẩn hóa

### 1. Component Usage
- ✅ Sử dụng Table component thay vì table HTML
- ✅ Sử dụng MenuIconBar cho search/filter/actions
- ✅ Sử dụng ActiveFilters để hiển thị filter đang áp dụng
- ✅ Sử dụng Card component với box shadow
- ✅ Sử dụng IconCard, Tooltip, Badge, Button từ shared components

### 2. Hooks Integration
- ✅ Sử dụng useDataTable và useDataTableConfig
- ✅ Sử dụng useActiveFilters
- ✅ Sử dụng useSlideForm cho form animations
- ✅ Sử dụng API hooks (useXXX, useCreateXXX, useDeleteXXX)

### 3. Theme Consistency
- ✅ Sử dụng CSS variables cho colors
- ✅ Gradient colors: #FF3333 to #FFCC99
- ✅ Box shadow thay vì borders
- ✅ Dark mode support
- ✅ Consistent spacing và typography

### 4. Internationalization
- ✅ Tất cả text sử dụng t() function
- ✅ Namespace 'marketing' cho module-specific text
- ✅ Namespace 'common' cho text chung
- ✅ Key structure nhất quán

### 5. TypeScript Compliance
- ✅ Strict typing, không dùng 'any'
- ✅ Proper interface definitions
- ✅ Enum usage cho status values
- ✅ Type-safe props và callbacks

## Ưu tiên thực hiện

### Mức độ ưu tiên cao:
1. **Zalo Pages** - Đang trong quá trình refactor
2. **MarketingDashboardPage** - Trang chính, cần chuẩn hóa theme
3. **CampaignPage, ReportsPage** - Trang quan trọng

### Mức độ ưu tiên trung bình:
1. **Email Pages** - Chức năng phụ
2. **Google Ads Pages** - Chức năng mở rộng

### Mức độ ưu tiên thấp:
1. **MarketingTestPage** - Trang test
2. **Skeleton pages** - Đã OK

## Checklist cho mỗi trang

### Pre-refactor:
- [ ] Backup code hiện tại
- [ ] Kiểm tra dependencies và imports
- [ ] Xác định API endpoints được sử dụng
- [ ] Xác định types và interfaces cần thiết

### During refactor:
- [ ] Thay thế table HTML bằng Table component
- [ ] Thêm MenuIconBar
- [ ] Thêm ActiveFilters
- [ ] Tích hợp useDataTable hooks
- [ ] Cập nhật theme colors
- [ ] Cập nhật đa ngôn ngữ

### Post-refactor:
- [ ] Kiểm tra TypeScript errors
- [ ] Chạy npm run build
- [ ] Chạy npm run lint
- [ ] Test functionality
- [ ] Kiểm tra responsive design
- [ ] Kiểm tra dark mode

## Timeline ước tính

- **Phase 1**: 2-3 ngày (3 trang Zalo còn lại)
- **Phase 2**: 1-2 ngày (3 trang dashboard/overview)
- **Phase 3**: 2-3 ngày (4 trang campaign/reports)
- **Phase 4**: 1-2 ngày (3 trang email/ads)
- **Phase 5**: 1 ngày (kiểm tra tổng thể)

**Tổng cộng**: 7-11 ngày làm việc

## Rủi ro và giải pháp

### Rủi ro:
1. **Breaking changes** - Thay đổi có thể ảnh hưởng functionality
2. **API compatibility** - Component mới có thể không tương thích với API cũ
3. **Performance** - Component mới có thể chậm hơn

### Giải pháp:
1. **Incremental refactor** - Refactor từng trang một
2. **Thorough testing** - Test kỹ sau mỗi thay đổi
3. **Rollback plan** - Có backup để rollback nếu cần
4. **Code review** - Review code trước khi merge

## Kết quả mong đợi

Sau khi hoàn thành:
- ✅ 100% trang sử dụng component chung
- ✅ Theme consistency hoàn toàn
- ✅ Đa ngôn ngữ đầy đủ
- ✅ TypeScript compliance 100%
- ✅ Code maintainability cao
- ✅ User experience nhất quán
- ✅ Performance tối ưu
