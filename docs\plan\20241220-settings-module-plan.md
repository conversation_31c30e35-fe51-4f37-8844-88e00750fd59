# Kế hoạch phát triển Module Settings

## Mụ<PERSON> tiêu
Tạo module settings với trang `/settings` để cấu hình:
- Theme (light/dark/custom)
- <PERSON><PERSON><PERSON> giờ (timezone)
- Từ khóa chuyển trang cho ChatPanel

## Cấu trúc module

### 1. Files cần tạo
- [x] `src/modules/settings/pages/SettingsPage.tsx` - Trang chính settings
- [x] `src/modules/settings/components/ThemeSettings.tsx` - Component cài đặt theme
- [x] `src/modules/settings/components/TimezoneSettings.tsx` - Component cài đặt múi giờ
- [x] `src/modules/settings/components/ChatKeywordSettings.tsx` - Component cài đặt từ khóa ChatPanel
- [x] `src/modules/settings/routers/settingsRoutes.tsx` - Routes cho module
- [x] `src/modules/settings/store/settingsSlice.ts` - Redux slice cho settings
- [x] `src/modules/settings/types/index.ts` - Types definition
- [x] `src/modules/settings/index.ts` - Module exports
- [x] `src/modules/settings/locales/` - Translation files (vi, en, zh)

### 2. Tích hợp hệ thống
- [x] Thêm settingsRoutes vào router chính
- [x] Thêm settingsSlice vào Redux store
- [x] Thêm settings translations vào i18n
- [x] Thêm common translation keys (settings, theme, etc.)
- [x] Cập nhật ChatPanel để sử dụng settings từ store
- [x] Sửa tất cả lỗi TypeScript
- [x] Build thành công 100%

## Tiến độ thực hiện

### Phase 1: Tạo cấu trúc cơ bản ✅
- ✅ Tạo types và interfaces
- ✅ Tạo Redux slice cho settings
- ✅ Tạo routing cơ bản

### Phase 2: Triển khai components ✅
- ✅ ThemeSettings component
- ✅ TimezoneSettings component
- ✅ ChatKeywordSettings component
- ✅ SettingsPage layout
- ✅ Translation files (vi, en, zh)

### Phase 3: Tích hợp hệ thống ✅
- ✅ Thêm vào router chính
- ✅ Tích hợp Redux store
- ✅ Thêm translations vào i18n
- ✅ Cập nhật ChatPanel để sử dụng settings từ store

### Phase 4: Hoàn thiện và kiểm tra ✅
- ✅ Sửa tất cả lỗi TypeScript
- ✅ Sửa lỗi FormItem error prop
- ✅ Sửa lỗi Select onChange handler
- ✅ Sửa lỗi RouteGuard import
- ✅ Sửa lỗi Card variant
- ✅ Sửa unused variables
- ✅ Build thành công 100%

## Báo cáo tiến độ

### Đã hoàn thành:
1. **Cấu trúc module hoàn chỉnh**: Tạo đầy đủ các files cần thiết
2. **Redux integration**: Settings slice đã được tích hợp vào store với persist
3. **UI Components**: 3 components chính cho theme, timezone và chat keywords
4. **Routing**: Route `/settings` đã hoạt động với protected route
5. **Internationalization**: Hỗ trợ đầy đủ 3 ngôn ngữ (vi, en, zh)
6. **TypeScript**: Không có lỗi TypeScript

### Hoàn thành 100%:
- ✅ Tất cả tính năng đã được triển khai thành công
- ✅ ChatPanel đã được tích hợp với settings store
- ✅ Tạo hook `useChatKeywords` để kết hợp default và custom keywords
- ✅ Sửa tất cả lỗi TypeScript và build thành công
- ✅ Module hoạt động ổn định và sẵn sàng sử dụng

### Tính năng chính:
- **Theme Settings**: Chuyển đổi giữa light/dark/custom theme
- **Timezone Settings**: Cấu hình múi giờ với preview thời gian thực
- **Chat Keywords**: Quản lý từ khóa điều hướng cho ChatPanel
- **Persistence**: Tất cả settings được lưu với Redux Persist

## Ghi chú kỹ thuật
- Sử dụng existing theme system từ ThemeContext
- Timezone sử dụng Intl.DateTimeFormat
- ChatPanel keywords lưu trong Redux persist
- Form validation với react-hook-form + zod
- Hook `useChatKeywords` kết hợp default menu items với custom keywords

## Hướng dẫn sử dụng

### 1. Truy cập trang Settings
- Điều hướng đến `/settings` hoặc
- Sử dụng ChatPanel: gõ "settings" hoặc "cài đặt"

### 2. Cấu hình Theme
- Chọn giữa Light, Dark, hoặc Custom theme
- Thay đổi áp dụng ngay lập tức
- Có thể reset về mặc định

### 3. Cài đặt múi giờ
- Chọn múi giờ từ danh sách có sẵn
- Xem preview thời gian hiện tại
- Có thể sử dụng múi giờ hệ thống

### 4. Quản lý từ khóa ChatPanel
- Bật/tắt từ khóa mặc định
- Thêm từ khóa tùy chỉnh mới
- Mỗi từ khóa có thể có mô tả
- Reset về danh sách mặc định

### 5. Sử dụng ChatPanel với keywords
- Gõ từ khóa trong ChatPanel để điều hướng
- Hệ thống tự động nhận diện và chuyển trang
- Keywords được lưu và đồng bộ giữa các phiên

## API Reference

### Redux Store
```typescript
// Lấy settings từ store
const { timezone, chatKeywords, customKeywords } = useSelector((state: RootState) => state.settings);

// Dispatch actions
dispatch(setTimezone('Asia/Ho_Chi_Minh'));
dispatch(addChatKeyword(newKeyword));
dispatch(toggleChatKeyword(keywordId));
```

### Hook useChatKeywords
```typescript
import { useChatKeywords } from '@/modules/settings';

const menuItems = useChatKeywords(authType);
```
