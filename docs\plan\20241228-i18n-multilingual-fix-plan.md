# Kế hoạch Kiểm tra và Sửa lỗi Đa ngôn ngữ (i18n)

## Tổng quan
Hệ thống hiện tại đang có nhiều lỗi đa ngôn ngữ cần được kiểm tra và sửa chữa cho 3 ngôn ngữ:
- Tiếng Việt (vi)
- <PERSON><PERSON><PERSON><PERSON> (en)
- Ti<PERSON>ng <PERSON>rung (zh)

## Phân tích hiện trạng

### Cấu trúc i18n hiện tại
- **Thư viện**: react-i18next
- **File cấu hình chính**: `src/lib/i18n.ts`
- **Ngôn ngữ mặc định**: Tiếng Việt (vi)
- **Fallback**: Tiế<PERSON>h (en)

### Các vấn đề đã phát hiện
1. **Module generic-page**: Thiếu file translation tiếng Trung, đang fallback về tiếng Anh
2. **Cấu tr<PERSON><PERSON> không nhất quán**: Một số module có cấu trúc translation kh<PERSON><PERSON> nhau
3. **Translation thiếu**: <PERSON><PERSON><PERSON><PERSON> key translation chưa đư<PERSON>c dịch đầy đủ
4. **Duplicate imports**: Có duplicate import trong i18n.ts (rpointAdminResources)

## Kế hoạch thực hiện

### Giai đoạn 1: Kiểm tra và phân tích toàn bộ hệ thống (30 phút)
- [ ] Kiểm tra tất cả module có thư mục locales
- [ ] Phân tích cấu trúc file translation của từng module
- [ ] Tạo danh sách các module thiếu translation
- [ ] Kiểm tra consistency của key translation

### Giai đoạn 2: Sửa lỗi cấu trúc và import (45 phút)
- [ ] Sửa duplicate import trong `src/lib/i18n.ts`
- [ ] Chuẩn hóa cấu trúc thư mục locales cho tất cả module
- [ ] Đảm bảo tất cả module có đầy đủ 3 file: en.json, vi.json, zh.json
- [ ] Sửa lỗi fallback không đúng

### Giai đoạn 3: Kiểm tra và bổ sung translation thiếu (60 phút)
- [ ] Kiểm tra từng module xem có key nào thiếu translation
- [ ] Bổ sung translation tiếng Trung cho các module thiếu
- [ ] Kiểm tra quality của translation hiện có
- [ ] Đảm bảo consistency về terminology

### Giai đoạn 4: Test và validation (30 phút)
- [ ] Test chuyển đổi ngôn ngữ trên UI
- [ ] Kiểm tra tất cả page có hiển thị đúng ngôn ngữ
- [ ] Verify không có missing translation key
- [ ] Test fallback mechanism

### Giai đoạn 5: Tối ưu hóa và cleanup (15 phút)
- [ ] Cleanup unused translation keys
- [ ] Tối ưu hóa cấu trúc import
- [ ] Cập nhật documentation
- [ ] Final build test

## Danh sách Module cần kiểm tra

### Core modules
- [ ] `src/locales/` (global translations)
- [ ] `src/lib/i18n/locales/` (common translations)

### Business modules
- [ ] `src/modules/auth/locales/`
- [ ] `src/modules/business/locales/`
- [ ] `src/modules/marketing/locales/`
- [ ] `src/modules/components/locales/`
- [ ] `src/modules/integration/locales/`
- [ ] `src/modules/subscription/locales/`
- [ ] `src/modules/marketplace/locales/`
- [ ] `src/modules/profile/locales/`
- [ ] `src/modules/data/locales/`
- [ ] `src/modules/blog/locales/`
- [ ] `src/modules/model-training/locales/`
- [ ] `src/modules/rpoint/locales/`
- [ ] `src/modules/calendar/locales/`

### Admin modules
- [ ] `src/modules/admin/auth/locales/`
- [ ] `src/modules/admin/employee/locales/`
- [ ] `src/modules/admin/affiliate/locales/`
- [ ] `src/modules/admin/user/locales/`
- [ ] `src/modules/admin/data/locales/`
- [ ] `src/modules/admin/marketplace/locales/`
- [ ] `src/modules/admin/marketing/locales/`
- [ ] `src/modules/admin/r-point/locales/`
- [ ] `src/modules/admin/business/locales/`
- [ ] `src/modules/admin/dashboard/locales/`

### Special modules
- [ ] `src/modules/generic-page/locales/` ⚠️ (Thiếu zh translation)
- [ ] `src/modules/generic-page-simple/i18n/`

## Tiêu chí hoàn thành
1. ✅ Tất cả module có đầy đủ 3 file translation (en, vi, zh)
2. ✅ Không có lỗi import hoặc cấu trúc
3. ✅ Tất cả key translation đều có đầy đủ 3 ngôn ngữ
4. ✅ UI hiển thị đúng khi chuyển đổi ngôn ngữ
5. ✅ Build thành công không có warning về i18n
6. ✅ Fallback mechanism hoạt động đúng

## Báo cáo tiến độ
- **Bắt đầu**: 28/12/2024 - 15:30
- **Giai đoạn hiện tại**: Giai đoạn 2 - Đang sửa lỗi cấu trúc và import
- **Hoàn thành**: [Thời gian hoàn thành]
- **Tổng thời gian**: [Tổng thời gian thực hiện]

## Phát hiện từ Giai đoạn 1

### Các module thiếu file translation
1. ❌ `src/modules/generic-page/i18n/` - Thiếu `zh.json`
2. ❌ `src/modules/agent/locales/` - Thư mục trống
3. ❌ `src/modules/animation/locales/` - Thư mục trống

### Lỗi cấu trúc đã phát hiện
1. ❌ Duplicate import trong `src/lib/i18n.ts`:
   - `rpointAdminResources` từ `@/modules/rpoint/admin`
   - `rpointAdminResources as newRPointAdminResources` từ `@/modules/admin/r-point/locales`

### Modules có cấu trúc đúng
- ✅ `src/modules/marketing/locales/` - Đầy đủ en, vi, zh
- ✅ `src/modules/components/locales/` - Đầy đủ en, vi, zh
- ✅ `src/modules/integration/locales/` - Đầy đủ en, vi, zh
- ✅ `src/modules/data/locales/` - Đầy đủ en, vi, zh
- ✅ `src/modules/profile/locales/` - Đầy đủ en, vi, zh
- ✅ `src/modules/calendar/locales/` - Đầy đủ en, vi, zh

---
*Cập nhật lần cuối: 28/12/2024*
