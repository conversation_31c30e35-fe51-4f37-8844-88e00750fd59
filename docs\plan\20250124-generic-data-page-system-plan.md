# Kế hoạch triển khai Generic Data Page System

## Tổng quan
Tạo một hệ thống Generic Data Page cho phép render các trang quản lý dữ liệu từ JSON configuration, dựa trên pattern hiện có của MediaPage, KnowledgeFilesPage, UrlPage.

## Mục tiêu
1. Tạo component có thể tái sử dụng cho các trang CRUD
2. Giảm code duplication
3. <PERSON><PERSON> dàng tạo trang mới chỉ bằng JSON config
4. Maintain consistency trong UI/UX

## Phân tích Pattern hiện tại

### Cấu trúc chung các trang:
```
- MenuIconBar (search, add, filters, column visibility, bulk actions)
- ActiveFilters (hiển thị filters đang active)
- SlideInForm (form thêm/sửa)
- Card > Table (hiển thị dữ liệu với pagination)
- ConfirmDeleteModal (xác nhận xóa)
```

### Hooks sử dụng:
- `useDataTable` - quản lý table state
- `useSlideForm` - quản lý form animation
- `useActiveFilters` - quản lý active filters
- Custom query hooks (useMediaList, useUrls, etc.)

### Components chính:
- `Table` với pagination, sorting, selection
- `MenuIconBar` với search, actions
- `ActiveFilters` hiển thị filters
- `SlideInForm` cho forms
- `ConfirmDeleteModal` cho xác nhận

## Thiết kế Architecture

### 1. Core Types & Interfaces

#### PageConfig Interface
```typescript
interface GenericPageConfig<T = any> {
  // Metadata
  title: string;
  description?: string;

  // API Configuration
  api: {
    endpoint: string;
    queryKey: string;
    methods: {
      list: string;
      create?: string;
      update?: string;
      delete?: string;
      bulkDelete?: string;
    };
  };

  // Table Configuration
  table: {
    columns: ColumnConfig[];
    rowKey: string;
    selectable?: boolean;
    sortable?: boolean;
    pagination?: PaginationConfig;
  };

  // Form Configuration
  form?: {
    fields: FormFieldConfig[];
    validation?: ValidationConfig;
  };

  // Filter Configuration
  filters?: FilterConfig[];

  // Actions Configuration
  actions?: ActionConfig[];

  // Permissions
  permissions?: {
    create?: boolean;
    read?: boolean;
    update?: boolean;
    delete?: boolean;
    bulkDelete?: boolean;
  };
}
```

#### Column Configuration
```typescript
interface ColumnConfig {
  key: string;
  title: string;
  dataIndex?: string;
  width?: string | number;
  sortable?: boolean;
  render?: {
    type: 'text' | 'image' | 'chip' | 'date' | 'actions' | 'custom';
    props?: Record<string, any>;
  };
  visible?: boolean;
}
```

#### Form Field Configuration
```typescript
interface FormFieldConfig {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'date' | 'file' | 'number';
  required?: boolean;
  validation?: ValidationRule[];
  props?: Record<string, any>;
  options?: SelectOption[];
}
```

### 2. Core Components

#### GenericDataPage Component
```typescript
interface GenericDataPageProps<T = any> {
  config: GenericPageConfig<T>;
  customHooks?: {
    useList?: (params: any) => any;
    useCreate?: () => any;
    useUpdate?: () => any;
    useDelete?: () => any;
  };
  customRenderers?: {
    [key: string]: (value: any, record: T) => React.ReactNode;
  };
  onDataChange?: (data: T[]) => void;
}
```

### 3. Utility Functions

#### Column Generator
- Tạo table columns từ config
- Handle các render types khác nhau
- Support custom renderers

#### Form Generator
- Tạo dynamic forms từ config
- Handle validation
- Support custom field types

#### API Integration
- Generic hooks cho CRUD operations
- Configurable endpoints
- Error handling

## Implementation Plan

### Phase 1: Core Infrastructure (Ngày 1)
1. Tạo type definitions
2. Implement GenericDataPage component cơ bản
3. Tạo column generator utility
4. Tạo basic form generator

### Phase 2: Advanced Features (Ngày 2)
1. Implement filter system
2. Add custom renderers support
3. Enhance form generator với validation
4. Add bulk operations support

### Phase 3: Demo & Testing (Ngày 3)
1. Tạo demo pages với JSON configs
2. Test với các use cases khác nhau
3. Optimize performance
4. Documentation

## File Structure

```
src/shared/components/generic/
├── index.ts
├── GenericDataPage.tsx
├── types/
│   ├── index.ts
│   ├── page-config.types.ts
│   ├── column-config.types.ts
│   └── form-config.types.ts
├── hooks/
│   ├── index.ts
│   ├── useGenericDataPage.ts
│   ├── useGenericApi.ts
│   └── useGenericForm.ts
├── utils/
│   ├── index.ts
│   ├── column-generator.ts
│   ├── form-generator.ts
│   └── config-validator.ts
├── components/
│   ├── index.ts
│   ├── GenericTable.tsx
│   ├── GenericForm.tsx
│   └── GenericFilters.tsx
└── demo/
    ├── index.ts
    ├── configs/
    │   ├── users-config.ts
    │   ├── products-config.ts
    │   └── orders-config.ts
    └── pages/
        ├── DemoUsersPage.tsx
        ├── DemoProductsPage.tsx
        └── DemoOrdersPage.tsx
```

## Demo Pages

### 1. Users Management Demo
- CRUD operations cho users
- Filters: status, role, date range
- Bulk operations: activate/deactivate, delete
- Form: name, email, role, avatar upload

### 2. Products Management Demo
- CRUD operations cho products
- Filters: category, status, price range
- Bulk operations: update status, delete
- Form: name, description, price, images, category

### 3. Orders Management Demo
- Read-only orders list
- Filters: status, date range, customer
- Export functionality
- Detail view modal

## Success Criteria
1. ✅ Có thể tạo trang CRUD hoàn chỉnh chỉ với JSON config
2. ✅ Code reuse tối thiểu 80% so với implement manual
3. ✅ Performance tương đương với pages hiện tại
4. ✅ Support đầy đủ features: CRUD, filters, bulk operations, pagination
5. ✅ Easy to extend với custom renderers và hooks

## Implementation Status

### ✅ Phase 1: Core Infrastructure (COMPLETED)
- ✅ Type definitions (page-config.types.ts)
- ✅ GenericDataPage component
- ✅ Column generator utility
- ✅ Form generator utility
- ✅ Generic API hooks
- ✅ Main useGenericDataPage hook

### ✅ Phase 2: Advanced Features (COMPLETED)
- ✅ Filter system implementation
- ✅ Custom renderers support
- ✅ Form generator với validation
- ✅ Bulk operations support
- ✅ Custom handlers support

### ✅ Phase 3: Demo & Testing (COMPLETED)
- ✅ Demo configurations (users, products, orders)
- ✅ Demo pages với mock data
- ✅ Demo index page với showcase
- ✅ Custom renderers examples
- ✅ Custom handlers examples

## Files Created

### Core System
- `src/shared/components/generic/types/page-config.types.ts`
- `src/shared/components/generic/GenericDataPage.tsx`
- `src/shared/components/generic/utils/column-generator.ts`
- `src/shared/components/generic/utils/form-generator.tsx`
- `src/shared/components/generic/hooks/useGenericApi.ts`
- `src/shared/components/generic/hooks/useGenericDataPage.ts`
- `src/shared/components/generic/components/GenericForm.tsx`

### Demo System
- `src/shared/components/generic/demo/configs/users-config.ts`
- `src/shared/components/generic/demo/configs/products-config.ts`
- `src/shared/components/generic/demo/configs/orders-config.ts`
- `src/shared/components/generic/demo/pages/DemoUsersPage.tsx`
- `src/shared/components/generic/demo/pages/DemoProductsPage.tsx`
- `src/shared/components/generic/demo/pages/DemoOrdersPage.tsx`
- `src/shared/components/generic/demo/DemoIndexPage.tsx`

## Usage Examples

### Basic Usage
```typescript
import { GenericDataPage } from '@/shared/components/generic';
import { usersConfig } from './configs/users-config';

const UsersPage = () => (
  <GenericDataPage config={usersConfig} />
);
```

### With Custom Features
```typescript
<GenericDataPage
  config={productsConfig}
  customHooks={customApiHooks}
  customRenderers={customColumnRenderers}
  customHandlers={customActionHandlers}
/>
```

## Next Steps
1. ✅ Integration testing với existing codebase
2. ✅ Performance optimization
3. ✅ Documentation completion
4. 🔄 Real API integration examples
5. 🔄 Additional field types support
6. 🔄 Advanced filtering options
