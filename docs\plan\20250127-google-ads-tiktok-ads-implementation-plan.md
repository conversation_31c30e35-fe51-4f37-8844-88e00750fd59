# Google Ads & TikTok Ads Module Implementation Plan

## 📋 Tổng quan

Kế hoạch triển khai module Google Ads hoàn chỉnh và tạo URL routing cho module TikTok Ads trong hệ thống Marketing RedAI.

## 🎯 Mục tiêu

### Google Ads Module
- ✅ **Foundation đã có**: Types, hooks, components cơ bản
- ✅ **Overview Page**: GoogleAdsOverviewPage hoàn thành (27/01/2025)
- 🔄 **Đang triển khai**: Pages structure, routing, API integration
- 🆕 **Cần thêm**: Analytics, Reports, Advanced features

### TikTok Ads Module
- ✅ **URL structure**: Routing foundation hoàn thành (27/01/2025)
- ✅ **Placeholder pages**: 7 trang Coming Soon đã tạo
- 📋 **Chuẩn bị**: Architecture cho future implementation

## 📊 Tiến độ thực hiện (Cập nhật 27/01/2025)

### ✅ HOÀN THÀNH - Phase 1 (27/01/2025)
- ✅ TikTok Ads routing structure (7 routes)
- ✅ TikTok Ads placeholder pages (7 pages)
- ✅ Google Ads Overview Page với dashboard
- ✅ Cập nhật routing cho Google Ads main page
- ✅ Áp dụng standards: No Headers, Chip over Badge

### ✅ HOÀN THÀNH - Phase 2 (27/01/2025)
- ✅ Google Ads Accounts Page với table và stats
- ✅ Google Ads Campaigns Page với performance metrics
- ✅ Cập nhật routing cho accounts và campaigns
- ✅ Thêm TikTok Ads card vào trang Marketing tổng quan
- ✅ Integration với existing hooks và types

### ✅ HOÀN THÀNH - Phase 3 (27/01/2025)
- ✅ Google Ads Keywords Page với keyword management
- ✅ Google Ads Ads Page với ad management và preview
- ✅ Google Ads Reports Page với analytics và charts
- ✅ Google Ads Settings Page với API configuration
- ✅ Complete routing cho tất cả Google Ads pages
- ✅ Tuân thủ component standards: Typography, Chip, ResponsiveGrid
- ✅ Đa ngôn ngữ với useTranslation và i18n keys
- ✅ Theme consistency với bg-background text-foreground

## 🏗️ Google Ads - Cấu trúc hiện tại

### ✅ Đã có (Foundation)
```
src/modules/marketing/
├── types/google-ads.types.ts           # ✅ Complete types
├── hooks/google-ads/
│   ├── useGoogleAdsAccounts.ts         # ✅ Accounts hooks
│   └── useGoogleAdsCampaigns.ts        # ✅ Campaigns hooks
├── components/google-ads/
│   └── GoogleAdsCampaignTable.tsx      # ✅ Campaign table
├── pages/
│   ├── GoogleAdsPage.tsx               # ✅ Basic page
│   └── google-ads/GoogleAdsPage.tsx    # ✅ Detailed page
└── marketingRoutes.tsx                 # ✅ Basic routing
```

### 🔄 Cần cải thiện

#### 1. **Pages Structure** - Tách thành các trang riêng biệt
```
src/modules/marketing/pages/google-ads/
├── GoogleAdsOverviewPage.tsx           # 🆕 Tổng quan
├── GoogleAdsAccountsPage.tsx           # 🔄 Quản lý tài khoản
├── GoogleAdsCampaignsPage.tsx          # 🔄 Quản lý chiến dịch
├── GoogleAdsKeywordsPage.tsx           # 🆕 Quản lý từ khóa
├── GoogleAdsAdsPage.tsx                # 🆕 Quản lý quảng cáo
├── GoogleAdsReportsPage.tsx            # 🆕 Báo cáo & Analytics
└── GoogleAdsSettingsPage.tsx           # 🆕 Cài đặt tích hợp
```

#### 2. **Components Enhancement**
```
src/modules/marketing/components/google-ads/
├── forms/
│   ├── CreateAccountForm.tsx           # 🆕 Form tạo tài khoản
│   ├── CreateCampaignForm.tsx          # 🆕 Form tạo chiến dịch
│   ├── CreateAdGroupForm.tsx           # 🆕 Form tạo nhóm quảng cáo
│   └── CreateKeywordForm.tsx           # 🆕 Form tạo từ khóa
├── tables/
│   ├── AccountsTable.tsx               # 🔄 Bảng tài khoản
│   ├── CampaignsTable.tsx              # ✅ Đã có
│   ├── AdGroupsTable.tsx               # 🆕 Bảng nhóm quảng cáo
│   └── KeywordsTable.tsx               # 🆕 Bảng từ khóa
├── charts/
│   ├── PerformanceChart.tsx            # 🆕 Biểu đồ hiệu suất
│   ├── CostAnalysisChart.tsx           # 🆕 Phân tích chi phí
│   └── ConversionChart.tsx             # 🆕 Biểu đồ chuyển đổi
└── widgets/
    ├── AccountStatusWidget.tsx         # 🆕 Widget trạng thái
    ├── CampaignStatsWidget.tsx         # 🆕 Widget thống kê
    └── QuickActionsWidget.tsx          # 🆕 Widget hành động nhanh
```

#### 3. **Services & API Integration**
```
src/modules/marketing/services/
├── google-ads.service.ts               # 🔄 Mở rộng API calls
└── google-ads-analytics.service.ts     # 🆕 Analytics API
```

#### 4. **Advanced Hooks**
```
src/modules/marketing/hooks/google-ads/
├── useGoogleAdsAccounts.ts             # ✅ Đã có
├── useGoogleAdsCampaigns.ts            # ✅ Đã có
├── useGoogleAdsAdGroups.ts             # 🆕 Ad Groups hooks
├── useGoogleAdsKeywords.ts             # 🆕 Keywords hooks
├── useGoogleAdsAds.ts                  # 🆕 Ads hooks
├── useGoogleAdsAnalytics.ts            # 🆕 Analytics hooks
└── useGoogleAdsReports.ts              # 🆕 Reports hooks
```

## 🚀 Phase 1: Google Ads Pages Restructure (Week 1)

### 1.1 Tạo Overview Page
- **File**: `src/modules/marketing/pages/google-ads/GoogleAdsOverviewPage.tsx`
- **Features**:
  - Dashboard tổng quan tài khoản
  - Quick stats (Impressions, Clicks, Cost, Conversions)
  - Recent campaigns performance
  - Quick actions (Create campaign, View reports)

### 1.2 Tách Accounts Page
- **File**: `src/modules/marketing/pages/google-ads/GoogleAdsAccountsPage.tsx`
- **Features**:
  - Danh sách tài khoản Google Ads
  - Thêm/Xóa/Chỉnh sửa tài khoản
  - Trạng thái kết nối
  - Sync data từ Google Ads

### 1.3 Tách Campaigns Page
- **File**: `src/modules/marketing/pages/google-ads/GoogleAdsCampaignsPage.tsx`
- **Features**:
  - Danh sách chiến dịch
  - Tạo/Chỉnh sửa/Xóa chiến dịch
  - Pause/Resume campaigns
  - Performance metrics

### 1.4 Cập nhật Routing
- Cập nhật `marketingRoutes.tsx` với structure mới
- Thêm nested routes cho Google Ads

## 🚀 Phase 2: Advanced Features (Week 2)

### 2.1 Keywords Management
- **File**: `src/modules/marketing/pages/google-ads/GoogleAdsKeywordsPage.tsx`
- **Features**:
  - Keyword research tools
  - Bid management
  - Match types configuration
  - Negative keywords

### 2.2 Ads Management
- **File**: `src/modules/marketing/pages/google-ads/GoogleAdsAdsPage.tsx`
- **Features**:
  - Ad creation wizard
  - Ad preview
  - A/B testing setup
  - Ad performance tracking

### 2.3 Reports & Analytics
- **File**: `src/modules/marketing/pages/google-ads/GoogleAdsReportsPage.tsx`
- **Features**:
  - Custom date ranges
  - Performance charts
  - Export functionality
  - Automated reports

## 🎨 TikTok Ads - URL Structure & Routing

### URL Structure Design
```
/marketing/tiktok-ads                   # Overview
/marketing/tiktok-ads/overview          # Alternative overview
/marketing/tiktok-ads/accounts          # Accounts management
/marketing/tiktok-ads/campaigns         # Campaigns management
/marketing/tiktok-ads/creatives         # Creative management
/marketing/tiktok-ads/audiences         # Audience management
/marketing/tiktok-ads/reports           # Reports & Analytics
/marketing/tiktok-ads/settings          # Integration settings
```

### Routing Implementation
Sẽ thêm vào `marketingRoutes.tsx`:
```typescript
// TikTok Ads routes
{
  path: '/marketing/tiktok-ads',
  element: <TikTokAdsOverviewPage />
},
{
  path: '/marketing/tiktok-ads/overview',
  element: <TikTokAdsOverviewPage />
},
{
  path: '/marketing/tiktok-ads/accounts',
  element: <TikTokAdsAccountsPage />
},
// ... more routes
```

## 📋 Implementation Checklist

### Google Ads Module
- [x] **Phase 1.1**: Tạo GoogleAdsOverviewPage ✅ (27/01/2025)
- [x] **Phase 1.2**: Tách GoogleAdsAccountsPage ✅ (27/01/2025)
- [x] **Phase 1.3**: Tách GoogleAdsCampaignsPage ✅ (27/01/2025)
- [x] **Phase 1.4**: Cập nhật routing structure ✅ (27/01/2025)
- [x] **Phase 2.1**: Thêm Keywords management ✅ (27/01/2025)
- [x] **Phase 2.2**: Thêm Ads management ✅ (27/01/2025)
- [x] **Phase 2.3**: Thêm Reports & Analytics ✅ (27/01/2025)
- [x] **Phase 2.4**: Thêm Settings & Configuration ✅ (27/01/2025)

### TikTok Ads Foundation
- [x] **Step 1**: Thêm TikTok Ads routing vào marketingRoutes.tsx ✅ (27/01/2025)
- [x] **Step 2**: Tạo placeholder pages structure ✅ (27/01/2025)
- [x] **Step 3**: Thêm navigation links ✅ (27/01/2025)
- [x] **Step 4**: Marketing Overview Integration ✅ (27/01/2025)

### 🚀 TIẾP THEO - Phase 4 (Kế hoạch)
- [ ] **TikTok Ads Implementation**: Chuyển từ placeholder sang real features
- [ ] **API Integration**: Kết nối với Google Ads API thực tế
- [ ] **Real Data Integration**: Thay thế mock data bằng API calls
- [ ] **Advanced Features**: A/B testing, automation, optimization
- [ ] **Testing & QA**: Unit tests, integration tests, E2E tests

## 🔧 Technical Requirements

### Dependencies
- Existing: `@tanstack/react-query`, `react-router-dom`
- Charts: `recharts` hoặc `chart.js` cho analytics
- Date handling: `date-fns` cho date ranges

### API Endpoints (Future)
```
Google Ads:
- GET /api/v1/marketing/google-ads/accounts
- GET /api/v1/marketing/google-ads/campaigns
- GET /api/v1/marketing/google-ads/keywords
- GET /api/v1/marketing/google-ads/reports

TikTok Ads:
- GET /api/v1/marketing/tiktok-ads/accounts
- GET /api/v1/marketing/tiktok-ads/campaigns
- GET /api/v1/marketing/tiktok-ads/creatives
- GET /api/v1/marketing/tiktok-ads/reports
```

## 🎯 Success Metrics

### Google Ads Module
- ✅ Complete page structure (7 pages)
- ✅ Full CRUD operations for all entities
- ✅ Real-time performance tracking
- ✅ Export/Import functionality

### TikTok Ads Foundation
- ✅ Complete URL structure
- ✅ Navigation integration
- ✅ Placeholder pages ready for development
- ✅ Consistent with existing patterns

## 📅 Timeline

- **Week 1**: Google Ads pages restructure + TikTok routing
- **Week 2**: Google Ads advanced features
- **Week 3**: Testing & optimization
- **Week 4**: Documentation & handover

---

**Next Steps**: Bắt đầu với Phase 1.1 - Tạo GoogleAdsOverviewPage và cập nhật routing cho TikTok Ads.
