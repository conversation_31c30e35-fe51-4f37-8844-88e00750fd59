# Product API Backend Requirements

## 📋 Tổng Quan

Docs API để Backend khớp với Frontend dựa trên phân tích từ `frontend-backend-product-api-analysis.md`.

## 🎯 Hướng 1: Cập nhật Backend để hỗ trợ Frontend (Recommended)

### 1. Cậ<PERSON> nhật `BusinessCreateProductDto`

```typescript
export class BusinessCreateProductDto {
  @IsString() @IsNotEmpty() @MaxLength(255)
  name: string;

  @IsOptional() @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum; // ✅ THÊM MỚI

  @IsNotEmpty()
  price: HasPriceDto | StringPriceDto | null;

  @IsEnum(PriceTypeEnum) @IsNotEmpty()
  typePrice: PriceTypeEnum;

  @IsOptional() @IsString() @MaxLength(1000)
  description?: string;

  @IsOptional() @IsArray() @IsString({ each: true })
  tags?: string[];

  @IsOptional() @IsObject() @ValidateNested()
  shipmentConfig?: BusinessShipmentConfigDto;

  @IsOptional() @IsArray() @ValidateNested({ each: true })
  classifications?: CreateClassificationDto[];

  @IsOptional() @IsArray() @ValidateNested({ each: true })
  customFields?: CreateCustomFieldDto[]; // ✅ THÊM MỚI

  @IsOptional() @IsArray() @IsString({ each: true })
  imagesMediaTypes?: string[]; // ✅ THÊM MỚI - Media types của ảnh sản phẩm

  // ❌ XÓA customGroupForm theo yêu cầu frontend
  // @IsOptional() @IsObject() @ValidateNested()
  // customGroupForm?: CreateCustomGroupFormDto;

  @IsOptional() @IsArray() @ValidateNested({ each: true })
  classifications?: CreateProductVariantDto[]; // ✅ ĐỔI TÊN từ variants thành classifications
}
```

### 2. Tạo DTOs mới cần thiết

#### `CreateCustomFieldDto`
```typescript
export class CreateCustomFieldDto {
  @IsString() @IsNotEmpty() @MaxLength(255)
  name: string;

  @IsString() @IsNotEmpty()
  type: string; // 'text' | 'number' | 'select' | 'date' | etc.

  @IsOptional() @IsString()
  value?: string;

  @IsOptional() @IsBoolean()
  required?: boolean;

  @IsOptional() @IsArray() @IsString({ each: true })
  options?: string[]; // For select type

  @IsOptional() @IsString() @MaxLength(500)
  description?: string;
}
```

// ❌ XÓA CreateCustomGroupFormDto theo yêu cầu frontend

#### `CreateProductVariantDto`
```typescript
export class CreateProductVariantDto {
  @IsString() @IsNotEmpty() @MaxLength(255)
  name: string;

  @IsOptional() @IsString() @MaxLength(100)
  sku?: string;

  @IsOptional() @IsObject() @ValidateNested()
  price?: HasPriceDto | StringPriceDto;

  @IsOptional() @IsNumber() @Min(0)
  stock?: number;

  @IsOptional() @IsArray() @IsString({ each: true })
  attributes?: string[]; // ['Color: Red', 'Size: L']

  @IsOptional() @IsString() @MaxLength(500)
  description?: string;

  @IsOptional() @IsBoolean()
  isActive?: boolean;
}
```

### 3. Cập nhật Service Layer

```typescript
// user-product.service.ts
async createProduct(
  createProductDto: BusinessCreateProductDto, 
  userId: number
): Promise<ProductResponseDto> {
  
  // Validate business logic
  await this.validateProductData(createProductDto);
  
  // Create main product
  const product = await this.productRepository.create({
    ...createProductDto,
    createdBy: userId,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  });

  // Handle custom fields if provided
  if (createProductDto.customFields?.length > 0) {
    await this.createProductCustomFields(product.id, createProductDto.customFields);
  }

  // ❌ XÓA customGroupForm theo yêu cầu frontend
  // if (createProductDto.customGroupForm) {
  //   await this.createProductCustomGroupForm(product.id, createProductDto.customGroupForm);
  // }

  // Handle classifications if provided (đổi tên từ variants)
  if (createProductDto.classifications?.length > 0) {
    await this.createProductClassifications(product.id, createProductDto.classifications);
  }

  return this.mapToResponseDto(product);
}

private async validateProductData(dto: BusinessCreateProductDto): Promise<void> {
  // Validate price consistency
  if (dto.typePrice === PriceTypeEnum.HAS_PRICE && !dto.price) {
    throw new BadRequestException('Price is required when typePrice is HAS_PRICE');
  }

  if (dto.typePrice === PriceTypeEnum.STRING_PRICE && 
      (!dto.price || !('priceDescription' in dto.price))) {
    throw new BadRequestException('Price description is required when typePrice is STRING_PRICE');
  }

  // Validate classifications (đổi tên từ variants)
  if (dto.classifications?.length > 0) {
    const skus = dto.classifications.map(v => v.sku).filter(Boolean);
    if (skus.length !== new Set(skus).size) {
      throw new BadRequestException('Classification SKUs must be unique');
    }
  }
}
```

### 4. Cập nhật Response DTO

```typescript
export class ProductResponseDto {
  id: number;
  name: string;
  productType?: ProductTypeEnum; // ✅ THÊM MỚI
  price: HasPriceDto | StringPriceDto | null;
  typePrice: PriceTypeEnum;
  description?: string;
  tags?: string[];
  shipmentConfig?: BusinessShipmentConfigDto;
  classifications?: ProductVariantResponseDto[]; // ✅ ĐỔI TÊN từ variants, sử dụng cấu trúc ProductVariantDto
  customFields?: CustomFieldResponseDto[]; // ✅ THÊM MỚI
  // ❌ XÓA customGroupForm theo yêu cầu frontend
  // customGroupForm?: CustomGroupFormResponseDto;
  createdAt: number;
  updatedAt: number;
  createdBy: number;
}

export class CreateProductResponseDto {
  product: ProductResponseDto;
  uploadUrls?: {
    imagesUploadUrls?: string[]; // ✅ THÊM MỚI - URLs để upload ảnh lên cloud
  };
}
```

### 5. Database Schema Updates

#### Products Table
```sql
ALTER TABLE products ADD COLUMN product_type VARCHAR(50) DEFAULT 'PHYSICAL';
```

#### Custom Fields Table
```sql
CREATE TABLE product_custom_fields (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL,
  value TEXT,
  required BOOLEAN DEFAULT FALSE,
  options JSON,
  description VARCHAR(500),
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);
```

#### Custom Group Forms Table
```sql
CREATE TABLE product_custom_group_forms (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description VARCHAR(500),
  config JSON,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);
```

#### Product Variants Table
```sql
CREATE TABLE product_variants (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  sku VARCHAR(100) UNIQUE,
  price JSON, -- Store HasPriceDto or StringPriceDto
  stock INTEGER DEFAULT 0,
  attributes JSON, -- Array of attribute strings
  description VARCHAR(500),
  is_active BOOLEAN DEFAULT TRUE,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);
```

## 🎯 Hướng 2: Giữ Backend hiện tại, cập nhật Frontend (Alternative)

### Cập nhật Frontend CreateProductDto
```typescript
export interface CreateProductDto {
  name: string;
  // Xóa: productType: ProductTypeEnum;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: PriceTypeEnum;
  description?: string;
  tags?: string[];
  shipmentConfig?: ShipmentConfigDto;
  classifications?: ClassificationDto[];
  // Xóa: customFields?: CustomFieldDto[];
  // Xóa: customGroupForm?: CustomGroupFormDto;
  // Xóa: variants?: ProductVariantDto[];
}
```

### Cập nhật Form Processing
```typescript
const productData: CreateProductDto = {
  name: formValues.name,
  typePrice: formValues.typePrice,
  price: getPriceData(formValues),
  description: formValues.description,
  tags: formValues.tags || [],
  shipmentConfig: formValues.shipmentConfig ? {
    lengthCm: Number(formValues.shipmentConfig.lengthCm) || undefined,
    widthCm: Number(formValues.shipmentConfig.widthCm) || undefined,
    heightCm: Number(formValues.shipmentConfig.heightCm) || undefined,
    weightGram: Number(formValues.shipmentConfig.weightGram) || undefined,
  } : undefined,
  classifications: formValues.classifications || [],
};
```

## 🚀 Recommendation

**Chọn Hướng 1** - Cập nhật Backend để hỗ trợ đầy đủ Frontend vì:

1. **Tính năng đầy đủ**: Hỗ trợ custom fields, variants, product types
2. **Tương lai**: Dễ mở rộng và phát triển
3. **UX tốt hơn**: Frontend không cần loại bỏ tính năng
4. **Consistency**: Đồng bộ giữa Frontend và Backend

## 📋 Implementation Checklist

### Phase 1: Core Updates
- [ ] Cập nhật `BusinessCreateProductDto`
- [ ] Tạo các DTOs mới (CustomField, CustomGroupForm, ProductVariant)
- [ ] Cập nhật database schema
- [ ] Cập nhật service layer

### Phase 2: Advanced Features  
- [ ] Implement custom fields logic
- [ ] Implement variants logic
- [ ] Add validation rules
- [ ] Update response DTOs

### Phase 3: Testing & Documentation
- [ ] Unit tests cho DTOs mới
- [ ] Integration tests cho API
- [ ] Update API documentation
- [ ] Frontend integration testing
