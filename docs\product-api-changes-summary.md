# Product API Changes Summary

## 📋 Tóm tắt thay đổi theo yêu cầu

Dựa trên yêu cầ<PERSON> củ<PERSON> user, đã thực hiện các thay đổi sau:

## 🔄 Thay đổi Frontend

### 1. **Xóa `customGroupForm`**
- ❌ Xóa khỏi `CreateProductDto` interface
- ❌ Xóa khỏi `UpdateProductDto` interface  
- ❌ Xóa khỏi form processing logic
- ❌ Xóa khỏi validation schema

### 2. **Đổi tên `variants` thành `classifications`**
- ✅ Đổi tên field trong `CreateProductDto` và `UpdateProductDto`
- ✅ Giữ nguyên cấu trúc dữ liệu của `ProductVariantDto`
- ✅ Cập nhật form state từ `productVariants` thành `productClassifications`
- ✅ Cập nhật tất cả handlers và functions liên quan

### 3. **Xóa `classifications` cũ**
- ❌ Xóa field `classifications?: ClassificationDto[]` cũ
- ✅ Thay thế bằng `classifications?: ProductVariantDto[]` mới

### 4. **Thêm `imagesMediaTypes`**
- ✅ Thêm field `imagesMediaTypes?: string[]` để chứa media types của ảnh
- ✅ API sẽ trả về `uploadUrls.imagesUploadUrls` để frontend upload lên cloud
- ✅ Sử dụng TaskQueue pattern theo MediaPage để upload ảnh

## 📁 Files đã cập nhật

### Frontend Types
```typescript
// src/modules/business/types/product.types.ts
export interface CreateProductDto {
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: PriceTypeEnum;
  description?: string;
  tags?: string[];
  shipmentConfig?: ShipmentConfigDto;
  imagesMediaTypes?: string[]; // ✅ THÊM MỚI - Media types của ảnh
  classifications?: ProductVariantDto[]; // ✅ ĐỔI TÊN từ variants
  customFields?: CustomFieldDto[];
  // ❌ XÓA customGroupForm
  // ❌ XÓA classifications cũ
}

export interface CreateProductResponse {
  product: ProductDto;
  uploadUrls?: {
    imagesUploadUrls?: string[]; // ✅ THÊM MỚI - URLs để upload ảnh
  };
}
```

### Frontend Form Component
```typescript
// src/modules/business/components/forms/ProductForm.tsx
interface ProductFormValues {
  // ... other fields
  classifications?: ProductVariant[]; // ✅ ĐỔI TÊN từ variants
}

// State management
const [productClassifications, setProductClassifications] = useState<ProductVariant[]>([]);

// Form data processing
const productData: CreateProductDto = {
  // ... other fields
  imagesMediaTypes: mediaFiles.length > 0 ? mediaFiles.map(file => file.file.type) : undefined,
  classifications: productClassifications.length > 0 ? productClassifications.map(variant => ({
    id: variant.id,
    name: variant.name,
    listPrice: Number(variant.listPrice) || 0,
    salePrice: Number(variant.salePrice) || 0,
    currency: variant.currency,
    customFields: variant.customFields,
  })) : undefined,
};

// Upload handling với TaskQueue theo pattern MediaPage
const response = await onSubmit(productData);
if (mediaFiles.length > 0 && response.uploadUrls?.imagesUploadUrls) {
  await uploadProductImages(mediaFiles, response.uploadUrls.imagesUploadUrls);
}
```

## 🔄 Thay đổi Backend (Recommended)

### 1. **Cập nhật DTO**
```typescript
// backend/business/user/dto/business-create-product.dto.ts
export class BusinessCreateProductDto {
  // ... existing fields
  
  @IsOptional() @IsArray() @ValidateNested({ each: true })
  customFields?: CreateCustomFieldDto[];

  // ❌ XÓA customGroupForm
  // @IsOptional() @IsObject() @ValidateNested()
  // customGroupForm?: CreateCustomGroupFormDto;

  @IsOptional() @IsArray() @ValidateNested({ each: true })
  classifications?: CreateProductVariantDto[]; // ✅ ĐỔI TÊN từ variants
}
```

### 2. **Cập nhật Service Logic**
```typescript
// backend/business/user/services/user-product.service.ts
async createProduct(createProductDto: BusinessCreateProductDto, userId: number) {
  // ... existing logic

  // ❌ XÓA customGroupForm handling
  // if (createProductDto.customGroupForm) {
  //   await this.createProductCustomGroupForm(product.id, createProductDto.customGroupForm);
  // }

  // ✅ ĐỔI TÊN từ variants thành classifications
  if (createProductDto.classifications?.length > 0) {
    await this.createProductClassifications(product.id, createProductDto.classifications);
  }
}

// ✅ ĐỔI TÊN validation
private async validateProductData(dto: BusinessCreateProductDto) {
  // Validate classifications (đổi tên từ variants)
  if (dto.classifications?.length > 0) {
    const skus = dto.classifications.map(v => v.sku).filter(Boolean);
    if (skus.length !== new Set(skus).size) {
      throw new BadRequestException('Classification SKUs must be unique');
    }
  }
}
```

### 3. **Cập nhật Response DTO**
```typescript
// backend/business/user/dto/product-response.dto.ts
export class ProductResponseDto {
  // ... existing fields
  
  classifications?: ProductVariantResponseDto[]; // ✅ ĐỔI TÊN từ variants
  customFields?: CustomFieldResponseDto[];
  
  // ❌ XÓA customGroupForm
  // customGroupForm?: CustomGroupFormResponseDto;
}
```

## 🗄️ Database Changes

### Không cần thay đổi Database
- Giữ nguyên table `product_variants` 
- Chỉ đổi tên field trong code logic
- Có thể đổi tên table sau nếu muốn: `product_variants` → `product_classifications`

## 🔍 API Specification Update

### Request Example
```json
{
  "name": "iPhone 15 Pro Max",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 29990000,
    "salePrice": 27990000,
    "currency": "VND"
  },
  "description": "iPhone 15 Pro Max với chip A17 Pro",
  "tags": ["smartphone", "apple", "premium"],
  "shipmentConfig": {
    "lengthCm": 20,
    "widthCm": 10,
    "heightCm": 2,
    "weightGram": 221
  },
  "customFields": [
    {
      "name": "Màn hình",
      "type": "text",
      "value": "6.7 inch Super Retina XDR",
      "required": true
    }
  ],
  "classifications": [  // ✅ ĐỔI TÊN từ variants
    {
      "name": "iPhone 15 Pro Max - Titan Tự Nhiên - 256GB",
      "listPrice": 29990000,
      "salePrice": 27990000,
      "currency": "VND",
      "customFields": [
        {
          "name": "Màu sắc",
          "type": "select",
          "value": "Titan Tự Nhiên"
        }
      ]
    }
  ]
  // ❌ XÓA customGroupForm
}
```

## ✅ Validation Rules

### Frontend Validation
- ✅ Tên sản phẩm: required, max 255 chars
- ✅ Classifications: optional array
- ✅ Custom fields: optional array
- ❌ Không validate customGroupForm nữa

### Backend Validation  
- ✅ Classifications SKUs must be unique
- ✅ Custom fields type validation
- ❌ Không validate customGroupForm nữa

## 🚀 Implementation Status

### ✅ Completed
- [x] Frontend types updated
- [x] Frontend form component updated
- [x] Frontend state management updated
- [x] Frontend data processing updated
- [x] Documentation updated

### 📋 Next Steps
1. **Backend Implementation**
   - [ ] Update DTOs
   - [ ] Update service logic
   - [ ] Update validation
   - [ ] Update response DTOs
   
2. **Testing**
   - [ ] Frontend form testing
   - [ ] API integration testing
   - [ ] End-to-end testing

3. **Optional Database Rename**
   - [ ] Rename `product_variants` table to `product_classifications`
   - [ ] Update entity mappings

## 🎯 Benefits

### 1. **Simplified Structure**
- Loại bỏ `customGroupForm` phức tạp
- Tập trung vào `classifications` và `customFields`

### 2. **Consistent Naming**
- `classifications` thay vì `variants` phù hợp với business logic
- Giữ nguyên cấu trúc dữ liệu mạnh mẽ của `ProductVariantDto`

### 3. **Maintainability**
- Code đơn giản hơn, dễ maintain
- Ít complexity trong form handling
- Rõ ràng về data flow

## 📝 Notes

- Frontend đã hoàn thành việc đổi tên và xóa fields
- Backend cần implement theo docs đã cập nhật
- Database schema có thể giữ nguyên hoặc rename table
- API specification đã được cập nhật theo thay đổi mới
