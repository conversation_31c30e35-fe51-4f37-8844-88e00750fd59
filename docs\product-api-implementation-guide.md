# Product API Implementation Guide

## 📋 Hướng dẫn triển khai Backend để khớp với Frontend

Dựa trên phân tích từ `frontend-backend-product-api-analysis.md`, đ<PERSON>y là hướng dẫn chi tiết để implement backend.

## 🚀 Step 1: Cập nhật DTOs

### 1.1 Tạo file `create-custom-field.dto.ts`
```typescript
// backend/business/user/dto/create-custom-field.dto.ts
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsArray, MaxLength } from 'class-validator';

export class CreateCustomFieldDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsString()
  @IsNotEmpty()
  type: string; // 'text' | 'number' | 'select' | 'date' | 'textarea' | 'checkbox'

  @IsOptional()
  @IsString()
  value?: string;

  @IsOptional()
  @IsBoolean()
  required?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  options?: string[]; // For select type

  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  placeholder?: string;

  @IsOptional()
  @IsString()
  defaultValue?: string;
}
```

### 1.2 Tạo file `create-custom-group-form.dto.ts`
```typescript
// backend/business/user/dto/create-custom-group-form.dto.ts
import { IsString, IsNotEmpty, IsOptional, IsArray, ValidateNested, IsObject, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateCustomFieldDto } from './create-custom-field.dto';

export class CreateCustomGroupFormDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  fields?: CreateCustomFieldDto[];

  @IsOptional()
  @IsObject()
  config?: Record<string, any>; // Flexible config object

  @IsOptional()
  @IsString()
  @MaxLength(50)
  layout?: string; // 'grid' | 'list' | 'tabs'

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
```

### 1.3 Tạo file `create-product-variant.dto.ts`
```typescript
// backend/business/user/dto/create-product-variant.dto.ts
import { IsString, IsNotEmpty, IsOptional, IsNumber, Min, IsArray, IsBoolean, MaxLength, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { HasPriceDto, StringPriceDto } from './price.dto';

export class CreateProductVariantDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  sku?: string;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => Object) // Will be validated as HasPriceDto or StringPriceDto
  price?: HasPriceDto | StringPriceDto;

  @IsOptional()
  @IsNumber()
  @Min(0)
  stock?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attributes?: string[]; // ['Color: Red', 'Size: L', 'Material: Cotton']

  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  image?: string; // URL to variant image

  @IsOptional()
  @IsNumber()
  @Min(0)
  weight?: number; // Weight in grams

  @IsOptional()
  @IsObject()
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
}
```

### 1.4 Cập nhật `business-create-product.dto.ts`
```typescript
// backend/business/user/dto/business-create-product.dto.ts
import { IsString, IsNotEmpty, IsOptional, IsArray, IsObject, ValidateNested, IsEnum, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { HasPriceDto, StringPriceDto } from './price.dto';
import { PriceTypeEnum, ProductTypeEnum } from '../enums';
import { BusinessShipmentConfigDto } from './business-shipment-config.dto';
import { CreateClassificationDto } from './create-classification.dto';
import { CreateCustomFieldDto } from './create-custom-field.dto';
import { CreateCustomGroupFormDto } from './create-custom-group-form.dto';
import { CreateProductVariantDto } from './create-product-variant.dto';

export class BusinessCreateProductDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum; // ✅ THÊM MỚI

  @IsNotEmpty()
  price: HasPriceDto | StringPriceDto | null;

  @IsEnum(PriceTypeEnum)
  @IsNotEmpty()
  typePrice: PriceTypeEnum;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => BusinessShipmentConfigDto)
  shipmentConfig?: BusinessShipmentConfigDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateClassificationDto)
  classifications?: CreateClassificationDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  customFields?: CreateCustomFieldDto[]; // ✅ THÊM MỚI

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => CreateCustomGroupFormDto)
  customGroupForm?: CreateCustomGroupFormDto; // ✅ THÊM MỚI

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateProductVariantDto)
  variants?: CreateProductVariantDto[]; // ✅ THÊM MỚI
}
```

## 🚀 Step 2: Cập nhật Enums

### 2.1 Tạo/Cập nhật `ProductTypeEnum`
```typescript
// backend/business/user/enums/product-type.enum.ts
export enum ProductTypeEnum {
  PHYSICAL = 'PHYSICAL',
  DIGITAL = 'DIGITAL',
  SERVICE = 'SERVICE',
  SUBSCRIPTION = 'SUBSCRIPTION',
  BUNDLE = 'BUNDLE',
}
```

### 2.2 Cập nhật index exports
```typescript
// backend/business/user/enums/index.ts
export * from './price-type.enum';
export * from './product-type.enum'; // ✅ THÊM MỚI
```

## 🚀 Step 3: Cập nhật Database Entities

### 3.1 Cập nhật Product Entity
```typescript
// backend/business/user/entities/product.entity.ts
import { Entity, Column, PrimaryGeneratedColumn, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ProductTypeEnum, PriceTypeEnum } from '../enums';
import { ProductCustomField } from './product-custom-field.entity';
import { ProductCustomGroupForm } from './product-custom-group-form.entity';
import { ProductVariant } from './product-variant.entity';

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: ProductTypeEnum,
    default: ProductTypeEnum.PHYSICAL
  })
  productType: ProductTypeEnum; // ✅ THÊM MỚI

  @Column({ type: 'json', nullable: true })
  price: any; // HasPriceDto | StringPriceDto | null

  @Column({
    type: 'enum',
    enum: PriceTypeEnum
  })
  typePrice: PriceTypeEnum;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'json', nullable: true })
  tags?: string[];

  @Column({ type: 'json', nullable: true })
  shipmentConfig?: any;

  @Column({ type: 'json', nullable: true })
  classifications?: any[];

  // ✅ THÊM MỚI - Relations
  @OneToMany(() => ProductCustomField, customField => customField.product, { cascade: true })
  customFields?: ProductCustomField[];

  @OneToMany(() => ProductCustomGroupForm, groupForm => groupForm.product, { cascade: true })
  customGroupForms?: ProductCustomGroupForm[];

  @OneToMany(() => ProductVariant, variant => variant.product, { cascade: true })
  variants?: ProductVariant[];

  @Column({ type: 'bigint' })
  createdAt: number;

  @Column({ type: 'bigint' })
  updatedAt: number;

  @Column()
  createdBy: number;
}
```

### 3.2 Tạo ProductCustomField Entity
```typescript
// backend/business/user/entities/product-custom-field.entity.ts
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from './product.entity';

@Entity('product_custom_fields')
export class ProductCustomField {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  productId: number;

  @ManyToOne(() => Product, product => product.customFields, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 50 })
  type: string;

  @Column({ type: 'text', nullable: true })
  value?: string;

  @Column({ default: false })
  required: boolean;

  @Column({ type: 'json', nullable: true })
  options?: string[];

  @Column({ length: 500, nullable: true })
  description?: string;

  @Column({ length: 100, nullable: true })
  placeholder?: string;

  @Column({ type: 'text', nullable: true })
  defaultValue?: string;

  @Column({ type: 'bigint' })
  createdAt: number;

  @Column({ type: 'bigint' })
  updatedAt: number;
}
```

### 3.3 Tạo ProductCustomGroupForm Entity
```typescript
// backend/business/user/entities/product-custom-group-form.entity.ts
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from './product.entity';

@Entity('product_custom_group_forms')
export class ProductCustomGroupForm {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  productId: number;

  @ManyToOne(() => Product, product => product.customGroupForms, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 500, nullable: true })
  description?: string;

  @Column({ type: 'json', nullable: true })
  fields?: any[]; // CreateCustomFieldDto[]

  @Column({ type: 'json', nullable: true })
  config?: Record<string, any>;

  @Column({ length: 50, nullable: true })
  layout?: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'bigint' })
  createdAt: number;

  @Column({ type: 'bigint' })
  updatedAt: number;
}
```

### 3.4 Tạo ProductVariant Entity
```typescript
// backend/business/user/entities/product-variant.entity.ts
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from './product.entity';

@Entity('product_variants')
export class ProductVariant {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  productId: number;

  @ManyToOne(() => Product, product => product.variants, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 100, nullable: true, unique: true })
  sku?: string;

  @Column({ type: 'json', nullable: true })
  price?: any; // HasPriceDto | StringPriceDto

  @Column({ default: 0 })
  stock: number;

  @Column({ type: 'json', nullable: true })
  attributes?: string[];

  @Column({ length: 500, nullable: true })
  description?: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ length: 255, nullable: true })
  image?: string;

  @Column({ nullable: true })
  weight?: number;

  @Column({ type: 'json', nullable: true })
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };

  @Column({ type: 'bigint' })
  createdAt: number;

  @Column({ type: 'bigint' })
  updatedAt: number;
}
```

## 🚀 Step 4: Cập nhật Service Layer

### 4.1 Cập nhật UserProductService
```typescript
// backend/business/user/services/user-product.service.ts
import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product, ProductCustomField, ProductCustomGroupForm, ProductVariant } from '../entities';
import { BusinessCreateProductDto } from '../dto';
import { PriceTypeEnum } from '../enums';

@Injectable()
export class UserProductService {
  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(ProductCustomField)
    private customFieldRepository: Repository<ProductCustomField>,
    @InjectRepository(ProductCustomGroupForm)
    private customGroupFormRepository: Repository<ProductCustomGroupForm>,
    @InjectRepository(ProductVariant)
    private variantRepository: Repository<ProductVariant>,
  ) {}

  async createProduct(createProductDto: BusinessCreateProductDto, userId: number): Promise<any> {
    // Validate business logic
    await this.validateProductData(createProductDto);

    const now = Date.now();

    // Create main product
    const product = await this.productRepository.save({
      ...createProductDto,
      createdBy: userId,
      createdAt: now,
      updatedAt: now,
    });

    // Handle custom fields if provided
    if (createProductDto.customFields?.length > 0) {
      await this.createProductCustomFields(product.id, createProductDto.customFields);
    }

    // Handle custom group form if provided
    if (createProductDto.customGroupForm) {
      await this.createProductCustomGroupForm(product.id, createProductDto.customGroupForm);
    }

    // Handle variants if provided
    if (createProductDto.variants?.length > 0) {
      await this.createProductVariants(product.id, createProductDto.variants);
    }

    // Return product with relations
    return this.productRepository.findOne({
      where: { id: product.id },
      relations: ['customFields', 'customGroupForms', 'variants'],
    });
  }

  private async validateProductData(dto: BusinessCreateProductDto): Promise<void> {
    // Validate price consistency
    if (dto.typePrice === PriceTypeEnum.HAS_PRICE && !dto.price) {
      throw new BadRequestException('Price is required when typePrice is HAS_PRICE');
    }

    if (dto.typePrice === PriceTypeEnum.STRING_PRICE && 
        (!dto.price || !('priceDescription' in dto.price))) {
      throw new BadRequestException('Price description is required when typePrice is STRING_PRICE');
    }

    // Validate variants
    if (dto.variants?.length > 0) {
      const skus = dto.variants.map(v => v.sku).filter(Boolean);
      if (skus.length !== new Set(skus).size) {
        throw new BadRequestException('Variant SKUs must be unique');
      }
    }
  }

  private async createProductCustomFields(productId: number, customFields: any[]): Promise<void> {
    const now = Date.now();
    const entities = customFields.map(field => ({
      ...field,
      productId,
      createdAt: now,
      updatedAt: now,
    }));
    await this.customFieldRepository.save(entities);
  }

  private async createProductCustomGroupForm(productId: number, groupForm: any): Promise<void> {
    const now = Date.now();
    await this.customGroupFormRepository.save({
      ...groupForm,
      productId,
      createdAt: now,
      updatedAt: now,
    });
  }

  private async createProductVariants(productId: number, variants: any[]): Promise<void> {
    const now = Date.now();
    const entities = variants.map(variant => ({
      ...variant,
      productId,
      createdAt: now,
      updatedAt: now,
    }));
    await this.variantRepository.save(entities);
  }
}
```

## 🚀 Step 5: Migration Scripts

### 5.1 Tạo Migration
```bash
npm run migration:generate -- --name=AddProductExtensions
```

### 5.2 Migration Content
```sql
-- Add product_type column to products table
ALTER TABLE products ADD COLUMN product_type VARCHAR(50) DEFAULT 'PHYSICAL';

-- Create product_custom_fields table
CREATE TABLE product_custom_fields (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL,
  value TEXT,
  required BOOLEAN DEFAULT FALSE,
  options JSON,
  description VARCHAR(500),
  placeholder VARCHAR(100),
  default_value TEXT,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);

-- Create product_custom_group_forms table
CREATE TABLE product_custom_group_forms (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description VARCHAR(500),
  fields JSON,
  config JSON,
  layout VARCHAR(50),
  is_active BOOLEAN DEFAULT TRUE,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);

-- Create product_variants table
CREATE TABLE product_variants (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  sku VARCHAR(100) UNIQUE,
  price JSON,
  stock INTEGER DEFAULT 0,
  attributes JSON,
  description VARCHAR(500),
  is_active BOOLEAN DEFAULT TRUE,
  image VARCHAR(255),
  weight INTEGER,
  dimensions JSON,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_product_custom_fields_product_id ON product_custom_fields(product_id);
CREATE INDEX idx_product_custom_group_forms_product_id ON product_custom_group_forms(product_id);
CREATE INDEX idx_product_variants_product_id ON product_variants(product_id);
CREATE INDEX idx_product_variants_sku ON product_variants(sku);
```

## ✅ Testing Checklist

- [ ] Test tạo product với tất cả fields mới
- [ ] Test validation cho required fields
- [ ] Test unique constraint cho variant SKUs
- [ ] Test cascade delete khi xóa product
- [ ] Test API response format
- [ ] Test frontend integration

## 📋 Next Steps

1. **Implement Response DTOs** cho custom fields, variants
2. **Add Update/Delete APIs** cho variants và custom fields
3. **Add Search/Filter** support cho variants
4. **Add Inventory Management** cho variants
5. **Add Image Upload** cho variants
