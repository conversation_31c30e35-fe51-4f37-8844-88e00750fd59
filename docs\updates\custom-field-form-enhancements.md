# Cập nhật CustomFieldForm - Tính năng nâng cao

## Tổng quan
Đã cập nhật form "Thêm trường tùy chỉnh" trong trang `/business/custom-field` với các tính năng mới:

## 🎯 C<PERSON>c tính năng đã thêm

### 1. Tr<PERSON><PERSON><PERSON> "Tên trường định danh" (ID)
- **Mới**: Trường bắt buộc để định danh trường
- **Placeholder**: `text-input-001`
- **Validation**: Chỉ cho phép chữ cái, số, gạch dưới và gạch ngang
- **Lưu vào**: `config.id` trong API

### 2. Trường "Nhãn" với Tag Input
- **Thay thế**: Input thông thường → Tag input với Chip
- **Tính năng**:
  - <PERSON><PERSON><PERSON><PERSON> tag và nhấn Enter để thêm
  - <PERSON><PERSON><PERSON> thị tags dưới dạng Chip có thể xóa
  - <PERSON><PERSON><PERSON> trữ tags trong state `tempTags`
  - <PERSON><PERSON> submit, join c<PERSON><PERSON> tags thành chuỗi phân tách bằng dấu phẩy
  - **Validation**: Yêu cầu ít nhất 1 tag (custom validation)
  - **Counter**: Hiển thị số lượng tags đã thêm

### 3. Trường "Mẫu kiểm tra" với 30 Pattern gợi ý
- **30 pattern phổ biến** bao gồm:
  - Email, Số điện thoại VN/Quốc tế
  - CMND/CCCD, Mã số thuế
  - URL, IPv4, UUID
  - Mật khẩu mạnh, Hex color
  - Ngày tháng, Giờ
  - Tên file, Slug URL
  - Số thẻ tín dụng, Mã QR
  - Tọa độ GPS, RGB color
  - Và nhiều pattern khác...

- **UI**: Grid 2 cột với scroll, click để chọn pattern
- **Hiển thị**: Tên pattern + preview 20 ký tự đầu

### 4. Conditional Fields theo Kiểu dữ liệu
- **Loại thành phần**: Chỉ hiển thị khi kiểu dữ liệu phù hợp
- **Độ dài tối thiểu/tối đa**: Chỉ hiển thị cho `text` và `number`
- **Mẫu kiểm tra**: Chỉ hiển thị cho `text`

## 🔧 Cài đặt kỹ thuật

### Import mới
```typescript
import { ConditionalField, Chip } from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
```

### Schema với custom validation
```typescript
const customFieldSchema = z.object({
  id: z.string().min(1, t('business:customField.form.idRequired')),
  // ... other fields
}).refine(() => validateTags(), {
  message: "Vui lòng thêm ít nhất một nhãn",
  path: ["label"],
});
```

### State quản lý tags
```typescript
const [tempTags, setTempTags] = useState<Record<string, string[]>>({});
const formRef = useRef<any>(null);

// Custom validation cho tags
const validateTags = () => {
  const labelTags = tempTags['labels'] || [];
  return labelTags.length > 0;
};
```

### Conditional Logic
```typescript
<ConditionalField
  condition={{
    field: 'type',
    type: ConditionType.IN,
    value: ['text', 'number'],
  }}
>
  <FormItem>...</FormItem>
</ConditionalField>
```

## 📋 Cấu trúc Pattern gợi ý

```typescript
const COMMON_PATTERNS = [
  { label: 'Email', value: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' },
  { label: 'Số điện thoại VN', value: '^(\\+84|0)[0-9]{9,10}$' },
  // ... 28 patterns khác
];
```

## 🎨 UI/UX Improvements

### Tag Input
- Placeholder: "Nhập nhãn và nhấn Enter"
- Chips với nút close
- Responsive layout

### Pattern Suggestions
- Grid layout với scroll
- Hover effects
- Truncated preview với tooltip
- Click to select functionality

### Conditional Fields
- Smooth show/hide transitions
- Proper field clearing when hidden
- Logical grouping by data type

## 🔄 Data Flow

### Submit Process
1. Validate tags (ít nhất 1 tag)
2. Lấy tags từ `tempTags['labels']`
3. Join thành chuỗi với dấu phẩy
4. Gửi API với `config.id` và label đã được format

### Load Process
1. Parse label từ API (split by comma)
2. Load vào `tempTags` state
3. Hiển thị dưới dạng chips

## ✅ Testing Points

- [ ] **ID field**: Nhập tên trường định danh, validation pattern
- [ ] **Tag input**: Thêm/xóa tags, validation ít nhất 1 tag
- [ ] **Pattern selection**: Click để chọn pattern
- [ ] **Conditional fields**: Hiển thị/ẩn theo kiểu dữ liệu
- [ ] **Form submission**: Data được format đúng với `config.id`
- [ ] **Edit mode**: Load tags và ID từ existing data
- [ ] **Validation**: Hiển thị lỗi khi không có tags
- [ ] **Counter**: Hiển thị số lượng tags
- [ ] **Responsive design**: Mobile/desktop

## 🚀 Benefits

1. **UX tốt hơn**: Tag input trực quan, pattern gợi ý hữu ích
2. **Logic rõ ràng**: Conditional fields giảm confusion
3. **Validation mạnh**: Custom validation cho tags, pattern validation cho ID
4. **Data integrity**: Trường ID định danh giúp quản lý tốt hơn
5. **Maintainable**: Sử dụng shared components và hooks
6. **Extensible**: Dễ thêm patterns mới hoặc conditions mới
