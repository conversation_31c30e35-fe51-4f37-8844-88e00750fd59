import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Card, 
  Typography, 
  ColorPicker, 
  RandomColorIconCard,
  Button 
} from '@/shared/components/common';

/**
 * Demo page để test ColorPicker và RandomColorIconCard
 */
const ColorPickerDemo: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const [selectedColor, setSelectedColor] = useState('#FF6B6B');
  const [randomColor, setRandomColor] = useState('#4ECDC4');

  const handleColorChange = (color: string) => {
    setSelectedColor(color);
    console.log('Color changed:', color);
  };

  const handleRandomColorGenerated = (color: string) => {
    setRandomColor(color);
    console.log('Random color generated:', color);
  };

  const resetColors = () => {
    setSelectedColor('#FF6B6B');
    setRandomColor('#4ECDC4');
  };

  return (
    <div className="w-full bg-background text-foreground p-6 space-y-6">
      <Typography variant="h1" className="mb-6">
        Color Picker Demo
      </Typography>

      {/* ColorPicker Demo */}
      <Card className="p-6">
        <Typography variant="h3" className="mb-4">
          ColorPicker Component
        </Typography>
        
        <div className="space-y-4">
          <div>
            <Typography variant="body1" className="mb-2">
              Selected Color: <span style={{ color: selectedColor }}>{selectedColor}</span>
            </Typography>
            <div 
              className="w-16 h-16 rounded-lg border border-border mb-4"
              style={{ backgroundColor: selectedColor }}
            />
          </div>

          <ColorPicker
            value={selectedColor}
            onChange={handleColorChange}
            placeholder={t('marketing:tags.form.colorPlaceholder', 'Chọn màu cho tag')}
          />
        </div>
      </Card>

      {/* RandomColorIconCard Demo */}
      <Card className="p-6">
        <Typography variant="h3" className="mb-4">
          RandomColorIconCard Component
        </Typography>
        
        <div className="space-y-4">
          <div>
            <Typography variant="body1" className="mb-2">
              Random Color: <span style={{ color: randomColor }}>{randomColor}</span>
            </Typography>
            <div 
              className="w-16 h-16 rounded-lg border border-border mb-4"
              style={{ backgroundColor: randomColor }}
            />
          </div>

          <div className="flex items-center space-x-4">
            <RandomColorIconCard
              onColorGenerated={handleRandomColorGenerated}
              size="md"
              variant="ghost"
            />
            <Typography variant="body2" className="text-muted-foreground">
              Click để generate màu ngẫu nhiên
            </Typography>
          </div>
        </div>
      </Card>

      {/* Combined Demo */}
      <Card className="p-6">
        <Typography variant="h3" className="mb-4">
          Combined Usage (như trong TagForm)
        </Typography>
        
        <div className="space-y-4">
          <div>
            <Typography variant="body1" className="mb-2">
              Tag Color Preview:
            </Typography>
            <div className="flex items-center space-x-2 mb-4">
              <div 
                className="w-6 h-6 rounded border border-border"
                style={{ backgroundColor: selectedColor }}
              />
              <Typography variant="body2">
                {selectedColor}
              </Typography>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Color preview button như trong TagForm */}
            <div
              className="w-12 h-10 rounded-lg border-2 border-border cursor-pointer hover:scale-105 transition-transform shadow-sm"
              style={{ backgroundColor: selectedColor }}
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'color';
                input.value = selectedColor;
                input.onchange = (e) => {
                  const target = e.target as HTMLInputElement;
                  handleColorChange(target.value);
                };
                input.click();
              }}
              title={`${t('marketing:tags.form.colorPlaceholder', 'Chọn màu cho tag')}: ${selectedColor}`}
            />

            <RandomColorIconCard
              onColorGenerated={handleColorChange}
              size="md"
              variant="ghost"
            />

            <span className="text-sm text-muted-foreground font-mono">
              {selectedColor}
            </span>
          </div>
        </div>
      </Card>

      {/* Actions */}
      <Card className="p-6">
        <div className="flex space-x-4">
          <Button onClick={resetColors} variant="outline">
            Reset Colors
          </Button>
          <Button 
            onClick={() => console.log('Current colors:', { selectedColor, randomColor })}
            variant="primary"
          >
            Log Colors
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default ColorPickerDemo;
