/**
 * Performance optimization utilities
 */

/**
 * Debounce function to limit the rate at which a function can fire
 * @param func The function to debounce
 * @param wait The time to wait in milliseconds
 * @returns A debounced function
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null;

  return function (...args: Parameters<T>): void {
    const later = () => {
      timeout = null;
      func(...args);
    };

    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = window.setTimeout(later, wait);
  };
}

/**
 * Throttle function to limit the rate at which a function can fire
 * @param func The function to throttle
 * @param limit The time limit in milliseconds
 * @returns A throttled function
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  let lastFunc: number | null = null;
  let lastRan: number | null = null;

  return function (...args: Parameters<T>): void {
    if (!inThrottle) {
      func(...args);
      lastRan = Date.now();
      inThrottle = true;
      setTimeout(
        () => {
          inThrottle = false;
          if (lastFunc && lastRan) {
            const elapsed = Date.now() - lastRan;
            if (elapsed >= limit) {
              func(...args);
              lastRan = Date.now();
            }
          }
        },
        limit - (lastRan ? Date.now() - lastRan : 0)
      );
    } else {
      lastFunc = window.setTimeout(
        () => {
          if (lastRan) {
            const elapsed = Date.now() - lastRan;
            if (elapsed >= limit) {
              func(...args);
              lastRan = Date.now();
            }
          }
        },
        limit - (lastRan ? Date.now() - lastRan : 0)
      );
    }
  };
}

/**
 * Memoize function to cache the results of expensive function calls
 * @param func The function to memoize
 * @returns A memoized function
 */
export function memoize<T extends (...args: unknown[]) => unknown>(
  func: T
): (...args: Parameters<T>) => ReturnType<T> {
  const cache = new Map<string, ReturnType<T>>();

  return function (...args: Parameters<T>): ReturnType<T> {
    const key = JSON.stringify(args);
    if (cache.has(key)) {
      return cache.get(key) as ReturnType<T>;
    }
    const result = func(...args) as ReturnType<T>;
    cache.set(key, result);
    return result;
  };
}

/**
 * Create a hook for detecting when an element is in the viewport
 * @param options IntersectionObserver options
 * @returns A function that takes a ref and returns whether the element is in view
 */
export function createIntersectionObserver(
  options: IntersectionObserverInit = { threshold: 0.1 }
): (ref: React.RefObject<Element>) => boolean {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return () => true; // Fallback for environments without IntersectionObserver
  }

  return (ref: React.RefObject<Element>): boolean => {
    const [isInView, setIsInView] = React.useState(false);

    React.useEffect(() => {
      // Sử dụng options từ closure để tránh re-render không cần thiết
      const currentOptions = options;
      const observer = new IntersectionObserver((entries) => {
        const entry = entries[0];
        if (entry) {
          setIsInView(entry.isIntersecting);
        }
      }, currentOptions);

      const currentRef = ref.current;
      if (currentRef) {
        observer.observe(currentRef);
      }

      return () => {
        if (currentRef) {
          observer.unobserve(currentRef);
        }
      };
    }, [ref]);

    return isInView;
  };
}

/**
 * Lazy load images
 * @param src The image source
 * @param placeholder The placeholder image source
 * @returns An object with the loaded image source and loading state
 */
export function useLazyImage(
  src: string,
  placeholder: string = ''
): { imageSrc: string; isLoading: boolean } {
  const [imageSrc, setImageSrc] = React.useState(placeholder);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const img = new Image();
    img.src = src;
    img.onload = () => {
      setImageSrc(src);
      setIsLoading(false);
    };
  }, [src]);

  return { imageSrc, isLoading };
}

// Add React import for the hooks
import React from 'react';
