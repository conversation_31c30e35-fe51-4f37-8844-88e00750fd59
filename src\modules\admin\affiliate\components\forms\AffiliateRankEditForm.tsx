import React, { useState, useMemo } from 'react';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Textarea,
  Toggle,
  Typography,
  Alert,
} from '@/shared/components/common';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';
import { useAffiliateRankData } from '../../hooks/useAffiliateData';
import {
  AffiliateConditionRange,
  AffiliateRankDto,
  UpdateAffiliateRankDto,
} from '../../types/api.types';

// Import component ImageUploader
import ImageUploader from '@/modules/business/components/ImageUploader';

// Hàm kiểm tra xem khoảng [min, max] có trùng với bất kỳ khoảng nào trong ranges không
// Đối với form chỉnh sửa, chúng ta cần loại trừ khoảng hiện tại của rank đang chỉnh sửa
const isRangeOverlapping = (
  min: number,
  max: number,
  ranges: AffiliateConditionRange[],
  currentRankId?: number
): boolean => {
  // Nếu đang chỉnh sửa rank hiện tại, chúng ta cần lọc ra các khoảng không thuộc rank hiện tại
  // Vì AffiliateConditionRange không có id, chúng ta cần so sánh khoảng min-max
  const filteredRanges = currentRankId
    ? ranges.filter(range => !(range.minCondition === min && range.maxCondition === max))
    : ranges;

  return filteredRanges.some(range => {
    // Kiểm tra xem có bất kỳ phần giao nào giữa [min, max] và [range.minCondition, range.maxCondition]
    return min <= range.maxCondition && max >= range.minCondition;
  });
};

// Schema cho form với validation động
const createFormSchema = (
  conditionRanges: AffiliateConditionRange[] = [],
  currentRankId?: number
) => {
  return z
    .object({
      rankName: z.string().min(1, 'Tên cấp bậc là bắt buộc'),
      commission: z.coerce
        .number()
        .min(0, 'Tỷ lệ hoa hồng không được âm')
        .max(100, 'Tỷ lệ hoa hồng không được vượt quá 100%'),
      minCondition: z.coerce.number().min(0, 'Điều kiện tối thiểu không được âm'),
      maxCondition: z.coerce.number().min(0, 'Điều kiện tối đa không được âm'),
      isActive: z.boolean(),
      description: z.string().optional(),
    })
    .refine(data => data.maxCondition >= data.minCondition, {
      message: 'Điều kiện tối đa phải lớn hơn hoặc bằng điều kiện tối thiểu',
      path: ['maxCondition'],
    })
    .refine(
      data => {
        if (!conditionRanges || conditionRanges.length === 0) return true;
        return !isRangeOverlapping(
          data.minCondition,
          data.maxCondition,
          conditionRanges,
          currentRankId
        );
      },
      {
        message: 'Khoảng điều kiện đã được sử dụng bởi cấp bậc khác',
        path: ['minCondition'],
      }
    );
};

// Định nghĩa kiểu dữ liệu cho form values
export type AffiliateRankEditFormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface AffiliateRankEditFormProps {
  rank: AffiliateRankDto;
  onSubmit: (id: number, values: UpdateAffiliateRankDto, file?: File) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Component form chỉnh sửa cấp bậc affiliate
 */
const AffiliateRankEditForm: React.FC<AffiliateRankEditFormProps> = ({
  rank,
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['affiliate', 'common']);
  const [rankImage, setRankImage] = useState<{ file: File; preview: string } | null>(null);
  const { useUsedConditionRanges } = useAffiliateRankData();
  const { data: conditionRangesResponse, isLoading: isLoadingRanges } = useUsedConditionRanges();
  const conditionRanges = conditionRangesResponse as AffiliateConditionRange[] | undefined;
  const [validationError, setValidationError] = useState<string | null>(null);

  // Tạo schema form với validation dựa trên conditionRanges
  const schema = useMemo(() => {
    return createFormSchema(conditionRanges, rank.id);
  }, [conditionRanges, rank.id]);

  // Tính toán giá trị mặc định từ rank hiện tại
  const defaultValues = useMemo(() => {
    return {
      rankName: rank.rankName,
      commission: rank.commission,
      minCondition: rank.minCondition,
      maxCondition: rank.maxCondition,
      isActive: rank.isActive,
      description: rank.description || '',
    };
  }, [rank]);

  // Xử lý khi upload ảnh
  const handleImageUpload = (file: File, dataUrl: string) => {
    setRankImage({ file, preview: dataUrl });
  };

  // Kiểm tra khoảng điều kiện trước khi submit
  const validateConditionRange = (values: Record<string, unknown>): boolean => {
    if (!conditionRanges || conditionRanges.length === 0) return true;

    const min = values['minCondition'] as number;
    const max = values['maxCondition'] as number;

    // Kiểm tra min <= max
    if (min > max) {
      setValidationError('Điều kiện tối thiểu phải nhỏ hơn hoặc bằng điều kiện tối đa');
      return false;
    }

    // Kiểm tra khoảng có trùng với khoảng nào khác không (trừ khoảng hiện tại)
    if (isRangeOverlapping(min, max, conditionRanges, rank.id)) {
      setValidationError(
        'Khoảng điều kiện đã được sử dụng bởi cấp bậc khác. Vui lòng chọn khoảng khác.'
      );
      return false;
    }

    setValidationError(null);
    return true;
  };

  // Xử lý khi submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    if (validateConditionRange(values)) {
      const updateData: UpdateAffiliateRankDto = {
        rankName: values['rankName'] as string,
        commission: values['commission'] as number,
        minCondition: values['minCondition'] as number,
        maxCondition: values['maxCondition'] as number,
        isActive: values['isActive'] as boolean,
        description: values['description'] as string,
      };
      onSubmit(rank.id, updateData, rankImage?.file);
    }
  };

  return (
    <Card className="mb-4 p-4">
      {validationError && (
        <Alert
          type="error"
          className="mb-4"
          title={t('common:error')}
          message=""
          description={validationError}
          closable={true}
          onClose={() => setValidationError(null)}
        />
      )}

      <Form
        schema={schema}
        onSubmit={handleSubmit}
        className="space-y-6"
        defaultValues={defaultValues}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <FormItem name="rankName" label={t('affiliate:rank.form.name')} required>
              <Input placeholder={t('affiliate:rank.form.namePlaceholder')} fullWidth />
            </FormItem>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <FormItem name="commission" label={t('affiliate:rank.form.commission')} required>
                <Input
                  type="number"
                  placeholder={t('affiliate:rank.form.commissionPlaceholder')}
                  fullWidth
                  min={0}
                  max={100}
                  step={0.1}
                  className="number-input-custom"
                />
              </FormItem>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <FormItem name="minCondition" label={t('affiliate:rank.form.minCondition')} required>
                <Input
                  type="number"
                  placeholder={t('affiliate:rank.form.minConditionPlaceholder')}
                  fullWidth
                  min={0}
                  className="number-input-custom"
                />
              </FormItem>

              <FormItem name="maxCondition" label={t('affiliate:rank.form.maxCondition')} required>
                <Input
                  type="number"
                  placeholder={t('affiliate:rank.form.maxConditionPlaceholder')}
                  fullWidth
                  min={0}
                  className="number-input-custom"
                />
              </FormItem>
            </div>

            {isLoadingRanges ? (
              <div className="mt-2 text-sm text-gray-500">
                {t('affiliate:rank.form.loadingConditionRanges')}
              </div>
            ) : conditionRanges && conditionRanges.length > 0 ? (
              <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
                <Typography variant="caption" className="text-yellow-700 dark:text-yellow-400">
                  {t('affiliate:rank.form.usedConditionRanges')}
                </Typography>
                <div className="mt-1 text-xs text-gray-600 dark:text-gray-300">
                  {conditionRanges
                    .filter(
                      range =>
                        !(
                          range.minCondition === rank.minCondition &&
                          range.maxCondition === rank.maxCondition
                        )
                    ) // Lọc bỏ khoảng của rank hiện tại
                    .map((range, index) => (
                      <div
                        key={index}
                        className="inline-block mr-2 mb-1 px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded"
                      >
                        {range.minCondition} - {range.maxCondition}
                      </div>
                    ))}
                </div>
              </div>
            ) : null}

            <FormItem name="isActive" label={t('affiliate:rank.form.status')} className="mt-4">
              <Toggle />
            </FormItem>
          </div>

          <div>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                {t('affiliate:rank.form.rankBadge')}
              </label>
              <ImageUploader
                currentImage={rankImage?.preview || rank.rankBadge}
                onImageUpload={handleImageUpload}
                height="h-48"
                placeholder={t('affiliate:rank.form.rankBadgePlaceholder')}
                className="border-dashed border-2 border-gray-300 hover:border-primary transition-colors"
              />
              <Typography variant="caption" className="text-gray-500 dark:text-gray-400 mt-2 block">
                {t('affiliate:rank.form.rankBadgeHint')}
              </Typography>
            </div>

            <FormItem name="description" label={t('affiliate:rank.form.description')}>
              <Textarea
                placeholder={t('affiliate:rank.form.descriptionPlaceholder')}
                rows={5}
                fullWidth
              />
            </FormItem>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('common:cancel')}
          </Button>
          <Button type="submit" variant="primary" isLoading={isSubmitting}>
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AffiliateRankEditForm;
