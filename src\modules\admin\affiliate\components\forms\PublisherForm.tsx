import React from 'react';
import { Card, Form, FormItem, Input, Button, Select } from '@/shared/components/common';
import { z } from 'zod';
import { PublisherStatus } from '../../types/affiliate.types';
import { useTranslation } from 'react-i18next';

// Schema cho form
const formSchema = z.object({
  userId: z.number().min(1, 'ID người dùng là bắt buộc'),
  referralCode: z.string().min(1, 'Mã giới thiệu là bắt buộc'),
  status: z.nativeEnum(PublisherStatus, {
    errorMap: () => ({ message: 'Trạng thái là bắt buộc' }),
  }),
});

export type PublisherFormValues = z.infer<typeof formSchema>;

interface PublisherFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  initialValues?: Partial<PublisherFormValues>;
  isSubmitting?: boolean;
}

/**
 * Component form thêm/sửa publisher
 */
const PublisherForm: React.FC<PublisherFormProps> = ({
  onSubmit,
  onCancel,
  initialValues,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['affiliate', 'common']);

  return (
    <Card className="mb-4 p-4">
      <Form
        schema={formSchema}
        onSubmit={onSubmit}
        className="space-y-4"
        defaultValues={initialValues}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="userId" label={t('affiliate:publisher.form.userId')} required>
            <Input
              type="number"
              placeholder={t('affiliate:publisher.form.userIdPlaceholder')}
              fullWidth
            />
          </FormItem>

          <FormItem name="referralCode" label={t('affiliate:publisher.form.referralCode')} required>
            <Input placeholder={t('affiliate:publisher.form.referralCodePlaceholder')} fullWidth />
          </FormItem>
        </div>

        <FormItem name="status" label={t('affiliate:publisher.form.status')} required>
          <Select
            options={[
              { value: PublisherStatus.ACTIVE, label: t('common:active') },
              { value: PublisherStatus.INACTIVE, label: t('common:inactive') },
              { value: PublisherStatus.PENDING, label: t('common:pending') },
              { value: PublisherStatus.SUSPENDED, label: t('common:suspended') },
            ]}
            placeholder={t('affiliate:publisher.form.statusPlaceholder')}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('common:cancel')}
          </Button>
          <Button type="submit" variant="primary" isLoading={isSubmitting}>
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default PublisherForm;
