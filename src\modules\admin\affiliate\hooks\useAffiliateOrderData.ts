import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  AffiliateOrderDto,
  AffiliateOrderQueryDto,
  AffiliateOrderStatus,
  AffiliateOrderType,
} from '../types/affiliate.types';
// Sử dụng các kiểu dữ liệu từ api.types
import {
  AffiliateOrderDto as AffiliateOrderApiDto,
  AffiliateOrderQueryDto as AffiliateOrderApiQueryDto,
} from '../types/api.types';

/**
 * Chuyển đổi từ DTO API sang DTO UI
 */
const mapApiDtoToUiDto = (apiDto: AffiliateOrderApiDto): AffiliateOrderDto => {
  // <PERSON>ác định loại đơn hàng dựa vào paymentMethod hoặc các thông tin khác
  const orderType = AffiliateOrderType.POINT_PURCHASE; // Mặc định là mua point

  // Chuyển đổi trạng thái từ API sang UI
  let status: AffiliateOrderStatus;
  const apiStatus = apiDto.status as unknown as string;

  if (apiStatus === 'CONFIRMED') {
    status = AffiliateOrderStatus.COMPLETED;
  } else if (apiStatus === 'PENDING') {
    status = AffiliateOrderStatus.PENDING;
  } else if (apiStatus === 'FAILED' || apiStatus === 'REFUNDED') {
    status = AffiliateOrderStatus.CANCELLED;
  } else {
    status = AffiliateOrderStatus.PENDING;
  }

  // Format thời gian
  const createdAt = new Date(apiDto.createdAt).toLocaleDateString('vi-VN');
  // Sử dụng updatedAt từ apiDto hoặc fallback về createdAt
  const updatedAt = apiDto.updatedAt
    ? new Date(apiDto.updatedAt).toLocaleDateString('vi-VN')
    : createdAt;

  // Tạo đối tượng AffiliateOrderDto từ dữ liệu API
  return {
    id: apiDto.id,
    publisherId: apiDto.publisherId || 0,
    publisherName: apiDto.publisherName || 'Không có',
    userId: apiDto.userId,
    // Xử lý userName từ các thuộc tính có thể có của apiDto
    userName: (apiDto as unknown as { user?: { fullName?: string } }).user?.fullName || 'Không có',
    // Sử dụng orderNumber từ các thuộc tính có thể có hoặc tạo mặc định
    orderNumber: (apiDto as unknown as { referenceId?: string }).referenceId || `ORD-${apiDto.id}`,
    orderType,
    // Xử lý orderAmount từ các thuộc tính có thể có
    orderAmount: (apiDto as unknown as { amount?: number }).amount || 0,
    commissionAmount: apiDto.commissionAmount || 0,
    commissionRate: apiDto.commissionRate || 0,
    status,
    createdAt,
    updatedAt,
  };
};

/**
 * Chuyển đổi từ query UI sang query API
 */
const mapUiQueryToApiQuery = (uiQuery: AffiliateOrderQueryDto): AffiliateOrderApiQueryDto => {
  // Tạo đối tượng cơ bản với các thuộc tính chung
  const apiQuery = {
    page: uiQuery.page,
    limit: uiQuery.limit,
    search: uiQuery.search, // Sử dụng search thay vì keyword
    userId: uiQuery.userId,
    publisherId: uiQuery.publisherId,
  } as AffiliateOrderApiQueryDto;

  // Chuyển đổi status nếu có
  if (uiQuery.status) {
    let apiStatus: string;
    switch (uiQuery.status) {
      case AffiliateOrderStatus.COMPLETED:
        apiStatus = 'CONFIRMED';
        break;
      case AffiliateOrderStatus.PENDING:
        apiStatus = 'PENDING';
        break;
      case AffiliateOrderStatus.CANCELLED:
        apiStatus = 'FAILED,REFUNDED';
        break;
      default:
        apiStatus = '';
    }
    // Gán status dưới dạng unknown để tránh lỗi kiểu
    (apiQuery as unknown as { status: string }).status = apiStatus;
  }

  // Chuyển đổi ngày bắt đầu và kết thúc nếu có
  if (uiQuery.startDate) {
    (apiQuery as unknown as { startDate: string }).startDate = new Date(
      uiQuery.startDate
    ).toISOString();
  }
  if (uiQuery.endDate) {
    (apiQuery as unknown as { endDate: string }).endDate = new Date(uiQuery.endDate).toISOString();
  }

  return apiQuery;
};

/**
 * Hook để lấy dữ liệu đơn hàng affiliate
 */
export const useAffiliateOrderData = () => {
  /**
   * Hook lấy danh sách đơn hàng affiliate
   */
  const useOrders = (queryParams: AffiliateOrderQueryDto) => {
    return useQuery({
      queryKey: ['affiliate-orders', queryParams],
      queryFn: async () => {
        const apiQueryParams = mapUiQueryToApiQuery(queryParams);
        const response = await apiClient.get<ApiResponseDto<PaginatedResult<AffiliateOrderApiDto>>>(
          '/admin/r-point/transactions',
          { params: apiQueryParams }
        );

        // Chuyển đổi dữ liệu từ API sang UI
        // Truy cập đúng cấu trúc dữ liệu từ response
        const apiData = response.result as unknown as PaginatedResult<AffiliateOrderApiDto>;

        // Kiểm tra và xử lý dữ liệu
        if (!apiData || !apiData.items) {
          return {
            items: [],
            meta: {
              totalItems: 0,
              itemCount: 0,
              itemsPerPage: queryParams.limit || 10,
              totalPages: 0,
              currentPage: queryParams.page || 1,
            },
          };
        }

        return {
          items: apiData.items.map(mapApiDtoToUiDto),
          meta: {
            totalItems: apiData.meta?.totalItems || 0,
            itemCount: apiData.items.length,
            itemsPerPage: apiData.meta?.itemsPerPage || queryParams.limit || 10,
            totalPages: apiData.meta?.totalPages || 0,
            currentPage: apiData.meta?.currentPage || queryParams.page || 1,
          },
        };
      },
      enabled: !!queryParams,
    });
  };

  /**
   * Hook lấy chi tiết đơn hàng affiliate
   */
  const useOrderDetail = (id: number) => {
    return useQuery({
      queryKey: ['affiliate-order', id],
      queryFn: async () => {
        const response = await apiClient.get<ApiResponseDto<AffiliateOrderApiDto>>(
          `/admin/r-point/transactions/${id}`
        );
        // Truy cập đúng cấu trúc dữ liệu từ response
        return mapApiDtoToUiDto(response.result as unknown as AffiliateOrderApiDto);
      },
      enabled: !!id,
    });
  };

  return {
    useOrders,
    useOrderDetail,
  };
};

export default useAffiliateOrderData;
