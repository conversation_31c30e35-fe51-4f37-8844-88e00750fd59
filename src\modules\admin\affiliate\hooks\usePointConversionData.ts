import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { PointConversionDto, PointConversionQueryDto } from '../types/point-conversion.types';

/**
 * Hook để lấy dữ liệu lịch sử chuyển đổi điểm affiliate
 */
export const usePointConversionData = () => {
  /**
   * Hook lấy danh sách lịch sử chuyển đổi điểm
   */
  const usePointConversions = (queryParams: PointConversionQueryDto) => {
    return useQuery({
      queryKey: ['point-conversions', queryParams],
      queryFn: async () => {
        const response = await apiClient.get<ApiResponseDto<PaginatedResult<PointConversionDto>>>(
          '/admin/affiliate/point-conversions',
          { params: queryParams }
        );

        return response.result;
      },
      enabled: !!queryParams,
    });
  };

  /**
   * Hook lấy chi tiết lịch sử chuyển đổi điểm
   */
  const usePointConversionDetail = (id: number) => {
    return useQuery({
      queryKey: ['point-conversion', id],
      queryFn: async () => {
        const response = await apiClient.get<ApiResponseDto<PointConversionDto>>(
          `/admin/affiliate/point-conversions/${id}`
        );
        return response.result;
      },
      enabled: !!id,
    });
  };

  /**
   * Hook lấy tổng số lịch sử chuyển đổi điểm
   */
  const useTotalPointConversions = () => {
    return useQuery({
      queryKey: ['point-conversions-count'],
      queryFn: async () => {
        const response = await apiClient.get<ApiResponseDto<PaginatedResult<PointConversionDto>>>(
          '/admin/affiliate/point-conversions',
          { params: { page: 1, limit: 1 } }
        );
        // Chuyển đổi response.result sang unknown trước, sau đó mới chuyển sang PaginatedResult
        const result = response.result as unknown as PaginatedResult<PointConversionDto>;
        return result.meta.totalItems;
      },
    });
  };

  return {
    usePointConversions,
    usePointConversionDetail,
    useTotalPointConversions,
  };
};

export default usePointConversionData;
