import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý Affiliate
 */
const AffiliateManagementPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);
  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Publisher Card */}
        <ModuleCard
          title={t('affiliate:publisher.title')}
          description={t('affiliate:publisher.description')}
          icon="users"
          linkTo="/admin/affiliate/publishers"
        />

        {/* Rank Card */}
        <ModuleCard
          title={t('affiliate:rank.title')}
          description={t('affiliate:rank.description')}
          icon="award"
          linkTo="/admin/affiliate/ranks"
        />
        {/* Orders Card */}
        <ModuleCard
          title={t('affiliate:order.title')}
          description={t('affiliate:order.description')}
          icon="shopping-cart"
          linkTo="/admin/affiliate/orders"
        />

        {/* Point Conversion Card */}
        <ModuleCard
          title={t('affiliate:pointConversion.title')}
          description={t('affiliate:pointConversion.description')}
          icon="refresh-cw"
          linkTo="/admin/affiliate/point-conversions"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default AffiliateManagementPage;
