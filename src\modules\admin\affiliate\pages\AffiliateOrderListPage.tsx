import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import {
  AffiliateOrderDto,
  AffiliateOrderStatus,
  AffiliateOrderType,
  AffiliateOrderQueryDto,
} from '../types/affiliate.types';
import { useAffiliateOrderData } from '../hooks/useAffiliateOrderData';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

/**
 * Trang quản lý đơn hàng affiliate
 */
const AffiliateOrderListPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // Sử dụng hook để lấy dữ liệu đơn hàng affiliate
  const { useOrders } = useAffiliateOrderData();

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: 'completed',
        label: t('affiliate:order.status.completed'),
        icon: 'check',
        value: AffiliateOrderStatus.COMPLETED,
      },
      {
        id: 'pending',
        label: t('affiliate:order.status.pending'),
        icon: 'clock',
        value: AffiliateOrderStatus.PENDING,
      },
      {
        id: 'cancelled',
        label: t('affiliate:order.status.cancelled'),
        icon: 'x',
        value: AffiliateOrderStatus.CANCELLED,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AffiliateOrderQueryDto => {
    const queryParams: AffiliateOrderQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || '',
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    // Thêm filter theo trạng thái nếu không phải 'all'
    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as AffiliateOrderStatus;
    }

    // Thêm filter theo khoảng thời gian nếu có
    if (params.dateRange[0]) {
      queryParams.startDate = params.dateRange[0].toISOString();
    }
    if (params.dateRange[1]) {
      queryParams.endDate = params.dateRange[1].toISOString();
    }

    return queryParams;
  };

  // Columns cho bảng
  const columns: TableColumn<AffiliateOrderDto>[] = [
    {
      key: 'orderNumber',
      title: t('affiliate:order.table.orderNumber'),
      dataIndex: 'orderNumber',
      width: '10%',
    },
    {
      key: 'publisherName',
      title: t('affiliate:order.table.publisherName'),
      dataIndex: 'publisherName',
      width: '15%',
    },
    {
      key: 'userName',
      title: t('affiliate:order.table.userName'),
      dataIndex: 'userName',
      width: '15%',
    },
    {
      key: 'orderType',
      title: t('affiliate:order.table.orderType'),
      dataIndex: 'orderType',
      width: '10%',
      render: (value: unknown) => {
        const type = value as AffiliateOrderType;
        let label = '';
        switch (type) {
          case AffiliateOrderType.POINT_PURCHASE:
            label = t('affiliate:order.type.pointPurchase');
            break;
          case AffiliateOrderType.SUBSCRIPTION:
            label = t('affiliate:order.type.subscription');
            break;
          case AffiliateOrderType.SERVICE:
            label = t('affiliate:order.type.service');
            break;
          default:
            label = type;
        }
        return <div>{label}</div>;
      },
    },
    {
      key: 'orderAmount',
      title: t('affiliate:order.table.orderAmount'),
      dataIndex: 'orderAmount',
      width: '10%',
      render: (value: unknown) => {
        return (
          <div className="text-right">
            {new Intl.NumberFormat('vi-VN').format(value as number)} đ
          </div>
        );
      },
    },
    {
      key: 'commissionAmount',
      title: t('affiliate:order.table.commissionAmount'),
      dataIndex: 'commissionAmount',
      width: '10%',
      render: (value: unknown) => {
        return (
          <div className="text-right">
            {new Intl.NumberFormat('vi-VN').format(value as number)} đ
          </div>
        );
      },
    },
    {
      key: 'status',
      title: t('affiliate:order.table.status'),
      dataIndex: 'status',
      width: '10%',
      render: (value: unknown) => {
        const status = value as AffiliateOrderStatus;
        return (
          <div
            className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
              status === AffiliateOrderStatus.COMPLETED
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : status === AffiliateOrderStatus.PENDING
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            }`}
          >
            {status === AffiliateOrderStatus.COMPLETED
              ? t('affiliate:order.status.completed')
              : status === AffiliateOrderStatus.PENDING
                ? t('affiliate:order.status.pending')
                : t('affiliate:order.status.cancelled')}
          </div>
        );
      },
    },
    {
      key: 'createdAt',
      title: t('affiliate:order.table.createdAt'),
      dataIndex: 'createdAt',
      width: '10%',
    },
  ];

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AffiliateOrderDto, AffiliateOrderQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );
  // Gọi API lấy danh sách đơn hàng với queryParams từ dataTable
  const { data: orderData, isLoading } = useOrders(dataTable.queryParams);

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [AffiliateOrderStatus.COMPLETED]: t('affiliate:order.status.completed'),
      [AffiliateOrderStatus.PENDING]: t('affiliate:order.status.pending'),
      [AffiliateOrderStatus.CANCELLED]: t('affiliate:order.status.cancelled'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={orderData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: orderData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: orderData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default AffiliateOrderListPage;
