import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  PointConversionDto,
  PointConversionStatus,
  PointConversionQueryDto,
} from '../types/point-conversion.types';
import { usePointConversionData } from '../hooks/usePointConversionData';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

/**
 * Trang quản lý lịch sử chuyển đổi điểm affiliate
 */
const PointConversionListPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // Sử dụng hook để lấy dữ liệu lịch sử chuyển đổi điểm
  const { usePointConversions } = usePointConversionData();

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: 'success',
        label: t('affiliate:pointConversion.status.success'),
        icon: 'check',
        value: PointConversionStatus.SUCCESS,
      },
      {
        id: 'pending',
        label: t('affiliate:pointConversion.status.pending'),
        icon: 'clock',
        value: PointConversionStatus.PENDING,
      },
      {
        id: 'failed',
        label: t('affiliate:pointConversion.status.failed'),
        icon: 'x',
        value: PointConversionStatus.FAILED,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): PointConversionQueryDto => {
    const queryParams: PointConversionQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || '',
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    // Thêm filter theo trạng thái nếu không phải 'all'
    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as PointConversionStatus;
    }

    // Thêm filter theo khoảng thời gian nếu có
    if (params.dateRange[0]) {
      queryParams.begin = Math.floor(params.dateRange[0].getTime() / 1000);
    }
    if (params.dateRange[1]) {
      queryParams.end = Math.floor(params.dateRange[1].getTime() / 1000);
    }

    return queryParams;
  };

  // Columns cho bảng
  const columns: TableColumn<PointConversionDto>[] = [
    { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
    {
      key: 'userName',
      title: t('affiliate:pointConversion.table.userName'),
      dataIndex: 'userName',
      width: '15%',
      sortable: true,
    },
    {
      key: 'userEmail',
      title: t('affiliate:pointConversion.table.userEmail'),
      dataIndex: 'userEmail',
      width: '15%',
      sortable: true,
    },
    {
      key: 'amount',
      title: t('affiliate:pointConversion.table.amount'),
      dataIndex: 'amount',
      width: '15%',
      sortable: true,
      render: (value: unknown) => {
        const numValue = typeof value === 'string' ? parseFloat(value) : (value as number);
        return (
          <div className="text-right">{new Intl.NumberFormat('vi-VN').format(numValue)} đ</div>
        );
      },
    },
    {
      key: 'pointsConverted',
      title: t('affiliate:pointConversion.table.pointsConverted'),
      dataIndex: 'pointsConverted',
      width: '15%',
      sortable: true,
      render: (value: unknown) => {
        const numValue = typeof value === 'string' ? parseFloat(value) : (value as number);
        return <div className="text-right">{new Intl.NumberFormat('vi-VN').format(numValue)}</div>;
      },
    },
    {
      key: 'conversionRate',
      title: t('affiliate:pointConversion.table.conversionRate'),
      dataIndex: 'conversionRate',
      width: '10%',
      sortable: true,
      render: (value: unknown) => {
        return <div className="text-center">{value as number}</div>;
      },
    },
    {
      key: 'status',
      title: t('affiliate:pointConversion.table.status'),
      dataIndex: 'status',
      width: '10%',
      sortable: true,
      render: (value: unknown) => {
        const status = value as PointConversionStatus;
        return (
          <div
            className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
              status === PointConversionStatus.SUCCESS
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : status === PointConversionStatus.PENDING
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            }`}
          >
            {status === PointConversionStatus.SUCCESS
              ? t('affiliate:pointConversion.status.success')
              : status === PointConversionStatus.PENDING
                ? t('affiliate:pointConversion.status.pending')
                : t('affiliate:pointConversion.status.failed')}
          </div>
        );
      },
    },
    {
      key: 'createdAt',
      title: t('affiliate:pointConversion.table.createdAt'),
      dataIndex: 'createdAt',
      width: '15%',
      sortable: true,
      render: (value: unknown) => {
        let timestamp: number;
        if (typeof value === 'string') {
          // Nếu là string, chuyển đổi sang number
          timestamp = parseInt(value, 10);
        } else {
          timestamp = value as number;
        }

        // Kiểm tra xem timestamp có phải là milliseconds hay seconds
        // Nếu timestamp quá lớn (milliseconds), không cần nhân với 1000
        const date = timestamp > 9999999999 ? new Date(timestamp) : new Date(timestamp * 1000);
        return (
          <div>
            {date.toLocaleDateString('vi-VN')} {date.toLocaleTimeString('vi-VN')}
          </div>
        );
      },
    },
    {
      key: 'actions',
      title: t('common:actions'),
      width: '10%',
      render: (_: unknown, record: PointConversionDto) => (
        <div className="flex space-x-2">
          <Tooltip content={t('common:view')} position="top">
            <IconCard
              icon="eye"
              variant="default"
              size="sm"
              onClick={() => console.log('View', record.id)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<PointConversionDto, PointConversionQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách lịch sử chuyển đổi điểm với queryParams từ dataTable
  const { data: apiResponse, isLoading } = usePointConversions(dataTable.queryParams);

  // Chuyển đổi apiResponse sang dạng PaginatedResult để sử dụng
  const conversionData = apiResponse
    ? (apiResponse as unknown as PaginatedResult<PointConversionDto>)
    : undefined;

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [PointConversionStatus.SUCCESS]: t('affiliate:pointConversion.status.success'),
      [PointConversionStatus.PENDING]: t('affiliate:pointConversion.status.pending'),
      [PointConversionStatus.FAILED]: t('affiliate:pointConversion.status.failed'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={conversionData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: conversionData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: conversionData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default PointConversionListPage;
