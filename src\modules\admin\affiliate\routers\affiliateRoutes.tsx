import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Loading } from '@/shared/components/common';
import { lazy } from 'react';

const AffiliateManagementPage = lazy(() => import('../pages/AffiliateManagementPage'));
const PublisherListPage = lazy(() => import('../pages/PublisherListPage'));
const AffiliateOrderListPage = lazy(() => import('../pages/AffiliateOrderListPage'));
const AffiliateRankListPageOptimized = lazy(() => import('../pages/AffiliateRankListPageOptimized'));
const PointConversionListPage = lazy(() => import('../pages/PointConversionListPage'));

/**
 * Routes cho module affiliate
 */

export const affiliateRoutes: RouteObject[] = [
  {
    path: '/admin/affiliate',
    element: (
      <AdminLayout title="Quản lý Affiliate">
        <Suspense fallback={<Loading />}>
          <AffiliateManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/affiliate/publishers',
    element: (
      <AdminLayout title="Quản lý Publisher">
        <Suspense fallback={<Loading />}>
          <PublisherListPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/affiliate/ranks',
    element: (
      <AdminLayout title="Quản lý Cấp bậc Affiliate">
        <Suspense fallback={<Loading />}>
          <AffiliateRankListPageOptimized />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/affiliate/orders',
    element: (
      <AdminLayout title="Quản lý Đơn hàng Affiliate">
        <Suspense fallback={<Loading />}>
          <AffiliateOrderListPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/affiliate/point-conversions',
    element: (
      <AdminLayout title="Lịch sử đổi điểm Affiliate">
        <Suspense fallback={<Loading />}>
          <PointConversionListPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default affiliateRoutes;
