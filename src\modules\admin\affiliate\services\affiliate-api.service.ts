/**
 * Service cho API affiliate
 */
import { apiClient } from '@/shared/api';
import {
  AffiliateAccountDto,
  AffiliateAccountQueryDto,
  AffiliateAccountStatus,
  AffiliateConditionRangesResponse,
  AffiliateOrderDto,
  AffiliateOrderQueryDto,
  AffiliateRankDto,
  AffiliateRankQueryDto,
  CreateAffiliateRankDto,
  PaginatedAffiliateAccountResult,
  PaginatedAffiliateOrderResult,
  PaginatedAffiliateRankResult,
  UpdateAffiliateRankDto,
} from '../types/api.types';

/**
 * Service cho API tài khoản affiliate
 */
export const affiliateAccountApi = {
  /**
   * Lấy danh sách tài khoản affiliate
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tài khoản affiliate với phân trang
   */
  getAccounts: async (queryDto: AffiliateAccountQueryDto) => {
    const response = await apiClient.get<PaginatedAffiliateAccountResult>(
      '/admin/affiliate/accounts',
      { params: queryDto }
    );
    return response.result;
  },

  /**
   * Lấy thông tin chi tiết tài khoản affiliate
   * @param id ID của tài khoản affiliate
   * @returns Thông tin chi tiết tài khoản affiliate
   */
  getAccountById: async (id: number) => {
    const response = await apiClient.get<AffiliateAccountDto>(`/admin/affiliate/accounts/${id}`);
    return response.result;
  },

  /**
   * Cập nhật trạng thái tài khoản affiliate
   * @param id ID của tài khoản affiliate
   * @param status Trạng thái mới
   * @returns Thông tin tài khoản affiliate đã cập nhật
   */
  updateAccountStatus: async (id: number, status: AffiliateAccountStatus) => {
    const response = await apiClient.patch<AffiliateAccountDto>(
      `/admin/affiliate/accounts/${id}/status`,
      { status }
    );
    return response.result;
  },
};

/**
 * Service cho API rank affiliate
 */
export const affiliateRankApi = {
  /**
   * Lấy danh sách rank affiliate
   * @param queryDto Tham số truy vấn
   * @returns Danh sách rank affiliate với phân trang
   */
  getRanks: async (queryDto: AffiliateRankQueryDto) => {
    const response = await apiClient.get<PaginatedAffiliateRankResult>('/admin/affiliate/ranks', {
      params: queryDto,
    });
    return response.result;
  },

  /**
   * Lấy thông tin chi tiết rank affiliate
   * @param id ID của rank affiliate
   * @returns Thông tin chi tiết rank affiliate
   */
  getRankById: async (id: number) => {
    const response = await apiClient.get<AffiliateRankDto>(`/admin/affiliate/ranks/${id}`);
    return response.result;
  },

  /**
   * Tạo rank affiliate mới
   * @param createRankDto Thông tin rank cần tạo
   * @returns Thông tin rank đã tạo
   */
  createRank: async (createRankDto: CreateAffiliateRankDto) => {
    const response = await apiClient.post<AffiliateRankDto>(
      '/admin/affiliate/ranks',
      createRankDto
    );
    return response.result;
  },

  /**
   * Cập nhật thông tin rank affiliate
   * @param id ID của rank affiliate
   * @param updateRankDto Thông tin cần cập nhật
   * @returns Thông tin rank đã cập nhật
   */
  updateRank: async (id: number, updateRankDto: UpdateAffiliateRankDto) => {
    const response = await apiClient.patch<AffiliateRankDto>(
      `/admin/affiliate/ranks/${id}`,
      updateRankDto
    );
    return response.result;
  },

  /**
   * Cập nhật trạng thái kích hoạt của rank affiliate
   * @param id ID của rank affiliate
   * @param isActive Trạng thái kích hoạt mới
   * @returns Thông tin rank đã cập nhật
   */
  updateRankStatus: async (id: number, isActive: boolean) => {
    const response = await apiClient.patch<AffiliateRankDto>(
      `/admin/affiliate/ranks/${id}/status`,
      { isActive }
    );
    return response.result;
  },

  /**
   * Lấy khoảng minCondition và maxCondition đã sử dụng
   * @returns Danh sách khoảng minCondition và maxCondition đã sử dụng
   */
  getUsedConditionRanges: async () => {
    const response = await apiClient.get<AffiliateConditionRangesResponse>(
      '/admin/affiliate/ranks/info/condition-ranges'
    );
    return response.result;
  },

  /**
   * Xóa rank affiliate
   * @param id ID của rank affiliate cần xóa
   * @returns Kết quả xóa
   */
  deleteRank: async (id: number) => {
    const response = await apiClient.delete<{ success: boolean }>(
      `/admin/affiliate/ranks/delete/${id}`
    );
    return response.result;
  },
};

/**
 * Service cho API đơn hàng affiliate
 */
export const affiliateOrderApi = {
  /**
   * Lấy danh sách đơn hàng affiliate
   * @param queryDto Tham số truy vấn
   * @returns Danh sách đơn hàng affiliate với phân trang
   */
  getOrders: async (queryDto: AffiliateOrderQueryDto) => {
    const response = await apiClient.get<PaginatedAffiliateOrderResult>('/admin/affiliate/orders', {
      params: queryDto,
    });
    return response.result;
  },

  /**
   * Lấy thông tin chi tiết đơn hàng affiliate
   * @param id ID của đơn hàng affiliate
   * @returns Thông tin chi tiết đơn hàng affiliate
   */
  getOrderById: async (id: number) => {
    const response = await apiClient.get<AffiliateOrderDto>(`/admin/affiliate/orders/${id}`);
    return response.result;
  },
};
