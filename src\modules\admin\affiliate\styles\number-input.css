/**
 * CSS tùy chỉnh cho input number
 */

/* Tùy chỉnh nút tăng/giảm của input number */
.number-input-custom::-webkit-outer-spin-button,
.number-input-custom::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
  opacity: 1;
}

/* Tạo nút tăng/giảm tùy chỉnh */
.number-input-custom {
  position: relative;
}

/* Tạo container cho nút tăng/giảm */
.number-input-wrapper {
  position: relative;
}

/* Thêm nút tăng/giảm tùy chỉnh */
.number-input-wrapper::after {
  content: '';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='18 15 12 9 6 15'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: contain;
  pointer-events: none;
  opacity: 0.5;
}

/* Loại bỏ mũi tên mặc định trên Firefox */
.number-input-custom {
  -moz-appearance: textfield;
}

/* Tùy chỉnh khi hover */
.number-input-custom:hover {
  border-color: var(--color-primary);
}

/* Tùy chỉnh khi focus */
.number-input-custom:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

/* Tạo nút tăng/giảm tùy chỉnh với JavaScript */
.number-input-controls {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  width: 20px;
  border-left: 1px solid var(--color-border);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.number-input-wrapper:hover .number-input-controls {
  opacity: 1;
}

.number-input-up,
.number-input-down {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  font-size: 10px;
  color: var(--color-text-secondary);
}

.number-input-up {
  border-bottom: 1px solid var(--color-border);
}

.number-input-up:hover,
.number-input-down:hover {
  background-color: var(--color-bg-hover);
}
