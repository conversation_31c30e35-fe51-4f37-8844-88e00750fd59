/**
 * <PERSON><PERSON><PERSON> kiểu dữ liệu liên quan đến affiliate UI
 */
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';
import {
  AffiliateAccountStatus as ApiAffiliateAccountStatus,
  AffiliateOrderStatus as ApiAffiliateOrderStatus,
  AffiliateOrderType as ApiAffiliateOrderType,
} from './api.types';

/**
 * Trạng thái publisher
 */
export enum PublisherStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

/**
 * DTO cho publisher
 */
export interface PublisherDto {
  id: number;
  userId: number;
  userName: string;
  userEmail: string;
  userPhone: string;
  referralCode: string;
  totalReferrals: number;
  totalCommission: number;
  status: PublisherStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO cho tạo publisher mới
 */
export interface CreatePublisherDto {
  userId: number;
  referralCode: string;
  status: PublisherStatus;
}

/**
 * DTO cho cập nhật publisher
 */
export interface UpdatePublisherDto {
  referralCode?: string;
  status?: PublisherStatus;
}

/**
 * DTO cho query publisher
 */
export interface PublisherQueryDto extends QueryDto {
  status?: PublisherStatus;
  userId?: number;
}

/**
 * Trạng thái rank affiliate
 */
export enum AffiliateRankStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * DTO cho rank affiliate
 */
export interface AffiliateRankDto {
  id: number;
  name: string;
  description: string;
  minReferrals: number;
  commissionRate: number;
  status: AffiliateRankStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO cho tạo rank affiliate mới
 */
export interface CreateAffiliateRankDto {
  name: string;
  description: string;
  minReferrals: number;
  commissionRate: number;
  status: AffiliateRankStatus;
}

/**
 * DTO cho cập nhật rank affiliate
 */
export interface UpdateAffiliateRankDto {
  name?: string;
  description?: string;
  minReferrals?: number;
  commissionRate?: number;
  status?: AffiliateRankStatus;
}

/**
 * DTO cho query rank affiliate
 */
export interface AffiliateRankQueryDto extends QueryDto {
  status?: AffiliateRankStatus;
}

/**
 * Trạng thái đơn hàng affiliate
 */
export enum AffiliateOrderStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * Loại đơn hàng affiliate
 */
export enum AffiliateOrderType {
  POINT_PURCHASE = 'point_purchase',
  SUBSCRIPTION = 'subscription',
  SERVICE = 'service',
}

/**
 * DTO cho đơn hàng affiliate
 */
export interface AffiliateOrderDto {
  id: number;
  publisherId: number;
  publisherName: string;
  userId: number;
  userName: string;
  orderNumber: string;
  orderType: AffiliateOrderType;
  orderAmount: number;
  commissionAmount: number;
  commissionRate: number;
  status: AffiliateOrderStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO cho query đơn hàng affiliate
 */
export interface AffiliateOrderQueryDto extends QueryDto {
  status?: AffiliateOrderStatus;
  orderType?: AffiliateOrderType;
  publisherId?: number;
  userId?: number;
  startDate?: string;
  endDate?: string;
}

/**
 * Kiểu dữ liệu cho kết quả phân trang publisher
 */
export type PaginatedPublisherResult = PaginatedResult<PublisherDto>;

/**
 * Kiểu dữ liệu cho kết quả phân trang rank affiliate
 */
export type PaginatedAffiliateRankResult = PaginatedResult<AffiliateRankDto>;

/**
 * Kiểu dữ liệu cho kết quả phân trang đơn hàng affiliate
 */
export type PaginatedAffiliateOrderResult = PaginatedResult<AffiliateOrderDto>;

/**
 * Kiểu dữ liệu cho phản hồi API publisher
 */
export type PublisherApiResponse<T> = ApiResponseDto<T>;

/**
 * Kiểu dữ liệu cho phản hồi API rank affiliate
 */
export type AffiliateRankApiResponse<T> = ApiResponseDto<T>;

/**
 * Kiểu dữ liệu cho phản hồi API đơn hàng affiliate
 */
export type AffiliateOrderApiResponse<T> = ApiResponseDto<T>;

/**
 * Hàm chuyển đổi từ API AffiliateAccountStatus sang UI PublisherStatus
 */
export const mapApiStatusToPublisherStatus = (
  status: ApiAffiliateAccountStatus
): PublisherStatus => {
  switch (status) {
    case ApiAffiliateAccountStatus.ACTIVE:
      return PublisherStatus.ACTIVE;
    case ApiAffiliateAccountStatus.INACTIVE:
      return PublisherStatus.INACTIVE;
    case ApiAffiliateAccountStatus.PENDING:
      return PublisherStatus.PENDING;
    case ApiAffiliateAccountStatus.BLOCKED:
      return PublisherStatus.SUSPENDED;
    default:
      return PublisherStatus.INACTIVE;
  }
};

/**
 * Hàm chuyển đổi từ UI PublisherStatus sang API AffiliateAccountStatus
 */
export const mapPublisherStatusToApiStatus = (
  status: PublisherStatus
): ApiAffiliateAccountStatus => {
  switch (status) {
    case PublisherStatus.ACTIVE:
      return ApiAffiliateAccountStatus.ACTIVE;
    case PublisherStatus.INACTIVE:
      return ApiAffiliateAccountStatus.INACTIVE;
    case PublisherStatus.PENDING:
      return ApiAffiliateAccountStatus.PENDING;
    case PublisherStatus.SUSPENDED:
      return ApiAffiliateAccountStatus.BLOCKED;
    default:
      return ApiAffiliateAccountStatus.INACTIVE;
  }
};

/**
 * Hàm chuyển đổi từ API AffiliateOrderStatus sang UI AffiliateOrderStatus
 */
export const mapApiOrderStatusToUiOrderStatus = (
  status: ApiAffiliateOrderStatus
): AffiliateOrderStatus => {
  switch (status) {
    case ApiAffiliateOrderStatus.COMPLETED:
      return AffiliateOrderStatus.COMPLETED;
    case ApiAffiliateOrderStatus.PENDING:
      return AffiliateOrderStatus.PENDING;
    case ApiAffiliateOrderStatus.CANCELLED:
      return AffiliateOrderStatus.CANCELLED;
    default:
      return AffiliateOrderStatus.PENDING;
  }
};

/**
 * Hàm chuyển đổi từ API AffiliateOrderType sang UI AffiliateOrderType
 */
export const mapApiOrderTypeToUiOrderType = (type: ApiAffiliateOrderType): AffiliateOrderType => {
  switch (type) {
    case ApiAffiliateOrderType.POINT_PURCHASE:
      return AffiliateOrderType.POINT_PURCHASE;
    case ApiAffiliateOrderType.SUBSCRIPTION:
      return AffiliateOrderType.SUBSCRIPTION;
    case ApiAffiliateOrderType.SERVICE:
      return AffiliateOrderType.SERVICE;
    default:
      return AffiliateOrderType.POINT_PURCHASE;
  }
};
