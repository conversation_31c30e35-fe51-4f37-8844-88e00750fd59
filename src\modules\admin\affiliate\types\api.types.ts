/**
 * Types cho API affiliate
 */
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái tài khoản affiliate
 */
export enum AffiliateAccountStatus {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  BLOCKED = 'BLOCKED',
  INACTIVE = 'INACTIVE',
}

/**
 * DTO cho thông tin người dùng trong tài khoản affiliate
 */
export interface AffiliateAccountUserDto {
  id: number;
  fullName: string;
  email: string;
  phoneNumber?: string;
}

/**
 * DTO cho thông tin rank trong tài khoản affiliate
 */
export interface AffiliateAccountRankDto {
  id: number;
  rankName: string;
  rankBadge: string;
  commission: number;
}

/**
 * DTO cho thông tin tài khoản affiliate
 */
export interface AffiliateAccountDto {
  id: number;
  user: AffiliateAccountUserDto;
  rank: AffiliateAccountRankDto;
  accountType: string;
  status: AffiliateAccountStatus;
  availableBalance: number;
  totalEarnings: number;
  performance: number;
  referralCode: string;
  createdAt: number;
  updatedAt?: number;
}

/**
 * DTO cho tham số truy vấn danh sách tài khoản affiliate
 */
export interface AffiliateAccountQueryDto extends QueryDto {
  status?: AffiliateAccountStatus;
  rankId?: number;
  accountType?: string;
}

/**
 * DTO cho thông tin rank affiliate
 */
export interface AffiliateRankDto {
  id: number;
  rankName: string;
  rankBadge: string;
  commission: number;
  minCondition: number;
  maxCondition: number;
  isActive: boolean;
  displayOrder: number;
  description?: string;
  createdAt: number;
  updatedAt?: number;
  uploadUrl?: string;
}

/**
 * DTO cho tham số truy vấn danh sách rank affiliate
 */
export interface AffiliateRankQueryDto extends QueryDto {
  isActive?: boolean;
}

/**
 * Enum cho loại hình ảnh
 */
export enum ImageTypeEnum {
  JPEG = 'image/jpeg',
  PNG = 'image/png',
  GIF = 'image/gif',
  WEBP = 'image/webp',
  SVG = 'image/svg+xml',
}

/**
 * DTO cho tạo rank affiliate mới
 */
export interface CreateAffiliateRankDto {
  /**
   * Tên rank
   */
  rankName: string;

  /**
   * Icon rank (key)
   */
  rankBadge?: string;

  /**
   * Loại hình ảnh cho icon rank
   */
  mediaType?: ImageTypeEnum;

  /**
   * Kích thước tối đa của file icon (bytes)
   */
  maxSize?: number;

  /**
   * Mức hoa hồng rank (%)
   */
  commission: number;

  /**
   * Điều kiện tối thiểu
   */
  minCondition: number;

  /**
   * Điều kiện tối đa
   */
  maxCondition: number;

  /**
   * Trạng thái kích hoạt
   */
  isActive: boolean;

  /**
   * Thứ tự hiển thị
   */
  displayOrder: number;

  /**
   * Mô tả rank
   */
  description?: string;
}

/**
 * DTO cho phản hồi khi tạo rank affiliate
 */
export interface CreateAffiliateRankResponseDto extends AffiliateRankDto {
  /**
   * URL tạm thời để upload icon rank
   */
  uploadUrl?: string;
}

/**
 * DTO cho cập nhật rank affiliate
 */
export interface UpdateAffiliateRankDto {
  rankName?: string;
  rankBadge?: string;
  commission?: number;
  minCondition?: number;
  maxCondition?: number;
  isActive?: boolean;
  displayOrder?: number;
  description?: string;
}

/**
 * Enum cho trạng thái đơn hàng affiliate
 */
export enum AffiliateOrderStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

/**
 * Enum cho loại đơn hàng affiliate
 */
export enum AffiliateOrderType {
  POINT_PURCHASE = 'POINT_PURCHASE',
  SUBSCRIPTION = 'SUBSCRIPTION',
  SERVICE = 'SERVICE',
}

/**
 * DTO cho thông tin đơn hàng affiliate
 */
export interface AffiliateOrderDto {
  id: number;
  orderNumber: string;
  publisherId: number;
  publisherName: string;
  userId: number;
  userName: string;
  orderType: AffiliateOrderType;
  orderAmount: number;
  commissionAmount: number;
  commissionRate: number;
  status: AffiliateOrderStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO cho tham số truy vấn danh sách đơn hàng affiliate
 */
export interface AffiliateOrderQueryDto extends QueryDto {
  status?: AffiliateOrderStatus;
  orderType?: AffiliateOrderType;
  publisherId?: number;
  userId?: number;
  startDate?: string;
  endDate?: string;
}

/**
 * Kiểu dữ liệu cho kết quả phân trang tài khoản affiliate
 */
export type PaginatedAffiliateAccountResult = PaginatedResult<AffiliateAccountDto>;

/**
 * Kiểu dữ liệu cho kết quả phân trang rank affiliate
 */
export type PaginatedAffiliateRankResult = PaginatedResult<AffiliateRankDto>;

/**
 * Kiểu dữ liệu cho kết quả phân trang đơn hàng affiliate
 */
export type PaginatedAffiliateOrderResult = PaginatedResult<AffiliateOrderDto>;

/**
 * Kiểu dữ liệu cho phản hồi API tài khoản affiliate
 */
export type AffiliateAccountApiResponse<T> = ApiResponseDto<T>;

/**
 * Kiểu dữ liệu cho phản hồi API rank affiliate
 */
export type AffiliateRankApiResponse<T> = ApiResponseDto<T>;

/**
 * Kiểu dữ liệu cho phản hồi API đơn hàng affiliate
 */
export type AffiliateOrderApiResponse<T> = ApiResponseDto<T>;

/**
 * Kiểu dữ liệu cho khoảng điều kiện đã sử dụng
 */
export interface AffiliateConditionRange {
  /**
   * Giá trị điều kiện tối thiểu
   */
  minCondition: number;

  /**
   * Giá trị điều kiện tối đa
   */
  maxCondition: number;
}

/**
 * Kiểu dữ liệu cho phản hồi API khoảng điều kiện đã sử dụng
 */
export type AffiliateConditionRangesResponse = ApiResponseDto<AffiliateConditionRange[]>;
