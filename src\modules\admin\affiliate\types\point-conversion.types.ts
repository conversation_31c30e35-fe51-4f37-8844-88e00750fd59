/**
 * <PERSON><PERSON><PERSON> kiểu dữ liệu liên quan đến chuyển đổi điểm affiliate
 */
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Trạng thái chuyển đổi điểm
 */
export enum PointConversionStatus {
  PENDING = 'PENDING', // Đang xử lý
  SUCCESS = 'SUCCESS', // Thành công
  FAILED = 'FAILED', // Thất bại
}

/**
 * DTO cho thông tin lịch sử chuyển đổi điểm
 */
export interface PointConversionDto {
  id: number;
  affiliateAccountId: number;
  userName: string;
  userEmail: string;
  pointsConverted: number | string;
  conversionRate: number;
  amount: number;
  createdAt: number | string;
  status: PointConversionStatus;
}

/**
 * DTO cho tham số truy vấn lịch sử chuyển đổi điểm
 */
export interface PointConversionQueryDto extends QueryDto {
  affiliateAccountId?: number;
  begin?: number;
  end?: number;
  status?: PointConversionStatus;
}

/**
 * Kiểu dữ liệu cho kết quả phân trang lịch sử chuyển đổi điểm
 */
export type PaginatedPointConversionResult = PaginatedResult<PointConversionDto>;

/**
 * Kiểu dữ liệu cho phản hồi API lịch sử chuyển đổi điểm
 */
export type PointConversionApiResponse<T> = ApiResponseDto<T>;
