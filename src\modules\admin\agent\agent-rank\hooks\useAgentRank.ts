import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { adminAgentRankService } from '../services/agent-rank.service';
import {
  AgentRankDetail,
  CreateAgentRankParams,
  UpdateAgentRankParams,
  AgentRankQueryParams,
  UpdateAgentRankResponse,
} from '../types/agent-rank.types';

// Query keys
export const ADMIN_AGENT_RANK_QUERY_KEYS = {
  all: ['admin', 'agent-rank'] as const,
  lists: () => [...ADMIN_AGENT_RANK_QUERY_KEYS.all, 'list'] as const,
  list: (params: AgentRankQueryParams) => [...ADMIN_AGENT_RANK_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_AGENT_RANK_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...ADMIN_AGENT_RANK_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách agent rank
 */
export const useAdminAgentRanks = (params: AgentRankQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_RANK_QUERY_KEYS.list(params),
    queryFn: () => adminAgentRankService.getAgentRanks(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thông tin chi tiết agent rank
 */
export const useAdminAgentRankDetail = (
  id: number,
  options?: Omit<UseQueryOptions<AgentRankDetail>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<AgentRankDetail>({
    queryKey: ADMIN_AGENT_RANK_QUERY_KEYS.detail(id),
    queryFn: () => adminAgentRankService.getAgentRankById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để tạo agent rank mới
 */
export const useCreateAdminAgentRank = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAgentRankParams) => adminAgentRankService.createAgentRank(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent rank
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_RANK_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật agent rank
 */
export const useUpdateAdminAgentRank = () => {
  const queryClient = useQueryClient();

  return useMutation<UpdateAgentRankResponse, Error, { id: number; data: UpdateAgentRankParams }>({
    mutationFn: ({ id, data }: { id: number; data: UpdateAgentRankParams }) =>
      adminAgentRankService.updateAgentRank(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết agent rank
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_RANK_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_RANK_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa agent rank
 */
export const useDeleteAdminAgentRank = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => adminAgentRankService.deleteAgentRank(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent rank
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_RANK_QUERY_KEYS.lists(),
      });
    },
  });
};
