import { z } from 'zod';
import { AgentRankSortBy, SortDirection } from '../types/agent-rank.types';

/**
 * Schema cho tạo agent rank
 */
export const createAgentRankSchema = z.object({
  name: z.string().min(1, 'Tên cấp bậc không được để trống').max(255, 'Tên cấp bậc không được quá 255 ký tự'),
  description: z.string().optional(),
  fileName: z.string().min(1, 'Tên file huy hiệu không được để trống'),
  minExp: z.coerce.number().min(0, 'Điểm kinh nghiệm tối thiểu không được nhỏ hơn 0'),
  maxExp: z.coerce.number().min(1, 'Điểm kinh nghiệm tối đa không được nhỏ hơn 1'),
  active: z.boolean().optional().default(false),
}).refine(
  (data) => data.maxExp > data.minExp,
  {
    message: 'Điểm kinh nghiệm tối đa phải lớn hơn điểm kinh nghiệm tối thiểu',
    path: ['maxExp'],
  }
);

/**
 * Schema cho cập nhật agent rank
 */
export const updateAgentRankSchema = z.object({
  name: z.string().min(1, 'Tên cấp bậc không được để trống').max(255, 'Tên cấp bậc không được quá 255 ký tự').optional(),
  description: z.string().optional(),
  fileName: z.string().min(1, 'Tên file huy hiệu không được để trống').optional(),
  minExp: z.coerce.number().min(0, 'Điểm kinh nghiệm tối thiểu không được nhỏ hơn 0').optional(),
  maxExp: z.coerce.number().min(1, 'Điểm kinh nghiệm tối đa không được nhỏ hơn 1').optional(),
  active: z.boolean().optional(),
}).refine(
  (data) => {
    if (data.maxExp !== undefined && data.minExp !== undefined) {
      return data.maxExp > data.minExp;
    }
    return true;
  },
  {
    message: 'Điểm kinh nghiệm tối đa phải lớn hơn điểm kinh nghiệm tối thiểu',
    path: ['maxExp'],
  }
);

/**
 * Schema cho query agent rank
 */
export const agentRankQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  active: z.boolean().optional(),
  sortBy: z.nativeEnum(AgentRankSortBy).optional().default(AgentRankSortBy.ID),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.ASC),
});

/**
 * Schema cho agent rank list item
 */
export const agentRankListItemSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().nullable(),
  badge: z.string(),
  minExp: z.number(),
  maxExp: z.number(),
  active: z.boolean(),
});

/**
 * Schema cho agent rank detail
 */
export const agentRankDetailSchema = agentRankListItemSchema;

/**
 * Schema cho response tạo agent rank
 */
export const createAgentRankResponseSchema = z.object({
  uploadUrl: z.string(),
  id: z.number(),
});

// Export types từ schemas
export type CreateAgentRankFormData = z.infer<typeof createAgentRankSchema>;
export type UpdateAgentRankFormData = z.infer<typeof updateAgentRankSchema>;
export type AgentRankQueryFormData = z.infer<typeof agentRankQuerySchema>;
