import { apiClient } from '@/shared/api/axios';
import {
  AgentRankDetail,
  CreateAgentRankParams,
  UpdateAgentRankParams,
  AgentRankQueryParams,
  CreateAgentRankResponse,
  UpdateAgentRankResponse,
} from '../types/agent-rank.types';

/**
 * Service để tương tác với API agent rank của admin
 */
export class AdminAgentRankService {
  private baseUrl = '/admin/agent-ranks';

  /**
   * Lấy danh sách agent rank
   * @param params Tham số truy vấn
   * @returns Danh sách agent rank
   */
  async getAgentRanks(params: AgentRankQueryParams) {
    try {
      // Chuẩn bị query parameters với sortBy mặc định
      const queryParams = {
        page: params.page || 1,
        limit: params.limit || 10,
        sortBy: params.sortBy || 'name',
        ...params
      };

      console.log('🔍 [AdminAgentRankService] Query params:', queryParams);

      const response = await apiClient.get(this.baseUrl, {
        params: queryParams,
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching agent ranks:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết agent rank theo ID
   * @param id ID của agent rank
   * @returns Thông tin chi tiết agent rank
   */
  async getAgentRankById(id: number): Promise<AgentRankDetail> {
    try {
      const response = await apiClient.get<AgentRankDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching agent rank ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo agent rank mới
   * @param data Dữ liệu tạo agent rank
   * @returns Response tạo agent rank
   */
  async createAgentRank(data: CreateAgentRankParams): Promise<CreateAgentRankResponse> {
    try {
      const response = await apiClient.post<CreateAgentRankResponse>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating agent rank:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin agent rank
   * @param id ID của agent rank
   * @param data Dữ liệu cập nhật
   * @returns Response cập nhật agent rank
   */
  async updateAgentRank(id: number, data: UpdateAgentRankParams): Promise<UpdateAgentRankResponse> {
    try {
      const response = await apiClient.put<UpdateAgentRankResponse>(`${this.baseUrl}/${id}`, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error updating agent rank ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa agent rank
   * @param id ID của agent rank
   * @returns Kết quả xóa
   */
  async deleteAgentRank(id: number): Promise<boolean> {
    try {
      const response = await apiClient.delete<boolean>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error deleting agent rank ${id}:`, error);
      throw error;
    }
  }
}

// Export instance
export const adminAgentRankService = new AdminAgentRankService();
