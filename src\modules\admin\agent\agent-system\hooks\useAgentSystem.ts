import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminAgentSystemService } from '../services/agent-system.service';
import {
  AgentSystemDetail,
  CreateAgentSystemParams,
  UpdateAgentSystemParams,
  UpdateAgentSystemStatusParams,
  AgentSystemQueryParams,
} from '../types/agent-system.types';

// Query keys
export const ADMIN_AGENT_SYSTEM_QUERY_KEYS = {
  all: ['admin', 'agent-system'] as const,
  lists: () => [...ADMIN_AGENT_SYSTEM_QUERY_KEYS.all, 'list'] as const,
  list: (params: AgentSystemQueryParams) =>
    [...ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_AGENT_SYSTEM_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_AGENT_SYSTEM_QUERY_KEYS.details(), id] as const,
  trash: () => [...ADMIN_AGENT_SYSTEM_QUERY_KEYS.all, 'trash'] as const,
  trashList: (params: AgentSystemQueryParams) =>
    [...ADMIN_AGENT_SYSTEM_QUERY_KEYS.trash(), params] as const,
};

/**
 * Hook để lấy danh sách agent system
 */
export const useAdminAgentSystems = (params: AgentSystemQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.list(params),
    queryFn: () => adminAgentSystemService.getAgentSystems(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy danh sách agent system đã xóa
 */
export const useAdminAgentSystemsTrash = (params: AgentSystemQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.trashList(params),
    queryFn: () => adminAgentSystemService.getDeletedAgentSystems(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thông tin chi tiết agent system
 */
export const useAdminAgentSystemDetail = (id: string) => {
  return useQuery<AgentSystemDetail>({
    queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.detail(id),
    queryFn: () => adminAgentSystemService.getAgentSystemById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tạo agent system mới
 */
export const useCreateAdminAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAgentSystemParams) => adminAgentSystemService.createAgentSystem(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent system
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật agent system
 */
export const useUpdateAdminAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentSystemParams }) =>
      adminAgentSystemService.updateAgentSystem(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết agent system
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật trạng thái agent system
 */
export const useUpdateAdminAgentSystemStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentSystemStatusParams }) =>
      adminAgentSystemService.updateAgentSystemStatus(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết agent system
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa agent system
 */
export const useDeleteAdminAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentSystemService.deleteAgentSystem(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent system
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
      // Invalidate danh sách trash
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.trash(),
      });
    },
  });
};

/**
 * Hook để gán vai trò cho agent system
 */
export const useAssignRoleToAdminAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, roleId }: { id: string; roleId: string }) =>
      adminAgentSystemService.assignRoleToAgentSystem(id, roleId),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết agent system
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa vai trò khỏi agent system
 */
export const useRemoveRoleFromAdminAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, roleId }: { id: string; roleId: string }) =>
      adminAgentSystemService.removeRoleFromAgentSystem(id, roleId),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết agent system
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để khôi phục agent system đã xóa
 */
export const useRestoreAdminAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentSystemService.restoreAgentSystem(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent system
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
      // Invalidate danh sách trash
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.trash(),
      });
    },
  });
};

/**
 * Hook để toggle trạng thái active của agent system
 */
export const useToggleActiveAdminAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentSystemService.toggleActiveAgentSystem(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent system
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để set agent system làm supervisor
 */
export const useSetSupervisorAdminAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentSystemService.setSupervisorAgentSystem(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent system để cập nhật tất cả cards
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};
