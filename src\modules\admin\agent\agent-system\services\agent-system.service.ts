import { apiClient } from '@/shared/api/axios';
import {
  AgentSystemDetail,
  CreateAgentSystemParams,
  UpdateAgentSystemParams,
  UpdateAgentSystemStatusParams,
  AgentSystemQueryParams,
  CreateAgentSystemResponse,
  UpdateAgentSystemResponse,
} from '../types/agent-system.types';

/**
 * Service để tương tác với API agent system của admin
 */
export class AdminAgentSystemService {
  private baseUrl = '/admin/agents/system';

  /**
   * L<PERSON>y danh sách agent system
   * @param params Tham số truy vấn
   * @returns Danh sách agent system
   */
  async getAgentSystems(params: AgentSystemQueryParams) {
    try {
      const response = await apiClient.get(this.baseUrl, {
        params,
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching agent systems:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách agent system đã xóa
   * @param params Tham số truy vấn
   * @returns Danh sách agent system đã xóa
   */
  async getDeletedAgentSystems(params: AgentSystemQueryParams) {
    try {
      const response = await apiClient.get(`${this.baseUrl}/trash`, {
        params,
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching deleted agent systems:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết agent system theo ID
   * @param id ID của agent system
   * @returns Thông tin chi tiết agent system
   */
  async getAgentSystemById(id: string): Promise<AgentSystemDetail> {
    try {
      const response = await apiClient.get<AgentSystemDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching agent system ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo agent system mới
   * @param data Dữ liệu tạo agent system
   * @returns Response tạo agent system
   */
  async createAgentSystem(data: CreateAgentSystemParams): Promise<CreateAgentSystemResponse> {
    try {
      const response = await apiClient.post<CreateAgentSystemResponse>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating agent system:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin agent system
   * @param id ID của agent system
   * @param data Dữ liệu cập nhật
   * @returns Response cập nhật agent system
   */
  async updateAgentSystem(
    id: string,
    data: UpdateAgentSystemParams
  ): Promise<UpdateAgentSystemResponse> {
    try {
      const response = await apiClient.patch<UpdateAgentSystemResponse>(
        `${this.baseUrl}/${id}`,
        data,
        { tokenType: 'admin' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error updating agent system ${id}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái agent system
   * @param id ID của agent system
   * @param data Dữ liệu cập nhật trạng thái
   * @returns Kết quả cập nhật trạng thái
   */
  async updateAgentSystemStatus(id: string, data: UpdateAgentSystemStatusParams): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}/status`, data, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error updating agent system status ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa agent system
   * @param id ID của agent system
   * @returns Kết quả xóa
   */
  async deleteAgentSystem(id: string): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting agent system ${id}:`, error);
      throw error;
    }
  }

  /**
   * Gán vai trò cho agent system
   * @param id ID của agent system
   * @param roleId ID của vai trò
   * @returns Kết quả gán vai trò
   */
  async assignRoleToAgentSystem(id: string, roleId: string): Promise<boolean> {
    try {
      await apiClient.post(`${this.baseUrl}/${id}/roles/${roleId}`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error assigning role ${roleId} to agent system ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa vai trò khỏi agent system
   * @param id ID của agent system
   * @param roleId ID của vai trò
   * @returns Kết quả xóa vai trò
   */
  async removeRoleFromAgentSystem(id: string, roleId: string): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}/roles/${roleId}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error removing role ${roleId} from agent system ${id}:`, error);
      throw error;
    }
  }

  /**
   * Khôi phục agent system đã xóa
   * @param id ID của agent system
   * @returns Kết quả khôi phục
   */
  async restoreAgentSystem(id: string): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}/restore`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error restoring agent system ${id}:`, error);
      throw error;
    }
  }

  /**
   * Toggle trạng thái active của agent system
   * @param id ID của agent system
   * @returns Kết quả toggle active
   */
  async toggleActiveAgentSystem(id: string): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}/toggle-active`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error toggling active status for agent system ${id}:`, error);
      throw error;
    }
  }

  /**
   * Set agent system làm supervisor
   * @param id ID của agent system
   * @returns Kết quả set supervisor
   */
  async setSupervisorAgentSystem(id: string): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}/set-supervisor`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error setting agent system ${id} as supervisor:`, error);
      throw error;
    }
  }
}

// Export instance
export const adminAgentSystemService = new AdminAgentSystemService();
