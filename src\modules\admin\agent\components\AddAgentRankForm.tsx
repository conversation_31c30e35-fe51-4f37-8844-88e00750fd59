import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Card,
  FormGrid,
  Divider,
  Icon,
  Checkbox,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { CreateAgentRankParams } from '../agent-rank/types/agent-rank.types';
import { NotificationUtil } from '@/shared/utils/notification';

// Types
type CreateAgentRankDto = CreateAgentRankParams;

interface CreateAgentRankResponse {
  result?: {
    uploadUrl?: string;
  };
}

interface AddAgentRankFormProps {
  onSubmit: (values: CreateAgentRankDto) => Promise<CreateAgentRankResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createAgentRankSchema = (t: any) => z.object({
  name: z.string()
    .min(1, t('admin:agent.rank.validation.nameRequired', 'Tên cấp bậc là bắt buộc'))
    .trim(),
  description: z.string()
    .min(1, t('admin:agent.rank.validation.descriptionRequired', 'Mô tả là bắt buộc'))
    .trim(),
  minExp: z.coerce.number()
    .min(0, t('admin:agent.rank.validation.minExpInvalid', 'Kinh nghiệm tối thiểu phải >= 0'))
    .int(t('admin:agent.rank.validation.minExpInteger', 'Kinh nghiệm tối thiểu phải là số nguyên')),
  maxExp: z.coerce.number()
    .min(1, t('admin:agent.rank.validation.maxExpInvalid', 'Kinh nghiệm tối đa phải > 0'))
    .int(t('admin:agent.rank.validation.maxExpInteger', 'Kinh nghiệm tối đa phải là số nguyên')),
  active: z.boolean().optional(),
}).refine((data) => data.maxExp > data.minExp, {
  message: t('admin:agent.rank.validation.expRangeInvalid', 'Kinh nghiệm tối đa phải lớn hơn kinh nghiệm tối thiểu'),
  path: ['maxExp'],
});

const AddAgentRankForm: React.FC<AddAgentRankFormProps> = ({ onSubmit, onCancel, onSuccess }) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Badge upload states
  const [badgeFiles, setBadgeFiles] = useState<FileWithMetadata[]>([]);

  // Default values for the form
  const defaultValues = React.useMemo(() => ({
    name: '',
    description: '',
    minExp: 0,
    maxExp: 100,
    active: true,
  }), []);

  // Handle badge file selection - chỉ cho phép 1 ảnh
  const handleBadgeChange = useCallback((files: FileWithMetadata[]) => {
    // Chỉ lấy file đầu tiên nếu có nhiều file
    if (files.length > 0 && files[0]) {
      setBadgeFiles([files[0]]);
    } else {
      setBadgeFiles([]);
    }
  }, []);

  // Upload image file to S3 using presigned URL
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    console.log(`🔍 [uploadImageFile] Starting upload:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadUrl: presignedUrl
    });

    const response = await fetch(presignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    console.log(`🔍 [uploadImageFile] Response status:`, response.status);
    console.log(`🔍 [uploadImageFile] Response ok:`, response.ok);

    if (!response.ok) {
      const responseText = await response.text();
      console.error(`❌ [uploadImageFile] Upload failed:`, {
        status: response.status,
        statusText: response.statusText,
        responseBody: responseText
      });
      throw new Error(`Failed to upload image: ${response.status} ${response.statusText}`);
    }

    console.log(`✅ [uploadImageFile] Upload successful for:`, file.name);
    return response;
  };

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    setIsSubmitting(true);

    try {
      // Prepare form data
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const rankData: any = {
        name: values['name'] as string,
        description: values['description'] as string,
        fileName: badgeFiles.length > 0 && badgeFiles[0] ? badgeFiles[0].file.name : 'default-badge.png',
        minExp: values['minExp'] as number,
        maxExp: values['maxExp'] as number,
        active: values['active'] as boolean || true,
      };

      console.log('Submitting rank data:', rankData);

      // Submit form data
      const createResult = await onSubmit(rankData);
      console.log('Rank created successfully:', createResult);

      // Upload badge if provided
      const allUploadPromises: Promise<void>[] = [];

      if (badgeFiles.length > 0 && createResult.result?.uploadUrl) {
        console.log('🔍 Starting badge upload...');
        if (badgeFiles[0]) {
          console.log('🔍 Badge file details:', {
            fileName: badgeFiles[0].file.name,
            fileSize: badgeFiles[0].file.size,
            fileType: badgeFiles[0].file.type
          });
        }
        console.log('🔍 Badge upload URL:', createResult.result.uploadUrl);

        const badgeFile = badgeFiles[0]?.file;
        const uploadUrl = createResult.result.uploadUrl;

        const badgeUploadPromise = (async () => {
          if (!badgeFile) {
            throw new Error('Badge file is undefined');
          }

          console.log(`🔍 Uploading badge:`, {
            fileName: badgeFile.name,
            fileSize: badgeFile.size,
            fileType: badgeFile.type,
            uploadUrl: uploadUrl
          });

          try {
            await uploadImageFile(badgeFile, uploadUrl);
            console.log(`✅ Badge uploaded successfully`);
          } catch (error) {
            console.error(`❌ Exception uploading badge:`, error);
            throw error;
          }
        })();
        allUploadPromises.push(badgeUploadPromise);
      } else {
        console.log('⚠️ SKIPPING badge upload:', {
          hasBadgeFiles: badgeFiles.length > 0,
          hasUploadUrl: !!createResult.result?.uploadUrl,
          badgeFilesCount: badgeFiles.length
        });
      }

      // Đợi tất cả uploads hoàn thành
      console.log('🔍 Total upload promises:', allUploadPromises.length);
      if (allUploadPromises.length > 0) {
        try {
          await Promise.all(allUploadPromises);
          console.log('🎉 All uploads completed successfully');
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);
          throw new Error(`Upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
        }
      } else {
        console.log('ℹ️ No files to upload');
      }

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error submitting rank form:', error);

      // Hiển thị thông báo lỗi chi tiết
      let errorMessage = t('admin:agent.rank.form.createError', 'Có lỗi xảy ra khi tạo cấp bậc');

      // Kiểm tra nếu là lỗi từ API
      if (error && typeof error === 'object' && 'response' in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const apiError = error as any;
        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        } else if (apiError.response?.data?.error) {
          errorMessage = apiError.response.data.error;
        } else if (apiError.message) {
          errorMessage = apiError.message;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });

      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h4" className="font-semibold">
          {t('admin:agent.rank.addRank', 'Thêm Cấp Bậc Agent')}
        </Typography>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          disabled={isSubmitting}
          leftIcon={<Icon name="x" size="sm" />}
        >
          {t('common.close', 'Đóng')}
        </Button>
      </div>

      <Form
        schema={createAgentRankSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.rank.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:agent.rank.form.name', 'Tên cấp bậc')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.rank.form.namePlaceholder', 'Nhập tên cấp bậc')}
              />
            </FormItem>

            <FormItem
              name="description"
              label={t('admin:agent.rank.form.description', 'Mô tả')}
              required
            >
              <Textarea
                fullWidth
                rows={3}
                placeholder={t('admin:agent.rank.form.descriptionPlaceholder', 'Nhập mô tả cấp bậc')}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Experience Range */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.rank.form.expRange', 'Khoảng kinh nghiệm')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem
              name="minExp"
              label={t('admin:agent.rank.form.minExp', 'Kinh nghiệm tối thiểu')}
              required
            >
              <Input
                type="number"
                fullWidth
                min={0}
                placeholder="0"
              />
            </FormItem>

            <FormItem
              name="maxExp"
              label={t('admin:agent.rank.form.maxExp', 'Kinh nghiệm tối đa')}
              required
            >
              <Input
                type="number"
                fullWidth
                min={1}
                placeholder="100"
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Badge Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.rank.form.badge', 'Huy hiệu')}
          </Typography>

          <MultiFileUpload
            label={t('admin:agent.rank.form.badgeUpload', 'Tải lên huy hiệu')}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.rank.form.badgeHelp', 'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)')}
            value={badgeFiles}
            onChange={handleBadgeChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        {/* Status */}
        <div className="space-y-4">
          <FormItem name="active">
            <Checkbox
              label={t('admin:agent.rank.form.active', 'Kích hoạt')}
              variant="filled"
            />
          </FormItem>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
          >
            {t('admin:agent.rank.form.create', 'Tạo Cấp Bậc')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AddAgentRankForm;
