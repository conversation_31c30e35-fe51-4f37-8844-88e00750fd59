import React from 'react';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentCard from './AdminAgentCard';
import { AgentSystemListItem } from '../agent-system/types/agent-system.types';

interface AdminAgentGridProps {
  agents: AgentSystemListItem[];
  onEditAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị danh sách Admin Agents dưới dạng grid
 */
const AdminAgentGrid: React.FC<AdminAgentGridProps> = ({ agents, onEditAgent }) => {
  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {agents.map(agent => (
        <div key={agent.id} className="h-full">
          <AdminAgentCard
            agent={agent}
            allAgents={agents}
            {...(onEditAgent && { onEditAgent })}
          />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

export default AdminAgentGrid;
