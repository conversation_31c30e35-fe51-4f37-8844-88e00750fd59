import React, { useState } from 'react';
import { Card, Icon<PERSON>ard, Tooltip, Chip, Modal, Button, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { AgentRankListItem } from '../agent-rank/types/agent-rank.types';
import { useDeleteAdminAgentRank } from '../agent-rank/hooks/useAgentRank';
import { NotificationUtil } from '@/shared/utils/notification';

interface AdminAgentRankCardProps {
  rank: AgentRankListItem;
  /** Callback khi click edit - truyền toàn bộ dữ liệu rank thay vì chỉ ID */
  onEditRank?: (rank: AgentRankListItem) => void;
}

/**
 * Component hiển thị thông tin của một Agent Rank
 */
const AdminAgentRankCard: React.FC<AdminAgentRankCardProps> = ({
  rank,
  onEditRank
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Hooks cho API calls
  const deleteRankMutation = useDeleteAdminAgentRank();

  const handleEditRank = () => {
    if (onEditRank) {
      onEditRank(rank);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteRankMutation.mutateAsync(rank.id);
      setShowDeleteModal(false);

      NotificationUtil.success({
        message: t('admin:agent.rank.deleteSuccess', 'Xóa cấp bậc thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting agent rank:', error);
      NotificationUtil.error({
        message: t('admin:agent.rank.deleteError', 'Có lỗi xảy ra khi xóa cấp bậc'),
        duration: 5000,
      });
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Xác định variant cho exp range chip
  const getExpRangeVariant = (): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    if (rank.minExp === 0) return 'success'; // Beginner
    if (rank.maxExp >= 1000) return 'danger'; // Expert
    if (rank.maxExp >= 500) return 'warning'; // Advanced
    return 'info'; // Intermediate
  };

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4">
          <div className="flex flex-col space-y-4">
            {/* Hàng 1: Badge, tên, và trạng thái */}
            <div className="flex items-center gap-3 overflow-hidden">
              {/* Badge */}
              <div className="flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20">
                <div className="w-full h-full relative">
                  <img
                    src={rank.badge || '/assets/images/default-badge.png'}
                    alt={rank.name}
                    className="w-full h-full rounded-full object-cover border-2 border-gray-200 dark:border-gray-700"
                  />
                  {/* Chỉ báo trạng thái active */}
                  <div
                    className={`absolute bottom-1 right-1 w-3 h-3 rounded-full z-20 ${
                      rank.active ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  />
                </div>
              </div>

              {/* Thông tin rank: tên và mô tả */}
              <div className="flex flex-col min-w-0 flex-grow">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                  <div className="min-w-0">
                    <Typography variant="h5" className="font-semibold text-foreground truncate">
                      {rank.name}
                    </Typography>
                    <div className="text-xs text-muted-foreground line-clamp-2">
                      {rank.description || t('common.noDescription', 'Không có mô tả')}
                    </div>
                  </div>
                  <div className="flex-shrink-0 mt-1 sm:mt-0">
                    <Chip
                      variant={getExpRangeVariant()}
                      size="sm"
                      className="font-normal"
                    >
                      {rank.minExp} - {rank.maxExp} EXP
                    </Chip>
                  </div>
                </div>
              </div>
            </div>

            {/* Hàng 2: Các nút chức năng */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Chip
                  variant={rank.active ? 'success' : 'default'}
                  size="sm"
                >
                  {rank.active ? t('admin:agent.rank.active', 'Hoạt động') : t('admin:agent.rank.inactive', 'Không hoạt động')}
                </Chip>
              </div>

              <div className="flex space-x-2">
                <Tooltip content={t('admin:agent.rank.editAction', 'Chỉnh sửa')} position="top">
                  <IconCard icon="edit" variant="default" size="md" onClick={handleEditRank} />
                </Tooltip>
                <Tooltip content={t('admin:agent.rank.deleteAction', 'Xóa')} position="top">
                  <IconCard
                    icon="trash"
                    variant="default"
                    size="md"
                    onClick={handleDeleteClick}
                    className="text-red-500 hover:text-red-600"
                    disabled={deleteRankMutation.isPending}
                  />
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('admin:agent.rank.confirmDelete', 'Xác nhận xóa cấp bậc')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={handleDeleteCancel}
              disabled={deleteRankMutation.isPending}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              isLoading={deleteRankMutation.isPending}
            >
              {t('admin:agent.rank.deleteAction')}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <p className="text-muted-foreground">
            {t('admin:agent.rank.deleteMessage', 'Bạn có chắc chắn muốn xóa cấp bậc này không? Hành động này không thể hoàn tác.')}
          </p>

          <div className="bg-muted p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={rank.badge || '/assets/images/default-badge.png'}
                alt={rank.name}
                className="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <p className="font-medium text-foreground">{rank.name}</p>
                <p className="text-sm text-muted-foreground">{rank.minExp} - {rank.maxExp} EXP</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AdminAgentRankCard;
