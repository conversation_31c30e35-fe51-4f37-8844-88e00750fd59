import React from 'react';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import { AgentRankListItem } from '../agent-rank/types/agent-rank.types';
import AdminAgentRankCard from './AdminAgentRankCard';

interface AdminAgentRankGridProps {
  /** Danh sách agent ranks */
  ranks: AgentRankListItem[];
  /** Callback khi click edit rank - truyền toàn bộ dữ liệu rank */
  onEditRank?: (rank: AgentRankListItem) => void;
}

/**
 * Component hiển thị grid các Agent Rank cards
 */
const AdminAgentRankGrid: React.FC<AdminAgentRankGridProps> = ({
  ranks,
  onEditRank
}) => {
  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {ranks.map(rank => (
        <div key={rank.id} className="h-full">
          <AdminAgentRankCard
            rank={rank}
            {...(onEditRank && { onEditRank })}
          />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

export default AdminAgentRankGrid;
