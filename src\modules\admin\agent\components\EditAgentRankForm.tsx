import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Card,
  FormGrid,
  Divider,
  Icon,
  Checkbox,
  Loading,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { UpdateAgentRankParams, AgentRankListItem } from '../agent-rank/types/agent-rank.types';
import { useAdminAgentRankDetail } from '../agent-rank/hooks/useAgentRank';
import { NotificationUtil } from '@/shared/utils/notification';

// Types
type UpdateAgentRankDto = UpdateAgentRankParams;

interface UpdateAgentRankResponse {
  result?: {
    id: number;
    name: string;
    description: string;
    badge: string;
    minExp: number;
    maxExp: number;
    active: boolean;
    uploadUrl: string;
  };
}

interface EditAgentRankFormProps {
  rankId: number;
  rankData?: AgentRankListItem; // Thêm prop để truyền dữ liệu rank từ bên ngoài
  onSubmit: (values: UpdateAgentRankDto) => Promise<UpdateAgentRankResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateAgentRankSchema = (t: any) => z.object({
  name: z.string()
    .min(1, t('admin:agent.rank.validation.nameRequired', 'Tên cấp bậc là bắt buộc'))
    .trim(),
  description: z.string()
    .min(1, t('admin:agent.rank.validation.descriptionRequired', 'Mô tả là bắt buộc'))
    .trim(),
  minExp: z.coerce.number()
    .min(0, t('admin:agent.rank.validation.minExpInvalid', 'Kinh nghiệm tối thiểu phải >= 0'))
    .int(t('admin:agent.rank.validation.minExpInteger', 'Kinh nghiệm tối thiểu phải là số nguyên')),
  maxExp: z.coerce.number()
    .min(1, t('admin:agent.rank.validation.maxExpInvalid', 'Kinh nghiệm tối đa phải > 0'))
    .int(t('admin:agent.rank.validation.maxExpInteger', 'Kinh nghiệm tối đa phải là số nguyên')),
  active: z.boolean().optional(),
}).refine((data) => data.maxExp > data.minExp, {
  message: t('admin:agent.rank.validation.expRangeInvalid', 'Kinh nghiệm tối đa phải lớn hơn kinh nghiệm tối thiểu'),
  path: ['maxExp'],
});

const EditAgentRankForm: React.FC<EditAgentRankFormProps> = ({
  rankId,
  rankData: propRankData,
  onSubmit,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Badge upload states
  const [badgeFiles, setBadgeFiles] = useState<FileWithMetadata[]>([]);

  // Load rank detail chỉ khi không có propRankData
  const { data: apiRankData, isLoading, error } = useAdminAgentRankDetail(rankId, {
    enabled: !propRankData && !!rankId, // Chỉ gọi API khi không có dữ liệu từ props và có rankId
  });

  // Sử dụng dữ liệu từ props hoặc từ API
  const rankData = propRankData || apiRankData;

  // Default values for the form - sử dụng rankData nếu có
  const defaultValues = React.useMemo(() => ({
    name: rankData?.name || '',
    description: rankData?.description || '',
    minExp: rankData?.minExp || 0,
    maxExp: rankData?.maxExp || 100,
    active: rankData?.active ?? true,
  }), [rankData]);

  // Handle badge file selection
  const handleBadgeChange = useCallback((files: FileWithMetadata[]) => {
    if (files.length > 0 && files[0]) {
      setBadgeFiles([files[0]]);
    } else {
      setBadgeFiles([]);
    }
  }, []);

  // Upload image file to S3 using presigned URL
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    if (!response.ok) {
      throw new Error(`Failed to upload image: ${response.status} ${response.statusText}`);
    }

    return response;
  };

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    setIsSubmitting(true);

    try {
      // Prepare form data
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateData: any = {
        name: values['name'] as string,
        description: values['description'] as string,
        minExp: values['minExp'] as number,
        maxExp: values['maxExp'] as number,
        active: values['active'] as boolean,
      };

      // Chỉ thêm fileName nếu có file mới được upload
      if (badgeFiles.length > 0 && badgeFiles[0]) {
        updateData.badge = badgeFiles[0].file.name;
      }

      console.log('Updating rank data:', updateData);

      // Submit form data
      const updateResult = await onSubmit(updateData);
      console.log('Rank updated successfully:', updateResult);

      // Upload badge if provided - sử dụng badge URL từ response để tạo presigned URL
      const allUploadPromises: Promise<void>[] = [];

      if (badgeFiles.length > 0 && updateResult.result?.badge) {
        console.log('🔍 Starting badge upload...');
        console.log('🔍 Badge URL from response:', updateResult.result.badge);

        const badgeFile = badgeFiles[0]?.file;
        // Sử dụng badge URL từ response làm presigned URL để upload
        const uploadUrl = updateResult.result.badge;

        const badgeUploadPromise = (async () => {
          if (!badgeFile) {
            throw new Error('Badge file is undefined');
          }
          try {
            await uploadImageFile(badgeFile, uploadUrl);
            console.log('✅ Badge uploaded successfully to S3');
          } catch (error) {
            console.error('❌ Exception uploading badge:', error);
            throw error;
          }
        })();
        allUploadPromises.push(badgeUploadPromise);
      } else if (badgeFiles.length > 0) {
        console.log('⚠️ No badge URL in response, skipping upload');
      }

      // Đợi tất cả uploads hoàn thành
      if (allUploadPromises.length > 0) {
        try {
          await Promise.all(allUploadPromises);
          console.log('🎉 All uploads completed successfully');
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);
          throw new Error(`Upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
        }
      }

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error updating rank form:', error);

      // Hiển thị thông báo lỗi chi tiết
      let errorMessage = t('admin:agent.rank.form.updateError', 'Có lỗi xảy ra khi cập nhật cấp bậc');

      // Kiểm tra nếu là lỗi từ API
      if (error && typeof error === 'object' && 'response' in error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const apiError = error as any;
        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        } else if (apiError.response?.data?.error) {
          errorMessage = apiError.response.data.error;
        } else if (apiError.message) {
          errorMessage = apiError.message;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });

      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Early returns after all hooks
  if (isLoading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </Card>
    );
  }

  if (error || !rankData) {
    return (
      <Card>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted">
            {t('admin:agent.rank.edit.notFound', 'Không tìm thấy cấp bậc')}
          </Typography>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h4" className="font-semibold">
          {t('admin:agent.rank.editRank', 'Chỉnh sửa Cấp Bậc Agent')}
        </Typography>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          disabled={isSubmitting}
          leftIcon={<Icon name="x" size="sm" />}
        >
          {t('common.close', 'Đóng')}
        </Button>
      </div>

      <Form
        schema={updateAgentRankSchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.rank.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:agent.rank.form.name', 'Tên cấp bậc')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.rank.form.namePlaceholder', 'Nhập tên cấp bậc')}
              />
            </FormItem>

            <FormItem
              name="description"
              label={t('admin:agent.rank.form.description', 'Mô tả')}
              required
            >
              <Textarea
                fullWidth
                rows={3}
                placeholder={t('admin:agent.rank.form.descriptionPlaceholder', 'Nhập mô tả cấp bậc')}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Experience Range */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.rank.form.expRange', 'Khoảng kinh nghiệm')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem
              name="minExp"
              label={t('admin:agent.rank.form.minExp', 'Kinh nghiệm tối thiểu')}
              required
            >
              <Input
                type="number"
                fullWidth
                min={0}
                placeholder="0"
              />
            </FormItem>

            <FormItem
              name="maxExp"
              label={t('admin:agent.rank.form.maxExp', 'Kinh nghiệm tối đa')}
              required
            >
              <Input
                type="number"
                fullWidth
                min={1}
                placeholder="100"
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Current Badge Display */}
        {rankData.badge && (
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.rank.form.currentBadge', 'Huy hiệu hiện tại')}
            </Typography>
            <div className="flex items-center gap-3">
              <img
                src={rankData.badge}
                alt={rankData.name}
                className="w-16 h-16 rounded-full object-cover border-2 border-border"
              />
              <Typography variant="body2" className="text-muted-foreground">
                {t('admin:agent.rank.form.currentBadgeNote', 'Tải lên ảnh mới để thay thế')}
              </Typography>
            </div>
          </div>
        )}

        {/* Badge Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.rank.form.badge', 'Huy hiệu mới')}
          </Typography>

          <MultiFileUpload
            label={t('admin:agent.rank.form.badgeUpload', 'Tải lên huy hiệu')}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.rank.form.badgeHelp', 'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)')}
            value={badgeFiles}
            onChange={handleBadgeChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        {/* Status */}
        <div className="space-y-4">
          <FormItem name="active">
            <Checkbox
              label={t('admin:agent.rank.form.active', 'Kích hoạt')}
              variant="filled"
            />
          </FormItem>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
          >
            {t('admin:agent.rank.form.update', 'Cập nhật')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditAgentRankForm;
