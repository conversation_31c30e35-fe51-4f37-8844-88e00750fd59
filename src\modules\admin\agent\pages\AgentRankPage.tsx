import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination, SlideInForm } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { AdminAgentRankGrid, AddAgentRankForm, EditAgentRankForm } from '../components';
import { useAdminAgentRanks, useCreateAdminAgentRank, useUpdateAdminAgentRank } from '../agent-rank/hooks/useAgentRank';
import { AgentRankListItem, AgentRankQueryParams, AgentRankSortBy, CreateAgentRankParams, UpdateAgentRankParams } from '../agent-rank/types/agent-rank.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

// Interface cho dữ liệu từ API
interface ApiAgentRankItem {
  id: number;
  name: string;
  description: string;
  badge: string;
  minExp: number;
  maxExp: number;
  active: boolean;
}

// Interface cho response từ API
interface ApiAgentRankResponse {
  items: ApiAgentRankItem[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

/**
 * Trang hiển thị danh sách Agent Ranks
 */
const AgentRankPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const { success } = useSmartNotification();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');
  const [sortBy] = useState<AgentRankSortBy>(AgentRankSortBy.NAME);

  // Form states
  const [isCreateFormVisible, setIsCreateFormVisible] = useState(false);
  const [isEditFormVisible, setIsEditFormVisible] = useState(false);
  const [editingRankId, setEditingRankId] = useState<number | null>(null);
  const [editingRankData, setEditingRankData] = useState<AgentRankListItem | null>(null);

  // Query params
  const queryParams: AgentRankQueryParams = {
    page,
    limit,
    search: search || '',
    sortBy,
  };

  // Debug log query params
  console.log('🔍 [AgentRankPage] Query params:', queryParams);

  // Lấy danh sách agent ranks
  const { data: ranksResponse, isLoading, error, refetch } = useAdminAgentRanks(queryParams);

  // Mutations
  const createRankMutation = useCreateAdminAgentRank();
  const updateRankMutation = useUpdateAdminAgentRank();

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleAddRank = () => {
    setIsCreateFormVisible(true);
  };

  

  // Form handlers
  const hideCreateForm = () => setIsCreateFormVisible(false);
  const hideEditForm = () => {
    setIsEditFormVisible(false);
    setEditingRankId(null);
    setEditingRankData(null);
  };

  const showEditForm = (rank: AgentRankListItem) => {
    setEditingRankId(rank.id);
    setEditingRankData(rank);
    setIsEditFormVisible(true);
  };

  // Handle form submission
  const handleSubmitCreateRank = async (values: CreateAgentRankParams) => {
    try {
      const response = await createRankMutation.mutateAsync(values);
      console.log('🔍 [AgentRankPage] API response:', response);

      // Service trả về response, nhưng AddAgentRankForm expect { result: ... }
      return {
        result: response
      };
    } catch (error) {
      console.error('Error creating agent rank:', error);
      throw error;
    }
  };

  // Handle edit form submission
  const handleSubmitEditRank = async (values: UpdateAgentRankParams) => {
    if (!editingRankId) {
      throw new Error('No rank ID for editing');
    }

    try {
      const response = await updateRankMutation.mutateAsync({
        id: editingRankId,
        data: values
      });

      console.log('🔍 [AgentRankPage] Edit API response:', response);

      // Return in the format expected by EditAgentRankForm
      return {
        result: {
          id: editingRankId,
          name: values.name || editingRankData?.name || '',
          description: values.description || editingRankData?.description || '',
          badge: response.uploadUrl || editingRankData?.badge || '',
          minExp: values.minExp || editingRankData?.minExp || 0,
          maxExp: values.maxExp || editingRankData?.maxExp || 100,
          active: values.active ?? editingRankData?.active ?? true,
          uploadUrl: response.uploadUrl || ''
        }
      };
    } catch (error) {
      console.error('Error updating agent rank:', error);
      throw error;
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Transform dữ liệu từ API thành format phù hợp với component
  const ranks = useMemo(() => {
    const response = ranksResponse as ApiAgentRankResponse | undefined;
    if (!response?.items) {
      return [];
    }

    const apiRanks = response.items;

    // Debug log để kiểm tra dữ liệu từ API
    console.log('🔍 [AgentRankPage] API Response:', apiRanks);

    return apiRanks.map((apiRank: ApiAgentRankItem): AgentRankListItem => {
      const mappedRank = {
        id: apiRank.id,
        name: apiRank.name,
        description: apiRank.description,
        badge: apiRank.badge,
        minExp: apiRank.minExp,
        maxExp: apiRank.maxExp,
        active: apiRank.active,
      };

      // Debug log cho từng rank
      console.log('🔍 [AgentRankPage] Mapped rank:', {
        id: mappedRank.id,
        name: mappedRank.name,
        active: mappedRank.active,
        expRange: `${mappedRank.minExp}-${mappedRank.maxExp}`
      });

      return mappedRank;
    });
  }, [ranksResponse]);

  const totalItems = (ranksResponse as ApiAgentRankResponse | undefined)?.meta?.totalItems || 0;

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddRank}
          items={[]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddRank}
          items={[]}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error')}
          description={t('admin:agent.rank.list.loadError', 'Không thể tải danh sách cấp bậc. Vui lòng thử lại.')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddRank}
        items={[]}
      />

      {/* Sort Selector */}
      <div className="mb-4 flex justify-end">
    
      </div>

       {/* SlideInForm for Create Rank */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <AddAgentRankForm
          onSubmit={handleSubmitCreateRank}
          onCancel={hideCreateForm}
          onSuccess={() => {
            hideCreateForm();
            success({
              title: t('admin:agent.rank.createSuccess', 'Thành công'),
              message: t('admin:agent.rank.createSuccessMessage', 'Cấp bậc agent đã được tạo thành công'),
            });
            refetch(); // Refresh the list
          }}
        />
      </SlideInForm>

      {/* SlideInForm for Edit Rank */}
      <SlideInForm isVisible={isEditFormVisible}>
        {editingRankId && (
          <EditAgentRankForm
            rankId={editingRankId}
            {...(editingRankData && { rankData: editingRankData })}
            onSubmit={handleSubmitEditRank}
            onCancel={hideEditForm}
            onSuccess={() => {
              hideEditForm();
              success({
                title: t('admin:agent.rank.updateSuccess', 'Thành công'),
                message: t('admin:agent.rank.updateSuccessMessage', 'Cấp bậc agent đã được cập nhật thành công'),
              });
              refetch(); // Refresh the list
            }}
          />
        )}
      </SlideInForm>

      {ranks.length > 0 ? (
        <>
          <AdminAgentRankGrid ranks={ranks} onEditRank={showEditForm} />

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="award"
          title={t('admin:agent.rank.list.noRanks', 'Không có cấp bậc nào')}
          description={
            search
              ? t('admin:agent.rank.noSearchResults', 'Không tìm thấy cấp bậc phù hợp với từ khóa tìm kiếm.')
              : t('admin:agent.rank.list.noRanksDescription', 'Hiện tại chưa có cấp bậc nào trong hệ thống.')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddRank}
            >
              {t('admin:agent.rank.addRank', 'Thêm Cấp Bậc')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AgentRankPage;
