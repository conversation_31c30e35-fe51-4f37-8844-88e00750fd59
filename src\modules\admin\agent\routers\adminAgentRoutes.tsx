import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Loading } from '@/shared/components/common';

const AgentManagementPage = lazy(() => import('../pages/AgentManagementPage'));
const AgentSystemPage = lazy(() => import('../pages/AgentSystemPage'));
const AgentSystemDeletePage = lazy(() => import('../pages/AgentSystemDeletePage'));
const AgentRankPage = lazy(() => import('../pages/AgentRankPage'));

/**
 * Routes cho module Admin Agent
 */
export const adminAgentRoutes: RouteObject[] = [
  {
    path: '/admin/agent',
    element: (
      <AdminLayout title="Quản lý Agent">
        <Suspense fallback={<Loading />}>
          <AgentManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/system',
    element: (
      <AdminLayout title="Quản lý System Agent">
        <Suspense fallback={<Loading />}>
          <AgentSystemPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/system/trash',
    element: (
      <AdminLayout title="Thùng rác System Agent">
        <Suspense fallback={<Loading />}>
          <AgentSystemDeletePage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/agent/ranks',
    element: (
      <AdminLayout title="Quản lý Agent Rank">
        <Suspense fallback={<Loading />}>
          <AgentRankPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  // TODO: Thêm các route khác
  // {
  //   path: '/admin/agent/users',
  //   element: (
  //     <AdminLayout title="Quản lý User Agent">
  //       <Suspense fallback={<Loading />}>
  //         <AgentUserPage />
  //       </Suspense>
  //     </AdminLayout>
  //   ),
  // },
  // {
  //   path: '/admin/agent/types',
  //   element: (
  //     <AdminLayout title="Quản lý Type Agent">
  //       <Suspense fallback={<Loading />}>
  //         <AgentTypePage />
  //       </Suspense>
  //     </AdminLayout>
  //   ),
  // },
];

export default adminAgentRoutes;