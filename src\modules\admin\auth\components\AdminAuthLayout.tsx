import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { ThemeToggle } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import LanguageSwitcher from '../../../auth/components/LanguageSwitcher';
import { Typography, Icon } from '@/shared/components/common';

interface AdminAuthLayoutProps {
  children: ReactNode;
  title: string;
}

/**
 * Layout component for admin authentication pages
 */
const AdminAuthLayout: React.FC<AdminAuthLayoutProps> = ({ children, title }) => {
  const { t } = useTranslation();
  const { toggleTheme, currentTheme } = useTheme();

  return (
    <>
      <Helmet>
        <title>{title} | RedAI Admin</title>
      </Helmet>
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-800 to-slate-900 dark:from-dark-900 dark:to-dark-800">
        {/* Header with logo */}
        <div className="pt-8 pb-4 px-6 flex justify-center">
          <div className="flex items-center">
            <Icon name="settings" size="lg" className="text-primary mr-2" />
            <Typography variant="h4" className="text-white font-bold">
              {t('admin.header.title', { defaultValue: 'RedAI Admin' })}
            </Typography>
          </div>
        </div>

        {/* Auth content centered in page */}
        <div className="flex-grow flex items-center justify-center p-4">
          <div className="w-full max-w-md dark:bg-dark-800 rounded-lg shadow-xl dark:border-gray-700">
            {children}
          </div>
        </div>

        {/* Footer with language and theme toggles */}
        <div className="py-4 px-6 flex justify-center">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">{t('common.language')}:</span>
              <LanguageSwitcher variant="minimal" />
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">{t('common.theme')}:</span>
              <ThemeToggle theme={currentTheme.mode} onToggle={toggleTheme} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminAuthLayout;
