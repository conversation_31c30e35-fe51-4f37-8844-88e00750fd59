import { useMutation, useQuery } from '@tanstack/react-query';
import { AdminAuthService } from '../services/admin-auth.service';
import {
  AdminLoginRequest,
  AdminForgotPasswordRequest,
  AdminVerifyForgotPasswordRequest,
  AdminResetPasswordRequest,
  AdminResendOtpRequest,
} from '../types/admin-auth.types';

// Định nghĩa các query keys cho admin auth
export const ADMIN_AUTH_QUERY_KEYS = {
  CURRENT_EMPLOYEE: ['admin', 'auth', 'currentEmployee'],
  LOGIN: ['admin', 'auth', 'login'],
  FORGOT_PASSWORD: ['admin', 'auth', 'forgotPassword'],
  VERIFY_FORGOT_PASSWORD: ['admin', 'auth', 'verifyForgotPassword'],
  RESET_PASSWORD: ['admin', 'auth', 'resetPassword'],
  RESEND_OTP: ['admin', 'auth', 'resendOtp'],
};

/**
 * Hook để đăng nhập admin
 */
export const useAdminLogin = () => {
  return useMutation({
    mutationFn: (data: AdminLoginRequest) => AdminAuthService.login(data),
    retry: 0, // Tắt retry cho API đăng nhập
  });
};

/**
 * Hook để lấy thông tin nhân viên hiện tại
 */
export const useCurrentEmployee = () => {
  return useQuery({
    queryKey: ADMIN_AUTH_QUERY_KEYS.CURRENT_EMPLOYEE,
    queryFn: () => AdminAuthService.getCurrentEmployee(),
    // Không tự động gọi API nếu không có token
    enabled: !!localStorage.getItem('admin_token'),
    select: data => data.result,
  });
};

/**
 * Hook để quên mật khẩu admin
 */
export const useAdminForgotPassword = () => {
  return useMutation({
    mutationFn: (data: AdminForgotPasswordRequest) => AdminAuthService.forgotPassword(data),
    retry: 0, // Tắt retry cho API quên mật khẩu
  });
};

/**
 * Hook để xác thực OTP quên mật khẩu admin
 */
export const useAdminVerifyForgotPassword = () => {
  return useMutation({
    mutationFn: (data: AdminVerifyForgotPasswordRequest) =>
      AdminAuthService.verifyForgotPassword(data),
    retry: 0, // Tắt retry cho API xác thực OTP
  });
};

/**
 * Hook để đặt lại mật khẩu admin
 */
export const useAdminResetPassword = () => {
  return useMutation({
    mutationFn: (data: AdminResetPasswordRequest) => AdminAuthService.resetPassword(data),
    retry: 0, // Tắt retry cho API đặt lại mật khẩu
  });
};

/**
 * Hook để gửi lại OTP
 */
export const useAdminResendOtp = () => {
  return useMutation({
    mutationFn: (data: AdminResendOtpRequest) => AdminAuthService.resendOtp(data),
    retry: 0, // Tắt retry cho API gửi lại OTP
  });
};

/**
 * Hook để đăng xuất admin
 */
export const useAdminLogout = () => {
  return useMutation({
    mutationFn: () => AdminAuthService.logout(),
    retry: 0, // Tắt retry cho API đăng xuất
  });
};
