import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, ResponsiveImage } from '@/shared/components/common';
import AdminLoginForm from '../components/AdminLoginForm';
// Import logo
import logoImage from '@/shared/assets/images/logo/logo.png';

/**
 * Admin login page
 */
const AdminLoginPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // <PERSON>le forgot password button click - navigate to forgot password page
  const handleForgotPassword = () => {
    navigate('/admin/auth/forgot-password');
  };

  return (
    <Card variant="elevated" className="w-full max-w-md p-8">
      <div className="flex flex-col items-center mb-8">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-16">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-full object-contain max-w-[70%]"
          />
        </div>
      </div>

      <div className="mb-6">
        <div className="text-center mb-6">
          <Typography variant="h5" className="font-bold mb-2">
            {t('auth:admin.login')}
          </Typography>
          <Typography variant="body2" color="muted">
            {t('auth:admin.accessMessage')}
          </Typography>
        </div>

        <div className="space-y-6">
          <AdminLoginForm onForgotPassword={handleForgotPassword} />
        </div>
      </div>
    </Card>
  );
};

export default AdminLoginPage;
