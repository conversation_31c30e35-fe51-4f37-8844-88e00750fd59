import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Form, FormItem, Input, Button, Icon, Alert, Typography } from '@/shared/components/common';
import { createResetPasswordSchema } from '../../../auth/schemas/auth.schema';
import { useResetPassword } from '../../../auth/hooks/useAuthQuery';

/**
 * Admin reset password page
 */
const AdminResetPasswordPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { mutate: resetPassword, isPending } = useResetPassword();

  // Lấy resetToken từ localStorage
  const resetToken = localStorage.getItem('resetToken');

  // Kiểm tra xem có resetToken không
  useEffect(() => {
    if (!resetToken) {
      navigate('/admin/auth/forgot-password');
    }
  }, [resetToken, navigate]);

  // Create reset password schema with translations
  const resetPasswordSchema = createResetPasswordSchema(t);

  // Handle form submission
  const handleSubmit = (values: Record<string, unknown>) => {
    // Reset error message
    setErrorMessage(null);
    setSuccessMessage(null);

    if (resetToken) {
      resetPassword(
        {
          token: resetToken,
          password: values['password'] as string,
          confirmPassword: values['confirmPassword'] as string,
        },
        {
          onSuccess: () => {
            // Hiển thị thông báo thành công
            setSuccessMessage(t('auth:resetPasswordSuccess'));

            // Xóa resetToken
            localStorage.removeItem('resetToken');

            // Chuyển hướng đến trang đăng nhập sau 3 giây
            setTimeout(() => {
              navigate('/admin/auth');
            }, 3000);
          },
          onError: (error: unknown) => {
            console.error('Reset password error:', error);

            let errorMsg = t('auth:resetPasswordError');

            if (error && typeof error === 'object' && 'response' in error && error.response) {
              const axiosError = error as { response: { data?: { message?: string } } };
              if (axiosError.response.data?.message) {
                errorMsg = axiosError.response.data.message;
              }
            }

            setErrorMessage(errorMsg);
          },
        }
      );
    }
  };

  return (
    <div className="w-full max-w-md">
      <div className="text-center mb-6">
        <Typography variant="h5" className="font-bold mb-2">
          {t('auth:resetPassword')}
        </Typography>
        <Typography variant="body2" color="muted">
          {t('auth:newPasswordDescription')}
        </Typography>
      </div>

      {successMessage ? (
        <div className="space-y-4">
          <Alert type="success" message={successMessage} className="mb-4" />
          <Typography variant="body2" color="muted" className="text-center">
            {t('auth:redirecting')}
          </Typography>
        </div>
      ) : (
        <Form schema={resetPasswordSchema} onSubmit={handleSubmit} className="space-y-4">
          <FormItem name="password" label={t('auth:newPassword')} required>
            <Input
              type="password"
              placeholder="••••••••"
              leftIcon={<Icon name="settings" size="sm" />}
              fullWidth
            />
          </FormItem>

          <FormItem name="confirmPassword" label={t('auth:confirmPassword')} required>
            <Input
              type="password"
              placeholder="••••••••"
              leftIcon={<Icon name="settings" size="sm" />}
              fullWidth
            />
          </FormItem>

          <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {t('auth:passwordRequirements')}
          </div>

          {errorMessage && <Alert type="error" message={errorMessage} className="mb-4" />}

          <Button type="submit" variant="primary" fullWidth isLoading={isPending}>
            {t('auth:resetPassword')}
          </Button>
        </Form>
      )}
    </div>
  );
};

export default AdminResetPasswordPage;
