import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components';
import AuthLayout from '@/shared/layouts/AuthLayout';
import i18n from '@/lib/i18n';

// Import Admin Auth pages with lazy loading
const AdminLoginPage = lazy(() => import('@/modules/admin/auth/pages/AdminLoginPage'));
const AdminForgotPasswordPage = lazy(
  () => import('@/modules/admin/auth/pages/AdminForgotPasswordPage')
);
const AdminVerifyForgotPasswordPage = lazy(
  () => import('@/modules/admin/auth/pages/AdminVerifyForgotPasswordPage')
);
const AdminResetPasswordPage = lazy(
  () => import('@/modules/admin/auth/pages/AdminResetPasswordPage')
);

/**
 * Auth module routes
 */
const authAdminRouters: RouteObject[] = [
  // Admin Auth Routes
  {
    path: '/admin/auth',
    element: (
      <AuthLayout title={i18n.t('auth:admin.login')}>
        <Suspense fallback={<Loading />}>
          <AdminLoginPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/admin/auth/forgot-password',
    element: (
      <AuthLayout title={i18n.t('auth:admin.forgotPassword')}>
        <Suspense fallback={<Loading />}>
          <AdminForgotPasswordPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/admin/auth/verify-forgot-password',
    element: (
      <AuthLayout title={i18n.t('auth:admin.verifyPasswordReset')}>
        <Suspense fallback={<Loading />}>
          <AdminVerifyForgotPasswordPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/admin/auth/reset-password',
    element: (
      <AuthLayout title={i18n.t('auth:admin.resetPassword')}>
        <Suspense fallback={<Loading />}>
          <AdminResetPasswordPage />
        </Suspense>
      </AuthLayout>
    ),
  },
];

export default authAdminRouters;
