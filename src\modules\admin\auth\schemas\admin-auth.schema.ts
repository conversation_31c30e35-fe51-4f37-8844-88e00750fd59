import { z } from 'zod';
import { TFunction } from 'i18next';

/**
 * Admin login form values interface
 */
export interface AdminLoginFormValues {
  email: string;
  password: string;
  rememberMe: boolean;
  recaptchaToken?: string;
}

/**
 * Admin forgot password form values interface
 */
export interface AdminForgotPasswordFormValues {
  email: string;
}

/**
 * Admin reset password form values interface
 */
export interface AdminResetPasswordFormValues {
  password: string;
  confirmPassword: string;
}

/**
 * Create admin login schema with translations
 */
export const createAdminLoginSchema = (t: TFunction) => {
  return z.object({
    email: z
      .string()
      .min(1, t('adminValidation:required', { field: t('auth:email') }))
      .email(t('adminValidation:email')),
    password: z
      .string()
      .min(1, t('adminValidation:required', { field: t('auth:password') }))
      .min(6, t('adminValidation:minLength', { field: t('auth:password'), length: 6 })),
    rememberMe: z.boolean().optional(),
    recaptchaToken: z.string().optional(),
  });
};

/**
 * Create admin forgot password schema with translations
 */
export const createAdminForgotPasswordSchema = (t: TFunction) => {
  return z.object({
    email: z
      .string()
      .min(1, t('adminValidation:required', { field: t('auth:email') }))
      .email(t('adminValidation:email')),
  });
};

/**
 * Create admin reset password schema with translations
 */
export const createAdminResetPasswordSchema = (t: TFunction) => {
  return z
    .object({
      password: z
        .string()
        .min(1, t('adminValidation:required', { field: t('auth:newPassword') }))
        .min(8, t('adminValidation:minLength', { field: t('auth:newPassword'), length: 8 }))
        .regex(/[A-Z]/, t('adminValidation:passwordUppercase'))
        .regex(/[a-z]/, t('adminValidation:passwordLowercase'))
        .regex(/[0-9]/, t('adminValidation:passwordNumber'))
        .regex(/[^A-Za-z0-9]/, t('adminValidation:passwordSpecial')),
      confirmPassword: z
        .string()
        .min(1, t('adminValidation:required', { field: t('auth:confirmPassword') })),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('adminValidation:passwordsDoNotMatch'),
      path: ['confirmPassword'],
    });
};
