import { apiClient } from '@/shared/api';
import {
  AdminLoginRequest,
  AdminLoginResponse,
  AdminForgotPasswordRequest,
  AdminForgotPasswordResponse,
  AdminVerifyForgotPasswordRequest,
  AdminVerifyForgotPasswordResponse,
  AdminResetPasswordRequest,
  AdminResetPasswordResponse,
  AdminResendOtpRequest,
  AdminResendOtpResponse,
  Employee,
} from '../types/admin-auth.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Service cho các chức năng xác thực admin
 */
export const AdminAuthService = {
  /**
   * Đăng nhập admin
   * @param data Dữ liệu đăng nhập
   * @returns Promise với kết quả đăng nhập
   */
  login: (data: AdminLoginRequest) => {
    return apiClient.post<ApiResponseDto<AdminLoginResponse>>('/employees/login', data);
  },

  /**
   * Quên mật khẩu admin
   * @param data Dữ liệu quên mật khẩu
   * @returns Promise với kết quả quên mật khẩu
   */
  forgotPassword: (data: AdminForgotPasswordRequest) => {
    return apiClient.post<ApiResponseDto<AdminForgotPasswordResponse>>(
      '/employees/forgot-password',
      data
    );
  },

  /**
   * Xác thực OTP quên mật khẩu admin
   * @param data Dữ liệu xác thực OTP
   * @returns Promise với kết quả xác thực OTP
   */
  verifyForgotPassword: (data: AdminVerifyForgotPasswordRequest) => {
    return apiClient.post<ApiResponseDto<AdminVerifyForgotPasswordResponse>>(
      '/employees/verify-forgot-password',
      data
    );
  },

  /**
   * Đặt lại mật khẩu admin
   * @param data Dữ liệu đặt lại mật khẩu
   * @returns Promise với kết quả đặt lại mật khẩu
   */
  resetPassword: (data: AdminResetPasswordRequest) => {
    return apiClient.post<ApiResponseDto<AdminResetPasswordResponse>>(
      '/employees/reset-password',
      data
    );
  },

  /**
   * Gửi lại OTP
   * @param data Dữ liệu gửi lại OTP
   * @returns Promise với kết quả gửi lại OTP
   */
  resendOtp: (data: AdminResendOtpRequest) => {
    return apiClient.post<ApiResponseDto<AdminResendOtpResponse>>('/employees/resend-otp', data);
  },

  /**
   * Lấy thông tin nhân viên hiện tại
   * @returns Promise với thông tin nhân viên
   */
  getCurrentEmployee: () => {
    return apiClient.get<ApiResponseDto<Employee>>('/employees/me');
  },

  /**
   * Đăng xuất admin
   * @returns Promise với kết quả đăng xuất
   */
  logout: () => {
    return apiClient.post<ApiResponseDto<{ success: boolean }>>('/employees/logout');
  },
};
