/**
 * Types cho module admin auth
 */

/**
 * Trạng thái nhân viên
 */
export enum EmployeeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
}

/**
 * Thông tin vai trò nhân viên
 */
export interface EmployeeRole {
  id: number;
  name: string;
  code: string;
  description?: string;
}

/**
 * Thông tin quyền hạn
 */
export interface EmployeePermission {
  module: string;
  action: string;
  description?: string;
}

/**
 * Thông tin nhân viên
 */
export interface Employee {
  id: number;
  email: string;
  fullName: string;
  avatar?: string;
  roles: EmployeeRole[];
  permissions: string[];
}

/**
 * Yêu cầu đăng nhập admin
 */
export interface AdminLoginRequest {
  email: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Phản hồi đăng nhập admin
 */
export interface AdminLoginResponse {
  accessToken: string;
  expiresIn: number;
  expiresAt: number;
  employee: {
    id: number;
    email: string;
    fullName: string;
    avatar?: string;
    roles: EmployeeRole[];
    permissions: string[];
  };
}

/**
 * Yêu cầu quên mật khẩu admin
 */
export interface AdminForgotPasswordRequest {
  email: string;
}

/**
 * Phản hồi quên mật khẩu admin
 */
export interface AdminForgotPasswordResponse {
  otpToken: string;
  expiresAt: number;
  maskedEmail: string;
  otp?: string; // Chỉ có trong môi trường development
}

/**
 * Yêu cầu xác thực OTP quên mật khẩu admin
 */
export interface AdminVerifyForgotPasswordRequest {
  otp: string;
  otpToken: string;
}

/**
 * Phản hồi xác thực OTP quên mật khẩu admin
 */
export interface AdminVerifyForgotPasswordResponse {
  changePasswordToken: string;
  expiresAt: number;
}

/**
 * Yêu cầu đặt lại mật khẩu admin
 */
export interface AdminResetPasswordRequest {
  newPassword: string;
  changePasswordToken: string;
}

/**
 * Phản hồi đặt lại mật khẩu admin
 */
export interface AdminResetPasswordResponse {
  success: boolean;
  message?: string;
}

/**
 * Yêu cầu gửi lại OTP
 */
export interface AdminResendOtpRequest {
  email: string;
  otpToken?: string;
}

/**
 * Phản hồi gửi lại OTP
 */
export interface AdminResendOtpResponse {
  otpToken: string;
  expiresAt: number;
  maskedEmail: string;
  otp?: string; // Chỉ có trong môi trường development
}
