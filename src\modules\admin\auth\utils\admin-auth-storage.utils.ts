import { SavedCredentials } from '@/modules/auth/types/auth.types';

const CREDENTIALS_KEY = 'admin_auth_credentials';
const REMEMBER_ME_KEY = 'admin_auth_remember_me';
const CREDENTIALS_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

/**
 * Mã hóa mật khẩu đơn giản bằng Base64
 * Lưu ý: Đây không phải là phương pháp mã hóa an toàn, chỉ để minh họa
 * Trong thực tế, nên sử dụng thư viện mã hóa chuyên dụng
 * @param password Mật khẩu cần mã hóa
 * @returns Mật khẩu đã mã hóa
 */
const encryptPassword = (password: string): string => {
  try {
    if (!password) {
      return '';
    }

    // Mã hóa đơn giản bằng Base64
    return btoa(password);
  } catch (error) {
    console.error('Error encrypting password:', error);
    return '';
  }
};

/**
 * Giải mã mật khẩu đã được mã hóa bằng Base64
 * @param encryptedPassword Mật khẩu đã mã hóa
 * @returns Mật khẩu gốc
 */
const decryptPassword = (encryptedPassword: string): string => {
  try {
    if (!encryptedPassword) {
      return '';
    }

    // Giải mã từ Base64
    return atob(encryptedPassword);
  } catch (error) {
    console.error('Error decrypting password:', error);
    return '';
  }
};

/**
 * Lưu thông tin đăng nhập vào localStorage
 * @param username Tên đăng nhập
 * @param password Mật khẩu
 */
export const saveCredentials = (username: string, password: string): void => {
  try {
    // Mã hóa mật khẩu trước khi lưu
    const encryptedPassword = encryptPassword(password);

    const credentials: SavedCredentials = {
      username,
      encryptedPassword,
      timestamp: Date.now(),
    };

    // Lưu thông tin đăng nhập
    localStorage.setItem(CREDENTIALS_KEY, JSON.stringify(credentials));

    // Lưu trạng thái Remember me
    localStorage.setItem(REMEMBER_ME_KEY, 'true');
  } catch (error) {
    console.error('Error saving credentials:', error);
  }
};

/**
 * Lấy thông tin đăng nhập từ localStorage
 * @returns Thông tin đăng nhập hoặc null nếu không có
 */
export const getCredentials = (): { username: string; password: string } | null => {
  try {
    const credentialsJson = localStorage.getItem(CREDENTIALS_KEY);
    if (!credentialsJson) {
      return null;
    }

    const parsedCredentials = JSON.parse(credentialsJson) as SavedCredentials;
    if (!parsedCredentials || !parsedCredentials.username) {
      return null;
    }

    // Giải mã mật khẩu
    const password = decryptPassword(parsedCredentials.encryptedPassword || '');

    return {
      username: parsedCredentials.username,
      password,
    };
  } catch (error) {
    console.error('Error getting credentials:', error);
    return null;
  }
};

/**
 * Xóa thông tin đăng nhập khỏi localStorage
 */
export const clearCredentials = (): void => {
  try {
    localStorage.removeItem(CREDENTIALS_KEY);
    localStorage.removeItem(REMEMBER_ME_KEY);
  } catch (error) {
    console.error('Error clearing credentials:', error);
  }
};

/**
 * Kiểm tra xem thông tin đăng nhập có hợp lệ không
 * @returns true nếu thông tin đăng nhập hợp lệ, false nếu không
 */
export const areCredentialsValid = (): boolean => {
  try {
    const credentialsJson = localStorage.getItem(CREDENTIALS_KEY);
    if (!credentialsJson) {
      return false;
    }

    const savedCredentialsRaw = JSON.parse(credentialsJson) as SavedCredentials;
    if (!savedCredentialsRaw || !savedCredentialsRaw.timestamp) {
      return false;
    }

    // Kiểm tra xem thông tin đăng nhập đã hết hạn chưa
    const now = Date.now();
    const expiryTime = savedCredentialsRaw.timestamp + CREDENTIALS_EXPIRY;

    return now < expiryTime;
  } catch (error) {
    console.error('Error checking credentials validity:', error);
    return false;
  }
};

/**
 * Kiểm tra xem người dùng đã chọn "Remember me" chưa
 * @returns true nếu đã chọn, false nếu chưa
 */
export const isRememberMeChecked = (): boolean => {
  try {
    return localStorage.getItem(REMEMBER_ME_KEY) === 'true';
  } catch (error) {
    console.error('Error checking remember me state:', error);
    return false;
  }
};
