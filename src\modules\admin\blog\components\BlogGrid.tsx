import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ResponsiveGrid } from '@/shared/components/common';
import { BlogApiItem } from '@/modules/blog/types/blog.types';
import BlogCard from '@/modules/blog/components/BlogCard';

interface BlogGridProps {
  blogs: BlogApiItem[];
}

/**
 * Component hiển thị danh sách blog dạng grid
 * Sử dụng ResponsiveGrid để tự động điều chỉnh số cột dựa trên kích thước màn hình và trạng thái chatpanel
 */
const BlogGrid: React.FC<BlogGridProps> = ({ blogs }) => {
  const navigate = useNavigate();

  // Tạo hàm xử lý sự kiện click tag bên ngoài render để tránh tạo lại hàm mỗi khi render
  const handleTagClick = useCallback((tag: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/blog/tag/${tag}`);
  }, [navigate]);

  // Tạo hàm xử lý sự kiện click blog
  const handleBlogClick = useCallback((blogId: number, e: React.MouseEvent) => {
    e.preventDefault();
    window.location.href = `/admin/blog/detail/${blogId}`;
  }, []);

  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {blogs.map((blog) => (
        <div
          key={blog.id}
          className="block h-full cursor-pointer"
          onClick={(e) => handleBlogClick(blog.id, e)}
        >
          <BlogCard
            blog={blog}
            onTagClick={handleTagClick}
          />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

// Không sử dụng memo để đảm bảo component luôn được render lại khi cần
export default BlogGrid;
