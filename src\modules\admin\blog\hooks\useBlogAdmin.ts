import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { getAdminBlogs, getAdminBlogDetail } from '../services/blog-admin.service';
import {
  BlogDetailAdminApiResponse,
  BlogListAdminApiResponse,
  GetBlogsAdminQueryDto,
} from '../types/blog-admin.types';

// Query keys cho các API liên quan đến Admin Blogs
export const BLOG_ADMIN_QUERY_KEYS = {
  ADMIN_BLOGS: 'adminBlogs',
  ADMIN_BLOG_DETAIL: 'adminBlogDetail',
};

/**
 * Hook để lấy danh sách tất cả bài viết với phân trang và lọc
 * @param params Tham số truy vấn
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetAdminBlogs = (
  params?: GetBlogsAdminQueryDto,
  options?: UseQueryOptions<BlogListAdminApiResponse>
) => {
  return useQuery({
    queryKey: [BLOG_ADMIN_QUERY_KEYS.ADMIN_BLOGS, params],
    queryFn: () => getAdminBlogs(params),
    ...options,
  });
};

/**
 * Hook để lấy chi tiết bài viết theo ID
 * @param id ID của bài viết
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetAdminBlogDetail = (
  id: number | undefined,
  options?: UseQueryOptions<BlogDetailAdminApiResponse>
) => {
  return useQuery({
    queryKey: [BLOG_ADMIN_QUERY_KEYS.ADMIN_BLOG_DETAIL, id],
    queryFn: () => getAdminBlogDetail(id as number),
    enabled: !!id,
    ...options,
  });
};
