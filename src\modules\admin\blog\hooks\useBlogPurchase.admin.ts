import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { 
  BlogPurchaseAdminResponse, 
  GetBlogPurchasesAdminQueryDto 
} from '../types/blog-purchase.admin.types';
import { BLOG_PURCHASE_ADMIN_QUERY_KEYS } from '../constants/blog-purchase.admin.query-keys';
import { getAdminBlogPurchases } from '../services/blog-purchase.admin.service';

/**
 * Hook để lấy danh sách giao dịch mua bài viết (Admin)
 * 
 * @param params Tham số truy vấn
 * @param options TanStack Query options
 * @returns Query result
 * 
 * @example
 * // Sử dụng cơ bản
 * const { data, isLoading, error } = useGetAdminBlogPurchases({ page: 1, limit: 10 });
 * 
 * @example
 * // Sử dụng với options
 * const { data, isLoading, error } = useGetAdminBlogPurchases(
 *   { blog_id: 1 },
 *   { 
 *     enabled: !!blogId,
 *     staleTime: 5 * 60 * 1000 // 5 phút
 *   }
 * );
 */
export const useGetAdminBlogPurchases = (
  params?: GetBlogPurchasesAdminQueryDto,
  options?: UseQueryOptions<BlogPurchaseAdminResponse>
) => {
  return useQuery({
    queryKey: [BLOG_PURCHASE_ADMIN_QUERY_KEYS.BLOG_PURCHASES, params],
    queryFn: () => getAdminBlogPurchases(params),
    ...options,
  });
};
