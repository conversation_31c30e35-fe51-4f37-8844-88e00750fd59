{"title": "Blog Management", "description": "Manage blogs and blog purchases", "list": {"title": "Blog List", "description": "View and manage all blogs", "noResults": "No blogs found."}, "detail": {"title": "Blog Details", "description": "View and manage blog details", "content": "Content", "author": "Author", "createdAt": "Created at", "updatedAt": "Updated at", "status": "Status", "tags": "Tags", "point": "Points", "viewCount": "Views", "like": "<PERSON>s"}, "status": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected", "draft": "Draft"}, "actions": {"approve": "Approve", "reject": "Reject", "delete": "Delete", "edit": "Edit", "view": "View"}, "filters": {"all": "All", "approved": "Approved", "pending": "Pending", "rejected": "Rejected", "draft": "Draft"}, "purchaseManagement": {"title": "Blog Management", "description": "Manage blogs and track blog purchase history.", "purchasedBlogs": {"title": "Blog List", "description": "View the list of all blogs.", "totalBlogs": "Total blogs", "manage": "View list"}, "transactions": {"title": "Transaction List", "description": "View the list of blog purchase transactions.", "totalTransactions": "Total transactions", "manage": "View list"}}, "purchases": {"title": "Blog Purchases", "description": "View and manage blog purchase transactions", "noResults": "No purchases found.", "table": {"id": "ID", "user": "User", "blog": "Blog", "point": "Points", "purchasedAt": "Purchased at", "platformFee": "Platform Fee", "sellerReceive": "<PERSON><PERSON> Receives"}}, "confirmation": {"approve": {"title": "Approve Blog", "message": "Are you sure you want to approve this blog?"}, "reject": {"title": "Reject Blog", "message": "Are you sure you want to reject this blog?"}, "delete": {"title": "Delete Blog", "message": "Are you sure you want to delete this blog?"}}}