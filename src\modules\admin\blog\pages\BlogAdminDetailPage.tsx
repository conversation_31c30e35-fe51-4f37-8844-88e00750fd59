import React, { useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts';
import { useGetAdminBlogDetail } from '../hooks/useBlogAdmin';
import BlogDetail from '@/modules/blog/components/BlogDetail';
import { PurchaseBlogResponse, BlogResponseDto, AuthorType, BlogStatus } from '@/modules/blog/types/blog.types';

/**
 * Trang hiển thị chi tiết blog (Admin)
 */
const BlogAdminDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // State để quản lý trạng thái hiển thị nội dung đầy đủ và mua blog
  const [showFullContent, setShowFullContent] = useState<boolean>(false);
  const [purchaseLoading, setPurchaseLoading] = useState<boolean>(false);

  // Sử dụng hook theme mới
  useTheme();

  // Chuyển đổi id từ string sang number
  const blogId = id ? parseInt(id) : undefined;

  // Sử dụng hook useGetBlogDetail từ useBlogList.ts
  const { data, isLoading, error } = useGetAdminBlogDetail(blogId);

  // Xử lý khi bấm nút Mua ngay
  const purchaseBlog = async (): Promise<PurchaseBlogResponse> => {
    try {
      setPurchaseLoading(true);

      // Giả lập API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Cập nhật trạng thái hiển thị nội dung đầy đủ
      setShowFullContent(true);
      setPurchaseLoading(false);

      return {
        success: true,
        message: 'Mua blog thành công'
      };
    } catch {
      setPurchaseLoading(false);
      return {
        success: false,
        message: 'Có lỗi xảy ra khi mua blog. Vui lòng thử lại sau.'
      };
    }
  };

  // Xử lý khi bấm nút Mua ngay
  const handlePurchase = async () => {
    const result = await purchaseBlog();

    // Hiển thị thông báo thành công hoặc thất bại
    if (result.success) {
      // Sử dụng component Notification trực tiếp
      const notificationElement = document.createElement('div');
      notificationElement.id = 'notification-container';
      document.body.appendChild(notificationElement);

      // Render thông báo thành công
      const successMessage = t('blog.purchaseSuccess', 'Mua blog thành công');
      const successDescription = t(
        'blog.purchaseSuccessDesc',
        'Bạn đã có thể xem nội dung đầy đủ của blog này.'
      );

      // Hiển thị thông báo (giả lập)
      console.log(`${successMessage}: ${successDescription}`);

      // Xóa container sau 5 giây
      setTimeout(() => {
        document.body.removeChild(notificationElement);
      }, 5000);
    } else {
      // Hiển thị thông báo lỗi (giả lập)
      console.error(`Mua blog thất bại: ${result.message}`);
    }
  };

  // Xử lý khi bấm nút Hủy
  const handleCancel = () => {
    navigate('/blog');
  };

  // Chuyển đổi dữ liệu từ BlogDetailAdmin sang BlogResponseDto
  const blog = useMemo(() => {
    if (!data?.result) return null;

    const adminBlog = data.result;

    // Chuyển đổi sang BlogResponseDto
    return {
      id: adminBlog.id,
      title: adminBlog.title,
      content: adminBlog.content,
      point: Number(adminBlog.point),
      viewCount: Number(adminBlog.viewCount),
      thumbnailUrl: adminBlog.thumbnailUrl,
      tags: adminBlog.tags,
      createdAt: Number(adminBlog.createdAt),
      updatedAt: Number(adminBlog.updatedAt),
      userId: adminBlog.userId,
      employeeId: adminBlog.employeeId,
      authorType: adminBlog.authorType as AuthorType,
      author: {
        id: adminBlog.author.id,
        name: adminBlog.author.name,
        type: adminBlog.author.type as AuthorType,
        avatar: adminBlog.author.avatar
      },
      employeeModerator: adminBlog.employeeModerator,
      status: adminBlog.status as BlogStatus,
      enable: adminBlog.enable,
      like: Number(adminBlog.like),
      isPurchased: true // Admin luôn có quyền xem đầy đủ nội dung
    } as BlogResponseDto;
  }, [data?.result]);

  return (
    <div>
      {/* Blog detail */}
      <BlogDetail
        blog={blog}
        loading={isLoading}
        error={error ? 'Không thể tải thông tin blog. Vui lòng thử lại sau.' : null}
        showFullContent={showFullContent}
        onPurchase={handlePurchase}
        onCancel={handleCancel}
        purchaseLoading={purchaseLoading}
      />
    </div>
  );
};

export default BlogAdminDetailPage;
