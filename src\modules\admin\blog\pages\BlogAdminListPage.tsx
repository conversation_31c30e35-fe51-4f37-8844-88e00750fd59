import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Loading, Pagination, Typography } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useGetAdminBlogs } from '../hooks/useBlogAdmin';
import { BlogApiItem, AuthorType } from '@/modules/blog/types/blog.types';
import BlogGrid from '@/modules/admin/blog/components/BlogGrid';

interface BlogAdminListPageProps {
  initialTag?: string;
}

/**
 * Trang hiển thị danh sách blog
 */
const BlogAdminListPage: React.FC<BlogAdminListPageProps> = ({ initialTag }) => {
  const { t } = useTranslation(['blogAdmin', 'common']);
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [searchInput] = useState('');
  const [tag, setTag] = useState<string | undefined>(initialTag);
  const [, setFilterType] = useState('all');

  // Sử dụng hook theme mới
  useTheme();
  // Sử dụng hằng số cho limit vì không cần thay đổi trong phiên bản tối giản
  const limit = 10;

  // Sử dụng TanStack Query để lấy dữ liệu blog
  const { data, isLoading, error } = useGetAdminBlogs({
    page,
    limit,
    search,
    tags: tag || '' // Sử dụng tags thay vì tag để phù hợp với API
  });

  // Xử lý khi thay đổi trang
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleAddBlog = () => {
    console.log('Add new agent');
    // Xử lý logic khi thêm agent mới
  };

  const handleFilterChange = (type: string) => {
    setFilterType(type);
  };

  // Xử lý khi tìm kiếm
  const handleSearch = useCallback(() => {
    setSearch(searchInput);
    setPage(1);
  }, [searchInput]);

  // Không cần hàm formatDate nữa vì chúng ta sử dụng định dạng ngày trực tiếp

  // Reset page when tag changes
  useEffect(() => {
    setPage(1);
  }, [tag]);

  // Update tag when initialTag changes
  useEffect(() => {
    setTag(initialTag);
  }, [initialTag]);

  // Chuyển đổi từ BlogListItemAdmin[] sang BlogApiItem[]
  const convertedBlogs = useMemo(() => {
    if (!data?.result?.content) return [];

    return data.result.content.map(blog => ({
      id: blog.id,
      title: blog.title,
      content: blog.content,
      point: blog.point,
      viewCount: blog.viewCount,
      thumbnailUrl: blog.thumbnailUrl,
      tags: blog.tags,
      createdAt: blog.createdAt,
      updatedAt: blog.updatedAt,
      userId: blog.userId,
      employeeId: blog.employeeId || undefined,
      authorType: blog.authorType as AuthorType,
      author: {
        id: blog.author.id,
        name: blog.author.name,
        type: blog.author.type as AuthorType,
        avatar: blog.author.avatar
      },
      employeeModerator: blog.employeeModerator,
      status: blog.status,
      enable: blog.enable,
      like: blog.like
    } as BlogApiItem));
  }, [data?.result?.content]);

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddBlog}
        items={[
          {
            id: 'all',
            label: t('common:all'),
            icon: 'list',
            onClick: () => handleFilterChange('all'),
          },
          {
            id: 'tag',
            label: t('chat.aiAssistants'),
            icon: 'assistant',
            onClick: () => handleFilterChange('assistant'),
          },
          {
            id: 'agent',
            label: t('chat.specializedAgents', 'Specialized Agents'),
            icon: 'robot',
            onClick: () => handleFilterChange('agent'),
          },
        ]}
      />

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center min-h-[400px]">
          <Loading />
        </div>
      )}

      {/* Blog list */}
      {!isLoading && !error && data?.result && (
        <>
          <BlogGrid blogs={convertedBlogs} />

          {/* Pagination - Simplified and elegant */}
          {data.result.totalItems > 0 && (
            <div className="flex justify-end">
              <Pagination
                variant="compact"
                currentPage={page}
                totalPages={data.result.totalPages}
                onPageChange={handlePageChange}
                showFirstLastButtons={false}
                showItemsPerPageSelector={false}
                showPageInfo={false}
                maxPageButtons={5}
                size="md"
                borderless={true}
              />
            </div>
          )}

          {/* Empty state */}
          {data.result.content.length === 0 && (
            <div className="text-center py-12">
              <Typography variant="body1" color="muted" className="text-lg">
                {t('list.noResults')}
              </Typography>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default BlogAdminListPage;
