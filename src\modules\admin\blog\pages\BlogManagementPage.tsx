import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

interface BlogListPageProps {
  initialTag?: string;
}
/**
 * Trang tổng quan quản lý Blog đã mua
 */
const BlogManagementPage: React.FC<BlogListPageProps> = () => {
  const { t } = useTranslation(['blogAdmin', 'common']);

  return (
    <div>
      <Typography variant="h1" className="mb-6">
        {t('purchaseManagement.title')}
      </Typography>
      <Typography variant="body1" className="mb-8">
        {t('purchaseManagement.description')}
      </Typography>

      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Purchased Blogs Card */}
        <ModuleCard
          title={t('purchaseManagement.purchasedBlogs.title')}
          description={t('purchaseManagement.purchasedBlogs.description')}
          icon="book-open"
          linkTo="/admin/blog/list"
        />
        <ModuleCard
          title={t('purchaseManagement.transactions.title')}
          description={t('purchaseManagement.transactions.description')}
          icon="credit-card"
          linkTo="/admin/blog/purchases"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default BlogManagementPage;
