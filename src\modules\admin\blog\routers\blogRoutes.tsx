import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Loading } from '@/shared/components/common';
import { lazy } from 'react';

const BlogManagementPage = lazy(() => import('../pages/BlogManagementPage'));
const BlogAdminListPage = lazy(() => import('../pages/BlogAdminListPage'));
const BlogAdminDetailPage = lazy(() => import('../pages/BlogAdminDetailPage'));

/**
 * Routes cho module affiliate
 */

export const blogAdminRoutes: RouteObject[] = [
  {
    path: '/admin/blog',
    element: (
      <AdminLayout title="Quản lý Blog">
        <Suspense fallback={<Loading />}>
          <BlogManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/blog/list',
    element: (
      <AdminLayout title="Admin Blog" key="admin-blog-list-layout">
        <Suspense fallback={<Loading />} key="admin-blog-list">
          <BlogAdminListPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: 'admin/blog/detail/:id',
    element: (
      <AdminLayout title="Chi tiết bài viết" key="blog-detail-layout">
        <Suspense fallback={<Loading />} key="blog-detail">
          <BlogAdminDetailPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default blogAdminRoutes;
