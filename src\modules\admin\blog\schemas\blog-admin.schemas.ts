import { z } from 'zod';
import { BlogStatus, AuthorType } from '../types/blog-admin.types';

/**
 * Schema cho thông tin tác giả
 */
export const blogAuthorSchema = z.object({
  id: z.number(),
  name: z.string(),
  type: z.string(),
  avatar: z.string(),
});

/**
 * Schema cho thông tin người kiểm duyệt
 */
export const blogModeratorSchema = z
  .object({
    id: z.number(),
    name: z.string(),
    avatar: z.string().optional(),
  })
  .nullable();

/**
 * Schema cho chi tiết blog (response của API lấy chi tiết blog)
 */
export const blogDetailAdminSchema = z.object({
  id: z.number(),
  title: z.string(),
  content: z.string(),
  contentUploadUrl: z.string(),
  thumbnailUploadUrl: z.string(),
  point: z.string(),
  viewCount: z.string(),
  thumbnailUrl: z.string(),
  tags: z.array(z.string()),
  createdAt: z.string(),
  updatedAt: z.string(),
  userId: z.number(),
  employeeId: z.number().nullable(),
  authorType: z.string(),
  author: blogAuthorSchema,
  employeeModerator: blogModeratorSchema,
  status: z.nativeEnum(BlogStatus),
  enable: z.boolean(),
  like: z.string(),
});

/**
 * Schema cho item trong danh sách blog (response của API lấy danh sách blog)
 */
export const blogListItemAdminSchema = z.object({
  id: z.number(),
  title: z.string(),
  content: z.string(),
  contentUploadUrl: z.string(),
  thumbnailUploadUrl: z.string(),
  point: z.number(),
  viewCount: z.number(),
  thumbnailUrl: z.string(),
  tags: z.array(z.string()),
  createdAt: z.number(),
  updatedAt: z.number(),
  userId: z.number(),
  employeeId: z.number().nullable(),
  authorType: z.string(),
  author: blogAuthorSchema,
  employeeModerator: blogModeratorSchema,
  status: z.nativeEnum(BlogStatus),
  enable: z.boolean(),
  like: z.number(),
});

/**
 * Schema cho response của API lấy danh sách blog
 */
export const blogListResponseAdminSchema = z.object({
  content: z.array(blogListItemAdminSchema),
  totalItems: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalPages: z.number(),
  currentPage: z.number(),
});

/**
 * Schema cho query params của API lấy danh sách blog
 */
export const getBlogsAdminQuerySchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
  status: z.nativeEnum(BlogStatus).optional(),
  authorType: z.nativeEnum(AuthorType).optional(),
  author_type: z.nativeEnum(AuthorType).optional(),
  tags: z.union([z.string(), z.array(z.string())]).optional(),
  search: z.string().optional(),
  sort: z.string().optional().default('createdAt'),
  order: z.enum(['ASC', 'DESC']).optional().default('DESC'),
  userId: z.number().optional(),
  user_id: z.number().optional(),
  employeeId: z.number().optional(),
  employee_id: z.number().optional(),
  titleSearch: z.string().optional(),
});

/**
 * Schema cho API response của danh sách blog
 */
export const blogListAdminApiResponseSchema = z.object({
  code: z.number(),
  message: z.string(),
  result: blogListResponseAdminSchema,
});

/**
 * Schema cho API response của chi tiết blog
 */
export const blogDetailAdminApiResponseSchema = z.object({
  code: z.number(),
  message: z.string(),
  result: blogDetailAdminSchema,
});
