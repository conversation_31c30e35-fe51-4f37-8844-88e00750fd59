import { z } from 'zod';

/**
 * Schema cho thông tin blog trong giao dịch mua
 */
export const BlogPurchaseAdminBlogInfoSchema = z.object({
  id: z.number(),
  title: z.string(),
  authorType: z.string()
});

/**
 * Schema cho thông tin người dùng trong giao dịch mua
 */
export const BlogPurchaseAdminUserInfoSchema = z.object({
  id: z.number(),
  name: z.string()
});

/**
 * Schema cho thông tin giao dịch mua bài viết
 */
export const BlogPurchaseAdminItemSchema = z.object({
  id: z.number(),
  userId: z.number(),
  blogId: z.number(),
  blog: BlogPurchaseAdminBlogInfoSchema,
  user: BlogPurchaseAdminUserInfoSchema,
  point: z.number(),
  purchasedAt: z.number(),
  platformFeePercent: z.number(),
  sellerReceivePrice: z.number()
});

/**
 * Schema cho kết quả phân trang của danh sách giao dịch mua bài viết
 */
export const BlogPurchaseAdminPaginatedResultSchema = z.object({
  content: z.array(BlogPurchaseAdminItemSchema),
  totalItems: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalPages: z.number(),
  currentPage: z.number()
});

/**
 * Schema cho response API của danh sách giao dịch mua bài viết
 */
export const BlogPurchaseAdminResponseSchema = z.object({
  code: z.number(),
  message: z.string(),
  result: BlogPurchaseAdminPaginatedResultSchema
});

/**
 * Schema cho tham số truy vấn của API lấy danh sách giao dịch mua bài viết
 */
export const GetBlogPurchasesAdminQueryDtoSchema = z.object({
  page: z.number().optional(),
  limit: z.number().optional(),
  blog_id: z.number().optional(),
  user_id: z.number().optional(),
  start_date: z.number().optional(),
  end_date: z.number().optional()
});
