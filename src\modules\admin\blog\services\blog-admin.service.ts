import { apiClient } from '@/shared/api';
import {
  BlogDetailAdminApiResponse,
  BlogListAdminApiResponse,
  GetBlogsAdminQueryDto,
} from '../types/blog-admin.types';

const API_BASE_URL = '';
/**
 * <PERSON><PERSON><PERSON> danh sách tất cả bài viết với phân trang và lọc
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getAdminBlogs = async (
  params?: GetBlogsAdminQueryDto
): Promise<BlogListAdminApiResponse> => {
  return apiClient.get(`${API_BASE_URL}/admin/blog`, { params });
};

/**
 * Lấy chi tiết bài viết theo ID
 * @param id ID của bài viết
 * @returns Promise với response từ API
 */
export const getAdminBlogDetail = async (id: number): Promise<BlogDetailAdminApiResponse> => {
  return apiClient.get(`${API_BASE_URL}/admin/blog/${id}`);
};
