import { apiClient } from '@/shared/api';
import { 
  BlogPurchaseAdminResponse, 
  GetBlogPurchasesAdminQueryDto 
} from '../types/blog-purchase.admin.types';

/**
 * Base URL cho API admin blog
 */
const API_BASE_URL = '';

/**
 * <PERSON><PERSON><PERSON> danh sách giao dịch mua bài viết (Admin)
 * 
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 * 
 * @example
 * // Lấy tất cả giao dịch mua bài viết với phân trang
 * const response = await getAdminBlogPurchases({ page: 1, limit: 10 });
 * 
 * @example
 * // Lọc theo ID bài viết và khoảng thời gian
 * const response = await getAdminBlogPurchases({ 
 *   blog_id: 1, 
 *   start_date: 1632474086123,
 *   end_date: 1632574086123
 * });
 */
export const getAdminBlogPurchases = async (
  params?: GetBlogPurchasesAdminQueryDto
): Promise<BlogPurchaseAdminResponse> => {
  return apiClient.get(`${API_BASE_URL}/admin/blog/purchases`, { params });
};
