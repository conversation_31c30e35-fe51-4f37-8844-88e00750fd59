/**
 * Interface cho API response
 */
export interface ApiResponse<T = unknown> {
  code: number;
  message: string;
  result?: T;
}

/**
 * Enum cho trạng thái blog
 */
export enum BlogStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

/**
 * Enum cho loại tác giả
 */
export enum AuthorType {
  USER = 'USER',
  SYSTEM = 'SYSTEM'
}

/**
 * Interface cho thông tin tác giả
 */
export interface BlogAuthor {
  id: number;
  name: string;
  type: string;
  avatar: string;
}

/**
 * Interface cho thông tin người kiểm duyệt
 */
export interface BlogModerator {
  id: number;
  name: string;
  avatar?: string;
}

/**
 * Interface cho chi tiết blog (response của API lấy chi tiết blog)
 */
export interface BlogDetailAdmin {
  id: number;
  title: string;
  content: string;
  contentUploadUrl: string;
  thumbnailUploadUrl: string;
  point: string;
  viewCount: string;
  thumbnailUrl: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  userId: number;
  employeeId: number | null;
  authorType: string;
  author: BlogAuthor;
  employeeModerator: BlogModerator | null;
  status: BlogStatus;
  enable: boolean;
  like: string;
}

/**
 * Interface cho item trong danh sách blog (response của API lấy danh sách blog)
 */
export interface BlogListItemAdmin {
  id: number;
  title: string;
  content: string;
  contentUploadUrl: string;
  thumbnailUploadUrl: string;
  point: number;
  viewCount: number;
  thumbnailUrl: string;
  tags: string[];
  createdAt: number;
  updatedAt: number;
  userId: number;
  employeeId: number | null;
  authorType: string;
  author: BlogAuthor;
  employeeModerator: BlogModerator | null;
  status: BlogStatus;
  enable: boolean;
  like: number;
}

/**
 * Interface cho response của API lấy danh sách blog
 */
export interface BlogListResponseAdmin {
  content: BlogListItemAdmin[];
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Interface cho query params của API lấy danh sách blog
 */
export interface GetBlogsAdminQueryDto {
  page?: number;
  limit?: number;
  status?: BlogStatus;
  authorType?: AuthorType;
  author_type?: AuthorType;
  tags?: string | string[];
  search?: string;
  sort?: string;
  order?: 'ASC' | 'DESC';
  userId?: number;
  user_id?: number;
  employeeId?: number;
  employee_id?: number;
  titleSearch?: string;
}

/**
 * Interface cho API response của danh sách blog
 */
export type BlogListAdminApiResponse = ApiResponse<BlogListResponseAdmin>;

/**
 * Interface cho API response của chi tiết blog
 */
export type BlogDetailAdminApiResponse = ApiResponse<BlogDetailAdmin>;
