
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Thông tin blog trong giao dịch mua
 */
export interface BlogPurchaseAdminBlogInfo {
  /**
   * ID của bài viết
   */
  id: number;

  /**
   * Tiêu đề bài viết
   */
  title: string;

  /**
   * Loại tác giả (USER, EMPLOYEE, SYSTEM)
   */
  authorType: string;
}

/**
 * Thông tin người dùng trong giao dịch mua
 */
export interface BlogPurchaseAdminUserInfo {
  /**
   * ID của người dùng
   */
  id: number;

  /**
   * Tên người dùng
   */
  name: string;
}

/**
 * Thông tin giao dịch mua bài viết
 */
export interface BlogPurchaseAdminItem {
  /**
   * ID của giao dịch
   */
  id: number;

  /**
   * ID của người dùng mua bài viết
   */
  userId: number;

  /**
   * ID của bài viết được mua
   */
  blogId: number;

  /**
   * Thông tin bài viết
   */
  blog: BlogPurchaseAdminBlogInfo;

  /**
   * Thông tin người dùng
   */
  user: BlogPurchaseAdminUserInfo;

  /**
   * Số điểm đã trả
   */
  point: number;

  /**
   * Thời gian mua (timestamp)
   */
  purchasedAt: number;

  /**
   * Phần trăm phí nền tảng
   */
  platformFeePercent: number;

  /**
   * Số điểm người bán nhận được
   */
  sellerReceivePrice: number;
}

/**
 * Kết quả phân trang cho danh sách giao dịch mua bài viết
 */
export type BlogPurchaseAdminPaginatedResult = PaginatedResult<BlogPurchaseAdminItem>

/**
 * Response API cho danh sách giao dịch mua bài viết
 */
export type BlogPurchaseAdminResponse = ApiResponse<BlogPurchaseAdminPaginatedResult>;

/**
 * Tham số truy vấn cho API lấy danh sách giao dịch mua bài viết
 */
export interface GetBlogPurchasesAdminQueryDto {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng bản ghi trên mỗi trang
   */
  limit?: number;

  /**
   * Lọc theo ID của bài viết
   */
  blog_id?: number;

  /**
   * Lọc theo ID của người dùng
   */
  user_id?: number;

  /**
   * Timestamp bắt đầu
   */
  start_date?: number;

  /**
   * Timestamp kết thúc
   */
  end_date?: number;
}
