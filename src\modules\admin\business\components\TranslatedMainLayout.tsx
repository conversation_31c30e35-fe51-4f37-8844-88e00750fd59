import React from 'react';
import { useTranslation } from 'react-i18next';
import AdminLayout from '@/shared/layouts/AdminLayout';

interface TranslatedMainLayoutProps {
  children: React.ReactNode;
  titleKey: string;
}

/**
 * Component wrapper để sử dụng translation cho admin business
 */
const TranslatedMainLayout: React.FC<TranslatedMainLayoutProps> = ({ children, titleKey }) => {
  const { t } = useTranslation(['admin']);
  return (
    <AdminLayout title={t(`admin:business.routes.${titleKey}`)}>
      {children}
    </AdminLayout>
  );
};

export default TranslatedMainLayout;
