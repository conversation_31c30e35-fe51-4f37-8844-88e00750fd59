import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Loading } from '@/shared/components/common';
import { useCustomField } from '../../hooks/useCustomFieldQuery';
import CustomFieldForm from './CustomFieldForm';

interface CustomFieldDetailFormProps {
  id: number;
  onSubmit: () => void;
  onCancel: () => void;
}

/**
 * Form chỉnh sửa trường tùy chỉnh với dữ liệu được tải từ API
 */
const CustomFieldDetailForm: React.FC<CustomFieldDetailFormProps> = ({ id, onSubmit, onCancel }) => {
  const { t } = useTranslation(['business', 'common']);
  const { data: customFieldData, isLoading, error } = useCustomField(id);

  if (isLoading) {
    return (
      <Card title={t('business:customField.edit')}>
        <div className="flex justify-center items-center p-8">
          <Loading size="lg" />
        </div>
      </Card>
    );
  }

  if (error || !customFieldData) {
    return (
      <Card title={t('business:customField.edit')}>
        <div className="p-4 text-center text-red-500">
          {t('business:customField.loadError')}
        </div>
      </Card>
    );
  }

  return (
    <CustomFieldForm
      initialData={customFieldData}
      onSubmit={onSubmit}
      onCancel={onCancel}
    />
  );
};

export default CustomFieldDetailForm;
