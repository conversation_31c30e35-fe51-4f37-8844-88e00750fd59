import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  IconCard,
  Select,
  Card,
  Textarea,
  ConditionalField,
  DatePicker,
  Checkbox,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { z } from 'zod';
import { useCreateCustomField, useUpdateCustomField } from '../../hooks/useCustomFieldQuery';
import { CustomFieldDetail, CreateCustomFieldData } from '../../services/custom-field.service';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

interface CustomFieldFormProps {
  onSubmit: () => void;
  onCancel: () => void;
  initialData?: CustomFieldDetail;
  isSubmitting?: boolean;
}

// Định nghĩa các pattern phổ biến với key để đa ngôn ngữ
const COMMON_PATTERN_KEYS = [
  { key: 'email', value: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' },
  { key: 'phoneVN', value: '^(\\+84|0)[0-9]{9,10}$' },
  { key: 'phoneIntl', value: '^\\+[1-9]\\d{1,14}$' },
  { key: 'postalCodeVN', value: '^[0-9]{5,6}$' },
  { key: 'lettersOnly', value: '^[a-zA-Z]+$' },
  { key: 'numbersOnly', value: '^[0-9]+$' },
  { key: 'alphanumeric', value: '^[a-zA-Z0-9]+$' },
  { key: 'noSpecialChars', value: '^[a-zA-Z0-9\\s]+$' },
  { key: 'url', value: '^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$' },
  { key: 'ipv4', value: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$' },
  { key: 'strongPassword', value: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$' },
  { key: 'vietnameseName', value: '^[a-zA-ZÀ-ỹ\\s]+$' },
  { key: 'studentId', value: '^[A-Z]{2}[0-9]{6}$' },
  { key: 'nationalId', value: '^[0-9]{9,12}$' },
  { key: 'taxCode', value: '^[0-9]{10,13}$' },
  { key: 'dateFormat', value: '^(0[1-9]|[12][0-9]|3[01])\\/(0[1-9]|1[012])\\/(19|20)\\d\\d$' },
  { key: 'timeFormat', value: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
  { key: 'hexColor', value: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$' },
  { key: 'base64', value: '^[A-Za-z0-9+\\/]*={0,2}$' },
  { key: 'uuid', value: '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' },
  { key: 'filename', value: '^[^<>:"/\\\\|?*]+\\.[a-zA-Z0-9]+$' },
  { key: 'urlSlug', value: '^[a-z0-9]+(?:-[a-z0-9]+)*$' },
  { key: 'variableName', value: '^[a-zA-Z_$][a-zA-Z0-9_$]*$' },
  { key: 'creditCard', value: '^[0-9]{13,19}$' },
  { key: 'qrCode', value: '^[A-Z0-9 $%*+\\-./:]+$' },
  { key: 'gpsCoordinate', value: '^-?([1-8]?[1-9]|[1-9]0)\\.{1}\\d{1,6}$' },
  { key: 'rgbColor', value: '^rgb\\(([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\)$' },
  { key: 'domain', value: '^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$' },
  { key: 'decimal', value: '^\\d+(\\.\\d{1,2})?$' },
  { key: 'barcode', value: '^[0-9]{8,14}$' },
];

/**
 * Form tạo và chỉnh sửa trường tùy chỉnh
 */
const CustomFieldForm: React.FC<CustomFieldFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { mutateAsync: createCustomField, isPending: isCreating } = useCreateCustomField();
  const { mutateAsync: updateCustomField, isPending: isUpdating } = useUpdateCustomField();

  const formRef = useRef<FormRef<Record<string, unknown>> | null>(null);
  const patternInputRef = useRef<HTMLInputElement>(null);

  // State cho advanced settings
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Schema cho form
  const customFieldSchema = z.object({
    id: z.string()
      .min(1, t('admin:business.customField.form.idRequired'))
      .regex(/^[a-zA-Z0-9_]+$/, t('admin:business.customField.form.configIdValidation', 'ConfigId chỉ được chứa chữ cái, số và dấu gạch dưới')),
    displayName: z.string().min(1, t('admin:business.customField.form.displayNameRequired')),
    label: z.string().optional(), // Nhãn không bắt buộc
    type: z.string().min(1, t('admin:business.customField.form.typeRequired')),
    placeholder: z.string().optional(),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
    validation: z.object({
      minLength: z.string().optional(),
      maxLength: z.string().optional(),
      pattern: z.string().optional(),
    }).optional(),
    options: z.string().optional(),
  });

  // Giá trị mặc định cho form
  const defaultValues = {
    id: '',
    displayName: '',
    type: 'text',
    placeholder: '',
    defaultValue: '',
    description: '',
    validation: {
      minLength: '',
      maxLength: '',
      pattern: '',
    },
    options: '',
  };

  // Định nghĩa kiểu dữ liệu cho form values
  type CustomFieldFormValues = z.infer<typeof customFieldSchema>;

  // Xử lý khi submit form
  const handleSubmit = async (values: CustomFieldFormValues) => {
    try {
      // Parse options nếu có
      let parsedOptions: Array<{ label: string; value: string }> | undefined;
      if (values.options) {
        try {
          const optionsString = String(values.options);
          if (optionsString.includes('{')) {
            // Nếu là JSON format
            parsedOptions = JSON.parse(optionsString);
          } else {
            // Xử lý format "label|value" cho mỗi dòng
            parsedOptions = optionsString
              .split('\n')
              .map(line => line.trim())
              .filter(line => line.length > 0)
              .map(line => {
                if (line.includes('|')) {
                  // Format: "label|value"
                  const [label, value] = line.split('|').map(part => part.trim());
                  return {
                    label: label || '',
                    value: value || ''
                  };
                } else {
                  // Fallback: sử dụng cùng giá trị cho cả label và value
                  return {
                    label: line,
                    value: line.toLowerCase().replace(/\s+/g, '_'),
                  };
                }
              });
          }
        } catch (error) {
          console.error('Error parsing options:', error);
        }
      }

      // Chuẩn bị dữ liệu cho API theo cấu trúc admin
      const formData: CreateCustomFieldData = {
        configId: String(values.id),
        label: String(values.displayName),
        type: String(values.type),
        required: false, // Mặc định không bắt buộc
        configJson: {
          ...(values.placeholder && { placeholder: String(values.placeholder) }),
          ...(values.description && { description: String(values.description) }),
          ...(values.defaultValue && { defaultValue: String(values.defaultValue) }),
          ...(parsedOptions && parsedOptions.length > 0 && { options: parsedOptions }),
          validation: {
            ...(values.validation?.minLength && { minLength: Number(values.validation.minLength) }),
            ...(values.validation?.maxLength && { maxLength: Number(values.validation.maxLength) }),
            ...(values.validation?.pattern && { pattern: String(values.validation.pattern) }),
          },
        },
      };

      if (initialData) {
        // Cập nhật trường tùy chỉnh - chuyển đổi sang UpdateCustomFieldData
        const updateData = {
          configId: formData.configId,
          label: formData.label,
          type: formData.type,
          required: formData.required,
          configJson: formData.configJson,
        };
        await updateCustomField({
          id: initialData.id,
          data: updateData,
        });
      } else {
        // Tạo trường tùy chỉnh mới
        await createCustomField(formData);
      }

      // Gọi callback onSubmit
      onSubmit();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Chuẩn bị giá trị mặc định từ dữ liệu ban đầu
  const getInitialValues = () => {
    if (!initialData) return defaultValues;

    // Lấy dữ liệu validation từ configJson
    const validation = initialData.configJson?.['validation'] as Record<string, unknown> | undefined;

    return {
      id: initialData.configId || '', // ✅ Sử dụng configId từ API response
      displayName: initialData.configJson?.['displayName'] as string || initialData.label || '',
      type: initialData.type,
      placeholder: initialData.configJson?.['placeholder'] as string || '',
      defaultValue: initialData.configJson?.['defaultValue'] as string || '',
      description: initialData.configJson?.['description'] as string || '',
      validation: {
        minLength: validation?.['minLength'] ? String(validation['minLength']) : '',
        maxLength: validation?.['maxLength'] ? String(validation['maxLength']) : '',
        pattern: validation?.['pattern'] ? String(validation['pattern']) : '',
      },
      options: initialData.configJson?.['options']
        ? Array.isArray(initialData.configJson['options'])
          ? (initialData.configJson['options'] as Array<{ label: string; value: string }>)
              .map(option => `${option.label}|${option.value}`)
              .join('\n')
          : JSON.stringify(initialData.configJson['options'])
        : '',
    };
  };

  return (
    <Card title={initialData ? t('admin:business.customField.edit') : t('admin:business.customField.add')}>
      <Form
        ref={formRef}
        schema={customFieldSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={getInitialValues()}
        className="p-4 space-y-6"
      >
        {/* Thông tin cơ bản */}
        <div className="space-y-4 mb-6">
          <FormItem
            name="type"
            label={t('admin:business.customField.type')}
            required
          >
            <Select
              fullWidth
              options={[
                { value: 'text', label: t('admin:business.customField.types.text') },
                { value: 'number', label: t('admin:business.customField.types.number') },
                { value: 'boolean', label: t('admin:business.customField.types.boolean') },
                { value: 'date', label: t('admin:business.customField.types.date') },
                { value: 'select', label: t('admin:business.customField.types.select') },
                { value: 'object', label: t('admin:business.customField.types.object') },
                { value: 'array', label: t('admin:business.customField.types.array') },
              ]}
            />
          </FormItem>

          <FormItem
            name="id"
            label={t('admin:business.customField.form.fieldIdLabel')}
            required
            description={t('admin:business.customField.form.configIdValidation', 'ConfigId chỉ được chứa chữ cái, số và dấu gạch dưới')}
          >
            <Input
              fullWidth
              placeholder={t('admin:business.customField.form.fieldIdPlaceholder')}
              pattern="^[a-zA-Z0-9_]+$"
            />
          </FormItem>

          <FormItem
            name="displayName"
            label={t('admin:business.customField.form.displayNameLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('admin:business.customField.form.displayNamePlaceholder')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('admin:business.customField.form.description')}
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('admin:business.customField.form.descriptionPlaceholder')}
            />
          </FormItem>

          {/* Tùy chọn cho kiểu dữ liệu select */}
          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.EQUALS,
              value: 'select',
            }}
          >
            <FormItem
              name="options"
              label={t('admin:business.customField.form.options')}
            >
              <Textarea
                fullWidth
                rows={6}
                placeholder="Nhập các tùy chọn theo format: label|value (mỗi dòng một tùy chọn)&#10;Ví dụ:&#10;Tùy chọn 1|1&#10;Tùy chọn 2|2&#10;Tùy chọn 3|3"
              />
            </FormItem>
          </ConditionalField>
        </div>

        {/* Checkbox để hiện/ẩn cài đặt nâng cao */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            checked={showAdvancedSettings}
            onChange={setShowAdvancedSettings}
            label={t('admin:business.customField.form.showAdvancedSettings', 'Hiển thị cài đặt nâng cao')}
          />
        </div>

        {/* Cài đặt nâng cao */}
        {showAdvancedSettings && (
          <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <FormItem
              name="placeholder"
              label={t('admin:business.customField.form.placeholder')}
            >
              <Input fullWidth placeholder={t('admin:business.customField.form.placeholderPlaceholder')} />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'number',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('admin:business.customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  type="number"
                  placeholder={t('admin:business.customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'boolean',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('admin:business.customField.form.defaultValue')}
              >
                <Select
                  fullWidth
                  placeholder={t('admin:business.customField.form.booleanDefaultPlaceholder')}
                  options={[
                    { value: 'true', label: t('admin:business.customField.booleanValues.true') },
                    { value: 'false', label: t('admin:business.customField.booleanValues.false') },
                  ]}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'date',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('admin:business.customField.form.defaultValue')}
              >
                <DatePicker
                  fullWidth
                  placeholder={t('admin:business.customField.form.dateDefaultPlaceholder')}
                  format="dd/MM/yyyy"
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.IN,
                value: ['text', 'select', 'object', 'array'],
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('admin:business.customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  placeholder={t('admin:business.customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Validation cho Text */}
              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.minLength"
                  label={t('admin:business.customField.validation.minLength')}
                >
                  <Input fullWidth type="number" min="0" placeholder="0" />
                </FormItem>
              </ConditionalField>

              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.maxLength"
                  label={t('admin:business.customField.validation.maxLength')}
                >
                  <Input fullWidth type="number" min="0" placeholder="100" />
                </FormItem>
              </ConditionalField>


            </div>

              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.pattern"
                  label={t('admin:business.customField.validation.pattern')}
                >
                  <div className="space-y-2">
                    <Input
                      ref={patternInputRef}
                      fullWidth
                      placeholder="^[A-Za-z0-9]+$"
                    />
                    <div className="text-sm text-gray-600">
                      <p className="font-medium mb-2">{t('admin:business.customField.form.patternSuggestions')}</p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                        {COMMON_PATTERN_KEYS.map((pattern, index) => (
                          <button
                            key={index}
                            type="button"
                            className="text-left text-xs p-1 hover:bg-gray-100 rounded truncate"
                            title={pattern.value}
                            onClick={() => {
                              if (patternInputRef.current && formRef.current) {
                                // Cập nhật giá trị input
                                patternInputRef.current.value = pattern.value;
                                // Cập nhật form value
                                formRef.current.setValues({
                                  'validation.pattern': pattern.value
                                });
                                // Trigger change event
                                patternInputRef.current.dispatchEvent(new Event('input', { bubbles: true }));
                              }
                            }}
                          >
                            <span className="font-medium text-blue-600">{t(`admin:business.customField.patterns.${pattern.key}`)}:</span>
                            <span className="ml-1 text-gray-500 font-mono">{pattern.value.substring(0, 20)}...</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </FormItem>
              </ConditionalField>

          </div>
        )}

        <div className="flex justify-end space-x-2 pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            disabled={isSubmitting || isCreating || isUpdating}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('common:save')}
            onClick={() => {
              // Trigger form submit programmatically
              formRef.current?.submit();
            }}
            disabled={isSubmitting || isCreating || isUpdating}
            isLoading={isSubmitting || isCreating || isUpdating}
          />
        </div>
      </Form>
    </Card>
  );
};

export default CustomFieldForm;
