import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Divider, IconCard, Avatar, Loading, Grid } from '@/shared/components/common';
import { useOrder } from '../../hooks/useOrderQuery';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

interface ViewOrderFormProps {
  id: number;
  onClose: () => void;
}

/**
 * Form xem chi tiết đơn hàng
 */
const ViewOrderForm: React.FC<ViewOrderFormProps> = ({
  id,
  onClose,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { data: orderDetail, isLoading } = useOrder(id);

  if (isLoading) {
    return (
      <Loading/>
    );
  }

  if (!orderDetail) {
    return (
      <Card title={t('admin:business.order.detail')}>
        <div className="p-4">
          <p className="text-muted">{t('admin:business.order.notFound')}</p>
        </div>
      </Card>
    );
  }

  const formatTimestamp = (timestamp: number) => {
    try {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return format(date, 'dd/MM/yyyy HH:mm', { locale: vi });
      }
      return String(timestamp);
    } catch {
      return String(timestamp);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const getShippingStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: t('admin:business.order.status.pending'),
      processing: t('admin:business.order.status.processing'),
      shipped: t('admin:business.order.status.shipped'),
      delivered: t('admin:business.order.status.delivered'),
      cancelled: t('admin:business.order.status.cancelled'),
    };
    return statusMap[status] || status;
  };

  const getShippingStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: 'text-warning',
      processing: 'text-primary',
      shipped: 'text-info',
      delivered: 'text-success',
      cancelled: 'text-destructive',
    };
    return colorMap[status] || 'text-muted';
  };

  return (
    <Card title={`${t('admin:business.order.detail')} #${orderDetail.id}`}>
      <div className="p-4 space-y-6">
        {/* Thông tin cơ bản */}
        <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="sm">
          <div className="flex items-center gap-x-2">
            <Typography variant='body2' color="muted" noWrap block={false}>ID:</Typography>
            <Typography variant='body2'>{orderDetail.id}</Typography>
          </div>
          <div className="flex items-center gap-x-2">
            <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.order.source')}:</Typography>
            <Typography variant='body2'>{orderDetail.source}</Typography>
          </div>
          <div className="flex items-center gap-x-2">
            <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.order.shippingStatus')}:</Typography>
            <Typography variant='body2' className={getShippingStatusColor(orderDetail.shippingStatus)}>
              {getShippingStatusText(orderDetail.shippingStatus)}
            </Typography>
          </div>
          <div className="flex items-center gap-x-2">
            <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.order.hasShipping')}:</Typography>
            <Typography variant='body2'>
              {orderDetail.hasShipping ? t('admin:business.order.yes') : t('admin:business.order.no')}
            </Typography>
          </div>
        </Grid>

        <Divider />

        {/* Thông tin khách hàng */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.order.customerInfo')}</Typography>
          <div className="flex items-center space-x-4">
            <Avatar
              src={orderDetail.customer.avatar}
              alt={orderDetail.customer.name}
              size="md"
            />
            <div className="space-y-1">
              <Typography variant='body1' className="font-medium">
                {orderDetail.customer.name}
              </Typography>
              <Typography variant='body2' color="muted">
                {orderDetail.customer.email.primary}
              </Typography>
              <Typography variant='body2' color="muted">
                {orderDetail.customer.phone}
              </Typography>
              <Typography variant='body2' color="muted">
                {orderDetail.customer.platform} • {orderDetail.customer.timezone}
              </Typography>
            </div>
          </div>
        </div>

        <Divider />

        {/* Thông tin sản phẩm */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.order.productInfo')}</Typography>
          {orderDetail.productInfo.map((productInfo, index) => (
            <div key={index} className="rounded-lg p-4 space-y-3">
              {productInfo.products.map((product) => (
                <div key={product.id} className="flex justify-between items-center">
                  <div className="flex-1">
                    <Typography variant='body1' className="font-medium">
                      {product.name}
                    </Typography>
                    <Typography variant='body2' color="muted">
                      {formatCurrency(product.price)} x {product.quantity}
                    </Typography>
                  </div>
                  <Typography variant='body1' className="font-medium">
                    {formatCurrency(product.price * product.quantity)}
                  </Typography>
                </div>
              ))}
            </div>
          ))}
        </div>

        <Divider />

        {/* Thông tin hóa đơn */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.order.billInfo')}</Typography>
          <div className="space-y-2">
            <div className="flex justify-between">
              <Typography variant='body2' color="muted">{t('admin:business.order.subtotal')}:</Typography>
              <Typography variant='body2'>{formatCurrency(orderDetail.billInfo.subtotal)}</Typography>
            </div>
            <div className="flex justify-between font-medium">
              <Typography variant='body1'>{t('admin:business.order.total')}:</Typography>
              <Typography variant='body1'>{formatCurrency(orderDetail.billInfo.total)}</Typography>
            </div>
          </div>
        </div>

        <Divider />

        {/* Thông tin vận chuyển */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.order.logisticInfo')}</Typography>
          <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="sm">
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.order.carrier')}:</Typography>
              <Typography variant='body2'>{orderDetail.logisticInfo.carrier}</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.order.address')}:</Typography>
              <Typography variant='body2'>{orderDetail.logisticInfo.address || t('admin:business.order.notSet')}</Typography>
            </div>
          </Grid>
        </div>

        <Divider />

        {/* Thông tin thời gian */}
        <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="sm">
          <div className="flex items-center gap-x-2">
            <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.order.createdAt')}:</Typography>
            <Typography variant='body2'>{formatTimestamp(orderDetail.createdAt)}</Typography>
          </div>
          <div className="flex items-center gap-x-2">
            <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.order.updatedAt')}:</Typography>
            <Typography variant='body2'>{formatTimestamp(orderDetail.updatedAt)}</Typography>
          </div>
        </Grid>

        {/* Actions */}
        <div className="flex justify-end pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('admin:business.order.close')}
            onClick={onClose}
          />
        </div>
      </div>
    </Card>
  );
};

export default ViewOrderForm;
