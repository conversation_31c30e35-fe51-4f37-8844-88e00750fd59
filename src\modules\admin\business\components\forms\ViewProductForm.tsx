import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Divider, Button, Chip, ResponsiveImage, Icon, Loading } from '@/shared/components/common';
import { useProductDetail } from '../../hooks/useProductQuery';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

interface ViewProductFormProps {
  id: number;
  onClose: () => void;
}

/**
 * Form xem chi tiết sản phẩm
 */
const ViewProductForm: React.FC<ViewProductFormProps> = ({
  id,
  onClose,
}) => {
  const { t } = useTranslation(['admin']);
  const { data: productDetail, isLoading } = useProductDetail(id);

  if (isLoading) {
    return (
    <Loading/>
    );
  }

  if (!productDetail) {
    return (
      <Card title={t('admin:business.product.detail')}>
        <div className="p-4">
          <Typography variant='h6'>{t('admin:business.product.notFound')}</Typography>
        </div>
      </Card>
    );
  }

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(Number(timestamp));
      if (!isNaN(date.getTime())) {
        return format(date, 'dd/MM/yyyy HH:mm', { locale: vi });
      }
      return String(timestamp);
    } catch {
      return String(timestamp);
    }
  };

  const formatCurrency = (amount: number, currency: string = 'VND') => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const getStatusColor = (status: string | object) => {
    // Handle case where status might be an object
    const statusValue = typeof status === 'object' ? JSON.stringify(status) : status;
    const colorMap: Record<string, 'default' | 'primary' | 'success' | 'warning' | 'danger'> = {
      PENDING: 'warning',
      APPROVED: 'success',
      REJECTED: 'danger',
      ACTIVE: 'success',
      INACTIVE: 'default',
    };
    return colorMap[statusValue] || 'default';
  };

  const getStatusText = (status: string | object) => {
    // Handle case where status might be an object
    const statusValue = typeof status === 'object' ? JSON.stringify(status) : status;
    const statusMap: Record<string, string> = {
      PENDING: t('admin:business.product.statusTypes.PENDING', 'Chờ duyệt'),
      APPROVED: t('admin:business.product.statusTypes.APPROVED', 'Đã duyệt'),
      REJECTED: t('admin:business.product.statusTypes.REJECTED', 'Từ chối'),
      ACTIVE: t('admin:business.product.viewForm.status.active', 'Hoạt động'),
      INACTIVE: t('admin:business.product.viewForm.status.inactive', 'Không hoạt động'),
    };
    return statusMap[statusValue] || statusValue;
  };

  const getTypePriceText = (typePrice: string) => {
    const typePriceMap: Record<string, string> = {
      HAS_PRICE: t('admin:business.product.viewForm.priceTypes.yes', 'Có giá'),
      FREE: t('admin:business.product.viewForm.priceTypes.no', 'Miễn phí'),
      CONTACT: t('admin:business.product.viewForm.priceTypes.other', 'Liên hệ'),
    };
    return typePriceMap[typePrice] || typePrice;
  };

  return (
    <Card title={`${t('admin:business.product.detail')} - ${productDetail.name}`}>
      <div className="p-4 space-y-6">
        {/* Thông tin cơ bản */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.product.basicInfo')}</Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>ID:</Typography>
              <Typography variant='body2'>{productDetail.id}</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.status.status')}:</Typography>
              <Chip size="sm" variant={getStatusColor(productDetail.status)}>
                {getStatusText(productDetail.status)}
              </Chip>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.typeprice')}:</Typography>
              <Typography variant='body2'>{getTypePriceText(productDetail.typePrice)}</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.createdBy')}:</Typography>
              <Typography variant='body2'>{productDetail.createdBy}</Typography>
            </div>
          </div>

          <div className="gap-x-2 flex items-center">
            <Typography variant='body2' color="muted">
              {t('admin:business.product.name')}:
            </Typography>
            <Typography variant='body2' className="font-medium">
              {productDetail.name}
            </Typography>
          </div>

          <div className="gap-x-2 flex items-center">
            <Typography variant='body2' color="muted">
              {t('admin:business.product.description')}:
            </Typography>
            <Typography variant='body2' className="bg-gray-50 dark:bg-gray-800 p-3 rounded">
              {productDetail.description || t('admin:business.product.viewForm.noDescription')}
            </Typography>
          </div>
        </div>

        <Divider />

        {/* Thông tin giá */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.product.priceInfo')}</Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.listPrice')}:</Typography>
              <Typography variant='body2' className="font-medium">
                {formatCurrency(productDetail.price.listPrice, productDetail.price.currency)}
              </Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.salePrice')}:</Typography>
              <Typography variant='body2' className="font-medium text-green-600">
                {formatCurrency(productDetail.price.salePrice, productDetail.price.currency)}
              </Typography>
            </div>
          </div>
        </div>

        <Divider />

        {/* Hình ảnh */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.product.viewForm.imagesTitle')}</Typography>
          {productDetail.images && productDetail.images.length > 0 ? (
            <div className="grid grid-cols-4 md:grid-cols-6 gap-4">
              {productDetail.images.map((image, index) => (
                <div key={index} className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                  <ResponsiveImage
                    src={typeof image === 'string' ? image : (image as { url?: string })?.url || '/placeholder-image.jpg'}
                    alt={`Product image ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-32 bg-gray-50 rounded-lg">
              <div className="text-center">
                <Icon name="image" size="lg" className="text-gray-400 mx-auto mb-2" />
                <Typography variant='body2' color="muted">
                  {t('admin:business.product.noImages')}
                </Typography>
              </div>
            </div>
          )}
        </div>

        <Divider />

        {/* Thông tin vận chuyển */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.product.shipmentConfig')}</Typography>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.width')}:</Typography>
              <Typography variant='body2'>{productDetail.shipmentConfig.widthCm} cm</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.height')}:</Typography>
              <Typography variant='body2'>{productDetail.shipmentConfig.heightCm} cm</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.length')}:</Typography>
              <Typography variant='body2'>{productDetail.shipmentConfig.lengthCm} cm</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.weight')}:</Typography>
              <Typography variant='body2'>{productDetail.shipmentConfig.weightGram} g</Typography>
            </div>
          </div>
        </div>

        <Divider />

        {/* Tags */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.product.tags')}</Typography>
          {productDetail.tags && productDetail.tags.length > 0 ? (
            <div className="flex flex-wrap  gap-2">
              {productDetail.tags.map((tag, index) => (
                <Chip key={index} size="sm" className='font-medium' variant="info">
                  {tag}
                </Chip>
              ))}
            </div>
          ) : (
            <Typography variant='body2' color="muted">
              {t('admin:business.product.noTags')}
            </Typography>
          )}
        </div>

        <Divider />

        {/* Thông tin thời gian */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center gap-x-2">
            <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.viewForm.createdAt', 'Ngày tạo')}:</Typography>
            <Typography variant='body2'>{formatTimestamp(productDetail.createdAt)}</Typography>
          </div>
          <div className="flex items-center gap-x-2">
            <Typography variant='body2' color="muted" noWrap block={false}>{t('admin:business.product.viewForm.updatedAt', 'Ngày cập nhật')}:</Typography>
            <Typography variant='body2'>{formatTimestamp(productDetail.updatedAt)}</Typography>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end pt-4">
          <Button
            variant='outline'
            onClick={onClose}
          >
            {t('admin:business.product.viewForm.close', 'Đóng')}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ViewProductForm;
