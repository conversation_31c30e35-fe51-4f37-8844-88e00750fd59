import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Divider, Button, Chip, Loading, Grid } from '@/shared/components/common';
import { useWarehouseCustomFieldDetail } from '../../hooks/useWarehouseCustomFieldQuery';

interface ViewWarehouseCustomfieldFormProps {
  warehouseId: number;
  fieldId: number;
  onClose: () => void;
}

/**
 * Form xem chi tiết trường tùy chỉnh kho
 */
const ViewWarehouseCustomfieldForm: React.FC<ViewWarehouseCustomfieldFormProps> = ({
  warehouseId,
  fieldId,
  onClose,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { data: fieldDetail, isLoading } = useWarehouseCustomFieldDetail(warehouseId, fieldId);

  if (isLoading) {
    return <Loading />;
  }

  if (!fieldDetail) {
    return (
      <Card title={t('admin:business.warehouseCustomField.detail')}>
        <div className="p-4">
          <p className="text-muted">{t('admin:business.warehouseCustomField.notFound')}</p>
        </div>
      </Card>
    );
  }

  const getFieldTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      text: t('admin:business.customField.fieldTypes.text'),
      number: t('admin:business.customField.fieldTypes.number'),
      email: t('admin:business.customField.fieldTypes.email'),
      phone: t('admin:business.customField.fieldTypes.phone'),
      date: t('admin:business.customField.fieldTypes.date'),
      boolean: t('admin:business.customField.fieldTypes.boolean'),
      select: t('admin:business.customField.fieldTypes.select'),
      textarea: t('admin:business.customField.fieldTypes.textarea'),
    };
    return typeMap[type] || type;
  };

  const getFieldTypeColor = (type: string) => {
    const colorMap: Record<string, 'default' | 'primary' | 'success' | 'warning' | 'danger'> = {
      text: 'default',
      number: 'primary',
      email: 'success',
      phone: 'success',
      date: 'warning',
      boolean: 'primary',
      select: 'default',
      textarea: 'default',
    };
    return colorMap[type] || 'default';
  };

  return (
    <Card title={`${t('admin:business.warehouseCustomField.detail')} - ${fieldDetail.fieldLabel}`}>
      <div className="p-4 space-y-6">
        {/* Thông tin cơ bản */}
        <div className="space-y-4">
          <Typography variant="h6">{t('admin:business.warehouseCustomField.basicInfo')}</Typography>
          <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="sm">
            <div className="flex items-center gap-x-2">
              <Typography variant="body2" color="muted" noWrap block={false}>
                {t('admin:business.warehouseCustomField.warehouseId')}:
              </Typography>
              <Typography variant="body2">{fieldDetail.warehouseId}</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant="body2" color="muted" noWrap block={false}>
                {t('admin:business.warehouseCustomField.fieldId')}:
              </Typography>
              <Typography variant="body2">{fieldDetail.fieldId}</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant="body2" color="muted" noWrap block={false}>
                {t('admin:business.warehouseCustomField.warehouseName')}:
              </Typography>
              <Typography variant="body2" className="font-medium">
                {fieldDetail.warehouseName}
              </Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant="body2" color="muted" noWrap block={false}>
                {t('admin:business.warehouseCustomField.fieldLabel')}:
              </Typography>
              <Typography variant="body2" className="font-medium">
                {fieldDetail.fieldLabel}
              </Typography>
            </div>
          </Grid>
        </div>

        <Divider />

        {/* Thông tin trường */}
        <div className="space-y-4">
          <Typography variant="h6">{t('admin:business.warehouseCustomField.fieldInfo')}</Typography>
          <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="sm">
            <div className="flex items-center gap-x-2">
              <Typography variant="body2" color="muted" noWrap block={false}>
                {t('admin:business.customField.type')}:
              </Typography>
              <Chip size="sm" variant={getFieldTypeColor(fieldDetail.fieldDetails.type)}>
                {getFieldTypeText(fieldDetail.fieldDetails.type)}
              </Chip>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant="body2" color="muted" noWrap block={false}>
                {t('admin:business.customField.required')}:
              </Typography>
              <Chip size="sm" variant={fieldDetail.fieldDetails.required ? 'success' : 'default'}>
                {fieldDetail.fieldDetails.required
                  ? t('admin:business.warehouseCustomField.yes')
                  : t('admin:business.warehouseCustomField.no')}
              </Chip>
            </div>
          </Grid>

          {/* Cấu hình trường */}
          {fieldDetail.fieldDetails.configJson && (
            <div className="space-y-2">
              <div className="bg-muted/50 rounded space-y-3">
                <Grid columns={{ xs: 1, lg: 2 }} columnGap="md" rowGap="sm">
                  {fieldDetail.fieldDetails.configJson.description && (
                    <div className="flex items-start gap-x-2">
                      <Typography variant="body2" color="muted" className="min-w-fit">
                        {t('admin:business.customField.description')}:
                      </Typography>
                      <Typography variant="body2">
                        {fieldDetail.fieldDetails.configJson.description}
                      </Typography>
                    </div>
                  )}
                  {fieldDetail.fieldDetails.configJson.placeholder && (
                    <div className="flex items-start gap-x-2">
                      <Typography variant="body2" color="muted" className="min-w-fit">
                        {t('admin:business.customField.placeholder')}:
                      </Typography>
                      <Typography variant="body2">
                        {fieldDetail.fieldDetails.configJson.placeholder}
                      </Typography>
                    </div>
                  )}
                  {fieldDetail.fieldDetails.configJson.maxLength && (
                    <div className="flex items-center gap-x-2">
                      <Typography variant="body2" color="muted" noWrap block={false}>
                        {t('admin:business.customField.maxLength')}:
                      </Typography>
                      <Typography variant="body2">
                        {fieldDetail.fieldDetails.configJson.maxLength}
                      </Typography>
                    </div>
                  )}
                </Grid>
              </div>
            </div>
          )}
        </div>

        <Divider />

        {/* Giá trị hiện tại */}
        <div className="space-y-4">
          <Typography variant="h6">
            {t('admin:business.warehouseCustomField.currentValue')}
          </Typography>
          <div className="flex item-center gap-x-2">
            <Typography variant="body2" color="muted">
              {t('admin:business.customField.value')}:
            </Typography>
        <Typography variant="body2">
              {fieldDetail.value?.value || t('admin:business.warehouseCustomField.notSet')}
            </Typography>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={onClose}>
            {t('admin:business.warehouseCustomField.close')}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ViewWarehouseCustomfieldForm;
