import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Divider, Loading, Grid, IconCard } from '@/shared/components/common';
import { useWarehouseDetail } from '../../hooks/usePhysicalWarehouseAdminQuery';

interface ViewWarehouseFormProps {
  warehouseId: number;
  onClose: () => void;
}

/**
 * Form xem chi tiết kho vật lý
 */
const ViewWarehouseForm: React.FC<ViewWarehouseFormProps> = ({
  warehouseId,
  onClose,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { data: warehouseDetail, isLoading } = useWarehouseDetail(warehouseId);

  if (isLoading) {
    return (
      <Loading />
    );
  }

  if (!warehouseDetail) {
    return (
      <Card title={t('admin:business.warehouse.detail')}>
        <div className="p-4">
          <p className="text-muted">{t('admin:business.warehouse.notFound')}</p>
        </div>
      </Card>
    );
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const getFieldTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      TEXT: 'Văn bản',
      NUMBER: 'Số',
      EMAIL: 'Email',
      PHONE: 'Số điện thoại',
      DATE: 'Ngày',
      BOOLEAN: 'Đúng/Sai',
      SELECT: 'Lựa chọn',
      TEXTAREA: 'Văn bản dài',
    };
    return typeMap[type] || type;
  };

  return (
    <Card title={`${t('admin:business.warehouse.detail')} - ${warehouseDetail.warehouse.name}`}>
      <div className="p-4 space-y-6">
        {/* Thông tin cơ bản */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.warehouse.basicInfo')}</Typography>
          <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="sm">
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>
                {t('admin:business.warehouse.id')}:
              </Typography>
              <Typography variant='body2'>{warehouseDetail.warehouseId}</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>
                {t('admin:business.warehouse.name')}:
              </Typography>
              <Typography variant='body2'>{warehouseDetail.warehouse.name}</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>
                {t('admin:business.warehouse.type')}:
              </Typography>
              <Typography variant='body2'>{warehouseDetail.warehouse.type}</Typography>
            </div>
            <div className="flex items-center gap-x-2">
              <Typography variant='body2' color="muted" noWrap block={false}>
                {t('admin:business.warehouse.capacity')}:
              </Typography>
              <Typography variant='body2'>{formatNumber(warehouseDetail.capacity)}</Typography>
            </div>
          </Grid>

          <div className="space-y-2">
            <Typography variant='body2' color="muted">
              {t('admin:business.warehouse.description')}:
            </Typography>
            <Typography variant='body2' className="bg-muted/50 p-3 rounded">
              {warehouseDetail.warehouse.description || t('admin:business.warehouse.noDescription')}
            </Typography>
          </div>

          <div className="space-y-2">
            <Typography variant='body2' color="muted">
              {t('admin:business.warehouse.address')}:
            </Typography>
            <Typography variant='body2' className="bg-muted/50 p-3 rounded">
              {warehouseDetail.address || t('admin:business.warehouse.notSet')}
            </Typography>
          </div>
        </div>

        <Divider />

        {/* Trường tùy chỉnh */}
        <div className="space-y-4">
          <Typography variant='h6'>{t('admin:business.warehouse.customFields')}</Typography>

          {warehouseDetail.customFields && warehouseDetail.customFields.length > 0 ? (
            <div className="space-y-4">
              {warehouseDetail.customFields.map((field) => (
                <div key={field.fieldId} className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <Typography variant='body1' className="font-medium">
                      {field.label}
                    </Typography>
                    <Typography variant='body2' color="muted" className="text-xs">
                      ID: {field.fieldId} • {getFieldTypeText(field.type)}
                    </Typography>
                  </div>

                  <div className="space-y-1">
                    <Typography variant='body2' color="muted">
                      {t('admin:business.customField.value')}:
                    </Typography>
                    <div className="bg-muted/50 p-3 rounded">
                      <Typography variant='body2'>
                        {field.value?.value || t('admin:business.warehouse.notSet')}
                      </Typography>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <Typography variant='body1' color="muted">
              {t('admin:business.warehouse.noCustomFields')}
            </Typography>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('admin:business.warehouse.close')}
            onClick={onClose}
          />
        </div>
      </div>
    </Card>
  );
};

export default ViewWarehouseForm;
