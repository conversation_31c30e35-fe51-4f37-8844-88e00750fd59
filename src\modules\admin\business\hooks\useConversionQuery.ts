import { useQuery } from '@tanstack/react-query';
import { ConversionService } from '../services/conversion.service';
import { ConversionQueryParams, CustomerConversionQueryParams } from '../types/conversion.types';

// Đ<PERSON><PERSON> nghĩa các query key cho khách hàng chuyển đổi
export const CUSTOMER_CONVERSION_QUERY_KEYS = {
  all: ['admin', 'customer-conversions'] as const,
  lists: () => [...CUSTOMER_CONVERSION_QUERY_KEYS.all, 'list'] as const,
  list: (filters: CustomerConversionQueryParams) => [...CUSTOMER_CONVERSION_QUERY_KEYS.lists(), filters] as const,
};

// Định nghĩa các query key cho chuyển đổi (legacy)
export const CONVERSION_QUERY_KEYS = {
  all: ['admin', 'conversions'] as const,
  lists: () => [...CONVERSION_QUERY_KEYS.all, 'list'] as const,
  list: (filters: ConversionQueryParams) => [...CONVERSION_QUERY_KEYS.lists(), filters] as const,
  details: () => [...CONVERSION_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...CONVERSION_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách khách hàng chuyển đổi (API mới)
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useCustomerConversions = (params?: CustomerConversionQueryParams) => {
  return useQuery({
    queryKey: CUSTOMER_CONVERSION_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => ConversionService.getCustomerConversions(params),
    select: (data) => data.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy danh sách bản ghi chuyển đổi (legacy)
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useConversions = (params?: ConversionQueryParams) => {
  return useQuery({
    queryKey: CONVERSION_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => ConversionService.getConverts(params),
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy chi tiết bản ghi chuyển đổi theo ID
 * @param id ID của bản ghi chuyển đổi
 * @returns Query object
 */
export const useConversion = (id: number) => {
  return useQuery({
    queryKey: CONVERSION_QUERY_KEYS.detail(id),
    queryFn: () => ConversionService.getConvertById(id),
    enabled: !!id,
    select: (data) => data.result,
  });
};


