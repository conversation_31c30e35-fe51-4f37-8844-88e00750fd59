import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CustomGroupFormService, CustomGroupFormQueryParams, CreateCustomGroupFormData, UpdateCustomGroupFormData } from '../services/custom-group-form.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';
import { useTranslation } from 'react-i18next';

/**
 * Query keys cho custom group form API
 */
export const CUSTOM_GROUP_FORM_QUERY_KEYS = {
  all: ['business', 'custom-group-forms'] as const,
  list: (params: CustomGroupFormQueryParams) => [...CUSTOM_GROUP_FORM_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...CUSTOM_GROUP_FORM_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook lấy danh sách nhóm trường tùy chỉnh
 */
export const useCustomGroupForms = (params: CustomGroupFormQueryParams = {}) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  return useQuery({
    queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.list(params),
    queryFn: () => CustomGroupFormService.getCustomGroupForms(params),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết nhóm trường tùy chỉnh theo ID
 */
export const useCustomGroupForm = (id: number) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  return useQuery({
    queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.detail(id),
    queryFn: () => CustomGroupFormService.getCustomGroupFormById(id),
    select: (data) => data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook tạo nhóm trường tùy chỉnh mới
 */
export const useCreateCustomGroupForm = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (data: CreateCustomGroupFormData) => CustomGroupFormService.createCustomGroupForm(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:customGroupForm.createSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customGroupForm.createError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook cập nhật nhóm trường tùy chỉnh
 */
export const useUpdateCustomGroupForm = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomGroupFormData }) =>
      CustomGroupFormService.updateCustomGroupForm(id, data),
    onSuccess: (_, variables) => {
      NotificationUtil.success({
        message: t('business:customGroupForm.updateSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.detail(variables.id),
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customGroupForm.updateError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook xóa nhóm trường tùy chỉnh
 */
export const useDeleteCustomGroupForm = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (id: number) => CustomGroupFormService.deleteCustomGroupForm(id),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:customGroupForm.deleteSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customGroupForm.deleteError'),
        duration: 3000,
      });
    },
  });
};
