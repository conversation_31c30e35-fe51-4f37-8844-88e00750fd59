import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FileService } from '../services/file.service';
import {
  CreateFileDto,
  FileQueryParams,
  UpdateFileDto,
} from '../types/file.types';

// Định nghĩa các query key
export const FILE_QUERY_KEYS = {
  all: ['files'] as const,
  lists: () => [...FILE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: FileQueryParams) => [...FILE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...FILE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...FILE_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách file
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useFiles = (params?: FileQueryParams) => {
  return useQuery({
    queryKey: FILE_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => FileService.getFiles(params),
  });
};

/**
 * Hook để lấy chi tiết file theo ID
 * @param id ID của file
 * @returns Query object
 */
export const useFile = (id: number) => {
  return useQuery({
    queryKey: FILE_QUERY_KEYS.detail(id),
    queryFn: () => FileService.getFileById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo file mới
 * @returns Mutation object
 */
export const useCreateFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFileDto) => FileService.createFile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật file
 * @returns Mutation object
 */
export const useUpdateFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateFileDto }) =>
      FileService.updateFile(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa file
 * @returns Mutation object
 */
export const useDeleteFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => FileService.deleteFile(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.lists() });
    },
  });
};
