import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FolderService } from '../services/folder.service';
import {
  CreateFolderDto,
  FolderQueryParams,
  UpdateFolderDto,
} from '../types/folder.types';

// Định nghĩa các query key
export const FOLDER_QUERY_KEYS = {
  all: ['folders'] as const,
  lists: () => [...FOLDER_QUERY_KEYS.all, 'list'] as const,
  list: (filters: FolderQueryParams) => [...FOLDER_QUERY_KEYS.lists(), filters] as const,
  details: () => [...FOLDER_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...FOLDER_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách folder
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useFolders = (params?: FolderQueryParams) => {
  return useQuery({
    queryKey: FOLDER_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => FolderService.getFolders(params),
  });
};

/**
 * Hook để lấy chi tiết folder theo ID
 * @param id ID của folder
 * @returns Query object
 */
export const useFolder = (id: number) => {
  return useQuery({
    queryKey: FOLDER_QUERY_KEYS.detail(id),
    queryFn: () => FolderService.getFolderById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo folder mới
 * @returns Mutation object
 */
export const useCreateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFolderDto) => FolderService.createFolder(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FOLDER_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật folder
 * @returns Mutation object
 */
export const useUpdateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateFolderDto }) =>
      FolderService.updateFolder(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: FOLDER_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: FOLDER_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa folder
 * @returns Mutation object
 */
export const useDeleteFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => FolderService.deleteFolder(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FOLDER_QUERY_KEYS.lists() });
    },
  });
};
