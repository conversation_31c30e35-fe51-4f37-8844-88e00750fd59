import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  PhysicalWarehouseAdminDto,
  PhysicalWarehouseAdminDetailDto,
  QueryPhysicalWarehouseAdminDto,
  WarehouseDetail,
} from '../types/physical-warehouse.types';
import {
  getPhysicalWarehousesAdminWithBusinessLogic,
  getPhysicalWarehouseAdminByIdWithBusinessLogic,
  getWarehouseDetailWithBusinessLogic,
} from '../services/physical-warehouse.service';
import {
  PHYSICAL_WAREHOUSE_ADMIN_QUERY_KEYS,
  PHYSICAL_WAREHOUSE_ADMIN_STALE_TIME,
} from '../constants/physical-warehouse.constants';

/**
 * Hooks Layer - TanStack Query implementation cho physical warehouse admin
 */

/**
 * Hook để lấy danh sách kho vật lý admin
 * @param params Tham số truy vấn
 * @returns Query result với danh sách kho vật lý
 */
export const usePhysicalWarehousesAdmin = (
  params?: QueryPhysicalWarehouseAdminDto
): UseQueryResult<ApiResponseDto<PaginatedResult<PhysicalWarehouseAdminDto>>, Error> => {
  return useQuery({
    queryKey: PHYSICAL_WAREHOUSE_ADMIN_QUERY_KEYS.LIST((params as unknown as Record<string, unknown>) || {}),
    queryFn: () => getPhysicalWarehousesAdminWithBusinessLogic(params),
    staleTime: PHYSICAL_WAREHOUSE_ADMIN_STALE_TIME,
    enabled: true,
  });
};

/**
 * Hook để lấy chi tiết kho vật lý admin theo ID
 * @param id ID của kho vật lý
 * @param enabled Có enable query không
 * @returns Query result với chi tiết kho vật lý
 */
export const usePhysicalWarehouseAdminById = (
  id: number,
  enabled: boolean = true
): UseQueryResult<ApiResponseDto<PhysicalWarehouseAdminDetailDto>, Error> => {
  return useQuery({
    queryKey: PHYSICAL_WAREHOUSE_ADMIN_QUERY_KEYS.DETAIL(id),
    queryFn: () => getPhysicalWarehouseAdminByIdWithBusinessLogic(id),
    staleTime: PHYSICAL_WAREHOUSE_ADMIN_STALE_TIME,
    enabled: enabled && !!id && id > 0,
  });
};

/**
 * Hook để lấy chi tiết kho vật lý theo ID (API mới)
 * @param warehouseId ID của kho vật lý
 * @param enabled Có enable query không
 * @returns Query result với chi tiết kho vật lý
 */
export const useWarehouseDetail = (
  warehouseId: number,
  enabled: boolean = true
): UseQueryResult<WarehouseDetail, Error> => {
  return useQuery({
    queryKey: [...PHYSICAL_WAREHOUSE_ADMIN_QUERY_KEYS.ALL, 'warehouse-detail', warehouseId],
    queryFn: () => getWarehouseDetailWithBusinessLogic(warehouseId),
    select: (data) => data.result,
    staleTime: PHYSICAL_WAREHOUSE_ADMIN_STALE_TIME,
    enabled: enabled && !!warehouseId && warehouseId > 0,
  });
};
