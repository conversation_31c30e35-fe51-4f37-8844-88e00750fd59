import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import VirtualWarehouseAdminService from '../services/virtual-warehouse-admin.service';
import {
  QueryVirtualWarehouseAdminDto,
  UpdateVirtualWarehouseStatusDto,
} from '../types/virtual-warehouse.types';

// Định nghĩa các query key
export const VIRTUAL_WAREHOUSE_QUERY_KEYS = {
  all: ['admin-virtual-warehouses'] as const,
  lists: () => [...VIRTUAL_WAREHOUSE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: QueryVirtualWarehouseAdminDto) => [...VIRTUAL_WAREHOUSE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...VIRTUAL_WAREHOUSE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...VIRTUAL_WAREHOUSE_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách kho ảo
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useVirtualWarehouses = (params?: QueryVirtualWarehouseAdminDto) => {
  return useQuery({
    queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => VirtualWarehouseAdminService.getVirtualWarehouses(params),
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy chi tiết kho ảo
 * @param id ID của kho ảo
 * @returns Query object
 */
export const useVirtualWarehouseDetail = (id?: number) => {
  return useQuery({
    queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.detail(id || 0),
    queryFn: () => VirtualWarehouseAdminService.getVirtualWarehouseById(id || 0),
    enabled: !!id, // Chỉ gọi API khi có ID
    select: (data) => data.result,
  });
};

/**
 * Hook để cập nhật trạng thái kho ảo
 * @returns Mutation object để cập nhật trạng thái
 */
export const useUpdateVirtualWarehouseStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateVirtualWarehouseStatusDto) =>
      VirtualWarehouseAdminService.updateMultipleVirtualWarehousesStatus(data as unknown as Record<string, unknown>),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};
