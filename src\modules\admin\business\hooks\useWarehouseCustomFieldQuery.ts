import { useQuery } from '@tanstack/react-query';
import { WarehouseCustomFieldService } from '../services/warehouse-custom-field.service';
import { WarehouseCustomFieldQueryParams } from '../types/warehouse.types';

// Đ<PERSON>nh nghĩa các query key
export const WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS = {
  all: ['admin', 'warehouseCustomFields'] as const,
  lists: () => [...WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.all, 'list'] as const,
  list: (params: WarehouseCustomFieldQueryParams) => [...WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.lists(), params] as const,
  details: () => [...WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.all, 'detail'] as const,
  detail: (warehouseId: number, fieldId: number) => [...WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.details(), warehouseId, fieldId] as const,
};

/**
 * Hook để lấy danh sách trường tùy chỉnh của kho với phân trang
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useWarehouseCustomFields = (params?: WarehouseCustomFieldQueryParams) => {
  return useQuery({
    queryKey: WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => WarehouseCustomFieldService.getCustomFields(params),
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy chi tiết trường tùy chỉnh của kho theo ID
 * @param warehouseId ID của kho
 * @param fieldId ID của trường tùy chỉnh
 * @returns Query object
 */
export const useWarehouseCustomField = (warehouseId: number, fieldId: number) => {
  return useQuery({
    queryKey: WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.detail(warehouseId, fieldId),
    queryFn: () => WarehouseCustomFieldService.getCustomFieldById(warehouseId, fieldId),
    enabled: !!warehouseId && !!fieldId,
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy chi tiết trường tùy chỉnh của kho (alias cho useWarehouseCustomField)
 * @param warehouseId ID của kho
 * @param fieldId ID của trường tùy chỉnh
 * @returns Query object
 */
export const useWarehouseCustomFieldDetail = (warehouseId: number, fieldId: number) => {
  return useWarehouseCustomField(warehouseId, fieldId);
};


