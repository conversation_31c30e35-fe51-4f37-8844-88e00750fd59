{"admin": {"business": {"title": "Business Management", "description": "Manage your business activities", "product": {"title": "Products", "description": "Manage your product catalog", "totalProducts": "Total Products", "manage": "Manage Products", "tags": "Tags", "priceType": {"title": "Price Type", "hasPrice": "Fixed Price", "stringPrice": "Descriptive Price", "noPrice": "No Price"}, "productType": {"title": "Product Type", "PHYSICAL": "Physical Product", "DIGITAL": "Digital Product", "SERVICE": "Service", "EVENT": "Event", "COMBO": "Combo Product"}, "typePrice": "Price Type", "status": {"status": "Status", "active": "Active", "inactive": "Inactive", "outOfStock": "Out of Stock", "draft": "Draft", "PENDING": "Pending", "APPROVED": "Approved", "REJECTED": "Rejected", "ACTIVE": "Active", "INACTIVE": "Inactive", "DELETED": "Deleted"}, "form": {"title": "Add New Product", "name": "Product Name", "description": "Description", "price": "Prices", "category": "Category", "sku": "SKU", "status": "Status", "typeprice": "Price Type", "inventory": "Inventory", "submit": "Save Product", "cancel": "Cancel", "createTitle": "Add New Product", "editTitle": "Edit Product", "namePlaceholder": "Enter product name", "descriptionPlaceholder": "Enter product description", "pricePlaceholder": "Enter product price", "categoryPlaceholder": "Select product category", "skuPlaceholder": "Enter product SKU", "statusPlaceholder": "Select product status", "inventoryPlaceholder": "Enter product inventory", "tagsPlaceholder": "Enter product tag", "mediaPlaceholder": "Drag and drop or click to upload product image", "media": "Product Image", "shipmentConfig": {"title": "Shipment Configuration", "widthCm": "Width (cm)", "heightCm": "Height (cm)", "lengthCm": "Length (cm)"}, "customFields": {"title": "Custom Fields", "selectField": "Select Custom Field", "selectGroupForm": "Select Custom Field Group", "searchPlaceholder": "Search custom fields...", "searchGroupPlaceholder": "Search custom field groups...", "selectedFields": "Selected Custom Fields", "selectedGroupForm": "Selected Custom Field Group", "addField": "Add Custom Field", "addGroupForm": "Add Custom Field Group"}, "variants": {"title": "Product Classification", "addVariant": "Add Classification", "variant": "Classification", "noVariants": "No classifications yet. Click \"Add Classification\" to get started.", "customFields": "Classification Attributes", "searchCustomField": "Search attributes"}, "priceDescriptionPlaceholder": "Enter price description", "priceTypePlaceholder": "Select price type", "priceTypes": {"yes": "Yes", "no": "No", "other": "Other"}}, "customFields": {"title": "Custom Fields", "selectField": "Select Custom Field", "selectGroupForm": "Select Custom Field Group", "searchPlaceholder": "Search custom fields...", "searchGroupPlaceholder": "Search custom field groups...", "selectedFields": "Selected Custom Fields", "selectedGroupForm": "Selected Custom Field Group", "addField": "Add Custom Field", "addGroupForm": "Add Custom Field Group"}, "listPrice": "List Price", "salePrice": "Sale Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "priceDescription": "Price Description", "createSuccess": "Product created successfully", "createError": "Error creating product", "updateSuccess": "Product updated successfully", "updateError": "Error updating product", "deleteSuccess": "Product deleted successfully", "deleteError": "Error deleting product", "bulkDeleteSuccess": "{{count}} products deleted successfully", "bulkDeleteError": "Error deleting multiple products", "selectToDelete": "Please select at least one product to delete", "confirmDeleteMessage": "Are you sure you want to delete this product?", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected products?", "createProduct": "Create Product", "editProduct": "Edit Product", "productList": "Product List", "productDetails": "Product Details", "productInfo": "Product Information", "productAttributes": "Product Attributes", "productImages": "Product Images", "fields": {"name": "Product Name", "price": "Price", "priceType": "Price Type", "priceTypes": {"yes": "Yes", "no": "No", "other": "Other"}, "regularPrice": "Regular Price", "salePrice": "Sale Price", "basicInfo": "Basic Information", "priceNote": "Price Note", "brand": "Brand", "url": "URL", "description": "Description", "attributes": "Attributes", "attributeName": "Attribute Name", "attributeType": "Data Type", "attributeValue": "Default Value"}, "attributeTypes": {"text": "Text", "number": "Number", "date": "Date", "boolean": "Yes/No", "list": "List"}, "images": {"addImages": "Add Product Images", "image": "Image", "url": "URL", "video": "Video", "uploadImage": "Upload Image", "enterImageUrl": "Enter Image URL", "enterVideoUrl": "Enter Video URL", "recommendedSize": "Recommended size: 800x600px, max 2MB", "addToList": "Add to List", "uploadedImages": "Uploaded Images", "urlImages": "URL Images", "videoList": "Video List", "setCover": "Set as Cover", "coverImage": "Cover Image", "uploadedFromComputer": "Uploaded from computer", "dragAndDrop": "Drag and drop or click to upload product image"}, "actions": {"createProduct": "Create Product", "saveProduct": "Save Product", "view": "View", "deleteProduct": "Delete Product", "cancelCreation": "Cancel"}, "messages": {"productCreated": "Product created successfully", "productUpdated": "Product updated successfully", "productDeleted": "Product deleted successfully", "confirmDelete": "Are you sure you want to delete this product?"}, "basicInfo": "Basic information", "priceInfo": "Price information", "noImages": "No images", "shipmentConfig": "Shipping configuration", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "length": "Length", "weight": "Weight", "noTags": "No tags", "createdBy": "Created by", "name": "Product Name", "typeprice": "Price Type", "notFound": "Product not found", "detail": "Product details", "bulkUpdateSuccess": "Status updated successfully", "bulkUpdateSuccessMessage": "Updated status for {{count}} products", "bulkUpdateError": "Status update error", "bulkUpdateErrorMessage": "Unable to update product status. Please try again.", "bulkUpdateStatus": "Bulk update status", "confirmBulkStatusUpdateMessage": "Select new status for {{count}} selected products:", "selectStatus": "Select status", "rejectReason": "Rejection reason", "rejectReasonPlaceholder": "Enter rejection reason...", "approveProducts": "Approve Products", "rejectProducts": "Reject Products", "approveSuccess": "Approval Successful", "approveSuccessMessage": "Successfully approved {{count}} products", "rejectSuccess": "Rejection Successful", "rejectSuccessMessage": "Successfully rejected {{count}} products", "approveError": "Approval Error", "rejectError": "Rejection Error", "approveErrorMessage": "Unable to approve products. Please try again.", "rejectErrorMessage": "Unable to reject products. Please try again.", "confirmApproveMessage": "Are you sure you want to approve {{count}} selected products?", "confirmRejectTitle": "Reject Products", "confirmRejectMessage": "Please enter rejection reason for {{count}} selected products:", "rejectReasonRequired": "Please enter rejection reason", "statusTypes": {"APPROVED": "Approved", "PENDING": "Pending approval", "REJECTED": "Rejected"}, "viewForm": {"status": {"active": "Active", "inactive": "Inactive"}, "priceTypes": {"yes": "Has Price", "no": "Free", "other": "Contact"}, "imagesTitle": "Product Images", "noDescription": "No description", "createdAt": "Created At", "updatedAt": "Updated At", "close": "Close", "view": "View"}, "filters": {"all": "All"}, "table": {"image": "Image", "name": "Product Name", "price": "Sale Price", "typePrice": "Price Type", "productType": "Product Type", "status": "Status", "actions": "Actions", "moreActions": "More actions", "selectedItems": "Selected {{count}} items", "clearSelection": "Clear all"}, "update": "Update", "loadError": "Error loading product data"}, "customField": {"title": "Custom Fields", "description": "Manage custom fields", "maxLength": "Max Length", "add": "Add Custom Field", "edit": "Edit Custom Field", "addForm": "Add New Custom Field", "editForm": "Edit Custom Field", "component": "Component Type", "components": {"input": "Input", "textarea": "Textarea", "select": "Select", "checkbox": "Checkbox", "radio": "Radio", "date": "Date", "number": "Number", "file": "File", "multiSelect": "Multi-select"}, "type": "Data Type", "configId": "Config Id", "types": {"text": "Text", "number": "Number", "boolean": "Boolean", "date": "Date", "select": "Select Box", "object": "Object", "array": "Array"}, "name": "Field Name", "label": "Label", "placeholder": "Placeholder", "defaultValue": "Default Value", "options": "Options", "validation": {"minLength": "Min Length", "maxLength": "Max Length", "pattern": "Pattern", "min": "Min Value", "max": "Max Value"}, "configuration": "Configuration", "value": "Value", "fieldTypes": {"text": "Text", "number": "Number", "email": "Email", "phone": "Phone", "date": "Date", "boolean": "Boolean", "select": "Select", "textarea": "Textarea"}, "form": {"componentRequired": "Please select a component type", "labelRequired": "Please enter a label", "typeRequired": "Please select a data type", "idRequired": "Please enter field identifier name", "configIdRequired": "Configuration ID is required", "configIdPlaceholder": "Enter configuration ID", "labelPlaceholder": "Enter display label", "descriptionPlaceholder": "Enter field description", "placeholderPlaceholder": "Enter placeholder text", "defaultValuePlaceholder": "Enter default value", "optionsPlaceholder": "Enter options, comma separated or JSON format", "selectOptionsPlaceholder": "Enter values in Name|Value format, one pair per line. Example:\na|1\nb|2", "booleanDefaultPlaceholder": "Select default value", "dateDefaultPlaceholder": "Select default date", "labelTagRequired": "Please add at least one label", "fieldIdLabel": "Field Identifier Name", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "Display Name", "displayNamePlaceholder": "Enter display name for this field", "displayNameRequired": "Please enter display name", "labelInputPlaceholder": "Enter label and press Enter", "tagsCount": "labels added", "patternSuggestions": "Common pattern suggestions:", "showAdvancedSettings": "Show Advanced Settings", "description": "Description", "placeholder": "Placeholder", "defaultValue": "Default Value", "options": "Options"}, "createSuccess": "Custom field created successfully", "createError": "Error creating custom field", "updateSuccess": "Custom field updated successfully", "updateError": "Error updating custom field", "deleteSuccess": "Custom field deleted successfully", "deleteError": "Error deleting custom field", "batchDeleteSuccess": "{{count}} custom fields deleted successfully", "batchDeleteError": "Error deleting multiple custom fields", "batchDeletePartialSuccess": "Batch delete completed: {{successCount}} successful, {{failureCount}} failed out of {{totalCount}} fields", "loadError": "Error loading custom fields", "booleanValues": {"true": "Yes", "false": "No"}, "patterns": {"email": "Email", "phoneVN": "VN Phone Number", "phoneIntl": "International Phone", "postalCodeVN": "VN Postal Code", "lettersOnly": "Letters Only", "numbersOnly": "Numbers Only", "alphanumeric": "Letters & Numbers", "noSpecialChars": "No Special Characters", "url": "URL", "ipv4": "IPv4", "strongPassword": "Strong Password", "vietnameseName": "Vietnamese Name", "studentId": "Student ID", "nationalId": "National ID", "taxCode": "Tax Code", "dateFormat": "Date (dd/mm/yyyy)", "timeFormat": "Time (hh:mm)", "hexColor": "Hex Color", "base64": "Base64", "uuid": "UUID", "filename": "Filename", "urlSlug": "URL Slug", "variableName": "Variable Name", "creditCard": "Credit Card Number", "qrCode": "QR Code", "gpsCoordinate": "GPS Coordinate", "rgbColor": "RGB Color", "domain": "Domain Name", "decimal": "Decimal Number", "barcode": "Barcode"}, "confirmDeleteMessage": "Are you sure you want to delete this custom field?", "table": {"name": "Field Name", "component": "Component", "type": "Type", "required": "Required", "createdAt": "Created At", "status": "Status", "actions": "Actions", "moreActions": "More actions"}, "filters": {"all": "All"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "status": {"status": "Status", "active": "Active", "approved": "Approved", "pending": "Pending", "inactive": "Inactive"}, "required": {"required": "Required", "yes": "Yes", "no": "No"}, "confirmDelete": {"title": "Confirm Delete"}, "detail": "Custom Field Details", "notFound": "Custom field not found", "configJson": "Configuration JSON", "noConfigJson": "No configuration JSON", "linkedGroups": "Linked Groups", "createdAt": "Created At", "close": "Close", "save": "Save", "cancel": "Cancel"}, "routes": {"business": "Business Management - Admin", "product": "Product Management - Admin", "conversion": "Conversion Management - Admin", "order": "Order Management - Admin", "warehouse": "Warehouse Management - Admin", "virtualWarehouse": "Virtual Warehouse Management - Admin", "customField": "Custom Field Management - Admin", "warehouseCustomField": "Warehouse Custom Field Management - Admin", "file": "File Management - Admin", "folder": "Folder Management - Admin"}, "businessPage": {"modules": {"product": {"title": "Product Management", "description": "Manage and track user products"}, "conversion": {"title": "Conversion Management", "description": "Track and manage conversion records"}, "order": {"title": "Order Management", "description": "Manage and track user orders"}, "warehouse": {"title": "Warehouse Management", "description": "Manage physical and virtual warehouses"}, "virtualWarehouse": {"title": "Virtual Warehouse Management", "description": "Manage virtual warehouses and system integration"}, "customField": {"title": "Custom Fields", "description": "Manage system custom fields"}, "userCustomer": {"title": "User Customer Management", "description": "View and track user customers", "detailTitle": "Customer Details"}, "warehouseCustomField": {"title": "Warehouse Custom Fields", "description": "Manage warehouse custom fields"}, "file": {"title": "File Management", "description": "Manage system files and documents"}, "folder": {"title": "Folder Management", "description": "Manage system folder structure"}}}, "virtualWarehouse": {"title": "Virtual Warehouses", "description": "Manage virtual warehouses", "manage": "Manage Virtual Warehouses", "warehouseId": "Warehouse ID", "associatedSystem": "Associated System", "purpose": "Purpose", "warehouse": "Warehouse Information", "customFields": "Custom Fields", "name": "Warehouse Name", "type": "Warehouse Type", "status": "Status", "createSuccess": "Virtual warehouse created successfully", "createError": "Error creating virtual warehouse", "updateSuccess": "Virtual warehouse updated successfully", "updateError": "Error updating virtual warehouse", "deleteSuccess": "Virtual warehouse deleted successfully", "deleteError": "Error deleting virtual warehouse", "confirmDeleteMessage": "Are you sure you want to delete this virtual warehouse?", "notFound": "Virtual warehouse not found", "detail": "Virtual Warehouse Details", "basicInfo": "Basic Information", "warehouseInfo": "Warehouse Information", "customFieldsInfo": "Custom Fields", "noCustomFields": "No custom fields", "noAssociatedSystem": "No associated system", "noPurpose": "No purpose specified", "close": "Close", "table": {"warehouseId": "Warehouse ID", "name": "Warehouse Name", "associatedSystem": "Associated System", "purpose": "Purpose", "type": "Type", "status": "Status", "createdAt": "Created At", "actions": "Actions", "moreActions": "More actions"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "types": {"VIRTUAL": "Virtual", "PHYSICAL": "Physical"}, "statusTypes": {"ACTIVE": "Active", "INACTIVE": "Inactive"}}, "customGroupForm": {"title": "Custom Field Groups", "description": "Manage custom field groups", "createSuccess": "Custom field group created successfully", "createError": "Error creating custom field group", "updateSuccess": "Custom field group updated successfully", "updateError": "Error updating custom field group", "deleteSuccess": "Custom field group deleted successfully", "deleteError": "Error deleting custom field group", "loadError": "Error loading custom field groups"}, "order": {"title": "Orders", "description": "Manage orders", "createOrder": "Create New Order", "editOrder": "Edit Order", "viewOrder": "View Order Details", "orderNumber": "Order Number", "customerInfo": "Customer Information", "customerName": "Customer Name", "customerEmail": "Email", "customerPhone": "Phone", "customerAddress": "Address", "items": "Order Items", "noItems": "No items in this order", "quantity": "Quantity", "totalAmount": "Total Amount", "status": {"title": "Status", "pending": "Pending", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled", "refunded": "Refunded"}, "paymentMethod": "Payment Method", "paymentMethods": {"cash": "Cash", "creditCard": "Credit Card", "bankTransfer": "Bank Transfer", "digitalWallet": "Digital Wallet"}, "paymentStatus": {"title": "Payment Status", "paid": "Paid", "unpaid": "Unpaid", "partiallyPaid": "Partially Paid"}, "notes": "Notes", "form": {"customerNamePlaceholder": "Enter customer name", "customerEmailPlaceholder": "Enter customer email", "customerPhonePlaceholder": "Enter customer phone", "customerAddressPlaceholder": "Enter customer address", "notesPlaceholder": "Enter notes for this order"}, "createSuccess": "Order created successfully", "createError": "Error creating order", "updateSuccess": "Order updated successfully", "updateError": "Error updating order", "deleteSuccess": "Order deleted successfully", "deleteError": "Error deleting order", "confirmDeleteMessage": "Are you sure you want to delete this order?", "table": {"orderNumber": "Order Number", "customer": "Customer", "totalAmount": "Total Amount", "carrier": "Carrier", "orderDate": "Order Date", "status": "Status", "actions": "Actions", "moreActions": "More actions"}, "filters": {"all": "All", "withShipping": "With Shipping", "withoutShipping": "No Shipping", "pendingShipping": "Pending Shipping", "shipped": "Shipped", "delivered": "Delivered"}, "actions": {"view": "View"}, "detail": "Order Details", "notFound": "Order not found", "source": "Source", "shippingStatus": "Shipping Status", "hasShipping": "Has Shipping", "yes": "Yes", "no": "No", "productInfo": "Product Information", "billInfo": "Bill Information", "subtotal": "Subtotal", "total": "Total", "logisticInfo": "Logistics Information", "carrier": "Carrier", "address": "Address", "notSet": "Not set", "createdAt": "Created At", "updatedAt": "Updated At", "close": "Close", "shipped": "Shipped", "delivered": "Delivered", "stats": {"totalOrders": "Total Orders", "totalOrdersDesc": "Total number of orders in the system", "pendingOrders": "Pending Orders", "pendingOrdersDesc": "Number of orders awaiting processing", "completedOrders": "Completed Orders", "completedOrdersDesc": "Number of completed orders", "cancelledOrders": "Cancelled Orders", "cancelledOrdersDesc": "Number of cancelled orders", "totalRevenue": "Total Revenue", "totalRevenueDesc": "Total revenue from completed orders", "shippedOrders": "Paid Orders", "shippedOrdersDesc": "Number of paid orders", "unpaidOrders": "Unpaid Orders", "unpaidOrdersDesc": "Number of unpaid orders", "withShipping": "With Shipping", "withShippingDesc": "Number of orders requiring shipping", "withoutShipping": "No Shipping", "withoutShippingDesc": "Number of orders without shipping"}}, "conversion": {"title": "Conversions", "description": "Track and manage conversions", "totalConversions": "Total Conversions", "manage": "Manage Conversions", "id": "ID", "customerId": "Customer ID", "userId": "User ID", "type": "Conversion Type", "name": "Name", "source": "Source", "destination": "Destination", "value": "Value", "date": "Date", "status": {"completed": "Completed", "pending": "Pending", "failed": "Failed"}, "detail": "Conversion Details", "conversionInfo": "Conversion Information", "customerInfo": "Customer Information", "conversionType": "Conversion Type", "notes": "Notes", "content": "Content", "campaign": "Campaign", "avatar": "Avatar", "email": "Email", "phone": "Phone", "platform": "Platform", "timezone": "Timezone", "agentId": "Agent ID", "metadata": "<PERSON><PERSON><PERSON>", "tags": "Tags", "primaryEmail": "Primary Email", "secondaryEmail": "Secondary Email", "notFound": "Conversion information not found", "table": {"id": "ID", "customerId": "Customer ID", "userId": "User ID", "type": "Conversion Type", "source": "Source", "createdAt": "Created At", "actions": "Actions", "moreActions": "More actions"}, "filters": {"all": "All"}, "actions": {"view": "View"}, "createdAt": "Created At", "updatedAt": "Updated At", "close": "Close"}, "customer": {"title": "Customers", "description": "Manage customer information", "totalCustomers": "Total Customers", "manage": "Manage Customers", "status": {"active": "Active", "inactive": "Inactive", "blocked": "Blocked"}}, "report": {"title": "Reports", "description": "View business reports", "totalReports": "Total Reports", "view": "View Reports"}, "inventory": {"title": "Inventory", "description": "Manage inventory and stock", "totalItems": "Total Items", "manage": "Manage Inventory", "status": {"inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock"}}, "file": {"detail": "File Details", "notFound": "File not found", "basicInfo": "Basic Information", "name": "File Name", "originalName": "Original Name", "type": "File Type", "size": "Size", "extension": "Extension", "mimeType": "MIME Type", "folderInfo": "Folder Information", "urls": "URLs", "url": "URL", "thumbnailUrl": "Thumbnail URL", "metadata": "<PERSON><PERSON><PERSON>", "noMetadata": "No metadata", "storageKey": "Storage Key", "types": {"image": "Image", "video": "Video", "document": "Document", "audio": "Audio", "other": "Other"}, "status": {"active": "Active", "inactive": "Inactive"}, "table": {"name": "File Name", "size": "Size", "folder": "Folder", "createdAt": "Created At", "actions": "Actions", "moreActions": "More actions"}, "actions": {"view": "View"}, "filters": {"all": "All", "image": "Image", "video": "Video", "document": "Document", "audio": "Audio", "other": "Other"}, "loadError": "Error loading file data", "notSet": "Not set"}, "folder": {"detail": "Folder Details", "notFound": "Folder not found", "basicInfo": "Basic Information", "name": "Folder Name", "parentId": "Parent ID", "description": "Description", "pathInfo": "Path Information", "path": "Path", "breadcrumbs": "Full Path", "noBreadcrumbs": "Root folder", "parentInfo": "Parent Folder Information", "parentName": "Parent Name", "parentPath": "Parent Path", "ownerInfo": "Owner Information", "ownerId": "Owner ID", "ownerName": "Owner Name", "ownerEmail": "Owner <PERSON><PERSON>", "fileCount": "File Count", "subFolderCount": "Subfolder Count", "files": "file(s)", "subFolders": "subfolder(s)", "root": "Root", "filesList": "Files List", "filesInFolder": "Files in this folder will be displayed here", "status": {"active": "Active", "inactive": "Inactive"}, "table": {"name": "Folder Name", "parent": "<PERSON><PERSON>er", "path": "Path", "owner": "Owner", "createdAt": "Created At", "actions": "Actions", "moreActions": "More actions"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive"}, "actions": {"view": "View"}, "rootFolder": "Root Folder", "unknownOwner": "Unknown", "loadError": "Error loading folder data"}, "warehouse": {"title": "Warehouses", "description": "Manage warehouses", "name": "Warehouse Name", "code": "Warehouse Code", "desc": "Description", "type": "Warehouse Type", "types": {"PHYSICAL": "Physical Warehouse", "VIRTUAL": "Virtual Warehouse"}, "status": "Status", "address": "Address", "contact": "Contact Information", "add": "Add Warehouse", "edit": "Edit Warehouse", "addForm": "Add New Warehouse", "editForm": "Edit Warehouse Information", "createSuccess": "Warehouse created successfully", "updateSuccess": "Warehouse updated successfully", "deleteSuccess": "Warehouse deleted successfully", "createError": "Error creating warehouse", "updateError": "Error updating warehouse", "deleteError": "Error deleting warehouse", "errors": {"nameExists": "Warehouse name already exists"}, "confirmDeleteMessage": "Are you sure you want to delete this warehouse?", "form": {"namePlaceholder": "Enter warehouse name", "descriptionPlaceholder": "Enter warehouse description", "typePlaceholder": "Select warehouse type", "selectType": "Select warehouse type"}, "detail": "Warehouse details", "basicInfo": "Basic information", "customFieldsInfo": "Custom fields information", "capacity": "Capacity", "customFields": "Custom fields", "noCustomFields": "No custom fields", "fieldLabel": "Field label", "fieldType": "Field type", "fieldValue": "Field value", "notFound": "Warehouse not found", "id": "ID", "noDescription": "No description", "notSet": "Not set", "close": "Close", "table": {"id": "ID", "name": "Warehouse Name", "description": "Description", "address": "Address", "capacity": "Capacity", "actions": "Actions", "moreActions": "More actions"}, "filters": {"all": "All", "highCapacity": "High Capacity", "lowCapacity": "Low Capacity"}, "actions": {"view": "View"}}, "warehouseCustomField": {"title": "Warehouse custom fields", "description": "Manage warehouse custom fields", "detail": "Warehouse custom field details", "basicInfo": "Basic information", "fieldInfo": "Field information", "currentValue": "Current value", "warehouseId": "Warehouse ID", "fieldId": "Field ID", "warehouseName": "Warehouse name", "fieldLabel": "Field label", "notFound": "Warehouse custom field not found", "bulkUpdateStatus": "Update status", "yes": "Yes", "no": "No", "notSet": "Not set", "close": "Close", "loadError": "Error loading warehouse custom field data", "table": {"id": "ID", "name": "Field Name", "label": "Field Label", "value": "Value", "warehouseId": "Warehouse ID", "actions": "Actions", "moreActions": "More actions"}, "filters": {"all": "All warehouses", "text": "Text fields", "number": "Number fields", "boolean": "Boolean fields", "date": "Date fields"}, "actions": {"view": "View"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "back": "Back", "next": "Next", "submit": "Submit", "search": "Search", "filter": "Filter", "sort": "Sort", "add": "Add", "remove": "Remove", "upload": "Upload", "download": "Download", "view": "View", "details": "Details", "actions": "Actions", "active": "Active", "inactive": "Inactive", "all": "All", "enter": "Enter", "select": "Select", "and": "and", "pressEnter": "press Enter", "yes": "Yes", "no": "No", "close": "Close", "name": "Name", "createdAt": "Created At", "updatedAt": "Updated At", "noDescription": "No description", "notSet": "Not set", "selectedItems": "Selected {{count}} items", "clearSelection": "Clear all", "update": "Update", "timeInfo": "Time Information", "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending"}}}}}