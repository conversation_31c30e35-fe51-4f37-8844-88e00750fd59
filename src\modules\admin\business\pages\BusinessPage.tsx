import React from 'react';
import { useTranslation } from 'react-i18next';
import { ResponsiveGrid } from '@/shared/components/common';
import { ModuleCard } from '@/modules/components/card';

/**
 * Trang tổng quan về module Business cho Admin
 */
const BusinessPage: React.FC = () => {
  const { t } = useTranslation(['admin']);
  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Quản lý sản phẩm người dùng */}
        <ModuleCard
          title={t('admin:business.businessPage.modules.product.title')}
          description={t('admin:business.businessPage.modules.product.description')}
          icon="box"
          linkTo="/admin/business/product"
        />

        {/* Quản lý chuyển đổi */}
        <ModuleCard
          title={t('admin:business.businessPage.modules.conversion.title')}
          description={t('admin:business.businessPage.modules.conversion.description')}
          icon="refresh-cw"
          linkTo="/admin/business/conversion"
        />

        {/* Quản lý đơn hàng */}
        <ModuleCard
          title={t('admin:business.businessPage.modules.order.title')}
          description={t('admin:business.businessPage.modules.order.description')}
          icon="shopping-cart"
          linkTo="/admin/business/order"
        />

        {/* Quản lý kho */}
        <ModuleCard
          title={t('admin:business.businessPage.modules.warehouse.title')}
          description={t('admin:business.businessPage.modules.warehouse.description')}
          icon="package"
          linkTo="/admin/business/warehouse"
        />

        {/* Trường tùy chỉnh */}
        <ModuleCard
          title={t('admin:business.businessPage.modules.customField.title')}
          description={t('admin:business.businessPage.modules.customField.description')}
          icon="database"
          linkTo="/admin/business/custom-field"
        />

        {/* Quản lý khách hàng của người dùng */}
        <ModuleCard
          title={t('admin:business.businessPage.modules.userCustomer.title')}
          description={t('admin:business.businessPage.modules.userCustomer.description')}
          icon="user"
          linkTo="/admin/business/user-customer"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default BusinessPage;
