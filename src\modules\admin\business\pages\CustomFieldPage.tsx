import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ConfirmDeleteModal, ActionMenu, Chip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useCustomFields, useDeleteCustomField } from '../hooks/useCustomFieldQuery';
import { CustomFieldQueryParams, CustomFieldListItem } from '../services/custom-field.service';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import CustomFieldForm from '../components/forms/CustomFieldForm';
import EditCustomFieldForm from '../components/forms/EditCustomFieldForm';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang quản lý trường tùy chỉnh
 */
const CustomFieldPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho form và modal
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [selectedFieldId, setSelectedFieldId] = useState<number | null>(null);
  const [formMode, setFormMode] = useState<'add' | 'edit'>('add');

  // State cho table selection và bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);


  // Xử lý chỉnh sửa
  const handleEdit = useCallback((id: number) => {
    setSelectedFieldId(id);
    setFormMode('edit');
    showForm();
  }, [setSelectedFieldId, setFormMode, showForm]);



  // Định nghĩa cột cho bảng
  const columns: TableColumn<CustomFieldListItem>[] = useMemo(
    () => [
      {
        key: 'label',
        title: t('admin:business.customField.name'),
        dataIndex: 'label',
        sortable: true,
      },
      {
        key: 'configId',
        title: t('admin:business.customField.configId'),
        dataIndex: 'configId',
        sortable: true,
      },
      {
        key: 'type',
        title: t('admin:business.customField.type'),
        dataIndex: 'type',
        render: (value: unknown) => {
          const type = value as string;
          // Sử dụng đa ngôn ngữ cho kiểu dữ liệu
          const typeTranslationKey = `admin:business.customField.types.${type}`;
          return t(typeTranslationKey, type);
        },
        sortable: true,
      },
      {
        key: 'required',
        title: t('admin:business.customField.table.required'),
        dataIndex: 'required',
        render: (value: unknown) => ((value as boolean) ? t('admin:business.customField.required.yes') : t('admin:business.customField.required.no')),
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('admin:business.customField.table.createdAt'),
        dataIndex: 'createAt',
        render: (value: unknown) => {
          if (!value) return '-';
          try {
            const timestamp = value as string;
            // Kiểm tra xem timestamp có phải là số hợp lệ không
            if (/^\d+$/.test(timestamp)) {
              // Là timestamp dạng số
              const date = new Date(Number(timestamp));
              if (!isNaN(date.getTime())) {
                return format(date, 'dd/MM/yyyy HH:mm', { locale: vi });
              }
            }
            // Nếu không phải timestamp dạng số, thử parse như date string
            const date = new Date(timestamp);
            if (!isNaN(date.getTime())) {
              return format(date, 'dd/MM/yyyy HH:mm', { locale: vi });
            }
            return String(value);
          } catch {
            return String(value);
          }
        },
        sortable: true,
      },
      {
        key: 'status',
        title: t('admin:business.customField.table.status'),
        dataIndex: 'status',
        render: (value: unknown) => {
          const status = value as string;
          const statusMap: Record<string, { text: string; variant: 'success' | 'warning' | 'danger' | 'default' }> = {
            'ACTIVE': { text: t('admin:business.customField.status.active'), variant: 'success' },
            'APPROVED': { text: t('admin:business.customField.status.approved'), variant: 'success' },
            'PENDING': { text: t('admin:business.customField.status.pending'), variant: 'warning' },
            'INACTIVE': { text: t('admin:business.customField.status.inactive'), variant: 'danger' },
          };

          const statusInfo = statusMap[status];
          return (
            <Chip
              size="sm"
              variant={statusInfo?.variant || 'default'}
            >
              {statusInfo?.text || status}
            </Chip>
          );
        },
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '10%',
        render: (_, record) => {
          // Chỉ có action edit, bỏ delete action
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record.id),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEdit]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): CustomFieldQueryParams => {
    const queryParams: CustomFieldQueryParams = {
      page: params.page,
      limit: params.pageSize,
      ...(params.searchTerm && { search: params.searchTerm }),
      ...(params.sortBy && { sortBy: params.sortBy }),
      ...(params.sortDirection && { sortDirection: params.sortDirection }),
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.type = params.filterValue as string;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<CustomFieldListItem, CustomFieldQueryParams>({
      columns,
      filterOptions: [
        { id: 'all', label: t('admin:business.customField.filters.all'), icon: 'list', value: 'all' },
        { id: 'text', label: t('admin:business.customField.types.text'), icon: 'text', value: 'text' },
        { id: 'number', label: t('admin:business.customField.types.number'), icon: 'hash', value: 'number' },
        { id: 'boolean', label: t('admin:business.customField.types.boolean'), icon: 'toggle-left', value: 'boolean' },
        { id: 'date', label: t('admin:business.customField.types.date'), icon: 'calendar', value: 'date' },
        { id: 'select', label: t('admin:business.customField.types.select'), icon: 'list', value: 'select' },
        { id: 'object', label: t('admin:business.customField.types.object'), icon: 'box', value: 'object' },
        { id: 'array', label: t('admin:business.customField.types.array'), icon: 'layers', value: 'array' },
      ],
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách trường tùy chỉnh
  const { data: customFieldsData, isLoading } = useCustomFields(dataTable.queryParams);

  // Mutation để xóa trường tùy chỉnh
  const deleteCustomFieldMutation = useDeleteCustomField();

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback((column: string | null, order: SortOrder | null) => {
    // Nếu column hoặc order là null, reset sort
    if (column === null || order === null) {
      dataTable.tableData.handleSortChange(null, null);
      return;
    }

    dataTable.tableData.handleSortChange(column, order as SortOrder);
  }, [dataTable.tableData]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      text: t('admin:business.customField.types.text'),
      number: t('admin:business.customField.types.number'),
      boolean: t('admin:business.customField.types.boolean'),
      date: t('admin:business.customField.types.date'),
      select: t('admin:business.customField.types.select'),
      object: t('admin:business.customField.types.object'),
      array: t('admin:business.customField.types.array'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    setSelectedFieldId(null);
    setFormMode('add');
    showForm();
  };



  // Xử lý submit form
  const handleSubmit = () => {
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    setSelectedFieldId(null);
    setFormMode('add');
    hideForm();
  };

  // Xử lý thay đổi selection
  const handleSelectionChange = useCallback((selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  }, []);

  // Xử lý hiển thị modal xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('admin:business.customField.selectToDelete', 'Vui lòng chọn ít nhất một trường tùy chỉnh để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys.length, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(() => {
    if (selectedRowKeys.length === 0) return;

    const idsToDelete = selectedRowKeys.map(key => Number(key));
    deleteCustomFieldMutation.mutate(idsToDelete, {
      onSuccess: () => {
        setShowBulkDeleteConfirm(false);
        setSelectedRowKeys([]);
      },
    });
  }, [selectedRowKeys, deleteCustomFieldMutation]);

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={selectedRowKeys.length > 0 ? [
          {
            icon: 'trash',
            tooltip: `${t('admin:business.customField.bulkDelete', 'Xóa nhiều')} (${selectedRowKeys.length})`,
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
          },
        ] : []}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        {formMode === 'edit' && selectedFieldId ? (
          <EditCustomFieldForm
            id={selectedFieldId}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        ) : (
          <CustomFieldForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        )}
      </SlideInForm>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('admin:business.customField.confirmDelete.title', 'Xác nhận xóa')}
        message={t('admin:business.customField.confirmBulkDeleteMessage', `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} trường tùy chỉnh đã chọn?`)}
        isSubmitting={deleteCustomFieldMutation.isPending}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={customFieldsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectionChange,
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: customFieldsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: customFieldsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default CustomFieldPage;
