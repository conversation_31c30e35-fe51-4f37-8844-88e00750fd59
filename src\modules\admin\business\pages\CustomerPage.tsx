import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';

// Enum cho trạng thái khách hàng
enum CustomerStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BLOCKED = 'blocked',
}

// Interface cho dữ liệu khách hàng
interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  status: CustomerStatus;
  totalOrders: number;
  totalSpent: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Trang quản lý khách hàng
 */
const CustomerPage: React.FC = () => {
  const { t } = useTranslation('business');

  // State cho filter
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Mock data
  const customerData = useMemo<Customer[]>(
    () => [
      {
        id: '1',
        name: 'Nguyễn Văn A',
        email: '<EMAIL>',
        phone: '0901234567',
        address: 'Hà Nội',
        status: CustomerStatus.ACTIVE,
        totalOrders: 10,
        totalSpent: 15000000,
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
      },
      {
        id: '2',
        name: 'Trần Thị B',
        email: '<EMAIL>',
        phone: '0901234568',
        address: 'Hồ Chí Minh',
        status: CustomerStatus.INACTIVE,
        totalOrders: 5,
        totalSpent: 7500000,
        createdAt: '2023-01-02',
        updatedAt: '2023-01-02',
      },
      {
        id: '3',
        name: 'Lê Văn C',
        email: '<EMAIL>',
        phone: '0901234569',
        address: 'Đà Nẵng',
        status: CustomerStatus.BLOCKED,
        totalOrders: 0,
        totalSpent: 0,
        createdAt: '2023-01-03',
        updatedAt: '2023-01-03',
      },
      {
        id: '4',
        name: 'Phạm Thị D',
        email: '<EMAIL>',
        phone: '0901234570',
        address: 'Cần Thơ',
        status: CustomerStatus.ACTIVE,
        totalOrders: 15,
        totalSpent: 25000000,
        createdAt: '2023-01-04',
        updatedAt: '2023-01-04',
      },
    ],
    []
  );

  // Định nghĩa cột cho bảng
  const columns: TableColumn<Customer>[] = [
    {
      key: 'name',
      title: 'Tên khách hàng',
      dataIndex: 'name',
      sortable: true,
    },
    {
      key: 'email',
      title: 'Email',
      dataIndex: 'email',
      sortable: true,
    },
    {
      key: 'phone',
      title: 'Số điện thoại',
      dataIndex: 'phone',
      sortable: true,
    },
    {
      key: 'address',
      title: 'Địa chỉ',
      dataIndex: 'address',
      sortable: true,
    },
    {
      key: 'totalOrders',
      title: 'Tổng đơn hàng',
      dataIndex: 'totalOrders',
      sortable: true,
    },
    {
      key: 'totalSpent',
      title: 'Tổng chi tiêu',
      dataIndex: 'totalSpent',
      render: (value: unknown) => {
        return new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND',
        }).format(value as number);
      },
      sortable: true,
    },
    {
      key: 'status',
      title: t('common.status'),
      dataIndex: 'status',
      render: (value: unknown) => {
        const status = value as CustomerStatus;
        const statusMap = {
          [CustomerStatus.ACTIVE]: { text: t('customer.status.active'), color: 'success' },
          [CustomerStatus.INACTIVE]: { text: t('customer.status.inactive'), color: 'warning' },
          [CustomerStatus.BLOCKED]: { text: t('customer.status.blocked'), color: 'danger' },
        };

        return <span className={`text-${statusMap[status].color}`}>{statusMap[status].text}</span>;
      },
      sortable: true,
    },
    {
      key: 'actions',
      title: t('common.actions'),
      render: (_, record) => {
        return (
          <div className="flex space-x-2">
            <Tooltip content={t('common.view')}>
              <IconCard
                icon="eye"
                variant="ghost"
                size="sm"
                onClick={() => console.log('View', record.id)}
              />
            </Tooltip>
            <Tooltip content={t('common.edit')}>
              <IconCard
                icon="edit"
                variant="ghost"
                size="sm"
                onClick={() => console.log('Edit', record.id)}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // Lọc dữ liệu
  const filteredData = useMemo(() => {
    return customerData.filter((customer: Customer) => {
      const matchesSearch =
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.address.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filter === 'all' || customer.status === filter;
      return matchesSearch && matchesFilter;
    });
  }, [customerData, searchTerm, filter]);

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('Form values:', values);
    // Xử lý thêm khách hàng
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={setSearchTerm}
        onAdd={handleAdd}
        items={[
          {
            id: 'all',
            label: t('common.all'),
            icon: 'list',
            onClick: () => setFilter('all'),
          },
          {
            id: 'active',
            label: t('customer.status.active'),
            icon: 'check',
            onClick: () => setFilter(CustomerStatus.ACTIVE),
          },
          {
            id: 'inactive',
            label: t('customer.status.inactive'),
            icon: 'eye-off',
            onClick: () => setFilter(CustomerStatus.INACTIVE),
          },
          {
            id: 'blocked',
            label: t('customer.status.blocked'),
            icon: 'slash',
            onClick: () => setFilter(CustomerStatus.BLOCKED),
          },
        ]}
      />

      <SlideInForm isVisible={isVisible}>
        <Card title="Thêm khách hàng mới">
          <div className="p-4">
            <p>Form thêm khách hàng mới</p>
            <div className="flex justify-end mt-4">
              <button className="px-4 py-2 bg-gray-200 rounded mr-2" onClick={handleCancel}>
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 bg-primary text-white rounded"
                onClick={() => handleSubmit({})}
              >
                {t('common.save')}
              </button>
            </div>
          </div>
        </Card>
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table columns={columns} data={filteredData} rowKey="id" pagination />
      </Card>
    </div>
  );
};

export default CustomerPage;
