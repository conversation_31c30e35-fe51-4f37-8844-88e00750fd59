import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, Chip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import { formatTimestamp } from '@/shared/utils/date';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';
import ListOverviewCard from '@/shared/components/widgets/ListOverviewCard/ListOverviewCard';
import { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';
import {
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle,
  DollarSign,
  Truck,
  Package
} from 'lucide-react';

import { useOrders, useDeleteOrder, useOrderStats } from '../hooks/useOrderQuery';
import { Order, OrderStatus, OrderQueryParams, OrderListItem } from '../services/order.service';
import OrderForm from '../components/forms/OrderForm';

/**
 * Trang quản lý đơn hàng
 */
const OrdersPage: React.FC = () => {
  const { t, i18n } = useTranslation(['business', 'common', 'admin']);

  // Helper function để lấy locale config theo ngôn ngữ hiện tại
  const getLocaleConfig = useCallback(() => {
    switch (i18n.language) {
      case 'en':
        return { locale: 'en-US', currency: 'USD' };
      case 'zh':
        return { locale: 'zh-CN', currency: 'CNY' };
      default:
        return { locale: 'vi-VN', currency: 'VND' };
    }
  }, [i18n.language]);

  // State cho form và modal
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Định nghĩa các tùy chọn filter
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), value: 'all', icon: 'list' },
      {
        id: 'with-shipping',
        label: t('business:order.filters.withShipping'),
        value: 'with-shipping',
        icon: 'truck',
      },
      {
        id: 'without-shipping',
        label: t('business:order.filters.withoutShipping'),
        value: 'without-shipping',
        icon: 'package',
      },
      {
        id: 'pending-shipping',
        label: t('business:order.filters.pendingShipping'),
        value: 'pending-shipping',
        icon: 'clock',
      },
      {
        id: 'shipped',
        label: t('business:order.filters.shipped'),
        value: 'shipped',
        icon: 'truck',
      },
      {
        id: 'delivered',
        label: t('business:order.filters.delivered'),
        value: 'delivered',
        icon: 'check-circle',
      },
      // Legacy status filters for backward compatibility
      {
        id: 'pending',
        label: t('business:order.status.pending'),
        value: OrderStatus.PENDING,
        icon: 'clock',
      },
      {
        id: 'processing',
        label: t('business:order.status.processing'),
        value: OrderStatus.PREPARING,
        icon: 'loader',
      },
      {
        id: 'completed',
        label: t('business:order.status.completed'),
        value: OrderStatus.COMPLETED,
        icon: 'check-circle',
      },
      {
        id: 'cancelled',
        label: t('business:order.status.cancelled'),
        value: OrderStatus.CANCELLED,
        icon: 'x-circle',
      },
    ],
    [t]
  );

  // Xử lý xem chi tiết đơn hàng
  const handleView = useCallback(
    (order: OrderListItem) => {
      setSelectedOrder(order as unknown as Order);
      showDetailForm();
    },
    [showDetailForm]
  );

  // Xử lý xóa đơn hàng
  const handleDelete = useCallback((order: OrderListItem) => {
    setSelectedOrder(order as unknown as Order);
    setIsDeleteModalOpen(true);
  }, []);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<OrderListItem>[]>(
    () => [
      {
        key: 'orderNumber',
        title: t('business:order.orderNumber'),
        dataIndex: 'orderNumber',
        sortable: true,
        width: '15%',
      },
      {
        key: 'customerName',
        title: t('business:order.customerName'),
        dataIndex: 'customerName',
        sortable: true,
        width: '20%',
      },
      {
        key: 'totalAmount',
        title: t('business:order.totalAmount'),
        dataIndex: 'totalAmount',
        sortable: true,
        width: '15%',
        render: (value: unknown) => {
          const amount = value as number;
          const { locale, currency } = getLocaleConfig();

          return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency,
          }).format(amount);
        },
      },
      {
        key: 'status',
        title: t('business:order.status.title'),
        dataIndex: 'status',
        sortable: true,
        width: '15%',
        render: (value: unknown) => {
          const status = value as OrderStatus;
          let chipVariant: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default' = 'default';
          let label = '';

          switch (status) {
            case OrderStatus.PENDING:
              chipVariant = 'warning';
              label = t('business:order.status.pending');
              break;
            case OrderStatus.PREPARING:
              chipVariant = 'primary';
              label = t('business:order.status.processing');
              break;
            case OrderStatus.COMPLETED:
              chipVariant = 'success';
              label = t('business:order.status.completed');
              break;
            case OrderStatus.CANCELLED:
              chipVariant = 'danger';
              label = t('business:order.status.cancelled');
              break;
            case OrderStatus.REFUNDED:
              chipVariant = 'info';
              label = t('business:order.status.refunded');
              break;
            default:
              label = String(status);
          }

          return <Chip variant={chipVariant}>{label}</Chip>;
        },
      },
      {
        key: 'paymentStatus',
        title: t('business:order.paymentStatus.title'),
        dataIndex: 'paymentStatus',
        sortable: true,
        width: '15%',
        render: (value: unknown) => {
          const paymentStatus = value as 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
          let chipVariant: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default' = 'default';
          let label = '';

          switch (paymentStatus) {
            case 'PAID':
              chipVariant = 'success';
              label = t('business:order.paymentStatus.paid');
              break;
            case 'UNPAID':
              chipVariant = 'danger';
              label = t('business:order.paymentStatus.unpaid');
              break;
            case 'PARTIALLY_PAID':
              chipVariant = 'warning';
              label = t('business:order.paymentStatus.partiallyPaid');
              break;
            default:
              label = String(paymentStatus);
          }

          return <Chip variant={chipVariant}>{label}</Chip>;
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt'),
        dataIndex: 'createdAt',
        sortable: true,
        width: '15%',
        render: (value: unknown) => formatTimestamp(value as number),
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: OrderListItem) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleView(record),
            },
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => handleDelete(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleView, handleDelete, getLocaleConfig]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedOrder(null);
  }, []);

  // Mutation để xóa đơn hàng
  const { mutateAsync: deleteOrder } = useDeleteOrder();

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!selectedOrder) return;

    try {
      // Gọi API xóa đơn hàng
      await deleteOrder(selectedOrder.id);

      // Đóng popup
      setIsDeleteModalOpen(false);
      setSelectedOrder(null);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:order.deleteSuccess'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting order:', error);
      NotificationUtil.error({
        message: t('business:order.deleteError'),
        duration: 3000,
      });
    }
  }, [selectedOrder, deleteOrder, t]);

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
    hideDetailForm();
    setSelectedOrder(null);
  };

  // Xử lý submit form thêm mới
  const handleSubmit = async (values: Record<string, unknown>) => {
    console.log('Form values:', values);
    // Xử lý thêm đơn hàng (sẽ triển khai sau)
    hideAddForm();
  };

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback((column: string | null, order: SortOrder) => {
    const sortDirection = order === 'asc' ? SortDirection.ASC : SortDirection.DESC;
    return { sortBy: column, sortDirection };
  }, []);

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): OrderQueryParams => {
    const queryParams: OrderQueryParams = {
      page: params.page,
      limit: params.pageSize,
    };

    // Chỉ thêm các property khi có giá trị thực sự
    if (params.searchTerm) {
      queryParams.search = params.searchTerm;
    }
    if (params.sortBy) {
      queryParams.sortBy = params.sortBy;
    }
    if (params.sortDirection) {
      queryParams.sortDirection = params.sortDirection;
    }

    // Xử lý filter theo API mới
    if (params.filterValue !== 'all') {
      switch (params.filterValue) {
        case 'with-shipping':
          queryParams.hasShipping = true;
          break;
        case 'without-shipping':
          queryParams.hasShipping = false;
          break;
        case 'pending-shipping':
          queryParams.shippingStatus = 'pending';
          break;
        case 'shipped':
          queryParams.shippingStatus = 'shipped';
          break;
        case 'delivered':
          queryParams.shippingStatus = 'delivered';
          break;
        default:
          // Fallback cho legacy status filter
          if (typeof params.filterValue === 'string' && Object.values(OrderStatus).includes(params.filterValue as OrderStatus)) {
            queryParams.status = params.filterValue as OrderStatus;
          }
          break;
      }
    }

    // Xử lý dateRange nếu có
    if (params.dateRange[0] && params.dateRange[1]) {
      queryParams.createdAtFrom = params.dateRange[0].getTime();
      queryParams.createdAtTo = params.dateRange[1].getTime();
      // Giữ legacy fields cho backward compatibility
      queryParams.fromDate = params.dateRange[0].toISOString().split('T')[0];
      queryParams.toDate = params.dateRange[1].toISOString().split('T')[0];
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<OrderListItem, OrderQueryParams>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách đơn hàng với queryParams từ dataTable
  const { data: orderData, isLoading } = useOrders(dataTable.queryParams);

  // Gọi API lấy thống kê đơn hàng
  const { data: statsData, isLoading: isStatsLoading } = useOrderStats();

  // Tạo dữ liệu cho các card thống kê
  const overviewCards = useMemo<OverviewCardProps[]>(() => {
    if (!statsData) return [];

    const { locale, currency } = getLocaleConfig();

    return [
      {
        title: t('business:order.stats.totalOrders'),
        value: statsData.totalOrders.toLocaleString(locale),
        description: t('business:order.stats.totalOrdersDesc'),
        icon: ShoppingCart,
        color: 'blue',
        isLoading: isStatsLoading,
      },
      {
        title: t('business:order.stats.pendingOrders'),
        value: statsData.pendingOrders.toLocaleString(locale),
        description: t('business:order.stats.pendingOrdersDesc'),
        icon: Clock,
        color: 'orange',
        isLoading: isStatsLoading,
      },
      {
        title: t('business:order.stats.completedOrders'),
        value: statsData.completedOrders.toLocaleString(locale),
        description: t('business:order.stats.completedOrdersDesc'),
        icon: CheckCircle,
        color: 'green',
        isLoading: isStatsLoading,
      },
      {
        title: t('business:order.stats.cancelledOrders'),
        value: statsData.cancelledOrders.toLocaleString(locale),
        description: t('business:order.stats.cancelledOrdersDesc'),
        icon: XCircle,
        color: 'red',
        isLoading: isStatsLoading,
      },
      {
        title: t('business:order.stats.totalRevenue'),
        value: new Intl.NumberFormat(locale, {
          style: 'currency',
          currency: currency,
          notation: 'compact',
          maximumFractionDigits: 1,
        }).format(statsData.totalRevenue),
        description: t('business:order.stats.totalRevenueDesc'),
        icon: DollarSign,
        color: 'purple',
        isLoading: isStatsLoading,
      },
      {
        title: t('business:order.stats.withShipping'),
        value: statsData.withShipping.toLocaleString(locale),
        description: t('business:order.stats.withShippingDesc'),
        icon: Truck,
        color: 'blue',
        isLoading: isStatsLoading,
      },
      {
        title: t('business:order.stats.withoutShipping'),
        value: statsData.withoutShipping.toLocaleString(locale),
        description: t('business:order.stats.withoutShippingDesc'),
        icon: Package,
        color: 'gray',
        isLoading: isStatsLoading,
      },
    ];
  }, [statsData, isStatsLoading, t, getLocaleConfig]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      // Shipping filters
      'with-shipping': t('business:order.filters.withShipping'),
      'without-shipping': t('business:order.filters.withoutShipping'),
      'pending-shipping': t('business:order.filters.pendingShipping'),
      'shipped': t('business:order.filters.shipped'),
      'delivered': t('business:order.filters.delivered'),
      // Legacy status filters
      [OrderStatus.PENDING]: t('business:order.status.pending'),
      [OrderStatus.PREPARING]: t('business:order.status.processing'),
      [OrderStatus.COMPLETED]: t('business:order.status.completed'),
      [OrderStatus.CANCELLED]: t('business:order.status.cancelled'),
      [OrderStatus.REFUNDED]: t('business:order.status.refunded'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Card thống kê tổng quan */}
      <div className="mb-6">
        <ListOverviewCard
          items={overviewCards}
          isLoading={isStatsLoading}
          skeletonCount={9}
          maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 4 }}
        />
      </div>

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <OrderForm onSubmit={handleSubmit} onCancel={handleCancel} isSubmitting={false} />
      </SlideInForm>

      {/* Form chi tiết */}
      <SlideInForm isVisible={isDetailFormVisible}>
        <OrderForm
          order={selectedOrder}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={false}
          isViewMode={true}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={orderData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: orderData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: orderData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete')}
        message={t('business:order.confirmDeleteMessage')}
        itemName={selectedOrder?.orderNumber || ''}
      />
    </div>
  );
};

export default OrdersPage;
