import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, Icon, ResponsiveImage, Button, Modal, Textarea, Select, Typography, ActionMenu, StatusBadge } from '@/shared/components/common';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import ActiveFilters from '@/modules/components/filters/ActiveFilters';
import { useProducts, useUpdateProductStatus, useApproveProducts, useRejectProducts } from '../hooks/useProductQuery';
import { ProductDto, UpdateProductStatusDto, ProductTypeEnum, ProductQueryParams, ProductStatus } from '../types/product.types';
import ViewProductForm from '../components/forms/ViewProductForm';

/**
 * Trang quản lý sản phẩm cho Admin
 */
const ProductPage: React.FC = () => {
  const { t } = useTranslation();

  // State cho form và table
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [selectedProductId, setSelectedProductId] = useState<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // State cho batch update status
  const [showBulkStatusUpdateModal, setShowBulkStatusUpdateModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [rejectReason, setRejectReason] = useState<string>('');

  // Hook cho update status
  const updateStatusMutation = useUpdateProductStatus();
  const approveProductsMutation = useApproveProducts();
  const rejectProductsMutation = useRejectProducts();
  const { success, error: showError } = useSmartNotification();

  // Handler functions
  const handleView = useCallback((id: number) => {
    setSelectedProductId(id);
    showForm();
  }, [setSelectedProductId, showForm]);

  const handleCloseForm = useCallback(() => {
    setSelectedProductId(null);
    hideForm();
  }, [setSelectedProductId, hideForm]);

  // Định nghĩa filter options
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('admin:business.product.filters.all', 'Tất cả'), value: 'all' },
      {
        id: 'pending',
        label: t('admin:business.product.status.PENDING', 'Chờ phê duyệt'),
        value: ProductStatus.PENDING,
      },
      {
        id: 'approved',
        label: t('admin:business.product.status.APPROVED', 'Đã phê duyệt'),
        value: ProductStatus.APPROVED,
      },
      {
        id: 'rejected',
        label: t('admin:business.product.status.REJECTED', 'Đã từ chối'),
        value: ProductStatus.REJECTED,
      },
      {
        id: 'active',
        label: t('admin:business.product.status.ACTIVE', 'Hoạt động'),
        value: ProductStatus.ACTIVE,
      },
      {
        id: 'inactive',
        label: t('admin:business.product.status.INACTIVE', 'Không hoạt động'),
        value: ProductStatus.INACTIVE,
      },
      {
        id: 'deleted',
        label: t('admin:business.product.status.DELETED', 'Đã xóa'),
        value: ProductStatus.DELETED,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): ProductQueryParams => {
    const queryParams: ProductQueryParams = {
      page: params.page,
      limit: params.pageSize,
    };

    // Chỉ thêm các property khi có giá trị thực sự
    if (params.searchTerm) {
      queryParams.search = params.searchTerm;
    }
    if (params.sortBy) {
      queryParams.sortBy = params.sortBy;
    }
    if (params.sortDirection) {
      queryParams.sortDirection = params.sortDirection === SortDirection.ASC ? 'ASC' : 'DESC';
    }

    // Thêm filter status nếu không phải 'all'
    if (params.filterValue !== 'all' && params.filterValue) {
      queryParams.status = params.filterValue as string;
    }

    return queryParams;
  };

  // Định nghĩa cột cho bảng
  const columns: TableColumn<ProductDto>[] = useMemo(() => [
    {
      key: 'images',
      title: t('admin:business.product.table.image', 'Hình ảnh'),
      dataIndex: 'images.0.key',
      width: '10%',
      render: (_: unknown, record: ProductDto) => (
        <div className="w-12 h-12 rounded overflow-hidden bg-gray-100">
          {record.images && record.images.length > 0 ? (
            <ResponsiveImage
              src={typeof record.images[0] === 'string' ? record.images[0] : record.images[0]?.url || '/placeholder-image.jpg'}
              alt="Product"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Icon name="image" size="sm" className="text-gray-400" />
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'name',
      title: t('admin:business.product.table.name', 'Tên sản phẩm'),
      dataIndex: 'name',
      sortable: true,
    },
    {
      key: 'price',
      title: t('admin:business.product.table.price', 'Giá bán'),
      dataIndex: 'price.salePrice',
      render: (value: unknown) => {
        return new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND',
        }).format(value as number);
      },
      sortable: true,
    },
    {
      key: 'productType',
      title: t('admin:business.product.table.productType', 'Loại sản phẩm'),
      dataIndex: 'productType',
      render: (value: unknown) => {
        const productType = value as ProductTypeEnum;
        const productTypeMap = {
          [ProductTypeEnum.PHYSICAL]: {
            text: t('admin:business.product.productType.PHYSICAL', 'Sản phẩm vật lý'),
            variant: 'primary' as const
          },
          [ProductTypeEnum.DIGITAL]: {
            text: t('admin:business.product.productType.DIGITAL', 'Sản phẩm số'),
            variant: 'info' as const
          },
          [ProductTypeEnum.SERVICE]: {
            text: t('admin:business.product.productType.SERVICE', 'Dịch vụ'),
            variant: 'success' as const
          },
          [ProductTypeEnum.EVENT]: {
            text: t('admin:business.product.productType.EVENT', 'Sự kiện'),
            variant: 'warning' as const
          },
          [ProductTypeEnum.COMBO]: {
            text: t('admin:business.product.productType.COMBO', 'Combo sản phẩm'),
            variant: 'danger' as const
          },
        };

        const typeInfo = productTypeMap[productType];
        return (
          <StatusBadge
            text={typeInfo?.text || productType}
            variant={typeInfo?.variant || 'info'}
          />
        );
      },
      sortable: true,
    },
    {
      key: 'status',
      title: t('admin:business.product.table.status', 'Trạng thái'),
      dataIndex: 'status',
      render: (value: unknown) => {
        const status = value as ProductStatus;
        const statusMap = {
          [ProductStatus.PENDING]: {
            text: t('admin:business.product.status.PENDING', 'Chờ phê duyệt'),
            variant: 'warning' as const
          },
          [ProductStatus.APPROVED]: {
            text: t('admin:business.product.status.APPROVED', 'Đã phê duyệt'),
            variant: 'success' as const
          },
          [ProductStatus.REJECTED]: {
            text: t('admin:business.product.status.REJECTED', 'Đã từ chối'),
            variant: 'danger' as const
          },
          [ProductStatus.ACTIVE]: {
            text: t('admin:business.product.status.ACTIVE', 'Hoạt động'),
            variant: 'success' as const
          },
          [ProductStatus.INACTIVE]: {
            text: t('admin:business.product.status.INACTIVE', 'Không hoạt động'),
            variant: 'info' as const
          },
          [ProductStatus.DELETED]: {
            text: t('admin:business.product.status.DELETED', 'Đã xóa'),
            variant: 'danger' as const
          },
        };

        const statusInfo = statusMap[status];
        return (
          <StatusBadge
            text={statusInfo?.text || status}
            variant={statusInfo?.variant || 'info'}
          />
        );
      },
      sortable: true,
    },
    {
      key: 'actions',
      title: t('admin:business.product.table.actions', 'Thao tác'),
      width: '10%',
      render: (_: unknown, record: ProductDto) => {
        // Tạo danh sách các action items
        const actionItems: ActionMenuItem[] = [
          {
            id: 'view',
            label: t('admin:business.product.actions.view', 'Xem'),
            icon: 'eye',
            onClick: () => handleView(record.id),
          },
        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip={t('admin:business.product.table.moreActions', 'Thêm hành động')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            showAllInMenu={false}
            preferRight={true}
          />
        );
      },
    },
  ], [t, handleView]);

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ProductDto, ProductQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách sản phẩm với queryParams từ dataTable
  const { data: productData, isLoading } = useProducts(dataTable.queryParams);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [ProductStatus.PENDING]: t('admin:business.product.status.PENDING', 'Chờ phê duyệt'),
      [ProductStatus.APPROVED]: t('admin:business.product.status.APPROVED', 'Đã phê duyệt'),
      [ProductStatus.REJECTED]: t('admin:business.product.status.REJECTED', 'Đã từ chối'),
      [ProductStatus.ACTIVE]: t('admin:business.product.status.ACTIVE', 'Hoạt động'),
      [ProductStatus.INACTIVE]: t('admin:business.product.status.INACTIVE', 'Không hoạt động'),
      [ProductStatus.DELETED]: t('admin:business.product.status.DELETED', 'Đã xóa'),
    },
    t,
  });

  // Handler cho row selection
  const handleSelectionChange = useCallback((keys: React.Key[]) => {
    setSelectedRowKeys(keys);
  }, []);

  // Handler cho batch approve
  const handleBulkApprove = useCallback(async () => {
    try {
      const productIds = selectedRowKeys.map(key => Number(key));
      await approveProductsMutation.mutateAsync(productIds);
      setSelectedRowKeys([]);
      success({
        title: t('admin:business.product.approveSuccess', 'Phê duyệt thành công'),
        message: t('admin:business.product.approveSuccessMessage', 'Đã phê duyệt {{count}} sản phẩm', { count: productIds.length }),
      });
    } catch (err) {
      console.error('Error approving products:', err);
      showError({
        title: t('admin:business.product.approveError', 'Lỗi phê duyệt'),
        message: t('admin:business.product.approveErrorMessage', 'Không thể phê duyệt sản phẩm. Vui lòng thử lại.'),
      });
    }
  }, [selectedRowKeys, approveProductsMutation, success, showError, t]);

  // Handler cho batch reject
  const handleBulkReject = useCallback(async () => {
    if (!rejectReason.trim()) {
      showError({
        title: t('admin:business.product.rejectError', 'Lỗi từ chối'),
        message: t('admin:business.product.rejectReasonRequired', 'Vui lòng nhập lý do từ chối'),
      });
      return;
    }

    try {
      const productIds = selectedRowKeys.map(key => Number(key));
      await rejectProductsMutation.mutateAsync({ productIds, rejectReason });
      setSelectedRowKeys([]);
      setRejectReason('');
      setShowRejectModal(false);
      success({
        title: t('admin:business.product.rejectSuccess', 'Từ chối thành công'),
        message: t('admin:business.product.rejectSuccessMessage', 'Đã từ chối {{count}} sản phẩm', { count: productIds.length }),
      });
    } catch (err) {
      console.error('Error rejecting products:', err);
      showError({
        title: t('admin:business.product.rejectError', 'Lỗi từ chối'),
        message: t('admin:business.product.rejectErrorMessage', 'Không thể từ chối sản phẩm. Vui lòng thử lại.'),
      });
    }
  }, [selectedRowKeys, rejectReason, rejectProductsMutation, success, showError, t]);

  // Handler cho batch update status (giữ lại cho tương thích)
  const handleBulkStatusUpdate = useCallback(async () => {
    if (!selectedStatus) return;

    try {
      const productIds = selectedRowKeys.map(key => Number(key));
      const updateData: UpdateProductStatusDto = {
        productIds,
        status: selectedStatus,
        rejectReason: selectedStatus === 'REJECTED' ? rejectReason : undefined,
      };

      await updateStatusMutation.mutateAsync(updateData);
      setSelectedRowKeys([]);
      setSelectedStatus('');
      setShowBulkStatusUpdateModal(false);
      setRejectReason('');
      success({
        title: t('admin:business.product.bulkUpdateSuccess', 'Thành công'),
        message: t('admin:business.product.bulkUpdateSuccessMessage', 'Đã cập nhật trạng thái cho {{count}} sản phẩm', { count: productIds.length }),
      });
    } catch (err) {
      console.error('Error updating product status:', err);
      showError({
        title: t('admin:business.product.bulkUpdateError', 'Lỗi'),
        message: t('admin:business.product.bulkUpdateErrorMessage', 'Không thể cập nhật trạng thái sản phẩm. Vui lòng thử lại.'),
      });
    }
  }, [selectedRowKeys, selectedStatus, rejectReason, updateStatusMutation, success, showError, t]);







  return (
    <div className="w-full bg-background text-foreground">
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={filterOptions.map(option => {
          let icon = 'list'; // default icon

          switch (option.id) {
            case 'all':
              icon = 'list';
              break;
            case 'pending':
              icon = 'calendar'; // Sử dụng calendar thay cho clock
              break;
            case 'approved':
              icon = 'check';
              break;
            case 'rejected':
              icon = 'x';
              break;
            case 'active':
              icon = 'zap'; // Sử dụng zap để thể hiện hoạt động
              break;
            case 'inactive':
              icon = 'eye-off';
              break;
            case 'deleted':
              icon = 'trash';
              break;
            default:
              icon = 'list';
          }

          return {
            id: option.id,
            label: option.label,
            icon,
            onClick: () => dataTable.filter.setSelectedId(option.id),
          };
        })}
        additionalIcons={[
          {
            icon: 'check',
            tooltip: t('admin:business.product.approveProducts', 'Phê duyệt sản phẩm'),
            variant: 'primary',
            onClick: handleBulkApprove,
            className: 'text-green-500',
            condition: selectedRowKeys.length > 0,
          },
          {
            icon: 'x',
            tooltip: t('admin:business.product.rejectProducts', 'Từ chối sản phẩm'),
            variant: 'primary',
            onClick: () => setShowRejectModal(true),
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Active Filters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />



      {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        {selectedProductId && (
          <ViewProductForm
            id={selectedProductId}
            onClose={handleCloseForm}
          />
        )}
      </SlideInForm>

      {/* Modal từ chối sản phẩm */}
      <Modal
        isOpen={showRejectModal}
        onClose={() => {
          setShowRejectModal(false);
          setRejectReason('');
        }}
        title={t('admin:business.product.confirmRejectTitle', 'Từ chối sản phẩm')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => {
              setShowRejectModal(false);
              setRejectReason('');
            }}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button
              variant="primary"
              onClick={handleBulkReject}
              disabled={!rejectReason.trim()}
            >
              {t('admin:business.product.rejectProducts', 'Từ chối')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-4">
            {t(
              'admin:business.product.confirmRejectMessage',
              'Vui lòng nhập lý do từ chối cho {{count}} sản phẩm đã chọn:',
              { count: selectedRowKeys.length }
            )}
          </Typography>
          <Textarea
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            placeholder={t('admin:business.product.rejectReasonPlaceholder', 'Nhập lý do từ chối...')}
            rows={4}
            required
          />
        </div>
      </Modal>

      {/* Modal cập nhật trạng thái nhiều */}
      <Modal
        isOpen={showBulkStatusUpdateModal}
        onClose={() => setShowBulkStatusUpdateModal(false)}
        title={t('admin:business.product.bulkUpdateStatus', 'Cập nhật trạng thái nhiều sản phẩm')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowBulkStatusUpdateModal(false)}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button
              variant="primary"
              onClick={handleBulkStatusUpdate}
              disabled={!selectedStatus}
            >
              {t('common.save', 'Cập nhật')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-4">
            {t(
              'admin:business.product.confirmBulkStatusUpdateMessage',
              'Chọn trạng thái mới cho {{count}} sản phẩm đã chọn:',
              { count: selectedRowKeys.length }
            )}
          </Typography>
          <Select
            placeholder={t('admin:business.product.selectStatus', 'Chọn trạng thái')}
            value={selectedStatus}
            onChange={(value) => setSelectedStatus(value as string)}
            options={[
              {
                value: 'APPROVED',
                label: t('admin:business.product.status.APPROVED', 'Đã phê duyệt'),
              },
              {
                value: 'PENDING',
                label: t('admin:business.product.status.PENDING', 'Chờ phê duyệt'),
              },
              {
                value: 'REJECTED',
                label: t('admin:business.product.status.REJECTED', 'Đã từ chối'),
              },
            ]}
            fullWidth
          />
          {selectedStatus === 'REJECTED' && (
            <div className="mt-4">
              <Typography className="mb-2">
                {t('admin:business.product.rejectReason', 'Lý do từ chối:')}
              </Typography>
              <Textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                placeholder={t('admin:business.product.rejectReasonPlaceholder', 'Nhập lý do từ chối...')}
                rows={4}
              />
            </div>
          )}
        </div>
      </Modal>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={productData?.items || []}
          rowKey="id"
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectionChange,
          }}
          pagination={{
            current: dataTable.tableData.currentPage,
            pageSize: dataTable.tableData.pageSize,
            total: productData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
          }}
          loading={isLoading}
        />
      </Card>
    </div>
  );
};

export default ProductPage;
