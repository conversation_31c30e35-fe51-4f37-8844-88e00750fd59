import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, StatusBadge } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import { formatTimestamp } from '@/shared/utils/date';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';

import { useProducts, useDeleteProduct, useDeleteMultipleProducts } from '../hooks/useProductQuery';
import { ProductDto, PriceTypeEnum, ProductTypeEnum, ProductQueryParams } from '../types/product.types';
import { ProductForm, ProductEditForm } from '../components';

/**
 * Trang quản lý sản phẩm
 */
const ProductsPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);

  // State cho form và modal
  const [selectedProduct, setSelectedProduct] = useState<ProductDto | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Định nghĩa các tùy chọn filter
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), value: 'all' },
      // Filter theo loại giá
      {
        id: 'hasPrice',
        label: t('business:product.priceType.hasPrice'),
        value: PriceTypeEnum.HAS_PRICE,
      },
      {
        id: 'stringPrice',
        label: t('business:product.priceType.stringPrice'),
        value: PriceTypeEnum.STRING_PRICE,
      },
      {
        id: 'noPrice',
        label: t('business:product.priceType.noPrice'),
        value: PriceTypeEnum.NO_PRICE,
      },
      // Filter theo loại sản phẩm
      {
        id: 'physical',
        label: t('business:product.types.physical.title', 'Sản phẩm vật lý'),
        value: ProductTypeEnum.PHYSICAL,
      },
      {
        id: 'digital',
        label: t('business:product.types.digital.title', 'Sản phẩm số'),
        value: ProductTypeEnum.DIGITAL,
      },
      {
        id: 'service',
        label: t('business:product.types.service.title', 'Dịch vụ'),
        value: ProductTypeEnum.SERVICE,
      },
      {
        id: 'event',
        label: t('business:product.types.event.title', 'Sự kiện'),
        value: ProductTypeEnum.EVENT,
      },
      {
        id: 'combo',
        label: t('business:product.types.combo.title', 'Combo sản phẩm'),
        value: ProductTypeEnum.COMBO,
      },
    ],
    [t]
  );

  // Xử lý xem chi tiết sản phẩm
  const handleView = useCallback(
    (product: ProductDto) => {
      setSelectedProduct(product);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý chỉnh sửa sản phẩm
  const handleEdit = useCallback(
    (product: ProductDto) => {
      setSelectedProduct(product);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý xóa sản phẩm
  const handleDelete = useCallback((product: ProductDto) => {
    setSelectedProduct(product);
    setIsDeleteModalOpen(true);
  }, []);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<ProductDto>[]>(
    () => [
      {
        key: 'name',
        title: t('business:product.name'),
        dataIndex: 'name',
        sortable: true,
        width: '25%',
      },
      {
        key: 'typePrice',
        title: t('business:product.priceType.title'),
        dataIndex: 'typePrice',
        sortable: true,
        width: '15%',
        render: (value: unknown) => {
          const typePriceValue = value as PriceTypeEnum;
          let chipVariant: 'primary' | 'success' | 'warning' | 'danger' | 'info' = 'info';
          let label = '';

          switch (typePriceValue) {
            case PriceTypeEnum.HAS_PRICE:
              chipVariant = 'success';
              label = t('business:product.priceType.hasPrice');
              break;
            case PriceTypeEnum.STRING_PRICE:
              chipVariant = 'warning';
              label = t('business:product.priceType.stringPrice');
              break;
            case PriceTypeEnum.NO_PRICE:
              chipVariant = 'danger';
              label = t('business:product.priceType.noPrice');
              break;
            default:
              label = String(typePriceValue);
          }

          return <StatusBadge text={label} variant={chipVariant} />;
        },
      },
      {
        key: 'tags',
        title: t('business:product.tags'),
        dataIndex: 'tags',
        width: '20%',
        render: (value: unknown) => {
          const tags = value as string[] | undefined;
          if (!tags || tags.length === 0) return '-';
          return tags.join(', ');
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt'),
        dataIndex: 'createdAt',
        sortable: true,
        width: '15%',
        render: (value: unknown) => formatTimestamp(value as number),
      },
      {
        key: 'createdBy',
        title: t('common:createdBy'),
        dataIndex: 'createdBy',
        width: '10%',
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: ProductDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleView(record),
            },
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record),
            },
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => handleDelete(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleView, handleEdit, handleDelete]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedProduct(null);
  }, []);

  // Mutation để xóa sản phẩm
  const { mutateAsync: deleteProduct } = useDeleteProduct();

  // Mutation để xóa nhiều sản phẩm
  const { mutateAsync: deleteMultipleProducts } = useDeleteMultipleProducts();

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!selectedProduct) return;

    try {
      // Gọi API xóa sản phẩm
      await deleteProduct(selectedProduct.id);

      // Đóng popup
      setIsDeleteModalOpen(false);
      setSelectedProduct(null);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.deleteSuccess'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      NotificationUtil.error({
        message: t('business:product.deleteError'),
        duration: 3000,
      });
    }
  }, [selectedProduct, deleteProduct, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('business:product.selectToDelete'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Gọi API xóa nhiều sản phẩm cùng lúc
      await deleteMultipleProducts(selectedRowKeys as number[]);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.bulkDeleteSuccess', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting products:', error);
      NotificationUtil.error({
        message: t('business:product.bulkDeleteError'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleProducts, t]);

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
    hideEditForm();
    setSelectedProduct(null);
  };

  // Xử lý submit form thêm mới
  const handleSubmit = async (values: Record<string, unknown>) => {
    console.log('Form values:', values);
    // Xử lý thêm sản phẩm (sẽ triển khai sau)
    hideAddForm();
  };

  // Xử lý submit form chỉnh sửa
  const handleEditSubmit = async (values: Record<string, unknown>) => {
    console.log('Edit form values:', values);
    // Xử lý cập nhật sản phẩm (sẽ triển khai sau)
    hideEditForm();
    setSelectedProduct(null);
  };

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): ProductQueryParams => {
    const queryParams: ProductQueryParams = {
      page: params.page,
      limit: params.pageSize,
    };

    // Chỉ thêm các property khi có giá trị thực sự
    if (params.searchTerm) {
      queryParams.search = params.searchTerm;
    }
    if (params.sortBy) {
      queryParams.sortBy = params.sortBy;
    }
    if (params.sortDirection) {
      queryParams.sortDirection = params.sortDirection;
    }

    if (params.filterValue !== 'all') {
      // Kiểm tra xem filterValue có phải là PriceTypeEnum không
      if (Object.values(PriceTypeEnum).includes(params.filterValue as PriceTypeEnum)) {
        queryParams.typePrice = params.filterValue as PriceTypeEnum;
      }
      // Kiểm tra xem filterValue có phải là ProductTypeEnum không
      else if (Object.values(ProductTypeEnum).includes(params.filterValue as ProductTypeEnum)) {
        queryParams.productType = params.filterValue as ProductTypeEnum;
      }
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ProductDto, ProductQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách sản phẩm với queryParams từ dataTable
  const { data: productData, isLoading } = useProducts(dataTable.queryParams);

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback((column: string | null, order: SortOrder | null) => {
    // Nếu column hoặc order là null, reset sort
    if (column === null || order === null) {
      dataTable.tableData.handleSortChange(null, null);
      return;
    }

    dataTable.tableData.handleSortChange(column, order);
  }, [dataTable.tableData]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      // Price type labels
      [PriceTypeEnum.HAS_PRICE]: t('business:product.priceType.hasPrice'),
      [PriceTypeEnum.STRING_PRICE]: t('business:product.priceType.stringPrice'),
      [PriceTypeEnum.NO_PRICE]: t('business:product.priceType.noPrice'),
      // Product type labels
      [ProductTypeEnum.PHYSICAL]: t('business:product.types.physical.title', 'Sản phẩm vật lý'),
      [ProductTypeEnum.DIGITAL]: t('business:product.types.digital.title', 'Sản phẩm số'),
      [ProductTypeEnum.SERVICE]: t('business:product.types.service.title', 'Dịch vụ'),
      [ProductTypeEnum.EVENT]: t('business:product.types.event.title', 'Sự kiện'),
      [ProductTypeEnum.COMBO]: t('business:product.types.combo.title', 'Combo sản phẩm'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <ProductForm onSubmit={handleSubmit} onCancel={handleCancel} isSubmitting={false} />
      </SlideInForm>

      {/* Form chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        <ProductEditForm
          product={selectedProduct}
          onSubmit={handleEditSubmit}
          onCancel={handleCancel}
          isSubmitting={false}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={productData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: productData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: productData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete')}
        message={t('business:product.confirmDeleteMessage')}
        itemName={selectedProduct?.name || ''}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t('business:product.confirmBulkDeleteMessage', { count: selectedRowKeys.length })}
      />
    </div>
  );
};

export default ProductsPage;
