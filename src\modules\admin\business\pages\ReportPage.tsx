import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Tabs, Typography } from '@/shared/components/common';

/**
 * Trang báo cáo kinh doanh
 */
const ReportPage: React.FC = () => {
  const { t } = useTranslation('business');
  // Sử dụng t trong các tiêu đề và nhãn
  const [activeTab, setActiveTab] = useState('sales');

  // Mock data cho biểu đồ
  const salesData = [
    { month: 'T1', value: 1200000 },
    { month: 'T2', value: 1900000 },
    { month: 'T3', value: 1500000 },
    { month: 'T4', value: 2200000 },
    { month: 'T5', value: 2800000 },
    { month: 'T6', value: 2100000 },
    { month: 'T7', value: 2500000 },
    { month: 'T8', value: 3200000 },
    { month: 'T9', value: 3800000 },
    { month: 'T10', value: 3500000 },
    { month: 'T11', value: 4200000 },
    { month: 'T12', value: 4800000 },
  ];

  // Tổng doanh thu
  const totalSales = salesData.reduce((sum, item) => sum + item.value, 0);

  // Định dạng tiền tệ
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card className="p-4">
          <Typography variant="h6" className="mb-2">
            {t('report.totalRevenue', 'Tổng doanh thu')}
          </Typography>
          <Typography variant="h3" color="primary">
            {formatCurrency(totalSales)}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('report.year', 'Năm 2023')}
          </Typography>
        </Card>

        <Card className="p-4">
          <Typography variant="h6" className="mb-2">
            {t('report.totalOrders', 'Tổng đơn hàng')}
          </Typography>
          <Typography variant="h3" color="primary">
            1,245
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('report.year', 'Năm 2023')}
          </Typography>
        </Card>

        <Card className="p-4">
          <Typography variant="h6" className="mb-2">
            {t('report.newCustomers', 'Khách hàng mới')}
          </Typography>
          <Typography variant="h3" color="primary">
            328
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('report.year', 'Năm 2023')}
          </Typography>
        </Card>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'sales',
              label: 'Doanh thu',
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      Biểu đồ doanh thu sẽ được hiển thị ở đây
                    </Typography>
                  </div>
                </div>
              ),
            },
            {
              key: 'orders',
              label: 'Đơn hàng',
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      Biểu đồ đơn hàng sẽ được hiển thị ở đây
                    </Typography>
                  </div>
                </div>
              ),
            },
            {
              key: 'customers',
              label: 'Khách hàng',
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      Biểu đồ khách hàng sẽ được hiển thị ở đây
                    </Typography>
                  </div>
                </div>
              ),
            },
            {
              key: 'products',
              label: 'Sản phẩm',
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      Biểu đồ sản phẩm sẽ được hiển thị ở đây
                    </Typography>
                  </div>
                </div>
              ),
            },
          ]}
        />
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
        <Card title="Sản phẩm bán chạy">
          <div className="p-4">
            <div className="h-60 flex items-center justify-center">
              <Typography variant="h5" className="text-muted-foreground">
                Danh sách sản phẩm bán chạy sẽ được hiển thị ở đây
              </Typography>
            </div>
          </div>
        </Card>

        <Card title="Khách hàng tiềm năng">
          <div className="p-4">
            <div className="h-60 flex items-center justify-center">
              <Typography variant="h5" className="text-muted-foreground">
                Danh sách khách hàng tiềm năng sẽ được hiển thị ở đây
              </Typography>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ReportPage;
