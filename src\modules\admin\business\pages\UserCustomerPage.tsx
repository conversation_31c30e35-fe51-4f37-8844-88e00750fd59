import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import ActiveFilters from '@/modules/components/filters/ActiveFilters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useCustomerConversions } from '../hooks/useConversionQuery';
import { useQueryClient } from '@tanstack/react-query';
import { CUSTOMER_CONVERSION_QUERY_KEYS } from '../hooks/useConversionQuery';
import {
  CustomerConversionDto,
  CustomerConversionQueryParams,
} from '../types/conversion.types';
import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Trang quản lý khách hàng của người dùng (Admin) - Chỉ xem, không tạo/sửa
 */
const UserCustomerPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'business', 'common']);
  const queryClient = useQueryClient();

  // State cho filter và pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [platform, setPlatform] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // State cho detail view
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerConversionDto | null>(
    null
  );

  // Query parameters
  const queryParams: CustomerConversionQueryParams = useMemo(
    () => ({
      page: currentPage,
      limit: pageSize,
      ...(searchTerm && { search: searchTerm }),
      ...(platform && { platform }),
      sortBy: 'createdAt',
      sortDirection: SortDirection.DESC,
    }),
    [currentPage, pageSize, searchTerm, platform]
  );

  // Fetch data using new API
  const { data: customerData, isLoading } = useCustomerConversions(queryParams);

  // Sử dụng hook animation cho detail view
  const { isVisible: isDetailVisible, showForm: showDetail, hideForm: hideDetail } = useSlideForm();

  // Định nghĩa cột cho bảng
  const columns: TableColumn<CustomerConversionDto>[] = [
    {
      key: 'avatar',
      title: t('admin:business.conversion.table.avatar'),
      dataIndex: 'avatar',
      render: (value: unknown, record) => {
        const avatarUrl = value as string;
        return (
          <div className="flex items-center">
            {avatarUrl ? (
              <img
                src={avatarUrl}
                alt={record.name}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">
                  {record.name?.charAt(0)?.toUpperCase() || '?'}
                </span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: 'name',
      title: t('admin:business.conversion.table.name'),
      dataIndex: 'name',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'email',
      title: t('admin:business.conversion.table.email'),
      dataIndex: 'email',
      sortable: true,
      render: (value: unknown) => {
        if (!value) return 'N/A';
        if (typeof value === 'object' && value !== null) {
          const email = value as { primary?: string; secondary?: string };
          return email.primary || email.secondary || 'N/A';
        }
        return String(value);
      },
    },
    {
      key: 'phone',
      title: t('admin:business.conversion.table.phone'),
      dataIndex: 'phone',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'platform',
      title: t('admin:business.conversion.table.platform'),
      dataIndex: 'platform',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'userId',
      title: t('admin:business.conversion.table.userId'),
      dataIndex: 'userId',
      sortable: true,
    },
    {
      key: 'agentId',
      title: t('admin:business.conversion.table.agentId'),
      dataIndex: 'agentId',
      render: (value: unknown) => (value as string) || 'N/A',
      sortable: true,
    },
    {
      key: 'createdAt',
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      sortable: true,
      render: (value: unknown) => {
        try {
          if (!value) return 'N/A';

          let date: Date;

          if (typeof value === 'number') {
            date = new Date(value);
          } else if (typeof value === 'string') {
            if (/^\d+$/.test(value)) {
              date = new Date(Number(value));
            } else {
              date = new Date(value);
            }
          } else if (value instanceof Date) {
            date = value;
          } else {
            return 'N/A';
          }

          if (isNaN(date.getTime())) {
            return 'N/A';
          }

          return date.toLocaleDateString('vi-VN');
        } catch (error) {
          console.error('Error formatting date:', error, 'Value:', value);
          return 'N/A';
        }
      },
    },
    {
      key: 'actions',
      title: t('common:actions'),
      render: (_, record) => {
        return (
          <div className="flex space-x-2">
            <Tooltip content={t('common:view')}>
              <IconCard
                icon="eye"
                variant="ghost"
                size="sm"
                onClick={() => handleViewCustomer(record)}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // Xử lý xem chi tiết khách hàng
  const handleViewCustomer = (customer: CustomerConversionDto) => {
    setSelectedCustomer(customer);
    showDetail();
  };

  // Xử lý thay đổi platform filter
  const handlePlatformFilter = (selectedPlatform: string) => {
    setPlatform(selectedPlatform === 'all' ? '' : selectedPlatform);
    setCurrentPage(1); // Reset về trang đầu khi filter
  };

  // Xử lý clear search
  const handleClearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
  };

  // Xử lý clear platform filter
  const handleClearPlatformFilter = () => {
    setPlatform('');
    setCurrentPage(1);
  };

  // Xử lý clear all filters
  const handleClearAllFilters = () => {
    setSearchTerm('');
    setPlatform('');
    setCurrentPage(1);
  };

  // Xử lý đóng detail view
  const handleCloseDetail = () => {
    setSelectedCustomer(null);
    hideDetail();
  };

  // Loading và error handling được xử lý bởi Table component

  return (
    <div className="w-full bg-background text-foreground">
      <MenuIconBar
        onSearch={setSearchTerm}
        // Không có onAdd - loại bỏ chức năng thêm mới
        items={[
          {
            id: 'all',
            label: t('common:all'),
            icon: 'list',
            onClick: () => handlePlatformFilter('all'),
          },
          {
            id: 'facebook',
            label: 'Facebook',
            icon: 'facebook',
            onClick: () => handlePlatformFilter('Facebook'),
          },
          {
            id: 'zalo',
            label: 'Zalo',
            icon: 'message-square',
            onClick: () => handlePlatformFilter('Zalo'),
          },
          {
            id: 'website',
            label: 'Website',
            icon: 'globe',
            onClick: () => handlePlatformFilter('Website'),
          },
        ]}
        additionalIcons={[
          {
            icon: 'download',
            tooltip: t('admin:business.conversion.export', 'Xuất dữ liệu'),
            variant: 'primary',
            onClick: () => {
              // TODO: Implement export functionality
              console.log('Export customer conversion data');
            },
          },
          {
            icon: 'refresh-cw',
            tooltip: t('common:refresh', 'Làm mới'),
            variant: 'default',
            onClick: () => {
              // Refresh data by invalidating queries
              queryClient.invalidateQueries({
                queryKey: CUSTOMER_CONVERSION_QUERY_KEYS.lists(),
              });
            },
          },
        ]}
      />

      {/* Active Filters */}
      <ActiveFilters
        searchTerm={searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={platform}
        filterLabel={platform ? platform : undefined}
        onClearFilter={handleClearPlatformFilter}
        onClearAll={handleClearAllFilters}
      />

      {/* Detail view khách hàng - chỉ xem, không sửa */}
      <SlideInForm isVisible={isDetailVisible}>
        {selectedCustomer && (
          <Card title={t('admin:business.businessPage.modules.userCustomer.detailTitle')}>
            <div className="p-4">
              <div className="space-y-4">
                <div>
                  <strong>ID:</strong> {selectedCustomer.id}
                </div>
                <div>
                  <strong>Tên:</strong> {selectedCustomer.name || 'N/A'}
                </div>
                <div>
                  <strong>Email:</strong>{' '}
                  {selectedCustomer.email
                    ? typeof selectedCustomer.email === 'object'
                      ? selectedCustomer.email.primary || selectedCustomer.email.secondary || 'N/A'
                      : selectedCustomer.email
                    : 'N/A'}
                </div>
                <div>
                  <strong>Điện thoại:</strong> {selectedCustomer.phone || 'N/A'}
                </div>
                <div>
                  <strong>Platform:</strong> {selectedCustomer.platform || 'N/A'}
                </div>
                <div>
                  <strong>User ID:</strong> {selectedCustomer.userId}
                </div>
                <div>
                  <strong>Agent ID:</strong> {selectedCustomer.agentId || 'N/A'}
                </div>
                <div>
                  <strong>Địa chỉ:</strong> {selectedCustomer.address || 'N/A'}
                </div>
                <div>
                  <strong>Múi giờ:</strong> {selectedCustomer.timezone || 'N/A'}
                </div>
                <div>
                  <strong>Ngày tạo:</strong>{' '}
                  {selectedCustomer.createdAt
                    ? new Date(selectedCustomer.createdAt).toLocaleDateString('vi-VN')
                    : 'N/A'}
                </div>
                {selectedCustomer.metadata && selectedCustomer.metadata.length > 0 && (
                  <div>
                    <strong>Metadata:</strong>
                    <div className="mt-2 space-y-1">
                      {selectedCustomer.metadata.map((meta, index) => (
                        <div key={index} className="text-sm">
                          <span className="font-medium">{meta.fieldName}:</span> {meta.fieldValue}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <div className="flex justify-end mt-4">
                <button
                  className="px-4 py-2 bg-gray-200 rounded"
                  onClick={handleCloseDetail}
                >
                  {t('common:close')}
                </button>
              </div>
            </div>
          </Card>
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={columns}
          data={customerData?.items || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: customerData?.meta?.totalItems || 0,
            showSizeChanger: true,
            onChange: (page: number, size?: number) => {
              setCurrentPage(page);
              if (size && size !== pageSize) {
                setPageSize(size);
              }
            },
          }}
        />
      </Card>
    </div>
  );
};

export default UserCustomerPage;
