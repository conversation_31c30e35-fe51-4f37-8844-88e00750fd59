import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, Loading, ActionMenu } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useWarehouseCustomFields } from '../hooks/useWarehouseCustomFieldQuery';
import { WarehouseCustomFieldDto } from '../types/warehouse.types';
import ViewWarehouseCustomfieldForm from '../components/forms/ViewWarehouseCustomfieldForm';

/**
 * Trang quản lý trường tùy chỉnh kho cho Admin
 */
const WarehouseCustomFieldPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho filter và pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [warehouseId, setWarehouseId] = useState<number | undefined>(undefined);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);



  // State cho form
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<number | null>(null);
  const [selectedFieldId, setSelectedFieldId] = useState<number | null>(null);

  // Sử dụng hook để lấy dữ liệu trường tùy chỉnh kho
  const { data: fieldsResponse, isLoading, error } = useWarehouseCustomFields({
    page,
    limit,
    ...(searchTerm && { search: searchTerm }),
    ...(warehouseId && { warehouseId }),
  });

  // Lấy dữ liệu từ API response
  const fields = fieldsResponse?.items || [];
  const totalItems = fieldsResponse?.meta?.totalItems || 0;

  // Định nghĩa cột cho bảng
  const columns: TableColumn<WarehouseCustomFieldDto>[] = [
    {
      key: 'id',
      title: t('admin:business.warehouseCustomField.table.id'),
      dataIndex: 'fieldId',
      sortable: true,
    },
    {
      key: 'name',
      title: t('admin:business.warehouseCustomField.table.name'),
      dataIndex: 'warehouseName',
    },
    {
      key: 'type',
      title: t('admin:business.warehouseCustomField.table.label'),
      dataIndex: 'fieldLabel',
    },
    {
      key: 'value',
      title: t('admin:business.warehouseCustomField.table.value'),
      dataIndex: 'value.value',

    },
    {
      key: 'warehouseId',
      title: t('admin:business.warehouseCustomField.table.warehouseId'),
      dataIndex: 'warehouseId',
    },


     {
      key: 'actions',
      title: t('admin:business.warehouseCustomField.table.actions'),
      render: (_, record) => {
        // Tạo danh sách các action items
        const actionItems: ActionMenuItem[] = [
          {
            id: 'view',
            label: t('admin:business.warehouseCustomField.actions.view'),
            icon: 'eye',
            onClick: () => handleView(record.warehouseId, record.fieldId),
          },
        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip={t('admin:business.warehouseCustomField.table.moreActions')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            showAllInMenu={false}
            preferRight={true}
          />
        );
      },
    },
  ];

  // Handler functions
  const handleView = useCallback((warehouseId: number, fieldId: number) => {
    console.log('handleView called with:', { warehouseId, fieldId });
    setSelectedWarehouseId(warehouseId);
    setSelectedFieldId(fieldId);
    showForm();

    console.log('Form should be visible now');
  }, [setSelectedWarehouseId, setSelectedFieldId, showForm]);

  const handleCloseForm = useCallback(() => {
    setSelectedWarehouseId(null);
    setSelectedFieldId(null);
    hideForm();
  }, [setSelectedWarehouseId, setSelectedFieldId, hideForm]);




  // Xử lý thay đổi trang
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  return (
    <div>
      <MenuIconBar
        onSearch={setSearchTerm}
        items={[
          {
            id: 'all',
            label: t('admin:business.warehouseCustomField.filters.all'),
            icon: 'list',
            onClick: () => setWarehouseId(undefined),
          },
          {
            id: 'text',
            label: t('admin:business.warehouseCustomField.filters.text'),
            icon: 'type',
            onClick: () => console.log('Filter by text fields'),
          },
          {
            id: 'number',
            label: t('admin:business.warehouseCustomField.filters.number'),
            icon: 'hash',
            onClick: () => console.log('Filter by number fields'),
          },
          {
            id: 'boolean',
            label: t('admin:business.warehouseCustomField.filters.boolean'),
            icon: 'toggle-left',
            onClick: () => console.log('Filter by boolean fields'),
          },
          {
            id: 'date',
            label: t('admin:business.warehouseCustomField.filters.date'),
            icon: 'calendar',
            onClick: () => console.log('Filter by date fields'),
          },
        ]}
      />

        {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        {selectedWarehouseId && selectedFieldId ? (
          <ViewWarehouseCustomfieldForm
            warehouseId={selectedWarehouseId}
            fieldId={selectedFieldId}
            onClose={handleCloseForm}
          />
        ) : (
          <div className="p-4">
            <p>Debug: warehouseId={selectedWarehouseId}, fieldId={selectedFieldId}, isFormVisible={isFormVisible}</p>
          </div>
        )}
      </SlideInForm>

      {isLoading ? (
        <Loading />
      ) : error ? (
        <Card>
          <div className="p-4 text-center text-red-500">
            {t('admin:business.warehouseCustomField.loadError')}
          </div>
        </Card>
      ) : (
        <Card className="overflow-hidden">
          <Table
            columns={columns}
            data={fields}
            rowKey="id"
            pagination={{
              current: page,
              pageSize: limit,
              total: totalItems,
              onChange: handlePageChange,
            }}
          />
        </Card>
      )}


    </div>
  );
};

export default WarehouseCustomFieldPage;
