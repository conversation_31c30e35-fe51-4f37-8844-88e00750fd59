import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { usePhysicalWarehousesAdmin } from '../hooks/usePhysicalWarehouseAdminQuery';
import {
  PhysicalWarehouseAdminDto,
  QueryPhysicalWarehouseAdminDto,
} from '../types/physical-warehouse.types';
import ViewWarehouseForm from '../components/forms/ViewWarehouseForm';

/**
 * Trang quản lý kho vật lý cho Admin
 */
const WarehousePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho form và table
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<number | null>(null);

  // Xử lý xem chi tiết
  const handleView = useCallback((id: number) => {
    setSelectedWarehouseId(id);
    showForm();
  }, [setSelectedWarehouseId, showForm]);

  // Xử lý đóng form
  const handleCloseForm = useCallback(() => {
    setSelectedWarehouseId(null);
    hideForm();
  }, [setSelectedWarehouseId, hideForm]);



  // Định nghĩa cột cho bảng
  const columns: TableColumn<PhysicalWarehouseAdminDto>[] = useMemo(() => [
    {
      key: 'warehouseId',
      title: t('admin:business.warehouse.table.id'),
      dataIndex: 'warehouseId',
      sortable: true,
      width: '10%',
    },
    {
      key: 'name',
      title: t('admin:business.warehouse.table.name'),
      sortable: true,
      width: '20%',
      render: (_, record) => record.warehouse?.name || t('admin:business.warehouse.notSet'),
    },
    {
      key: 'description',
      title: t('admin:business.warehouse.table.description'),
      width: '25%',
      render: (_, record) => {
        const description = record.warehouse?.description;
        return description || t('admin:business.warehouse.noDescription');
      },
    },
    {
      key: 'address',
      title: t('admin:business.warehouse.table.address'),
      dataIndex: 'address',
      width: '25%',
    },
    {
      key: 'capacity',
      title: t('admin:business.warehouse.table.capacity'),
      dataIndex: 'capacity',
      width: '10%',
      render: (value: unknown) => {
        const capacity = value as number | null;
        return capacity ? capacity.toLocaleString() : t('admin:business.warehouse.notSet');
      },
    },

    {
      key: 'actions',
      title: t('admin:business.warehouse.table.actions'),
      render: (_, record) => {
        // Tạo danh sách các action items
        const actionItems: ActionMenuItem[] = [
          {
            id: 'view',
            label: t('admin:business.warehouse.actions.view'),
            icon: 'eye',
            onClick: () => handleView(Number(record.warehouseId)),
          },
        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip={t('admin:business.warehouse.table.moreActions')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            showAllInMenu={false}
            preferRight={true}
          />
        );
      },
    },
  ], [t, handleView]);

  // Create query params function
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): QueryPhysicalWarehouseAdminDto => {
    const queryParams: QueryPhysicalWarehouseAdminDto = {
      page: params.page,
      limit: params.pageSize,
      sortBy: params.sortBy as 'warehouseId' | 'address' | 'capacity' | 'createdAt' || 'createdAt',
      sortDirection: params.sortDirection || SortDirection.DESC,
    };

    // Xử lý search term
    if (params.searchTerm) {
      queryParams.address = params.searchTerm;
    }

    // Xử lý filter values
    if (params.filterValue && params.filterValue !== 'all') {
      switch (params.filterValue) {
        case 'high-capacity':
          queryParams.minCapacity = 5000; // Kho có sức chứa cao (>= 5000)
          break;
        case 'low-capacity':
          queryParams.maxCapacity = 3000; // Kho có sức chứa thấp (<= 1000)
          break;
        default:
          // Nếu filterValue là string, có thể là address filter
          if (typeof params.filterValue === 'string') {
            queryParams.address = params.filterValue;
          }
          break;
      }
    }

    return queryParams;
  }, []);

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<PhysicalWarehouseAdminDto, QueryPhysicalWarehouseAdminDto>({
      columns,
      filterOptions: [
        { id: 'all', label: t('admin:business.warehouse.filters.all'), icon: 'list', value: 'all' },
        { id: 'high-capacity', label: t('admin:business.warehouse.filters.highCapacity'), icon: 'trending-up', value: 'high-capacity' },
        { id: 'low-capacity', label: t('admin:business.warehouse.filters.lowCapacity'), icon: 'trending-down', value: 'low-capacity' },
      ],
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách kho
  const { data: warehousesData, isLoading } = usePhysicalWarehousesAdmin(dataTable.queryParams);

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback((column: string | null, order: SortOrder | null) => {
    // Nếu column hoặc order là null, reset sort
    if (column === null || order === null) {
      dataTable.tableData.handleSortChange(null, null);
      return;
    }

    dataTable.tableData.handleSortChange(column, order as SortOrder);
  }, [dataTable.tableData]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      'high-capacity': t('admin:business.warehouse.filters.highCapacity'),
      'low-capacity': t('admin:business.warehouse.filters.lowCapacity'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        {selectedWarehouseId && (
          <ViewWarehouseForm
            warehouseId={selectedWarehouseId}
            onClose={handleCloseForm}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={warehousesData?.result?.items || []}
          rowKey="warehouseId"
          loading={isLoading}
          sortable={true}

          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: warehousesData?.result?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: warehousesData?.result?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default WarehousePage;
