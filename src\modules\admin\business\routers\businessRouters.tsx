import { Suspense, lazy } from 'react';
import { Loading } from '@/shared/components/common';
import { RouteObject } from 'react-router-dom';
import TranslatedMainLayout from '../components/TranslatedMainLayout';

// Lazy load pages
const BusinessPage = lazy(() => import('../pages/BusinessPage'));
const ProductPage = lazy(() => import('../pages/ProductPage'));
const ConversionPage = lazy(() => import('../pages/ConversionPage'));
const OrderPage = lazy(() => import('../pages/OrderPage'));
const WarehousePage = lazy(() => import('../pages/WarehousePage'));
const CustomFieldPage = lazy(() => import('../pages/CustomFieldPage'));
const WarehouseCustomFieldPage = lazy(() => import('../pages/WarehouseCustomFieldPage'));
const UserCustomerPage = lazy(() => import('../pages/UserCustomerPage'));

/**
 * Business module routes for Admin
 */
export const businessAdminRoutes: RouteObject[] = [
  // Trang tổng quan
  {
    path: '/admin/business',
    element: (
      <TranslatedMainLayout titleKey="business">
        <Suspense fallback={<Loading />}>
          <BusinessPage />
        </Suspense>
      </TranslatedMainLayout>
    ),
  },

  // Trang sản phẩm
  {
    path: '/admin/business/product',
    element: (
      <TranslatedMainLayout titleKey="product">
        <Suspense fallback={<Loading />}>
          <ProductPage />
        </Suspense>
      </TranslatedMainLayout>
    ),
  },

  // Trang chuyển đổi
  {
    path: '/admin/business/conversion',
    element: (
      <TranslatedMainLayout titleKey="conversion">
        <Suspense fallback={<Loading />}>
          <ConversionPage />
        </Suspense>
      </TranslatedMainLayout>
    ),
  },

  // Trang đơn hàng
  {
    path: '/admin/business/order',
    element: (
      <TranslatedMainLayout titleKey="order">
        <Suspense fallback={<Loading />}>
          <OrderPage />
        </Suspense>
      </TranslatedMainLayout>
    ),
  },

  // Trang kho vật lý
  {
    path: '/admin/business/warehouse',
    element: (
      <TranslatedMainLayout titleKey="warehouse">
        <Suspense fallback={<Loading />}>
          <WarehousePage />
        </Suspense>
      </TranslatedMainLayout>
    ),
  },

  // Trang trường tùy chỉnh
  {
    path: '/admin/business/custom-field',
    element: (
      <TranslatedMainLayout titleKey="customField">
        <Suspense fallback={<Loading />}>
          <CustomFieldPage />
        </Suspense>
      </TranslatedMainLayout>
    ),
  },

  // Trang quản lý khách hàng của người dùng
  {
    path: '/admin/business/user-customer',
    element: (
      <TranslatedMainLayout titleKey="userCustomer">
        <Suspense fallback={<Loading />}>
          <UserCustomerPage />
        </Suspense>
      </TranslatedMainLayout>
    ),
  },

  // Trang trường tùy chỉnh kho (route mới cho WarehouseCustomFieldPage)
  {
    path: '/admin/business/warehouse-custom-field-old',
    element: (
      <TranslatedMainLayout titleKey="warehouseCustomField">
        <Suspense fallback={<Loading />}>
          <WarehouseCustomFieldPage />
        </Suspense>
      </TranslatedMainLayout>
    ),
  },
];

export default businessAdminRoutes;
