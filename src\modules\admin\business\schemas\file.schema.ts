import { z } from 'zod';
import { FileStatus, FileType } from '../types/file.types';

/**
 * <PERSON><PERSON>a cho tham số truy vấn file
 */
export const fileQuerySchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().optional().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  folderId: z.coerce.number().int().positive().optional(),
  type: z.nativeEnum(FileType).optional(),
  status: z.nativeEnum(FileStatus).optional(),
  extension: z.string().optional(),
});

/**
 * Schema cho dữ liệu tạo file
 */
export const createFileSchema = z.object({
  name: z.string().min(1, 'Tên file không được để trống').max(255, 'Tên file không được vượt quá 255 ký tự'),
  originalName: z.string().min(1, 'Tên gốc file không được để trống'),
  mimeType: z.string().min(1, 'MIME type không được để trống'),
  size: z.number().int().positive('Kích thước file phải là số dương'),
  extension: z.string().min(1, 'Phần mở rộng file không được để trống'),
  type: z.nativeEnum(FileType, {
    errorMap: () => ({ message: 'Loại file không hợp lệ' }),
  }),
  url: z.string().url('URL không hợp lệ').optional(),
  thumbnailUrl: z.string().url('URL thumbnail không hợp lệ').optional(),
  folderId: z.number().int().positive('ID folder phải là số dương').optional(),
  metadata: z.record(z.unknown()).optional(),
});

/**
 * Schema cho dữ liệu cập nhật file
 */
export const updateFileSchema = z.object({
  name: z.string().min(1, 'Tên file không được để trống').max(255, 'Tên file không được vượt quá 255 ký tự').optional(),
  folderId: z.number().int().positive('ID folder phải là số dương').optional().nullable(),
  status: z.nativeEnum(FileStatus, {
    errorMap: () => ({ message: 'Trạng thái file không hợp lệ' }),
  }).optional(),
  metadata: z.record(z.unknown()).optional(),
});
