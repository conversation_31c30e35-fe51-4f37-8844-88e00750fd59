import { z } from 'zod';
import { FolderStatus } from '../types/folder.types';

/**
 * Schema cho tham số truy vấn folder
 */
export const folderQuerySchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().optional().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  parentId: z.coerce.number().int().positive().optional(),
  status: z.nativeEnum(FolderStatus).optional(),
});

/**
 * Schema cho dữ liệu tạo folder
 */
export const createFolderSchema = z.object({
  name: z.string().min(1, 'Tên thư mục không được để trống').max(255, '<PERSON>ê<PERSON> thư mục không được vượt quá 255 ký tự'),
  description: z.string().max(1000, '<PERSON><PERSON> tả không được vượt quá 1000 ký tự').optional(),
  parentId: z.number().int().positive('ID thư mục cha phải là số dương').optional().nullable(),
});

/**
 * Schema cho dữ liệu cập nhật folder
 */
export const updateFolderSchema = z.object({
  name: z.string().min(1, 'Tên thư mục không được để trống').max(255, 'Tên thư mục không được vượt quá 255 ký tự').optional(),
  description: z.string().max(1000, 'Mô tả không được vượt quá 1000 ký tự').optional(),
  parentId: z.number().int().positive('ID thư mục cha phải là số dương').optional().nullable(),
  status: z.nativeEnum(FolderStatus, {
    errorMap: () => ({ message: 'Trạng thái thư mục không hợp lệ' }),
  }).optional(),
});
