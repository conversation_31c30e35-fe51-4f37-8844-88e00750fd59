import { z } from 'zod';
import { PriceTypeEnum } from '../types/product.types';
import { TFunction } from 'i18next';

/**
 * Schema cho giá sản phẩm khi typePrice là HAS_PRICE
 */
export const hasPriceSchema = z.object({
  listPrice: z
    .union([z.string(), z.number()])
    .transform((val) => {
      if (val === '' || val === null || val === undefined) return 0;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? 0 : num;
    })
    .refine((val) => val >= 0, {
      message: 'Gi<PERSON> niêm yết phải lớn hơn hoặc bằng 0',
    }),
  salePrice: z
    .union([z.string(), z.number()])
    .transform((val) => {
      if (val === '' || val === null || val === undefined) return 0;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? 0 : num;
    })
    .refine((val) => val >= 0, {
      message: '<PERSON><PERSON><PERSON> bán phải lớn hơn hoặc bằng 0',
    }),
  currency: z.string().min(1, 'Đơn vị tiền tệ không được để trống'),
});

/**
 * Schema cho giá sản phẩm khi typePrice là STRING_PRICE
 */
export const stringPriceSchema = z.object({
  priceDescription: z.string().min(1, 'Mô tả giá không được để trống'),
});

/**
 * Schema cho cấu hình vận chuyển
 */
export const shipmentConfigSchema = z.object({
  lengthCm: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    })
    .refine((val) => val === undefined || val >= 0, {
      message: 'Chiều dài phải lớn hơn hoặc bằng 0',
    }),
  widthCm: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    })
    .refine((val) => val === undefined || val >= 0, {
      message: 'Chiều rộng phải lớn hơn hoặc bằng 0',
    }),
  heightCm: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    })
    .refine((val) => val === undefined || val >= 0, {
      message: 'Chiều cao phải lớn hơn hoặc bằng 0',
    }),
  weightGram: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    })
    .refine((val) => val === undefined || val >= 0, {
      message: 'Khối lượng phải lớn hơn hoặc bằng 0',
    }),
});

/**
 * Schema cho phân loại sản phẩm
 */
export const classificationSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, 'Tên phân loại không được để trống'),
  options: z.array(z.string().min(1, 'Tùy chọn không được để trống')),
});

/**
 * Schema cho thao tác ảnh
 */
export const imageOperationSchema = z.object({
  operation: z.enum(['ADD', 'DELETE']),
  position: z.number().optional(),
  key: z.string().optional(),
  mimeType: z.string().optional(),
});

/**
 * Schema creator function cho tạo sản phẩm với hỗ trợ đa ngôn ngữ
 */
export const createProductSchema = (t: TFunction) => {
  return z.object({
    name: z
      .string()
      .min(1, t('validation:required', { field: t('business:product.name') }))
      .max(255, t('validation:maxLength', { field: t('business:product.name'), length: 255 })),

    customFieldGroup: z.string().optional(),

    typePrice: z.nativeEnum(PriceTypeEnum, {
      errorMap: () => ({ message: t('validation:invalidOption', { field: t('business:product.priceType') }) }),
    }),

    price: z.union([
      hasPriceSchema,
      stringPriceSchema,
      z.null()
    ]).optional(),

    description: z.string().optional(),

    tags: z.array(z.string()).optional(),

    shipmentConfig: shipmentConfigSchema.optional(),

    classifications: z.array(classificationSchema).optional(),

    media: z.array(
      z.object({
        id: z.string(),
        file: z.instanceof(File),
        preview: z.string().optional(),
      })
    ).optional(),
  }).superRefine((data, ctx) => {
    // Kiểm tra giá phù hợp với loại giá
    if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
      if (!data.price || !('listPrice' in data.price)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t('business:validation.priceRequired'),
          path: ['price'],
        });
      }
    } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!data.price || !('priceDescription' in data.price)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t('business:validation.priceDescriptionRequired'),
          path: ['price'],
        });
      }
    } else if (data.typePrice === PriceTypeEnum.NO_PRICE) {
      if (data.price !== null) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t('business:validation.priceNotAllowed'),
          path: ['price'],
        });
      }
    }
  });
};

/**
 * Schema creator function cho cập nhật sản phẩm với hỗ trợ đa ngôn ngữ
 */
export const updateProductSchema = (t: TFunction) => {
  return z.object({
    name: z
      .string()
      .min(1, t('validation:required', { field: t('business:product.name') }))
      .max(255, t('validation:maxLength', { field: t('business:product.name'), length: 255 }))
      .optional(),

    typePrice: z.nativeEnum(PriceTypeEnum, {
      errorMap: () => ({ message: t('validation:invalidOption', { field: t('business:product.priceType') }) }),
    }).optional(),

    price: z.union([
      hasPriceSchema,
      stringPriceSchema,
      z.null()
    ]).optional(),

    description: z.string().optional(),

    images: z.array(imageOperationSchema).optional(),

    tags: z.array(z.string()).optional(),

    shipmentConfig: shipmentConfigSchema.optional(),

    classifications: z.array(classificationSchema).optional(),

    media: z.array(
      z.object({
        id: z.string(),
        file: z.instanceof(File),
        preview: z.string().optional(),
      })
    ).optional(),
  }).superRefine((data, ctx) => {
    // Kiểm tra giá phù hợp với loại giá nếu cả hai đều được cung cấp
    if (data.typePrice && data.price) {
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        if (!('listPrice' in data.price)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('business:validation.priceRequired'),
            path: ['price'],
          });
        }
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!('priceDescription' in data.price)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('business:validation.priceDescriptionRequired'),
            path: ['price'],
          });
        }
      } else if (data.typePrice === PriceTypeEnum.NO_PRICE) {
        if (data.price !== null) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('business:validation.priceNotAllowed'),
            path: ['price'],
          });
        }
      }
    }
  });
};

/**
 * Type cho schema tạo sản phẩm
 */
export type CreateProductSchema = z.infer<ReturnType<typeof createProductSchema>>;

/**
 * Type cho schema cập nhật sản phẩm
 */
export type UpdateProductSchema = z.infer<ReturnType<typeof updateProductSchema>>;
