import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  ConversionDetailDto,
  ConversionListItemDto,
  ConversionQueryParams,
  CustomerConversionDto,
  CustomerConversionQueryParams,
} from '../types/conversion.types';

/**
 * Service xử lý API liên quan đến chuyển đổi cho admin
 */
export const ConversionService = {
  /**
   * Lấy danh sách khách hàng chuyển đổi (API mới) - dùng cho cả conversion và user-customer
   * @param params Tham số truy vấn
   * @returns Danh sách khách hàng chuyển đổi với phân trang
   */
  getCustomerConversions: async (params?: CustomerConversionQueryParams): Promise<ApiResponseDto<PaginatedResult<CustomerConversionDto>>> => {
    return apiRequest.get('/admin/user-convert-customers', { params });
  },

  /**
   * L<PERSON>y danh sách bản ghi chuyển đổi (legacy)
   * @param params Tham số truy vấn
   * @returns Danh sách bản ghi chuyển đổi với phân trang
   */
  getConverts: async (params?: ConversionQueryParams): Promise<ApiResponseDto<PaginatedResult<ConversionListItemDto>>> => {
    return apiRequest.get('/admin/user-converts', { params });
  },

  /**
   * Lấy chi tiết bản ghi chuyển đổi theo ID
   * @param id ID của bản ghi chuyển đổi
   * @returns Chi tiết bản ghi chuyển đổi
   */
  getConvertById: async (id: number): Promise<ApiResponseDto<ConversionDetailDto>> => {
    return apiRequest.get(`/admin/user-converts/${id}`);
  },
};
