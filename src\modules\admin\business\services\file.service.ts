import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  CreateFileDto,
  FileDetailResponseDto,
  FileQueryParams,
  FileResponseDto,
  UpdateFileDto,
} from '../types/file.types';

/**
 * Service xử lý API liên quan đến file cho admin
 */
export const FileService = {
  /**
   * Lấy danh sách file
   * @param params Tham số truy vấn
   * @returns Danh sách file với phân trang
   */
  getFiles: async (params?: FileQueryParams): Promise<ApiResponseDto<PaginatedResult<FileResponseDto>>> => {
    return apiRequest.get('/admin/files', { params });
  },

  /**
   * Lấy chi tiết file theo ID
   * @param id ID của file
   * @returns Chi tiết file
   */
  getFileById: async (id: number): Promise<ApiResponseDto<FileDetailResponseDto>> => {
    return apiRequest.get(`/admin/files/${id}`);
  },

  /**
   * Tạo file mới
   * @param data Dữ liệu tạo file
   * @returns Thông tin file đã tạo
   */
  createFile: async (data: CreateFileDto): Promise<ApiResponseDto<FileResponseDto>> => {
    return apiRequest.post('/admin/files', data);
  },

  /**
   * Cập nhật file
   * @param id ID của file
   * @param data Dữ liệu cập nhật file
   * @returns Thông tin file đã cập nhật
   */
  updateFile: async (id: number, data: UpdateFileDto): Promise<ApiResponseDto<FileResponseDto>> => {
    return apiRequest.put(`/admin/files/${id}`, data);
  },

  /**
   * Xóa file
   * @param id ID của file
   * @returns Thông báo xóa thành công
   */
  deleteFile: async (id: number): Promise<ApiResponseDto<null>> => {
    return apiRequest.delete(`/admin/files/${id}`);
  },
};
