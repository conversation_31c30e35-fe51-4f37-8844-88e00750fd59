import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  CreateFolderDto,
  FolderDetailResponseDto,
  FolderQueryParams,
  FolderResponseDto,
  UpdateFolderDto,
} from '../types/folder.types';

/**
 * Service xử lý API liên quan đến folder cho admin
 */
export const FolderService = {
  /**
   * Lấy danh sách folder
   * @param params Tham số truy vấn
   * @returns Danh sách folder với phân trang
   */
  getFolders: async (params?: FolderQueryParams): Promise<ApiResponseDto<PaginatedResult<FolderResponseDto>>> => {
    return apiRequest.get('/admin/folders', { params });
  },

  /**
   * <PERSON><PERSON>y chi tiết folder theo ID
   * @param id ID của folder
   * @returns Chi tiết folder
   */
  getFolderById: async (id: number): Promise<ApiResponseDto<FolderDetailResponseDto>> => {
    return apiRequest.get(`/admin/folders/${id}`);
  },

  /**
   * Tạo folder mới
   * @param data Dữ liệu tạo folder
   * @returns Thông tin folder đã tạo
   */
  createFolder: async (data: CreateFolderDto): Promise<ApiResponseDto<FolderResponseDto>> => {
    return apiRequest.post('/admin/folders', data);
  },

  /**
   * Cập nhật folder
   * @param id ID của folder
   * @param data Dữ liệu cập nhật folder
   * @returns Thông tin folder đã cập nhật
   */
  updateFolder: async (id: number, data: UpdateFolderDto): Promise<ApiResponseDto<FolderResponseDto>> => {
    return apiRequest.put(`/admin/folders/${id}`, data);
  },

  /**
   * Xóa folder
   * @param id ID của folder
   * @returns Thông báo xóa thành công
   */
  deleteFolder: async (id: number): Promise<ApiResponseDto<null>> => {
    return apiRequest.delete(`/admin/folders/${id}`);
  },
};
