import { apiClient } from '@/shared/api';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { SortDirection } from '@/shared/dto/request/query.dto';
import {
  PhysicalWarehouseAdminDto,
  PhysicalWarehouseAdminDetailDto,
  QueryPhysicalWarehouseAdminDto,
  WarehouseDetail,
} from '../types/physical-warehouse.types';
import { PHYSICAL_WAREHOUSE_ADMIN_ENDPOINTS } from '../constants/physical-warehouse.constants';

/**
 * API Layer - Raw API calls cho physical warehouse admin
 */

/**
 * Lấy danh sách kho vật lý admin
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getPhysicalWarehousesAdmin = async (
  params?: QueryPhysicalWarehouseAdminDto
): Promise<ApiResponseDto<PaginatedResult<PhysicalWarehouseAdminDto>>> => {
  return apiClient.get(PHYSICAL_WAREHOUSE_ADMIN_ENDPOINTS.BASE, { params });
};

/**
 * Lấy chi tiết kho vật lý admin theo ID
 * @param id ID của kho vật lý
 * @returns Promise với response từ API
 */
export const getPhysicalWarehouseAdminById = async (
  id: number
): Promise<ApiResponseDto<PhysicalWarehouseAdminDetailDto>> => {
  return apiClient.get(PHYSICAL_WAREHOUSE_ADMIN_ENDPOINTS.DETAIL(id));
};

/**
 * Lấy chi tiết kho vật lý theo ID (API mới)
 * @param warehouseId ID của kho vật lý
 * @returns Promise với response từ API
 */
export const getWarehouseDetail = async (
  warehouseId: number
): Promise<ApiResponseDto<WarehouseDetail>> => {
  return apiClient.get(`/admin/physical-warehouses/${warehouseId}`);
};

/**
 * Services Layer - Business logic cho physical warehouse admin
 */

/**
 * Lấy danh sách kho vật lý admin với business logic
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getPhysicalWarehousesAdminWithBusinessLogic = async (
  params?: QueryPhysicalWarehouseAdminDto
): Promise<ApiResponseDto<PaginatedResult<PhysicalWarehouseAdminDto>>> => {
  // Thiết lập default parameters
  const defaultParams: QueryPhysicalWarehouseAdminDto = {
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortDirection: SortDirection.DESC,
    ...params,
  };

  // Validate parameters
  if (defaultParams.limit && defaultParams.limit > 100) {
    throw new Error('Limit cannot exceed 100');
  }

  if (defaultParams.minCapacity && defaultParams.maxCapacity) {
    if (defaultParams.minCapacity > defaultParams.maxCapacity) {
      throw new Error('Min capacity cannot be greater than max capacity');
    }
  }

  return getPhysicalWarehousesAdmin(defaultParams);
};

/**
 * Lấy chi tiết kho vật lý admin với business logic
 * @param id ID của kho vật lý
 * @returns Promise với response từ API
 */
export const getPhysicalWarehouseAdminByIdWithBusinessLogic = async (
  id: number
): Promise<ApiResponseDto<PhysicalWarehouseAdminDetailDto>> => {
  // Validate ID
  if (!id || id <= 0) {
    throw new Error('Invalid warehouse ID');
  }

  return getPhysicalWarehouseAdminById(id);
};

/**
 * Lấy chi tiết kho vật lý với business logic (API mới)
 * @param warehouseId ID của kho vật lý
 * @returns Promise với response từ API
 */
export const getWarehouseDetailWithBusinessLogic = async (
  warehouseId: number
): Promise<ApiResponseDto<WarehouseDetail>> => {
  // Validate ID
  if (!warehouseId || warehouseId <= 0) {
    throw new Error('Invalid warehouse ID');
  }

  return getWarehouseDetail(warehouseId);
};
