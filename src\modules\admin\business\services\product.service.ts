import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  ProductDto,
  ProductQueryParams,
  ProductDetail,
  UpdateProductStatusDto,
} from '../types/product.types';

/**
 * Service xử lý các API liên quan đến sản phẩm cho admin
 */
export const ProductService = {
  /**
   * Lấy danh sách sản phẩm
   * @param params Tham số truy vấn
   * @returns Promise với danh sách sản phẩm và thông tin phân trang
   */
  getProducts: async (params?: ProductQueryParams): Promise<ApiResponseDto<PaginatedResult<ProductDto>>> => {
    return apiClient.get('/admin/user-products', { params });
  },

  /**
   * Lấy chi tiết sản phẩm theo ID
   * @param id ID của sản phẩm
   * @returns Promise với thông tin chi tiết sản phẩm
   */
  getProductById: async (id: number): Promise<ApiResponseDto<ProductDetail>> => {
    return apiClient.get(`/admin/user-products/${id}`);
  },

  /**
   * Cập nhật trạng thái sản phẩm
   * @param data Dữ liệu cập nhật trạng thái
   * @returns Promise với kết quả cập nhật
   */
  updateProductStatus: async (
    data: UpdateProductStatusDto
  ): Promise<ApiResponseDto<{ updatedCount: number }>> => {
    return apiClient.post('/admin/user-products/status', data);
  },

  /**
   * Phê duyệt nhiều sản phẩm
   * @param productIds Danh sách ID sản phẩm
   * @returns Promise với kết quả phê duyệt
   */
  approveProducts: async (
    productIds: number[]
  ): Promise<ApiResponseDto<{ updatedCount: number }>> => {
    return apiClient.post('/admin/user-products/status', {
      productIds,
      status: 'APPROVED',
    });
  },

  /**
   * Từ chối nhiều sản phẩm
   * @param productIds Danh sách ID sản phẩm
   * @param rejectReason Lý do từ chối
   * @returns Promise với kết quả từ chối
   */
  rejectProducts: async (
    productIds: number[],
    rejectReason: string
  ): Promise<ApiResponseDto<{ updatedCount: number }>> => {
    return apiClient.post('/admin/user-products/status', {
      productIds,
      status: 'REJECTED',
      rejectReason,
    });
  },

  /**
   * Xóa sản phẩm
   * @param id ID của sản phẩm
   * @returns Promise với kết quả xóa
   */
  deleteProduct: async (id: number): Promise<ApiResponseDto<null>> => {
    return apiClient.delete(`/admin/user-products/${id}`);
  },

  /**
   * Tạo sản phẩm mới
   * @param data Dữ liệu sản phẩm
   * @returns Promise với kết quả tạo
   */
  createProduct: async (data: Record<string, unknown>): Promise<ApiResponseDto<ProductDto>> => {
    return apiClient.post('/admin/user-products', data);
  },

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm
   * @param data Dữ liệu cập nhật
   * @returns Promise với kết quả cập nhật
   */
  updateProduct: async (id: number, data: Record<string, unknown>): Promise<ApiResponseDto<ProductDto>> => {
    return apiClient.put(`/admin/user-products/${id}`, data);
  },

  /**
   * Lấy presigned URL để upload file
   * @param fileType Loại file
   * @returns Promise với presigned URL
   */
  getPresignedUrl: async (fileType: string): Promise<ApiResponseDto<{ uploadUrl: string; fileKey: string }>> => {
    return apiClient.post('/admin/user-products/presigned-url', { fileType });
  },

  /**
   * Xóa nhiều sản phẩm
   * @param ids Danh sách ID sản phẩm cần xóa
   * @returns Promise với kết quả xóa
   */
  deleteMultipleProducts: async (ids: number[]): Promise<ApiResponseDto<{ deletedCount: number }>> => {
    return apiClient.post('/admin/user-products/bulk-delete', { ids });
  },
};

export default ProductService;
