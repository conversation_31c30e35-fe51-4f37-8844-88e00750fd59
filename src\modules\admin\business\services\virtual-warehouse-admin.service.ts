import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  VirtualWarehouseAdminDto,
  VirtualWarehouseAdminDetailDto,
  QueryVirtualWarehouseAdminDto,
} from '../types/virtual-warehouse.types';

/**
 * Service xử lý các API liên quan đến kho ảo cho admin
 */
export const VirtualWarehouseAdminService = {
  /**
   * Lấy danh sách kho ảo
   * @param params Tham số truy vấn
   * @returns Promise với danh sách kho ảo và thông tin phân trang
   */
  getVirtualWarehouses: async (params?: QueryVirtualWarehouseAdminDto): Promise<ApiResponseDto<PaginatedResult<VirtualWarehouseAdminDto>>> => {
    return apiClient.get('/admin/virtual-warehouses', { params });
  },

  /**
   * Lấy chi tiết kho ảo theo ID
   * @param id ID của kho ảo
   * @returns Promise với thông tin chi tiết kho ảo
   */
  getVirtualWarehouseById: async (id: number): Promise<ApiResponseDto<VirtualWarehouseAdminDetailDto>> => {
    return apiClient.get(`/admin/virtual-warehouses/${id}`);
  },

  /**
   * Tạo kho ảo mới
   * @param data Dữ liệu kho ảo
   * @returns Promise với kết quả tạo mới
   */
  createVirtualWarehouse: async (data: Record<string, unknown>): Promise<ApiResponseDto<VirtualWarehouseAdminDto>> => {
    return apiClient.post('/admin/virtual-warehouses', data);
  },

  /**
   * Cập nhật kho ảo
   * @param id ID của kho ảo
   * @param data Dữ liệu cập nhật
   * @returns Promise với kết quả cập nhật
   */
  updateVirtualWarehouse: async (id: number, data: Record<string, unknown>): Promise<ApiResponseDto<VirtualWarehouseAdminDto>> => {
    return apiClient.put(`/admin/virtual-warehouses/${id}`, data);
  },

  /**
   * Xóa kho ảo
   * @param id ID của kho ảo
   * @returns Promise với kết quả xóa
   */
  deleteVirtualWarehouse: async (id: number): Promise<ApiResponseDto<void>> => {
    return apiClient.delete(`/admin/virtual-warehouses/${id}`);
  },

  /**
   * Cập nhật trạng thái nhiều kho ảo
   * @param data Dữ liệu cập nhật trạng thái
   * @returns Promise với kết quả cập nhật
   */
  updateMultipleVirtualWarehousesStatus: async (data: Record<string, unknown>): Promise<ApiResponseDto<void>> => {
    return apiClient.put('/admin/virtual-warehouses/bulk-status', data);
  },
};

export default VirtualWarehouseAdminService;
