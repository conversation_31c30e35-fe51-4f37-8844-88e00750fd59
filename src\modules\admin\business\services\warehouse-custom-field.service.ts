import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  WarehouseCustomFieldDto,
  WarehouseCustomFieldDetail,
  WarehouseCustomFieldQueryParams,
} from '../types/warehouse.types';



/**
 * Service xử lý API liên quan đến trường tùy chỉnh của kho cho admin
 */
export const WarehouseCustomFieldService = {
  /**
   * Lấy danh sách trường tùy chỉnh của kho với phân trang
   * @param params Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh với phân trang
   */
  getCustomFields: async (params?: WarehouseCustomFieldQueryParams): Promise<ApiResponseDto<PaginatedResult<WarehouseCustomFieldDto>>> => {
    return apiRequest.get('/admin/warehouse-custom-fields', { params });
  },

  /**
   * Lấy chi tiết trường tùy chỉnh của kho theo ID
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Chi tiết trường tùy chỉnh
   */
  getCustomFieldById: async (warehouseId: number, fieldId: number): Promise<ApiResponseDto<WarehouseCustomFieldDetail>> => {
    return apiRequest.get(`/admin/warehouse-custom-fields/warehouses/${warehouseId}/fields/${fieldId}`);
  },
};
