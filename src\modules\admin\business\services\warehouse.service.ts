import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  CreateWarehouseDto,
  UpdateWarehouseDto,
  WarehouseDetailDto,
  WarehouseDto,
  WarehouseListItemDto,
  WarehouseQueryParams,
} from '../types/warehouse.types';

/**
 * Service xử lý API liên quan đến kho cho admin
 */
export const WarehouseService = {
  /**
   * Lấy danh sách kho
   * @param params Tham số truy vấn
   * @returns Danh sách kho với phân trang
   */
  getWarehouses: async (params?: WarehouseQueryParams): Promise<ApiResponseDto<PaginatedResult<WarehouseListItemDto>>> => {
    return apiRequest.get('/admin/warehouses', { params });
  },

  /**
   * Lấy chi tiết kho theo ID
   * @param id ID của kho
   * @returns Chi tiết kho
   */
  getWarehouseById: async (id: number): Promise<ApiResponseDto<WarehouseDetailDto>> => {
    return apiRequest.get(`/admin/warehouses/${id}`);
  },

  /**
   * Tạo kho mới
   * @param data Dữ liệu tạo kho
   * @returns Thông tin kho đã tạo
   */
  createWarehouse: async (data: CreateWarehouseDto): Promise<ApiResponseDto<WarehouseDto>> => {
    return apiRequest.post('/admin/warehouses', data);
  },

  /**
   * Cập nhật kho
   * @param id ID của kho
   * @param data Dữ liệu cập nhật kho
   * @returns Thông tin kho đã cập nhật
   */
  updateWarehouse: async (id: number, data: UpdateWarehouseDto): Promise<ApiResponseDto<WarehouseDto>> => {
    return apiRequest.put(`/admin/warehouses/${id}`, data);
  },

  /**
   * Xóa kho
   * @param id ID của kho
   * @returns Thông báo xóa thành công
   */
  deleteWarehouse: async (id: number): Promise<ApiResponseDto<null>> => {
    return apiRequest.delete(`/admin/warehouses/${id}`);
  },
};
