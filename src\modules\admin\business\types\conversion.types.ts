import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái chuyển đổi
 */
export enum ConversionStatus {
  COMPLETED = 'completed',
  PENDING = 'pending',
  FAILED = 'failed',
}

/**
 * Enum cho loại chuyển đổi
 */
export enum ConversionType {
  LEAD = 'lead',
  CUSTOMER = 'customer',
  SALE = 'sale',
}

/**
 * Interface cho metadata của khách hàng chuyển đổi
 */
export interface CustomerConversionMetadata {
  fieldName: string;
  fieldValue: string;
}

/**
 * Interface cho email của khách hàng chuyển đổi
 */
export interface CustomerConversionEmail {
  primary?: string;
  secondary?: string;
}

/**
 * Interface cho thông tin khách hàng chuyển đổi từ API mới
 */
export interface CustomerConversionDto {
  id: number;
  avatar?: string;
  name: string;
  email?: CustomerConversionEmail;
  phone?: string;
  platform?: string;
  timezone?: string;
  createdAt: number;
  updatedAt?: number;
  userId: number;
  agentId?: string;
  metadata?: CustomerConversionMetadata[];
  address?: string;
}

/**
 * Interface cho thông tin khách hàng trong chuyển đổi (legacy)
 */
export interface ConversionCustomerDto {
  id: number;
  avatar?: string;
  name: string;
  email?: string | {
    primary?: string;
    secondary?: string;
  };
  phone?: string;
  platform?: string;
  timezone?: string;
  createdAt: number;
  updatedAt?: number;
  userId: number;
  agentId?: string;
  metadata?: {
    tags?: string[];
  };
}

/**
 * Interface cho thông tin chuyển đổi
 */
export interface ConversionDto {
  id: number;
  name: string;
  source: string;
  destination: string;
  status: ConversionStatus;
  type: ConversionType;
  value: number;
  date: string;
  customerId: number;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho danh sách chuyển đổi
 */
export interface ConversionListItemDto {
  id: string | number;
  convertCustomerId?: string | number;
  userId?: number;
  conversionType?: string;
  source?: string;
  destination?: string;
  status?: ConversionStatus;
  type?: ConversionType;
  value?: number;
  date?: string;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Interface cho chi tiết chuyển đổi
 */
export interface ConversionDetailDto {
  id: number;
  convertCustomerId: number;
  userId: number;
  conversionType: string;
  source: string;
  notes: string;
  content: Record<string, unknown>;
  createdAt: number;
  updatedAt: number;
  customer: ConversionCustomerDto;
}

/**
 * Interface cho tham số truy vấn khách hàng chuyển đổi
 */
export interface CustomerConversionQueryParams extends QueryDto {
  userId?: number;
  agentId?: string;
  platform?: string;
  createdAtFrom?: string;
  createdAtTo?: string;
}

/**
 * Interface cho tham số truy vấn chuyển đổi (legacy)
 */
export interface ConversionQueryParams extends QueryDto {
  status?: ConversionStatus;
  type?: ConversionType;
  startDate?: string;
  endDate?: string;
  source?: string;
  destination?: string;
}
