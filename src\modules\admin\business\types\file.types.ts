import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái file
 */
export enum FileStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Enum cho loại file
 */
export enum FileType {
  IMAGE = 'image',
  VIDEO = 'video',
  DOCUMENT = 'document',
  AUDIO = 'audio',
  OTHER = 'other',
}

/**
 * Interface cho thông tin folder của file
 */
export interface FolderInfoDto {
  id: number;
  name: string;
  path: string;
  root: number;
}

/**
 * Interface cho thông tin cơ bản của file
 */
export interface FileDto {
  id: number;
  name: string;
  originalName?: string;
  mimeType?: string;
  size: number;
  extension?: string;
  type?: FileType;
  url?: string;
  thumbnailUrl?: string;
  status?: FileStatus;
  folderId?: number;
  storageKey: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho danh sách file
 */
export interface FileResponseDto extends FileDto {
  folder?: FolderInfoDto;
}

/**
 * Interface cho chi tiết file
 */
export interface FileDetailResponseDto extends FileDto {
  folder?: FolderInfoDto;
  metadata?: Record<string, unknown>;
}

/**
 * Interface cho tham số truy vấn file
 */
export interface FileQueryParams extends QueryDto {
  folderId?: number;
  type?: FileType;
  status?: FileStatus;
  extension?: string;
}

/**
 * Interface cho dữ liệu tạo file
 */
export interface CreateFileDto {
  name: string;
  originalName: string;
  mimeType: string;
  size: number;
  extension: string;
  type: FileType;
  url?: string;
  thumbnailUrl?: string;
  folderId?: number;
  metadata?: Record<string, unknown>;
}

/**
 * Interface cho dữ liệu cập nhật file
 */
export interface UpdateFileDto {
  name?: string;
  folderId?: number;
  status?: FileStatus;
  metadata?: Record<string, unknown>;
}
