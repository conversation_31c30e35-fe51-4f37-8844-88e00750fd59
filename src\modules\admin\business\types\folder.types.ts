import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái folder
 */
export enum FolderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Interface cho thông tin folder cha
 */
export interface ParentFolderInfoDto {
  id: number;
  name: string;
  path?: string;
}

/**
 * Interface cho thông tin người sở hữu
 */
export interface OwnerInfoDto {
  id: number;
  name: string;
  email?: string;
}

/**
 * Interface cho thông tin cơ bản của folder
 */
export interface FolderDto {
  id: number;
  name: string;
  path: string;
  description?: string;
  parentId?: number;
  status?: FolderStatus;
  root?: number | null;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho danh sách folder
 */
export interface FolderResponseDto extends FolderDto {
  parent?: ParentFolderInfoDto;
  owner?: OwnerInfoDto;
  fileCount?: number;
  subFolderCount?: number;
}

/**
 * Interface cho chi tiết folder
 */
export interface FolderDetailResponseDto extends FolderDto {
  parent?: ParentFolderInfoDto;
  owner: OwnerInfoDto;
  fileCount: number;
  subFolderCount?: number;
  files?: unknown[];
  breadcrumbs?: ParentFolderInfoDto[];
}

/**
 * Interface cho tham số truy vấn folder
 */
export interface FolderQueryParams extends QueryDto {
  parentId?: number;
  status?: FolderStatus;
}

/**
 * Interface cho dữ liệu tạo folder
 */
export interface CreateFolderDto {
  name: string;
  description?: string;
  parentId?: number;
}

/**
 * Interface cho dữ liệu cập nhật folder
 */
export interface UpdateFolderDto {
  name?: string;
  description?: string;
  parentId?: number;
  status?: FolderStatus;
}
