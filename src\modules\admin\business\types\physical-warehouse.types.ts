import { QueryDto, SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Interface cho thông tin kho vật lý admin
 */
export interface PhysicalWarehouseAdminDto {
  warehouseId: number;
  address: string;
  capacity: number | null;
  warehouse: {
    warehouseId: number;
    name: string;
    description?: string;
    type: string;
    createdAt: string;
    updatedAt: string;
  };
}

/**
 * Interface cho chi tiết kho vật lý admin
 */
export interface PhysicalWarehouseAdminDetailDto extends PhysicalWarehouseAdminDto {
  customFields?: PhysicalWarehouseCustomFieldDto[];
}

/**
 * Interface cho trường tùy chỉnh của kho vật lý
 */
export interface PhysicalWarehouseCustomFieldDto {
  id: number;
  warehouseId: number;
  name: string;
  type: string;
  value: unknown;
  required: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho giá trị trường tùy chỉnh
 */
export interface CustomFieldValue {
  value: string;
}

/**
 * Interface cho trường tùy chỉnh từ API
 */
export interface WarehouseCustomField {
  fieldId: number;
  label: string;
  type: string;
  value: CustomFieldValue;
}

/**
 * Interface cho thông tin kho cơ bản
 */
export interface WarehouseInfo {
  warehouseId: number;
  name: string;
  description: string;
  type: string;
}

/**
 * Interface cho chi tiết kho vật lý từ API
 */
export interface WarehouseDetail {
  warehouseId: number;
  address: string;
  capacity: number;
  warehouse: WarehouseInfo;
  customFields: WarehouseCustomField[];
}

/**
 * Interface cho danh sách kho vật lý admin
 */
export interface PhysicalWarehouseAdminListItem {
  warehouseId: number;
  name: string;
  description?: string;
  address: string;
  capacity: number | null;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho tham số truy vấn kho vật lý admin
 */
export interface QueryPhysicalWarehouseAdminDto extends QueryDto {
  address?: string;
  minCapacity?: number;
  maxCapacity?: number;
  sortBy?: 'warehouseId' | 'address' | 'capacity' | 'createdAt';
  sortDirection?: SortDirection;
}

/**
 * Interface cho dữ liệu tạo kho vật lý admin
 */
export interface CreatePhysicalWarehouseAdminDto {
  warehouseId: number;
  address: string;
  capacity?: number;
}

/**
 * Interface cho dữ liệu cập nhật kho vật lý admin
 */
export interface UpdatePhysicalWarehouseAdminDto {
  address?: string;
  capacity?: number;
}

/**
 * Enum cho trạng thái kho vật lý
 */
export enum PhysicalWarehouseStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  MAINTENANCE = 'MAINTENANCE',
}

/**
 * Interface cho tham số truy vấn kho vật lý với filter
 */
export interface PhysicalWarehouseQueryParams extends QueryDto {
  address?: string;
  minCapacity?: number;
  maxCapacity?: number;
  status?: PhysicalWarehouseStatus;
  sortBy?: string;
  sortDirection?: SortDirection;
}
