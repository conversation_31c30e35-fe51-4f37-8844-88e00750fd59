import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái kho ảo
 */
export enum VirtualWarehouseStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

/**
 * Enum cho loại kho
 */
export enum WarehouseType {
  VIRTUAL = 'VIRTUAL',
  PHYSICAL = 'PHYSICAL',
}

/**
 * Interface cho trường tùy chỉnh
 */
export interface VirtualWarehouseCustomFieldDto {
  fieldId: number;
  label: string;
  type: string;
  value: {
    value: string;
  };
}

/**
 * Interface cho thông tin kho
 */
export interface WarehouseInfoDto {
  warehouseId: number;
  name: string;
  description: string;
  type: WarehouseType;
}

/**
 * DTO cho item trong danh sách kho ảo admin
 */
export interface VirtualWarehouseAdminDto {
  warehouseId: number;
  associatedSystem: string;
  purpose: string;
  warehouse: WarehouseInfoDto;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO cho chi tiết kho ảo admin
 */
export interface VirtualWarehouseAdminDetailDto {
  warehouseId: number;
  associatedSystem: string;
  purpose: string;
  warehouse: WarehouseInfoDto;
  customFields: VirtualWarehouseCustomFieldDto[];
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO cho query kho ảo admin
 */
export interface QueryVirtualWarehouseAdminDto extends QueryDto {
  associatedSystem?: string;
}

/**
 * DTO cho cập nhật trạng thái nhiều kho ảo
 */
export interface UpdateVirtualWarehouseStatusDto {
  warehouseIds: number[];
  status: string;
  reason?: string;
}
