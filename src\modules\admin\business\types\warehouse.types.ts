import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái kho
 */
export enum WarehouseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Enum định nghĩa các loại kho
 */
export enum WarehouseTypeEnum {
  /**
   * Kho vật lý
   */
  PHYSICAL = 'PHYSICAL',

  /**
   * Kho ảo
   */
  VIRTUAL = 'VIRTUAL',
}

/**
 * Interface cho thông tin địa chỉ kho
 */
export interface WarehouseAddress {
  street?: string;
  district?: string;
  city?: string;
  province?: string;
  country?: string;
  postalCode?: string;
}

/**
 * Interface cho thông tin liên hệ kho
 */
export interface WarehouseContact {
  name?: string;
  phone?: string;
  email?: string;
  position?: string;
}

/**
 * Interface cho thông tin kho
 */
export interface WarehouseDto {
  id: number;
  name: string;
  code: string;
  description?: string;
  address?: WarehouseAddress;
  contact?: WarehouseContact;
  status: WarehouseStatus;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho danh sách kho
 */
export interface WarehouseListItemDto {
  warehouseId: number;
  name: string;
  description?: string;
  type?: string;
  code?: string;
  status?: WarehouseStatus;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface cho chi tiết kho
 */
export interface WarehouseDetailDto extends WarehouseDto {
  customFields?: WarehouseCustomFieldDto[];
  type?: WarehouseTypeEnum;
}

/**
 * Interface cho trường tùy chỉnh của kho
 */
export interface WarehouseCustomFieldDto {
  fieldId: number;
  warehouseId: number;
  name: string;
  type: string;
  value: unknown;
  required: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho tham số truy vấn kho
 */
export interface WarehouseQueryParams extends QueryDto {
  status?: WarehouseStatus;
}

/**
 * Interface cho dữ liệu tạo kho
 */
export interface CreateWarehouseDto {
  name: string;
  description?: string;
  type: WarehouseTypeEnum;
}

/**
 * Interface cho dữ liệu cập nhật kho
 */
export interface UpdateWarehouseDto {
  name?: string;
  code?: string;
  description?: string;
  address?: WarehouseAddress;
  contact?: WarehouseContact;
  status?: WarehouseStatus;
}

/**
 * Interface cho dữ liệu tạo trường tùy chỉnh của kho
 */
export interface CreateWarehouseCustomFieldDto {
  name: string;
  type: string;
  value: unknown;
  required?: boolean;
  order?: number;
}

/**
 * Interface cho dữ liệu cập nhật trường tùy chỉnh của kho
 */
export interface UpdateWarehouseCustomFieldDto {
  name?: string;
  type?: string;
  value?: unknown;
  required?: boolean;
  order?: number;
}

/**
 * Interface cho chi tiết trường tùy chỉnh kho từ API
 */
export interface WarehouseCustomFieldDetail {
  /**
   * ID của kho
   */
  warehouseId: number;

  /**
   * ID của trường
   */
  fieldId: number;

  /**
   * Giá trị của trường
   */
  value: {
    value: string;
  };

  /**
   * Tên kho
   */
  warehouseName: string;

  /**
   * Nhãn hiển thị của trường
   */
  fieldLabel: string;

  /**
   * Chi tiết cấu hình trường
   */
  fieldDetails: {
    label: string;
    type: string;
    required: boolean;
    configJson: {
      maxLength?: number;
      description?: string;
      placeholder?: string;
      [key: string]: unknown;
    };
  };
}

/**
 * Interface cho tham số truy vấn trường tùy chỉnh kho
 */
export interface WarehouseCustomFieldQueryParams extends QueryDto {
  warehouseId?: number;
}
