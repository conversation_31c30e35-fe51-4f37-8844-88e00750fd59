import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import { ResponsiveGrid, Typography } from '@/shared/components/common';
import PageWrapper from '@/shared/components/common/PageWrapper';
import { IconName } from '@/shared/components/common';
import { adminMenuItems } from '@/shared/components/layout/chat-panel/menu-items';

/**
 * Trang dashboard cho quản trị viên
 */
const DashboardAdminPage: React.FC = () => {
  const { t } = useTranslation(['common', 'admin', 'users', 'employees', 'affiliate', 'integration', 'rpoint', 'marketplace', 'subscription', 'settings', 'data', 'business', 'tools']);

  // Danh sách các module quản trị
  const adminModules = adminMenuItems.map(item => ({
    id: item.id,
    title: t(item.label),
    description: t(`admin:modules.${item.id}.description`, `Quản lý ${t(item.label)}`),
    icon: item.icon as IconName,
    linkTo: item.path,
    gradientColor: getGradientColorForModule(item.id),
  }));

  // Hàm xác định màu gradient cho từng module
  function getGradientColorForModule(id: string): 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' {
    // Phân bổ các màu gradient cho các module
    const colorMap: Record<string, 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'> = {
      'admin-dashboard': 'primary',
      'admin-users': 'secondary',
      'admin-employees': 'info',
      'admin-affiliate': 'success',
      'admin-integration': 'warning',
      'admin-rpoint': 'primary',
      'admin-marketplace': 'secondary',
      'admin-subscription': 'info',
      'admin-settings': 'warning',
      'admin-data': 'success',
      'business': 'primary',
      'admin-tools': 'secondary',
    };

    return colorMap[id] || 'primary';
  }

  return (
    <PageWrapper>
      <div className="mb-8">
        <Typography variant="h3" className="mb-2">
          {t('admin:welcome', 'Chào mừng đến với Trang quản trị RedAI')}
        </Typography>
        <Typography variant="body1" color="muted">
          {t('admin:welcomeDescription', 'Quản lý và điều hành hệ thống RedAI')}
        </Typography>
      </div>

      <ResponsiveGrid
        gap={4}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }}
      >
        {adminModules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            linkTo={module.linkTo}
            gradientColor={module.gradientColor}
            className="h-full"
          />
        ))}
      </ResponsiveGrid>
    </PageWrapper>
  );
};

export default DashboardAdminPage;
