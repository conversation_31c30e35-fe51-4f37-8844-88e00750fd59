import { RouteObject } from 'react-router-dom';
import { Suspense } from 'react';
import { Loading } from '@/shared';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { lazy } from 'react';

const DashboardAdminPage = lazy(() => import('@/modules/admin/dashboard/pages/DashboardAdminPage'));

/**
 * Routes cho module dashboard
 */
export const dashboardAdminRoutes: RouteObject[] = [
  {
    path: '/admin',
    element: (
      <AdminLayout title="Dashboard">
        <Suspense fallback={<Loading />}>
          <DashboardAdminPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default dashboardAdminRoutes;
