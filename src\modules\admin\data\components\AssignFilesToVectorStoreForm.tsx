import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import AssignFilesTable from '@/modules/admin/data/components/tables/AssignFilesTable';

interface AssignFilesToVectorStoreFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isLoading?: boolean;
  vectorStoreId: string;
}

/**
 * Form gán file vào Vector Store
 */
const AssignFilesToVectorStoreForm: React.FC<AssignFilesToVectorStoreFormProps> = ({
  onSubmit,
  onCancel,
  isLoading = false,
  vectorStoreId,
}) => {
  const { t } = useTranslation();

  // Xử lý khi submit form
  const handleSubmit = useCallback(
    (fileIds: string[]) => {
      onSubmit({ fileIds, vectorStoreId });
    },
    [onSubmit, vectorStoreId]
  );

  return (
    <AssignFilesTable
      onSubmit={handleSubmit}
      onCancel={onCancel}
      title={t('data:vectorStore.assignFiles', 'Gán file vào Vector Store')}
      isLoading={isLoading}
      vectorStoreId={vectorStoreId}
    />
  );
};

export default AssignFilesToVectorStoreForm;
