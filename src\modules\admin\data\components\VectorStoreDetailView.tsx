import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Table,
  ConfirmDeleteModal,
  IconCard,
} from '@/shared/components/common';

import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import ActiveFilters, { FilterTag } from '@/modules/components/filters/ActiveFilters';

import { NotificationUtil } from '@/shared/utils/notification';

import {
  useVectorStoreDetail,
  useKnowledgeFiles,
  useRemoveFileFromVectorStore,
  useRemoveMultipleFilesFromVectorStore,
} from '@/modules/admin/data/knowledge-files/hooks';
import { KnowledgeFileDto, KnowledgeFileQueryParams } from '@/modules/admin/data/knowledge-files/types';

interface VectorStoreDetailViewProps {
  vectorStoreId: string;
  onClose: () => void;
  onAssignFiles: (vectorStoreId: string) => void;
  ownerType?: 'USER' | 'ADMIN' | undefined;
}

/**
 * Component hiển thị chi tiết Vector Store và danh sách file đã gán
 */
const VectorStoreDetailView: React.FC<VectorStoreDetailViewProps> = ({
  vectorStoreId,
  onClose,
  onAssignFiles,
  ownerType,
}) => {
  const { t } = useTranslation();
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<KnowledgeFileDto | null>(null);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // Tạo query params cho API
  const queryParams = useMemo<KnowledgeFileQueryParams>(() => {
    const params: KnowledgeFileQueryParams = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection,
      vectorStoreId: vectorStoreId, // Lọc theo vector store ID
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection, vectorStoreId]);

  // Lấy thông tin chi tiết Vector Store
  const { data: vectorStoreDetail, isLoading: isLoadingVectorStoreDetail } =
    useVectorStoreDetail(vectorStoreId);

  // Lấy danh sách file trong Vector Store
  const {
    data: vectorStoreFilesData,
    isLoading: isLoadingVectorStoreFiles,
    refetch: refetchVectorStoreFiles,
  } = useKnowledgeFiles(queryParams);

  // Hook để xóa file khỏi Vector Store
  const { mutateAsync: removeFileFromVectorStore } = useRemoveFileFromVectorStore();

  // Hook để xóa nhiều file khỏi Vector Store
  const { removeMultipleFiles } = useRemoveMultipleFilesFromVectorStore();

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((file: KnowledgeFileDto) => {
    setFileToDelete(file);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!fileToDelete) return;

    try {
      await removeFileFromVectorStore({
        vectorStoreId: vectorStoreId,
        fileId: fileToDelete.id,
      });
      refetchVectorStoreFiles();

      NotificationUtil.success({
        message: t('data:vectorStore.removeFileSuccess', 'Xóa file khỏi Vector Store thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error removing file from Vector Store:', error);
      NotificationUtil.error({
        message: t('data:vectorStore.removeFileError', 'Lỗi khi xóa file khỏi Vector Store'),
        duration: 3000,
      });
    } finally {
      setShowDeleteConfirm(false);
      setFileToDelete(null);
    }
  }, [fileToDelete, removeFileFromVectorStore, vectorStoreId, refetchVectorStoreFiles, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedFileIds.length === 0) return;

    try {
      // Xóa tất cả file đã chọn trong một API call
      await removeMultipleFiles(vectorStoreId, selectedFileIds);
      refetchVectorStoreFiles();
      setSelectedFileIds([]);

      NotificationUtil.success({
        message: t('data:vectorStore.removeFilesSuccess', 'Xóa các file đã chọn thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error removing multiple files from Vector Store:', error);
      NotificationUtil.error({
        message: t('data:vectorStore.removeFilesError', 'Lỗi khi xóa các file đã chọn'),
        duration: 3000,
      });
    } finally {
      setShowBulkDeleteConfirm(false);
    }
  }, [selectedFileIds, removeMultipleFiles, vectorStoreId, refetchVectorStoreFiles, t]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Tạo các hàm xử lý riêng để đảm bảo filter được tắt đúng cách
  const handleClearSearch = useCallback(() => {
    setSearchTerm('');
    // Cập nhật lại query params và gọi lại API
    setCurrentPage(1);
  }, []);

  const handleClearSort = useCallback(() => {
    setSortBy('createdAt');
    setSortDirection(SortDirection.DESC);
    // Cập nhật lại query params và gọi lại API
    setCurrentPage(1);
  }, []);

  const handleClearAll = useCallback(() => {
    handleClearSearch();
    handleClearSort();
  }, [handleClearSearch, handleClearSort]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Định nghĩa các cột cho bảng
  const columns = useMemo(
    () => [
      {
        key: 'name',
        title: t('data:knowledgeFiles.table.name', 'Tên file'),
        dataIndex: 'name',
        width: '30%',
      },
      {
        key: 'extension',
        title: t('data:knowledgeFiles.table.extension', 'Định dạng'),
        dataIndex: 'extension',
        width: '15%',
      },
      {
        key: 'storage',
        title: t('data:knowledgeFiles.table.size', 'Kích thước'),
        dataIndex: 'storage',
        width: '20%',
        render: (value: unknown) => {
          // Chuyển đổi byte sang KB, MB, GB
          const size = Number(value);
          if (isNaN(size)) return 'N/A';

          if (size < 1024) {
            return `${size} B`;
          } else if (size < 1024 * 1024) {
            return `${(size / 1024).toFixed(2)} KB`;
          } else if (size < 1024 * 1024 * 1024) {
            return `${(size / (1024 * 1024)).toFixed(2)} MB`;
          } else {
            return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
          }
        },
      },
      {
        key: 'createdAt',
        title: t('data:common.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '20%',
        render: (value: unknown) => {
          const timestamp = Number(value);
          return isNaN(timestamp) ? 'N/A' : new Date(timestamp).toLocaleString();
        },
      },
      {
        key: 'actions',
        title: '',
        width: '15%',
        render: (_: unknown, record: KnowledgeFileDto & { isAssigned?: boolean }) =>
          record.isAssigned && (
            <IconCard
              icon="trash"
              size="sm"
              variant="ghost"
              title={t('data:vectorStore.removeFile', 'Xóa file')}
              onClick={() => handleShowDeleteConfirm(record)}
            />
          ),
      },
    ],
    [t, handleShowDeleteConfirm]
  );

  // Tổng hợp trạng thái loading
  const isLoading = isLoadingVectorStoreFiles || isLoadingVectorStoreDetail;

  // Tạo danh sách các bộ lọc đang áp dụng
  const activeFilters = useMemo<FilterTag[]>(() => {
    const filters: FilterTag[] = [];

    // Thêm bộ lọc Vector Store
    filters.push({
      id: 'vectorStore',
      label: t('data:vectorStore.filter.vectorStore', 'Vector Store'),
      value: `Vector Store ID: ${vectorStoreId}`,
      onRemove: () => {
        // Không cho phép xóa bộ lọc Vector Store trong trường hợp này
      },
    });

    // Thêm bộ lọc sắp xếp
    if (sortBy && sortDirection) {
      const sortLabel = sortBy === 'createdAt' ? t('common:createdAt', 'Ngày tạo') : sortBy;
      const directionLabel =
        sortDirection === SortDirection.ASC
          ? t('common:ascending', 'Tăng dần')
          : t('common:descending', 'Giảm dần');

      filters.push({
        id: 'sortBy',
        label: t('common:sortBy', 'Sắp xếp theo'),
        value: `${sortLabel} (${directionLabel})`,
        onRemove: handleClearSort,
      });
    }

    return filters;
  }, [vectorStoreId, sortBy, sortDirection, handleClearSort, t]);

  return (
    <Card className="p-6">
      <div className="space-y-4">
        <Typography variant="h6" className="mb-4">
          {t('data:vectorStore.detail', 'Chi tiết Vector Store')}
        </Typography>

        {isLoading ? (
          <div className="py-4 text-center">
            <Typography>{t('common:loading', 'Đang tải...')}</Typography>
          </div>
        ) : vectorStoreDetail ? (
          <div>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <Typography variant="subtitle2">
                  {t('data:vectorStore.table.name', 'Tên')}
                </Typography>
                <Typography>{vectorStoreDetail.storeName}</Typography>
              </div>
              <div>
                <Typography variant="subtitle2">
                  {t('data:vectorStore.table.files', 'Số file')}
                </Typography>
                <Typography>{vectorStoreDetail.files || 0}</Typography>
              </div>
              <div>
                <Typography variant="subtitle2">
                  {t('data:vectorStore.table.size', 'Dung lượng')}
                </Typography>
                <Typography>
                  {(() => {
                    const totalSize = vectorStoreDetail.size || 0;
                    if (totalSize < 1024) return `${totalSize} B`;
                    if (totalSize < 1024 * 1024) return `${(totalSize / 1024).toFixed(2)} KB`;
                    if (totalSize < 1024 * 1024 * 1024)
                      return `${(totalSize / (1024 * 1024)).toFixed(2)} MB`;
                    return `${(totalSize / (1024 * 1024 * 1024)).toFixed(2)} GB`;
                  })()}
                </Typography>
              </div>
              <div>
                <Typography variant="subtitle2">
                  {t('data:vectorStore.table.agents', 'Số agents')}
                </Typography>
                <Typography>{vectorStoreDetail.agents || 0}</Typography>
              </div>
            </div>

            <div className="mb-4">
              <MenuIconBar
                onSearch={handleSearch}
                items={[]}
                additionalIcons={[
                  // Chỉ hiển thị icon gán file nếu ownerType không phải là "USER"
                  ...(ownerType !== 'USER' ? [{
                    icon: 'plus' as const,
                    tooltip: t('data:vectorStore.assignFiles', 'Gán file'),
                    variant: 'primary' as const,
                    onClick: () => onAssignFiles(vectorStoreId),
                  }] : []),
                  {
                    icon: 'trash' as const,
                    tooltip: t('data:vectorStore.removeFiles', 'Xóa file đã chọn'),
                    variant: 'primary' as const,
                    onClick: () => {
                      if (selectedFileIds.length > 0) {
                        setShowBulkDeleteConfirm(true);
                      } else {
                        NotificationUtil.info({
                          message: t(
                            'data:vectorStore.selectFilesToRemove',
                            'Vui lòng chọn ít nhất một file để xóa'
                          ),
                          duration: 3000,
                        });
                      }
                    },
                    condition: selectedFileIds.length > 0,
                  },
                ]}
                onColumnVisibilityChange={handleColumnVisibilityChange}
                columns={visibleColumns}
                showDateFilter={false}
                showColumnFilter={true}
              />

              {/* Hiển thị các bộ lọc đang áp dụng */}
              <ActiveFilters
                searchTerm={searchTerm}
                onClearSearch={handleClearSearch}
                customTags={activeFilters}
                onClearAll={handleClearAll}
              />
            </div>

            {vectorStoreFilesData && (
              <div>
                <Table
                  columns={columns}
                  data={
                    vectorStoreFilesData?.items.map(file => ({
                      ...file,
                      isAssigned: true, // Tất cả file trong vectorStoreFilesData đều đã được gán
                    })) || []
                  }
                  rowKey="id"
                  loading={isLoadingVectorStoreFiles}
                  rowSelection={{
                    selectedRowKeys: selectedFileIds,
                    onChange: (keys: React.Key[]) => setSelectedFileIds(keys as string[]),
                  }}
                  pagination={{
                    current: currentPage,
                    pageSize: itemsPerPage,
                    total: vectorStoreFilesData?.meta?.totalItems || 0,
                    onChange: handlePageChange,
                    showSizeChanger: true,
                    pageSizeOptions: [10, 20, 50, 100],
                    showFirstLastButtons: true,
                    showPageInfo: true,
                  }}
                />
              </div>
            )}
          </div>
        ) : (
          <div className="py-4 text-center">
            <Typography>{t('data:vectorStore.notFound', 'Không tìm thấy Vector Store')}</Typography>
          </div>
        )}

        <div className="flex justify-end">
          <IconCard
            icon="x"
            size="md"
            variant="secondary"
            title={t('common:close', 'Đóng')}
            onClick={onClose}
          />
        </div>
      </div>

      {/* Modal xác nhận xóa file */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('data:vectorStore.removeFileTitle', 'Xóa file khỏi Vector Store')}
        message={t(
          'data:vectorStore.removeFileMessage',
          'Bạn có chắc chắn muốn xóa file này khỏi Vector Store?'
        )}
        itemName={fileToDelete?.name || ''}
      />

      {/* Modal xác nhận xóa nhiều file */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('data:vectorStore.removeFilesTitle', 'Xóa các file đã chọn')}
        message={t(
          'data:vectorStore.removeFilesMessage',
          'Bạn có chắc chắn muốn xóa {{count}} file đã chọn khỏi Vector Store?',
          { count: selectedFileIds.length }
        )}
      />
    </Card>
  );
};

export default VectorStoreDetailView;
