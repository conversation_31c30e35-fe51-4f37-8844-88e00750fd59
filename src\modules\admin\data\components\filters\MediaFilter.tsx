import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Input, Select, Button, Icon, IconButton } from '@/shared/components/common';
import { MediaStatusEnum } from '@/modules/data/media/types/media.types';

interface MediaFilterProps {
  onSearch: (term: string) => void;
  onFilterChange: (status: MediaStatusEnum | 'all') => void;
  onReset: () => void;
  initialSearchTerm?: string;
  initialStatus?: MediaStatusEnum | 'all';
}

/**
 * Component filter cho trang quản lý media
 */
const MediaFilter: React.FC<MediaFilterProps> = ({
  onSearch,
  onFilterChange,
  onReset,
  initialSearchTerm = '',
  initialStatus = 'all',
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [status, setStatus] = useState<MediaStatusEnum | 'all'>(initialStatus);

  // Xử lý khi nhấn Enter trong ô tìm kiếm
  const handleSearchKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        onSearch(searchTerm);
      }
    },
    [searchTerm, onSearch]
  );

  // Xử lý khi xóa từ khóa tìm kiếm
  const handleClearSearch = useCallback(() => {
    setSearchTerm('');
    onSearch('');
  }, [onSearch]);

  // Xử lý khi thay đổi trạng thái
  const handleStatusChange = useCallback(
    (value: string | number | string[] | number[]) => {
      const newStatus = value as MediaStatusEnum | 'all';
      setStatus(newStatus);
      onFilterChange(newStatus);
    },
    [onFilterChange]
  );

  // Xử lý khi reset filter
  const handleReset = useCallback(() => {
    setSearchTerm('');
    setStatus('all');
    onReset();
  }, [onReset]);

  // Danh sách trạng thái
  const statusOptions = [
    { value: 'all', label: t('common:all', 'Tất cả') },
    { value: MediaStatusEnum.APPROVED, label: t('admin:data.media.status.APPROVED', 'Hoạt động') },
    { value: MediaStatusEnum.DRAFT, label: t('admin:data.media.status.DRAFT', 'Nháp') },
  ];

  return (
    <Card className="mb-4 p-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Input
            placeholder={t('common:search', 'Tìm kiếm')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleSearchKeyDown}
            fullWidth
            rightIcon={
              searchTerm ? (
                <IconButton icon="x" variant="ghost" size="sm" onClick={handleClearSearch} />
              ) : (
                <Icon name="search" />
              )
            }
          />
        </div>

        <div>
          <Select
            options={statusOptions}
            value={status}
            onChange={handleStatusChange}
            placeholder={t('admin:data.media.table.status', 'Trạng thái')}
            fullWidth
          />
        </div>

        <div className="flex items-center">
          <Button variant="outline" onClick={handleReset} className="ml-auto">
            <Icon name="refresh-cw" className="mr-2" />
            {t('common:reset', 'Đặt lại')}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default MediaFilter;
