import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormItem, Input, Checkbox, Card, IconCard, Typography } from '@/shared/components/common';
import { z } from 'zod';
import { FormRef } from '@/shared/components/common/Form/Form';
import { Controller } from 'react-hook-form';

/**
 * Schema cho form crawl URL
 */
const crawlUrlSchema = z.object({
  url: z.string()
    .min(1, 'URL không được để trống')
    .url('URL không hợp lệ'),
  depth: z.preprocess(
    (val) => (typeof val === 'string' ? parseInt(val, 10) : val),
    z.number()
      .min(1, 'Độ sâu tối thiểu là 1')
      .max(3, 'Độ sâu tối đa là 3')
  ),
  maxUrls: z.preprocess(
    (val) => (val === '' ? undefined : typeof val === 'string' ? parseInt(val, 10) : val),
    z.number()
      .min(1, '<PERSON><PERSON> lượng URL tối thiểu là 1')
      .max(100, 'Số lượng URL tối đa là 100')
      .optional()
  ),
  ignoreRobotsTxt: z.boolean().default(false),
});

export type CrawlUrlFormValues = z.infer<typeof crawlUrlSchema>;

interface CrawlUrlFormProps {
  onSubmit: (values: CrawlUrlFormValues) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

/**
 * Form component for crawling URLs
 */
const CrawlUrlForm: React.FC<CrawlUrlFormProps> = ({ onSubmit, onCancel, isLoading = false }) => {
  const { t } = useTranslation();

  // Form ref
  const formRef = useRef<FormRef<CrawlUrlFormValues>>(null);

  // Handle form submission
  const handleSubmit = (values: CrawlUrlFormValues) => {
    onSubmit(values);
  };

  return (
    <Card>
      <Typography variant="h6" className="mb-4">
        {t('data:url.crawlUrl', 'Crawl URL')}
      </Typography>

      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={crawlUrlSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        className="space-y-4"
        defaultValues={{
          depth: 1,
          maxUrls: 10,
          ignoreRobotsTxt: false,
        }}
      >
        <FormItem name="url" label={t('data:url.form.url', 'URL')} required>
          <Input
            fullWidth
            placeholder="https://example.com"
          />
        </FormItem>


        <FormItem name="depth" label={t('data:url.form.depth', 'Độ sâu')} required>
          <Input
            type="number"
            min={1}
            max={3}
            fullWidth
          />
        </FormItem>


        <FormItem name="maxUrls" label={t('data:url.form.maxUrls', 'Số lượng URL tối đa')}>
          <Input
            type="number"
            min={1}
            max={100}
            fullWidth
          />
        </FormItem>


        <FormItem name="ignoreRobotsTxt">
          <Controller
            name="ignoreRobotsTxt"
            render={({ field }) => (
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={field.value}
                  onChange={(checked) => {
                    field.onChange(checked);
                  }}
                  color="danger"
                  variant="filled"
                />
                <label
                  className="text-sm font-medium cursor-pointer"
                  onClick={() => field.onChange(!field.value)}
                >
                  {t('data:url.form.ignoreRobotsTxt', 'Bỏ qua robots.txt')}
                </label>
              </div>
            )}
          />
        </FormItem>


        <div className="flex justify-end space-x-3 mt-6">
          <IconCard
            icon="x"
            variant="default"
            onClick={onCancel}
            disabled={isLoading}
            title={t('common.cancel', 'Hủy')}
          />
          <IconCard
            icon="crawl"
            variant="primary"
            onClick={() => formRef.current?.submit()}
            disabled={isLoading}
            isLoading={isLoading}
            title={t('data:url.form.startCrawl', 'Bắt Đầu Crawl')}
          />
        </div>
      </Form>
    </Card>
  );
};

export default CrawlUrlForm;
