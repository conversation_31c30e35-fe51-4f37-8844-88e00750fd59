import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, IconCard } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';

interface KnowledgeFileCreateFormProps {
  onSubmit: (values: { files: File[] }) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

/**
 * Form tạo file tri thức
 */
const KnowledgeFileCreateForm: React.FC<KnowledgeFileCreateFormProps> = ({
  onSubmit,
  onCancel,
  isLoading,
}) => {
  const { t } = useTranslation(['data']);
  const [selectedFiles, setSelectedFiles] = useState<FileWithMetadata[]>([]);

  const handleFilesChange = (files: FileWithMetadata[]) => {
    setSelectedFiles(files);
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedFiles.length === 0) {
      NotificationUtil.info({
        message: t(
          'data:knowledgeFiles.selectFilesToUpload',
          'Please select at least one file to upload'
        ),
        duration: 3000,
      });
      return;
    }

    // Extract File objects from FileWithMetadata
    const files = selectedFiles.map(fileData => fileData.file);
    onSubmit({ files });
  };

  const handleSubmit = () => {
    if (selectedFiles.length === 0) {
      NotificationUtil.info({
        message: t(
          'data:knowledgeFiles.selectFilesToUpload',
          'Please select at least one file to upload'
        ),
        duration: 3000,
      });
      return;
    }

    // Extract File objects from FileWithMetadata
    const files = selectedFiles.map(fileData => fileData.file);
    onSubmit({ files });
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800">
      <form onSubmit={handleFormSubmit} className="space-y-6">
        <Typography variant="h5" className="mb-4">
          {t('data:knowledgeFiles.uploadFiles', 'Tải lên File tri thức')}
        </Typography>

        <div className="space-y-4">
          <MultiFileUpload
            value={selectedFiles}
            onChange={handleFilesChange}
            accept=".pdf,.docx,.txt,.csv,.json,.doc,.xlsx,.xls"
            label={t('data:knowledgeFiles.selectFiles', 'Chọn file tri thức')}
            placeholder={t(
              'data:knowledgeFiles.dragAndDrop',
              'Kéo và thả các file vào đây hoặc nhấp để tải lên'
            )}
            showPreview={true}
            required={true}
            height="h-40"
          />
        </div>

        <div className="flex justify-end space-x-3">
          <IconCard
            icon="x"
            title={t('common:cancel', 'Hủy')}
            variant="default"
            onClick={onCancel}
            disabled={!!isLoading}
          />
          <IconCard
            icon="upload"
            title={isLoading ? t('common:uploading', 'Đang tải lên...') : t('common:upload', 'Tải lên')}
            variant="primary"
            onClick={handleSubmit}
            disabled={!!isLoading || selectedFiles.length === 0}
            isLoading={!!isLoading}
          />
        </div>
      </form>
    </div>
  );
};

export default KnowledgeFileCreateForm;
