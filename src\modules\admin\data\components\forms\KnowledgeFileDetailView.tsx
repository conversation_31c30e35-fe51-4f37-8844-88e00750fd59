import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, ConfirmDeleteModal } from '@/shared/components/common';
import { KnowledgeFileDto } from '@/modules/admin/data/knowledge-files/types';
import { formatDate } from '@/shared/utils/format';

// Hàm formatBytes
const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

interface KnowledgeFileDetailViewProps {
  file: KnowledgeFileDto;
  onClose: () => void;
  onDelete: (file: KnowledgeFileDto) => void;
}

/**
 * Component hiển thị chi tiết file tri thức
 */
const KnowledgeFileDetailView: React.FC<KnowledgeFileDetailViewProps> = ({
  file,
  onClose,
  onDelete,
}) => {
  const { t } = useTranslation(['admin', 'data', 'common']);

  // State cho popup xác nhận xóa
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);



  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(() => {
    onDelete(file);
    setShowDeleteConfirm(false);
    onClose();
  }, [file, onDelete, onClose]);

  return (
    <>
      <div className="p-6 bg-white dark:bg-gray-800">
        <div className="space-y-6">
          <Typography variant="h5" className="mb-4 dark:text-primary-400">
            {t('admin:data.knowledgeFiles.viewFile', 'View Knowledge File')}
          </Typography>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300 mb-1"
                >
                  {t('admin:data.knowledgeFiles.fileName', 'File name')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {file.name}
                </Typography>
              </div>

              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300 mb-1"
                >
                  {t('admin:data.knowledgeFiles.fileSize', 'Size')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {formatBytes(file.storage)}
                </Typography>
              </div>

              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300 mb-1"
                >
                  {t('admin:data.knowledgeFiles.fileType', 'File type')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {file.extension || '-'}
                </Typography>
              </div>

              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300 mb-1"
                >
                  {t('admin:data.knowledgeFiles.vectorStore', 'Vector Store')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {file.vectorStoreName ||
                    t(
                      'admin:data.knowledgeFiles.noVectorStore',
                      'Not assigned to any vector store'
                    )}
                </Typography>
              </div>

              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300 mb-1"
                >
                  {t('admin:data.knowledgeFiles.createdAt', 'Created at')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {formatDate(file.createdAt)}
                </Typography>
              </div>

           
            </div>

          </div>

          {/* Actions */}
          <div className="flex justify-end  w-full pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="outline" onClick={onClose}>
              {t('common:close', 'Close')}
            </Button>
            
         
          </div>
        </div>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:data.common.confirmDelete', 'Confirm Delete')}
        message={t(
          'admin:data.knowledgeFiles.confirmDeleteMessage',
          'Are you sure you want to delete this knowledge file?'
        )}
        itemName={file.name}
      />
    </>
  );
};

export default KnowledgeFileDetailView;
