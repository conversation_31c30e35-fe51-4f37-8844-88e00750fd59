import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Typography,
  Textarea,
  TagsInput,
  Card,
  Checkbox,
  IconCard,
} from '@/shared/components/common';
import { Controller } from 'react-hook-form';
import { z } from 'zod';
import { useFormErrors } from '@/shared/hooks/form';
import { Url } from '@/modules/admin/data/url/types/url.types';

// Schema cho form URL
const urlSchema = z.object({
  url: z.string().url('URL không hợp lệ').min(1, 'URL là bắt buộc'),
  title: z.string().min(1, 'Tiêu đề là bắt buộc'),
  content: z.string().optional(),
  tags: z.union([z.string(), z.array(z.string())]).optional(),
  isActive: z.boolean().optional(),
});

export type URLFormValues = z.infer<typeof urlSchema>;

/**
 * Props cho URLForm
 */
interface URLFormProps {
  /**
   * Giá trị ban đầu cho form (dùng khi chỉnh sửa hoặc xem chi tiết)
   */
  initialValues?: Partial<Url>;

  /**
   * Callback khi form được submit
   */
  onSubmit: (values: URLFormValues) => Promise<void> | void;

  /**
   * Callback khi người dùng hủy thao tác
   */
  onCancel: () => void;

  /**
   * Callback khi có lỗi từ API
   */
  onError?: (error: unknown) => void;

  /**
   * Chế độ chỉ đọc (dùng khi xem chi tiết)
   */
  readOnly?: boolean;

  /**
   * Trạng thái đang submit
   */
  isSubmitting?: boolean;
}

/**
 * Form để tạo mới, chỉnh sửa hoặc xem chi tiết URL
 */
const URLForm: React.FC<URLFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  onError,
  readOnly = false,
  isSubmitting = false,
}) => {
  const { t } = useTranslation();

  // Sử dụng useFormErrors hook để quản lý lỗi form
  const { formRef, setFormErrors } = useFormErrors<URLFormValues>();

  // Xử lý submit với error handling
  const handleSubmit = async (values: URLFormValues) => {
    try {
      console.log('Admin URLForm: Starting form submission with values:', values);
      await onSubmit(values);
      console.log('Admin URLForm: Form submission successful');
    } catch (error: unknown) {
      console.log('Admin URLForm: Caught error during submission:', error);
      // Define error interface for type safety
      interface ApiErrorWithCode {
        code?: number;
        message?: string;
        response?: {
          data?: {
            code?: number;
            message?: string;
          };
        };
      }

      const typedError = error as ApiErrorWithCode;

      console.log('Admin URLForm: Error structure analysis:', {
        error,
        errorType: typeof error,
        errorCode: typedError?.code,
        errorMessage: typedError?.message,
        hasCodeProperty: error && typeof error === 'object' && 'code' in error,
        codeValue: error && typeof error === 'object' && 'code' in error ? typedError.code : 'NO_CODE',
        codeType: error && typeof error === 'object' && 'code' in error ? typeof typedError.code : 'NO_CODE_TYPE',
      });

      // Xử lý lỗi 30004 - URL đã tồn tại
      if (error && typeof error === 'object' && 'code' in error && typedError.code === 30004) {
        console.log('Admin URLForm: Detected error 30004, setting form field error');
        const errorMessage = (error as { message?: string }).message || 'URL này đã tồn tại trong hệ thống của bạn';
        console.log('Admin URLForm: Setting error message:', errorMessage);

        setFormErrors({
          url: errorMessage,
        });

        console.log('Admin URLForm: Form errors set successfully');
        return; // Không gọi onError nếu đã xử lý
      } else {
        console.log('Admin URLForm: Error is not 30004, calling onError callback');
        // Gọi callback onError nếu có
        onError?.(error);
      }
    }
  };

  return (
    <Card>
      <Typography variant="h6" className="mb-4">
        {readOnly
          ? t('data:url.viewUrl', 'Xem URL')
          : initialValues?.id
            ? t('data:url.editUrl', 'Chỉnh sửa URL')
            : t('data:url.addNew', 'Thêm URL mới')}
      </Typography>

      <Form
        // @ts-expect-error - Form component has type compatibility issues with generic FormRef
        ref={formRef}
        defaultValues={initialValues}
        schema={urlSchema}
        // @ts-expect-error - Form component has type compatibility issues with onSubmit
        onSubmit={handleSubmit}
        className="space-y-4"
      >
        <FormItem name="url" label={t('data:url.form.url', 'URL')} required>
          <Input
            fullWidth
            readOnly={readOnly}
            placeholder={t('data:url.form.urlPlaceholder', 'https://example.com')}
          />
        </FormItem>
        {!readOnly && (
          <div className="text-xs text-gray-500 -mt-3 mb-3">
            {t('data:url.form.urlDescription', 'Nhập URL đầy đủ bao gồm http:// hoặc https://')}
          </div>
        )}

        <FormItem name="title" label={t('data:url.form.title', 'Tiêu đề')} required>
          <Input
            fullWidth
            readOnly={readOnly}
            placeholder={t('data:url.form.titlePlaceholder', 'Nhập tiêu đề')}
          />
        </FormItem>

        <FormItem name="content" label={t('data:url.form.description', 'Mô tả')}>
          <Textarea
            fullWidth
            readOnly={readOnly}
            rows={3}
            placeholder={t('data:url.form.descriptionPlaceholder', 'Nhập mô tả (không bắt buộc)')}
          />
        </FormItem>

        <FormItem name="tags" label={t('data:url.form.tags', 'Tags')}>
          <TagsInput
            fieldName="tags"
            formRef={formRef}
            placeholder={t('data:url.form.tagsPlaceholder', 'Nhập tag và nhấn Enter')}
            initialValue={
              Array.isArray(initialValues?.tags)
                ? initialValues?.tags.join(', ')
                : initialValues?.tags || ''
            }
            readOnly={readOnly}
          />
        </FormItem>
        {!readOnly && (
          <div className="text-xs text-gray-500 -mt-3 mb-3">
            {t(
              'data:url.form.tagsDescription',
              'Các tags giúp phân loại và tìm kiếm URL dễ dàng hơn'
            )}
          </div>
        )}

        <FormItem name="isActive">
          <Controller
            name="isActive"
            render={({ field }) => (
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={field.value ?? true}
                  onChange={(checked) => {
                    field.onChange(checked);
                  }}
                  disabled={readOnly}
                  color="primary"
                  variant="filled"
                />
                <label
                  className="text-sm font-medium cursor-pointer"
                  onClick={() => !readOnly && field.onChange(!field.value)}
                >
                  {t('data:url.form.activeStatus', 'Kích hoạt')}
                </label>
              </div>
            )}
          />
        </FormItem>

        <div className="flex justify-end space-x-3 mt-6">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={readOnly ? t('common.close', 'Đóng') : t('common.cancel', 'Hủy')}
            onClick={onCancel}
            disabled={isSubmitting}
          />
          {!readOnly && (
            <IconCard
              icon="save"
              variant="primary"
              size="md"
              title={t('common.save', 'Lưu')}
              onClick={() => {
                // Trigger form submit programmatically
                formRef.current?.submit();
              }}
              disabled={isSubmitting}
              isLoading={isSubmitting}
            />
          )}
        </div>
      </Form>
    </Card>
  );
};

export default URLForm;
