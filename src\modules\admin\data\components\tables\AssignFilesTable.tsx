import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, Typography, IconCard } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { NotificationUtil } from '@/shared/utils/notification';

import { useUnassignedKnowledgeFiles } from '@/modules/admin/data/knowledge-files/hooks';
import {
  KnowledgeFileDto,
  KnowledgeFileQueryParams,
} from '@/modules/admin/data/knowledge-files/types';
import { formatDate } from '@/shared/utils/format';

interface AssignFilesTableProps {
  onSubmit: (fileIds: string[]) => void;
  onCancel: () => void;
  title: string;
  isLoading?: boolean;
  vectorStoreId: string;
}

// comment

/**
 * Component hiển thị bảng chọn file để gán vào Vector Store
 */
const AssignFilesTable: React.FC<AssignFilesTableProps> = ({
  onSubmit,
  onCancel,
  title,
  isLoading = false,
  vectorStoreId,
}) => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<KnowledgeFileDto[]>([]);
  const [isLoadingFiles, setIsLoadingFiles] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // State cho chọn nhiều file
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Tạo query params cho API
  const queryParams = useMemo<KnowledgeFileQueryParams & { vectorStoreId: string }>(() => {
    const params: KnowledgeFileQueryParams & { vectorStoreId: string } = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection,
      vectorStoreId: vectorStoreId,
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection, vectorStoreId]);

  // Hooks để gọi API
  const {
    data: knowledgeFilesData,
    isLoading: isLoadingFilesData,
    error: filesError,
  } = useUnassignedKnowledgeFiles(queryParams);

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (knowledgeFilesData) {
      setFiles(knowledgeFilesData.items);
      setTotalItems(knowledgeFilesData.meta.totalItems);
    }

    setIsLoadingFiles(isLoadingFilesData);
  }, [knowledgeFilesData, filesError, isLoadingFilesData]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý khi submit form
  const handleSubmit = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('data:vectorStore.selectFilesWarning', 'Vui lòng chọn ít nhất một file'),
      });
      return;
    }

    onSubmit(selectedRowKeys as string[]);
  }, [onSubmit, selectedRowKeys, t]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('data:knowledgeFiles.table.name', 'Tên file'),
        dataIndex: 'name',
        width: '30%',
        sortable: true,
      },
      {
        key: 'extension',
        title: t('data:knowledgeFiles.table.extension', 'Định dạng'),
        dataIndex: 'extension',
        width: '15%',
        sortable: true,
      },
      {
        key: 'storage',
        title: t('data:knowledgeFiles.table.size', 'Kích thước'),
        dataIndex: 'storage',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          // Chuyển đổi byte sang KB, MB, GB
          const size = Number(value);
          if (isNaN(size)) return 'N/A';

          if (size < 1024) {
            return `${size} B`;
          } else if (size < 1024 * 1024) {
            return `${(size / 1024).toFixed(2)} KB`;
          } else if (size < 1024 * 1024 * 1024) {
            return `${(size / (1024 * 1024)).toFixed(2)} MB`;
          } else {
            return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
          }
        },
      },
      {
        key: 'createdAt',
        title: t('data:common.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '20%',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    return allColumns.filter(col => visibleColumnIds.includes(col.key));
  }, [t, visibleColumns]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(col => visibleColumns.find(vc => vc.id === col.key)?.visible);
  }, [columns, visibleColumns]);

  return (
    <Card>
      <Typography variant="h6" className="mb-4">
        {title}
      </Typography>

      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            items={[
              {
                id: 'all',
                label: t('common.all', 'Tất cả'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />
        </div>

        <div>
          <Table<KnowledgeFileDto>
            columns={filteredColumns}
            data={files}
            rowKey="id"
            loading={isLoadingFiles}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </div>

        {/* Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <IconCard
            icon="x"
            title={t('common:cancel', 'Hủy')}
            variant="default"
            onClick={onCancel}
            disabled={!!isLoading}
          />
          <IconCard
            icon="check"
            title={t('data:vectorStore.assignButton', 'Gán file')}
            variant="primary"
            onClick={handleSubmit}
            disabled={!!isLoading || selectedRowKeys.length === 0}
            isLoading={!!isLoading}
          />
        </div>
      </div>
    </Card>
  );
};

export default AssignFilesTable;
