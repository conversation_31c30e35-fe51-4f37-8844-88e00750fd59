import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, Chip } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';
import { AdminMediaDto } from '@/modules/admin/data/media/types/media.types';
import { MediaStatusEnum } from '@/modules/data/media/types/media.types';
import { formatFileSize, formatTimestamp } from '@/modules/admin/data/utils';

interface MediaTableProps {
  data: AdminMediaDto[];
  loading: boolean;
  totalItems: number;
  currentPage: number;
  itemsPerPage: number;
  sortBy: string;
  sortDirection: SortDirection;
  selectedRowKeys: string[];
  visibleColumns: string[];
  onPageChange: (page: number, pageSize: number) => void;
  onSortChange: (column: string | null, order: SortOrder) => void;
  onRowSelectionChange: (selectedRowKeys: React.Key[]) => void;
  onView: (record: AdminMediaDto) => void;
  onDelete: (record: AdminMediaDto) => void;
}

/**
 * Component hiển thị bảng danh sách media
 */
const MediaTable: React.FC<MediaTableProps> = ({
  data,
  loading,
  totalItems,
  currentPage,
  itemsPerPage,
  sortBy,
  sortDirection,
  selectedRowKeys,
  visibleColumns,
  onPageChange,
  onSortChange,
  onRowSelectionChange,
  onView,
  onDelete,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('admin:data.media.table.name', 'Tên file'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('admin:data.media.table.description', 'Mô tả'),
        dataIndex: 'description',
        width: '25%',
      },
      {
        key: 'size',
        title: t('admin:data.media.table.size', 'Kích thước'),
        dataIndex: 'size',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return formatFileSize(value as number);
        },
      },
      {
        key: 'author',
        title: t('admin:data.media.table.author', 'Người tạo'),
        dataIndex: 'author',
        width: '15%',
        render: (value: unknown, record: AdminMediaDto) => {
          return (
            <div className="flex flex-col">
              <span>{value as string}</span>
              <span className="text-xs text-gray-500">ID: {record.ownedBy}</span>
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('admin:data.media.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return formatTimestamp(value as number);
        },
      },
      {
        key: 'status',
        title: t('admin:data.media.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as MediaStatusEnum;
          let variant: 'default' | 'primary' | 'success' | 'warning' | 'danger' = 'default';

          switch (status) {
            case MediaStatusEnum.APPROVED:
              variant = 'success';
              break;
            case MediaStatusEnum.PENDING:
              variant = 'warning';
              break;
            case MediaStatusEnum.DRAFT:
              variant = 'default';
              break;
            case MediaStatusEnum.REJECTED:
              variant = 'danger';
              break;
            case MediaStatusEnum.DELETED:
              variant = 'danger';
              break;
          }

          return (
            <Chip size="sm" variant={variant}>
              {t(`admin:data.media.status.${status}`, status)}
            </Chip>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions', ''),
        render: (_: unknown, record: AdminMediaDto) => {
          const menuItems = [
            {
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => onView(record),
            },
            {
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => onDelete(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumns.includes(col.key));
  }, [t, visibleColumns, onView, onDelete]);

  return (
    <Table<AdminMediaDto>
      columns={columns}
      data={data}
      rowKey="id"
      loading={loading}
      sortable={true}
      onSortChange={onSortChange}
      defaultSort={{
        column: sortBy || '',
        order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
      }}
      pagination={{
        current: currentPage,
        pageSize: itemsPerPage,
        total: totalItems,
        onChange: onPageChange,
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 50, 100],
        showFirstLastButtons: true,
        showPageInfo: true,
      }}
      rowSelection={{
        selectedRowKeys,
        onChange: onRowSelectionChange,
      }}
    />
  );
};

export default MediaTable;
