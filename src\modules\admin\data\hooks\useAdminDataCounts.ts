import { useAdminMediaList } from '../media/hooks/useMedia';
import { useKnowledgeFiles } from '../knowledge-files/hooks/useKnowledgeFile';
import { useUrlsQuery } from '../url/hooks/useUrlQuery';
import { useVectorStores } from '../knowledge-files/hooks/useVectorStore';

/**
 * Interface cho kết quả đếm số lượng
 */
interface AdminCountsResult {
  mediaCount: number;
  knowledgeFilesCount: number;
  urlCount: number;
  vectorStoreCount: number;
  isLoading: boolean;
  isError: boolean;
}

/**
 * Hook để lấy số lượng các loại dữ liệu cho Admin
 * @returns Số lượng các loại dữ liệu
 */
export const useAdminDataCounts = (): AdminCountsResult => {
  // Lấy số lượng media
  const {
    data: mediaData,
    isLoading: isMediaLoading,
    isError: isMediaError,
  } = useAdminMediaList({
    page: 1,
    limit: 1,
  });

  // Lấy số lượng file tri thức
  const {
    data: knowledgeFilesData,
    isLoading: isKnowledgeFilesLoading,
    isError: isKnowledgeFilesError,
  } = useKnowledgeFiles({
    page: 1,
    limit: 10, // Tăng limit để đảm bảo lấy được dữ liệu
  });

  // Lấy số lượng URL
  const {
    data: urlData,
    isLoading: isUrlLoading,
    isError: isUrlError,
  } = useUrlsQuery({
    page: 1,
    limit: 1,
  });

  // Lấy số lượng vector store
  const {
    data: vectorStoreData,
    isLoading: isVectorStoreLoading,
    isError: isVectorStoreError,
  } = useVectorStores({
    page: 1,
    limit: 1,
  });

  // Xử lý dữ liệu an toàn hơn
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getCount = (data: any) => {
    if (!data) return 0;
    if (data.meta && typeof data.meta.totalItems === 'number') return data.meta.totalItems;
    if (data.items && Array.isArray(data.items)) return data.items.length;
    return 0;
  };

  // Log dữ liệu để debug
  console.log('Knowledge Files Data:', knowledgeFilesData);
  console.log('Knowledge Files Meta:', knowledgeFilesData?.meta);
  console.log('Knowledge Files Items:', knowledgeFilesData?.items);
  console.log('Knowledge Files Count from getCount:', getCount(knowledgeFilesData));

  return {
    mediaCount: getCount(mediaData),
    knowledgeFilesCount: getCount(knowledgeFilesData),
    urlCount: getCount(urlData),
    vectorStoreCount: getCount(vectorStoreData),
    isLoading: isMediaLoading || isKnowledgeFilesLoading || isUrlLoading || isVectorStoreLoading,
    isError: isMediaError || isKnowledgeFilesError || isUrlError || isVectorStoreError,
  };
};
