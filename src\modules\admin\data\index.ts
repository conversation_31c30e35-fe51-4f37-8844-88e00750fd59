/**
 * Export các components, hooks, services, types cho module admin data
 */
export * from './components';
export * from './hooks';
export * from './pages';
export * from './routers/adminDataRoutes';

// Export các module con với named exports để tránh xung đột
export * from './media';
export {
  // Knowledge files services
  knowledgeFileService,
  vectorStoreService,
  // Knowledge files hooks
  useKnowledgeFiles,
  useCreateKnowledgeFiles,
  useDeleteKnowledgeFile,
  useVectorStores,
  useCreateVectorStore,
  useDeleteVectorStore,
  KNOWLEDGE_FILE_QUERY_KEYS,
  VECTOR_STORE_QUERY_KEYS,
  // Knowledge files schemas (với alias để tránh xung đột)
  KnowledgeFileSchema,
  KnowledgeFileQueryParamsSchema,
  CreateKnowledgeFileSchema,
  KnowledgeFileListResponseSchema,
  ApiResponseSchema as KnowledgeFileApiResponseSchema,
  VectorStoreSchema,
  CreateVectorStoreSchema,
  VectorStoreQueryParamsSchema,
  VectorStoreListResponseSchema,
  VectorStoreFilesResponseSchema,
} from './knowledge-files';

// Export types separately
export type {
  KnowledgeFileDto,
  CreateKnowledgeFileDto,
  KnowledgeFileQueryParams,
  KnowledgeFileListResponse,
  VectorStoreDto,
  CreateVectorStoreDto,
  VectorStoreQueryParams,
  VectorStoreListResponse,
} from './knowledge-files';
export * from './url';
