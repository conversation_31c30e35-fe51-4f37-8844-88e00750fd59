import { apiClient } from '@/shared/api/axios';
import {
  ApiResponse,
  AssignFilesToVectorStoreDto,
  CreateVectorStoreDto,
  VectorStoreDto,
  VectorStoreFilesResponse,
  VectorStoreListResponse,
  VectorStoreQueryParams,
} from '../types';

/**
 * Service để tương tác với API quản lý vector store
 */
export class VectorStoreService {
  private baseUrl = '/admin';

  /**
   * L<PERSON>y danh sách vector store
   * @param params Tham số truy vấn
   * @returns Danh sách vector store và thông tin phân trang
   */
  async getVectorStores(params?: VectorStoreQueryParams): Promise<VectorStoreListResponse> {
    try {
      // Xây dựng query string từ params
      const queryParams = new URLSearchParams();
      if (params) {
        if (params.search) queryParams.append('search', params.search);
        if (params.page) queryParams.append('page', params.page.toString());
        if (params.limit) queryParams.append('limit', params.limit.toString());
        if (params.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);
      }

      const queryString = queryParams.toString();
      const url = `${this.baseUrl}/vector-stores${queryString ? `?${queryString}` : ''}`;

      const response = await apiClient.get<VectorStoreListResponse>(url, { tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error('[VectorStoreService] Error fetching vector stores:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết vector store
   * @param vectorStoreId ID của vector store
   * @returns Thông tin chi tiết vector store
   */
  async getVectorStoreDetail(vectorStoreId: string): Promise<VectorStoreDto> {
    try {
      const url = `${this.baseUrl}/vector-stores/${vectorStoreId}`;
      const response = await apiClient.get<VectorStoreDto>(url, { tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error(`[VectorStoreService] Error fetching vector store detail with ID ${vectorStoreId}:`, error);
      throw error;
    }
  }

  /**
   * Tạo vector store mới
   * @param data Dữ liệu tạo vector store
   * @returns Thông báo kết quả
   */
  async createVectorStore(data: CreateVectorStoreDto): Promise<ApiResponse> {
    try {
      const response = await apiClient.post<ApiResponse>(`${this.baseUrl}/vector-stores`, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('[VectorStoreService] Error creating vector store:', error);
      throw error;
    }
  }

  /**
   * Xem chi tiết vector store (danh sách file trong vector store)
   * @param vectorStoreId ID của vector store
   * @param page Số trang
   * @param limit Số lượng kết quả trên một trang
   * @returns Danh sách file trong vector store và thông tin phân trang
   */
  async getVectorStoreFiles(
    vectorStoreId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<VectorStoreFilesResponse> {
    try {
      const url = `${this.baseUrl}/vector-stores/${vectorStoreId}/files?page=${page}&limit=${limit}`;
      const response = await apiClient.get<VectorStoreFilesResponse>(url, { tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error(`[VectorStoreService] Error fetching files for vector store with ID ${vectorStoreId}:`, error);
      throw error;
    }
  }

  /**
   * Gỡ file khỏi vector store
   * @param vectorStoreId ID của vector store
   * @param fileId ID của file cần gỡ
   * @returns Thông báo kết quả
   */
  async removeFileFromVectorStore(vectorStoreId: string, fileId: string): Promise<ApiResponse> {
    try {
      const url = `${this.baseUrl}/vector-stores/${vectorStoreId}/files/${fileId}`;
      const response = await apiClient.delete<ApiResponse>(url, { tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error(`[VectorStoreService] Error removing file ${fileId} from vector store ${vectorStoreId}:`, error);
      throw error;
    }
  }

  /**
   * Gỡ nhiều file khỏi vector store
   * @param vectorStoreId ID của vector store
   * @param fileIds Mảng ID của các file cần gỡ
   * @returns Thông báo kết quả
   */
  async removeMultipleFilesFromVectorStore(vectorStoreId: string, fileIds: string[]): Promise<ApiResponse> {
    try {
      const url = `${this.baseUrl}/vector-stores/${vectorStoreId}/files`;
      const response = await apiClient.delete<ApiResponse>(url, {
        data: { fileIds },
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`[VectorStoreService] Error removing multiple files from vector store ${vectorStoreId}:`, error);
      throw error;
    }
  }

  /**
   * Xóa một hoặc nhiều vector store
   * @param storeIds ID hoặc mảng ID của các vector store cần xóa
   * @returns Thông báo kết quả
   */
  async deleteVectorStores(storeIds: string | string[]): Promise<ApiResponse> {
    try {
      // Chuyển đổi storeId thành mảng nếu chỉ truyền vào một ID
      const ids = Array.isArray(storeIds) ? storeIds : [storeIds];

      // Gửi request với body chứa mảng storeIds
      const response = await apiClient.delete<ApiResponse>(`${this.baseUrl}/vector-stores`, {
        data: { storeIds: ids },
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('[VectorStoreService] Error deleting vector stores:', error);
      throw error;
    }
  }

  /**
   * Gán file vào vector store
   * @param vectorStoreId ID của vector store
   * @param data Dữ liệu gán file
   * @returns Thông báo kết quả
   */
  async assignFilesToVectorStore(
    vectorStoreId: string,
    data: AssignFilesToVectorStoreDto
  ): Promise<ApiResponse> {
    try {
      const url = `${this.baseUrl}/vector-stores/${vectorStoreId}/files`;
      const response = await apiClient.post<ApiResponse>(url, data, { tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error(`[VectorStoreService] Error assigning files to vector store ${vectorStoreId}:`, error);
      throw error;
    }
  }
}

// Tạo instance của service để sử dụng trong toàn ứng dụng
export const vectorStoreService = new VectorStoreService();
