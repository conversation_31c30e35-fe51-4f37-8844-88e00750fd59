/**
 * Enum đại diện cho các trạng thái của file tri thức
 */
export enum KnowledgeFileStatus {
  /**
   * File đang chờ duyệt
   */
  PENDING = 'PENDING',

  /**
   * File đã được duyệt
   */
  APPROVED = 'APPROVED',

  /**
   * File bị từ chối
   */
  REJECTED = 'REJECTED',

  /**
   * File đang ở trạng thái nháp
   */
  DRAFT = 'DRAFT',

  /**
   * File đã bị xóa
   */
  DELETED = 'DELETED',
}

/**
 * Enum đại diện cho các loại chủ sở hữu
 */
export enum OwnerType {
  USER = 'user',
  EMPLOYEE = 'employee',
}

/**
 * Interface đại diện cho một file tri thức
 */
export interface KnowledgeFileDto {
  /**
   * ID của file tri thức
   */
  id: string;

  /**
   * Tên hiển thị của file tri thức
   */
  name: string;

  /**
   * Phần mở rộng của file
   */
  extension?: string;

  /**
   * Dung lượng của file tri thức (byte)
   */
  storage: number;

  /**
   * ID của vector store mà file này thuộc về (nếu có)
   */
  vectorStoreId?: string;

  /**
   * Tên của vector store mà file này thuộc về (nếu có)
   */
  vectorStoreName?: string;

  /**
   * URL để tải xuống file
   */
  downloadURL?: string;

  /**
   * Loại chủ sở hữu (ADMIN hoặc USER)
   */
  ownerType?: 'ADMIN' | 'USER';

  /**
   * Thời điểm tạo file (unix timestamp)
   */
  createdAt: number;

  /**
   * Thời điểm cập nhật file (unix timestamp)
   */
  updatedAt?: number;

  /**
   * Trạng thái của file
   */
  status?: KnowledgeFileStatus;
}

/**
 * Interface đại diện cho tham số truy vấn danh sách file tri thức
 */
export interface KnowledgeFileQueryParams {
  /**
   * ID của vector store để lọc file
   */
  vectorStoreId?: string;

  /**
   * Lọc theo định dạng file (ví dụ: "pdf,docx,txt")
   */
  extensions?: string;

  /**
   * Tên file để tìm kiếm
   */
  search?: string | undefined;

  /**
   * Số trang
   */
  page?: number;

  /**
   * Số lượng kết quả trên một trang
   */
  limit?: number;

  /**
   * Trường sắp xếp
   */
  sortBy?: string   | undefined;

  /**
   * Hướng sắp xếp ("asc" hoặc "desc")
   */
  sortDirection?: 'ASC' | 'DESC' | undefined;

  /**
   * Loại chủ sở hữu (ADMIN hoặc USER)
   */
  ownerType?: 'ADMIN' | 'USER';
}

/**
 * Interface đại diện cho dữ liệu tạo file tri thức mới
 */
export interface CreateKnowledgeFileDto {
  /**
   * Tên file
   */
  name: string;

  /**
   * Loại file (MIME type)
   */
  mime: string;

  /**
   * Dung lượng file (bytes)
   */
  storage: number;
}

/**
 * Interface đại diện cho phản hồi danh sách file tri thức
 */
export interface KnowledgeFileListResponse {
  items: KnowledgeFileDto[];
  meta: {
    totalItems: number;
    currentPage: number;
    itemsPerPage: number;
    totalPages: number;
  };
}

/**
 * Interface đại diện cho phản hồi API
 */
export interface ApiResponse<T = unknown> {
  code: number;
  message: string;
  data?: T;
  result?: T;
}

/**
 * Interface đại diện cho phản hồi tạo file tri thức admin
 */
export interface AdminBatchCreateFilesResponse extends ApiResponse<string[]> {
  result: string[]; // Array of presigned URLs
}
