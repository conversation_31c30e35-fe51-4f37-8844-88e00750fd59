import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { MediaService } from '../services/media.service';
import { AdminMediaDto, MediaQueryDto, PaginatedMediaResult } from '../types/media.types';
import { MediaUploadDto } from '@/modules/data/media/types/media.types';

// Key cho React Query
const ADMIN_MEDIA_QUERY_KEY = 'admin-media';

/**
 * Hook để lấy danh sách media cho admin
 * @param params Tham số truy vấn
 * @returns Query object với danh sách media
 */
export const useAdminMediaList = (params?: MediaQueryDto) => {
  return useQuery({
    queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list', params],
    queryFn: () => MediaService.getMediaList(params),
    select: data => data.result as PaginatedMediaResult,
  });
};

/**
 * Hook để lấy thông tin chi tiết media
 * @param id ID của media
 * @returns Query object với thông tin chi tiết media
 */
export const useAdminMediaDetail = (id: string) => {
  return useQuery({
    queryKey: [ADMIN_MEDIA_QUERY_KEY, 'detail', id],
    queryFn: () => MediaService.getMediaById(id),
    select: data => data.result as AdminMediaDto,
    enabled: !!id,
  });
};

/**
 * Hook để xóa media
 * @returns Mutation object để xóa media
 */
export const useDeleteMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => MediaService.deleteMedia(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để xóa nhiều media
 * @returns Mutation object để xóa nhiều media
 */
export const useDeleteMultipleMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => MediaService.deleteMultipleMedia(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để xóa liên kết agent media
 * @returns Mutation object để xóa liên kết agent media
 */
export const useDeleteAgentMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (mediaIds: string[]) => MediaService.deleteAgentMedia(mediaIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để cập nhật thông tin media cho admin
 * @param id ID của media
 * @returns Mutation object
 */
export const useUpdateAdminMedia = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { name?: string; description?: string; tags?: string[] }) =>
      MediaService.updateMedia(id, data),
    onSuccess: () => {
      // Invalidate và refetch thông tin chi tiết media
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'detail', id] });
      // Invalidate và refetch danh sách media
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để toggle trạng thái phê duyệt media
 * @returns Mutation object để toggle approval status
 */
export const useToggleMediaApproval = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => MediaService.toggleApprovalStatus(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách media
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để cập nhật trạng thái phê duyệt của một media
 * @returns Mutation object
 */
export const useUpdateMediaStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status, note }: {
      id: string;
      status: 'APPROVED' | 'REJECTED' | 'PENDING';
      note?: string
    }) => MediaService.updateMediaStatus(id, status, note),
    onSuccess: () => {
      // Invalidate và refetch danh sách media
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để cập nhật trạng thái phê duyệt của nhiều media
 * @returns Mutation object
 */
export const useUpdateMultipleMediaStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ mediaIds, status, note }: {
      mediaIds: string[];
      status: 'APPROVED' | 'REJECTED' | 'PENDING';
      note?: string
    }) => MediaService.updateMultipleMediaStatus(mediaIds, status, note),
    onSuccess: () => {
      // Invalidate và refetch danh sách media
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để tạo presigned URL cho admin
 * @returns Mutation object
 */
export const useAdminCreatePresignedUrl = () => {
  return {
    mutateAsync: async (dto: MediaUploadDto | MediaUploadDto[]) => {
      // Gọi service để tạo presigned URL
      const result = await MediaService.createPresignedUrl(dto);

      // Trả về kết quả với cấu trúc tương thích với code hiện tại
      return {
        result: result.result,
      };
    },
  };
};

/**
 * Hook để tải lên media cho admin
 * @returns Upload function
 */
export const useAdminUploadMedia = () => {
  const queryClient = useQueryClient();

  return {
    uploadMedia: async (url: string, file: File) => {
      await MediaService.uploadMedia(url, file);

      // Invalidate và refetch danh sách media
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });

      return true;
    },
  };
};
