import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  AdminMediaDto,
  DeleteMediaResult,
  MediaQueryDto,
  PaginatedMediaResult,
} from '../types/media.types';
import { MediaUploadDto } from '@/modules/data/media/types/media.types';

// Đường dẫn API chung cho admin media
const ADMIN_MEDIA_API_PATH = '/admin/media';

/**
 * Service cho quản lý media của admin
 */
export const MediaService = {
  /**
   * Lấy danh sách media cho admin
   * @param params Tham số truy vấn
   * @returns Promise với danh sách media có phân trang
   */
  getMediaList: async (params?: MediaQueryDto): Promise<ApiResponseDto<PaginatedMediaResult>> => {
    const response = await apiClient.get<PaginatedMediaResult>(ADMIN_MEDIA_API_PATH, {
      params,
    });
    return response;
  },

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết media theo ID
   * @param id ID của media
   * @returns Promise với thông tin chi tiết media
   */
  getMediaById: async (id: string): Promise<ApiResponseDto<AdminMediaDto>> => {
    const response = await apiClient.get<AdminMediaDto>(`${ADMIN_MEDIA_API_PATH}/${id}`);
    return response;
  },

  /**
   * Xóa media theo ID
   * @param id ID của media
   * @returns Promise với kết quả xóa
   */
  deleteMedia: async (id: string): Promise<ApiResponseDto<null>> => {
    const response = await apiClient.delete<null>(`${ADMIN_MEDIA_API_PATH}/${id}`);
    return response;
  },

  /**
   * Xóa nhiều media theo danh sách ID
   * @param ids Danh sách ID của media cần xóa
   * @returns Promise với kết quả xóa
   */
  deleteMultipleMedia: async (ids: string[]): Promise<ApiResponseDto<DeleteMediaResult>> => {
    const response = await apiClient.delete<DeleteMediaResult>(`${ADMIN_MEDIA_API_PATH}`, {
      data: { mediaIds: ids },
    });
    return response;
  },

  /**
   * Xóa liên kết agent media theo danh sách ID
   * @param mediaIds Danh sách ID của media cần xóa liên kết
   * @returns Promise với kết quả xóa liên kết
   */
  deleteAgentMedia: async (mediaIds: string[]): Promise<ApiResponseDto<DeleteMediaResult>> => {
    const response = await apiClient.delete<DeleteMediaResult>(
      `${ADMIN_MEDIA_API_PATH}/agent-media`,
      {
        data: mediaIds,
      }
    );
    return response;
  },

  /**
   * Cập nhật thông tin media
   * @param id ID của media
   * @param data Dữ liệu cập nhật
   * @returns Promise với thông tin media đã cập nhật
   */
  updateMedia: async (
    id: string,
    data: { name?: string; description?: string; tags?: string[] }
  ): Promise<ApiResponseDto<AdminMediaDto>> => {
    const response = await apiClient.put<AdminMediaDto>(`${ADMIN_MEDIA_API_PATH}/${id}`, data);
    return response;
  },

  /**
   * Toggle trạng thái phê duyệt media (APPROVED <-> DRAFT)
   * @param id ID của media
   * @returns Promise với thông tin media đã cập nhật
   */
  toggleApprovalStatus: async (id: string): Promise<ApiResponseDto<AdminMediaDto>> => {
    const response = await apiClient.post<AdminMediaDto>(`${ADMIN_MEDIA_API_PATH}/${id}/toggle-approval`);
    return response;
  },

  /**
   * Cập nhật trạng thái phê duyệt của một media
   * @param id ID của media
   * @param status Trạng thái mới (APPROVED, REJECTED, PENDING)
   * @param note Ghi chú (optional)
   * @returns Promise với thông tin media đã cập nhật
   */
  updateMediaStatus: async (
    id: string,
    status: 'APPROVED' | 'REJECTED' | 'PENDING',
    note?: string
  ): Promise<ApiResponseDto<AdminMediaDto>> => {
    const response = await apiClient.patch<AdminMediaDto>(
      `${ADMIN_MEDIA_API_PATH}/${id}/status`,
      { status, note },
      { tokenType: 'admin' }
    );
    return response;
  },

  /**
   * Cập nhật trạng thái phê duyệt của nhiều media cùng lúc
   * @param mediaIds Danh sách ID của các media
   * @param status Trạng thái mới (APPROVED, REJECTED, PENDING)
   * @param note Ghi chú (optional)
   * @returns Promise với kết quả cập nhật
   */
  updateMultipleMediaStatus: async (
    mediaIds: string[],
    status: 'APPROVED' | 'REJECTED' | 'PENDING',
    note?: string
  ): Promise<ApiResponseDto<{ updatedCount: number }>> => {
    const response = await apiClient.patch<{ updatedCount: number }>(
      `${ADMIN_MEDIA_API_PATH}/status`,
      { mediaIds, status, note },
      { tokenType: 'admin' }
    );
    return response;
  },

  /**
   * Tạo presigned URL để tải lên media (Admin)
   * @param dto Thông tin media cần tải lên (có thể là array hoặc single object)
   * @returns Thông tin presigned URL
   */
  createPresignedUrl: async (dto: MediaUploadDto | MediaUploadDto[]): Promise<ApiResponseDto<string[]>> => {
    // Đảm bảo dữ liệu được gửi dưới dạng mảng
    const mediaData = Array.isArray(dto) ? dto : [dto];

    const response = await apiClient.post<string[]>(
      `${ADMIN_MEDIA_API_PATH}/presigned-urls`,
      mediaData,
      { tokenType: 'admin' }
    );
    return response;
  },

  /**
   * Tải lên media
   * @param url URL ký sẵn để tải lên
   * @param file File cần tải lên
   * @returns Kết quả tải lên
   */
  uploadMedia: async (url: string, file: File): Promise<void> => {
    // Sử dụng fetch API để tải lên file với phương thức PUT
    await fetch(url, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });
  },
};
