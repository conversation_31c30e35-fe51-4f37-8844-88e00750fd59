import { MediaStatusEnum } from '@/modules/data/media/types/media.types';
import { PaginationMeta } from '@/shared/dto/response/paginated-result.dto';



/**
 * DTO cho thông tin media của admin
 */
export interface AdminMediaDto {
  /**
   * Mã định danh media
   */
  id: string;

  /**
   * Tên media
   */
  name: string;

  /**
   * Mô tả về tài nguyên media
   */
  description: string;

  /**
   * Kích thước media (byte)
   */
  size: number;

  /**
   * Các thẻ phân loại media
   */
  tags?: string[];

  /**
   * Khóa định danh trên hệ thống lưu trữ
   */
  storageKey: string;

  /**
   * Mã người sở hữu media
   */
  ownedBy: number;

  /**
   * Loại người sở hữu media
   */
  ownerType: 'USER' | 'ADMIN';

  /**
   * Tên người đăng tải
   */
  author?: string;

  /**
   * Avatar của người đăng tải
   */
  avatar?: string;

  /**
   * Thời điểm tạo bản ghi (unix timestamp)
   */
  createdAt: number;

  /**
   * Thời điểm cập nhật bản ghi (unix timestamp)
   */
  updatedAt: number;

  /**
   * ID của người đăng tải
   */
  authorId: number;

  /**
   * Trạng thái của media
   */
  status: MediaStatusEnum;

  /**
   * URL xem media
   */
  viewUrl?: string;
}

/**
 * DTO cho tham số truy vấn media
 */
export interface MediaQueryDto {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng item trên một trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string | undefined;

  /**
   * Sắp xếp theo trường
   */
  sortBy?: string | undefined;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC' | undefined;

  /**
   * Lọc theo trạng thái
   */
  status?: MediaStatusEnum;

  /**
   * Lọc theo người sở hữu
   */
  ownedBy?: number;

  /**
   * Lọc theo loại người sở hữu
   */
  ownerType?: 'USER' | 'ADMIN';

  /**
   * Lọc theo tags
   */
  tags?: string[];
}

/**
 * Kết quả phân trang cho media
 */
export interface PaginatedMediaResult {
  /**
   * Danh sách media
   */
  items: AdminMediaDto[];

  /**
   * Thông tin phân trang
   */
  meta: PaginationMeta;
}

/**
 * Kết quả xóa nhiều media
 */
export interface DeleteMediaResult {
  /**
   * Danh sách ID đã xóa thành công
   */
  deletedIds: string[];

  /**
   * Danh sách ID bị bỏ qua (không tìm thấy)
   */
  skippedIds: string[];

  /**
   * Danh sách ID xóa thất bại và lý do
   */
  failedIds: { id: string; reason: string }[];
}
