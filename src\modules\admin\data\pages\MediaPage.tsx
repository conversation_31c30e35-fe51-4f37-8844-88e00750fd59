import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import {
  Card,
  Table,
  ConfirmDeleteModal,
  ActionMenu,
  ActionMenuItem,
  Chip,
} from '@/shared/components/common';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';

// Import hooks từ module media
import {
  useAdminMediaList,
  useDeleteMultipleMedia,
  useUpdateMediaStatus,
  useUpdateMultipleMediaStatus,
  useAdminCreatePresignedUrl,
} from '@/modules/admin/data/media/hooks/useMedia';

// Import types từ module media
import { AdminMediaDto, MediaQueryDto } from '@/modules/admin/data/media/types/media.types';
import { MediaStatusEnum } from '@/modules/data/media/types/media.types';
import { formatDate } from '@/shared/utils/format';
import { MediaEditForm } from '../components';
import { FileForm } from '@/modules/data/components/forms';
import { CreateMediaFormValues } from '@/modules/data/components/forms/FileForm';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { MediaUploadDto } from '@/modules/data/media/types/media.types';

// Interface cho media list với file
interface MediaListItem extends MediaUploadDto {
  file: File;
  viewUrl: string;
}

/**
 * Trang quản lý media trong admin
 */
const MediaPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho xác nhận xóa
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<AdminMediaDto | null>(null);

  // State cho chọn nhiều và xóa nhiều
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho hiển thị media detail
  const [mediaToView, setMediaToView] = useState<AdminMediaDto | null>(null);

  // State cho upload media
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // State cho filter ownerType
  const [ownerTypeFilter, setOwnerTypeFilter] = useState<'all' | 'USER' | 'ADMIN'>('all');

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    hideForm: hideViewForm,
  } = useSlideForm();



  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setMediaToView(null);
  }, [hideViewForm]);

  // Sử dụng hook để xóa media và approval
  const { mutateAsync: deleteMultipleMedia } = useDeleteMultipleMedia();
  const { mutateAsync: updateMediaStatus } = useUpdateMediaStatus();
  const { mutateAsync: updateMultipleMediaStatus } = useUpdateMultipleMediaStatus();

  // Sử dụng admin hooks để upload media
  const { mutateAsync: createPresignedUrl } = useAdminCreatePresignedUrl();
  const queryClient = useQueryClient();

  // Hook để upload file với TaskQueue và xử lý lỗi CORS
  const fileUploadWithQueue = useCorsAwareFileUpload({
    defaultTaskTitle: 'Upload media (Admin)',
    autoAddToQueue: true,
  });

  // Tạo custom upload function cho admin với TaskQueue
  const uploadMedia = useCallback(
    async (mediaList: MediaListItem[]) => {
      try {
        if (!mediaList || !Array.isArray(mediaList) || mediaList.length === 0) {
          throw new Error('Danh sách media không hợp lệ');
        }

        // Chuẩn bị dữ liệu theo format API admin
        const adminMediaData: MediaUploadDto[] = mediaList.map(mediaItem => {
          const data: MediaUploadDto = {
            name: mediaItem.name,
            size: mediaItem.size,
            type: mediaItem.type,
          };

          if (mediaItem.description) {
            data.description = mediaItem.description;
          }

          if (mediaItem.tags) {
            data.tags = mediaItem.tags;
          }

          return data;
        });

        // Tạo presigned URLs cho tất cả media cùng lúc
        const presignedResponse = await createPresignedUrl(adminMediaData);

        // Backend trả về array of strings (URLs)
        const presignedUrls = presignedResponse?.result || [];

        if (!presignedUrls || !Array.isArray(presignedUrls) || presignedUrls.length === 0) {
          throw new Error('Không nhận được URL tải lên từ server');
        }

        // Upload từng file lên URL tạm thời với TaskQueue
        const uploadPromises = presignedUrls.map((uploadUrl, index) => {
          const mediaData = mediaList[index];
          if (!mediaData) {
            throw new Error(`Không tìm thấy dữ liệu media cho index ${index}`);
          }

          const { file } = mediaData;

          if (!file || !(file instanceof File)) {
            throw new Error(`Không tìm thấy file hợp lệ cho media ${index + 1}`);
          }

          return fileUploadWithQueue.uploadToUrlWithQueue({
            file,
            presignedUrl: uploadUrl,
            taskTitle: `Upload: ${mediaData.name}`,
            taskDescription: `Kích thước: ${(mediaData.size / 1024).toFixed(1)} KB`,
          });
        });

        // Chờ tất cả các file upload xong
        await Promise.all(uploadPromises);

        // Cập nhật lại danh sách media sau khi upload xong
        queryClient.invalidateQueries({ queryKey: ['admin-media', 'list'] });

        // Hiển thị thông báo thành công
        NotificationUtil.success({
          message: 'Tải lên media thành công',
          duration: 3000,
        });

        return presignedUrls;
      } catch (error) {
        console.error('Lỗi khi upload media:', error);
        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi tải lên media. Vui lòng thử lại sau.',
          duration: 5000,
        });
        throw error;
      }
    },
    [createPresignedUrl, fileUploadWithQueue, queryClient]
  );



  // Xử lý phê duyệt media
  const handleApproveMedia = useCallback(
    async (media: AdminMediaDto) => {
      try {
        await updateMediaStatus({
          id: media.id,
          status: 'APPROVED',
          note: 'Media đã được kiểm tra và phê duyệt',
        });

        NotificationUtil.success({
          message: t('admin:data.media.approveSuccess', 'Đã phê duyệt media thành công'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error approving media:', error);
        NotificationUtil.error({
          message: t('admin:data.media.approveError', 'Có lỗi xảy ra khi phê duyệt media'),
          duration: 3000,
        });
      }
    },
    [updateMediaStatus, t]
  );

  // Xử lý từ chối media
  const handleRejectMedia = useCallback(
    async (media: AdminMediaDto) => {
      try {
        await updateMediaStatus({
          id: media.id,
          status: 'REJECTED',
          note: 'Media không phù hợp với tiêu chuẩn',
        });

        NotificationUtil.success({
          message: t('admin:data.media.rejectSuccess', 'Đã từ chối media thành công'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error rejecting media:', error);
        NotificationUtil.error({
          message: t('admin:data.media.rejectError', 'Có lỗi xảy ra khi từ chối media'),
          duration: 3000,
        });
      }
    },
    [updateMediaStatus, t]
  );



  // Xử lý submit form tạo media mới
  const handleSubmitCreateMedia = useCallback(
    async (values: CreateMediaFormValues) => {
      try {
        setIsUploading(true);
        setUploadProgress(10);

        console.log('Form values:', values);

        // Chuẩn bị dữ liệu theo định dạng yêu cầu của API
        const mediaList: MediaListItem[] = values.files.map(fileData => {
          const item: MediaListItem = {
            name: fileData.name,
            size: fileData.file.size,
            type: fileData.file.type,
            viewUrl: 'test/test-' + Date.now() + '-' + Math.random().toString(36).substring(2, 8),
            file: fileData.file,
          };

          if (fileData.description) {
            item.description = fileData.description;
          }

          if (fileData.tags) {
            item.tags = fileData.tags.split(',').map((tag: string) => tag.trim());
          }

          return item;
        });

        // Upload media với TaskQueue
        await uploadMedia(mediaList);

        // Đóng form sau 1.5s
        setTimeout(() => {
          hideCreateForm();
          setIsUploading(false);
        }, 1500);
      } catch (err) {
        console.error('Upload error:', err);
        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi tải lên media. Vui lòng thử lại sau.',
          duration: 5000,
        });
        setIsUploading(false);
      }
    },
    [uploadMedia, hideCreateForm, setIsUploading, setUploadProgress]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo(
    () => [
      {
        key: 'name',
        title: t('admin:data.media.table.name', 'Tên'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown) => (
          <div className="flex items-center">
            <span className="truncate break-all max-w-[180px]" title={String(value || '')}>
              {String(value || '')}
            </span>
          </div>
        ),
      },
      {
        key: 'description',
        title: t('admin:data.media.table.description', 'Mô tả'),
        dataIndex: 'description',
        sortable: true,
      },
      {
        key: 'size',
        title: t('admin:data.media.table.size', 'Kích thước'),
        dataIndex: 'size',
        sortable: true,
        render: (value: unknown) => {
          const size = value as number;
          if (size < 1024) {
            return `${size} B`;
          } else if (size < 1024 * 1024) {
            return `${(size / 1024).toFixed(2)} KB`;
          } else if (size < 1024 * 1024 * 1024) {
            return `${(size / (1024 * 1024)).toFixed(2)} MB`;
          } else {
            return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
          }
        },
      },
      {
        key: 'author',
        title: t('admin:data.media.table.author', 'Người tạo'),
        dataIndex: 'author',
        render: (value: unknown, record: AdminMediaDto) => (
          <div className="flex flex-col">
            <span>{value as string}</span>
            <span className="text-xs text-gray-500">ID: {record.authorId}</span>
          </div>
        ),
      },
      {
        key: 'ownerType',
        title: t('admin:data.media.table.ownerType', 'Loại sở hữu'),
        dataIndex: 'ownerType',
        sortable: true,
        render: (value: unknown) => {
          const ownerType = value as 'USER' | 'ADMIN';
          return (
            <Chip
              size="sm"
              variant={ownerType === 'ADMIN' ? 'primary' : 'secondary'}
              className="whitespace-nowrap min-w-0 flex-shrink-0"
            >
              <span className="truncate">
                {ownerType === 'ADMIN'
                  ? t('admin:data.media.ownerType.admin', 'Admin')
                  : t('admin:data.media.ownerType.user', 'User')}
              </span>
            </Chip>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('admin:data.media.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
      {
        key: 'status',
        title: t('admin:data.media.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (_: unknown, record: AdminMediaDto) => (
          <div className="flex items-center">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                record.status === MediaStatusEnum.APPROVED
                  ? 'bg-green-100 text-green-800'
                  : record.status === MediaStatusEnum.DRAFT
                    ? 'bg-gray-100 text-gray-800'
                    : 'bg-yellow-100 text-yellow-800'
              }`}
            >
              {record.status === MediaStatusEnum.APPROVED
                ? t('common.status.active', 'Hoạt động')
                : record.status === MediaStatusEnum.DRAFT
                  ? t('common.status.draft', 'Nháp')
                  : t('common.status.pending', 'Chờ duyệt')}
            </span>
          </div>
        ),
      },
      {
        key: 'actions',
        title: t('', ''),
        width: '120px',
        render: (_: unknown, record: AdminMediaDto) => {
          // Tạo danh sách các action items dựa trên trạng thái hiện tại
          const actionItems: ActionMenuItem[] = [];

          // Thêm action phê duyệt nếu chưa được phê duyệt
          if (record.status !== MediaStatusEnum.APPROVED) {
            actionItems.push({
              id: 'approve',
              label: t('admin:data.media.actions.approve', 'Phê duyệt'),
              icon: 'check',
              onClick: () => handleApproveMedia(record),
            });
          } else {
            actionItems.push({
              id: 'reject',
              label: t('admin:data.media.actions.reject', 'Từ chối'),
              icon: 'x',
              onClick: () => handleRejectMedia(record),
            });
          }

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="200px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ],
    [
      t,
      handleApproveMedia,
      handleRejectMedia,
    ]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'approved',
        label: t('common.status.active', 'Đã phê duyệt'),
        icon: 'check',
        value: MediaStatusEnum.APPROVED,
      },
      {
        id: 'pending',
        label: t('common.status.pending', 'Chờ duyệt'),
        icon: 'clock',
        value: 'PENDING',
      },
      {
        id: 'rejected',
        label: t('admin:data.media.status.rejected', 'Đã từ chối'),
        icon: 'x',
        value: 'REJECTED',
      },
      {
        id: 'draft',
        label: t('common.status.draft', 'Nháp'),
        icon: 'edit',
        value: MediaStatusEnum.DRAFT,
      },
    ],
    [t]
  );

  // Xử lý thay đổi filter ownerType
  const handleOwnerTypeFilterChange = useCallback((filterId: string) => {
    setOwnerTypeFilter(filterId as 'all' | 'USER' | 'ADMIN');
  }, []);

  // Tạo filter options cho ownerType
  const ownerTypeFilterOptions = useMemo(() => [
    {
      id: 'all',
      label: t('admin:data.media.filter.all', 'Tất cả'),
      icon: 'list',
      onClick: () => handleOwnerTypeFilterChange('all'),
    },
    {
      id: 'user',
      label: t('admin:data.media.filter.user', 'User'),
      icon: 'user',
      onClick: () => handleOwnerTypeFilterChange('USER'),
    },
    {
      id: 'admin',
      label: t('admin:data.media.filter.admin', 'Admin'),
      icon: 'settings',
      onClick: () => handleOwnerTypeFilterChange('ADMIN'),
    },
  ], [t, handleOwnerTypeFilterChange]);

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): MediaQueryDto => {
      const queryParams: MediaQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as MediaStatusEnum;
      }

      // Thêm filter ownerType nếu không phải 'all'
      if (ownerTypeFilter !== 'all') {
        queryParams.ownerType = ownerTypeFilter;
      }

      return queryParams;
    },
    [ownerTypeFilter]
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AdminMediaDto, MediaQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Hooks để gọi API
  const { data: mediaData, isLoading: isLoadingMedia } = useAdminMediaList(dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng khi có dữ liệu từ API
  useEffect(() => {
    if (mediaData) {
      // Sử dụng tham chiếu để tránh vòng lặp vô hạn
      updateTableDataRef.current(mediaData, isLoadingMedia);
    }
  }, [mediaData, isLoadingMedia]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        [MediaStatusEnum.APPROVED]: t('common.status.active', 'Hoạt động'),
        [MediaStatusEnum.DRAFT]: t('common.status.draft', 'Nháp'),
      },
      t,
    });

  // Xử lý clear ownerType filter
  const handleClearOwnerTypeFilter = useCallback(() => {
    setOwnerTypeFilter('all');
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!fileToDelete) return;

    try {
      // Gọi API xóa file
      await deleteMultipleMedia([fileToDelete.id]);

      // Đóng popup
      setShowDeleteConfirm(false);
      setFileToDelete(null);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('admin:data.media.deleteSuccess', 'Đã xóa media thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting media:', error);
      NotificationUtil.error({
        message: t('admin:data.media.deleteError', 'Có lỗi xảy ra khi xóa media'),
        duration: 3000,
      });
    }
  }, [fileToDelete, deleteMultipleMedia, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    // Kiểm tra cả số lượng mục đã chọn và dữ liệu có tồn tại không
    if (selectedRowKeys.length === 0 || (mediaData?.items?.length ?? 0) === 0) {
      NotificationUtil.warning({
        message: t(
          'admin:data.media.selectFilesToDelete',
          'Vui lòng chọn ít nhất một media để xóa'
        ),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, mediaData?.items?.length, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Lưu số lượng media đã chọn trước khi xóa để hiển thị thông báo
      const deletedCount = selectedRowKeys.length;

      // Gọi API xóa nhiều media cùng lúc
      await deleteMultipleMedia(selectedRowKeys as string[]);

      // Đảm bảo đặt lại selectedRowKeys thành mảng rỗng trước khi đóng modal
      setSelectedRowKeys([]);
      setShowBulkDeleteConfirm(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('admin:data.media.bulkDeleteSuccess', `Đã xóa ${deletedCount} media thành công`),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting media:', error);
      NotificationUtil.error({
        message: t('admin:data.media.bulkDeleteError', 'Có lỗi xảy ra khi xóa media'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleMedia, t]);

  return (
    <div>
      {/* Thêm MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showCreateForm()}
        items={ownerTypeFilterOptions}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'check',
            tooltip: t('admin:data.media.bulkApprove', 'Phê duyệt nhiều'),
            variant: 'primary',
            onClick: async () => {
              if (selectedRowKeys.length > 0) {
                try {
                  await updateMultipleMediaStatus({
                    mediaIds: selectedRowKeys as string[],
                    status: 'APPROVED',
                    note: 'Batch approval cho media đã kiểm tra',
                  });
                  setSelectedRowKeys([]);
                  NotificationUtil.success({
                    message: t(
                      'admin:data.media.bulkApproveSuccess',
                      `Đã phê duyệt ${selectedRowKeys.length} media thành công`
                    ),
                    duration: 3000,
                  });
                } catch {
                  NotificationUtil.error({
                    message: t(
                      'admin:data.media.bulkApproveError',
                      'Có lỗi xảy ra khi phê duyệt media'
                    ),
                    duration: 3000,
                  });
                }
              }
            },
            className: 'text-green-500',
            condition: selectedRowKeys.length > 0,
          },
          {
            icon: 'x',
            tooltip: t('admin:data.media.bulkReject', 'Từ chối nhiều'),
            variant: 'primary',
            onClick: async () => {
              if (selectedRowKeys.length > 0) {
                try {
                  await updateMultipleMediaStatus({
                    mediaIds: selectedRowKeys as string[],
                    status: 'REJECTED',
                    note: 'Batch rejection cho media không phù hợp',
                  });
                  setSelectedRowKeys([]);
                  NotificationUtil.success({
                    message: t(
                      'admin:data.media.bulkRejectSuccess',
                      `Đã từ chối ${selectedRowKeys.length} media thành công`
                    ),
                    duration: 3000,
                  });
                } catch {
                  NotificationUtil.error({
                    message: t(
                      'admin:data.media.bulkRejectError',
                      'Có lỗi xảy ra khi từ chối media'
                    ),
                    duration: 3000,
                  });
                }
              }
            },
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
          {
            icon: 'trash',
            tooltip: t('common.bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: () => {
              // Kiểm tra lại số lượng mục đã chọn và dữ liệu có tồn tại không
              if (selectedRowKeys.length > 0 && (mediaData?.items?.length ?? 0) > 0) {
                handleShowBulkDeleteConfirm();
              } else {
                NotificationUtil.info({
                  message: t(
                    'admin:data.media.selectFilesToDelete',
                    'Vui lòng chọn ít nhất một media để xóa'
                  ),
                  duration: 3000,
                });
              }
            },
            className: 'text-red-500',
            // Chỉ hiển thị khi có dữ liệu và có mục được chọn
            condition: selectedRowKeys.length > 0 && (mediaData?.items?.length ?? 0) > 0,
          },
        ]}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        customTags={ownerTypeFilter !== 'all' ? [{
          id: 'ownerType',
          label: t('admin:data.media.filter.ownerType', 'Owner Type'),
          value: ownerTypeFilter === 'ADMIN' ?
            t('admin:data.media.filter.admin', 'Admin') :
            t('admin:data.media.filter.user', 'User'),
          onRemove: handleClearOwnerTypeFilter,
        }] : []}
        onClearAll={() => {
          handleClearAll();
          setOwnerTypeFilter('all');
        }}
      />

      {/* SlideInForm cho form thêm mới */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <FileForm
          onSubmit={handleSubmitCreateMedia}
          onCancel={hideCreateForm}
          isUploading={isUploading}
          uploadProgress={uploadProgress}
        />
      </SlideInForm>

      {/* SlideInForm cho form chỉnh sửa media */}
      <SlideInForm isVisible={isViewFormVisible}>
        {mediaToView && (
          <MediaEditForm
            media={mediaToView}
            onSuccess={handleCloseViewForm}
            onCancel={handleCloseViewForm}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table<AdminMediaDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={mediaData?.items || []}
          rowKey="id"
          loading={isLoadingMedia}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: mediaData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: mediaData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={t('admin:data.media.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa media này?')}
        itemName={fileToDelete?.name || ''}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={
          selectedRowKeys.length > 0
            ? t(
                'admin:data.media.confirmBulkDeleteMessage',
                `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} media đã chọn?`
              )
            : t('admin:data.media.noFilesSelected', 'Không có media nào được chọn')
        }
      />
    </div>
  );
};

export default MediaPage;
