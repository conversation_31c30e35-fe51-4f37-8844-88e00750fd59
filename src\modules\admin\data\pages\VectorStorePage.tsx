import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Table,
  Modal,
  ConfirmDeleteModal,
  ActionMenu,
  ActionMenuItem,
  Chip,
} from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import AssignFilesToVectorStoreForm from '@/modules/admin/data/components/AssignFilesToVectorStoreForm';
import VectorStoreDetailView from '@/modules/admin/data/components/VectorStoreDetailView';
import VectorStoreForm from '@/modules/admin/data/components/VectorStoreForm';

import {
  useVectorStores,
  useCreateVectorStore,
  useDeleteVectorStore,
  useAssignFilesToVectorStore,
} from '@/modules/admin/data/knowledge-files/hooks';
import {
  VectorStoreDto,
  VectorStoreQueryParams,
  CreateVectorStoreDto,
  AssignFilesToVectorStoreDto,
} from '@/modules/admin/data/knowledge-files/types';
import { formatDate } from '@/shared/utils/format';

// Hàm formatBytes với xử lý null/NaN
const formatBytes = (bytes: unknown, decimals = 2): string => {
  // Xử lý trường hợp null, undefined, NaN
  if (bytes === null || bytes === undefined || Number.isNaN(Number(bytes))) {
    return 'N/A';
  }

  const numBytes = Number(bytes);

  // Xử lý trường hợp số âm hoặc không hợp lệ
  if (numBytes < 0 || !Number.isFinite(numBytes)) {
    return 'N/A';
  }

  if (numBytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(numBytes) / Math.log(k));

  return parseFloat((numBytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Trang quản lý vector store
 */
const VectorStorePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [vectorStores, setVectorStores] = useState<VectorStoreDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [totalItems, setTotalItems] = useState(0);
  const [vectorStoreToDelete, setVectorStoreToDelete] = useState<VectorStoreDto | null>(null);

  const [vectorStoreToAssign, setVectorStoreToAssign] = useState<VectorStoreDto | null>(null);
  const [vectorStoreToView, setVectorStoreToView] = useState<VectorStoreDto | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedVectorStoreIds, setSelectedVectorStoreIds] = useState<string[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // State cho filter ownerType
  const [ownerTypeFilter, setOwnerTypeFilter] = useState<'all' | 'USER' | 'ADMIN'>('all');

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Sử dụng hook animation cho form gán file
  const {
    isVisible: isAssignFormVisible,
    showForm: showAssignForm,
    hideForm: hideAssignForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Khai báo các hàm xử lý sự kiện trước khi sử dụng trong columns

  const handleShowAssignForm = useCallback(
    (vectorStore: VectorStoreDto) => {
      setVectorStoreToAssign(vectorStore);
      showAssignForm();
    },
    [showAssignForm]
  );

  const handleShowDetailForm = useCallback(
    (vectorStore: VectorStoreDto) => {
      setVectorStoreToView(vectorStore);
      showDetailForm();
    },
    [showDetailForm]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'storeName',
        title: t('admin:data.vectorStore.table.name', 'Vector Store Name'),
        dataIndex: 'storeName',
        width: '25%',
        sortable: true,
      },
      {
        key: 'size',
        title: t('admin:data.vectorStore.table.size', 'Size'),
        dataIndex: 'size',
        width: '15%',
        sortable: true,
        render: (value: unknown) => formatBytes(value),
      },
      {
        key: 'files',
        title: t('admin:data.vectorStore.table.files', 'Files'),
        dataIndex: 'files',
        width: '10%',
        sortable: true,
      },
      {
        key: 'agents',
        title: t('admin:data.vectorStore.table.agents', 'Agents'),
        dataIndex: 'agents',
        width: '10%',
        sortable: true,
      },
      {
        key: 'ownerType',
        title: t('admin:data.vectorStore.table.ownerType', 'Owner Type'),
        dataIndex: 'ownerType',
        width: '12%',
        sortable: true,
        render: (value: unknown) => {
          const ownerType = value as 'USER' | 'ADMIN' | undefined;
          if (!ownerType) return '-';
          return (
            <Chip
              size="sm"
              variant={ownerType === 'ADMIN' ? 'primary' : 'secondary'}
              className="whitespace-nowrap min-w-0 flex-shrink-0"
            >
              <span className="truncate">
                {ownerType === 'ADMIN'
                  ? t('admin:data.vectorStore.ownerType.admin', 'Admin')
                  : t('admin:data.vectorStore.ownerType.user', 'User')}
              </span>
            </Chip>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('admin:data.vectorStore.table.createdAt', 'Created at'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
      {
        key: 'actions',
        title: t('', ''),
        width: '120px',
        render: (_: unknown, record: VectorStoreDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [];

          // Chỉ hiển thị "Assign Files" nếu ownerType không phải là "USER"
          if (record.ownerType !== 'USER') {
            actionItems.push({
              id: 'assign',
              label: t('admin:data.vectorStore.assignFiles', 'Assign Files'),
              icon: 'file',
              onClick: () => handleShowAssignForm(record),
            });
            actionItems.push({
              id: 'view',
              label: t('admin:data.vectorStore.viewDetails', 'View Details'),
              icon: 'eye',
              onClick: () => handleShowDetailForm(record),
            });
          }

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ];

    return allColumns;
  }, [t, handleShowAssignForm, handleShowDetailForm]);

  // Xử lý thay đổi filter ownerType
  const handleOwnerTypeFilterChange = useCallback((filterId: string) => {
    setOwnerTypeFilter(filterId as 'all' | 'USER' | 'ADMIN');
  }, []);

  // Tạo filter options cho ownerType
  const ownerTypeFilterOptions = useMemo(() => [
    {
      id: 'all',
      label: t('admin:data.vectorStore.filter.all', 'Tất cả'),
      icon: 'list',
      onClick: () => handleOwnerTypeFilterChange('all'),
    },
    {
      id: 'user',
      label: t('admin:data.vectorStore.filter.user', 'User'),
      icon: 'user',
      onClick: () => handleOwnerTypeFilterChange('USER'),
    },
    {
      id: 'admin',
      label: t('admin:data.vectorStore.filter.admin', 'Admin'),
      icon: 'settings',
      onClick: () => handleOwnerTypeFilterChange('ADMIN'),
    },
  ], [t, handleOwnerTypeFilterChange]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<VectorStoreDto, VectorStoreQueryParams>({
    columns,
    showDateFilter: false,
    createQueryParams: params => {
      const queryParams: VectorStoreQueryParams = {
        page: params.page,
        limit: params.pageSize,
      };

      if (params.searchTerm) {
        queryParams.search = params.searchTerm;
      }

      if (params.sortBy) {
        queryParams.sortBy = params.sortBy;
      }

      if (params.sortDirection) {
        queryParams.sortDirection = params.sortDirection as 'ASC' | 'DESC';
      }

      // Thêm filter ownerType nếu không phải 'all'
      if (ownerTypeFilter !== 'all') {
        queryParams.ownerType = ownerTypeFilter;
      }

      return queryParams;
    },
  });

  const dataTable = useDataTable(dataTableConfig);

  // Tạo query params cho API
  const queryParams = useMemo<VectorStoreQueryParams>(() => {
    const params: VectorStoreQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
    };

    if (dataTable.tableData.searchTerm) {
      params.search = dataTable.tableData.searchTerm;
    }

    if (dataTable.tableData.sortBy) {
      params.sortBy = dataTable.tableData.sortBy;
    }

    if (dataTable.tableData.sortDirection) {
      params.sortDirection = dataTable.tableData.sortDirection as 'ASC' | 'DESC';
    }

    // Thêm filter ownerType nếu không phải 'all'
    if (ownerTypeFilter !== 'all') {
      params.ownerType = ownerTypeFilter;
    }

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
    dataTable.tableData.sortBy,
    dataTable.tableData.sortDirection,
    ownerTypeFilter,
  ]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearSort, handleClearAll } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Xử lý clear ownerType filter
  const handleClearOwnerTypeFilter = useCallback(() => {
    setOwnerTypeFilter('all');
  }, []);

  // Hooks để gọi API
  const {
    data: vectorStoresData,
    isLoading: isLoadingVectorStores,
    error: vectorStoresError,
  } = useVectorStores(queryParams);

  const { mutateAsync: createVectorStore } = useCreateVectorStore();
  const { deleteStores } = useDeleteVectorStore();
  const { mutateAsync: assignFilesToVectorStore } = useAssignFilesToVectorStore();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (vectorStoresData) {
      setVectorStores(vectorStoresData.items);
      setTotalItems(vectorStoresData.meta.totalItems);
    }

    setIsLoading(isLoadingVectorStores);
  }, [vectorStoresData, vectorStoresError, isLoadingVectorStores]);

  // Xử lý thay đổi trang - sử dụng dataTable
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm - sử dụng dataTable
  const handleSearch = useCallback(
    (term: string) => {
      dataTable.tableData.handleSearch(term);
    },
    [dataTable.tableData]
  );

  // Xử lý thay đổi sắp xếp - sử dụng dataTable
  const handleSortChange = useCallback(
    (column: string | null, order: SortOrder) => {
      dataTable.tableData.handleSortChange(column, order);
    },
    [dataTable.tableData]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setVectorStoreToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!vectorStoreToDelete) return;

    try {
      await deleteStores(vectorStoreToDelete.storeId.toString());
      setShowDeleteConfirm(false);
      setVectorStoreToDelete(null);

      NotificationUtil.success({
        message: t(
          'admin:data.vectorStore.messages.deleteSuccess',
          'Vector Store deleted successfully'
        ),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting vector store:', error);
      NotificationUtil.error({
        message: t('admin:data.vectorStore.messages.deleteError', 'Error deleting Vector Store'),
        duration: 3000,
      });
    }
  }, [vectorStoreToDelete, deleteStores, t]);

  // Xử lý hiển thị xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedVectorStoreIds.length > 0) {
      setShowBulkDeleteConfirm(true);
    } else {
      NotificationUtil.info({
        message: t(
          'admin:data.vectorStore.selectFilesToRemove',
          'Please select at least one Vector Store to delete'
        ),
        duration: 3000,
      });
    }
  }, [selectedVectorStoreIds.length, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedVectorStoreIds.length === 0) return;

    try {
      // Xóa tất cả Vector Store đã chọn trong một API call
      await deleteStores(selectedVectorStoreIds);

      setShowBulkDeleteConfirm(false);
      setSelectedVectorStoreIds([]);

      NotificationUtil.success({
        message: t(
          'admin:data.vectorStore.removeFilesSuccess',
          'Selected Vector Stores deleted successfully'
        ),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting multiple Vector Stores:', error);
      NotificationUtil.error({
        message: t(
          'admin:data.vectorStore.removeFilesError',
          'Error deleting selected Vector Stores'
        ),
        duration: 3000,
      });
    }
  }, [selectedVectorStoreIds, deleteStores, t]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý submit form tạo vector store
  const handleSubmitCreateVectorStore = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsCreating(true);

        // Chuẩn bị dữ liệu cho API
        const vectorStoreData: CreateVectorStoreDto = {
          name: values['name'] as string,
        };

        // Gọi API tạo vector store
        await createVectorStore(vectorStoreData);
        hideForm();
        setIsCreating(false);
      } catch (error) {
        console.error('Error creating vector store:', error);
        setIsCreating(false);
      }
    },
    [createVectorStore, hideForm]
  );

  // Xử lý submit form gán file
  const handleSubmitAssignFiles = useCallback(
    async (values: { fileIds: string[] }) => {
      if (!vectorStoreToAssign) return;

      try {
        setIsAssigning(true);

        // Chuẩn bị dữ liệu cho API
        const assignData: { vectorStoreId: string; data: AssignFilesToVectorStoreDto } = {
          vectorStoreId: vectorStoreToAssign.storeId.toString(),
          data: {
            fileIds: values['fileIds'],
          },
        };

        // Gọi API gán file
        await assignFilesToVectorStore(assignData);

        // Đóng form và hiển thị thông báo thành công
        hideAssignForm();
        setVectorStoreToAssign(null);

        NotificationUtil.success({
          message: t(
            'admin:data.vectorStore.messages.assignSuccess',
            'Files assigned successfully'
          ),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error assigning files:', error);
        NotificationUtil.error({
          message: t('admin:data.vectorStore.messages.assignError', 'Error assigning files'),
          duration: 3000,
        });
      } finally {
        setIsAssigning(false);
      }
    },
    [vectorStoreToAssign, assignFilesToVectorStore, hideAssignForm, t]
  );

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: t('common:all', 'All'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div>
      {/* Thêm MenuIconBar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={() => showForm()}
        items={ownerTypeFilterOptions}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Delete multiple'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            condition: selectedVectorStoreIds.length > 0,
          },
        ]}
        onColumnVisibilityChange={handleColumnVisibilityChange}
        columns={visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection as SortDirection}
        onClearSort={handleClearSort}
        {...(ownerTypeFilter !== 'all' && {
          filterValue: ownerTypeFilter,
          filterLabel: ownerTypeFilter === 'ADMIN' ?
            t('admin:data.vectorStore.filter.admin', 'Admin') :
            t('admin:data.vectorStore.filter.user', 'User'),
          onClearFilter: handleClearOwnerTypeFilter,
        })}
        onClearAll={() => {
          handleClearAll();
          setOwnerTypeFilter('all');
        }}
      />

      {/* SlideInForm cho form thêm mới */}
      <SlideInForm isVisible={isVisible}>
        <VectorStoreForm
          onSubmit={handleSubmitCreateVectorStore}
          onCancel={hideForm}
          isLoading={isCreating}
        />
      </SlideInForm>

      {/* SlideInForm cho form gán file */}
      <SlideInForm isVisible={isAssignFormVisible}>
        {vectorStoreToAssign && (
          <AssignFilesToVectorStoreForm
            onSubmit={(values: Record<string, unknown>) =>
              handleSubmitAssignFiles({ fileIds: values['fileIds'] as string[] })
            }
            onCancel={hideAssignForm}
            isLoading={isAssigning}
            vectorStoreId={vectorStoreToAssign.storeId.toString()}
          />
        )}
      </SlideInForm>

      {/* SlideInForm cho xem chi tiết vector store */}
      <SlideInForm isVisible={isDetailFormVisible}>
        {vectorStoreToView && (
          <VectorStoreDetailView
            vectorStoreId={vectorStoreToView.storeId.toString()}
            onClose={hideDetailForm}
            ownerType={vectorStoreToView.ownerType || undefined}
            onAssignFiles={vectorStoreId => {
              hideDetailForm();
              const vectorStore = vectorStores.find(vs => vs.storeId.toString() === vectorStoreId);
              if (vectorStore) {
                handleShowAssignForm(vectorStore);
              }
            }}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table<VectorStoreDto>
          columns={filteredColumns}
          data={vectorStores}
          rowKey="storeId"
          loading={isLoading}
          sortable={true}
          onSortChange={handleSortChange}
          rowSelection={{
            selectedRowKeys: selectedVectorStoreIds,
            onChange: (keys: React.Key[]) => setSelectedVectorStoreIds(keys as string[]),
          }}
          defaultSort={{
            column: dataTable.tableData.sortBy || '',
            order: dataTable.tableData.sortDirection === SortDirection.ASC ? 'asc' : 'desc',
          }}
          pagination={{
            current: dataTable.tableData.currentPage,
            pageSize: dataTable.tableData.pageSize,
            total: totalItems,
            onChange: handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('admin:data.common.confirmDelete', 'Confirm Delete')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('common:cancel', 'Cancel')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete}>
              {t('common:delete', 'Delete')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t(
              'admin:data.vectorStore.confirmDeleteMessage',
              'Are you sure you want to delete this vector store?'
            )}
          </Typography>
          {vectorStoreToDelete && (
            <Typography variant="body2" className="mt-2 font-semibold">
              {vectorStoreToDelete.storeName}
            </Typography>
          )}
        </div>
      </Modal>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('admin:data.vectorStore.removeFilesTitle', 'Delete Selected Vector Stores')}
        message={t(
          'admin:data.vectorStore.removeFilesMessage',
          'Are you sure you want to delete {{count}} selected Vector Stores?',
          { count: selectedVectorStoreIds.length }
        )}
      />
    </div>
  );
};

export default VectorStorePage;
