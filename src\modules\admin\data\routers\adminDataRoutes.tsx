import { Loading } from '@/shared/components';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const AdminDataManagementPage = lazy(() => import('../pages/AdminDataManagementPage'));
const AdminMediaPage = lazy(() => import('@/modules/admin/data/pages/MediaPage'));
const URLPage = lazy(() => import('@/modules/admin/data/pages/URLPage'));
const KnowledgeFilesPage = lazy(() => import('@/modules/admin/data/pages/KnowledgeFilesPage'));
const VectorStorePage = lazy(() => import('@/modules/admin/data/pages/VectorStorePage'));

const adminDataRoutes: RouteObject[] = [
  {
    path: '/admin/data',
    element: (
      <AdminLayout title="Admin Data Management">
        <Suspense fallback={<Loading />}>
          <AdminDataManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/data/media',
    element: (
      <AdminLayout title="Admin Media Management">
        <Suspense fallback={<Loading />}>
          <AdminMediaPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/data/url',
    element: (
      <AdminLayout title="Admin URL Management">
        <Suspense fallback={<Loading />}>
          <URLPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/data/knowledge-files',
    element: (
      <AdminLayout title="Admin Knowledge Files Management">
        <Suspense fallback={<Loading />}>
          <KnowledgeFilesPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/data/vector-store',
    element: (
      <AdminLayout title="Admin Vector Store Management">
        <Suspense fallback={<Loading />}>
          <VectorStorePage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default adminDataRoutes;
