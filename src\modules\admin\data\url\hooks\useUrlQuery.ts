import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { UrlService } from '../services/url.service';
import { 
  UrlSearchParams, 
  CreateUrlParams, 
  UpdateUrlParams,
  CrawlUrlParams
} from '../types/url.types';

/**
 * Hook để lấy danh sách URL với phân trang và tìm kiếm
 */
export const useUrlsQuery = (params: UrlSearchParams = {}) => {
  return useQuery({
    queryKey: ['admin', 'urls', params],
    queryFn: () => UrlService.getUrls(params),
  });
};

/**
 * Hook để lấy thông tin chi tiết của một URL
 */
export const useUrlQuery = (id: string) => {
  return useQuery({
    queryKey: ['admin', 'url', id],
    queryFn: () => UrlService.getUrlById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tìm kiếm URL theo từ khóa
 */
export const useSearchUrlsQuery = (keyword: string, limit: number = 10) => {
  return useQuery({
    queryKey: ['admin', 'urls', 'search', keyword, limit],
    queryFn: () => UrlService.searchUrls(keyword, limit),
    enabled: !!keyword,
  });
};

/**
 * Hook để tạo URL mới
 */
export const useCreateUrlMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (urlData: CreateUrlParams) => UrlService.createUrl(urlData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'urls'] });
    },
  });
};

/**
 * Hook để cập nhật URL
 */
export const useUpdateUrlMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, urlData }: { id: string; urlData: UpdateUrlParams }) => 
      UrlService.updateUrl(id, urlData),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'urls'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'url', variables.id] });
    },
  });
};

/**
 * Hook để xóa URL
 */
export const useDeleteUrlMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => UrlService.deleteUrl(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'urls'] });
    },
  });
};

/**
 * Hook để đảo ngược trạng thái kích hoạt của URL
 */
export const useToggleUrlStatusMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => UrlService.toggleUrlStatus(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'urls'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'url', id] });
    },
  });
};

/**
 * Hook để crawl URL và các URL con
 */
export const useCrawlUrlMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (params: CrawlUrlParams) => UrlService.crawlUrl(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'urls'] });
    },
  });
};
