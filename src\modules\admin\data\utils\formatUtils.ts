/**
 * Utility functions cho module admin data
 */

/**
 * Chuyển đổi byte sang đơn vị đọc đượ<PERSON> (KB, MB, GB)
 * @param bytes Kích thước tính bằng byte
 * @param decimals Số chữ số thập phân (mặc định: 2)
 * @returns Chuỗi đã được định dạng
 */
export const formatFileSize = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Định dạng timestamp thành chuỗi ngày giờ
 * @param timestamp Timestamp (milliseconds)
 * @returns Chuỗi ngày giờ đã được định dạng
 */
export const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

/**
 * Rút gọn chuỗi nếu quá dài
 * @param str Chuỗi cần rút gọn
 * @param maxLength Độ dài tối đa (mặc định: 50)
 * @returns Chuỗi đã được rút gọn
 */
export const truncateString = (str: string, maxLength = 50): string => {
  if (!str) return '';
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength) + '...';
};
