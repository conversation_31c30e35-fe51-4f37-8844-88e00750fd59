/**
 * Component Card cho module Employee
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Card, Icon, Typography } from '@/shared/components/common';

/**
 * Props cho EmployeeCard
 */
export interface EmployeeCardProps {
  /**
   * Tiêu đề card
   */
  title: string;
  /**
   * Mô tả card
   */
  description: string;
  /**
   * Icon của card
   */
  icon: string;
  /**
   * Đường dẫn khi click vào card
   */
  path: string;
  /**
   * Màu nền của icon
   */
  iconBgColor?: string;
  /**
   * Màu của icon
   */
  iconColor?: string;
}

/**
 * Component Card cho module Employee
 */
export const EmployeeCard: React.FC<EmployeeCardProps> = ({
  title,
  description,
  icon,
  path,
  iconBgColor = 'bg-primary/10',
  iconColor = 'text-primary',
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  /**
   * Xử lý khi click vào card
   */
  const handleClick = () => {
    navigate(path);
  };

  return (
    <Card
      className="h-full cursor-pointer transition-all duration-300 hover:shadow-md"
      onClick={handleClick}
    >
      <div className="flex flex-col p-6 h-full">
        <div
          className={`w-12 h-12 rounded-lg ${iconBgColor} flex items-center justify-center mb-4`}
        >
          <Icon name={icon} size="md" className={iconColor} />
        </div>
        <Typography variant="h5" className="font-semibold mb-2">
          {t(title)}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t(description)}
        </Typography>
      </div>
    </Card>
  );
};

export default EmployeeCard;
