import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormItem, Input, Button, FormGrid, Card, Toggle, Typography } from '@/shared/components/common';
import { ImageUploader } from '@/modules/business/components';
// Định nghĩa enum tạm thời
enum ImageTypeEnum {
  JPEG = 'image/jpeg',
  PNG = 'image/png',
  WEBP = 'image/webp',
  GIF = 'image/gif',
  BMP = 'image/bmp',
}
import { createEmployeeSchema } from '../../schemas/employee.schema';
import { useFormErrors } from '@/shared';
import { CreateEmployeeDto } from '../../types/employee.types';
import { useCreateEmployee } from '../../hooks/useEmployeeQuery';
import { employeeService } from '../../services/employee.service';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';
import useNotification from '@/shared/hooks/common/useNotification';

interface EmployeeFormProps {
  onSuccess?: () => void;
  onCancel: () => void;
}

/**
 * Component form tạo/chỉnh sửa nhân viên
 */
const EmployeeForm: React.FC<EmployeeFormProps> = ({ onSuccess, onCancel }) => {
  const { t } = useTranslation(['employee', 'common']);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [avatarType, setAvatarType] = useState<string | null>(null);
  const { addFileUploadTask } = useTaskQueueContext();
  const { addNotification } = useNotification();
  const createEmployeeMutation = useCreateEmployee();

  // Xử lý khi upload avatar
  const handleAvatarUpload = (file: File, dataUrl: string) => {
    setAvatarFile(file);
    setAvatarPreview(dataUrl);
    setAvatarType(file.type);
  };

  // Sử dụng useFormErrors để xử lý lỗi từ API
  const { formRef, setFormErrors } = useFormErrors();

  // Xử lý submit form
  const handleSubmit = async (values: Record<string, unknown>) => {
    try {
      // Sử dụng state avatarFile thay vì lấy từ values
      // const avatarFile = values.avatarFile as File | undefined;
      // Không cần lấy avatarImageType và avatarMaxSize từ values vì sẽ sử dụng state

      // Log để debug
      console.log('State avatarFile:', avatarFile);
      console.log('State avatarType:', avatarType);

      // Đã xóa phần này vì đã được xử lý ở phần dưới

      // Chuyển đổi giá trị form thành CreateEmployeeDto
      const employeeData: CreateEmployeeDto = {
        fullName: values['fullName'] as string,
        email: values['email'] as string,
        phoneNumber: values['phoneNumber'] as string,
        password: values['password'] as string,
        address: values['address'] as string,
        enable: values['enable'] as boolean,
      };

      // Nếu có thông tin avatar, thêm vào DTO
      if (avatarFile) {
        // Xác định loại ảnh dựa trên state avatarType
        let finalAvatarImageType: string;
        if (avatarType === 'image/jpeg' || avatarType === 'image/jpg') {
          finalAvatarImageType = ImageTypeEnum.JPEG;
        } else if (avatarType === 'image/png') {
          finalAvatarImageType = ImageTypeEnum.PNG;
        } else if (avatarType === 'image/webp') {
          finalAvatarImageType = ImageTypeEnum.WEBP;
        } else if (avatarType === 'image/gif') {
          finalAvatarImageType = ImageTypeEnum.GIF;
        } else {
          finalAvatarImageType = ImageTypeEnum.JPEG; // Mặc định
        }

        // Đảm bảo luôn có avatarMaxSize
        const finalAvatarMaxSize = avatarFile.size;

        // Gán vào DTO
        employeeData.avatarImageType = finalAvatarImageType;
        employeeData.avatarMaxSize = finalAvatarMaxSize;

        console.log('Adding avatar info to DTO:', {
          avatarImageType: finalAvatarImageType,
          avatarMaxSize: finalAvatarMaxSize,
          fileSize: avatarFile.size,
          fileType: avatarFile.type
        });
      }

      // Log dữ liệu trước khi gọi API
      console.log('Employee data being sent to API:', JSON.stringify(employeeData, null, 2));

      // Gọi API tạo nhân viên mới
      createEmployeeMutation.mutate(employeeData, {
        onSuccess: async response => {
          try {
            // Log response từ API
            console.log('API response:', JSON.stringify(response, null, 2));

            // Nếu có avatar và API trả về URL để upload
            if (avatarFile && response.result.avatarUploadUrl) {
              console.log('Uploading avatar to:', response.result.avatarUploadUrl);
              console.log('Avatar file to upload:', avatarFile);

              // Upload avatar lên cloud sử dụng Task Queue
              addFileUploadTask({
                title: `Upload avatar cho ${values['fullName']}`,
                description: `Đang tải lên ảnh đại diện (${(avatarFile.size / 1024).toFixed(2)} KB)`,
                file: avatarFile,
                uploadUrl: response.result.avatarUploadUrl,
                execute: async (url: string, fileToUpload: File, onProgressCallback: (progress: number) => void) => {
                  try {
                    // Tạo XMLHttpRequest để theo dõi tiến trình
                    const xhr = new XMLHttpRequest();

                    // Tạo Promise để xử lý kết quả
                    const uploadPromise = new Promise<unknown>((resolve, reject) => {
                      // Xử lý sự kiện khi upload hoàn thành
                      xhr.onload = () => {
                        if (xhr.status >= 200 && xhr.status < 300) {
                          resolve({
                            success: true,
                            status: xhr.status,
                            url: response.result.avatarUploadUrl,
                            fileName: fileToUpload.name,
                            fileSize: fileToUpload.size,
                          });
                        } else {
                          reject(
                            new Error(
                              `Upload thất bại với mã lỗi ${xhr.status}: ${xhr.statusText || 'Không có thông tin lỗi'}`
                            )
                          );
                        }
                      };

                      // Xử lý sự kiện khi upload thất bại
                      xhr.onerror = () => {
                        reject(new Error('Lỗi kết nối khi upload file'));
                      };

                      // Xử lý sự kiện khi upload bị hủy
                      xhr.onabort = () => {
                        reject(new Error('Upload đã bị hủy'));
                      };

                      // Xử lý sự kiện khi tiến trình upload thay đổi
                      xhr.upload.onprogress = (event) => {
                        if (event.lengthComputable) {
                          const progress = Math.round((event.loaded / event.total) * 100);
                          onProgressCallback(progress);
                        }
                      };
                    });

                    // Mở kết nối
                    xhr.open('PUT', url, true);

                    // Gửi file
                    xhr.send(fileToUpload);

                    // Trả về kết quả
                    return await uploadPromise;
                  } catch (error) {
                    console.error('Lỗi khi upload file:', error);
                    throw error;
                  }
                },
                onSuccess: async () => {
                  console.log('Avatar uploaded successfully');

                  // Nếu API trả về avatarKey, cập nhật avatar cho nhân viên
                  if (response.result.avatarKey) {
                    try {
                      await employeeService.updateAvatar(response.result.id, {
                        avatarKey: response.result.avatarKey,
                      });
                      console.log('Avatar updated for employee');
                    } catch (updateError) {
                      console.error('Error updating avatar for employee:', updateError);
                    }
                  }
                },
                onError: (error: Error) => {
                  console.error('Error uploading avatar:', error);
                  addNotification(
                    'error',
                    t('employee:messages.avatarUploadError', 'Có lỗi xảy ra khi tải lên ảnh đại diện')
                  );
                },
                cancellable: true,
                retryable: true,
                maxRetries: 3,
                metadata: {
                  fileType: avatarFile.type,
                  fileSize: avatarFile.size,
                  fileName: avatarFile.name,
                  employeeId: response.result.id,
                },
              });
            }

            // Hiển thị thông báo thành công
            addNotification(
              'success',
              t('employee:messages.createSuccess', 'Tạo nhân viên mới thành công')
            );

            // Gọi callback onSuccess nếu có
            if (onSuccess) {
              onSuccess();
            }
          } catch (uploadError) {
            console.error('Error setting up avatar upload:', uploadError);
            // Vẫn hiển thị thông báo thành công vì nhân viên đã được tạo
            addNotification(
              'success',
              t(
                'employee:messages.createSuccess',
                'Tạo nhân viên mới thành công, nhưng có lỗi khi tải lên ảnh đại diện'
              )
            );
            if (onSuccess) {
              onSuccess();
            }
          }
        },
        onError: error => {
          // Hiển thị thông báo lỗi
          console.error('Error creating employee:', error);

          // Log chi tiết lỗi từ API
          if (error.response?.data) {
            console.log('API error response:', JSON.stringify(error.response.data, null, 2));
          }

          // Xử lý lỗi cụ thể từ API
          if (error.response?.data) {
            const errorData = error.response.data as { code?: number; message?: string; path?: string };

            // Xử lý lỗi email đã tồn tại
            if (errorData.code === 15001) {
              setFormErrors({
                email: 'Email đã được sử dụng'
              });
              return;
            }

            // Xử lý lỗi số điện thoại đã tồn tại
            if (errorData.code === 10009 || errorData.code === 15043) {
              setFormErrors({
                phoneNumber: 'Số điện thoại đã được sử dụng'
              });
              return;
            }

            // Xử lý lỗi mật khẩu yếu
            if (errorData.code === 15002) {
              setFormErrors({
                password: 'Mật khẩu không đủ mạnh. Vui lòng sử dụng ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt.'
              });
              return;
            }

            // Xử lý lỗi xác nhận mật khẩu không khớp
            if (errorData.code === 15003) {
              setFormErrors({
                confirmPassword: 'Mật khẩu xác nhận không khớp với mật khẩu'
              });
              return;
            }

            // Xử lý lỗi thiếu thông tin bắt buộc
            if (errorData.code === 15004) {
              // Hiển thị thông báo lỗi chung
              addNotification(
                'error',
                'Vui lòng điền đầy đủ thông tin bắt buộc'
              );
              return;
            }
          }

          // Hiển thị thông báo lỗi từ API nếu có, nếu không thì hiển thị thông báo mặc định
          const errorMessage = error.response?.data ?
            (error.response.data as { message?: string }).message || t('employee:messages.createError', 'Có lỗi xảy ra khi tạo nhân viên mới') :
            t('employee:messages.createError', 'Có lỗi xảy ra khi tạo nhân viên mới');

          addNotification(
            'error',
            errorMessage
          );
        },
      });
    } catch (error) {
      console.error('Error in form submission:', error);
      addNotification(
        'error',
        t('employee:messages.createError', 'Có lỗi xảy ra khi tạo nhân viên mới')
      );
    }
  };

  return (
    <Card title={t('employee:form.title', 'Thêm nhân viên mới')} className="mb-4">
      <div className="p-4">
        <Form schema={createEmployeeSchema} onSubmit={handleSubmit} className="space-y-6" ref={formRef}>
          <FormGrid columns={2} gap="md">
            <FormItem name="fullName" label={t('employee:form.fullName', 'Tên đầy đủ')} required>
              <Input
                placeholder={t('employee:form.fullNamePlaceholder', 'Nhập tên đầy đủ')}
                fullWidth
              />
            </FormItem>

            <FormItem name="email" label={t('employee:form.email', 'Email')} required>
              <Input
                type="email"
                placeholder={t('employee:form.emailPlaceholder', 'Nhập email')}
                fullWidth
              />
            </FormItem>
          </FormGrid>

          <FormGrid columns={2} gap="md">
            <FormItem
              name="phoneNumber"
              label={t('employee:form.phoneNumber', 'Số điện thoại')}
              required
            >
              <Input
                placeholder={t('employee:form.phoneNumberPlaceholder', 'Nhập số điện thoại')}
                fullWidth
              />
            </FormItem>

            <FormItem name="address" label={t('employee:form.address', 'Địa chỉ')} required>
              <Input
                placeholder={t('employee:form.addressPlaceholder', 'Nhập địa chỉ')}
                fullWidth
              />
            </FormItem>
          </FormGrid>

          <FormGrid columns={2} gap="md">
            <FormItem name="password" label={t('employee:form.password', 'Mật khẩu')} required>
              <Input
                type="password"
                placeholder={t('employee:form.passwordPlaceholder', 'Nhập mật khẩu')}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="confirmPassword"
              label={t('employee:form.confirmPassword', 'Xác nhận mật khẩu')}
              required
            >
              <Input
                type="password"
                placeholder={t('employee:form.confirmPasswordPlaceholder', 'Nhập lại mật khẩu')}
                fullWidth
              />
            </FormItem>
          </FormGrid>

          <FormItem name="avatar" label={t('employee:form.avatar', 'Ảnh đại diện')}>
            <div className="space-y-2">
              <ImageUploader
                onImageUpload={handleAvatarUpload}
                currentImage={avatarPreview}
                height="h-40"
                placeholder={t(
                  'employee:form.avatarPlaceholder',
                  'Kéo thả hoặc click để tải lên ảnh đại diện'
                )}
              />
              {avatarFile && (
                <div className="mt-2">
                  <Typography variant="caption" className="text-gray-500 dark:text-gray-400 block">
                    {t('employee:form.avatar_upload_size', 'Kích thước: {{size}}', {
                      size: (avatarFile.size / 1024).toFixed(2) + ' KB',
                    })}
                  </Typography>
                  <Typography variant="caption" className="text-gray-500 dark:text-gray-400 block">
                    {t('employee:form.avatar_upload_type', 'Định dạng: {{type}}', {
                      type: avatarType,
                    })}
                  </Typography>
                </div>
              )}
            </div>
          </FormItem>

          <FormItem name="enable" label={t('employee:form.enable', 'Kích hoạt tài khoản')}>
            <Toggle checked={true} />
          </FormItem>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onCancel}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button type="submit" variant="primary">
              {t('common:save', 'Lưu')}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default EmployeeForm;
