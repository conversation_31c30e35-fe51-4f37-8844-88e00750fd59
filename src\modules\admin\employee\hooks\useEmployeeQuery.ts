/**
 * Hooks React Query cho nhân viên
 */
import { useApiQuery, useApiMutation } from '@/shared/api/hooks';
import { useQueryClient } from '@tanstack/react-query';
import {
  EmployeeDto,
  CreateEmployeeDto,
  UpdateEmployeeDto,
  ChangeEmployeePasswordDto,
  AssignEmployeeRoleDto,
  EmployeeAvatarUploadDto,
  UpdateEmployeeAvatarDto,
  EmployeeQueryDto,
  PaginatedEmployeeResult,
  AvatarUploadResponseDto,
  ChangePasswordResponseDto,
} from '../types/employee.types';

// Định nghĩa các query key
export const EMPLOYEE_QUERY_KEYS = {
  all: ['employees'] as const,
  lists: () => [...EMPLOYEE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: EmployeeQueryDto) => [...EMPLOYEE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...EMPLOYEE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...EMPLOYEE_QUERY_KEYS.details(), id] as const,
  roles: (id: number) => [...EMPLOYEE_QUERY_KEYS.detail(id), 'roles'] as const,
};

/**
 * Hook để lấy danh sách nhân viên
 * @param queryDto Tham số truy vấn
 * @returns Query object
 */
export const useEmployees = (queryDto?: EmployeeQueryDto) => {
  return useApiQuery<PaginatedEmployeeResult>(
    EMPLOYEE_QUERY_KEYS.list(queryDto || { page: 1, limit: 10 }),
    '/employees',
    {
      params: queryDto,
    }
  );
};

/**
 * Hook để lấy thông tin nhân viên theo ID
 * @param id ID nhân viên
 * @returns Query object
 */
export const useEmployee = (id: number) => {
  return useApiQuery<EmployeeDto>(EMPLOYEE_QUERY_KEYS.detail(id), `/employees/${id}`);
};

/**
 * Hook để tạo nhân viên mới
 * @returns Mutation object
 */
export const useCreateEmployee = () => {
  const queryClient = useQueryClient();

  return useApiMutation<EmployeeDto, CreateEmployeeDto>('/employees', {
    onSuccess: () => {
      // Invalidate để load lại danh sách nhân viên
      queryClient.invalidateQueries({
        queryKey: EMPLOYEE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin nhân viên
 * @param id ID nhân viên
 * @returns Mutation object
 */
export const useUpdateEmployee = (id: number) => {
  const queryClient = useQueryClient();

  return useApiMutation<EmployeeDto, UpdateEmployeeDto>(`/employees/${id}`, {
    onSuccess: () => {
      // Invalidate để load lại thông tin nhân viên và danh sách
      queryClient.invalidateQueries({
        queryKey: EMPLOYEE_QUERY_KEYS.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: EMPLOYEE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để đổi mật khẩu nhân viên
 * @param id ID nhân viên
 * @returns Mutation object
 */
export const useChangePassword = (id: number) => {
  return useApiMutation<ChangePasswordResponseDto, ChangeEmployeePasswordDto>(
    `/employees/${id}/password`
  );
};

/**
 * Hook để gán vai trò cho nhân viên
 * @param id ID nhân viên
 * @returns Mutation object
 */
export const useAssignRoles = (id: number) => {
  const queryClient = useQueryClient();

  return useApiMutation<EmployeeDto, AssignEmployeeRoleDto>(`/employees/${id}/roles`, {
    onSuccess: () => {
      // Invalidate để load lại thông tin nhân viên và vai trò
      queryClient.invalidateQueries({
        queryKey: EMPLOYEE_QUERY_KEYS.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: EMPLOYEE_QUERY_KEYS.roles(id),
      });
    },
  });
};

/**
 * Hook để lấy danh sách vai trò của nhân viên
 * @param id ID nhân viên
 * @returns Query object
 */
export const useEmployeeRoles = (id: number) => {
  return useApiQuery<Record<string, unknown>[]>(
    EMPLOYEE_QUERY_KEYS.roles(id),
    `/employees/${id}/roles`
  );
};

/**
 * Hook để tạo URL tạm thời để tải lên avatar nhân viên
 * @param id ID nhân viên
 * @returns Mutation object
 */
export const useCreateAvatarUploadUrl = (id: number) => {
  return useApiMutation<AvatarUploadResponseDto, EmployeeAvatarUploadDto>(
    `/employees/${id}/avatar/upload-url`
  );
};

/**
 * Hook để cập nhật avatar cho nhân viên
 * @param id ID nhân viên
 * @returns Mutation object
 */
export const useUpdateAvatar = (id: number) => {
  const queryClient = useQueryClient();

  return useApiMutation<EmployeeDto, UpdateEmployeeAvatarDto>(`/employees/${id}/avatar`, {
    onSuccess: () => {
      // Invalidate để load lại thông tin nhân viên
      queryClient.invalidateQueries({
        queryKey: EMPLOYEE_QUERY_KEYS.detail(id),
      });
    },
  });
};
