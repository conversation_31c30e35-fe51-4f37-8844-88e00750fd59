/**
 * Hooks React Query cho vai trò và quyền
 */
import { useApiQuery, useApiMutation, useApiPutMutation } from '@/shared/api/hooks';
import { useQueryClient } from '@tanstack/react-query';
import { RoleDto, AssignRolePermissionsDto, RoleQueryDto } from '../types/role.types';
import { PermissionDto } from '../types/employee.types';

// Định nghĩa các query key
export const ROLE_QUERY_KEYS = {
  all: ['employee', 'roles'] as const,
  lists: () => [...ROLE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: RoleQueryDto) => [...ROLE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...ROLE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...ROLE_QUERY_KEYS.details(), id] as const,
  permissions: () => [...ROLE_QUERY_KEYS.all, 'permissions'] as const,
  rolePermissions: (id: number) => [...ROLE_QUERY_KEYS.detail(id), 'permissions'] as const,
};

/**
 * Hook để lấy danh sách tất cả vai trò
 * @returns Query object
 */
export const useAllRoles = () => {
  return useApiQuery<RoleDto[]>(ROLE_QUERY_KEYS.lists(), '/employee/roles');
};

/**
 * Hook để lấy thông tin vai trò theo ID
 * @param id ID vai trò
 * @returns Query object
 */
export const useRole = (id: number) => {
  return useApiQuery<RoleDto>(ROLE_QUERY_KEYS.detail(id), `/employee/roles/${id}`);
};

/**
 * Hook để lấy danh sách tất cả quyền
 * @returns Query object
 */
export const useAllPermissions = () => {
  return useApiQuery<PermissionDto[]>(
    ROLE_QUERY_KEYS.permissions(),
    '/employee/roles/permissions/all'
  );
};

/**
 * Hook để lấy danh sách quyền của vai trò
 * @param id ID vai trò
 * @returns Query object
 */
export const useRolePermissions = (id: number) => {
  return useApiQuery<PermissionDto[]>(
    ROLE_QUERY_KEYS.rolePermissions(id),
    `/employee/roles/${id}/permissions`
  );
};

/**
 * Hook để gán quyền cho vai trò
 * @param id ID vai trò
 * @returns Mutation object
 */
export const useAssignPermissions = (id: number) => {
  const queryClient = useQueryClient();

  return useApiMutation<RoleDto, AssignRolePermissionsDto>(`/employee/roles/${id}/permissions`, {
    onSuccess: () => {
      // Invalidate để load lại thông tin vai trò và quyền
      queryClient.invalidateQueries({
        queryKey: ROLE_QUERY_KEYS.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: ROLE_QUERY_KEYS.rolePermissions(id),
      });
      queryClient.invalidateQueries({
        queryKey: ROLE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để gán vai trò cho nhân viên
 * @param employeeId ID nhân viên
 * @returns Mutation object
 */
export const useAssignEmployeeRoles = (employeeId: number) => {
  const queryClient = useQueryClient();

  return useApiPutMutation<{ success: boolean }, { roleIds: number[] }>(`/employees/${employeeId}/roles`, {
    onSuccess: () => {
      // Invalidate để load lại thông tin nhân viên
      queryClient.invalidateQueries({
        queryKey: ['employee', 'employees'],
      });
    },
  });
};

/**
 * Hook để lấy vai trò của nhân viên
 * @param employeeId ID nhân viên
 * @returns Query object
 */
export const useEmployeeRoles = (employeeId: number) => {
  return useApiQuery<RoleDto[]>(
    ['employee', 'employees', employeeId, 'roles'],
    `/employees/${employeeId}/roles`
  );
};
