import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';
import AdminLayout from '@/shared/layouts/AdminLayout';
// Import pages
import { lazy } from 'react';

const EmployeePage = lazy(() => import('../pages/EmployeePage'));
const EmployeeListPage = lazy(() => import('../pages/EmployeeListPage'));
const RolePage = lazy(() => import('../pages/RolePage'));
const PermissionPage = lazy(() => import('../pages/PermissionPage'));


/**
 * Routes cho module employee
 */
export const employeeRoutes: RouteObject[] = [
  {
    path: '/admin/employees',
    element: (
      <AdminLayout title={i18n.t('employee:title')}>
        <Suspense fallback={<Loading />}>
          <EmployeePage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/employees/list',
    element: (
      <AdminLayout title={i18n.t('employee:list.title')}>
        <Suspense fallback={<Loading />}>
          <EmployeeListPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/employees/roles',
    element: (
      <AdminLayout title={i18n.t('employee:role.title')}>
        <Suspense fallback={<Loading />}>
          <RolePage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/employees/permissions',
    element: (
      <AdminLayout title={i18n.t('employee:permission.title')}>
        <Suspense fallback={<Loading />}>
          <PermissionPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default employeeRoutes;
