/**
 * Schema validation cho form nhân viên
 */
import { z } from 'zod';

/**
 * Schema cho form tạo nhân viên mới
 */
export const createEmployeeSchema = z
  .object({
    fullName: z
      .string()
      .min(2, 'Tên đầy đủ phải có ít nhất 2 ký tự')
      .max(100, 'Tên đầy đủ không được vượt quá 100 ký tự'),
    email: z.string().email('Email không hợp lệ').max(100, 'Email không được vượt quá 100 ký tự'),
    phoneNumber: z.string().regex(/^(0[3|5|7|8|9])+([0-9]{8})$/, 'Số điện thoại không hợp lệ'),
    password: z
      .string()
      .min(6, 'Mật khẩu phải có ít nhất 6 ký tự')
      .max(50, '<PERSON><PERSON><PERSON> khẩu không được vượt quá 50 ký tự'),
    confirmPassword: z
      .string()
      .min(6, '<PERSON><PERSON><PERSON> khẩu xác nhận phải có ít nhất 6 ký tự')
      .max(50, 'Mật khẩu xác nhận không được vượt quá 50 ký tự'),
    address: z
      .string()
      .min(5, 'Địa chỉ phải có ít nhất 5 ký tự')
      .max(200, 'Địa chỉ không được vượt quá 200 ký tự'),
    enable: z.boolean().optional().default(true),
    roleIds: z.array(z.number()).optional(),
    // Trường cho avatar
    avatarFile: z.instanceof(File).optional(),
    avatarImageType: z.string().optional(),
    avatarMaxSize: z.number().optional(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Mật khẩu xác nhận không khớp',
    path: ['confirmPassword'],
  });

/**
 * Schema cho form cập nhật nhân viên
 */
export const updateEmployeeSchema = z.object({
  fullName: z
    .string()
    .min(2, 'Tên đầy đủ phải có ít nhất 2 ký tự')
    .max(100, 'Tên đầy đủ không được vượt quá 100 ký tự')
    .optional(),
  phoneNumber: z
    .string()
    .regex(/^(0[3|5|7|8|9])+([0-9]{8})$/, 'Số điện thoại không hợp lệ')
    .optional(),
  address: z
    .string()
    .min(5, 'Địa chỉ phải có ít nhất 5 ký tự')
    .max(200, 'Địa chỉ không được vượt quá 200 ký tự')
    .optional(),
  enable: z.boolean().optional(),
});

/**
 * Schema cho form đổi mật khẩu nhân viên
 */
export const changePasswordSchema = z.object({
  newPassword: z
    .string()
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự')
    .max(50, 'Mật khẩu không được vượt quá 50 ký tự'),
});

/**
 * Schema cho form gán vai trò cho nhân viên
 */
export const assignRoleSchema = z.object({
  roleIds: z.array(z.number()).min(1, 'Phải chọn ít nhất một vai trò'),
});

/**
 * Schema cho form tải lên avatar nhân viên
 */
export const avatarUploadSchema = z.object({
  imageType: z.string().regex(/^image\/(jpeg|png|jpg)$/, 'Định dạng ảnh không hợp lệ'),
  maxSize: z
    .number()
    .min(1024, 'Kích thước tối thiểu là 1KB')
    .max(5242880, 'Kích thước tối đa là 5MB'),
});

/**
 * Schema cho form cập nhật avatar nhân viên
 */
export const updateAvatarSchema = z.object({
  avatarKey: z.string().min(1, 'Khóa avatar không được để trống'),
});

/**
 * Schema cho form đăng nhập nhân viên
 */
export const loginSchema = z.object({
  email: z.string().email('Email không hợp lệ'),
  password: z.string().min(1, 'Mật khẩu không được để trống'),
});

/**
 * Kiểu dữ liệu cho form tạo nhân viên mới
 */
export type CreateEmployeeFormValues = z.infer<typeof createEmployeeSchema>;

/**
 * Kiểu dữ liệu cho form cập nhật nhân viên
 */
export type UpdateEmployeeFormValues = z.infer<typeof updateEmployeeSchema>;

/**
 * Kiểu dữ liệu cho form đổi mật khẩu nhân viên
 */
export type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;

/**
 * Kiểu dữ liệu cho form gán vai trò cho nhân viên
 */
export type AssignRoleFormValues = z.infer<typeof assignRoleSchema>;

/**
 * Kiểu dữ liệu cho form tải lên avatar nhân viên
 */
export type AvatarUploadFormValues = z.infer<typeof avatarUploadSchema>;

/**
 * Kiểu dữ liệu cho form cập nhật avatar nhân viên
 */
export type UpdateAvatarFormValues = z.infer<typeof updateAvatarSchema>;

/**
 * Kiểu dữ liệu cho form đăng nhập nhân viên
 */
export type LoginFormValues = z.infer<typeof loginSchema>;
