/**
 * Schema validation cho form vai trò và quyền
 */
import { z } from 'zod';

/**
 * Schema cho form gán quyền cho vai trò
 */
export const assignPermissionsSchema = z.object({
  permissionIds: z.array(z.number()).min(1, '<PERSON><PERSON>i chọn ít nhất một quyền'),
});

/**
 * Kiểu dữ liệu cho form gán quyền cho vai trò
 */
export type AssignPermissionsFormValues = z.infer<typeof assignPermissionsSchema>;
