/**
 * Service cho quản lý nhân viên
 */
import { apiClient } from '@/shared/api';
import {
  EmployeeDto,
  EmployeeQueryDto,
  CreateEmployeeDto,
  UpdateEmployeeDto,
  ChangeEmployeePasswordDto,
  AssignEmployeeRoleDto,
  EmployeeAvatarUploadDto,
  UpdateEmployeeAvatarDto,
  EmployeeLoginDto,
  EmployeeLoginResponseDto,
  AvatarUploadResponseDto,
  ChangePasswordResponseDto,
  PaginatedEmployeeResult,
  EmployeeApiResponse,
} from '../types/employee.types';

/**
 * Base URL cho API nhân viên
 */
const BASE_URL = '/employees';

/**
 * Service cho quản lý nhân viên
 */
export const employeeService = {
  /**
   * Đăng nhập nhân viên
   * @param data Thông tin đăng nhập
   * @returns Thông tin đăng nhập thành công
   */
  login: async (data: EmployeeLoginDto): Promise<EmployeeApiResponse<EmployeeLoginResponseDto>> => {
    const response = await apiClient.post<EmployeeApiResponse<EmployeeLoginResponseDto>>(
      `${BASE_URL}/login`,
      data
    );
    return response.result;
  },

  /**
   * Lấy danh sách nhân viên
   * @param params Tham số truy vấn
   * @returns Danh sách nhân viên phân trang
   */
  getEmployees: async (
    params: EmployeeQueryDto
  ): Promise<EmployeeApiResponse<PaginatedEmployeeResult>> => {
    const response = await apiClient.get<EmployeeApiResponse<PaginatedEmployeeResult>>(BASE_URL, {
      params,
      tokenType: 'admin', // Explicitly use admin token
    });
    return response.result;
  },

  /**
   * Lấy thông tin chi tiết nhân viên
   * @param id ID nhân viên
   * @returns Thông tin chi tiết nhân viên
   */
  getEmployee: async (id: number): Promise<EmployeeApiResponse<EmployeeDto>> => {
    const response = await apiClient.get<EmployeeApiResponse<EmployeeDto>>(`${BASE_URL}/${id}`, {
      tokenType: 'admin', // Explicitly use admin token
    });
    return response.result;
  },

  /**
   * Tạo nhân viên mới
   * @param data Dữ liệu tạo nhân viên
   * @returns Thông tin nhân viên đã tạo
   */
  createEmployee: async (data: CreateEmployeeDto): Promise<EmployeeApiResponse<EmployeeDto>> => {
    const response = await apiClient.post<EmployeeApiResponse<EmployeeDto>>(BASE_URL, data, {
      tokenType: 'admin', // Explicitly use admin token
    });
    return response.result;
  },

  /**
   * Cập nhật thông tin nhân viên
   * @param id ID nhân viên
   * @param data Dữ liệu cập nhật
   * @returns Thông tin nhân viên đã cập nhật
   */
  updateEmployee: async (
    id: number,
    data: UpdateEmployeeDto
  ): Promise<EmployeeApiResponse<EmployeeDto>> => {
    const response = await apiClient.patch<EmployeeApiResponse<EmployeeDto>>(
      `${BASE_URL}/${id}`,
      data,
      {
        tokenType: 'admin', // Explicitly use admin token
      }
    );
    return response.result;
  },

  /**
   * Xóa nhân viên
   * @param id ID nhân viên
   * @returns Kết quả xóa
   */
  deleteEmployee: async (id: number): Promise<EmployeeApiResponse<null>> => {
    const response = await apiClient.delete<EmployeeApiResponse<null>>(`${BASE_URL}/${id}`, {
      tokenType: 'admin', // Explicitly use admin token
    });
    return response.result;
  },

  /**
   * Đổi mật khẩu nhân viên
   * @param id ID nhân viên
   * @param data Dữ liệu đổi mật khẩu
   * @returns Kết quả đổi mật khẩu
   */
  changePassword: async (
    id: number,
    data: ChangeEmployeePasswordDto
  ): Promise<EmployeeApiResponse<ChangePasswordResponseDto>> => {
    const response = await apiClient.post<EmployeeApiResponse<ChangePasswordResponseDto>>(
      `${BASE_URL}/${id}/change-password`,
      data,
      {
        tokenType: 'admin', // Explicitly use admin token
      }
    );
    return response.result;
  },

  /**
   * Gán vai trò cho nhân viên
   * @param id ID nhân viên
   * @param data Dữ liệu gán vai trò
   * @returns Thông tin nhân viên đã cập nhật
   */
  assignRoles: async (
    id: number,
    data: AssignEmployeeRoleDto
  ): Promise<EmployeeApiResponse<EmployeeDto>> => {
    const response = await apiClient.post<EmployeeApiResponse<EmployeeDto>>(
      `${BASE_URL}/${id}/roles`,
      data,
      {
        tokenType: 'admin', // Explicitly use admin token
      }
    );
    return response.result;
  },

  /**
   * Lấy URL tải lên avatar cho nhân viên
   * @param employeeId ID nhân viên
   * @param fileInfo Thông tin file (type và size)
   * @returns URL tải lên avatar và key
   */
  getEmployeeAvatarUploadUrl: async (
    employeeId: number,
    fileInfo: { type: string; size: number }
  ): Promise<EmployeeApiResponse<AvatarUploadResponseDto>> => {
    const response = await apiClient.post<EmployeeApiResponse<AvatarUploadResponseDto>>(
      `${BASE_URL}/${employeeId}/avatar-upload-url`,
      { file: fileInfo },
      {
        tokenType: 'admin', // Explicitly use admin token
      }
    );
    return response.result;
  },

  /**
   * Lấy URL tải lên avatar (API cũ)
   * @param data Dữ liệu tải lên avatar
   * @returns URL tải lên avatar
   * @deprecated Sử dụng getEmployeeAvatarUploadUrl thay thế
   */
  getAvatarUploadUrl: async (
    data: EmployeeAvatarUploadDto
  ): Promise<EmployeeApiResponse<AvatarUploadResponseDto>> => {
    const response = await apiClient.post<EmployeeApiResponse<AvatarUploadResponseDto>>(
      `${BASE_URL}/avatar/upload-url`,
      data,
      {
        tokenType: 'admin', // Explicitly use admin token
      }
    );
    return response.result;
  },

  /**
   * Cập nhật avatar nhân viên
   * @param id ID nhân viên
   * @param data Dữ liệu cập nhật avatar
   * @returns Thông tin nhân viên đã cập nhật
   */
  updateAvatar: async (
    id: number,
    data: UpdateEmployeeAvatarDto
  ): Promise<EmployeeApiResponse<EmployeeDto>> => {
    const response = await apiClient.patch<EmployeeApiResponse<EmployeeDto>>(
      `${BASE_URL}/${id}/avatar`,
      data,
      {
        tokenType: 'admin', // Explicitly use admin token
      }
    );
    return response.result;
  },
};
