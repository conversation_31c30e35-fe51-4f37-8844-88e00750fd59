/**
 * Service cho quản lý vai trò
 */
import { apiClient } from '@/shared/api';
import {
  RoleDto,
  RoleQueryDto,
  CreateRoleDto,
  UpdateRoleDto,
  AssignRolePermissionsDto,
  PaginatedRoleResult,
  RoleApiResponse,
} from '../types/role.types';
import { PermissionDto } from '../types/employee.types';

/**
 * Base URL cho API vai trò
 */
const BASE_URL = '/admin/roles';

/**
 * Service cho quản lý vai trò
 */
export const roleService = {
  /**
   * Lấy danh sách vai trò
   * @param params Tham số truy vấn
   * @returns Danh sách vai trò phân trang
   */
  getRoles: async (params: RoleQueryDto): Promise<RoleApiResponse<PaginatedRoleResult>> => {
    const response = await apiClient.get<RoleApiResponse<PaginatedRoleResult>>(BASE_URL, {
      params,
    });
    return response.result;
  },

  /**
   * <PERSON><PERSON>y thông tin chi tiết vai trò
   * @param id ID vai trò
   * @returns Thông tin chi tiết vai trò
   */
  getRole: async (id: number): Promise<RoleApiResponse<RoleDto>> => {
    const response = await apiClient.get<RoleApiResponse<RoleDto>>(`${BASE_URL}/${id}`);
    return response.result;
  },

  /**
   * Tạo vai trò mới
   * @param data Dữ liệu tạo vai trò
   * @returns Thông tin vai trò đã tạo
   */
  createRole: async (data: CreateRoleDto): Promise<RoleApiResponse<RoleDto>> => {
    const response = await apiClient.post<RoleApiResponse<RoleDto>>(BASE_URL, data);
    return response.result;
  },

  /**
   * Cập nhật thông tin vai trò
   * @param id ID vai trò
   * @param data Dữ liệu cập nhật
   * @returns Thông tin vai trò đã cập nhật
   */
  updateRole: async (id: number, data: UpdateRoleDto): Promise<RoleApiResponse<RoleDto>> => {
    const response = await apiClient.patch<RoleApiResponse<RoleDto>>(`${BASE_URL}/${id}`, data);
    return response.result;
  },

  /**
   * Xóa vai trò
   * @param id ID vai trò
   * @returns Kết quả xóa
   */
  deleteRole: async (id: number): Promise<RoleApiResponse<null>> => {
    const response = await apiClient.delete<RoleApiResponse<null>>(`${BASE_URL}/${id}`);
    return response.result;
  },

  /**
   * Gán quyền cho vai trò
   * @param id ID vai trò
   * @param data Dữ liệu gán quyền
   * @returns Thông tin vai trò đã cập nhật
   */
  assignPermissions: async (
    id: number,
    data: AssignRolePermissionsDto
  ): Promise<RoleApiResponse<RoleDto>> => {
    const response = await apiClient.post<RoleApiResponse<RoleDto>>(
      `${BASE_URL}/${id}/permissions`,
      data
    );
    return response.result;
  },

  /**
   * Lấy danh sách quyền
   * @returns Danh sách quyền
   */
  getPermissions: async (): Promise<RoleApiResponse<PermissionDto[]>> => {
    const response = await apiClient.get<RoleApiResponse<PermissionDto[]>>('/admin/permissions');
    return response.result;
  },
};
