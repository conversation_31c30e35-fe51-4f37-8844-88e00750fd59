/**
 * <PERSON><PERSON><PERSON> kiểu dữ liệu liên quan đến nhân viên
 */
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Trạng thái nhân viên
 */
export enum EmployeeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Giới tính nhân viên
 */
export enum EmployeeGender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

/**
 * DTO cho nhân viên
 */
export interface EmployeeDto {
  id: number;
  fullName: string;
  email: string;
  phoneNumber: string;
  address?: string;
  avatar?: string;
  avatarKey?: string;
  avatarUploadUrl?: string;
  avatarUrlExpiresAt?: number;
  gender?: EmployeeGender;
  dateOfBirth?: string;
  status: EmployeeStatus;
  enable?: boolean;
  department?: string;
  position?: string;
  joinDate?: string;
  createdAt: string;
  updatedAt: string;
  roles?: EmployeeRoleDto[];
}

/**
 * DTO cho vai trò của nhân viên
 */
export interface EmployeeRoleDto {
  id: number;
  name: string;
  description: string;
  permissions?: PermissionDto[];
}

/**
 * DTO cho quyền
 */
export interface PermissionDto {
  id: number;
  action: string; // Tên hành động (ví dụ: "create", "read", "update", "delete")
  description: string;
  module: string; // Module/nhóm quyền (ví dụ: "user", "role", "permission")
  createdAt?: number; // Timestamp tạo
}

/**
 * DTO cho tạo nhân viên mới
 */
export interface CreateEmployeeDto {
  fullName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword?: string;
  address?: string;
  gender?: EmployeeGender;
  dateOfBirth?: string;
  department?: string;
  position?: string;
  joinDate?: string;
  roleIds?: number[];
  enable?: boolean;
  avatarImageType?: string;
  avatarMaxSize?: number;
}

/**
 * DTO cho cập nhật nhân viên
 */
export interface UpdateEmployeeDto {
  fullName?: string;
  phoneNumber?: string;
  address?: string;
  gender?: EmployeeGender;
  dateOfBirth?: string;
  status?: EmployeeStatus;
  department?: string;
  position?: string;
  joinDate?: string;
  roleIds?: number[];
}

/**
 * DTO cho đổi mật khẩu nhân viên
 */
export interface ChangeEmployeePasswordDto {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * DTO cho gán vai trò cho nhân viên
 */
export interface AssignEmployeeRoleDto {
  roleIds: number[];
}

/**
 * DTO cho upload avatar nhân viên
 */
export interface EmployeeAvatarUploadDto {
  file: File;
}

/**
 * DTO cho cập nhật avatar nhân viên
 */
export interface UpdateEmployeeAvatarDto {
  avatarKey: string;
}

/**
 * DTO cho response upload avatar
 */
export interface AvatarUploadResponseDto {
  presignedUrl: string;
  key: string;
  expiresIn: number;
}

/**
 * DTO cho response đổi mật khẩu
 */
export interface ChangePasswordResponseDto {
  success: boolean;
  message: string;
}

/**
 * DTO cho response đăng nhập nhân viên
 */
export interface EmployeeLoginResponseDto {
  accessToken: string;
  refreshToken: string;
  employee: EmployeeDto;
}

/**
 * DTO cho đăng nhập nhân viên
 */
export interface EmployeeLoginDto {
  email: string;
  password: string;
}

/**
 * DTO cho query nhân viên
 */
export interface EmployeeQueryDto extends QueryDto {
  status?: EmployeeStatus;
  roleId?: number;
  department?: string;
}

/**
 * Kiểu dữ liệu cho kết quả phân trang nhân viên
 */
export type PaginatedEmployeeResult = PaginatedResult<EmployeeDto>;

/**
 * Kiểu dữ liệu cho phản hồi API nhân viên
 */
export type EmployeeApiResponse<T> = ApiResponseDto<T>;
