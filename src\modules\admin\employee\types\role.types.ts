/**
 * <PERSON><PERSON><PERSON> kiểu dữ liệu liên quan đến vai trò và quyền
 */
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';
import { PermissionDto } from './employee.types';

/**
 * Trạng thái vai trò
 */
export enum RoleStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * DTO cho vai trò
 */
export interface RoleDto {
  id: number;
  name: string;
  description: string;
  status?: RoleStatus; // Optional vì backend chưa có
  isSystem?: boolean; // Optional vì backend chưa có
  createdAt?: string; // Optional vì backend chưa có
  updatedAt?: string; // Optional vì backend chưa có
  permissions?: PermissionDto[];
}

/**
 * DTO cho tạo vai trò mới
 */
export interface CreateRoleDto {
  name: string;
  description: string;
  permissionIds?: number[];
}

/**
 * DTO cho cập nhật vai trò
 */
export interface UpdateRoleDto {
  name?: string;
  description?: string;
  status?: RoleStatus;
  permissionIds?: number[];
}

/**
 * DTO cho gán quyền cho vai trò
 */
export interface AssignRolePermissionsDto {
  permissionIds: number[];
}

/**
 * DTO cho query vai trò
 */
export interface RoleQueryDto extends QueryDto {
  status?: RoleStatus | undefined;
  isSystem?: boolean | undefined;
}

/**
 * Kiểu dữ liệu cho kết quả phân trang vai trò
 */
export type PaginatedRoleResult = PaginatedResult<RoleDto>;

/**
 * Kiểu dữ liệu cho phản hồi API vai trò
 */
export type RoleApiResponse<T> = ApiResponseDto<T>;

/**
 * Nhóm quyền
 */
export enum PermissionGroup {
  USER = 'user',
  EMPLOYEE = 'employee',
  ROLE = 'role',
  SYSTEM = 'system',
  REPORT = 'report',
  SETTING = 'setting',
}
