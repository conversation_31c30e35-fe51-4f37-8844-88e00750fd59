import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { ApiResponse } from '@/shared/hooks/useApiForm';

// Định nghĩa kiểu dữ liệu cho lỗi API
interface ApiErrorResponse {
  success: false;
  message: string;
  errors?: Record<string, string> | undefined;
}

/**
 * Chuyển đổi từ ApiResponseDto sang ApiResponse
 * @param response Phản hồi từ API
 * @returns Phản hồi đã chuyển đổi
 */
export function adaptApiResponse<T>(response: ApiResponseDto<T>): ApiResponse<T> {
  return {
    success: response.code >= 200 && response.code < 300,
    data: response.result,
    message: response.message,
  };
}

/**
 * Wrapper cho hàm gọi API để chuyển đổi kiểu dữ liệu
 * @param apiCall Hàm gọi API
 * @returns Hàm đã được wrap
 */
export function adaptApiCall<TParams, TResult>(
  apiCall: (params: TParams) => Promise<ApiResponseDto<TResult>>
): (params: TParams) => Promise<ApiResponse<TResult>> {
  return async (params: TParams) => {
    try {
      const response = await apiCall(params);
      return adaptApiResponse(response);
    } catch (error) {
      // Xử lý lỗi từ API
      const err = error as {
        response?: { data?: { message?: string; errors?: Record<string, string> } };
        message?: string;
      };

      if (err.response?.data) {
        const errorResponse: ApiErrorResponse = {
          success: false,
          message: err.response.data.message || 'Đã xảy ra lỗi',
          errors: err.response.data.errors,
        };
        return errorResponse as ApiResponse<TResult>;
      }

      // Xử lý lỗi khác
      const errorResponse: ApiErrorResponse = {
        success: false,
        message: err.message || 'Đã xảy ra lỗi không xác định',
      };
      return errorResponse as ApiResponse<TResult>;
    }
  };
}
