import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EmailServerService } from '../services';
import {
  EmailServerQueryParams,
  CreateEmailServerDto,
  UpdateEmailServerDto,
  TestEmailServerDto,
} from '../types';

/**
 * Query Keys
 */
export const emailServerQueryKeys = {
  all: ['admin', 'integration', 'email-servers'] as const,
  lists: () => [...emailServerQueryKeys.all, 'list'] as const,
  list: (params?: EmailServerQueryParams) => [...emailServerQueryKeys.lists(), params] as const,
  details: () => [...emailServerQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...emailServerQueryKeys.details(), id] as const,
};

/**
 * Hook để lấy danh sách email servers
 */
export const useEmailServers = (params?: EmailServerQueryParams) => {
  return useQuery({
    queryKey: emailServerQueryKeys.list(params),
    queryFn: () => EmailServerService.getEmailServers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết email server
 */
export const useEmailServer = (id: number) => {
  return useQuery({
    queryKey: emailServerQueryKeys.detail(id),
    queryFn: () => EmailServerService.getEmailServer(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo email server
 */
export const useCreateEmailServer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmailServerDto) => EmailServerService.createEmailServer(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: emailServerQueryKeys.lists() });
    },
  });
};

/**
 * Hook để cập nhật email server
 */
export const useUpdateEmailServer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateEmailServerDto }) =>
      EmailServerService.updateEmailServer(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: emailServerQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: emailServerQueryKeys.detail(id) });
    },
  });
};

/**
 * Hook để xóa email server
 */
export const useDeleteEmailServer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => EmailServerService.deleteEmailServer(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: emailServerQueryKeys.lists() });
    },
  });
};

/**
 * Hook để test kết nối email server
 */
export const useTestEmailServer = () => {
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: TestEmailServerDto }) =>
      EmailServerService.testEmailServerConnection(id, data),
  });
};
