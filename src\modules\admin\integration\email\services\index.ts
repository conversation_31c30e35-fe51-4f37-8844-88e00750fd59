import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  EmailServerConfiguration,
  CreateEmailServerDto,
  UpdateEmailServerDto,
  TestEmailServerDto,
  EmailServerQueryParams,
  EmailServerTestResult,
} from '../types';

/**
 * Email Server Configuration Service
 */
export class EmailServerService {
  private static readonly BASE_URL = '/admin/integration/email-server';

  /**
   * L<PERSON>y danh sách cấu hình máy chủ email
   */
  static async getEmailServers(
    params?: EmailServerQueryParams
  ): Promise<ApiResponseDto<PaginatedResult<EmailServerConfiguration>>> {
    return apiClient.get(this.BASE_URL, { params });
  }

  /**
   * L<PERSON>y thông tin chi tiết cấu hình máy chủ email
   */
  static async getEmailServer(id: number): Promise<ApiResponseDto<EmailServerConfiguration>> {
    return apiClient.get(`${this.BASE_URL}/${id}`);
  }

  /**
   * Tạo mới cấu hình máy chủ email
   */
  static async createEmailServer(
    data: CreateEmailServerDto
  ): Promise<ApiResponseDto<EmailServerConfiguration>> {
    return apiClient.post(this.BASE_URL, data);
  }

  /**
   * Cập nhật cấu hình máy chủ email
   */
  static async updateEmailServer(
    id: number,
    data: UpdateEmailServerDto
  ): Promise<ApiResponseDto<EmailServerConfiguration>> {
    return apiClient.put(`${this.BASE_URL}/${id}`, data);
  }

  /**
   * Xóa cấu hình máy chủ email
   */
  static async deleteEmailServer(id: number): Promise<ApiResponseDto<{ message: string }>> {
    return apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Kiểm tra kết nối máy chủ email
   */
  static async testEmailServerConnection(
    id: number,
    data: TestEmailServerDto
  ): Promise<ApiResponseDto<EmailServerTestResult>> {
    return apiClient.post(`${this.BASE_URL}/${id}/test`, data);
  }
}
