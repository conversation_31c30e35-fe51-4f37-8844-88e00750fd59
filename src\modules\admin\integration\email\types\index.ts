/**
 * Email Server Configuration Types
 */

/**
 * Type for additional email server settings
 */
export type EmailServerAdditionalSettings = Record<string, string | number | boolean>;

export interface EmailServerConfiguration {
  id: number;
  userId: number;
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls: boolean;
  isActive: boolean;
  additionalSettings?: EmailServerAdditionalSettings;
  createdAt: string; // Backend trả về string timestamp để khớp với integration version
  updatedAt: string; // Backend trả về string timestamp để khớp với integration version
}

export interface CreateEmailServerDto {
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls: boolean;
  isActive: boolean;
  additionalSettings?: EmailServerAdditionalSettings;
}

export interface UpdateEmailServerDto {
  serverName?: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  useSsl?: boolean;
  useStartTls?: boolean;
  isActive?: boolean;
  additionalSettings?: EmailServerAdditionalSettings;
}

export interface TestEmailServerDto {
  recipientEmail?: string;
  subject?: string;
}

export interface EmailServerQueryParams {
  page?: number;
  limit?: number;
  search?: string | null | undefined;
  userId?: number; // Giữ optional cho query params
}

export interface EmailServerTestResult {
  success: boolean;
  message: string;
  details?: unknown;
}

export interface EmailServerFormData extends Omit<CreateEmailServerDto, 'additionalSettings'> {
  additionalSettings?: string; // JSON string for form handling
}
