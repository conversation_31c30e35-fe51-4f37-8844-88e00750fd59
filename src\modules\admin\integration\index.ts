// Export email integration components
export { default as EmailServerForm } from '../../integration/components/EmailServerForm';

// Export email integration hooks
export * from './email/hooks';

// Export email integration services
export * from './email/services';

// Export email integration types
export * from './email/types';

// Export email integration schemas
export * from './email/schemas';

// Export pages
export { default as AdminIntegrationManagementPage } from './pages/AdminIntegrationManagementPage';
export { default as EmailServerManagementPage } from './pages/EmailServerManagementPage';

// Export routers
export { default as adminIntegrationRoutes } from './routers/adminIntegrationRoutes';

// Export locales
export { default as adminIntegrationResources } from './locales';
