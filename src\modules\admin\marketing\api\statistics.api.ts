import { mockCustomFields } from './custom-field.api';
import { MarketingOverviewStatistics } from '../types/statistics.types';

/**
 * <PERSON><PERSON><PERSON> lấy thống kê tổng quan marketing
 */
export const getMarketingOverviewStatistics = (): MarketingOverviewStatistics => {
  // Lấy số lượng trường tùy chỉnh từ mockCustomFields
  const customFieldsCount = mockCustomFields.length;

  return {
    totalAudiences: 1250,
    totalSegments: 15,
    totalCampaigns: 24,
    activeCampaigns: 8,
    totalContacts: 5680,
    tagsCount: 32,
    contactsGrowth: 12.5,
    customFieldsCount: customFieldsCount, // Sử dụng số lượng thực tế từ mockCustomFields
    campaignPerformance: {
      sent: 12500,
      delivered: 12100,
      opened: 8470,
      clicked: 3025,
      converted: 1210,
    },
    recentCampaigns: [
      {
        id: 1,
        name: '<PERSON><PERSON><PERSON> d<PERSON>ch mùa hè 2024',
        type: 'email',
        status: 'active',
        sentCount: 2500,
        openRate: 68.4,
        clickRate: 24.2,
        date: '2024-06-15',
      },
      {
        id: 2,
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON> mãi tháng 6',
        type: 'sms',
        status: 'active',
        sentCount: 1800,
        openRate: 0,
        clickRate: 0,
        date: '2024-06-10',
      },
      {
        id: 3,
        name: 'Thông báo sản phẩm mới',
        type: 'email',
        status: 'completed',
        sentCount: 3200,
        openRate: 72.1,
        clickRate: 31.5,
        date: '2024-06-05',
      },
    ],
  };
};
