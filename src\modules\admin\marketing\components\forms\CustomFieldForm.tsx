import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Typography,
} from '@/shared/components/common';
import { z } from 'zod';
import { TFunction } from 'i18next';

// Import enum từ types
import { CustomFieldType } from '../../types/custom-field.types';

// Schema for the form
const getFormSchema = (t: TFunction) =>
  z.object({
    fieldKey: z
      .string()
      .min(1, t('marketingAdmin:customFields.form.validation.fieldKeyRequired'))
      .regex(/^[a-z0-9_]+$/, t('marketingAdmin:customFields.form.validation.fieldKeyFormat')),
    displayName: z
      .string()
      .min(1, t('marketingAdmin:customFields.form.validation.displayNameRequired')),
    dataType: z.union([
      z.nativeEnum(CustomFieldType),
      z.literal('integer')
    ], {
      errorMap: () => ({
        message: t('marketingAdmin:customFields.form.validation.dataTypeRequired'),
      }),
    }),
    description: z.string().optional(),
  });

export type CustomFieldFormValues = z.infer<ReturnType<typeof getFormSchema>>;

interface CustomFieldFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component for adding/editing custom fields
 */
const CustomFieldForm: React.FC<CustomFieldFormProps> = ({ onSubmit, onCancel }) => {
  const { t } = useTranslation(['marketingAdmin', 'common']);

  const formSchema = getFormSchema(t);

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {t('marketingAdmin:customFields.addNew')}
      </Typography>

      <Form schema={formSchema} onSubmit={onSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="fieldKey" label={t('marketingAdmin:customFields.form.fieldKeyLabel')} required>
            <Input
              placeholder={t('marketingAdmin:customFields.form.fieldKeyPlaceholder')}
              fullWidth
              helperText={t('marketingAdmin:customFields.form.fieldKeyHelper')}
            />
          </FormItem>

          <FormItem name="displayName" label={t('marketingAdmin:customFields.form.displayNameLabel')} required>
            <Input
              placeholder={t('marketingAdmin:customFields.form.displayNamePlaceholder')}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="dataType" label={t('marketingAdmin:customFields.form.dataTypeLabel')} required>
          <Select
            options={[
              { value: CustomFieldType.TEXT, label: t('marketingAdmin:customFields.types.text') },
              { value: CustomFieldType.NUMBER, label: t('marketingAdmin:customFields.types.number') },
              { value: CustomFieldType.DATE, label: t('marketingAdmin:customFields.types.date') },
              { value: CustomFieldType.BOOLEAN, label: t('marketingAdmin:customFields.types.boolean') },
              // Add integer type for API compatibility
              { value: 'integer', label: t('marketingAdmin:customFields.types.integer') },
            ]}
            placeholder={t('marketingAdmin:customFields.form.dataTypePlaceholder')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('marketingAdmin:customFields.form.descriptionLabel')}>
          <Input
            placeholder={t('marketingAdmin:customFields.form.descriptionPlaceholder')}
            fullWidth
          />
        </FormItem>



        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel')}
          </Button>
          <Button type="submit" variant="primary">
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CustomFieldForm;
