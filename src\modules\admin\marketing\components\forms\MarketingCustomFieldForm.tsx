import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Card,
  Textarea,
  ConditionalField,
  Checkbox,
  IconCard,
  Typography,
  TagsInput,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { z } from 'zod';
import {
  MarketingCustomFieldDataType,
  CreateMarketingCustomFieldRequest,
  UpdateMarketingCustomFieldRequest,
  MarketingCustomFieldResponse,
  MarketingSelectOption,
} from '../../types/custom-field.types';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

interface MarketingCustomFieldFormProps {
  onSubmit: (data: CreateMarketingCustomFieldRequest | UpdateMarketingCustomFieldRequest) => Promise<void>;
  onCancel: () => void;
  initialData?: MarketingCustomFieldResponse | undefined;
  isLoading?: boolean;
}

// Schema validation cho form
const getFormSchema = (t: (key: string) => string) =>
  z.object({
    fieldKey: z
      .string()
      .min(1, t('marketingAdmin:customField.form.validation.fieldKeyRequired'))
      .regex(/^[a-zA-Z0-9_-]+$/, t('marketingAdmin:customField.form.validation.fieldKeyPattern')),
    displayName: z
      .string()
      .min(1, t('marketingAdmin:customField.form.validation.displayNameRequired')),
    dataType: z.nativeEnum(MarketingCustomFieldDataType, {
      errorMap: () => ({
        message: t('marketingAdmin:customField.form.validation.dataTypeRequired'),
      }),
    }),
    description: z.string().optional(),
    tags: z.array(z.string()).optional(),
    // Config fields
    placeholder: z.string().optional(),
    defaultValue: z.string().optional(),
    pattern: z.string().optional(),
    minLength: z.string().optional(),
    maxLength: z.string().optional(),
    minValue: z.string().optional(),
    maxValue: z.string().optional(),
    options: z.string().optional(),
  });

type FormValues = z.infer<ReturnType<typeof getFormSchema>>;

/**
 * Form tạo và chỉnh sửa trường tùy chỉnh Marketing Admin - hoàn toàn độc lập
 */
const MarketingCustomFieldForm: React.FC<MarketingCustomFieldFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isLoading = false,
}) => {
  const { t } = useTranslation(['marketingAdmin', 'common']);
  const { formRef, setFormErrors } = useFormErrors();
  
  // State cho advanced settings
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  const formSchema = useMemo(() => getFormSchema(t), [t]);

  // Chuẩn bị giá trị mặc định
  const defaultValues: FormValues = useMemo(() => {
    if (!initialData) {
      return {
        fieldKey: '',
        displayName: '',
        dataType: MarketingCustomFieldDataType.TEXT,
        description: '',
        tags: [],
        placeholder: '',
        defaultValue: '',
        pattern: '',
        minLength: '',
        maxLength: '',
        minValue: '',
        maxValue: '',
        options: '',
      };
    }

    // Parse tags array
    const tagsArray = Array.isArray(initialData.tags) ? initialData.tags : [];

    // Parse options cho select
    let optionsString = '';
    if (initialData.config?.['options'] && Array.isArray(initialData.config['options'])) {
      optionsString = (initialData.config['options'] as MarketingSelectOption[])
        .map((opt: MarketingSelectOption) => `${opt.title}:${opt.value}`)
        .join('\n');
    }

    return {
      fieldKey: initialData.fieldKey || '',
      displayName: initialData.displayName || '',
      dataType: initialData.dataType as MarketingCustomFieldDataType || MarketingCustomFieldDataType.TEXT,
      description: initialData.description || '',
      tags: tagsArray,
      placeholder: (initialData.config?.['placeholder'] as string) || '',
      defaultValue: String(initialData.config?.['defaultValue'] || ''),
      pattern: (initialData.config?.['pattern'] as string) || '',
      minLength: initialData.config?.['minLength'] ? String(initialData.config['minLength']) : '',
      maxLength: initialData.config?.['maxLength'] ? String(initialData.config['maxLength']) : '',
      minValue: initialData.config?.['minValue'] ? String(initialData.config['minValue']) : '',
      maxValue: initialData.config?.['maxValue'] ? String(initialData.config['maxValue']) : '',
      options: optionsString,
    };
  }, [initialData]);

  // Xử lý submit form
  const handleSubmit = async (values: unknown) => {
    const formValues = values as FormValues;
    try {
      // Parse tags array
      const tags = formValues.tags ? formValues.tags.filter((tag: string) => tag.trim().length > 0) : [];

      // Chuẩn bị config object
      const config: Record<string, unknown> = {};

      // Thêm placeholder
      if (formValues.placeholder) {
        config['placeholder'] = formValues.placeholder;
      }

      // Xử lý defaultValue theo dataType
      if (formValues.defaultValue) {
        switch (formValues.dataType) {
          case MarketingCustomFieldDataType.NUMBER:
            config['defaultValue'] = Number(formValues.defaultValue);
            break;
          case MarketingCustomFieldDataType.BOOLEAN:
            config['defaultValue'] = formValues.defaultValue === 'true';
            break;
          default:
            config['defaultValue'] = formValues.defaultValue;
        }
      }

      // Xử lý config theo dataType
      switch (formValues.dataType) {
        case MarketingCustomFieldDataType.TEXT:
          if (formValues.pattern) config['pattern'] = formValues.pattern;
          if (formValues.minLength) config['minLength'] = Number(formValues.minLength);
          if (formValues.maxLength) config['maxLength'] = Number(formValues.maxLength);
          break;

        case MarketingCustomFieldDataType.NUMBER:
          if (formValues.minValue) config['minValue'] = Number(formValues.minValue);
          if (formValues.maxValue) config['maxValue'] = Number(formValues.maxValue);
          break;

        case MarketingCustomFieldDataType.SELECT:
          if (formValues.options) {
            const options = formValues.options
              .split('\n')
              .map((line: string) => line.trim())
              .filter((line: string) => line.length > 0)
              .map((line: string) => {
                const [title, value] = line.split(':').map((s: string) => s.trim());
                return {
                  title: title || value,
                  value: value || title,
                };
              });
            config['options'] = options;
          }
          break;
      }

      // Chuẩn bị data để submit
      const submitData = {
        fieldKey: formValues.fieldKey,
        displayName: formValues.displayName,
        dataType: formValues.dataType,
        ...(formValues.description && { description: formValues.description }),
        ...(tags.length > 0 && { tags }),
        ...(Object.keys(config).length > 0 && { config }),
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting form:', error);
      // Handle validation errors if needed
      if (error && typeof error === 'object' && 'errors' in error) {
        const errorObj = error as { errors: Record<string, string> };
        setFormErrors(errorObj.errors);
      }
    }
  };

  return (
      <Card>
        <div>
          <Typography variant="h2" className="mb-6">
            {initialData ? t('marketingAdmin:customField.edit') : t('marketingAdmin:customField.add')}
          </Typography>

          <Form
            ref={formRef}
            schema={formSchema}
            onSubmit={handleSubmit}
            defaultValues={defaultValues}
          >
            {/* Thông tin cơ bản */}
            <div>
              <FormItem
                name="dataType"
                label={t('marketingAdmin:customField.dataType')}
                required
              >
                <Select
                  fullWidth
                  options={[
                    { value: MarketingCustomFieldDataType.TEXT, label: t('marketingAdmin:customField.dataTypes.text') },
                    { value: MarketingCustomFieldDataType.NUMBER, label: t('marketingAdmin:customField.dataTypes.number') },
                    { value: MarketingCustomFieldDataType.BOOLEAN, label: t('marketingAdmin:customField.dataTypes.boolean') },
                    { value: MarketingCustomFieldDataType.DATE, label: t('marketingAdmin:customField.dataTypes.date') },
                    { value: MarketingCustomFieldDataType.SELECT, label: t('marketingAdmin:customField.dataTypes.select') },
                    { value: MarketingCustomFieldDataType.OBJECT, label: t('marketingAdmin:customField.dataTypes.object') },
                  ]}
                />
              </FormItem>

              <FormItem
                name="fieldKey"
                label={t('marketingAdmin:customField.form.fieldKey')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('marketingAdmin:customField.form.fieldKeyPlaceholder')}
                  disabled={!!initialData} // Không cho phép sửa fieldKey khi edit
                />
              </FormItem>

              <FormItem
                name="displayName"
                label={t('marketingAdmin:customField.form.displayName')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('marketingAdmin:customField.form.displayNamePlaceholder')}
                />
              </FormItem>

              {/* Tùy chọn cho SELECT - hiển thị ngay sau tên hiển thị */}
              <ConditionalField
                condition={{
                  field: 'dataType',
                  type: ConditionType.EQUALS,
                  value: MarketingCustomFieldDataType.SELECT,
                }}
              >
                <FormItem
                  name="options"
                  label={t('marketingAdmin:customField.form.options')}
                  required
                >
                  <Textarea
                    fullWidth
                    rows={6}
                    placeholder={t('marketingAdmin:customField.form.selectOptionsPlaceholder')}
                  />
                </FormItem>
              </ConditionalField>

              <FormItem
                name="description"
                label={t('marketingAdmin:customField.form.description')}
              >
                <Textarea
                  fullWidth
                  rows={3}
                  placeholder={t('marketingAdmin:customField.form.descriptionPlaceholder')}
                />
              </FormItem>

              <FormItem
                name="tags"
                label={t('marketingAdmin:customField.form.tags')}
              >
                <TagsInput
                  fieldName="tags"
                  formRef={formRef}
                  placeholder={t('marketingAdmin:customField.form.tagsPlaceholder')}
                  initialValue={defaultValues.tags}
                />
              </FormItem>
            </div>

            {/* Checkbox để hiện/ẩn cài đặt nâng cao */}
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={showAdvancedSettings}
                onChange={setShowAdvancedSettings}
                label={t('marketingAdmin:customField.form.showAdvancedSettings')}
              />
            </div>

            {/* Cài đặt nâng cao */}
            {showAdvancedSettings && (
              <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <FormItem
                  name="placeholder"
                  label={t('marketingAdmin:customField.form.placeholder')}
                >
                  <Input
                    fullWidth
                    placeholder={t('marketingAdmin:customField.form.placeholderPlaceholder')}
                  />
                </FormItem>

                <FormItem
                  name="defaultValue"
                  label={t('marketingAdmin:customField.form.defaultValue')}
                >
                  <Input
                    fullWidth
                    placeholder={t('marketingAdmin:customField.form.defaultValuePlaceholder')}
                  />
                </FormItem>

                {/* Config cho TEXT */}
                <ConditionalField
                  condition={{
                    field: 'dataType',
                    type: ConditionType.EQUALS,
                    value: MarketingCustomFieldDataType.TEXT,
                  }}
                >
                  <div className="space-y-4">
                    <FormItem
                      name="pattern"
                      label={t('marketingAdmin:customField.form.pattern')}
                    >
                      <Input
                        fullWidth
                        placeholder="^[A-Za-z0-9]+$"
                      />
                    </FormItem>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem
                        name="minLength"
                        label={t('marketingAdmin:customField.form.minLength')}
                      >
                        <Input fullWidth type="number" min="0" placeholder="0" />
                      </FormItem>
                      <FormItem
                        name="maxLength"
                        label={t('marketingAdmin:customField.form.maxLength')}
                      >
                        <Input fullWidth type="number" min="1" placeholder="255" />
                      </FormItem>
                    </div>
                  </div>
                </ConditionalField>

                {/* Config cho NUMBER */}
                <ConditionalField
                  condition={{
                    field: 'dataType',
                    type: ConditionType.EQUALS,
                    value: MarketingCustomFieldDataType.NUMBER,
                  }}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem
                      name="minValue"
                      label={t('marketingAdmin:customField.form.minValue')}
                    >
                      <Input fullWidth type="number" placeholder="0" />
                    </FormItem>
                    <FormItem
                      name="maxValue"
                      label={t('marketingAdmin:customField.form.maxValue')}
                    >
                      <Input fullWidth type="number" placeholder="100" />
                    </FormItem>
                  </div>
                </ConditionalField>
              </div>
            )}

            {/* Action buttons */}
            <div className="flex justify-end space-x-2 pt-4">
              <IconCard
                icon="x"
                variant="secondary"
                title={t('common:cancel')}
                onClick={onCancel}
                disabled={isLoading}
              />
              <IconCard
                icon="save"
                variant="primary"
                title={t('common:save')}
                onClick={() => formRef.current?.submit()}
                disabled={isLoading}
                isLoading={isLoading}
              />
            </div>
          </Form>
        </div>
      </Card>
  );
};

export default MarketingCustomFieldForm;
