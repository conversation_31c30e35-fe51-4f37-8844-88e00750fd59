import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AudienceService } from '../services/audience.service';
import {
  AudienceFilterParams,
  AudienceFormData,
  AudienceListData,
  Audience,
} from '../types/audience.types';

// Key cho React Query
const AUDIENCE_QUERY_KEY = 'admin-audiences';

/**
 * Hook để lấy danh sách audience
 * @param params Tham số filter
 * @returns Query object với danh sách audience
 */
export const useAudiences = (params?: AudienceFilterParams) => {
  return useQuery({
    queryKey: [AUDIENCE_QUERY_KEY, params],
    queryFn: () => AudienceService.getAudiences(params),
    select: data => data.result as AudienceListData,
  });
};

/**
 * Hook để lấy chi tiết audience
 * @param id ID của audience
 * @returns Query object với chi tiết audience
 */
export const useAudience = (id: string) => {
  return useQuery({
    queryKey: [AUDIENCE_QUERY_KEY, id],
    queryFn: () => AudienceService.getAudience(id),
    select: data => data.result as Audience,
    enabled: !!id,
  });
};

/**
 * Hook để tạo audience mới
 * @returns Mutation object để tạo audience
 */
export const useCreateAudience = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AudienceFormData) => AudienceService.createAudience(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật audience
 * @returns Mutation object để cập nhật audience
 */
export const useUpdateAudience = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: AudienceFormData }) =>
      AudienceService.updateAudience(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa audience
 * @returns Mutation object để xóa audience
 */
export const useDeleteAudience = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => AudienceService.deleteAudience(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY] });
    },
  });
};

/**
 * Hook để xóa nhiều audience
 * @returns Mutation object để xóa nhiều audience
 */
export const useDeleteMultipleAudiences = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => AudienceService.deleteMultipleAudiences(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY] });
    },
  });
};
