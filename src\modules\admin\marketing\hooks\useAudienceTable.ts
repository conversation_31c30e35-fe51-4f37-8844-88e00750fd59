import { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  Audience,
  AudienceFilterParams,
  AudienceStatus,
  AudienceType,
} from '../types/audience.types';
import { useAudiences } from './useAudience';

/**
 * Hook để quản lý bảng dữ liệu audience
 * @returns Các hàm và state để quản lý bảng dữ liệu
 */
export const useAudienceTable = () => {
  const { t } = useTranslation();
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [filter, setFilter] = useState<AudienceFilterParams>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortDirection: 'DESC',
  });

  // Lấy danh sách audience
  const { data, isLoading, isError } = useAudiences(filter);

  // <PERSON><PERSON> lý thay đổi trang
  const handlePageChange = useCallback((page: number) => {
    setFilter(prev => ({ ...prev, page }));
  }, []);

  // X<PERSON> lý thay đổi số lượng hiển thị
  const handleLimitChange = useCallback((limit: number) => {
    setFilter(prev => ({ ...prev, page: 1, limit }));
  }, []);

  // Xử lý tìm kiếm
  const handleSearch = useCallback((search: string) => {
    setFilter(prev => ({ ...prev, page: 1, search }));
  }, []);

  // Xử lý sắp xếp
  const handleSort = useCallback((sortBy: string, sortDirection: 'ASC' | 'DESC') => {
    setFilter(prev => ({ ...prev, sortBy, sortDirection }));
  }, []);

  // Xử lý lọc theo trạng thái
  const handleStatusFilter = useCallback((status: AudienceStatus | string) => {
    setFilter(prev => ({ ...prev, page: 1, status }));
  }, []);

  // Xử lý lọc theo loại
  const handleTypeFilter = useCallback((type: AudienceType | string) => {
    setFilter(prev => ({ ...prev, page: 1, type }));
  }, []);

  // Xử lý chọn hàng
  const handleRowSelect = useCallback((id: string, selected: boolean) => {
    setSelectedIds(prev => {
      if (selected) {
        return [...prev, id];
      } else {
        return prev.filter(itemId => itemId !== id);
      }
    });
  }, []);

  // Xử lý chọn tất cả
  const handleSelectAll = useCallback(
    (selected: boolean) => {
      if (selected && data?.items) {
        setSelectedIds(data.items.map(item => item.id));
      } else {
        setSelectedIds([]);
      }
    },
    [data?.items]
  );

  // Định nghĩa cột
  const columns = useMemo<TableColumn<Audience>[]>(
    () => [
      {
        key: 'name',
        title: t('audience.name', 'Tên đối tượng'),
        dataIndex: 'name',
        render: (value: unknown) => value as string,
        sortable: true,
      },
      {
        key: 'type',
        title: t('audience.type', 'Loại'),
        dataIndex: 'type',
        render: (value: unknown) => {
          const type = value as AudienceType;
          switch (type) {
            case AudienceType.CUSTOMER:
              return t('audience.types.customer', 'Khách hàng');
            case AudienceType.LEAD:
              return t('audience.types.lead', 'Tiềm năng');
            case AudienceType.SUBSCRIBER:
              return t('audience.types.subscriber', 'Người đăng ký');
            case AudienceType.CUSTOM:
              return t('audience.types.custom', 'Tùy chỉnh');
            default:
              return type;
          }
        },
        sortable: true,
      },
      {
        key: 'status',
        title: t('audience.status', 'Trạng thái'),
        dataIndex: 'status',
        render: (value: unknown) => {
          const status = value as AudienceStatus;
          switch (status) {
            case AudienceStatus.ACTIVE:
              return t('audience.statuses.active', 'Hoạt động');
            case AudienceStatus.INACTIVE:
              return t('audience.statuses.inactive', 'Không hoạt động');
            case AudienceStatus.DRAFT:
              return t('audience.statuses.draft', 'Bản nháp');
            default:
              return status;
          }
        },
        sortable: true,
      },
      {
        key: 'totalContacts',
        title: t('audience.totalContacts', 'Số liên hệ'),
        dataIndex: 'totalContacts',
        render: (value: unknown) => value as number,
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('common.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        render: (value: unknown) => new Date(value as string).toLocaleDateString('vi-VN'),
        sortable: true,
      },
    ],
    [t]
  );

  return {
    data,
    isLoading,
    isError,
    columns,
    filter,
    selectedIds,
    handlePageChange,
    handleLimitChange,
    handleSearch,
    handleSort,
    handleStatusFilter,
    handleTypeFilter,
    handleRowSelect,
    handleSelectAll,
    setFilter,
    setSelectedIds,
  };
};
