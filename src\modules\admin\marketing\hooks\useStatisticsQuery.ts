/**
 * Hooks for marketing statistics API using TanStack Query
 */

import { useQuery } from '@tanstack/react-query';
import { MarketingStatisticsQueryParams } from '../types/statistics.types';
import { MarketingStatisticsService } from '../services/statistics.service';

/**
 * Query keys for marketing statistics API
 */
export const STATISTICS_QUERY_KEYS = {
  all: ['marketing', 'statistics'] as const,
  overview: (params?: MarketingStatisticsQueryParams) =>
    [...STATISTICS_QUERY_KEYS.all, 'overview', params] as const,
  audienceGrowth: (params?: MarketingStatisticsQueryParams) =>
    [...STATISTICS_QUERY_KEYS.all, 'audience-growth', params] as const,
  campaignPerformance: (params?: MarketingStatisticsQueryParams) =>
    [...STATISTICS_QUERY_KEYS.all, 'campaign-performance', params] as const,
  segmentDistribution: () => [...STATISTICS_QUERY_KEYS.all, 'segment-distribution'] as const,
};

/**
 * Hook to get overview statistics
 */
export const useOverviewStatistics = (params?: MarketingStatisticsQueryParams) => {
  return useQuery({
    queryKey: STATISTICS_QUERY_KEYS.overview(params),
    queryFn: () => MarketingStatisticsService.getOverviewStatistics(params),
    select: data => data.result,
  });
};

/**
 * Hook to get audience growth statistics
 */
export const useAudienceGrowthStatistics = (params?: MarketingStatisticsQueryParams) => {
  return useQuery({
    queryKey: STATISTICS_QUERY_KEYS.audienceGrowth(params),
    queryFn: () => MarketingStatisticsService.getAudienceGrowthStatistics(params),
    select: data => data.result,
  });
};

/**
 * Hook to get campaign performance statistics
 */
export const useCampaignPerformanceStatistics = (params?: MarketingStatisticsQueryParams) => {
  return useQuery({
    queryKey: STATISTICS_QUERY_KEYS.campaignPerformance(params),
    queryFn: () => MarketingStatisticsService.getCampaignPerformanceStatistics(params),
    select: data => data.result,
  });
};

/**
 * Hook to get segment distribution statistics
 */
export const useSegmentDistributionStatistics = () => {
  return useQuery({
    queryKey: STATISTICS_QUERY_KEYS.segmentDistribution(),
    queryFn: () => MarketingStatisticsService.getSegmentDistributionStatistics(),
    select: data => data.result,
  });
};
