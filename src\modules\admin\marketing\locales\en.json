{"marketingAdmin": {"title": "Marketing Management", "description": "Manage marketing campaigns and target audiences", "menu": {"marketing": "Marketing", "customFields": "Custom Fields"}, "common": {"all": "All", "active": "Active", "inactive": "Inactive", "draft": "Draft", "add": "Add New", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "search": "Search", "filter": "Filter", "actions": "Actions", "status": "Status", "name": "Name", "createdAt": "Created At", "updatedAt": "Updated At", "type": "Type", "id": "ID", "description": "Description"}, "customFields": {"title": "Custom Fields Management", "description": "Manage custom data fields", "totalFields": "Total Fields", "manage": "Manage Fields", "addNew": "Add New Custom Field", "edit": "Edit Custom Field", "name": "Field Name", "displayName": "Display Name", "fieldKey": "Field Key", "dataType": "Data Type", "type": "Field Type", "status": "Status", "isRequired": "Required", "options": "Options", "defaultValue": "Default Value", "confirmDelete": "Are you sure you want to delete this field?", "deleteConfirmation": "Are you sure you want to delete the custom field \"{{name}}\"?", "notFound": "Custom field not found", "createField": "Create New Field", "editField": "Edit Field", "deleteField": "Delete Field", "types": {"text": "Text (string)", "number": "Number", "integer": "Integer", "date": "Date", "boolean": "Boolean", "select": "Select", "checkbox": "Checkbox"}, "statuses": {"active": "Active", "inactive": "Inactive"}, "form": {"fieldKeyLabel": "Field Key", "fieldKeyPlaceholder": "Enter field key (e.g. customer_address)", "fieldKeyHelper": "Use only lowercase letters, numbers, and underscores, no spaces", "displayNameLabel": "Display Name", "displayNamePlaceholder": "Enter display name (e.g. Customer Address)", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter field description (e.g. Customer contact address)", "dataTypeLabel": "Data Type", "dataTypePlaceholder": "Select data type", "fieldKeyReadOnly": "Field key cannot be changed", "isRequiredLabel": "Required", "optionsLabel": "Options", "addOption": "Add Option", "optionValue": "Value", "optionValuePlaceholder": "Enter value", "optionLabel": "Label", "optionLabelPlaceholder": "Enter label", "defaultValueLabel": "Default Value", "defaultValuePlaceholder": "Enter default value", "validation": {"fieldKeyRequired": "Field key is required", "fieldKeyFormat": "Field key can only contain lowercase letters, numbers, and underscores, no spaces", "displayNameRequired": "Display name is required", "dataTypeRequired": "Data type is required", "optionValueRequired": "Option value is required", "optionLabelRequired": "Option label is required"}}, "messages": {"createSuccess": "Field created successfully", "updateSuccess": "Field updated successfully", "deleteSuccess": "Field deleted successfully", "createError": "Error creating field", "updateError": "Error updating field", "deleteError": "Error deleting field", "deleteConfirm": "Are you sure you want to delete this field?", "deleteConfirmTitle": "Confirm Delete"}}, "marketing": {"title": "Marketing Dashboard", "description": "Overview of marketing activities and performance", "metrics": {"campaigns": "Campaigns", "activeCampaigns": "Active Campaigns", "totalAudience": "Total Audience", "conversionRate": "Conversion Rate"}}}}