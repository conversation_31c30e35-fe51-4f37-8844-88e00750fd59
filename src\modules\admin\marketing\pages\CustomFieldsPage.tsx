import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, Tooltip, ConfirmDeleteModal } from '@/shared/components/common';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  useAdminMarketingCustomFields,
  useDeleteAdminMarketingCustomField,
  useDeleteMultipleAdminMarketingCustomFields,
  useCreateAdminMarketingCustomField,
  useUpdateAdminMarketingCustomField,
} from '../hooks/useAdminMarketingCustomFieldQuery';
import { MarketingCustomFieldQueryParams } from '../services/custom-field.service';
import {
  MarketingCustomFieldResponse,
  CreateMarketingCustomFieldRequest,
  UpdateMarketingCustomFieldRequest
} from '../types/custom-field.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import MarketingCustomFieldForm from '../components/forms/MarketingCustomFieldForm';

/**
 * Trang quản lý Admin Marketing Custom Fields - hoàn toàn độc lập
 */
const CustomFieldsPage: React.FC = () => {
  const { t } = useTranslation(['marketingAdmin', 'common']);

  // State cho form và selection
  const [selectedCustomField, setSelectedCustomField] = useState<MarketingCustomFieldResponse | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [customFieldToDelete, setCustomFieldToDelete] = useState<MarketingCustomFieldResponse | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);



  // Slide form hooks
  const {
    isVisible: isCreateVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  const {
    isVisible: isEditVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Mutations
  const createCustomFieldMutation = useCreateAdminMarketingCustomField();
  const updateCustomFieldMutation = useUpdateAdminMarketingCustomField();
  const deleteCustomFieldMutation = useDeleteAdminMarketingCustomField();
  const deleteMultipleCustomFieldsMutation = useDeleteMultipleAdminMarketingCustomFields();

  // Create query params function
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): MarketingCustomFieldQueryParams => {
      const queryParams: MarketingCustomFieldQueryParams = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection:
          params.sortDirection === SortDirection.ASC
            ? 'ASC'
            : params.sortDirection === SortDirection.DESC
              ? 'DESC'
              : undefined,
      };

      return queryParams;
    },
    []
  );

  const dataTableConfig = useDataTableConfig({
    columns: [
      {
        title: t('marketingAdmin:customField.form.fieldKeyLabel', 'Trường định danh'),
        dataIndex: 'fieldKey',
        key: 'fieldKey',
        sortable: true,
        width: 180,
        render: (fieldKey: unknown) => (
          <span className="font-mono text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
            {String(fieldKey)}
          </span>
        ),
      },
      {
        title: t('marketingAdmin:customField.form.displayNameLabel', 'Tên hiển thị'),
        dataIndex: 'displayName',
        key: 'displayName',
        sortable: true,
        width: 200,
      },
      {
        title: t('marketingAdmin:customField.dataType', 'Loại dữ liệu'),
        dataIndex: 'dataType',
        key: 'dataType',
        sortable: true,
        width: 120,
        render: (dataType: unknown) => {
          const typeStr = String(dataType || '');
          const typeMap: Record<string, string> = {
            'string': 'Text',
            'integer': 'Number',
            'boolean': 'Boolean',
            'date': 'Date',
            'select': 'Select',
            'object': 'Object',
          };
          return (
            <span className="inline-block px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded">
              {typeMap[typeStr] || typeStr}
            </span>
          );
        },
      },
      {
        title: t('marketingAdmin:customField.form.description', 'Mô tả'),
        dataIndex: 'description',
        key: 'description',
        width: 250,
        render: (description: unknown) => {
          const descStr = String(description || '');
          return (
            <Tooltip
              content={descStr || t('marketingAdmin:customField.noDescription', 'Không có mô tả')}
            >
              <span className="truncate max-w-xs block">{descStr || '-'}</span>
            </Tooltip>
          );
        },
      },
      {
        title: t('marketingAdmin:customField.form.tags', 'Tags'),
        dataIndex: 'tags',
        key: 'tags',
        width: 200,
        render: (tags: unknown) => {
          const tagArray = Array.isArray(tags) ? tags : [];
          if (tagArray.length === 0) {
            return <span className="text-gray-400"></span>;
          }
          return (
            <div className="flex flex-wrap gap-1">
              {tagArray.slice(0, 3).map((tag: string, index: number) => (
                <span
                  key={index}
                  className="inline-block px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded"
                >
                  {tag}
                </span>
              ))}
              {tagArray.length > 3 && (
                <span className="text-xs text-gray-500">+{tagArray.length - 3}</span>
              )}
            </div>
          );
        },
      },
      {
        title: t('common:actions', 'Thao tác'),
        key: 'actions',
        width: 100,
        render: (_: unknown, record: unknown) => {
          const customField = record as MarketingCustomFieldResponse;
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEditCustomField(customField),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleDeleteCustomField(customField),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm hành động')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    createQueryParams,
  });

  const dataTable = useDataTable(dataTableConfig);

  // Active filters
  const activeFilters = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Fetch data using dataTable query params
  const { data: customFieldsData, isLoading } = useAdminMarketingCustomFields(dataTable.queryParams);

  // Type guard để đảm bảo customFieldsData có đúng structure
  const tableData = customFieldsData as { items?: MarketingCustomFieldResponse[]; meta?: { currentPage: number; itemsPerPage: number; totalItems: number } } | undefined;

  // Handlers
  const handleCreateCustomField = useCallback(() => {
    setSelectedCustomField(null);
    showCreateForm();
  }, [showCreateForm]);

  const handleEditCustomField = useCallback(
    (customField: MarketingCustomFieldResponse) => {
      setSelectedCustomField(customField);
      showEditForm();
    },
    [showEditForm]
  );

  const handleDeleteCustomField = useCallback(
    (customField: MarketingCustomFieldResponse) => {
      setCustomFieldToDelete(customField);
      setShowDeleteConfirm(true);
    },
    []
  );

  const handleConfirmDelete = useCallback(async () => {
    if (!customFieldToDelete) return;

    try {
      await deleteCustomFieldMutation.mutateAsync(customFieldToDelete.id);
      setShowDeleteConfirm(false);
      setCustomFieldToDelete(null);
    } catch (error) {
      console.error('Error deleting custom field:', error);
    }
  }, [customFieldToDelete, deleteCustomFieldMutation]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setCustomFieldToDelete(null);
  }, []);

  // Bulk delete handlers
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) return;
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      const ids = selectedRowKeys.map(key => Number(key));
      await deleteMultipleCustomFieldsMutation.mutateAsync(ids);
      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('Error bulk deleting custom fields:', error);
    }
  }, [selectedRowKeys, deleteMultipleCustomFieldsMutation]);

  // Form handlers
  const handleCreateSubmit = useCallback(async (data: CreateMarketingCustomFieldRequest | UpdateMarketingCustomFieldRequest) => {
    try {
      await createCustomFieldMutation.mutateAsync(data as CreateMarketingCustomFieldRequest);
      hideCreateForm();
    } catch (error) {
      console.error('Error creating custom field:', error);
      throw error; // Re-throw để form xử lý
    }
  }, [createCustomFieldMutation, hideCreateForm]);

  const handleEditSubmit = useCallback(async (data: CreateMarketingCustomFieldRequest | UpdateMarketingCustomFieldRequest) => {
    if (!selectedCustomField) return;

    try {
      await updateCustomFieldMutation.mutateAsync({ id: selectedCustomField.id, data: data as UpdateMarketingCustomFieldRequest });
      hideEditForm();
      setSelectedCustomField(null);
    } catch (error) {
      console.error('Error updating custom field:', error);
      throw error; // Re-throw để form xử lý
    }
  }, [selectedCustomField, updateCustomFieldMutation, hideEditForm]);

  const handleCreateCancel = useCallback(() => {
    hideCreateForm();
  }, [hideCreateForm]);

  const handleEditCancel = useCallback(() => {
    hideEditForm();
    setSelectedCustomField(null);
  }, [hideEditForm]);

  // Menu actions
  const menuActions = useMemo(() => [
    {
      id: 'add',
      label: t('marketingAdmin:customField.add', 'Thêm trường tùy chỉnh'),
      icon: 'plus' as const,
      onClick: handleCreateCustomField,
    },
    ...(selectedRowKeys.length > 0
      ? [
          {
            id: 'bulk-delete',
            label: t('common:deleteSelected', 'Xóa đã chọn'),
            icon: 'trash' as const,
            onClick: handleShowBulkDeleteConfirm,
            variant: 'danger' as const,
          },
        ]
      : []),
  ], [handleCreateCustomField, selectedRowKeys.length, handleShowBulkDeleteConfirm, t]);

  return (
    <div className="w-full bg-background text-foreground">
      {/* Menu bar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={menuActions}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Active filters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={activeFilters.handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={activeFilters.getFilterLabel()}
        onClearFilter={activeFilters.handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={activeFilters.handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={activeFilters.handleClearSort}
        onClearAll={activeFilters.handleClearAll}
      />

      {/* Main table */}
      <Card>
        <Table<MarketingCustomFieldResponse>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={tableData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: tableData?.meta?.currentPage || 1,
            pageSize: tableData?.meta?.itemsPerPage || 10,
            total: tableData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Create form */}
      <SlideInForm isVisible={isCreateVisible}>
        <MarketingCustomFieldForm
          onSubmit={handleCreateSubmit}
          onCancel={handleCreateCancel}
          isLoading={createCustomFieldMutation.isPending}
        />
      </SlideInForm>

      {/* Edit form */}
      <SlideInForm isVisible={isEditVisible}>
        <MarketingCustomFieldForm
          onSubmit={handleEditSubmit}
          onCancel={handleEditCancel}
          initialData={selectedCustomField || undefined}
          isLoading={updateCustomFieldMutation.isPending}
        />
      </SlideInForm>

      {/* Delete confirmation modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('marketingAdmin:customField.deleteTitle', 'Xóa trường tùy chỉnh')}
        message={t('marketingAdmin:customField.deleteMessage', {
          name: customFieldToDelete?.displayName || '',
        })}
        isSubmitting={deleteCustomFieldMutation.isPending}
      />

      {/* Bulk delete confirmation modal */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('marketingAdmin:customField.bulkDeleteTitle', 'Xóa nhiều trường tùy chỉnh')}
        message={t('marketingAdmin:customField.bulkDeleteMessage', {
          count: selectedRowKeys.length,
        })}
        isSubmitting={deleteMultipleCustomFieldsMutation.isPending}
      />
    </div>
  );
};

export default CustomFieldsPage;
