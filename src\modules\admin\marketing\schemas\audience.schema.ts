import { z } from 'zod';

/**
 * Schema validation cho thuộc tính audience
 */
export const audienceAttributeSchema = z.object({
  name: z.string().min(1, 'Tên thuộc tính là bắt buộc'),
  value: z.string().min(1, 'Gi<PERSON> trị thuộc tính là bắt buộc'),
});

/**
 * Schema validation cho audience
 */
export const audienceSchema = z.object({
  name: z
    .string()
    .min(2, 'Tên phải có ít nhất 2 ký tự')
    .max(100, 'Tên không được vượt quá 100 ký tự'),
  email: z
    .string()
    .email('Email không hợp lệ')
    .min(1, 'Email là bắt buộc'),
  phone: z
    .string()
    .min(1, 'Số điện thoại là bắt buộc'),
  countryCode: z.string().optional(),
  tagIds: z.array(z.number()).optional(),
  attributes: z.array(audienceAttributeSchema).optional(),
});

/**
 * Schema validation cho filter audience
 */
export const audienceFilterSchema = z.object({
  page: z.number().optional(),
  limit: z.number().optional(),
  search: z.string().optional(),
  status: z.string().optional(),
  type: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
});

/**
 * Type cho form values của audience
 */
export type AudienceFormValues = z.infer<typeof audienceSchema>;

/**
 * Type cho filter values của audience
 */
export type AudienceFilterValues = z.infer<typeof audienceFilterSchema>;
