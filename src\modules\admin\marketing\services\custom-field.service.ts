/**
 * Service for audience custom field API
 */

import { apiClient } from '@/shared/api';
import {
  CreateCustomFieldRequest,
  CustomFieldDetailResponse,
  CustomFieldListResponse,
  CustomFieldQueryParams,
  UpdateCustomFieldRequest,
  CreateMarketingCustomFieldRequest,
  UpdateMarketingCustomFieldRequest,
  MarketingCustomFieldResponse,
} from '../types/custom-field.types';
import type { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Base URL for audience custom field API
 */
const BASE_URL = '/admin/marketing/audience-custom-fields';

/**
 * Custom field service
 */
export const CustomFieldService = {
  /**
   * Get custom fields with pagination and filtering
   */
  getCustomFields: async (params?: CustomFieldQueryParams): Promise<CustomFieldListResponse> => {
    try {
      // Chuẩn bị tham số truy vấn
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const queryParams: Record<string, any> = {
        page: params?.page || 1,
        limit: params?.limit || 10,
      };

      // Xử lý tham số tìm kiếm
      if (params?.search && params.search.trim()) {
        console.log('Search parameter detected:', params.search);
        queryParams['search'] = params.search.trim();
      }

      // Xử lý tham số sắp xếp
      if (params?.sortBy) {
        queryParams['sortBy'] = params.sortBy;
        if (params.sortDirection) {
          queryParams['sortDirection'] = params.sortDirection;
        }
      }

      // Log thông tin request
      console.log('Sending API request to:', BASE_URL);
      console.log('With params:', queryParams);

      // Gọi API
      const response = await apiClient.get<CustomFieldListResponse['result']>(BASE_URL, {
        params: queryParams,
        // Tăng timeout để tránh lỗi timeout
        timeout: 10000
      });

      console.log('API response received successfully');
      return response;
    } catch (error) {
      console.error('Error in getCustomFields:', error);
      throw error;
    }
  },

  /**
   * Get custom field by fieldKey
   */
  getCustomFieldById: async (fieldKey: string): Promise<CustomFieldDetailResponse> => {
    return apiClient.get<CustomFieldDetailResponse['result']>(`${BASE_URL}/${fieldKey}`);
  },

  /**
   * Create custom field
   */
  createCustomField: async (data: CreateCustomFieldRequest): Promise<CustomFieldDetailResponse> => {
    return apiClient.post<CustomFieldDetailResponse['result']>(BASE_URL, data);
  },

  /**
   * Update custom field
   */
  updateCustomField: async (
    fieldKey: string,
    data: UpdateCustomFieldRequest
  ): Promise<CustomFieldDetailResponse> => {
    return apiClient.put<CustomFieldDetailResponse['result']>(`${BASE_URL}/${fieldKey}`, data);
  },

  /**
   * Delete custom field
   */
  deleteCustomField: async (fieldKey: string): Promise<CustomFieldDetailResponse> => {
    return apiClient.delete<CustomFieldDetailResponse['result']>(`${BASE_URL}/${fieldKey}`);
  },
};

/**
 * Marketing Custom Field Query Parameters
 */
export interface MarketingCustomFieldQueryParams {
  page?: number | undefined;
  limit?: number | undefined;
  search?: string | undefined;
  sortBy?: string | undefined;
  sortDirection?: 'ASC' | 'DESC' | undefined;
}

/**
 * Base URL cho admin marketing custom field API
 */
const MARKETING_BASE_URL = '/admin/marketing/audience-custom-fields';

/**
 * Admin Marketing Custom Field Service - Layer 1: Raw API calls
 */
export const AdminMarketingCustomFieldService = {
  /**
   * Lấy danh sách trường tùy chỉnh với phân trang và lọc
   */
  getCustomFields: async (
    params?: MarketingCustomFieldQueryParams
  ): Promise<ApiResponseDto<PaginatedResult<MarketingCustomFieldResponse>>> => {
    try {
      const queryParams: Record<string, unknown> = {
        page: params?.page || 1,
        limit: params?.limit || 10,
      };

      if (params?.search && params.search.trim()) {
        queryParams['search'] = params.search.trim();
      }

      if (params?.sortBy) {
          queryParams['sortBy'] = params.sortBy;
        if (params.sortDirection) {
          queryParams['sortDirection'] = params.sortDirection;
        }
      }

      console.log('Admin Marketing Custom Fields API request:', MARKETING_BASE_URL, queryParams);

      const response = await apiClient.get<PaginatedResult<MarketingCustomFieldResponse>>(
        MARKETING_BASE_URL,
        { params: queryParams }
      );

      console.log('Admin Marketing Custom Fields API response:', response);
      return response;
    } catch (error) {
      console.error('Error in AdminMarketingCustomFieldService.getCustomFields:', error);
      throw error;
    }
  },

  /**
   * Lấy chi tiết trường tùy chỉnh theo ID
   */
  getCustomFieldById: async (
    id: number
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse>> => {
    try {
      const response = await apiClient.get<MarketingCustomFieldResponse>(
        `${MARKETING_BASE_URL}/${id}`
      );
      console.log('Get admin marketing custom field by ID response:', response);
      return response;
    } catch (error) {
      console.error('Error in AdminMarketingCustomFieldService.getCustomFieldById:', error);
      throw error;
    }
  },

  /**
   * Tạo trường tùy chỉnh mới
   */
  createCustomField: async (
    data: CreateMarketingCustomFieldRequest
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse>> => {
    try {
      console.log('Creating admin marketing custom field with data:', data);

      const response = await apiClient.post<MarketingCustomFieldResponse>(
        MARKETING_BASE_URL,
        data
      );

      console.log('Create admin marketing custom field response:', response);
      return response;
    } catch (error) {
      console.error('Error in AdminMarketingCustomFieldService.createCustomField:', error);
      throw error;
    }
  },

  /**
   * Cập nhật trường tùy chỉnh
   */
  updateCustomField: async (
    id: number,
    data: UpdateMarketingCustomFieldRequest
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse>> => {
    try {
      console.log('Updating admin marketing custom field:', id, data);

      const response = await apiClient.put<MarketingCustomFieldResponse>(
        `${MARKETING_BASE_URL}/${id}`,
        data
      );

      console.log('Update admin marketing custom field response:', response);
      return response;
    } catch (error) {
      console.error('Error in AdminMarketingCustomFieldService.updateCustomField:', error);
      throw error;
    }
  },

  /**
   * Xóa trường tùy chỉnh
   */
  deleteCustomField: async (
    id: number
  ): Promise<ApiResponseDto<{ success: boolean }>> => {
    try {
      console.log('Deleting admin marketing custom field:', id);

      const response = await apiClient.delete<{ success: boolean }>(
        `${MARKETING_BASE_URL}/${id}`
      );

      console.log('Delete admin marketing custom field response:', response);
      return response;
    } catch (error) {
      console.error('Error in AdminMarketingCustomFieldService.deleteCustomField:', error);
      throw error;
    }
  },

  /**
   * Xóa nhiều trường tùy chỉnh
   */
  deleteMultipleCustomFields: async (
    ids: number[]
  ): Promise<ApiResponseDto<{ success: boolean; deletedCount: number }>> => {
    try {
      console.log('Deleting multiple admin marketing custom fields:', ids);

      const response = await apiClient.delete<{ success: boolean; deletedCount: number }>(
        `${MARKETING_BASE_URL}/bulk`,
        { data: { ids } }
      );

      console.log('Delete multiple admin marketing custom fields response:', response);
      return response;
    } catch (error) {
      console.error('Error in AdminMarketingCustomFieldService.deleteMultipleCustomFields:', error);
      throw error;
    }
  },
};

/**
 * Admin Marketing Custom Field Business Service - Layer 2: Business logic
 */
export const AdminMarketingCustomFieldBusinessService = {
  /**
   * Lấy danh sách trường tùy chỉnh với business logic
   */
  getCustomFieldsWithBusinessLogic: async (params?: MarketingCustomFieldQueryParams) => {
    const defaultParams: MarketingCustomFieldQueryParams = {
      page: params?.page || 1,
      limit: params?.limit || 10,
      search: params?.search !== undefined ? params.search : undefined,
      sortBy: params?.sortBy || 'id',
      sortDirection: params?.sortDirection || 'DESC',
    };

    // Validate limit
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    return AdminMarketingCustomFieldService.getCustomFields(defaultParams);
  },

  /**
   * Tạo trường tùy chỉnh với validation
   */
  createCustomFieldWithValidation: async (data: CreateMarketingCustomFieldRequest) => {
    // Validate fieldKey format
    if (!/^[a-zA-Z0-9_-]+$/.test(data.fieldKey)) {
      throw new Error('Field key must contain only letters, numbers, underscores, and hyphens');
    }

    // Validate displayName
    if (!data.displayName || data.displayName.trim().length === 0) {
      throw new Error('Display name is required');
    }

    // Validate config based on dataType
    if (data.config) {
      switch (data.dataType) {
        case 'string':
          if (data.config['minLength'] && data.config['maxLength']) {
            if (Number(data.config['minLength']) > Number(data.config['maxLength'])) {
              throw new Error('Min length cannot be greater than max length');
            }
          }
          break;
        case 'integer':
          if (data.config['minValue'] && data.config['maxValue']) {
            if (Number(data.config['minValue']) > Number(data.config['maxValue'])) {
              throw new Error('Min value cannot be greater than max value');
            }
          }
          break;
        case 'select':
          if (!data.config['options'] || !Array.isArray(data.config['options']) || data.config['options'].length === 0) {
            throw new Error('Select field must have at least one option');
          }
          break;
      }
    }

    return AdminMarketingCustomFieldService.createCustomField(data);
  },

  /**
   * Cập nhật trường tùy chỉnh với validation
   */
  updateCustomFieldWithValidation: async (id: number, data: UpdateMarketingCustomFieldRequest) => {
    // Validate displayName if provided
    if (data.displayName !== undefined && (!data.displayName || data.displayName.trim().length === 0)) {
      throw new Error('Display name cannot be empty');
    }

    // Validate config based on dataType if provided
    if (data.config && data.dataType) {
      switch (data.dataType) {
        case 'string':
          if (data.config['minLength'] && data.config['maxLength']) {
            if (Number(data.config['minLength']) > Number(data.config['maxLength'])) {
              throw new Error('Min length cannot be greater than max length');
            }
          }
          break;
        case 'integer':
          if (data.config['minValue'] && data.config['maxValue']) {
            if (Number(data.config['minValue']) > Number(data.config['maxValue'])) {
              throw new Error('Min value cannot be greater than max value');
            }
          }
          break;
        case 'select':
          if (!data.config['options'] || !Array.isArray(data.config['options']) || data.config['options'].length === 0) {
            throw new Error('Select field must have at least one option');
          }
          break;
      }
    }

    return AdminMarketingCustomFieldService.updateCustomField(id, data);
  },
};
