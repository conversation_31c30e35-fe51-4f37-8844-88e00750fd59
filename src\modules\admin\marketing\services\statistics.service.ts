/**
 * Service for marketing statistics API
 */

import { apiClient } from '@/shared/api';
import {
  AudienceGrowthStatisticsResponse,
  CampaignPerformanceStatisticsResponse,
  MarketingOverviewStatisticsResponse,
  MarketingStatisticsQueryParams,
  SegmentDistributionStatisticsResponse,
} from '../types/statistics.types';

/**
 * Base URL for marketing statistics API
 */
const BASE_URL = '/marketing/statistics';

/**
 * Marketing statistics service
 */
export const MarketingStatisticsService = {
  /**
   * Get overview statistics
   */
  getOverviewStatistics: async (
    params?: MarketingStatisticsQueryParams
  ): Promise<MarketingOverviewStatisticsResponse> => {
    return apiClient.get<MarketingOverviewStatisticsResponse['result']>(`${BASE_URL}/overview`, {
      params,
    });
  },

  /**
   * Get audience growth statistics
   */
  getAudienceGrowthStatistics: async (
    params?: MarketingStatisticsQueryParams
  ): Promise<AudienceGrowthStatisticsResponse> => {
    return apiClient.get<AudienceGrowthStatisticsResponse['result']>(
      `${BASE_URL}/audience-growth`,
      {
        params,
      }
    );
  },

  /**
   * Get campaign performance statistics
   */
  getCampaignPerformanceStatistics: async (
    params?: MarketingStatisticsQueryParams
  ): Promise<CampaignPerformanceStatisticsResponse> => {
    return apiClient.get<CampaignPerformanceStatisticsResponse['result']>(
      `${BASE_URL}/campaign-performance`,
      {
        params,
      }
    );
  },

  /**
   * Get segment distribution statistics
   */
  getSegmentDistributionStatistics: async (): Promise<SegmentDistributionStatisticsResponse> => {
    return apiClient.get<SegmentDistributionStatisticsResponse['result']>(
      `${BASE_URL}/segment-distribution`
    );
  },
};
