/**
 * <PERSON><PERSON><PERSON> nghĩa các types cho Audience trong Admin
 */

/**
 * Enum cho trạng thái của Audience
 */
export enum AudienceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Enum cho loại Audience
 */
export enum AudienceType {
  CUSTOMER = 'customer',
  LEAD = 'lead',
  SUBSCRIBER = 'subscriber',
  CUSTOM = 'custom',
}

/**
 * Interface cho thuộc tính của Audience
 */
export interface AudienceAttribute {
  id: string;
  name: string;
  value: string;
}

/**
 * Interface cho Audience
 */
export interface Audience {
  id: string;
  name: string;
  description?: string;
  email?: string;
  phone?: string;
  countryCode?: string;
  tagIds?: number[];
  type: AudienceType;
  status: AudienceStatus;
  totalContacts: number;
  attributes: AudienceAttribute[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho tham số filter của Audience
 */
export interface AudienceFilterParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: AudienceStatus | string;
  type?: AudienceType | string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho phản hồi API danh sách Audience
 */
export interface AudienceListData {
  items: Audience[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Alias cho kiểu dữ liệu phản hồi API danh sách Audience
 */
export type AudienceListResponse = AudienceListData;

/**
 * Alias cho kiểu dữ liệu phản hồi API chi tiết Audience
 */
export type AudienceDetailResponse = Audience;

/**
 * Interface cho dữ liệu tạo/cập nhật Audience
 */
export interface AudienceFormData {
  name: string;
  email: string;
  phone: string;
  countryCode?: string;
  tagIds?: number[];
  attributes?: Omit<AudienceAttribute, 'id'>[];
}
