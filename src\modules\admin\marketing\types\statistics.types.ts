/**
 * Types for marketing statistics API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Marketing statistics query params
 */
export interface MarketingStatisticsQueryParams {
  startDate?: string;
  endDate?: string;
  period?: 'day' | 'week' | 'month' | 'year';
  sortBy?: string;
  sortDirection?: string;
  page?: number;
  limit?: number;
}

/**
 * Marketing overview statistics
 */
export interface MarketingOverviewStatistics {
  totalAudiences: number;
  totalSegments: number;
  totalCampaigns: number;
  activeCampaigns: number;
  totalContacts: number;
  tagsCount: number;
  contactsGrowth: number;
  customFieldsCount: number;
  campaignPerformance: {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    converted: number;
  };
  recentCampaigns: {
    id: number;
    name: string;
    type: string;
    status: string;
    sentCount: number;
    openRate: number;
    clickRate: number;
    date: string;
  }[];
}

/**
 * Audience growth statistics
 */
export interface AudienceGrowthStatistics {
  totalContacts: number;
  growth: number;
  growthPercentage: number;
  byPeriod: {
    period: string;
    count: number;
    growth: number;
  }[];
  bySource: {
    source: string;
    count: number;
    percentage: number;
  }[];
  byType: {
    type: string;
    count: number;
    percentage: number;
  }[];
}

/**
 * Campaign performance statistics
 */
export interface CampaignPerformanceStatistics {
  totalCampaigns: number;
  activeCampaigns: number;
  totalSent: number;
  averageOpenRate: number;
  averageClickRate: number;
  averageConversionRate: number;
  byType: {
    type: string;
    count: number;
    openRate: number;
    clickRate: number;
    conversionRate: number;
  }[];
  byPeriod: {
    period: string;
    sent: number;
    opened: number;
    clicked: number;
    converted: number;
  }[];
}

/**
 * Segment distribution statistics
 */
export interface SegmentDistributionStatistics {
  totalSegments: number;
  totalContacts: number;
  averageContactsPerSegment: number;
  bySize: {
    size: string;
    count: number;
    percentage: number;
  }[];
  byType: {
    type: string;
    count: number;
    percentage: number;
  }[];
  topSegments: {
    id: number;
    name: string;
    contactCount: number;
    percentage: number;
  }[];
}

/**
 * Marketing overview statistics response
 */
export type MarketingOverviewStatisticsResponse = ApiResponseDto<MarketingOverviewStatistics>;

/**
 * Audience growth statistics response
 */
export type AudienceGrowthStatisticsResponse = ApiResponseDto<AudienceGrowthStatistics>;

/**
 * Campaign performance statistics response
 */
export type CampaignPerformanceStatisticsResponse = ApiResponseDto<CampaignPerformanceStatistics>;

/**
 * Segment distribution statistics response
 */
export type SegmentDistributionStatisticsResponse = ApiResponseDto<SegmentDistributionStatistics>;
