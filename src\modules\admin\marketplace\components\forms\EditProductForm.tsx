import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Eye, Code } from 'lucide-react';
import {
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Icon,
  Typography,
  Checkbox,
  Card,
} from '@/shared/components/common';
import { EmailBuilder } from '@/shared/components/email-builder';
import { SubmitHandler, useFormContext } from 'react-hook-form';
import { ProductCategory, Product } from '../../types/product.types';

// Định nghĩa type cho form values
export interface EditProductFormValues {
  name: string;
  description?: string;
  image?: string;
  listedPrice?: number;
  discountedPrice?: number;
  category: ProductCategory;
  detail?: string;
  userManual?: string;
}



// Component để reset form khi product thay đổi
const FormResetter: React.FC<{ product: Product | null; defaultValues: EditProductFormValues }> = ({
  product,
  defaultValues,
}) => {
  const { reset, getValues } = useFormContext();

  useEffect(() => {
    if (product?.id && defaultValues) {
      console.log('🔄 [FormResetter] ==================== RESETTING FORM ====================');
      console.log('🔄 [FormResetter] Product ID:', product.id);
      console.log('🔄 [FormResetter] Current form values before reset:', getValues());
      console.log('🔄 [FormResetter] New defaultValues to reset with:', defaultValues);

      reset(defaultValues);

      // Verify reset worked
      setTimeout(() => {
        const newValues = getValues();
        console.log('🔄 [FormResetter] Form values after reset:', newValues);
        console.log(
          '🔄 [FormResetter] Reset successful:',
          JSON.stringify(newValues) === JSON.stringify(defaultValues)
        );
      }, 100);
    }
  }, [product?.id, defaultValues, reset, getValues]);

  return null;
};



// Interface cho ADD image operation
interface AddImageOperation {
  operation: 'ADD';
  mimeType: string;
  size?: number;
  name?: string;
}

// Interface cho DELETE image operation
interface DeleteImageOperation {
  operation: 'DELETE';
  key: string;
}

// Union type cho image operations
type ImageOperation = AddImageOperation | DeleteImageOperation;

// Interface cho update product request theo đúng backend DTO
interface UpdateProductRequest {
  productInfo: {
    name: string;
    listedPrice: number;
    discountedPrice: number;
    description?: string;
  };
  images: ImageOperation[];
  detailEdited: boolean;
  userManual: boolean;
  publishAfterUpdate?: boolean;
}

// Interface cho presigned URL image response
interface PresignedUrlImage {
  index: number;
  uploadUrl: string;
}

// Interface cho response từ API update sản phẩm
interface UpdateProductResponse {
  code: number;
  message: string;
  result: {
    product: Product;
    presignedUrlImage: PresignedUrlImage[];
    presignedUrlDetail: string | null;
    presignedUrlUserManual: string | null;
    publishError?: string;
  };
}

interface EditProductFormProps {
  product: Product;
  onSubmit: (request: UpdateProductRequest) => Promise<UpdateProductResponse>;
  onCancel: () => void;
  onSuccess?: () => void;
  isSubmitting?: boolean;
}

/**
 * Form chỉnh sửa sản phẩm cho admin
 */
const EditProductForm: React.FC<EditProductFormProps> = ({
  product,
  onSubmit,
  onCancel,
  onSuccess,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // Direct upload function for text content
  const uploadTextContent = async (
    content: string,
    presignedUrl: string,
    contentType: string = 'text/plain'
  ) => {
    console.log(`🔍 [uploadTextContent] Starting upload to:`, presignedUrl);
    console.log(`🔍 [uploadTextContent] Content type:`, contentType);
    console.log(`🔍 [uploadTextContent] Content length:`, content.length);

    const response = await fetch(presignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': contentType,
      },
      body: content,
    });

    if (!response.ok) {
      console.error(`❌ [uploadTextContent] Upload failed:`, response.status, response.statusText);
      throw new Error(`Failed to upload content: ${response.status} ${response.statusText}`);
    }

    console.log(`✅ [uploadTextContent] Upload successful`);
    return response;
  };

  // Direct upload function for image files
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    console.log(`🔍 [uploadImageFile] Starting upload:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadUrl: presignedUrl,
    });

    const response = await fetch(presignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    if (!response.ok) {
      console.error(`❌ [uploadImageFile] Upload failed:`, response.status, response.statusText);
      throw new Error(`Failed to upload image: ${response.status} ${response.statusText}`);
    }

    console.log(`✅ [uploadImageFile] Upload successful for:`, file.name);
    return response;
  };

  // State cho image upload - hỗ trợ nhiều ảnh
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [deletedImageKeys, setDeletedImageKeys] = useState<string[]>([]); // Track ảnh bị xóa
  const [isUploading, setIsUploading] = useState<boolean>(false);

  // Track mapping giữa preview và key để tránh lỗi index
  const [imageKeyMapping, setImageKeyMapping] = useState<Map<string, string>>(new Map());

  // Track thay đổi detail và userManual
  const [hasDetailChanged, setHasDetailChanged] = useState<boolean>(false);
  const [hasUserManualChanged, setHasUserManualChanged] = useState<boolean>(false);
  const originalDetail = product?.detail || '';
  const originalUserManual = product?.userManual || '';

  // State cho preview mode của EmailBuilder
  const [detailPreviewMode, setDetailPreviewMode] = useState<'design' | 'code'>('design');
  const [userManualPreviewMode, setUserManualPreviewMode] = useState<'design' | 'code'>('design');

  // Debug originalValues
  useEffect(() => {
    console.log('🔍 [EDIT_FORM] ==================== ORIGINAL VALUES DEBUG ====================');
    console.log('🔍 [EDIT_FORM] originalDetail:', {
      value: originalDetail,
      type: typeof originalDetail,
      length: originalDetail.length,
      isUrl: originalDetail.startsWith('https://'),
      preview: originalDetail.substring(0, 100) + '...',
    });
    console.log('🔍 [EDIT_FORM] originalUserManual:', {
      value: originalUserManual,
      type: typeof originalUserManual,
      length: originalUserManual.length,
      isUrl: originalUserManual.startsWith('https://'),
      preview: originalUserManual.substring(0, 100) + '...',
    });
  }, [originalDetail, originalUserManual]);

  // State cho publish after update
  const [publishAfterUpdate, setPublishAfterUpdate] = useState<boolean>(false);

  // Memoize defaultValues để tránh re-render không cần thiết
  const defaultValues = React.useMemo(() => {
    console.log(
      '🔍 [EDIT_FORM] ==================== COMPUTING DEFAULT VALUES ===================='
    );
    console.log('🔍 [EDIT_FORM] Product data:', {
      id: product?.id,
      name: product?.name,
      description: product?.description,
      listedPrice: product?.listedPrice,
      discountedPrice: product?.discountedPrice,
      category: product?.category,
      detail: product?.detail,
      userManual: product?.userManual,
    });

    const values = {
      name: product?.name || '',
      description: product?.description || '',
      listedPrice: product?.listedPrice || 0,
      discountedPrice: product?.discountedPrice || 0,
      category: product?.category || ProductCategory.AGENT,
      detail: product?.detail || '',
      userManual: product?.userManual || '',
    };

    console.log('🔍 [EDIT_FORM] Computed defaultValues:', values);
    console.log('🔍 [EDIT_FORM] Has product data:', !!product);
    console.log('🔍 [EDIT_FORM] Product name exists:', !!product?.name);
    console.log('🔍 [EDIT_FORM] Product description exists:', !!product?.description);

    return values;
  }, [product]);

  // Reset states khi product ID thay đổi (chỉ khi chuyển sang product khác)
  useEffect(() => {
    console.log('🔍 [EDIT_FORM] ==================== PRODUCT ID CHANGED ====================');
    console.log('🔍 [EDIT_FORM] New product ID:', product?.id);
    console.log('🔍 [EDIT_FORM] Resetting states...');
    setImageFiles([]);
    setDeletedImageKeys([]);
    setHasDetailChanged(false);
    setHasUserManualChanged(false);
    setPublishAfterUpdate(false);
    setIsUploading(false);
  }, [product?.id]); // Chỉ reset khi ID thay đổi, không phải toàn bộ product object



  // State cho nội dung detail và userManual
  const [detailContent, setDetailContent] = useState<string>('');
  const [userManualContent, setUserManualContent] = useState<string>('');

  // Khởi tạo content từ originalDetail và originalUserManual
  useEffect(() => {
    const fetchContentFromUrl = async (url: string) => {
      if (!url) return '';

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: { Accept: 'text/plain, text/html, */*' },
          mode: 'cors',
          cache: 'no-cache',
        });

        if (response.ok) {
          return await response.text();
        }
        return '';
      } catch (error) {
        console.error('Failed to fetch content:', error);
        return '';
      }
    };

    const initializeContent = async () => {
      // Xử lý detail content
      if (originalDetail) {
        const isDetailUrl = originalDetail.startsWith('https://') && originalDetail.includes('cdn.redai.vn');
        if (isDetailUrl) {
          const content = await fetchContentFromUrl(originalDetail);
          setDetailContent(content || originalDetail);
        } else {
          setDetailContent(originalDetail);
        }
      } else {
        setDetailContent('');
      }

      // Xử lý userManual content
      if (originalUserManual) {
        const isUserManualUrl = originalUserManual.startsWith('https://') && originalUserManual.includes('cdn.redai.vn');
        if (isUserManualUrl) {
          const content = await fetchContentFromUrl(originalUserManual);
          setUserManualContent(content || originalUserManual);
        } else {
          setUserManualContent(originalUserManual);
        }
      } else {
        setUserManualContent('');
      }
    };

    initializeContent();
  }, [originalDetail, originalUserManual]);

  // Callback cho EmailBuilder content change
  const handleDetailEmailBuilderChange = React.useCallback((html: string) => {
    setDetailContent(html);
    const hasChanged = html.trim() !== originalDetail.trim();
    setHasDetailChanged(hasChanged);
  }, [originalDetail]);

  const handleUserManualEmailBuilderChange = React.useCallback((html: string) => {
    setUserManualContent(html);
    const hasChanged = html.trim() !== originalUserManual.trim();
    setHasUserManualChanged(hasChanged);
  }, [originalUserManual]);

  // Debug state changes
  useEffect(() => {
    console.log(`🔍 [EDIT_FORM] hasDetailChanged changed to:`, hasDetailChanged);
  }, [hasDetailChanged]);

  useEffect(() => {
    console.log(`🔍 [EDIT_FORM] hasUserManualChanged changed to:`, hasUserManualChanged);
  }, [hasUserManualChanged]);

  // Debug defaultValues changes
  useEffect(() => {
    console.log('🔍 [EDIT_FORM] ==================== DEFAULT VALUES CHANGED ====================');
    console.log('🔍 [EDIT_FORM] New defaultValues:', defaultValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product?.id]);

  // Khởi tạo mapping và previews khi component mount
  useEffect(() => {
    if (product?.images && Array.isArray(product.images)) {
      const mapping = new Map<string, string>();
      const previews: string[] = [];

      product.images.forEach((imageObj, index) => {
        // Xử lý cấu trúc mới: { key, position, url }
        if (typeof imageObj === 'object' && imageObj.url && imageObj.key) {
          mapping.set(imageObj.url, imageObj.key);
          previews.push(imageObj.url);
          console.log(`🔍 [EDIT_FORM] Mapped image ${index}:`, {
            url: imageObj.url,
            key: imageObj.key,
          });
        } else if (typeof imageObj === 'string') {
          // Fallback cho cấu trúc cũ (array of strings)
          const key = `image-${index}-${imageObj.split('/').pop()}`;
          mapping.set(imageObj, key);
          previews.push(imageObj);
          console.log(`🔍 [EDIT_FORM] Mapped legacy image ${index}:`, { url: imageObj, key });
        }
      });

      setImageKeyMapping(mapping);
      setImagePreviews(previews);
      console.log('🔍 [EDIT_FORM] Final image key mapping:', mapping);
      console.log('🔍 [EDIT_FORM] Final image previews:', previews);
    }
  }, [product?.images]);

  // Xử lý khi thêm ảnh mới
  const handleAddImage = (file: File, dataUrl: string) => {
    console.log('🔍 [ADD IMAGE] Adding new image:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
    });
    setImageFiles(prev => {
      const newFiles = [...prev, file];
      console.log('🔍 [ADD IMAGE] Updated imageFiles count:', newFiles.length);
      return newFiles;
    });
    setImagePreviews(prev => {
      const newPreviews = [...prev, dataUrl];
      console.log('🔍 [ADD IMAGE] Updated imagePreviews count:', newPreviews.length);
      return newPreviews;
    });
  };

  // Xử lý khi xóa ảnh
  const handleRemoveImage = (index: number) => {
    console.log('🔍 [REMOVE IMAGE] Starting removal for index:', index);
    console.log('🔍 [REMOVE IMAGE] Current imagePreviews:', imagePreviews);
    console.log('🔍 [REMOVE IMAGE] Current imageFiles:', imageFiles);
    console.log('🔍 [REMOVE IMAGE] Current imageKeyMapping:', imageKeyMapping);

    // Lấy URL của ảnh đang xóa
    const imageUrl = imagePreviews[index];
    console.log('🔍 [REMOVE IMAGE] Image URL to remove:', imageUrl);

    // Kiểm tra xem có phải ảnh cũ không bằng cách tìm trong mapping
    const correspondingKey = imageKeyMapping.get(imageUrl || '');
    if (correspondingKey) {
      console.log('🔍 [REMOVE IMAGE] Removing OLD image with key:', correspondingKey);
      setDeletedImageKeys(prev => {
        const newDeleted = [...prev, correspondingKey];
        console.log('🗑️ [REMOVE IMAGE] Updated deletedImageKeys:', newDeleted);
        return newDeleted;
      });
      // Xóa khỏi mapping
      setImageKeyMapping(prev => {
        const newMapping = new Map(prev);
        newMapping.delete(imageUrl || '');
        console.log('🔍 [REMOVE IMAGE] Updated imageKeyMapping:', newMapping);
        return newMapping;
      });
    } else {
      console.log('🔍 [REMOVE IMAGE] Removing NEW image (no key found)');
    }

    // Xác định số ảnh gốc từ product
    const originalImageCount = product?.images?.length || 0;
    console.log('🔍 [REMOVE IMAGE] Original image count:', originalImageCount);
    console.log('🔍 [REMOVE IMAGE] Removing index:', index);

    if (index >= originalImageCount) {
      // Đây là ảnh mới, xóa khỏi imageFiles
      const newImageIndex = index - originalImageCount;
      console.log(
        '🔍 [REMOVE IMAGE] This is a NEW image, removing from imageFiles at index:',
        newImageIndex
      );
      setImageFiles(prev => {
        const newFiles = prev.filter((_, i) => i !== newImageIndex);
        console.log('🔍 [REMOVE IMAGE] Updated imageFiles:', newFiles);
        return newFiles;
      });
    } else {
      console.log('🔍 [REMOVE IMAGE] This is an ORIGINAL image, not removing from imageFiles');
    }

    // Luôn xóa khỏi imagePreviews
    setImagePreviews(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      console.log('🔍 [REMOVE IMAGE] Updated imagePreviews:', newPreviews);
      return newPreviews;
    });
  };

  // Xử lý khi submit form
  const handleFormSubmit: SubmitHandler<EditProductFormValues> = async values => {
    console.log('🔍 [EditProductForm] ==================== FORM SUBMIT ====================');
    console.log('🔍 [EditProductForm] Form submitted with values:', values);
    console.log('🔍 [EditProductForm] Original product data:', {
      name: product?.name,
      description: product?.description,
      listedPrice: product?.listedPrice,
      discountedPrice: product?.discountedPrice,
      category: product?.category,
    });
    console.log('🔍 [EditProductForm] Image files:', imageFiles);
    console.log('🔍 [EditProductForm] Deleted image keys:', deletedImageKeys);
    console.log('🔍 [EditProductForm] Has detail changed:', hasDetailChanged);
    console.log('🔍 [EditProductForm] Has userManual changed:', hasUserManualChanged);
    console.log('🔍 [EditProductForm] Publish after update:', publishAfterUpdate);

    try {
      setIsUploading(true);

      // Tạo image operations
      const imageOperations: ImageOperation[] = [];

      // Thêm operations cho ảnh mới (ADD)
      imageFiles.forEach(file => {
        imageOperations.push({
          operation: 'ADD',
          mimeType: file.type,
          size: file.size,
          name: file.name,
        });
      });

      // Thêm operations cho ảnh bị xóa (DELETE)
      deletedImageKeys.forEach(key => {
        imageOperations.push({
          operation: 'DELETE',
          key: key,
        });
      });

      console.log('🔍 [EditProductForm] Image operations:', imageOperations);

      // Tạo update request theo đúng format API backend
      const updateRequest: UpdateProductRequest = {
        productInfo: {
          name: values.name || '',
          listedPrice: values.listedPrice || 0,
          discountedPrice: values.discountedPrice || 0,
          description: values.description || '',
        },
        images: imageOperations, // Luôn gửi array, có thể rỗng
        detailEdited: hasDetailChanged, // Luôn gửi boolean
        userManual: hasUserManualChanged, // Luôn gửi boolean
        publishAfterUpdate: publishAfterUpdate,
      };

      console.log('🔍 [EditProductForm] ==================== COMPARISON ====================');
      console.log('🔍 [EditProductForm] Form values vs Original:');
      console.log('🔍 [EditProductForm] Name:', {
        form: values.name,
        original: product?.name,
        changed: values.name !== product?.name,
      });
      console.log('🔍 [EditProductForm] Description:', {
        form: values.description,
        original: product?.description,
        changed: values.description !== product?.description,
      });
      console.log('🔍 [EditProductForm] ListedPrice:', {
        form: values.listedPrice,
        original: product?.listedPrice,
        changed: values.listedPrice !== product?.listedPrice,
      });
      console.log('🔍 [EditProductForm] DiscountedPrice:', {
        form: values.discountedPrice,
        original: product?.discountedPrice,
        changed: values.discountedPrice !== product?.discountedPrice,
      });
      console.log('🔍 [EditProductForm] Category:', {
        form: values.category,
        original: product?.category,
        changed: values.category !== product?.category,
      });

      console.log(
        '🔍 [EditProductForm] Final update request:',
        JSON.stringify(updateRequest, null, 2)
      );

      // Gọi API update
      const response = await onSubmit(updateRequest);
      console.log('🔍 [EditProductForm] API response:', response);

      // Upload files nếu có presigned URLs
      if (response.result) {
        const uploadPromises: Promise<string>[] = [];

        // Upload images mới
        if (
          imageFiles.length > 0 &&
          response.result.presignedUrlImage &&
          response.result.presignedUrlImage.length > 0
        ) {
          console.log('🔍 [EditProductForm] Starting image uploads...');
          console.log('🔍 [EditProductForm] Images to upload:', imageFiles.length);
          console.log(
            '🔍 [EditProductForm] Presigned URLs received:',
            response.result.presignedUrlImage
          );

          // Sắp xếp presigned URLs theo index để đảm bảo mapping đúng
          const sortedPresignedUrls = response.result.presignedUrlImage.sort(
            (a, b) => a.index - b.index
          );
          console.log('🔍 [EditProductForm] Sorted presigned URLs:', sortedPresignedUrls);

          imageFiles.forEach((file, fileIndex) => {
            // Lấy presigned URL theo thứ tự (fileIndex tương ứng với thứ tự trong sortedPresignedUrls)
            const uploadUrlData = sortedPresignedUrls[fileIndex];
            if (uploadUrlData) {
              console.log(`🔍 [EditProductForm] Uploading image ${fileIndex}:`, {
                fileName: file.name,
                fileSize: file.size,
                fileType: file.type,
                uploadUrl: uploadUrlData.uploadUrl,
                presignedIndex: uploadUrlData.index,
              });

              const uploadPromise = uploadImageFile(file, uploadUrlData.uploadUrl)
                .then(() => {
                  console.log(
                    `✅ Image ${fileIndex} (presigned index ${uploadUrlData.index}) uploaded successfully`
                  );
                  return 'success';
                })
                .catch(error => {
                  console.error(`❌ Failed to upload image ${fileIndex}:`, error);
                  throw error;
                });
              uploadPromises.push(uploadPromise);
            } else {
              console.warn(`⚠️ No presigned URL found for image at file index ${fileIndex}`);
            }
          });
        }

        // Upload detail content nếu có thay đổi
        if (hasDetailChanged && detailContent && response.result.presignedUrlDetail) {
          console.log('🔍 [EditProductForm] Uploading detail content...');
          console.log('🔍 [EditProductForm] Detail content length:', detailContent.length);
          console.log(
            '🔍 [EditProductForm] Detail upload URL:',
            response.result.presignedUrlDetail
          );

          const uploadPromise = uploadTextContent(
            detailContent,
            response.result.presignedUrlDetail,
            'text/html'
          )
            .then(() => {
              console.log('✅ Detail content uploaded successfully');
              return 'success';
            })
            .catch(error => {
              console.error('❌ Failed to upload detail content:', error);
              throw error;
            });
          uploadPromises.push(uploadPromise);
        }

        // Upload userManual content nếu có thay đổi
        if (hasUserManualChanged && userManualContent && response.result.presignedUrlUserManual) {
          console.log('🔍 [EditProductForm] Uploading user manual content...');
          console.log('🔍 [EditProductForm] User manual content length:', userManualContent.length);
          console.log(
            '🔍 [EditProductForm] User manual upload URL:',
            response.result.presignedUrlUserManual
          );

          const uploadPromise = uploadTextContent(
            userManualContent,
            response.result.presignedUrlUserManual,
            'text/plain'
          )
            .then(() => {
              console.log('✅ User manual content uploaded successfully');
              return 'success';
            })
            .catch(error => {
              console.error('❌ Failed to upload user manual content:', error);
              throw error;
            });
          uploadPromises.push(uploadPromise);
        }

        // Chờ tất cả uploads hoàn thành
        if (uploadPromises.length > 0) {
          console.log(
            `🔍 [EditProductForm] Waiting for ${uploadPromises.length} uploads to complete...`
          );
          await Promise.all(uploadPromises);
          console.log('✅ [EditProductForm] All uploads completed successfully');
        } else {
          console.log('ℹ️ [EditProductForm] No files to upload');
        }
      }

      // Gọi callback success
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('❌ [EditProductForm] Error updating product:', error);
      // TODO: Hiển thị error message cho user
      throw error; // Re-throw để component cha có thể xử lý
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="p-6">
      <Typography variant="h2" className="mb-6">
        {t('admin:marketplace.product.editProduct', 'Chỉnh sửa sản phẩm')}
      </Typography>

      <Form
        key={product?.id} // Force re-render khi product thay đổi
        onSubmit={handleFormSubmit as unknown as SubmitHandler<Record<string, unknown>>}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Component để reset form khi product thay đổi */}
        <FormResetter product={product} defaultValues={defaultValues} />

        <FormItem
          name="name"
          label={t('admin:marketplace.product.form.name', 'Tên sản phẩm')}
          required
        >
          <Input
            placeholder={t('admin:marketplace.product.form.namePlaceholder', 'Nhập tên sản phẩm')}
            fullWidth
          />
        </FormItem>

        <FormItem
          name="description"
          label={t('admin:marketplace.product.form.description', 'Mô tả sản phẩm')}
        >
          <Input
            placeholder={t(
              'admin:marketplace.product.form.descriptionPlaceholder',
              'Nhập mô tả sản phẩm'
            )}
            fullWidth
          />
        </FormItem>

        <FormItem name="image" label={t('admin:marketplace.product.form.images', 'Ảnh sản phẩm')}>
          <div className="space-y-4">
          

            {/* Upload ảnh mới */}
            <div>
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={e => {
                  const files = Array.from(e.target.files || []);
                  files.forEach(file => {
                    const reader = new FileReader();
                    reader.onload = event => {
                      if (event.target?.result) {
                        handleAddImage(file, event.target.result as string);
                      }
                    };
                    reader.readAsDataURL(file);
                  });
                }}
                className="hidden"
                id="image-upload"
              />
              <label
                htmlFor="image-upload"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <Icon name="upload" size="lg" className="text-gray-400 mb-2" />
                <span className="text-sm text-muted">
                  {t(
                    'ad min:marketplace.product.form.imagePlaceholder',
                    'Kéo thả hoặc click để tải lên ảnh sản phẩm'
                  )}
                </span>
               
              </label>
            </div>
              {/* Hiển thị ảnh hiện có */}
            {imagePreviews.length > 0 && (
              <div className="grid grid-cols-6 md:grid-cols-8 gap-4">
                {imagePreviews.map((preview, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={preview.startsWith('http') ? preview : `https://cdn.redai.vn/${preview}`}
                      alt={`Product ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg border"
                    />
                    <button
                      type="button"
                      onClick={() => handleRemoveImage(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Icon name="x" size="xs" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Thông tin file */}
            {imageFiles.length > 0 && (
              <div className="text-sm text-muted">
                <p>
                  {t('admin:marketplace.product.form.selectedImages', 'Đã chọn {{count}} ảnh mới', {
                    count: imageFiles.length,
                  })}
                </p>
              </div>
            )}
          </div>
        </FormItem>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="listedPrice"
            label={t('admin:marketplace.product.form.listedPrice', 'Giá niêm yết (rpoint)')}
          >
            <Input
              type="number"
              placeholder={t(
                'admin:marketplace.product.form.listedPricePlaceholder',
                'Nhập giá niêm yết'
              )}
              min={0}
              step={1000}
              leftIcon={<Icon name="dollar-sign" size="sm" />}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="discountedPrice"
            label={t('admin:marketplace.product.form.discountedPrice', 'Giá khuyến mãi (rpoint)')}
          >
            <Input
              type="number"
              placeholder={t(
                'admin:marketplace.product.form.discountedPricePlaceholder',
                'Nhập giá khuyến mãi'
              )}
              min={0}
              step={1000}
              leftIcon={<Icon name="dollar-sign" size="sm" />}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem
          name="category"
          label={t('admin:marketplace.product.form.category', 'Thể loại')}
          required
        >
          <Select
            placeholder={t('admin:marketplace.product.form.categoryPlaceholder', 'Chọn thể loại')}
            options={[
              {
                value: ProductCategory.AGENT,
                label: t('admin:marketplace.product.category.AGENT', 'AI Agent'),
              },
              {
                value: ProductCategory.KNOWLEDGE_FILE,
                label: t('admin:marketplace.product.category.KNOWLEDGE_FILE', 'Knowledge File'),
              },
              {
                value: ProductCategory.TEMPLATE,
                label: t('admin:marketplace.product.category.TEMPLATE', 'Template'),
              },
              {
                value: ProductCategory.OTHER,
                label: t('admin:marketplace.product.category.OTHER', 'Khác'),
              },
            ]}
            fullWidth
          />
        </FormItem>

        {/* Thêm input cho detail và userManual */}
        <Typography variant="h6">
          {t('admin:marketplace.product.form.additionalInfo', 'Thông tin bổ sung')}
        </Typography>

        <Card
          title={t('admin:marketplace.product.form.detail', 'Thông tin chi tiết sản phẩm')}
          extra={
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant={detailPreviewMode === 'design' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setDetailPreviewMode('design')}
              >
                <Eye className="h-4 w-4 mr-1" />
                {t('marketing:email.templates.form.content.designMode', 'Design')}
              </Button>
              <Button
                type="button"
                variant={detailPreviewMode === 'code' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setDetailPreviewMode('code')}
              >
                <Code className="h-4 w-4 mr-1" />
                {t('marketing:email.templates.form.content.codeMode', 'HTML')}
              </Button>
            </div>
          }
        >
          {detailPreviewMode === 'code' ? (
            <FormItem
              label={t('admin:marketplace.product.form.detail', 'Thông tin chi tiết sản phẩm')}
              name="detail"
            >
              <textarea
                className="w-full p-3 border border-border rounded-md min-h-[400px] font-mono text-sm bg-card-muted text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                placeholder={t(
                  'admin:marketplace.product.form.detailPlaceholder',
                  'Nhập thông tin chi tiết sản phẩm (nếu có thay đổi)'
                )}
                value={detailContent}
                onChange={(e) => {
                  setDetailContent(e.target.value);
                  const hasChanged = e.target.value.trim() !== originalDetail.trim();
                  setHasDetailChanged(hasChanged);
                }}
              />
            </FormItem>
          ) : (
            <div className="border border-border rounded-lg overflow-hidden min-h-[500px] bg-card">
              <EmailBuilder
                initialValue={detailContent}
                onContentChange={handleDetailEmailBuilderChange}
              />
            </div>
          )}
        </Card>

        <Card
          title={t('admin:marketplace.product.form.userManual', 'Hướng dẫn sử dụng')}
          extra={
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant={userManualPreviewMode === 'design' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setUserManualPreviewMode('design')}
              >
                <Eye className="h-4 w-4 mr-1" />
                {t('marketing:email.templates.form.content.designMode', 'Design')}
              </Button>
              <Button
                type="button"
                variant={userManualPreviewMode === 'code' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setUserManualPreviewMode('code')}
              >
                <Code className="h-4 w-4 mr-1" />
                {t('marketing:email.templates.form.content.codeMode', 'HTML')}
              </Button>
            </div>
          }
        >
          {userManualPreviewMode === 'code' ? (
            <FormItem
              label={t('admin:marketplace.product.form.userManual', 'Hướng dẫn sử dụng')}
              name="userManual"
            >
              <textarea
                className="w-full p-3 border border-border rounded-md min-h-[400px] font-mono text-sm bg-card-muted text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                placeholder={t(
                  'admin:marketplace.product.form.userManualPlaceholder',
                  'Nhập hướng dẫn sử dụng sản phẩm (nếu có thay đổi)'
                )}
                value={userManualContent}
                onChange={(e) => {
                  setUserManualContent(e.target.value);
                  const hasChanged = e.target.value.trim() !== originalUserManual.trim();
                  setHasUserManualChanged(hasChanged);
                }}
              />
            </FormItem>
          ) : (
            <div className="border border-border rounded-lg overflow-hidden min-h-[500px] bg-card">
              <EmailBuilder
                initialValue={userManualContent}
                onContentChange={handleUserManualEmailBuilderChange}
              />
            </div>
          )}
        </Card>

        {/* Checkbox để publish sau khi update */}
        <Checkbox
          id="publishAfterUpdate"
          checked={publishAfterUpdate}
          onChange={setPublishAfterUpdate}
          label={t(
            'admin:marketplace.product.form.publishAfterUpdate',
            'Xuất bản sau khi cập nhật'
          )}
        />

        <div className="flex justify-end space-x-4 mt-8">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting || isUploading}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button variant="primary" type="submit" disabled={isSubmitting || isUploading}>
            {isSubmitting || isUploading
              ? t('common:updating', 'Đang cập nhật...')
              : t('common:update', 'Cập nhật')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default EditProductForm;
