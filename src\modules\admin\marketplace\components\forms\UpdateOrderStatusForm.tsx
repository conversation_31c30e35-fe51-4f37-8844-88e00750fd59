import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormItem, Button, Typography, Select } from '@/shared/components/common';
import { Order, OrderStatus } from '@/modules/admin/marketplace/types/order.types';

export interface UpdateOrderStatusFormProps {
  order: Order;
  onSubmit: (status: OrderStatus) => void;
  onCancel: () => void;
  hideButtons?: boolean; // Thêm prop để ẩn các nút
}

/**
 * Form component for updating order status
 */
const UpdateOrderStatusForm: React.FC<UpdateOrderStatusFormProps> = ({
  order,
  onSubmit,
  onCancel,
  hideButtons = false,
}) => {
  const { t } = useTranslation();
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>(order.status);

  // Status options
  const statusOptions = Object.values(OrderStatus).map(status => ({
    value: status,
    label: t(`admin.marketplace.order.status.${status}`, status),
  }));

  // Handle form submission
  const handleSubmit = () => {
    onSubmit(selectedStatus);
  };

  return (
    <div className="p-6">
      <Typography variant="h6" className="mb-4">
        {t('admin.marketplace.order.updateStatus', 'Cập nhật trạng thái đơn hàng')}
        <span className="ml-2 text-gray-500">#{order.orderNumber}</span>
      </Typography>

      <Form className="space-y-4" onSubmit={() => handleSubmit()}>
        <FormItem name="status" label={t('admin.marketplace.order.status', 'Trạng thái')} required>
          <Select
            options={statusOptions}
            value={selectedStatus}
            onChange={value => setSelectedStatus(value as OrderStatus)}
          />
        </FormItem>

        {!hideButtons && (
          <div className="flex justify-end space-x-3 mt-6">
            <Button variant="outline" onClick={onCancel} type="button">
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="primary" type="submit">
              {t('common.save', 'Lưu')}
            </Button>
          </div>
        )}
      </Form>
    </div>
  );
};

export default UpdateOrderStatusForm;
