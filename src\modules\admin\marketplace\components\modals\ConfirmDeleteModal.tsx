import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Typography, Button } from '@/shared/components/common';

interface ConfirmDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  itemName?: string;
  isSubmitting?: boolean;
}

/**
 * Modal xác nhận xóa dùng chung
 */
const ConfirmDeleteModal: React.FC<ConfirmDeleteModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  isSubmitting = false,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
      footer={
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button variant="danger" onClick={onConfirm} disabled={isSubmitting}>
            {t('common.delete', 'Xóa')}
          </Button>
        </div>
      }
    >
      <div className="py-4">
        <Typography>{message}</Typography>
        {itemName && (
          <Typography variant="body2" className="mt-2 font-semibold">
            {itemName}
          </Typography>
        )}
      </div>
    </Modal>
  );
};

export default ConfirmDeleteModal;
