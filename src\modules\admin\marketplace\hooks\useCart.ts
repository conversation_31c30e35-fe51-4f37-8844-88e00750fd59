import { useQuery } from '@tanstack/react-query';
import { CartService } from '../services/cart.service';

// Key cho React Query
const CART_QUERY_KEY = 'admin-marketplace-cart';
const ALL_CARTS_QUERY_KEY = 'admin-marketplace-all-carts';

/**
 * Hook để lấy danh sách giỏ hàng của tất cả người dùng
 * @returns Query object với danh sách giỏ hàng
 */
export const useAllCarts = () => {
  return useQuery({
    queryKey: [ALL_CARTS_QUERY_KEY],
    queryFn: () => CartService.getAllCarts(),
  });
};

/**
 * Hook để lấy chi tiết giỏ hàng theo ID
 * @param id ID của giỏ hàng
 * @returns Query object với thông tin chi tiết giỏ hàng
 */
export const useCartById = (id: string | number) => {
  return useQuery({
    queryKey: [CART_QUERY_KEY, id],
    queryFn: () => CartService.getCartById(id),
    enabled: !!id,
  });
};
