import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { OrderService } from '../services/order.service';
import {
  OrderDetailResponse,
  OrderFilterParams,
  OrderListResponse,
  OrderStatus,
} from '../types/order.types';

// Key cho React Query
const ORDER_QUERY_KEY = 'admin-marketplace-orders';

/**
 * Hook để lấy danh sách đơn hàng
 * @param params Tham số filter
 * @returns Query object với danh sách đơn hàng
 */
export const useOrders = (params?: OrderFilterParams) => {
  return useQuery({
    queryKey: [ORDER_QUERY_KEY, params],
    queryFn: () => OrderService.getOrders(params),
    select: data => data.result as OrderListResponse,
  });
};

/**
 * Hook để lấy chi tiết đơn hàng
 * @param id ID của đơn hàng
 * @returns Query object với chi tiết đơn hàng
 */
export const useOrder = (id: string) => {
  return useQuery({
    queryKey: [ORDER_QUERY_KEY, id],
    queryFn: () => OrderService.getOrder(id),
    select: data => data.result as OrderDetailResponse,
    enabled: !!id,
  });
};

/**
 * Hook để cập nhật trạng thái đơn hàng
 * @returns Mutation object để cập nhật trạng thái đơn hàng
 */
export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: OrderStatus }) =>
      OrderService.updateOrderStatus(id, status),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [ORDER_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [ORDER_QUERY_KEY, variables.id] });
    },
  });
};
