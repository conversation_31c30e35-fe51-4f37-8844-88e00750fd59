import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../services/product.service';
import {
  CreateProductDto,
  ProductDetailResponse,
  ProductFilterParams,
  ProductListResponse,
  UpdateProductDto,
} from '../types/product.types';

// Key cho React Query
const PRODUCT_QUERY_KEY = 'admin-marketplace-products';

/**
 * Hook để lấy danh sách sản phẩm
 * @param params Tham số filter
 * @returns Query object với danh sách sản phẩm
 */
export const useProducts = (params?: ProductFilterParams) => {
  return useQuery({
    queryKey: [PRODUCT_QUERY_KEY, params],
    queryFn: () => ProductService.getProducts(params),
    select: data => data.result as ProductListResponse,
  });
};

/**
 * Hook để lấy chi tiết sản phẩm
 * @param id ID của sản phẩm
 * @returns Query object với chi tiết sản phẩm
 */
export const useProduct = (id: string) => {
  return useQuery({
    queryKey: [PRODUCT_QUERY_KEY, id],
    queryFn: () => ProductService.getProduct(id),
    select: data => data.result as ProductDetailResponse,
    enabled: !!id,
  });
};

/**
 * Hook để tạo sản phẩm mới
 * @returns Mutation object để tạo sản phẩm
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductDto) => ProductService.createProduct(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật sản phẩm
 * @returns Mutation object để cập nhật sản phẩm
 */
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProductDto }) =>
      ProductService.updateProduct(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa sản phẩm
 * @returns Mutation object để xóa sản phẩm
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ProductService.deleteProduct(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY] });
    },
  });
};

/**
 * Hook để phê duyệt sản phẩm
 * @returns Mutation object để phê duyệt sản phẩm
 */
export const useApproveProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ProductService.approveProduct(id),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY, variables] });
    },
  });
};

/**
 * Hook để từ chối sản phẩm
 * @returns Mutation object để từ chối sản phẩm
 */
export const useRejectProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) =>
      ProductService.rejectProduct(id, reason),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa nhiều sản phẩm
 * @returns Mutation object để xóa nhiều sản phẩm
 */
export const useBatchDeleteProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productIds: number[]) => ProductService.batchDeleteProducts(productIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật trạng thái nhiều sản phẩm
 * @returns Mutation object để cập nhật trạng thái nhiều sản phẩm
 */
export const useBatchUpdateProductStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productIds, status }: { productIds: number[]; status: string }) =>
      ProductService.batchUpdateProductStatus(productIds, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY] });
    },
  });
};

/**
 * Hook để đăng bán sản phẩm (publish)
 * @returns Mutation object để publish sản phẩm
 */
export const usePublishProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ProductService.publishProduct(id),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [PRODUCT_QUERY_KEY, variables] });
    },
  });
};
