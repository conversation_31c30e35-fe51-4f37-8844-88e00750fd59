import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, Modal, Chip, Button, ActionMenu } from '@/shared/components/common';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { OrderForm, UpdateOrderStatusForm } from '@/modules/admin/marketplace/components/forms';

import { useOrders, useUpdateOrderStatus } from '@/modules/admin/marketplace/hooks/useOrder';
import {
  Order,
  OrderStatus,
  OrderType,
  OrderFilterParams,
} from '@/modules/admin/marketplace/types/order.types';
import { ProductCategory, ProductStatus } from '@/modules/admin/marketplace/types/product.types';

/**
 * Trang quản lý đơn hàng trong marketplace
 */
const OrdersPage: React.FC = () => {
  const { t } = useTranslation();
  const [orders, setOrders] = useState<Order[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [orderToView, setOrderToView] = useState<Order | null>(null);
  const [showStatusModal, setShowStatusModal] = useState(false);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);



  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewSlideForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<OrderFilterParams>(() => {
    const params: OrderFilterParams = {
      page: currentPage,
      limit: itemsPerPage,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    // Chỉ thêm search nếu có giá trị
    if (searchTerm) {
      params.search = searchTerm;
    }

    // Chỉ thêm sortBy nếu có giá trị
    if (sortBy) {
      params.sortBy = sortBy;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // Hooks để gọi API
  const {
    data: ordersData,
    isLoading: isLoadingOrders,
    error: ordersError,
  } = useOrders(queryParams);
  const { mutateAsync: updateOrderStatus } = useUpdateOrderStatus();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (ordersData) {
      // Định nghĩa kiểu dữ liệu cho dữ liệu API
      interface ApiOrderItem {
        id: number;
        productId: number;
        productName: string;
        point: number;
        quantity: number;
        platformFeePercent: number;
      }

      interface ApiOrder {
        id: number;
        user: {
          id: number;
          name: string;
          email: string;
          avatar: string;
        };
        orderLines: ApiOrderItem[];
        totalAmount: number;
        createdAt: number;
      }

      // Chuyển đổi dữ liệu từ API sang định dạng Order
      const transformedOrders = ((ordersData.items || []) as unknown as ApiOrder[]).map(
        (item: ApiOrder) => {
          // Tạo đối tượng Order từ dữ liệu API
          const order: Order = {
            id: String(item.id),
            userId: item.user?.id || 0,
            orderNumber: String(item.id), // Sử dụng id làm orderNumber
            items: (item.orderLines || []).map((line: ApiOrderItem) => ({
              id: String(line.id),
              productId: String(line.productId),
              quantity: line.quantity,
              price: line.point,
              product: {
                id: String(line.productId),
                name: line.productName,
                description: '',
                price: line.point,
                discountedPrice: line.point,
                category: ProductCategory.OTHER,
                status: ProductStatus.APPROVED,
                seller: {
                  id: 0,
                  name: '',
                  avatar: '',
                  type: 'USER',
                },
                createdAt: '',
                updatedAt: '',
                listedPrice: line.point,
                sourceId: String(line.productId),
              },
            })),
            totalItems: item.orderLines?.length || 0,
            totalPrice: item.totalAmount,
            status: OrderStatus.PENDING, // Giả định trạng thái mặc định
            type: OrderType.PURCHASE, // Giả định loại mặc định
            createdAt: new Date(Number(item.createdAt)).toISOString(),
            updatedAt: new Date(Number(item.createdAt)).toISOString(),
          };

          return order;
        }
      );

      setOrders(transformedOrders);
      setTotalItems(ordersData.meta?.totalItems || 0);
    }
  }, [ordersData, ordersError]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết đơn hàng
  const handleShowViewForm = useCallback(
    (order: Order) => {
      setOrderToView(order);
      showViewSlideForm();
    },
    [showViewSlideForm]
  );

  // Xử lý đóng form xem chi tiết đơn hàng
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    // Đặt timeout để đảm bảo animation đóng hoàn tất trước khi xóa dữ liệu
    setTimeout(() => {
      setOrderToView(null);
    }, 300);
  }, [hideViewForm]);

  // Xử lý hiển thị modal thay đổi trạng thái
  const handleShowStatusModal = useCallback((order: Order) => {
    setOrderToView(order);
    setShowStatusModal(true);
  }, []);

  // Xử lý hủy thay đổi trạng thái
  const handleCancelStatusChange = useCallback(() => {
    setShowStatusModal(false);
    setOrderToView(null);
  }, []);

  // Xử lý xác nhận thay đổi trạng thái
  const handleConfirmStatusChange = useCallback(
    async (status: OrderStatus) => {
      if (!orderToView) return;

      try {
        await updateOrderStatus({ id: orderToView.id, status });
        setShowStatusModal(false);
        setOrderToView(null);
      } catch (error) {
        console.error('Error updating order status:', error);
      }
    },
    [orderToView, updateOrderStatus]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'orderNumber',
        title: t('admin:marketplace.order.table.id', 'Mã đơn hàng'),
        dataIndex: 'orderNumber',
        width: '15%',
        sortable: true,
      },
      {
        key: 'userId',
        title: t('admin:marketplace.order.table.customer', 'Người dùng'),
        dataIndex: 'userId',
        width: '15%',
        sortable: true,
      },
      {
        key: 'totalPrice',
        title: t('admin:marketplace.order.table.total', 'Tổng tiền'),
        dataIndex: 'totalPrice',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <span>{String(value)} points</span>;
        },
      },
      {
        key: 'status',
        title: t('admin:marketplace.order.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as OrderStatus;
          let variant: 'default' | 'primary' | 'success' | 'warning' | 'danger' = 'default';

          switch (status) {
            case OrderStatus.COMPLETED:
              variant = 'success';
              break;
            case OrderStatus.PROCESSING:
              variant = 'primary';
              break;
            case OrderStatus.PENDING:
              variant = 'warning';
              break;
            case OrderStatus.CANCELLED:
              variant = 'danger';
              break;
            case OrderStatus.FAILED:
              variant = 'danger';
              break;
          }

          return (
            <Chip size="sm" variant={variant}>
              {t(`admin.marketplace.order.status.${status}`, status)}
            </Chip>
          );
        },
      },
      {
        key: 'type',
        title: t('admin:marketplace.order.table.type', 'Loại'),
        dataIndex: 'type',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const type = value as OrderType;
          return (
            <Chip size="sm" variant="default">
              {t(`admin:marketplace.order.type.${type}`, type)}
            </Chip>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('admin:marketplace.order.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          try {
            const date = new Date(value as string);
            return date.toLocaleString();
          } catch (error) {
            console.error('Invalid date format:', error);
            return String(value);
          }
        },
      },
      {
        key: 'actions',
        title: t('admin:marketplace.order.table.actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: Order) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common.view', 'Xem'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              id: 'change-status',
              label: t('admin.marketplace.order.changeStatus', 'Thay đổi trạng thái'),
              icon: 'edit',
              onClick: () => handleShowStatusModal(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('admin:marketplace.order.moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [t, visibleColumns, handleShowViewForm, handleShowStatusModal]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns]);

  // Tạo các filter cho MenuIconBar
  const filterItems = useMemo(() => {
    return [
      {
        id: 'status',
        label: t('admin.marketplace.order.filter.status', 'Trạng thái'),
        options: Object.values(OrderStatus).map(status => ({
          value: status,
          label: t(`admin.marketplace.order.status.${status}`, status),
        })),
      },
      {
        id: 'type',
        label: t('admin.marketplace.order.filter.type', 'Loại đơn hàng'),
        options: Object.values(OrderType).map(type => ({
          value: type,
          label: t(`admin.marketplace.order.type.${type}`, type),
        })),
      },
    ];
  }, [t]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={true}
            showColumnFilter={true}
            items={filterItems}
          />
        </div>
        {/* SlideInForm cho xem chi tiết đơn hàng */}
        <SlideInForm isVisible={isViewFormVisible}>
          {orderToView && (
            <OrderForm
              initialValues={orderToView}
              onSubmit={() => {}} // Không cần xử lý submit vì chỉ xem
              onCancel={handleCloseViewForm}
              readOnly={true} // Chế độ chỉ đọc
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<Order>
            columns={filteredColumns}
            data={orders}
            rowKey="orderNumber"
            loading={isLoadingOrders}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal thay đổi trạng thái */}
      <Modal
        isOpen={showStatusModal}
        onClose={handleCancelStatusChange}
        title={t('admin.marketplace.order.changeStatusTitle', 'Thay đổi trạng thái đơn hàng')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelStatusChange}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                // Tìm và kích hoạt nút Save trong form
                const saveButton = document.querySelector(
                  '.UpdateOrderStatusForm button[type="submit"]'
                );
                if (saveButton) {
                  (saveButton as HTMLButtonElement).click();
                }
              }}
            >
              {t('common.save', 'Lưu')}
            </Button>
          </div>
        }
      >
        {orderToView && (
          <div className="UpdateOrderStatusForm">
            <UpdateOrderStatusForm
              order={orderToView}
              onSubmit={handleConfirmStatusChange}
              onCancel={handleCancelStatusChange}
              hideButtons={true} // Ẩn các nút trong form
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrdersPage;
