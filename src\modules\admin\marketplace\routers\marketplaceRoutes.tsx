import { Loading } from '@/shared/components';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import MarketplaceManagementPage from '@/modules/admin/marketplace/pages/MarketplaceManagementPage';

// Import Marketplace module pages
const ProductsPage = lazy(() => import('@/modules/admin/marketplace/pages/ProductsPage'));
const OrdersPage = lazy(() => import('@/modules/admin/marketplace/pages/OrdersPage'));
const CartPage = lazy(() => import('@/modules/admin/marketplace/pages/CartPage'));

/**
 * Marketplace module routes
 */
const marketplaceAdminRoutes: RouteObject[] = [
  {
    path: '/admin/marketplace',
    element: (
      <AdminLayout title="Marketplace Management">
        <Suspense fallback={<Loading />}>
          <MarketplaceManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/marketplace/products',
    element: (
      <AdminLayout title="Product Management">
        <Suspense fallback={<Loading />}>
          <ProductsPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/marketplace/orders',
    element: (
      <AdminLayout title="Order Management">
        <Suspense fallback={<Loading />}>
          <OrdersPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/marketplace/cart',
    element: (
      <AdminLayout title="Cart Management">
        <Suspense fallback={<Loading />}>
          <CartPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default marketplaceAdminRoutes;
