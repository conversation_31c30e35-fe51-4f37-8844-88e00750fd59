import { z } from 'zod';

/**
 * Schema validation cho filter đơn hàng
 */
export const orderFilterSchema = z.object({
  page: z.number().optional(),
  limit: z.number().optional(),
  search: z.string().optional(),
  status: z.string().optional(),
  type: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
});

/**
 * Type cho filter values của đơn hàng
 */
export type OrderFilterValues = z.infer<typeof orderFilterSchema>;
