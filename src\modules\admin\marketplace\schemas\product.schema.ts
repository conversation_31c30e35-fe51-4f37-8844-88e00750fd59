import { z } from 'zod';
import { ProductCategory, UpdateProductOption } from '../types/product.types';

/**
 * Schema validation cho tạo sản phẩm mới
 */
export const createProductSchema = z.object({
  name: z
    .string()
    .min(3, 'Tên sản phẩm phải có ít nhất 3 ký tự')
    .max(500, 'Tên sản phẩm không được vượt quá 500 ký tự'),
  description: z.string().min(1, '<PERSON>ô tả sản phẩm là bắt buộc'),
  listedPrice: z.number().min(0, '<PERSON>i<PERSON> niêm yết không được âm'),
  discountedPrice: z.number().min(0, 'Giá sau giảm không được âm'),
  category: z.nativeEnum(ProductCategory, {
    errorMap: () => ({ message: '<PERSON><PERSON>i sản phẩm là bắt buộc' }),
  }),
  sourceId: z.string().uuid('ID nguồn sản phẩm không hợp lệ'),
  images: z.array(z.string()).optional(),
  userManual: z.string().optional(),
  detail: z.string().optional(),
  imagesMediaTypes: z.array(z.string()).optional(),
  userManualMediaType: z.string().optional(),
  detailMediaType: z.string().optional(),
});

/**
 * Schema validation cho thông tin sản phẩm khi cập nhật
 */
export const productInfoSchema = z.object({
  name: z
    .string()
    .min(3, 'Tên sản phẩm phải có ít nhất 3 ký tự')
    .max(500, 'Tên sản phẩm không được vượt quá 500 ký tự'),
  description: z.string().min(1, 'Mô tả sản phẩm là bắt buộc'),
  listedPrice: z.number().min(0, 'Giá niêm yết không được âm'),
  discountedPrice: z.number().min(0, 'Giá sau giảm không được âm'),
  category: z
    .nativeEnum(ProductCategory, {
      errorMap: () => ({ message: 'Loại sản phẩm là bắt buộc' }),
    })
    .optional(),
});

/**
 * Schema validation cho thao tác với hình ảnh
 */
export const imageOperationSchema = z.object({
  operation: z.enum(['ADD', 'DELETE'], {
    errorMap: () => ({ message: 'Thao tác với hình ảnh không hợp lệ' }),
  }),
  key: z.string().optional(),
  index: z.number().optional(),
  mimeType: z.string().optional(),
});

/**
 * Schema validation cho cập nhật sản phẩm
 */
export const updateProductSchema = z.object({
  productInfo: productInfoSchema,
  images: z.array(imageOperationSchema).optional(),
  detailEdited: z.boolean().optional(),
  userManual: z.boolean().optional(),
  updateOption: z.nativeEnum(UpdateProductOption, {
    errorMap: () => ({ message: 'Tùy chọn cập nhật không hợp lệ' }),
  }),
});

/**
 * Schema validation cho filter sản phẩm
 */
export const productFilterSchema = z.object({
  page: z.number().optional(),
  limit: z.number().optional(),
  search: z.string().optional(),
  status: z.string().optional(),
  category: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
});

/**
 * Type cho form values của tạo sản phẩm
 */
export type CreateProductFormValues = z.infer<typeof createProductSchema>;

/**
 * Type cho form values của cập nhật sản phẩm
 */
export type UpdateProductFormValues = z.infer<typeof updateProductSchema>;

/**
 * Type cho filter values của sản phẩm
 */
export type ProductFilterValues = z.infer<typeof productFilterSchema>;
