import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { CartResponse } from '../types/cart.types';

// Đường dẫn API chung cho marketplace
const MARKETPLACE_API_PATH = '/admin/marketplace';

/**
 * Service cho quản lý giỏ hàng trong marketplace
 */
export const CartService = {
  /**
   * Lấy danh sách giỏ hàng của tất cả người dùng
   * @returns Promise với danh sách giỏ hàng
   */
  getAllCarts: async (): Promise<ApiResponseDto<CartResponse[]>> => {
    const response = await apiClient.get<CartResponse[]>(`${MARKETPLACE_API_PATH}/cart`);
    return response;
  },

  /**
   * Lấy chi tiết giỏ hàng theo ID
   * @param id ID của giỏ hàng
   * @returns Promise với thông tin chi tiết giỏ hàng
   */
  getCartById: async (id: string | number): Promise<ApiResponseDto<CartResponse>> => {
    const response = await apiClient.get<CartResponse>(`${MARKETPLACE_API_PATH}/cart/${id}`);
    return response;
  },
};
