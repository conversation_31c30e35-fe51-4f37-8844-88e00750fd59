import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  CreateProductDto,
  ProductDetailResponse,
  ProductFilterParams,
  ProductListResponse,
  UpdateProductDto,
} from '../types/product.types';

// Đường dẫn API chung cho marketplace
const MARKETPLACE_API_PATH = '/admin/marketplace';

/**
 * Service cho quản lý sản phẩm trong marketplace
 */
export const ProductService = {
  /**
   * Lấy danh sách sản phẩm
   * @param params Tham số filter
   * @returns Promise với danh sách sản phẩm
   */
  getProducts: async (
    params?: ProductFilterParams
  ): Promise<ApiResponseDto<ProductListResponse>> => {
    const response = await apiClient.get<ProductListResponse>(`${MARKETPLACE_API_PATH}/products`, {
      params,
    });
    return response;
  },

  /**
   * <PERSON><PERSON>y chi tiết sản phẩm
   * @param id ID của sản phẩm
   * @returns Promise với chi tiết sản phẩm
   */
  getProduct: async (id: string): Promise<ApiResponseDto<ProductDetailResponse>> => {
    const response = await apiClient.get<ProductDetailResponse>(
      `${MARKETPLACE_API_PATH}/products/${id}`
    );
    return response;
  },

  /**
   * Tạo sản phẩm mới
   * @param data Dữ liệu sản phẩm cần tạo
   * @returns Promise với sản phẩm đã tạo
   */
  createProduct: async (data: CreateProductDto): Promise<ApiResponseDto<ProductDetailResponse>> => {
    const response = await apiClient.post<ProductDetailResponse>(
      `${MARKETPLACE_API_PATH}/products`,
      data
    );
    return response;
  },

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm
   * @param data Dữ liệu sản phẩm cần cập nhật
   * @returns Promise với sản phẩm đã cập nhật
   */
  updateProduct: async (
    id: string,
    data: UpdateProductDto
  ): Promise<ApiResponseDto<ProductDetailResponse>> => {
    const response = await apiClient.put<ProductDetailResponse>(
      `${MARKETPLACE_API_PATH}/products/${id}`,
      data
    );
    return response;
  },

  /**
   * Xóa sản phẩm
   * @param id ID của sản phẩm
   * @returns Promise với kết quả xóa
   */
  deleteProduct: async (id: string): Promise<ApiResponseDto<null>> => {
    const response = await apiClient.delete<null>(`${MARKETPLACE_API_PATH}/products/${id}`);
    return response;
  },

  /**
   * Phê duyệt sản phẩm
   * @param id ID của sản phẩm
   * @returns Promise với sản phẩm đã phê duyệt
   */
  approveProduct: async (id: string): Promise<ApiResponseDto<ProductDetailResponse>> => {
    const response = await apiClient.post<ProductDetailResponse>(
      `${MARKETPLACE_API_PATH}/products/${id}/approve`
    );
    return response;
  },

  /**
   * Từ chối sản phẩm
   * @param id ID của sản phẩm
   * @param reason Lý do từ chối
   * @returns Promise với sản phẩm đã từ chối
   */
  rejectProduct: async (
    id: string,
    reason: string
  ): Promise<ApiResponseDto<ProductDetailResponse>> => {
    const response = await apiClient.post<ProductDetailResponse>(
      `${MARKETPLACE_API_PATH}/products/${id}/reject`,
      { reason }
    );
    return response;
  },

  /**
   * Xóa nhiều sản phẩm
   * @param productIds Mảng ID của các sản phẩm cần xóa
   * @returns Promise với kết quả xóa
   */
  batchDeleteProducts: async (productIds: number[]): Promise<ApiResponseDto<null>> => {
    const response = await apiClient.delete<null>(`${MARKETPLACE_API_PATH}/products/batch`, {
      data: { productIds }
    });
    return response;
  },

  /**
   * Cập nhật trạng thái nhiều sản phẩm
   * @param productIds Mảng ID của các sản phẩm cần cập nhật
   * @param status Trạng thái mới
   * @returns Promise với kết quả cập nhật
   */
  batchUpdateProductStatus: async (
    productIds: number[],
    status: string
  ): Promise<ApiResponseDto<null>> => {
    const response = await apiClient.post<null>(`${MARKETPLACE_API_PATH}/products/batch-status-update`, {
      productIds,
      status
    });
    return response;
  },

  /**
   * Đăng bán sản phẩm (publish)
   * @param id ID của sản phẩm
   * @returns Promise với sản phẩm đã được publish
   */
  publishProduct: async (id: string): Promise<ApiResponseDto<ProductDetailResponse>> => {
    const response = await apiClient.post<ProductDetailResponse>(
      `${MARKETPLACE_API_PATH}/products/${id}/publish`
    );
    return response;
  },
};
