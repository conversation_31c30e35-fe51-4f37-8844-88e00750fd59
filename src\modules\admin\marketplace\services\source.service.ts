import { apiClient } from '@/shared/api/axios';
import { SearchItem } from '@/shared/components/common/SearchInputWithLazyLoading/types';

/**
 * Interface cho tham số phân trang
 */
export interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  items: T[];
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Interface cho Knowledge File response
 */
export interface KnowledgeFileDto {
  id: string;
  name: string;
  description?: string;
  mime?: string;
  storage?: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface cho Type Agent response
 */
export interface TypeAgentDto {
  id: number;
  name: string;
  description?: string;
  avatar?: string;
  category?: string;
  isSystem?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface cho Template Agent response
 */
export interface TemplateAgentDto {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  instruction?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface cho API response với pagination
 */
export interface ApiPaginatedResponse<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Load knowledge files từ API /admin/knowledge-files
 * @param inputValue Từ khóa tìm kiếm
 * @param pagination Thông tin phân trang
 * @returns Promise với danh sách SearchItem hoặc PaginatedResult
 */
export const loadKnowledgeFiles = async (
  inputValue: string,
  pagination?: PaginationParams
): Promise<SearchItem[] | PaginatedResult<SearchItem>> => {
  try {
    const params: Record<string, string | number> = {};

    if (inputValue.trim()) {
      params['search'] = inputValue.trim();
    }

    if (pagination) {
      params['page'] = pagination.page;
      params['limit'] = pagination.limit;
    }

    const response = await apiClient.get<ApiPaginatedResponse<KnowledgeFileDto>>(
      '/admin/knowledge-files',
      {
        params,
        tokenType: 'admin'
      }
    );

    const data = response.result;

    // Transform data to SearchItem format
    const items: SearchItem[] = data.items.map((file) => ({
      id: file.id,
      name: file.name,
      description: file.description || `MIME: ${file.mime || 'Unknown'}`,
      data: file
    }));

    // Return paginated result if pagination info exists
    if (data.meta) {
      return {
        items,
        meta: data.meta
      };
    }

    return items;
  } catch (error) {
    console.error('Error loading knowledge files:', error);
    return [];
  }
};

/**
 * Load type agents từ API /admin/type-agents
 * @param inputValue Từ khóa tìm kiếm theo name
 * @param pagination Thông tin phân trang
 * @returns Promise với danh sách SearchItem hoặc PaginatedResult
 */
export const loadTypeAgents = async (
  inputValue: string,
  pagination?: PaginationParams
): Promise<SearchItem[] | PaginatedResult<SearchItem>> => {
  try {
    const params: Record<string, string | number> = {};

    if (inputValue.trim()) {
      params['search'] = inputValue.trim(); // Tìm kiếm theo name
    }

    if (pagination) {
      params['page'] = pagination.page;
      params['limit'] = pagination.limit;
    }

    const response = await apiClient.get<ApiPaginatedResponse<TypeAgentDto>>(
      '/admin/type-agents',
      {
        params,
        tokenType: 'admin'
      }
    );

    const data = response.result;

    // Transform data to SearchItem format
    const items: SearchItem[] = data.items.map((agent) => ({
      id: agent.id,
      name: agent.name,
      description: agent.description || agent.category || 'Type Agent',
      ...(agent.avatar && { image: agent.avatar }),
      data: agent
    }));

    // Return paginated result if pagination info exists
    if (data.meta) {
      return {
        items,
        meta: data.meta
      };
    }

    return items;
  } catch (error) {
    console.error('Error loading type agents:', error);
    return [];
  }
};

/**
 * Load template agents từ API /admin/agents/template
 * @param inputValue Từ khóa tìm kiếm theo name
 * @param pagination Thông tin phân trang
 * @returns Promise với danh sách SearchItem hoặc PaginatedResult
 */
export const loadTemplateAgents = async (
  inputValue: string,
  pagination?: PaginationParams
): Promise<SearchItem[] | PaginatedResult<SearchItem>> => {
  try {
    const params: Record<string, string | number> = {};

    if (inputValue.trim()) {
      params['search'] = inputValue.trim(); // Tìm kiếm theo name
    }

    if (pagination) {
      params['page'] = pagination.page;
      params['limit'] = pagination.limit;
    }

    const response = await apiClient.get<ApiPaginatedResponse<TemplateAgentDto>>(
      '/admin/agents/template',
      {
        params,
        tokenType: 'admin'
      }
    );

    const data = response.result;

    // Transform data to SearchItem format
    const items: SearchItem[] = data.items.map((agent) => ({
      id: agent.id,
      name: agent.name,
      description: agent.description || 'Template Agent',
      ...(agent.avatar && { image: agent.avatar }),
      data: agent
    }));

    // Return paginated result if pagination info exists
    if (data.meta) {
      return {
        items,
        meta: data.meta
      };
    }

    return items;
  } catch (error) {
    console.error('Error loading template agents:', error);
    return [];
  }
};
