import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Textarea,
  Checkbox,
} from '@/shared/components/common';
import { createPointSchema } from '../../schemas';
import { PointDto, CreatePointDto, UpdatePointDto } from '../../types';
import { usePointData } from '../../hooks';

// Define form value type
export type PointFormValues = z.infer<typeof createPointSchema>;

interface PointFormProps {
  /**
   * Initial data for the form (when editing)
   */
  initialData?: PointDto | null;

  /**
   * Function to handle form cancellation
   */
  onCancel: () => void;
}

/**
 * Form component for creating/editing R-Point packages
 */
const PointForm: React.FC<PointFormProps> = ({
  initialData,
  onCancel,
}) => {
  const { t } = useTranslation(['rpointAdmin', 'common']);
  const isEditMode = !!initialData;
  const [isCustomize, setIsCustomize] = useState(initialData?.isCustomize || false);

  // Sử dụng hooks từ usePointData
  const { useCreatePoint, useUpdatePoint } = usePointData();
  const createPointMutation = useCreatePoint();
  const updatePointMutation = useUpdatePoint();

  // Xác định trạng thái loading
  const isSubmitting = createPointMutation.isPending || updatePointMutation.isPending;

  // Prepare default values for the form
  const defaultValues = initialData ?
    {
      name: initialData.name,
      cash: initialData.cash,
      point: initialData.point,
      description: initialData.description || '',
      isCustomize: initialData.isCustomize || false,
      // Nếu là customize point, sử dụng giá trị từ initialData hoặc giá trị mặc định
      // Nếu không phải customize point, vẫn cần các giá trị mặc định cho các trường này
      // để tránh lỗi khi chuyển từ point thường sang customize
      rate: initialData.isCustomize ? (initialData.rate || 1000) : 1000,
      min: initialData.isCustomize ? (initialData.min || 50000) : 50000,
      max: initialData.isCustomize ? (initialData.max || 500000) : 500000,
    } :
    {
      name: '',
      cash: 0,
      point: 0,
      description: '',
      isCustomize: false,
      rate: 1000, // Default rate value
      min: 50000, // Default min value
      max: 500000, // Default max value
    };

  // Update isCustomize state when initialData changes
  useEffect(() => {
    if (initialData) {
      setIsCustomize(initialData.isCustomize || false);
    } else {
      setIsCustomize(false);
    }
  }, [initialData]);

  // Định nghĩa interface cho formattedValues
  interface FormattedValues {
    name: string;
    cash?: number;
    point?: number;
    description: string;
    isCustomize?: boolean;
    rate?: number | null;
    min?: number | null;
    max?: number | null;
  }

  // Handle form submission
  const handleFormSubmit = async (values: Record<string, unknown>) => {
    console.log('[PointForm] Form submission started');
    console.log('[PointForm] Raw form values:', JSON.stringify(values, null, 2));
    console.log('[PointForm] Is edit mode:', isEditMode);
    console.log('[PointForm] Initial data:', initialData);

    try {
      // Format values for submission
      let formattedValues: FormattedValues;

      // Nếu đang tạo mới (không phải edit mode)
      if (!isEditMode) {
        // Khi tạo mới, chỉ cho phép tạo point thường (không phải customize)
        formattedValues = {
          name: String(values['name'] || ''),
          cash: Number(values['cash'] || 0),
          point: Number(values['point'] || 0),
          description: String(values['description'] || '')
          // Không gửi các trường isCustomize, rate, min, max khi tạo mới
        };

        // Gọi API tạo mới
        console.log('[PointForm] Calling create API with:', JSON.stringify(formattedValues, null, 2));
        await createPointMutation.mutateAsync(formattedValues as CreatePointDto);
        console.log('[PointForm] Create API call successful');
        onCancel(); // Đóng form sau khi tạo thành công
      }
      // Nếu đang chỉnh sửa (edit mode)
      else {
        const isCustomize = Boolean(values['isCustomize']);

        if (!isCustomize) {
          // For non-customize points - không gửi field 'point' theo API spec
          formattedValues = {
            name: String(values['name'] || ''),
            cash: Number(values['cash'] || 0),
            description: String(values['description'] || ''),
            isCustomize: false,
            // Backend sẽ tự tính toán point dựa trên cash và rate
          };
        } else {
          // For customize points (only in edit mode)
          // Make sure we have all required fields for customize points
          const rate = Number(values['rate'] || 1000);
          const min = Number(values['min'] || 50000);
          const max = Number(values['max'] || 500000);

          // Validate required fields
          if (rate <= 0) {
            console.error('Rate must be greater than 0');
            return;
          }

          if (min < 50000) {
            console.error('Min value must be at least 50,000');
            return;
          }

          if (max > 500000) {
            console.error('Max value must be at most 500,000');
            return;
          }

          if (min >= max) {
            console.error('Min value must be less than max value');
            return;
          }

          formattedValues = {
            name: String(values['name'] || ''),
            description: String(values['description'] || ''),
            isCustomize: true,
            // Không gửi cash và point cho customize points
            // Backend sẽ xử lý dựa trên rate, min, max
            rate: rate,
            min: min,
            max: max
          };
        }

        // Gọi API cập nhật
        console.log('[PointForm] Calling update API with:');
        console.log('[PointForm] - ID:', initialData!.id);
        console.log('[PointForm] - Data:', JSON.stringify(formattedValues, null, 2));

        await updatePointMutation.mutateAsync({
          id: initialData!.id,
          data: formattedValues as UpdatePointDto
        });
        console.log('[PointForm] Update API call successful');
        onCancel(); // Đóng form sau khi cập nhật thành công
      }
    } catch (error) {
      // Lỗi đã được xử lý trong hooks
      console.error('Form submission error:', error);
    }
  };

  // Không cần hàm handleCustomizeChange nữa vì đã sử dụng hàm inline

  return (
    <Card
      title={isEditMode ? t('rpointAdmin:points.form.update') : t('rpointAdmin:points.form.create')}
    >
      <Form
        schema={createPointSchema}
        onSubmit={handleFormSubmit}
        className="space-y-6"
        defaultValues={defaultValues}
      >
        <FormItem
          name="name"
          label={t('rpointAdmin:points.form.name')}
          required
        >
          <Input
            placeholder={t('rpointAdmin:points.form.name')}
            fullWidth
          />
        </FormItem>

        <div>
          <FormItem
            name="isCustomize"
            label=""
          >
            <Checkbox
              label={t('rpointAdmin:points.form.isCustomize', 'Customize')}
              checked={isCustomize}
              onChange={(checked) => {
                setIsCustomize(checked);
                // Khi thay đổi checkbox, cập nhật giá trị trong form
                const formElement = document.querySelector('form');
                if (formElement) {
                  const event = new Event('change', { bubbles: true });
                  const customEvent = Object.assign(event, { target: { name: 'isCustomize', value: checked } });
                  formElement.dispatchEvent(customEvent);
                }
              }}
              disabled={!isEditMode}
            />
          </FormItem>
          {!isEditMode && isCustomize && (
            <div className="text-sm text-red-500 mt-1 ml-1">
              {t('rpointAdmin:points.form.customizeEditOnly', 'Customize option is only available when editing an existing point')}
            </div>
          )}
        </div>

        {!isCustomize ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="cash"
              label={t('rpointAdmin:points.form.cash')}
              required
            >
              <Input
                type="number"
                placeholder={t('rpointAdmin:points.form.cash')}
                min={20000}
                max={500000}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="point"
              label={t('rpointAdmin:points.form.point')}
              required
            >
              <Input
                type="number"
                placeholder={t('rpointAdmin:points.form.point')}
                min={1}
                fullWidth
              />
            </FormItem>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormItem
              name="rate"
              label={t('rpointAdmin:points.form.rate', 'Rate')}
              required
            >
              <Input
                type="number"
                placeholder={t('rpointAdmin:points.form.rate', 'Rate')}
                min={1}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="min"
              label={t('rpointAdmin:points.form.min', 'Min')}
              required
            >
              <Input
                type="number"
                placeholder={t('rpointAdmin:points.form.min', 'Min')}
                min={50000}
                max={500000}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="max"
              label={t('rpointAdmin:points.form.max', 'Max')}
              required
            >
              <Input
                type="number"
                placeholder={t('rpointAdmin:points.form.max', 'Max')}
                min={20000}
                max={500000}
                fullWidth
              />
            </FormItem>
          </div>
        )}

        <FormItem
          name="description"
          label={t('rpointAdmin:points.form.description')}
          required
        >
          <Textarea
            placeholder={t('rpointAdmin:points.form.description')}
            rows={4}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-4 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('rpointAdmin:points.form.cancel')}
          </Button>
          <Button type="submit" variant="primary" isLoading={isSubmitting}>
            {t('rpointAdmin:points.form.submit')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default PointForm;
