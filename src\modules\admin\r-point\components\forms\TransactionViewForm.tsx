import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON>ton,
  Card,
  Typography,
  Divider,
  Skeleton,
  ScrollArea,
} from '@/shared/components/common';
import { TransactionDto, TransactionStatus } from '../../types';
import { formatCurrency, formatDateTime } from '@/shared/utils/format';

interface TransactionViewFormProps {
  /**
   * Transaction data to display
   */
  transaction: TransactionDto | null;

  /**
   * Loading state
   */
  isLoading: boolean;

  /**
   * Error state
   */
  error: unknown;

  /**
   * Function to handle form cancellation/close
   */
  onClose: () => void;
}

/**
 * Form component for viewing transaction details
 */
const TransactionViewForm: React.FC<TransactionViewFormProps> = ({
  transaction,
  isLoading,
  error,
  onClose,
}) => {
  const { t } = useTranslation(['rpointAdmin', 'common']);

  // Hàm lấy class cho trạng thái
  const getStatusClass = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.CONFIRMED:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case TransactionStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case TransactionStatus.FAILED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case TransactionStatus.REFUNDED:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full flex flex-col">
        <div className="flex justify-between items-center mb-4 sticky top-0 bg-white dark:bg-gray-800 z-10 py-2">
          <Skeleton className="h-8 w-64" />
          <Button variant="outline" onClick={onClose}>
            {t('common:close')}
          </Button>
        </div>
        <ScrollArea
          height="calc(100vh - 180px)"
          className="flex-1 pr-4"
          autoHide={false}
          direction="vertical"
        >
          <div className="space-y-6">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
            </div>

            <Divider className="my-4" />

            <Skeleton className="h-6 w-32 mb-4" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
            </div>
          </div>
        </ScrollArea>
      </Card>
    );
  }

  if (error || !transaction) {
    return (
      <Card className="w-full flex flex-col">
        <div className="flex justify-between items-center mb-4 sticky top-0 bg-white dark:bg-gray-800 z-10 py-2">
          <Typography variant="h2" className="text-red-500">
            {t('common:error')}
          </Typography>
          <Button variant="outline" onClick={onClose}>
            {t('common:close')}
          </Button>
        </div>
        <ScrollArea
          height="calc(100vh - 180px)"
          className="flex-1 pr-4"
          autoHide={false}
          direction="vertical"
        >
          <Typography variant="body1">
            {t('common:errorLoadingData')}
          </Typography>
        </ScrollArea>
      </Card>
    );
  }

  return (
    <Card className="w-full flex flex-col">
      <div className="flex justify-between items-center mb-4 sticky top-0 bg-white dark:bg-gray-800 z-10 py-2">
        <Typography variant="h4">
          {t('rpointAdmin:transactions.detail.title')} #{transaction.id}
        </Typography>
        <Button variant="outline" onClick={onClose}>
          {t('common:close')}
        </Button>
      </div>

      <ScrollArea
        height="calc(100vh - 180px)"
        className="flex-1 pr-4"
        autoHide={false}
        direction="vertical"
      >
        {/* Thông tin khách hàng */}
        <div className="mb-6">
          <Typography variant="h5" className="mb-4 text-xl font-bold">
            {t('rpointAdmin:transactions.detail.userInfo')}
          </Typography>
          {transaction.user ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('common:name')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {transaction.user.fullName}
                </Typography>
              </div>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('common:email')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {transaction.user.email}
                </Typography>
              </div>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('common:phone')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {transaction.user.phone}
                </Typography>
              </div>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('common:userId')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {transaction.userId}
                </Typography>
              </div>
            </div>
          ) : (
            <Typography variant="body1">
              {t('common:userNotFound')} (ID: {transaction.userId})
            </Typography>
          )}
        </div>

        <Divider className="my-4" />

        {/* Thông tin giao dịch */}
        <div className="mb-6">
          <Typography variant="h5" className="mb-4 text-xl font-bold">
            {t('rpointAdmin:transactions.detail.transactionInfo')}
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.table.amount')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {formatCurrency(transaction.amount)} VND
              </Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.table.pointsAmount')}
              </Typography>
              <Typography variant="body1" className="font-medium text-primary">
                {transaction.pointsAmount}
              </Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.table.status')}
              </Typography>
              <div
                className={`px-3 py-1 rounded-full text-center text-sm font-medium inline-block ${getStatusClass(
                  transaction.status
                )}`}
              >
                {t(`rpointAdmin:transactions.status.${transaction.status}`)}
              </div>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.table.createdAt')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {formatDateTime(transaction.createdAt)}
              </Typography>
            </div>
            {transaction.completedAt && (
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('rpointAdmin:transactions.table.completedAt')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {formatDateTime(transaction.completedAt)}
                </Typography>
              </div>
            )}
          </div>
        </div>

        <Divider className="my-4" />

        {/* Thông tin gói point */}
        <div className="mb-6">
          <Typography variant="h5" className="mb-4 text-xl font-bold">
            {t('common:pointPackage')}
          </Typography>
          {transaction.point ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('common:name')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {transaction.point.name}
                </Typography>
              </div>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('rpointAdmin:points.table.cash')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {formatCurrency(transaction.point.cash)} VND
                </Typography>
              </div>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('rpointAdmin:points.table.rate')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  1:{transaction.point.rate}
                </Typography>
              </div>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('common:packageId')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {transaction.pointId}
                </Typography>
              </div>
            </div>
          ) : (
            <Typography variant="body1">
              {t('common:packageNotFound')} (ID: {transaction.pointId})
            </Typography>
          )}
        </div>

        <Divider className="my-4" />

        {/* Thông tin thanh toán */}
        <div className="mb-6">
          <Typography variant="h5" className="mb-4 text-xl font-bold">
            {t('rpointAdmin:transactions.detail.paymentInfo')}
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.table.paymentMethod')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {transaction.paymentMethod}
              </Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('common:referenceId')}
              </Typography>
              <Typography variant="body1" className="font-medium font-mono">
                {transaction.referenceId}
              </Typography>
            </div>
            {transaction.balanceBefore !== undefined && (
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('common:balanceBefore')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {transaction.balanceBefore}
                </Typography>
              </div>
            )}
            {transaction.balanceAfter !== undefined && (
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('common:balanceAfter')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {transaction.balanceAfter}
                </Typography>
              </div>
            )}
            {transaction.couponId > 0 && (
              <>
                <div>
                  <Typography variant="body2" className="text-gray-500 mb-1">
                    {t('common:couponId')}
                  </Typography>
                  <Typography variant="body1" className="font-medium">
                    {transaction.couponId}
                  </Typography>
                </div>
                <div>
                  <Typography variant="body2" className="text-gray-500 mb-1">
                    {t('common:couponAmount')}
                  </Typography>
                  <Typography variant="body1" className="font-medium">
                    {formatCurrency(transaction.couponAmount)} VND
                  </Typography>
                </div>
              </>
            )}
          </div>
        </div>
      </ScrollArea>
    </Card>
  );
};

export default TransactionViewForm;
