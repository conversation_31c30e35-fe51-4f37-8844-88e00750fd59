  import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CouponService } from '../services';
import { CouponQueryParams, CreateCouponWithoutStatusDto, CouponUpdateRecord } from '../types';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';

/**
 * Hook quản lý dữ liệu coupon
 */
export const useCouponData = () => {
  const queryClient = useQueryClient();

  /**
   * Hook lấy danh sách coupon
   * @param params Tham số truy vấn
   * @returns Danh sách coupon và thông tin phân trang
   */
  const useCoupons = (params: CouponQueryParams) => {
    return useQuery({
      queryKey: ['coupons', params],
      queryFn: () => CouponService.getCoupons(params),
      select: data => data.result,
    });
  };

  /**
   * Hook lấy thông tin chi tiết của một coupon
   * @param id ID của coupon
   * @returns Thông tin chi tiết coupon
   */
  const useCouponDetail = (id: string) => {
    return useQuery({
      queryKey: ['coupon', id],
      queryFn: () => CouponService.getCouponById(id),
      select: data => data.result,
      enabled: !!id,
    });
  };

  /**
   * Hook tạo mới coupon
   */
  const useCreateCoupon = () => {
    return useMutation({
      mutationFn: (data: CreateCouponWithoutStatusDto) => {
        console.log('Sending data to API:', data);
        // Kiểm tra và chuyển đổi dữ liệu trước khi gửi
        const sanitizedData: CreateCouponWithoutStatusDto = {
          ...data,
          // Đảm bảo các trường số là số
          discountValue: Number(data.discountValue),
          minOrderValue: Number(data.minOrderValue),
          startDate: Number(data.startDate),
          endDate: Number(data.endDate),
          perUserLimit: Number(data.perUserLimit),
          // Đảm bảo các trường có thể null
          maxDiscountAmount: data.maxDiscountAmount === null ? null : Number(data.maxDiscountAmount),
          usageLimit: data.usageLimit === null ? null : Number(data.usageLimit),
        };
        return CouponService.createCoupon(sanitizedData);
      },
      onSuccess: (data) => {
        NotificationUtil.success({ message: data.message || 'Tạo mới coupon thành công' });
        queryClient.invalidateQueries({ queryKey: ['coupons'] });
      },
      onError: (error: AxiosError<{ message?: string; detail?: { originalError?: string } }>) => {
        console.error('API Error:', error);
        console.error('Error response:', error.response?.data);

        // Hiển thị thông báo lỗi chi tiết hơn
        let errorMessage = 'Tạo mới coupon thất bại';

        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error.response?.data?.detail?.originalError) {
          errorMessage = `Lỗi: ${error.response.data.detail.originalError}`;
        }

        NotificationUtil.error({ message: errorMessage });
      },
    });
  };

  /**
   * Hook cập nhật thông tin coupon
   */
  const useUpdateCoupon = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string; data: CouponUpdateRecord }) => {
        console.log('Updating coupon with data:', data);
        // Kiểm tra và chuyển đổi dữ liệu trước khi gửi
        const sanitizedData: CouponUpdateRecord = { ...data };

        // Chuyển đổi các trường số
        if (sanitizedData.discountValue !== undefined) {
          sanitizedData.discountValue = Number(sanitizedData.discountValue);
        }

        if (sanitizedData.minOrderValue !== undefined) {
          sanitizedData.minOrderValue = Number(sanitizedData.minOrderValue);
        }

        if (sanitizedData.startDate !== undefined) {
          sanitizedData.startDate = Number(sanitizedData.startDate);
        }

        if (sanitizedData.endDate !== undefined) {
          sanitizedData.endDate = Number(sanitizedData.endDate);
        }

        if (sanitizedData.perUserLimit !== undefined) {
          sanitizedData.perUserLimit = Number(sanitizedData.perUserLimit);
        }

        // Xử lý các trường có thể null
        if (sanitizedData.maxDiscountAmount !== undefined) {
          sanitizedData.maxDiscountAmount = sanitizedData.maxDiscountAmount === null
            ? null
            : Number(sanitizedData.maxDiscountAmount);
        }

        if (sanitizedData.usageLimit !== undefined) {
          sanitizedData.usageLimit = sanitizedData.usageLimit === null
            ? null
            : Number(sanitizedData.usageLimit);
        }

        // Đảm bảo không có trường status
        if ('status' in sanitizedData) {
          delete sanitizedData['status'];
        }

        console.log('Sanitized data for update:', sanitizedData);

        return CouponService.updateCoupon(id, sanitizedData);
      },
      onSuccess: (data) => {
        NotificationUtil.success({ message: data.message || 'Cập nhật coupon thành công' });
        queryClient.invalidateQueries({ queryKey: ['coupons'] });
        queryClient.invalidateQueries({ queryKey: ['coupon'] });
      },
      onError: (error: AxiosError<{ message?: string; detail?: { originalError?: string } }>) => {
        console.error('API Error:', error);
        console.error('Error response:', error.response?.data);

        // Hiển thị thông báo lỗi chi tiết hơn
        let errorMessage = 'Cập nhật coupon thất bại';

        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error.response?.data?.detail?.originalError) {
          errorMessage = `Lỗi: ${error.response.data.detail.originalError}`;
        }

        NotificationUtil.error({ message: errorMessage });
      },
    });
  };

  /**
   * Hook xóa coupon
   */
  const useDeleteCoupon = () => {
    return useMutation({
      mutationFn: (id: string) => CouponService.deleteCoupon(id),
      onSuccess: data => {
        NotificationUtil.success({ message: data.message || 'Xóa coupon thành công' });
        queryClient.invalidateQueries({ queryKey: ['coupons'] });
      },
      onError: (error: AxiosError<{ message: string }>) => {
        NotificationUtil.error({ message: error.response?.data?.message || 'Xóa coupon thất bại' });
      },
    });
  };

  return {
    useCoupons,
    useCouponDetail,
    useCreateCoupon,
    useUpdateCoupon,
    useDeleteCoupon,
  };
};
