import { useQuery } from '@tanstack/react-query';
import { TransactionService } from '../services';
import { TransactionQueryParams } from '../types';

/**
 * Hook quản lý dữ liệu giao dịch
 */
export const useTransactionData = () => {
  /**
   * Hook lấy danh sách giao dịch
   * @param params Tham số truy vấn
   * @returns Danh sách giao dịch và thông tin phân trang
   */
  const useTransactions = (params: TransactionQueryParams) => {
    return useQuery({
      queryKey: ['transactions', params],
      queryFn: () => TransactionService.getTransactions(params),
      select: (data) => data.result,
      staleTime: 1000 * 60, // 1 phút
    });
  };

  /**
   * Hook lấy thông tin chi tiết của một giao dịch
   * @param id ID của giao dịch
   * @param options Tùy chọn cho useQuery
   * @returns Thông tin chi tiết giao dịch
   */
  const useTransactionDetail = (id: number, options?: { enabled?: boolean }) => {
    return useQuery({
      queryKey: ['transaction', id],
      queryFn: () => TransactionService.getTransactionById(id),
      select: (data) => data.result,
      enabled: options?.enabled !== undefined ? options.enabled : !!id,
    });
  };

  /**
   * Hook lấy thống kê về r-point và giao dịch
   * @param startTime Thời gian bắt đầu (tùy chọn)
   * @param endTime Thời gian kết thúc (tùy chọn)
   * @returns Thống kê chi tiết
   */
  const useStatistics = (startTime?: number, endTime?: number) => {
    return useQuery({
      queryKey: ['transaction-statistics', startTime, endTime],
      queryFn: () => TransactionService.getStatistics(startTime, endTime),
      select: data => data.result,
    });
  };

  return {
    useTransactions,
    useTransactionDetail,
    useStatistics,
  };
};
