import React from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Button, Skeleton, IconCard } from '@/shared/components/common';
import { useCouponData } from '../hooks';
import { CouponStatus, DiscountType } from '../types';
import { formatCurrency, formatDateTime } from '@/shared/utils/format';

/**
 * Trang chi tiết mã khuyến mãi
 */
const CouponDetailPage: React.FC = () => {
  const { t } = useTranslation(['r-point', 'common']);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Lấy thông tin chi tiết mã khuyến mãi
  const { useCouponDetail } = useCouponData();
  const { data: coupon, isLoading, error } = useCouponDetail(id || '');

  // Xử lý quay lại
  const handleBack = () => {
    navigate('/admin/r-point/coupons');
  };

  // Xử lý chỉnh sửa
  const handleEdit = () => {
    navigate(`/admin/r-point/coupons/${id}/edit`);
  };

  // Hàm lấy class cho trạng thái
  const getStatusClass = (status: CouponStatus) => {
    switch (status) {
      case CouponStatus.ACTIVE:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case CouponStatus.INACTIVE:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      case CouponStatus.EXPIRED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Button variant="outline" onClick={handleBack} className="mr-4">
            <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
            {t('r-point:coupons.detail.back')}
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <Card className="p-6">
          <Skeleton className="h-6 w-32 mb-4" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-3/4 mb-6" />

          <Skeleton className="h-6 w-32 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-6 w-32 mb-4" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-6 w-32 mb-4" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-6 w-32 mb-4" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-6 w-32 mb-4" />
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (error || !coupon) {
    return (
      <div className="p-6">
        <Button variant="outline" onClick={handleBack} className="mb-6">
          <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
          {t('r-point:coupons.detail.back')}
        </Button>
        <Card className="p-6">
          <Typography variant="h2" className="text-red-500 mb-4">
            {t('common:error')}
          </Typography>
          <Typography variant="body1">{t('common:errorLoadingData')}</Typography>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="outline" onClick={handleBack} className="mr-4">
            <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
            {t('r-point:coupons.detail.back')}
          </Button>
          <Typography variant="h1">{t('r-point:coupons.detail.title')}</Typography>
        </div>
        <Button variant="primary" onClick={handleEdit}>
          <IconCard icon="edit" variant="ghost" size="sm" className="mr-2" />
          {t('common:edit')}
        </Button>
      </div>

      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Typography variant="h2">{coupon.code}</Typography>
          <div
            className={`px-3 py-1 rounded-full text-center text-sm font-medium inline-block ${getStatusClass(
              coupon.status
            )}`}
          >
            {t(`r-point:coupons.status.${coupon.status}`)}
          </div>
        </div>
        <Typography variant="body1" className="mb-6">
          {coupon.description}
        </Typography>

        <Typography variant="h3" className="mb-4">
          {t('common:details')}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('r-point:coupons.table.discountType')}
            </Typography>
            <Typography variant="h4">
              {t(`r-point:coupons.discountType.${coupon.discountType}`)}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('r-point:coupons.table.discountValue')}
            </Typography>
            <Typography variant="h4" className="text-primary">
              {coupon.discountType === DiscountType.PERCENTAGE
                ? `${coupon.discountValue}%`
                : `${formatCurrency(coupon.discountValue)} VND`}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('r-point:coupons.table.minOrderValue')}
            </Typography>
            <Typography variant="h4">{formatCurrency(coupon.minOrderValue)} VND</Typography>
          </div>
          {coupon.discountType === DiscountType.PERCENTAGE && coupon.maxDiscountAmount && (
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('r-point:coupons.table.maxDiscountAmount')}
              </Typography>
              <Typography variant="h4">{formatCurrency(coupon.maxDiscountAmount)} VND</Typography>
            </div>
          )}
        </div>

        <Typography variant="h3" className="mt-6 mb-4">
          {t('common:validity')}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('r-point:coupons.table.startDate')}
            </Typography>
            <Typography variant="h4">{formatDateTime(coupon.startDate)}</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('r-point:coupons.table.endDate')}
            </Typography>
            <Typography variant="h4">{formatDateTime(coupon.endDate)}</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('r-point:coupons.table.usageLimit')}
            </Typography>
            <Typography variant="h4">
              {coupon.usageLimit !== null ? coupon.usageLimit : t('common:unlimited')}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('r-point:coupons.table.perUserLimit')}
            </Typography>
            <Typography variant="h4">{coupon.perUserLimit}</Typography>
          </div>
        </div>

        <Typography variant="h3" className="mt-6 mb-4">
          {t('common:metadata')}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('common:id')}
            </Typography>
            <Typography variant="h4" className="font-mono">
              {coupon.id}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('common:createdAt')}
            </Typography>
            <Typography variant="h4">{formatDateTime(coupon.createdAt)}</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('common:updatedAt')}
            </Typography>
            <Typography variant="h4">{formatDateTime(coupon.updatedAt)}</Typography>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CouponDetailPage;
