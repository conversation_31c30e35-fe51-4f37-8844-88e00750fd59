import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Typography, Modal, Button, ModernMenu } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { CouponDto, CouponStatus, DiscountType, CouponUpdateRecord } from '../types';
import { useCouponData } from '../hooks';
import CouponForm from '../components/forms/CouponForm';
import { formatCurrency } from '@/shared/utils/format';
import { formatDateTime } from '@/shared/utils/date';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang quản lý mã khuyến mãi
 */
const CouponListPage: React.FC = () => {
  const { t } = useTranslation(['rpointAdmin', 'common']);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm, formData, setFormData } = useSlideForm<CouponDto | null>();

  // State quản lý trạng thái hiển thị Modal xác nhận xóa
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    visible: boolean;
    couponId: string | null;
    couponCode: string | null;
  }>({
    visible: false,
    couponId: null,
    couponCode: null
  });

  // Lấy hook từ useCouponData
  const { useCoupons, useDeleteCoupon, useCreateCoupon, useUpdateCoupon } = useCouponData();

  // Mutation xóa coupon
  const deleteCouponMutation = useDeleteCoupon();

  // Mutation tạo mới coupon
  const createCouponMutation = useCreateCoupon();

  // Mutation cập nhật coupon
  const updateCouponMutation = useUpdateCoupon();

  // Xử lý xem chi tiết
  // const handleView = useCallback((coupon: CouponDto) => {
  //   // Chuyển đến trang chi tiết
  //   console.log('View coupon', coupon);
  // }, []);

    // State quản lý menu hành động
    const [actionMenu, setActionMenu] = useState<{
      visible: boolean;
      recordId: string | null;
      recordName: string | null;
    }>({
      visible: false,
      recordId: null,
      recordName: null,
    });

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (coupon: CouponDto) => {
      setFormData(coupon);
      showForm();
    },
    [setFormData, showForm]
  );

  // Xử lý hiển thị xác nhận xóa
  const handleDelete = useCallback((id: string, code: string) => {
    // Hiển thị Modal xác nhận
    setDeleteConfirmation({
      visible: true,
      couponId: id,
      couponCode: code
    });
  }, []);

  // Xử lý xác nhận xóa
  const confirmDelete = useCallback(() => {
    if (deleteConfirmation.couponId) {
      deleteCouponMutation.mutate(deleteConfirmation.couponId);
      // Ẩn Modal sau khi xóa
      setDeleteConfirmation({
        visible: false,
        couponId: null,
        couponCode: null
      });
    }
  }, [deleteConfirmation.couponId, deleteCouponMutation]);

  // Xử lý hủy xóa
  const cancelDelete = useCallback(() => {
    setDeleteConfirmation({
      visible: false,
      couponId: null,
      couponCode: null
    });
  }, []);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<CouponDto>[]>(
    () => [
      {
        key: 'code',
        title: t('rpointAdmin:coupons.table.code'),
        dataIndex: 'code',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="font-mono font-medium">{value as string}</div>;
        },
      },
      {
        key: 'description',
        title: t('rpointAdmin:coupons.table.description'),
        dataIndex: 'description',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="truncate">{value as string}</div>;
        },
      },
      {
        key: 'discount',
        title: t('rpointAdmin:coupons.table.discountValue'),
        dataIndex: 'discountValue',
        width: '10%',
        sortable: true,
        render: (value: unknown, record: CouponDto) => {
          const discountValue = value as number;
          const discountType = record.discountType;

          return (
            <div className="text-center font-medium">
              {discountType === DiscountType.PERCENTAGE
                ? `${discountValue}%`
                : `${formatCurrency(discountValue)} VND`}
            </div>
          );
        },
      },
      {
        key: 'minOrderValue',
        title: t('rpointAdmin:coupons.table.minOrderValue'),
        dataIndex: 'minOrderValue',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{formatCurrency(value as number)} VND</div>;
        },
      },
      {
        key: 'dateRange',
        title: t('rpointAdmin:coupons.table.startDate'),
        dataIndex: 'startDate',
        width: '15%',
        sortable: true,
        render: (value: unknown, record: CouponDto) => {
          // Kiểm tra và xử lý giá trị timestamp không hợp lệ
          const formatSafeDateTime = (timestamp: number | unknown): string => {
            if (timestamp === null || timestamp === undefined) {
              return '-';
            }

            // Kiểm tra xem timestamp có phải là số hợp lệ không
            const numericTimestamp = Number(timestamp);
            if (isNaN(numericTimestamp)) {
              return '-';
            }

            try {
              // Kiểm tra xem timestamp có tạo được đối tượng Date hợp lệ không
              const date = new Date(numericTimestamp);
              if (isNaN(date.getTime())) {
                return '-';
              }

              return formatDateTime(numericTimestamp);
            } catch (error) {
              console.error('Error formatting date:', error);
              return '-';
            }
          };

          return (
            <div>
              <div>{formatSafeDateTime(value)}</div>
              <div className="text-xs text-gray-500">
                {t('common:to')} {formatSafeDateTime(record.endDate)}
              </div>
            </div>
          );
        },
      },
      {
        key: 'status',
        title: t('rpointAdmin:coupons.table.status'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as CouponStatus;
          let statusClass = '';

          switch (status) {
            case CouponStatus.ACTIVE:
              statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
              break;
            case CouponStatus.INACTIVE:
              statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
              break;
            case CouponStatus.EXPIRED:
              statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
              break;
          }

          return (
            <div className={`px-2 py-1 rounded-full text-center text-xs font-medium ${statusClass}`}>
              {t(`rpointAdmin:coupons.status.${status}`)}
            </div>
          );
        },
      },

      {
        key: 'actions',
        title: t('rpointAdmin:coupons.table.actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: CouponDto) => (
          <div className="relative">
            <IconCard
              icon="menu"
              variant="default"
              size="sm"
              onClick={() => {
                setActionMenu({
                  visible: true,
                  recordId: record.id,
                  recordName: record.code,
                });
              }}
            />
            {actionMenu.visible && actionMenu.recordId === record.id && (
              <ModernMenu
                isOpen={true}
                onClose={() => setActionMenu({ visible: false, recordId: null, recordName: null })}
                placement="left"
                items={[
                  {
                    id: 'edit',
                    label: t('common:edit', 'Chỉnh sửa'),
                    icon: 'edit',
                    onClick: () => {
                      handleEdit(record);
                      setActionMenu({ visible: false, recordId: null, recordName: null });
                    },
                  },
                  {
                    id: 'delete',
                    label: t('common:delete', 'Xóa'),
                    icon: 'trash',
                    onClick: () => {
                      handleDelete(record.id, record.code);
                      setActionMenu({ visible: false, recordId: null, recordName: null });
                    },
                  },
                ]}
              />
            )}
          </div>
        ),
      },
    ],
    [t, handleDelete, handleEdit, actionMenu.recordId, actionMenu.visible]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('rpointAdmin:coupons.filter.active'), icon: 'check', value: CouponStatus.ACTIVE },
      { id: 'inactive', label: t('rpointAdmin:coupons.filter.inactive'), icon: 'eye-off', value: CouponStatus.INACTIVE },
      { id: 'expired', label: t('rpointAdmin:coupons.filter.expired'), icon: 'clock', value: CouponStatus.EXPIRED },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    const queryParams: Record<string, unknown> = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams['status'] = params.filterValue;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách coupon với queryParams từ dataTable
  const { data: couponData, isLoading } = useCoupons(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    setFormData(null);
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi startDate và endDate từ chuỗi datetime-local thành timestamp
    const formattedValues = { ...values };

    // Hàm chuyển đổi an toàn từ chuỗi datetime-local sang timestamp
    const safeConvertToTimestamp = (dateTimeString: string): number => {
      try {
        // Đảm bảo chuỗi datetime-local có định dạng hợp lệ
        // Format chuẩn: YYYY-MM-DDTHH:MM
        if (!dateTimeString.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)) {
          // Nếu không phải định dạng chuẩn, thử chuyển đổi từ định dạng hiển thị
          // Format hiển thị: MM/DD/YYYY HH:MM AM/PM
          const parts = dateTimeString.match(/^(\d{2})\/(\d{2})\/(\d{4})\s+(\d{2}):(\d{2})\s+(AM|PM)$/);
          if (parts) {
            // Bỏ qua phần tử đầu tiên (toàn bộ chuỗi khớp)
            const [, month, day, year, hours, minutes, ampm] = parts;
            let hour = parseInt(hours || '0', 10);

            // Chuyển đổi 12h sang 24h
            if (ampm === 'PM' && hour < 12) hour += 12;
            if (ampm === 'AM' && hour === 12) hour = 0;

            // Tạo chuỗi ISO
            const isoString = `${year}-${month}-${day}T${hour.toString().padStart(2, '0')}:${minutes}`;
            const date = new Date(isoString);
            return date.getTime();
          }

          // Nếu không khớp với bất kỳ định dạng nào, sử dụng thời gian hiện tại
          console.warn(`Invalid date format: ${dateTimeString}, using current time instead`);
          return Date.now();
        }

        const date = new Date(dateTimeString);
        const timestamp = date.getTime();

        // Kiểm tra xem timestamp có hợp lệ không
        if (isNaN(timestamp)) {
          console.warn(`Invalid date: ${dateTimeString}, using current time instead`);
          return Date.now();
        }

        return timestamp;
      } catch (error) {
        console.error(`Error converting date: ${dateTimeString}`, error);
        return Date.now(); // Fallback to current time
      }
    };

    // Xử lý startDate
    if (typeof formattedValues['startDate'] === 'string') {
      formattedValues['startDate'] = safeConvertToTimestamp(formattedValues['startDate']);
    }

    // Xử lý endDate
    if (typeof formattedValues['endDate'] === 'string') {
      formattedValues['endDate'] = safeConvertToTimestamp(formattedValues['endDate']);
    }

    // Đảm bảo các trường số được chuyển đổi đúng định dạng
    if (typeof formattedValues['discountValue'] === 'string') {
      formattedValues['discountValue'] = parseFloat(formattedValues['discountValue']) || 0;
    }

    if (typeof formattedValues['minOrderValue'] === 'string') {
      formattedValues['minOrderValue'] = parseFloat(formattedValues['minOrderValue']) || 0;
    }

    if (typeof formattedValues['maxDiscountAmount'] === 'string') {
      const value = parseFloat(formattedValues['maxDiscountAmount']);
      formattedValues['maxDiscountAmount'] = isNaN(value) ? null : value;
    }

    if (typeof formattedValues['usageLimit'] === 'string') {
      const value = parseFloat(formattedValues['usageLimit']);
      formattedValues['usageLimit'] = isNaN(value) ? null : value;
    }

    if (typeof formattedValues['perUserLimit'] === 'string') {
      formattedValues['perUserLimit'] = parseInt(formattedValues['perUserLimit'], 10) || 1;
    }

    // Tạo đối tượng dữ liệu đúng định dạng theo yêu cầu API
    const couponData = {
      code: String(formattedValues['code'] || ''),
      description: String(formattedValues['description'] || ''),
      discountType: formattedValues['discountType'] as DiscountType,
      discountValue: Number(formattedValues['discountValue'] || 0),
      minOrderValue: Number(formattedValues['minOrderValue'] || 0),
      maxDiscountAmount: formattedValues['maxDiscountAmount'] === null || formattedValues['maxDiscountAmount'] === undefined
        ? null
        : Number(formattedValues['maxDiscountAmount']),
        startDate: Number(new Date(String(formattedValues['startDate'] || '')).getTime() || Date.now()),
      endDate: Number(new Date(String(formattedValues['endDate'] || '')).getTime() || (Date.now() + 30 * 24 * 60 * 60 * 1000)),
      usageLimit: formattedValues['usageLimit'] === null || formattedValues['usageLimit'] === undefined
        ? null
        : Number(formattedValues['usageLimit']),
      perUserLimit: Number(formattedValues['perUserLimit'] || 1)
    };

    // Kiểm tra dữ liệu trước khi gửi
    if (!couponData.code) {
      alert('Mã giảm giá không được để trống');
      return;
    }

    if (couponData.discountValue <= 0) {
      alert('Giá trị giảm giá phải lớn hơn 0');
      return;
    }

    if (couponData.startDate >= couponData.endDate) {
      alert('Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc');
      return;
    }

    if (couponData.discountType === DiscountType.PERCENTAGE && couponData.discountValue > 100) {
      alert('Giá trị giảm giá theo phần trăm phải nhỏ hơn hoặc bằng 100%');
      return;
    }

    console.log('Coupon data to be sent:', couponData);

    if (formData) {
      // Cập nhật coupon - chỉ gửi các trường đã thay đổi
      const originalData = {
        code: formData.code,
        description: formData.description,
        discountType: formData.discountType,
        discountValue: formData.discountValue,
        minOrderValue: formData.minOrderValue,
        maxDiscountAmount: formData.maxDiscountAmount,
        startDate: formData.startDate,
        endDate: formData.endDate,
        usageLimit: formData.usageLimit,
        perUserLimit: formData.perUserLimit
      };

      // Tạo object chỉ chứa các trường đã thay đổi
      const changedFields: CouponUpdateRecord = {};

      // So sánh từng trường và chỉ thêm vào changedFields nếu có sự thay đổi
      if (couponData.code !== originalData.code) changedFields.code = couponData.code;
      if (couponData.description !== originalData.description) changedFields.description = couponData.description;
      if (couponData.discountType !== originalData.discountType) changedFields.discountType = couponData.discountType;
      if (couponData.discountValue !== originalData.discountValue) changedFields.discountValue = couponData.discountValue;
      if (couponData.minOrderValue !== originalData.minOrderValue) changedFields.minOrderValue = couponData.minOrderValue;
      if (JSON.stringify(couponData.maxDiscountAmount) !== JSON.stringify(originalData.maxDiscountAmount))
        changedFields.maxDiscountAmount = couponData.maxDiscountAmount;
      if (couponData.startDate !== originalData.startDate) changedFields.startDate = couponData.startDate;
      if (couponData.endDate !== originalData.endDate) changedFields.endDate = couponData.endDate;
      if (JSON.stringify(couponData.usageLimit) !== JSON.stringify(originalData.usageLimit))
        changedFields.usageLimit = couponData.usageLimit;
      if (couponData.perUserLimit !== originalData.perUserLimit) changedFields.perUserLimit = couponData.perUserLimit;

      console.log('Changed fields:', changedFields);

      // Chỉ cập nhật nếu có ít nhất một trường thay đổi
      if (Object.keys(changedFields).length > 0) {
        updateCouponMutation.mutate({
          id: formData.id,
          data: changedFields
        });
      } else {
        console.log('No fields changed, skipping update');
        NotificationUtil.info({ message: 'Không có thông tin nào thay đổi' });
      }
    } else {
      // Tạo mới coupon với dữ liệu từ form
      createCouponMutation.mutate(couponData);
    }
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [CouponStatus.ACTIVE]: t('rpointAdmin:coupons.filter.active'),
      [CouponStatus.INACTIVE]: t('rpointAdmin:coupons.filter.inactive'),
      [CouponStatus.EXPIRED]: t('rpointAdmin:coupons.filter.expired')
    },
    t,
  });

  return (
    <div className="p-6">
      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={deleteConfirmation.visible}
        onClose={cancelDelete}
        size="sm"
        title={t('common:confirmDelete')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={cancelDelete}>
              {t('common:cancel')}
            </Button>
            <Button variant="primary" onClick={confirmDelete}>
              {t('common:delete')}
            </Button>
          </div>
        }
      >
        <div className="text-center py-4">
          <Typography variant="body1">
            {t('rpointAdmin:coupons.confirmDelete', { code: deleteConfirmation.couponCode })}
          </Typography>
        </div>
      </Modal>



      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        <CouponForm onSubmit={handleSubmit} onCancel={handleCancel} initialData={formData} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={couponData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: couponData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: couponData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default CouponListPage;
