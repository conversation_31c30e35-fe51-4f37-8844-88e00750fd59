import React from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Button, Skeleton, IconCard } from '@/shared/components/common';
import { usePointData } from '../hooks';
import { formatCurrency } from '@/shared/utils/format';

/**
 * Trang chi tiết gói R-Point
 */
const PointDetailPage: React.FC = () => {
  const { t } = useTranslation(['rpointAdmin', 'common']);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Lấy thông tin chi tiết gói point
  const { usePointDetail } = usePointData();
  const { data: point, isLoading, error } = usePointDetail(Number(id));

  // Xử lý quay lại
  const handleBack = () => {
    navigate('/admin/r-point/points');
  };

  // Xử lý chỉnh sửa
  const handleEdit = () => {
    navigate(`/admin/r-point/points/${id}/edit`);
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Button variant="outline" onClick={handleBack} className="mr-4">
            <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
            {t('rpointAdmin:points.detail.back')}
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <Card className="p-6">
          <Skeleton className="h-6 w-32 mb-4" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-3/4 mb-6" />

          <Skeleton className="h-6 w-32 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-6 w-32 mb-4" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-6 w-32 mb-4" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-6 w-32 mb-4" />
            </div>
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-6 w-32 mb-4" />
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (error || !point) {
    return (
      <div className="p-6">
        <Button variant="outline" onClick={handleBack} className="mb-6">
          <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
          {t('rpointAdmin:points.detail.back')}
        </Button>
        <Card className="p-6">
          <Typography variant="h2" className="text-red-500 mb-4">
            {t('common:error')}
          </Typography>
          <Typography variant="body1">{t('common:errorLoadingData')}</Typography>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="outline" onClick={handleBack} className="mr-4">
            <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
            {t('rpointAdmin:points.detail.back')}
          </Button>
          <Typography variant="h1">
            {t('rpointAdmin:points.detail.title')}
          </Typography>
        </div>
        <Button variant="primary" onClick={handleEdit}>
          <IconCard icon="edit" variant="ghost" size="sm" className="mr-2" />
          {t('common:edit')}
        </Button>
      </div>

      <Card className="p-6">
        <Typography variant="h2" className="mb-4">
          {point.name}
        </Typography>
        <Typography variant="body1" className="mb-6">
          {point.description || t('common:noDescription')}
        </Typography>

        <Typography variant="h3" className="mb-4">
          {t('common:details')}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:points.table.cash')}
            </Typography>
            <Typography variant="h4">{formatCurrency(point.cash)} VND</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:points.table.point')}
            </Typography>
            <Typography variant="h4" className="text-primary">
              {point.point}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:points.table.rate')}
            </Typography>
            <Typography variant="h4">1:{point.rate}</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:points.table.isCustomize')}
            </Typography>
            <div
              className={`px-3 py-1 rounded-full text-center text-sm font-medium inline-block ${
                point.isCustomize
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              }`}
            >
              {point.isCustomize ? t('rpointAdmin:points.filter.customize') : t('rpointAdmin:points.filter.fixed')}
            </div>
          </div>
        </div>

        {point.isCustomize && (
          <div className="mt-6">
            <Typography variant="h3" className="mb-4">
              {t('common:customizationOptions')}
            </Typography>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('rpointAdmin:points.form.min')}
                </Typography>
                <Typography variant="h4">
                  {point.min !== null ? `${formatCurrency(point.min)} VND` : '-'}
                </Typography>
              </div>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('rpointAdmin:points.form.max')}
                </Typography>
                <Typography variant="h4">
                  {point.max !== null ? `${formatCurrency(point.max)} VND` : '-'}
                </Typography>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default PointDetailPage;
