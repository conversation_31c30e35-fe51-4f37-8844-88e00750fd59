import React from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Button, Skeleton, IconCard, Divider } from '@/shared/components/common';
import { useTransactionData } from '../hooks';
import { TransactionStatus } from '../types';
import { formatCurrency, formatDateTime } from '@/shared/utils/format';

/**
 * Trang chi tiết giao dịch
 */
const TransactionDetailPage: React.FC = () => {
  const { t } = useTranslation(['rpointAdmin', 'common']);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Lấy thông tin chi tiết giao dịch
  const { useTransactionDetail } = useTransactionData();
  const { data: transaction, isLoading, error } = useTransactionDetail(Number(id));

  // Xử lý quay lại
  const handleBack = () => {
    navigate('/admin/r-point/transactions');
  };

  // Hàm lấy class cho trạng thái
  const getStatusClass = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.CONFIRMED:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case TransactionStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case TransactionStatus.FAILED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case TransactionStatus.REFUNDED:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Button variant="outline" onClick={handleBack} className="mr-4">
            <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
            {t('rpointAdmin:transactions.detail.back')}
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <Card className="p-6 mb-6">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
          </div>
        </Card>
        <Card className="p-6">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
          </div>
        </Card>
      </div>
    );
  }

  if (error || !transaction) {
    return (
      <div className="p-6">
        <Button variant="outline" onClick={handleBack} className="mb-6">
          <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
          {t('rpointAdmin:transactions.detail.back')}
        </Button>
        <Card className="p-6">
          <Typography variant="h2" className="text-red-500 mb-4">
            {t('rpointAdmin:transactions.detail.error')}
          </Typography>
          <Typography variant="body1">
            {t('rpointAdmin:transactions.detail.errorLoadingData')}
          </Typography>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Button variant="outline" onClick={handleBack} className="mr-4">
          <IconCard icon="arrow-left" variant="ghost" size="sm" className="mr-2" />
          {t('rpointAdmin:transactions.detail.back')}
        </Button>
        <Typography variant="h1">
          {t('rpointAdmin:transactions.detail.title', { id: transaction.id })}
        </Typography>
      </div>

      {/* Thông tin khách hàng */}
      <Card className="p-6 mb-6">
        <Typography variant="h2" className="mb-4">
          {t('rpointAdmin:transactions.detail.userInfo')}
        </Typography>
        {transaction.user ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.detail.name')}
              </Typography>
              <Typography variant="h4">{transaction.user.fullName}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.detail.email')}
              </Typography>
              <Typography variant="h4">{transaction.user.email}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.detail.phone')}
              </Typography>
              <Typography variant="h4">{transaction.user.phone}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.detail.userId')}
              </Typography>
              <Typography variant="h4">{transaction.userId}</Typography>
            </div>
          </div>
        ) : (
          <Typography variant="body1">
            {t('rpointAdmin:transactions.detail.userNotFound', { id: transaction.userId })}
          </Typography>
        )}
      </Card>

      {/* Thông tin giao dịch */}
      <Card className="p-6 mb-6">
        <Typography variant="h2" className="mb-4">
          {t('rpointAdmin:transactions.detail.transactionInfo')}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:transactions.table.amount')}
            </Typography>
            <Typography variant="h4">{formatCurrency(transaction.amount)} VND</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:transactions.table.pointsAmount')}
            </Typography>
            <Typography variant="h4" className="text-primary">
              {transaction.pointsAmount}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:transactions.table.status')}
            </Typography>
            <div
              className={`px-3 py-1 rounded-full text-center text-sm font-medium inline-block ${getStatusClass(
                transaction.status
              )}`}
            >
              {t(`rpointAdmin:transactions.status.${transaction.status}`)}
            </div>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:transactions.table.createdAt')}
            </Typography>
            <Typography variant="h4">{formatDateTime(transaction.createdAt)}</Typography>
          </div>
          {transaction.completedAt && (
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.table.completedAt')}
              </Typography>
              <Typography variant="h4">{formatDateTime(transaction.completedAt)}</Typography>
            </div>
          )}
        </div>

        <Divider className="my-6" />

        <Typography variant="h3" className="mb-4">
          {t('rpointAdmin:transactions.detail.pointPackageInfo')}
        </Typography>
        {transaction.point ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.detail.packageName')}
              </Typography>
              <Typography variant="h4">{transaction.point.name}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:points.table.cash')}
              </Typography>
              <Typography variant="h4">{formatCurrency(transaction.point.cash)} VND</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:points.table.rate')}
              </Typography>
              <Typography variant="h4">1:{transaction.point.rate}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                {t('rpointAdmin:transactions.detail.packageId')}
              </Typography>
              <Typography variant="h4">{transaction.pointId}</Typography>
            </div>
          </div>
        ) : (
          <Typography variant="body1">
            {t('rpointAdmin:transactions.detail.packageNotFound', { id: transaction.pointId })}
          </Typography>
        )}
      </Card>

      {/* Thông tin thanh toán */}
      <Card className="p-6">
        <Typography variant="h2" className="mb-4">
          {t('rpointAdmin:transactions.detail.paymentInfo')}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:transactions.table.paymentMethod')}
            </Typography>
            <Typography variant="h4">{transaction.paymentMethod}</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:transactions.detail.referenceId')}
            </Typography>
            <Typography variant="h4" className="font-mono">
              {transaction.referenceId}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:transactions.detail.balanceBefore')}
            </Typography>
            <Typography variant="h4">{transaction.balanceBefore}</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500 mb-1">
              {t('rpointAdmin:transactions.detail.balanceAfter')}
            </Typography>
            <Typography variant="h4">{transaction.balanceAfter}</Typography>
          </div>
          {transaction.couponId > 0 && (
            <>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('rpointAdmin:transactions.detail.couponId')}
                </Typography>
                <Typography variant="h4">{transaction.couponId}</Typography>
              </div>
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  {t('rpointAdmin:transactions.detail.couponAmount')}
                </Typography>
                <Typography variant="h4">{formatCurrency(transaction.couponAmount)} VND</Typography>
              </div>
            </>
          )}
        </div>
      </Card>
    </div>
  );
};

export default TransactionDetailPage;
