// <PERSON><PERSON><PERSON> nghĩa routes cho module r-point
import { RouteObject } from 'react-router-dom';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Suspense } from 'react';
import { Loading } from '@/shared';
import { lazy } from 'react';

const RPointDashboardPage = lazy(() => import('../pages/RPointDashboardPage'));
const PointListPage = lazy(() => import('../pages/PointListPage'));
const PointDetailPage = lazy(() => import('../pages/PointDetailPage'));
const TransactionListPage = lazy(() => import('../pages/TransactionListPage'));
const TransactionDetailPage = lazy(() => import('../pages/TransactionDetailPage'));
const CouponListPage = lazy(() => import('../pages/CouponListPage'));
const CouponDetailPage = lazy(() => import('../pages/CouponDetailPage'));

/**
 * Routes cho module R-Point admin
 */
export const rpointAdminRoutes: RouteObject[] = [
  {
    path: '/admin/r-point',
    element: (
      <AdminLayout title="Gói R-Point">
        <Suspense fallback={<Loading />}>
          <RPointDashboardPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/r-point/points',
    element: (
      <AdminLayout title="Gói R-Point">
        <Suspense fallback={<Loading />}>
          <PointListPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/r-point/points/:id',
    element: (
      <AdminLayout title="Gói R-Point">
        <Suspense fallback={<Loading />}>
          <PointDetailPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/r-point/transactions',
    element: (
      <AdminLayout title="Gói R-Point">
        <Suspense fallback={<Loading />}>
          <TransactionListPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/r-point/transactions/:id',
    element: (
      <AdminLayout title="Gói R-Point">
        <Suspense fallback={<Loading />}>
          <TransactionDetailPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/r-point/coupons',
    element: (
      <AdminLayout title="Gói R-Point">
        <Suspense fallback={<Loading />}>
          <CouponListPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/r-point/coupons/:id',
    element: (
      <AdminLayout title="Gói R-Point">
        <Suspense fallback={<Loading />}>
          <CouponDetailPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];
