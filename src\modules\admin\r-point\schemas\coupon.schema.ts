import { z } from 'zod';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { CouponStatus, DiscountType } from '../types/enums';

/**
 * Schema cho thông tin coupon
 */
export const couponSchema = z.object({
  id: z.string().uuid(),
  code: z.string(),
  description: z.string(),
  discountType: z.nativeEnum(DiscountType),
  discountValue: z.coerce.number().min(0),
  minOrderValue: z.coerce.number().min(0),
  maxDiscountAmount: z.coerce.number().nullable(),
  startDate: z.coerce.number(),
  endDate: z.coerce.number(),
  usageLimit: z.coerce.number().nullable(),
  perUserLimit: z.coerce.number().min(1),
  status: z.nativeEnum(CouponStatus),
  createdAt: z.coerce.number(),
  updatedAt: z.coerce.number(),
});

/**
 * Schema cho tham số truy vấn danh sách coupon
 */
export const couponQuerySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(10),
  search: z.string().optional(),
  status: z.nativeEnum(CouponStatus).optional(),
  sortBy: z.string().optional(),
  sortDirection: z.nativeEnum(SortDirection).optional(),
});

/**
 * Schema cho dữ liệu tạo mới coupon
 */
export const createCouponSchema = z.object({
  code: z.string().min(1, 'Mã giảm giá không được để trống'),
  description: z.string(),
  discountType: z.nativeEnum(DiscountType),
  discountValue: z.coerce.number().min(0, 'Giá trị giảm giá phải lớn hơn hoặc bằng 0'),
  minOrderValue: z.coerce.number().min(0, 'Giá trị đơn hàng tối thiểu phải lớn hơn hoặc bằng 0'),
  maxDiscountAmount: z.coerce.number().nullable().optional(),
  startDate: z.preprocess(
    (val) => {
      // Nếu là chuỗi, thử chuyển đổi thành timestamp
      if (typeof val === 'string') {
        try {
          const date = new Date(val);
          const timestamp = date.getTime();
          return isNaN(timestamp) ? Date.now() : timestamp;
        } catch {
          return Date.now(); // Fallback to current time
        }
      }
      // Nếu là số, sử dụng trực tiếp
      if (typeof val === 'number') {
        return isNaN(val) ? Date.now() : val;
      }
      // Mặc định sử dụng thời gian hiện tại
      return Date.now();
    },
    z.number()
  ),
  endDate: z.preprocess(
    (val) => {
      // Nếu là chuỗi, thử chuyển đổi thành timestamp
      if (typeof val === 'string') {
        try {
          const date = new Date(val);
          const timestamp = date.getTime();
          return isNaN(timestamp) ? (Date.now() + 30 * 24 * 60 * 60 * 1000) : timestamp;
        } catch {
          return Date.now() + 30 * 24 * 60 * 60 * 1000; // Fallback to 30 days from now
        }
      }
      // Nếu là số, sử dụng trực tiếp
      if (typeof val === 'number') {
        return isNaN(val) ? (Date.now() + 30 * 24 * 60 * 60 * 1000) : val;
      }
      // Mặc định sử dụng thời gian hiện tại + 30 ngày
      return Date.now() + 30 * 24 * 60 * 60 * 1000;
    },
    z.number()
  ),
  usageLimit: z.coerce.number().nullable().optional(),
  perUserLimit: z.coerce.number().min(1, 'Số lần sử dụng cho mỗi người dùng phải lớn hơn hoặc bằng 1'),
  status: z.nativeEnum(CouponStatus),
}).refine(data => {
  // Nếu là giảm giá theo phần trăm, discountValue phải <= 100
  if (data.discountType === DiscountType.PERCENTAGE) {
    return data.discountValue <= 100;
  }
  return true;
}, {
  message: 'Giá trị giảm giá theo phần trăm phải nhỏ hơn hoặc bằng 100%',
  path: ['discountValue'],
}).refine(data => {
  // Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc
  return data.startDate < data.endDate;
}, {
  message: 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc',
  path: ['startDate'],
});

/**
 * Schema cho dữ liệu cập nhật coupon
 */
export const updateCouponSchema = z.object({
  code: z.string().min(1, 'Mã giảm giá không được để trống').optional(),
  description: z.string().optional(),
  discountType: z.nativeEnum(DiscountType).optional(),
  discountValue: z.coerce.number().min(0, 'Giá trị giảm giá phải lớn hơn hoặc bằng 0').optional(),
  minOrderValue: z.coerce.number().min(0, 'Giá trị đơn hàng tối thiểu phải lớn hơn hoặc bằng 0').optional(),
  maxDiscountAmount: z.coerce.number().nullable().optional(),
  startDate: z.preprocess(
    (val) => {
      if (val === undefined) return undefined;
      // Nếu là chuỗi, thử chuyển đổi thành timestamp
      if (typeof val === 'string') {
        try {
          const date = new Date(val);
          const timestamp = date.getTime();
          return isNaN(timestamp) ? Date.now() : timestamp;
        } catch {
          return Date.now(); // Fallback to current time
        }
      }
      // Nếu là số, sử dụng trực tiếp
      if (typeof val === 'number') {
        return isNaN(val) ? Date.now() : val;
      }
      // Mặc định sử dụng thời gian hiện tại
      return Date.now();
    },
    z.number().optional()
  ),
  endDate: z.preprocess(
    (val) => {
      if (val === undefined) return undefined;
      // Nếu là chuỗi, thử chuyển đổi thành timestamp
      if (typeof val === 'string') {
        try {
          const date = new Date(val);
          const timestamp = date.getTime();
          return isNaN(timestamp) ? (Date.now() + 30 * 24 * 60 * 60 * 1000) : timestamp;
        } catch {
          return Date.now() + 30 * 24 * 60 * 60 * 1000; // Fallback to 30 days from now
        }
      }
      // Nếu là số, sử dụng trực tiếp
      if (typeof val === 'number') {
        return isNaN(val) ? (Date.now() + 30 * 24 * 60 * 60 * 1000) : val;
      }
      // Mặc định sử dụng thời gian hiện tại + 30 ngày
      return Date.now() + 30 * 24 * 60 * 60 * 1000;
    },
    z.number().optional()
  ),
  usageLimit: z.coerce.number().nullable().optional(),
  perUserLimit: z.coerce.number().min(1, 'Số lần sử dụng cho mỗi người dùng phải lớn hơn hoặc bằng 1').optional(),
  status: z.nativeEnum(CouponStatus).optional(),
});
