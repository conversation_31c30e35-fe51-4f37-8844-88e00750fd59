import { z } from 'zod';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { TransactionStatus } from '../types/enums';

/**
 * Schema cho thông tin point trong transaction
 */
export const transactionPointSchema = z.object({
  id: z.coerce.number(),
  name: z.string(),
  cash: z.coerce.number(),
  rate: z.coerce.number(),
});

/**
 * Schema cho thông tin user trong transaction
 */
export const transactionUserSchema = z.object({
  id: z.coerce.number(),
  fullName: z.string(),
  email: z.string().email(),
  phone: z.string(),
});

/**
 * Schema cho thông tin giao dịch
 */
export const transactionSchema = z.object({
  id: z.coerce.number(),
  userId: z.coerce.number(),
  user: transactionUserSchema.nullable(),
  amount: z.coerce.number(),
  pointsAmount: z.coerce.number(),
  pointId: z.coerce.number(),
  point: transactionPointSchema.nullable(),
  status: z.nativeEnum(TransactionStatus),
  paymentMethod: z.string(),
  referenceId: z.string(),
  balanceBefore: z.coerce.number(),
  balanceAfter: z.coerce.number(),
  couponId: z.coerce.number(),
  couponAmount: z.coerce.number(),
  createdAt: z.coerce.number(),
  completedAt: z.coerce.number(),
});

/**
 * Schema cho tham số truy vấn danh sách giao dịch
 */
export const transactionQuerySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(10),
  userId: z.coerce.number().optional(),
  status: z.nativeEnum(TransactionStatus).optional(),
  keyword: z.string().optional(),
  startTime: z.coerce.number().optional(),
  endTime: z.coerce.number().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.nativeEnum(SortDirection).optional(),
}).refine(data => {
  // Nếu có startTime và endTime, startTime phải nhỏ hơn endTime
  if (data.startTime !== undefined && data.endTime !== undefined) {
    return data.startTime < data.endTime;
  }
  return true;
}, {
  message: 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc',
  path: ['startTime'],
});

/**
 * Schema cho thống kê về r-point và giao dịch
 */
export const statisticsSchema = z.object({
  totalTransactions: z.coerce.number(),
  successfulTransactions: z.coerce.number(),
  failedTransactions: z.coerce.number(),
  pendingTransactions: z.coerce.number(),
  totalAmount: z.coerce.number(),
  totalPointsSold: z.coerce.number(),
  uniqueUsers: z.coerce.number(),
  averageTransactionValue: z.coerce.number(),
  dailyStats: z.array(z.object({
    date: z.coerce.number(),
    transactions: z.coerce.number(),
    amount: z.coerce.number(),
    points: z.coerce.number(),
  })),
});
