import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { CouponDto, CouponQueryParams, CreateCouponWithoutStatusDto, CouponUpdateRecord } from '../types';
import { apiClient } from '@/shared/api';

/**
 * Service xử lý các API liên quan đến coupon
 */
export const CouponService = {
  /**
   * Lấy danh sách coupon với phân trang và lọc
   * @param params Tham số truy vấn
   * @returns Danh sách coupon và thông tin phân trang
   */
  async getCoupons(params: CouponQueryParams): Promise<ApiResponseDto<PaginatedResult<CouponDto>>> {
    return apiClient.get('/admin/r-point/coupons', { params });
  },

  /**
   * Lấy thông tin chi tiết của một coupon
   * @param id ID của coupon
   * @returns Thông tin chi tiết coupon
   */
  async getCouponById(id: string): Promise<ApiResponseDto<CouponDto>> {
    return apiClient.get(`/admin/r-point/coupons/${id}`);
  },

  /**
   * Tạo mới coupon
   * @param data Thông tin coupon cần tạo
   * @returns Coupon đã tạo
   */
  async createCoupon(data: CreateCouponWithoutStatusDto): Promise<ApiResponseDto<CouponDto>> {
    console.log('CouponService.createCoupon - data:', data);
    try {
      const response = await apiClient.post<CouponDto>('/admin/r-point/coupons', data);
      console.log('CouponService.createCoupon - response:', response);
      return response;
    } catch (error) {
      console.error('CouponService.createCoupon - error:', error);
      throw error;
    }
  },

  /**
   * Cập nhật thông tin coupon
   * @param id ID của coupon
   * @param data Thông tin cần cập nhật
   * @returns Coupon đã cập nhật
   */
  async updateCoupon(id: string, data: CouponUpdateRecord): Promise<ApiResponseDto<CouponDto>> {
    console.log('CouponService.updateCoupon - data:', data);
    try {
      const response = await apiClient.put<CouponDto>(`/admin/r-point/coupons/${id}`, data);
      console.log('CouponService.updateCoupon - response:', response);
      return response;
    } catch (error) {
      console.error('CouponService.updateCoupon - error:', error);
      throw error;
    }
  },

  /**
   * Xóa coupon
   * @param id ID của coupon
   * @returns Thông báo xóa thành công
   */
  async deleteCoupon(id: string): Promise<ApiResponseDto<{ success: boolean }>> {
    return apiClient.delete(`/admin/r-point/coupons/${id}`);
  },
};
