import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { StatisticsDto, TransactionDto, TransactionQueryParams } from '../types';
import { apiClient } from '@/shared/api';

/**
 * Service xử lý các API liên quan đến giao dịch
 */
export const TransactionService = {
  /**
   * L<PERSON>y danh sách giao dịch với phân trang và lọc
   * @param params Tham số truy vấn
   * @returns Danh sách giao dịch và thông tin phân trang
   */
  async getTransactions(params: TransactionQueryParams): Promise<ApiResponseDto<PaginatedResult<TransactionDto>>> {
    try {
      console.log('Fetching transactions with params:', params);
      const response = await apiClient.get<PaginatedResult<TransactionDto>>('/admin/r-point/transactions', { params });
      console.log('Transactions response:', response);
      return response;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }
  },

  /**
   * <PERSON><PERSON>y thông tin chi tiết của một giao dịch
   * @param id ID của giao dịch
   * @returns Thông tin chi tiết giao dịch
   */
  async getTransactionById(id: number): Promise<ApiResponseDto<TransactionDto>> {
    return apiClient.get<TransactionDto>(`/admin/r-point/transactions/${id}`);
  },

  /**
   * Lấy thống kê về r-point và giao dịch
   * @param startTime Thời gian bắt đầu (tùy chọn)
   * @param endTime Thời gian kết thúc (tùy chọn)
   * @returns Thống kê chi tiết
   */
  async getStatistics(
    startTime?: number,
    endTime?: number
  ): Promise<ApiResponseDto<StatisticsDto>> {
    const params: Record<string, number> = {};
    if (startTime) params['startTime'] = startTime;
    if (endTime) params['endTime'] = endTime;

    return apiClient.get<StatisticsDto>('/admin/r-point/transactions/statistics/overview', { params });
  },
};
