import { SortDirection } from '@/shared/dto/request/query.dto';
import { CouponStatus, DiscountType } from './enums';

/**
 * Interface cho thông tin coupon
 */
export interface CouponDto {
  /**
   * ID của coupon
   */
  id: string;

  /**
   * Mã giảm giá
   */
  code: string;

  /**
   * Mô tả chi tiết về mã giảm giá
   */
  description: string;

  /**
   * Loại giảm giá: phần trăm hoặc số tiền cố định
   */
  discountType: DiscountType;

  /**
   * Giá trị giảm giá tương ứng với loại (%, số tiền)
   */
  discountValue: number;

  /**
   * Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá
   */
  minOrderValue: number;

  /**
   * Giảm giá tối đa cho mã giảm giá loại phần trăm
   */
  maxDiscountAmount: number | null;

  /**
   * Thời điểm bắt đầu áp dụng mã (Unix timestamp)
   */
  startDate: number;

  /**
   * Thời điểm kết thúc áp dụng mã (Unix timestamp)
   */
  endDate: number;

  /**
   * Tổng số lần sử dụng tối đa cho toàn bộ hệ thống
   */
  usageLimit: number | null;

  /**
   * Số lần một người dùng được sử dụng mã này
   */
  perUserLimit: number;

  /**
   * Trạng thái mã giảm giá
   */
  status: CouponStatus;

  /**
   * Thời gian tạo mã (Unix timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật mã gần nhất (Unix timestamp)
   */
  updatedAt: number;
}

/**
 * Interface cho tham số truy vấn danh sách coupon
 */
export interface CouponQueryParams {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng item trên mỗi trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Lọc theo trạng thái
   */
  status?: CouponStatus;

  /**
   * Sắp xếp theo trường
   */
  sortBy?: string;

  /**
   * Thứ tự sắp xếp
   */
  sortDirection?: SortDirection;
}

/**
 * Interface cho dữ liệu tạo mới coupon
 */
export interface CreateCouponDto {
  /**
   * Mã giảm giá
   */
  code: string;

  /**
   * Mô tả chi tiết về mã giảm giá
   */
  description: string;

  /**
   * Loại giảm giá: phần trăm hoặc số tiền cố định
   */
  discountType: DiscountType;

  /**
   * Giá trị giảm giá tương ứng với loại (%, số tiền)
   */
  discountValue: number;

  /**
   * Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá
   */
  minOrderValue: number;

  /**
   * Giảm giá tối đa cho mã giảm giá loại phần trăm
   */
  maxDiscountAmount?: number | null;

  /**
   * Thời điểm bắt đầu áp dụng mã (Unix timestamp)
   */
  startDate: number;

  /**
   * Thời điểm kết thúc áp dụng mã (Unix timestamp)
   */
  endDate: number;

  /**
   * Tổng số lần sử dụng tối đa cho toàn bộ hệ thống
   */
  usageLimit?: number | null;

  /**
   * Số lần một người dùng được sử dụng mã này
   */
  perUserLimit: number;

  /**
   * Trạng thái mã giảm giá
   */
  status: CouponStatus;
}

/**
 * Type cho dữ liệu cập nhật coupon
 */
export type UpdateCouponDto = Partial<CreateCouponDto>;

/**
 * Interface cho dữ liệu tạo mới coupon không có trường status
 */
export interface CreateCouponWithoutStatusDto {
  /**
   * Mã giảm giá
   */
  code: string;

  /**
   * Mô tả chi tiết về mã giảm giá
   */
  description: string;

  /**
   * Loại giảm giá: phần trăm hoặc số tiền cố định
   */
  discountType: DiscountType;

  /**
   * Giá trị giảm giá tương ứng với loại (%, số tiền)
   */
  discountValue: number;

  /**
   * Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá
   */
  minOrderValue: number;

  /**
   * Giảm giá tối đa cho mã giảm giá loại phần trăm
   */
  maxDiscountAmount?: number | null;

  /**
   * Thời điểm bắt đầu áp dụng mã (Unix timestamp)
   */
  startDate: number;

  /**
   * Thời điểm kết thúc áp dụng mã (Unix timestamp)
   */
  endDate: number;

  /**
   * Tổng số lần sử dụng tối đa cho toàn bộ hệ thống
   */
  usageLimit?: number | null;

  /**
   * Số lần một người dùng được sử dụng mã này
   */
  perUserLimit: number;
}

/**
 * Type cho dữ liệu cập nhật coupon không có trường status
 */
export type UpdateCouponWithoutStatusDto = Partial<CreateCouponWithoutStatusDto>;

/**
 * Interface cho dữ liệu cập nhật coupon dạng Record
 */
export interface CouponUpdateRecord {
  code?: string;
  description?: string;
  discountType?: DiscountType;
  discountValue?: number;
  minOrderValue?: number;
  maxDiscountAmount?: number | null;
  startDate?: number;
  endDate?: number;
  usageLimit?: number | null;
  perUserLimit?: number;
  [key: string]: unknown;
}
