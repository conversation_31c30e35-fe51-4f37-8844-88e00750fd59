import { SortDirection } from '@/shared/dto/request/query.dto';
import { TransactionStatus } from './enums';

/**
 * Interface cho thông tin point trong transaction
 */
export interface TransactionPointDto {
  /**
   * ID của gói point
   */
  id: number;

  /**
   * Tên của gói point
   */
  name: string;

  /**
   * Số tiền của gói point
   */
  cash: number;

  /**
   * Tỷ lệ quy đổi
   */
  rate: number;
}

/**
 * Interface cho thông tin user trong transaction
 */
export interface TransactionUserDto {
  /**
   * ID của người dùng
   */
  id: number;

  /**
   * Tên của người dùng
   */
  fullName: string;

  /**
   * Email của người dùng
   */
  email: string;

  /**
   * Số điện thoại của người dùng
   */
  phone: string;
}

/**
 * Interface cho thông tin giao dịch
 */
export interface TransactionDto {
  /**
   * ID của giao dịch
   */
  id: number;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Thông tin người dùng
   */
  user: TransactionUserDto | null;

  /**
   * Số tiền giao dịch
   */
  amount: number;

  /**
   * Số lượng point mua
   */
  pointsAmount: number;

  /**
   * ID của gói point
   */
  pointId: number;

  /**
   * Thông tin gói point
   */
  point: TransactionPointDto | null;

  /**
   * Trạng thái giao dịch
   */
  status: TransactionStatus;

  /**
   * Phương thức thanh toán
   */
  paymentMethod: string;

  /**
   * Mã tham chiếu từ cổng thanh toán
   */
  referenceId: string;

  /**
   * Số dư trước giao dịch
   */
  balanceBefore: number;

  /**
   * Số dư sau giao dịch
   */
  balanceAfter: number;

  /**
   * ID của coupon sử dụng
   */
  couponId: number;

  /**
   * Số tiền giảm giá từ coupon
   */
  couponAmount: number;

  /**
   * Thời gian tạo giao dịch
   */
  createdAt: number;

  /**
   * Thời gian hoàn thành giao dịch
   */
  completedAt: number;
}

/**
 * Interface cho tham số truy vấn danh sách giao dịch
 */
export interface TransactionQueryParams {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng item trên mỗi trang
   */
  limit?: number;

  /**
   * ID của người dùng
   */
  userId?: number;

  /**
   * Trạng thái giao dịch
   */
  status?: TransactionStatus;

  /**
   * Từ khóa tìm kiếm
   */
  keyword?: string;

  /**
   * Thời gian bắt đầu (Unix timestamp)
   */
  startTime?: number;

  /**
   * Thời gian kết thúc (Unix timestamp)
   */
  endTime?: number;

  /**
   * Sắp xếp theo trường
   */
  sortBy?: string;

  /**
   * Thứ tự sắp xếp
   */
  sortDirection?: SortDirection;
}

/**
 * Interface cho thống kê về r-point và giao dịch
 */
export interface StatisticsDto {
  /**
   * Tổng số giao dịch
   */
  totalTransactions: number;

  /**
   * Tổng số giao dịch thành công
   */
  successfulTransactions: number;

  /**
   * Tổng số giao dịch thất bại
   */
  failedTransactions: number;

  /**
   * Tổng số giao dịch đang chờ xử lý
   */
  pendingTransactions: number;

  /**
   * Tổng số tiền giao dịch
   */
  totalAmount: number;

  /**
   * Tổng số point đã bán
   */
  totalPointsSold: number;

  /**
   * Số lượng người dùng đã mua point
   */
  uniqueUsers: number;

  /**
   * Giá trị giao dịch trung bình
   */
  averageTransactionValue: number;

  /**
   * Thống kê theo ngày
   */
  dailyStats: {
    /**
     * Ngày (Unix timestamp)
     */
    date: number;

    /**
     * Số lượng giao dịch
     */
    transactions: number;

    /**
     * Tổng số tiền
     */
    amount: number;

    /**
     * Tổng số point
     */
    points: number;
  }[];
}
