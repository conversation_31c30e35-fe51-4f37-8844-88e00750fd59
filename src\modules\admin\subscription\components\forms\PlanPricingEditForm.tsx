import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Input,
  Select,
  FormItem,
  Card,
  Toggle,
} from '@/shared/components/common';
import { PlanPricing, BillingCycle, UsageUnit, UpdatePlanPricingDto } from '../../types/plan-pricing.admin.types';

interface PlanPricingEditFormProps {
  pricing: PlanPricing;
  onSubmit: (id: number, data: UpdatePlanPricingDto) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Form chỉnh sửa tùy chọn giá
 */
const PlanPricingEditForm: React.FC<PlanPricingEditFormProps> = ({
  pricing,
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho form fields
  const [formData, setFormData] = useState({
    planId: pricing.planId.toString(),
    billingCycle: pricing.billingCycle,
    price: pricing.price,
    usageLimit: pricing.usageLimit,
    usageUnit: pricing.usageUnit,
    isActive: pricing.isActive,
  });

  // State cho errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update form data khi pricing prop thay đổi
  useEffect(() => {
    setFormData({
      planId: pricing.planId.toString(),
      billingCycle: pricing.billingCycle,
      price: pricing.price,
      usageLimit: pricing.usageLimit,
      usageUnit: pricing.usageUnit,
      isActive: pricing.isActive,
    });
  }, [pricing]);

  // Xử lý thay đổi planId
  const handlePlanIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, planId: value }));
    if (errors['planId']) {
      setErrors(prev => ({ ...prev, ['planId']: '' }));
    }
  };

  // Xử lý thay đổi billingCycle
  const handleBillingCycleChange = (value: string | number | string[] | number[]) => {
    setFormData(prev => ({ ...prev, billingCycle: value as BillingCycle }));
    if (errors['billingCycle']) {
      setErrors(prev => ({ ...prev, ['billingCycle']: '' }));
    }
  };

  // Xử lý thay đổi price
  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, price: value }));
    if (errors['price']) {
      setErrors(prev => ({ ...prev, ['price']: '' }));
    }
  };

  // Xử lý thay đổi usageLimit
  const handleUsageLimitChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, usageLimit: value }));
    if (errors['usageLimit']) {
      setErrors(prev => ({ ...prev, ['usageLimit']: '' }));
    }
  };

  // Xử lý thay đổi usageUnit
  const handleUsageUnitChange = (value: string | number | string[] | number[]) => {
    setFormData(prev => ({ ...prev, usageUnit: value as UsageUnit }));
    if (errors['usageUnit']) {
      setErrors(prev => ({ ...prev, ['usageUnit']: '' }));
    }
  };

  // Xử lý thay đổi isActive
  const handleIsActiveChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData['planId'].trim()) {
      newErrors['planId'] = 'ID gói dịch vụ là bắt buộc';
    } else if (isNaN(Number(formData['planId']))) {
      newErrors['planId'] = 'ID gói dịch vụ phải là số';
    }

    if (!formData['price'].trim()) {
      newErrors['price'] = 'Giá là bắt buộc';
    } else if (isNaN(Number(formData['price'])) || Number(formData['price']) < 0) {
      newErrors['price'] = 'Giá phải là số dương';
    }

    if (!formData['usageLimit'].trim()) {
      newErrors['usageLimit'] = 'Giới hạn sử dụng là bắt buộc';
    } else if (isNaN(Number(formData['usageLimit'])) || Number(formData['usageLimit']) < 0) {
      newErrors['usageLimit'] = 'Giới hạn sử dụng phải là số dương';
    }

    if (!formData['billingCycle']) {
      newErrors['billingCycle'] = 'Chu kỳ thanh toán là bắt buộc';
    }

    if (!formData['usageUnit']) {
      newErrors['usageUnit'] = 'Đơn vị sử dụng là bắt buộc';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Xử lý submit form
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const updateData: UpdatePlanPricingDto = {};

      // Chỉ gửi các field đã thay đổi
      if (Number(formData.planId) !== pricing.planId) {
        updateData.planId = Number(formData.planId);
      }
      if (formData.billingCycle !== pricing.billingCycle) {
        updateData.billingCycle = formData.billingCycle;
      }
      if (formData.price !== pricing.price) {
        updateData.price = Number(formData.price);
      }
      if (formData.usageLimit !== pricing.usageLimit) {
        updateData.usageLimit = Number(formData.usageLimit);
      }
      if (formData.usageUnit !== pricing.usageUnit) {
        updateData.usageUnit = formData.usageUnit;
      }
      if (formData.isActive !== pricing.isActive) {
        updateData.isActive = formData.isActive;
      }

      await onSubmit(pricing.id, updateData);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Options cho billing cycle
  const billingCycleOptions = [
    {
      value: BillingCycle.MONTH,
      label: 'Tháng',
    },
    {
      value: BillingCycle.SIX_MONTHS,
      label: '6 Tháng',
    },
    {
      value: BillingCycle.YEAR,
      label: 'Năm',
    },
  ];

  // Options cho usage unit
  const usageUnitOptions = [
    {
      value: UsageUnit.BYTES,
      label: 'Bytes',
    },
    {
      value: UsageUnit.REQUEST,
      label: 'Request',
    },
  ];

  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h3" className="mb-6">
          Chỉnh sửa tùy chọn giá #{pricing.id}
        </Typography>

        <form onSubmit={handleFormSubmit} className="space-y-6">
          {/* ID gói dịch vụ */}
          <FormItem
            label="ID gói dịch vụ"
            required
          >
            <Input
              value={formData.planId}
              onChange={handlePlanIdChange}
              placeholder="Nhập ID gói dịch vụ"
              error={errors['planId']}
              fullWidth
              type="number"
            />
          </FormItem>

          {/* Chu kỳ thanh toán */}
          <FormItem
            label="Chu kỳ thanh toán"
            required
          >
            <Select
              value={formData.billingCycle}
              onChange={handleBillingCycleChange}
              options={billingCycleOptions}
              placeholder="Chọn chu kỳ thanh toán"
              error={errors['billingCycle']}
              fullWidth
            />
          </FormItem>

          {/* Giá */}
          <FormItem
            label="Giá (VND)"
            required
          >
            <Input
              value={formData.price}
              onChange={handlePriceChange}
              placeholder="Nhập giá"
              error={errors['price']}
              fullWidth
              type="number"
              min="0"
              step="0.01"
            />
          </FormItem>

          {/* Giới hạn sử dụng */}
          <FormItem
            label="Giới hạn sử dụng"
            required
          >
            <Input
              value={formData.usageLimit}
              onChange={handleUsageLimitChange}
              placeholder="Nhập giới hạn sử dụng"
              error={errors['usageLimit']}
              fullWidth
              type="number"
              min="0"
            />
          </FormItem>

          {/* Đơn vị sử dụng */}
          <FormItem
            label="Đơn vị sử dụng"
            required
          >
            <Select
              value={formData.usageUnit}
              onChange={handleUsageUnitChange}
              options={usageUnitOptions}
              placeholder="Chọn đơn vị sử dụng"
              error={errors['usageUnit'] || ''}
              fullWidth
            />
          </FormItem>

          {/* Trạng thái kích hoạt */}
          <FormItem
            label="Trạng thái"
          >
            <div className="flex items-center space-x-2">
              <Toggle
                checked={formData.isActive}
                onChange={handleIsActiveChange}
              />
              <span className="text-sm text-gray-600">
                {formData.isActive ? 'Hoạt động' : 'Không hoạt động'}
              </span>
            </div>
          </FormItem>

          {/* Buttons */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              {t('common:cancel')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isSubmitting}
              disabled={isSubmitting}
            >
              Cập nhật tùy chọn giá
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default PlanPricingEditForm;
