{"user": {"title": "User Management", "description": "Manage user information in the system", "addUser": "Add User", "editUser": "Edit User", "basicInfo": "Basic Information", "additionalInfo": "Additional Information", "fullName": "Full Name", "email": "Email", "phoneNumber": "Phone Number", "password": "Password", "confirmPassword": "Confirm Password", "accountTypeLabel": "Account Type", "genderLabel": "Gender", "dateOfBirth": "Date of Birth", "address": "Address", "enterFullName": "Enter full name", "enterEmail": "Enter email", "enterPhoneNumber": "Enter phone number", "enterPassword": "Enter password", "enterAddress": "Enter address", "selectAccountType": "Select account type", "selectGender": "Select gender", "selectDateOfBirth": "Select date of birth", "individual": "Individual", "business": "Business", "male": "Male", "female": "Female", "other": "Other", "list": {"title": "User List", "description": "Manage the list of users in the system"}, "form": {"title": "User Information", "createTitle": "Add New User", "editTitle": "Edit User Information", "basicInfo": "Basic Information", "contactInfo": "Contact Information", "accountInfo": "Account Information"}, "fields": {"id": "ID", "fullName": "Full Name", "email": "Email", "phoneNumber": "Phone Number", "status": "Status", "accountType": "Account Type", "verification": "Verification", "points": "Points", "createdAt": "Created At", "actions": "Actions", "avatar": "Avatar", "gender": "Gender", "dateOfBirth": "Date of Birth", "address": "Address"}, "status": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended", "deleted": "Deleted"}, "accountType": {"individual": "Individual", "business": "Business"}, "gender": {"male": "Male", "female": "Female", "other": "Other"}, "verification": {"verified": "Verified", "notVerified": "Not Verified", "email": "Email", "phone": "Phone"}, "actions": {"create": "Add User", "edit": "Edit", "delete": "Delete", "view": "View Details", "save": "Save", "cancel": "Cancel", "search": "Search", "filter": "Filter"}, "messages": {"createSuccess": "User created successfully", "updateSuccess": "User information updated successfully", "deleteSuccess": "User deleted successfully", "createError": "Failed to create user", "updateError": "Failed to update user information", "deleteError": "Failed to delete user"}, "deleteUserConfirmTitle": "Confirm Delete User", "deleteUserConfirmMessage": "Are you sure you want to delete user {{name}}?"}}