import { RouteObject } from 'react-router-dom';
import UserListPageOptimized from '../pages/UserListPageOptimized';
import { Suspense } from 'react';
import { Loading } from '@/shared';
import AdminLayout from '@/shared/layouts/AdminLayout';

/**
 * Routes cho quản lý người dùng
 */
export const userRoutes: RouteObject[] = [
  {
    path: '/admin/users-page',
    element: (
      <AdminLayout title="User Management (Optimized)">
        <Suspense fallback={<Loading />}>
          <UserListPageOptimized />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default userRoutes;
