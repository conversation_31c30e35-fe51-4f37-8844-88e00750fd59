/**
 * Schemas cho form quản lý người dùng
 */
import { z } from 'zod';
import { TFunction } from 'i18next';
import { UserGender, UserType } from '../types/user.types';

/**
 * Schema tạo người dùng mới
 */
export const createUserSchema = (t: TFunction) => {
  return z
    .object({
      fullName: z
        .string()
        .min(1, t('validation:required', { field: t('user:fullName') }))
        .min(2, t('validation:minLength', { field: t('user:fullName'), length: 2 }))
        .max(100, t('validation:maxLength', { field: t('user:fullName'), length: 100 })),
      email: z
        .string()
        .min(1, t('validation:required', { field: t('user:email') }))
        .email(t('validation:email')),
      phoneNumber: z
        .string()
        .min(1, t('validation:required', { field: t('user:phoneNumber') }))
        .regex(/^[0-9]{10,11}$/, t('validation:phoneNumber')),
      password: z
        .string()
        .min(1, t('validation:required', { field: t('user:password') }))
        .min(8, t('validation:minLength', { field: t('user:password'), length: 8 }))
        .max(50, t('validation:maxLength', { field: t('user:password'), length: 50 })),
      confirmPassword: z
        .string()
        .min(1, t('validation:required', { field: t('user:confirmPassword') })),
      type: z.nativeEnum(UserType, {
        errorMap: () => ({ message: t('validation:required', { field: t('user:accountType') }) }),
      }),
      gender: z
        .nativeEnum(UserGender, {
          errorMap: () => ({ message: t('validation:required', { field: t('user:gender') }) }),
        })
        .optional(),
      dateOfBirth: z.string().optional(),
      address: z
        .string()
        .max(255, t('validation:maxLength', { field: t('user:address'), length: 255 }))
        .optional(),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation:passwordsDoNotMatch'),
      path: ['confirmPassword'],
    });
};

/**
 * Schema cập nhật người dùng
 */
export const updateUserSchema = (t: TFunction) => {
  return z.object({
    fullName: z
      .string()
      .min(1, t('validation:required', { field: t('user:fullName') }))
      .min(2, t('validation:minLength', { field: t('user:fullName'), length: 2 }))
      .max(100, t('validation:maxLength', { field: t('user:fullName'), length: 100 })),
    email: z
      .string()
      .min(1, t('validation:required', { field: t('user:email') }))
      .email(t('validation:email')),
    phoneNumber: z
      .string()
      .min(1, t('validation:required', { field: t('user:phoneNumber') }))
      .regex(/^[0-9]{10,11}$/, t('validation:phoneNumber')),
    type: z.nativeEnum(UserType, {
      errorMap: () => ({ message: t('validation:required', { field: t('user:accountType') }) }),
    }),
    gender: z
      .nativeEnum(UserGender, {
        errorMap: () => ({ message: t('validation:required', { field: t('user:gender') }) }),
      })
      .optional(),
    dateOfBirth: z.string().optional(),
    address: z
      .string()
      .max(255, t('validation:maxLength', { field: t('user:address'), length: 255 }))
      .optional(),
  });
};

/**
 * Schema cập nhật mật khẩu người dùng
 */
export const updatePasswordSchema = (t: TFunction) => {
  return z
    .object({
      password: z
        .string()
        .min(1, t('validation:required', { field: t('user:password') }))
        .min(8, t('validation:minLength', { field: t('user:password'), length: 8 }))
        .max(50, t('validation:maxLength', { field: t('user:password'), length: 50 })),
      confirmPassword: z
        .string()
        .min(1, t('validation:required', { field: t('user:confirmPassword') })),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation:passwordsDoNotMatch'),
      path: ['confirmPassword'],
    });
};
