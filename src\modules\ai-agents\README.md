# Module AI Agents

Mo<PERSON><PERSON> quản lý và hiển thị các AI Agents trong hệ thống RedAI.

## <PERSON><PERSON><PERSON> trúc thư mục

```
src/modules/ai-agents/
├── components/       # Components của module
│   ├── AgentCard.tsx # Component hiển thị thông tin của một AI Agent
│   ├── AgentGrid.tsx # Component hiển thị danh sách AI Agents dạng grid
│   └── index.ts      # Export các components
├── data/             # Dữ liệu của module
│   └── agents.ts     # Dữ liệu mock cho AI Agents
├── pages/            # Các trang của module
│   ├── AIAgentsPage.tsx        # Trang danh sách AI Agents
│   ├── AgentDetailPage.tsx     # Trang chi tiết AI Agent
│   ├── AgentCategoriesPage.tsx # Trang danh sách AI Agents theo danh mục
│   └── index.ts                # Export các pages
└── index.ts          # Export module
```

## Các trang

1. **AIAgentsPage**: Hiển thị danh sách tất cả AI Agents với chức năng tìm kiếm
2. **AgentDetailPage**: Hiển thị thông tin chi tiết của một AI Agent
3. **AgentCategoriesPage**: Hiển thị AI Agents được nhóm theo danh mục

## Routes

- `/ai-agents`: Trang danh sách AI Agents
- `/ai-agents/categories`: Trang danh sách AI Agents theo danh mục
- `/ai-agents/:id`: Trang chi tiết AI Agent

## Cách sử dụng

```jsx
// Import các components
import { AgentCard, AgentGrid } from '@/modules/ai-agents/components';
import { aiAgents } from '@/modules/ai-agents/data/agents';

// Sử dụng component
<AgentGrid agents={aiAgents} onSelectAgent={handleSelectAgent} />;
```

## Phát triển

Để thêm một trang mới vào module:

1. Tạo file trang mới trong thư mục `pages/`
2. Export trang từ `pages/index.ts`
3. Thêm route mới vào `src/shared/routers/modules/aiRoutes.tsx`
