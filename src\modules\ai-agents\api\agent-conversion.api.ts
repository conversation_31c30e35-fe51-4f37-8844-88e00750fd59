import { apiClient } from '@/shared/api/axios';

// Types cho Conversion - cập nhật theo API spec mới
export interface ConvertConfigItem {
  name: string;
  type: 'string' | 'number' | 'boolean';
  description: string;
  required: boolean;
  defaultValue: string | number | boolean;
}

export interface AgentConversionResponse {
  convertConfig: ConvertConfigItem[];
}

export interface UpdateAgentConversionDto {
  convertConfig: ConvertConfigItem[];
}

// API functions
export const getAgentConversion = async (agentId: string): Promise<AgentConversionResponse> => {
  const response = await apiClient.get(`/user/agents/${agentId}/conversion`);
  return response.result; // apiClient.get đã trả về { code, message, result }
};

export const updateAgentConversion = async (
  agentId: string,
  data: UpdateAgentConversionDto
): Promise<AgentConversionResponse> => {
  console.log('updateAgentConversion - Calling API:', {
    agentId,
    endpoint: `/user/agents/${agentId}/conversion`,
    data
  });

  const response = await apiClient.put(`/user/agents/${agentId}/conversion`, data);
  console.log('updateAgentConversion - API response:', response);
  return response.result; // Sửa từ response.data.result thành response.result
};
