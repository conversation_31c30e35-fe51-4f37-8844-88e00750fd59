import { apiClient } from '@/shared/api/axios';

// Types cho Facebook Pages
export interface FacebookPageDto {
  id: string;
  avatarPage: string;
  pageName: string;
}

export interface AgentFacebookPagesResponse {
  facebookPages: FacebookPageDto[];
}

export interface AddFacebookPagesDto {
  facebookPageIds: string[];
}

// API functions
export const getAgentFacebookPages = async (agentId: string): Promise<AgentFacebookPagesResponse> => {
  const response = await apiClient.get(`/user/agents/${agentId}/facebook-pages`);
  return response.data.result;
};

export const addFacebookPagesToAgent = async (
  agentId: string,
  data: AddFacebookPagesDto
): Promise<void> => {
  console.log('addFacebookPagesToAgent - Calling API:', {
    agentId,
    endpoint: `/user/agents/${agentId}/facebook-pages`,
    data
  });

  const response = await apiClient.post(`/user/agents/${agentId}/facebook-pages`, data);
  console.log('addFacebookPagesToAgent - API response:', response);
};

export const removeFacebookPageFromAgent = async (
  agentId: string,
  pageId: string
): Promise<void> => {
  console.log('removeFacebookPageFromAgent - Calling API:', {
    agentId,
    pageId,
    endpoint: `/user/agents/${agentId}/facebook-pages/${pageId}`
  });

  const response = await apiClient.delete(`/user/agents/${agentId}/facebook-pages/${pageId}`);
  console.log('removeFacebookPageFromAgent - API response:', response);
};
