import { apiClient } from '@/shared/api/axios';

// Types cho Agent List
export interface AgentListItemDto {
  id: string;
  name: string;
  avatar: string;
  typeId: number; // Đ<PERSON>y là field quan trọng
  typeName: string;
  provider: string;
  modelId: string;
  active: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface AgentListResponse {
  items: AgentListItemDto[];
  total: number;
  page: number;
  limit: number;
}

export interface GetAgentListParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
}

// API functions
export const getAgentList = async (params: GetAgentListParams = {}): Promise<AgentListResponse> => {
  const response = await apiClient.get('/user/agents', { params });
  return response.result; // apiClient.get đã trả về { code, message, result }
};

export const getAgentById = async (agentId: string): Promise<AgentListItemDto | null> => {
  try {
    const response = await getAgentList({ search: agentId });
    const agent = response.items.find(item => item.id === agentId);
    return agent || null;
  } catch (error) {
    console.error('Error getting agent by ID:', error);
    return null;
  }
};
