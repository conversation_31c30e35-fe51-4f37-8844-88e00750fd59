import { apiClient } from '@/shared/api/axios';

// Types cho Media
export interface MediaDto {
  id: string;
  name: string;
  url: string;
  description: string;
  size: number;
  createdAt: number;
}

export interface AgentMediaResponse {
  items: MediaDto[];
}

export interface AddMediaDto {
  mediaIds: string[];
}

export interface RemoveMediaDto {
  mediaIds: string[];
}

// API functions
export const getAgentMedia = async (agentId: string): Promise<AgentMediaResponse> => {
  const response = await apiClient.get(`/user/agents/${agentId}/medias`);
  return response.result; // apiClient.get đã trả về { code, message, result }
};

export const addMediaToAgent = async (
  agentId: string,
  data: AddMediaDto
): Promise<void> => {
  console.log('addMediaToAgent - Calling API:', {
    agentId,
    endpoint: `/user/agents/${agentId}/medias`,
    data
  });

  const response = await apiClient.post(`/user/agents/${agentId}/medias`, data);
  console.log('addMediaToAgent - API response:', response);
};

export const removeMediaFromAgent = async (
  agentId: string,
  data: RemoveMediaDto
): Promise<void> => {
  console.log('removeMediaFromAgent - Calling API:', {
    agentId,
    data,
    endpoint: `/user/agents/${agentId}/medias`
  });

  const response = await apiClient.delete(`/user/agents/${agentId}/medias`, { data });
  console.log('removeMediaFromAgent - API response:', response);
};
