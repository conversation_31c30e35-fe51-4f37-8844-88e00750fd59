import { apiClient } from '@/shared/api/axios';

// Types cho Products
export interface ProductDto {
  id: number;
  name: string;
  imageUrl: string;
  createdAt: number;
}

export interface AgentProductsResponse {
  items: ProductDto[];
}

export interface AddProductsDto {
  productIds: number[];
}

export interface RemoveProductsDto {
  productIds: number[];
}

// API functions
export const getAgentProducts = async (agentId: string): Promise<AgentProductsResponse> => {
  const response = await apiClient.get(`/user/agents/${agentId}/products`);
  return response.result; // apiClient.get đã trả về { code, message, result }
};

export const addProductsToAgent = async (
  agentId: string,
  data: AddProductsDto
): Promise<void> => {
  console.log('addProductsToAgent - Calling API:', {
    agentId,
    endpoint: `/user/agents/${agentId}/products`,
    data
  });

  const response = await apiClient.post(`/user/agents/${agentId}/products`, data);
  console.log('addProductsToAgent - API response:', response);
};

export const removeProductsFromAgent = async (
  agentId: string,
  data: RemoveProductsDto
): Promise<void> => {
  console.log('removeProductsFromAgent - Calling API:', {
    agentId,
    data,
    endpoint: `/user/agents/${agentId}/products`
  });

  const response = await apiClient.delete(`/user/agents/${agentId}/products`, { data });
  console.log('removeProductsFromAgent - API response:', response);
};
