import { apiClient } from '@/shared/api/axios';

// Types cho Profile
export interface AgentProfileDto {
  gender: string;
  dateOfBirth: number;
  position: string;
  education: string;
  skills: string[];
  personality: string[];
  languages: string[];
  nations: string;
  updatedAt: number;
}

export interface UpdateAgentProfileDto {
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  dateOfBirth: number; // timestamp
  position: string;
  education: string;
  skills: string[];
  personality: string[];
  languages: string[];
  nations: string;
}

// API functions
export const getAgentProfile = async (agentId: string): Promise<AgentProfileDto> => {
  const response = await apiClient.get(`/user/agents/${agentId}/profile`);
  return response.data.result;
};

export const updateAgentProfile = async (
  agentId: string,
  data: UpdateAgentProfileDto
): Promise<AgentProfileDto> => {
  console.log('updateAgentProfile - Calling API:', {
    agentId,
    endpoint: `/user/agents/${agentId}/profile`,
    data
  });

  const response = await apiClient.put(`/user/agents/${agentId}/profile`, data);
  console.log('updateAgentProfile - API response:', response);
  return response.result; // Sửa từ response.data.result thành response.result
};
