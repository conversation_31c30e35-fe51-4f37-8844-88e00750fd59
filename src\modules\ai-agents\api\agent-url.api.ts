import { apiClient } from '@/shared/api/axios';

// Types cho URLs
export interface UrlDto {
  id: string;
  url: string;
  title: string;
  createdAt: number;
}

export interface AgentUrlsResponse {
  items: UrlDto[];
}

export interface AddUrlsDto {
  urlIds: string[];
}

export interface RemoveUrlsDto {
  urlIds: string[];
}

// API functions
export const getAgentUrls = async (agentId: string): Promise<AgentUrlsResponse> => {
  const response = await apiClient.get(`/user/agents/${agentId}/urls`);
  return response.result; // apiClient.get đã trả về { code, message, result }
};

export const addUrlsToAgent = async (
  agentId: string,
  data: AddUrlsDto
): Promise<void> => {
  console.log('addUrlsToAgent - Calling API:', {
    agentId,
    endpoint: `/user/agents/${agentId}/urls`,
    data
  });

  const response = await apiClient.post(`/user/agents/${agentId}/urls`, data);
  console.log('addUrlsToAgent - API response:', response);
};

export const removeUrlsFromAgent = async (
  agentId: string,
  data: RemoveUrlsDto
): Promise<void> => {
  console.log('removeUrlsFromAgent - Calling API:', {
    agentId,
    data,
    endpoint: `/user/agents/${agentId}/urls`
  });

  const response = await apiClient.delete(`/user/agents/${agentId}/urls`, { data });
  console.log('removeUrlsFromAgent - API response:', response);
};
