import { apiClient } from '@/shared/api/axios';

// Types cho Websites
export interface WebsiteDto {
  id: string;
  websiteName: string;
  host: string;
  verify: boolean;
  isActive: boolean;
}

export interface AgentWebsitesResponse {
  websites: WebsiteDto[];
}

export interface AddWebsitesDto {
  websiteIds: string[];
}

// API functions
export const getAgentWebsites = async (agentId: string): Promise<AgentWebsitesResponse> => {
  const response = await apiClient.get(`/user/agents/${agentId}/websites`);
  return response.result; // apiClient.get đã trả về { code, message, result }
};

export const addWebsitesToAgent = async (
  agentId: string,
  data: AddWebsitesDto
): Promise<void> => {
  console.log('addWebsitesToAgent - Calling API:', {
    agentId,
    endpoint: `/user/agents/${agentId}/websites`,
    data
  });

  const response = await apiClient.post(`/user/agents/${agentId}/websites`, data);
  console.log('addWebsitesToAgent - API response:', response);
};

export const removeWebsiteFromAgent = async (
  agentId: string,
  websiteId: string
): Promise<void> => {
  console.log('removeWebsiteFromAgent - Calling API:', {
    agentId,
    websiteId,
    endpoint: `/user/agents/${agentId}/websites/${websiteId}`
  });

  const response = await apiClient.delete(`/user/agents/${agentId}/websites/${websiteId}`);
  console.log('removeWebsiteFromAgent - API response:', response);
};
