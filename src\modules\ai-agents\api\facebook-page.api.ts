import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * API functions cho Facebook Page integration
 * Tương ứng với AgentFacebookPageController trong backend
 */

export interface FacebookPageDto {
  id: string;
  pageId: string;
  pageName: string;
  pageAvatar?: string;
  accessToken: string;
  isActive: boolean;
  createdAt: number;
}

export interface AgentFacebookPageDto {
  agentId: string;
  facebookPageId: string;
  facebookPage: FacebookPageDto;
  isActive: boolean;
  settings?: {
    autoReply?: boolean;
    replyDelay?: number;
    workingHours?: {
      start: string;
      end: string;
      timezone: string;
    };
  };
  createdAt: number;
}

export interface FacebookPageQueryDto extends QueryDto {
  search?: string;
  isActive?: boolean;
}

export interface ConnectFacebookPageDto {
  facebookPageId: string;
  settings?: {
    autoReply?: boolean;
    replyDelay?: number;
    workingHours?: {
      start: string;
      end: string;
      timezone: string;
    };
  };
}

export interface UpdateAgentFacebookPageDto {
  isActive?: boolean;
  settings?: {
    autoReply?: boolean;
    replyDelay?: number;
    workingHours?: {
      start: string;
      end: string;
      timezone: string;
    };
  };
}

/**
 * Lấy danh sách Facebook Pages có sẵn
 * GET /user/facebook-pages
 */
export const getAvailableFacebookPages = async (
  params?: FacebookPageQueryDto
): Promise<ApiResponse<PaginatedResult<FacebookPageDto>>> => {
  return apiClient.get('/user/facebook-pages', { params });
};

/**
 * Lấy danh sách Facebook Pages được kết nối với agent
 * GET /user/agents/{id}/facebook-pages
 */
export const getAgentFacebookPages = async (
  agentId: string,
  params?: QueryDto
): Promise<ApiResponse<PaginatedResult<AgentFacebookPageDto>>> => {
  return apiClient.get(`/user/agents/${agentId}/facebook-pages`, { params });
};

/**
 * Kết nối Facebook Page với agent
 * POST /user/agents/{id}/facebook-pages
 */
export const connectFacebookPageToAgent = async (
  agentId: string,
  data: ConnectFacebookPageDto
): Promise<ApiResponse<AgentFacebookPageDto>> => {
  return apiClient.post(`/user/agents/${agentId}/facebook-pages`, data);
};

/**
 * Cập nhật cài đặt Facebook Page của agent
 * PUT /user/agents/{agentId}/facebook-pages/{pageId}
 */
export const updateAgentFacebookPage = async (
  agentId: string,
  pageId: string,
  data: UpdateAgentFacebookPageDto
): Promise<ApiResponse<AgentFacebookPageDto>> => {
  return apiClient.put(`/user/agents/${agentId}/facebook-pages/${pageId}`, data);
};

/**
 * Ngắt kết nối Facebook Page khỏi agent
 * DELETE /user/agents/{agentId}/facebook-pages/{pageId}
 */
export const disconnectFacebookPageFromAgent = async (
  agentId: string,
  pageId: string
): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${agentId}/facebook-pages/${pageId}`);
};
