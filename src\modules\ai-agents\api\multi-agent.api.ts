import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * API functions cho Multi-Agent management
 * Tương ứng với MultiAgentUserController trong backend
 */

export interface MultiAgentRelationDto {
  childAgentId: string;
  childAgentName: string;
  childAgentAvatar?: string;
  prompt?: string;
}

export interface MultiAgentResponseDto {
  parentAgentId: string;
  relations: MultiAgentRelationDto[];
  updatedAt: number;
}

export interface CreateMultiAgentRelationDto {
  childAgentId: string;
  prompt?: string;
}

export interface UpdateMultiAgentRelationDto {
  childAgentId: string;
  prompt?: string;
}

export interface BulkUpdateMultiAgentDto {
  relations: CreateMultiAgentRelationDto[];
}

/**
 * L<PERSON>y danh sách quan hệ multi-agent
 * GET /user/agents/{id}/multi-agent
 */
export const getMultiAgentRelations = async (
  agentId: string
): Promise<ApiResponse<MultiAgentResponseDto>> => {
  return apiClient.get(`/user/agents/${agentId}/multi-agent`);
};

/**
 * Tạo quan hệ multi-agent mới
 * POST /user/agents/{id}/multi-agent
 */
export const createMultiAgentRelation = async (
  agentId: string,
  data: CreateMultiAgentRelationDto
): Promise<ApiResponse<MultiAgentRelationDto>> => {
  return apiClient.post(`/user/agents/${agentId}/multi-agent`, data);
};

/**
 * Cập nhật quan hệ multi-agent
 * PUT /user/agents/{parentId}/multi-agent/{childId}
 */
export const updateMultiAgentRelation = async (
  parentAgentId: string,
  childAgentId: string,
  data: UpdateMultiAgentRelationDto
): Promise<ApiResponse<MultiAgentRelationDto>> => {
  return apiClient.put(`/user/agents/${parentAgentId}/multi-agent/${childAgentId}`, data);
};

/**
 * Xóa quan hệ multi-agent
 * DELETE /user/agents/{parentId}/multi-agent/{childId}
 */
export const deleteMultiAgentRelation = async (
  parentAgentId: string,
  childAgentId: string
): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${parentAgentId}/multi-agent/${childAgentId}`);
};

/**
 * Cập nhật hàng loạt quan hệ multi-agent
 * PUT /user/agents/{id}/multi-agent/bulk
 */
export const bulkUpdateMultiAgentRelations = async (
  agentId: string,
  data: BulkUpdateMultiAgentDto
): Promise<ApiResponse<MultiAgentResponseDto>> => {
  return apiClient.put(`/user/agents/${agentId}/multi-agent/bulk`, data);
};
