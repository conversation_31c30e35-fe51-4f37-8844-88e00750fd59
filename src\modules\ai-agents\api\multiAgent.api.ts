import { apiClient } from '@/shared/api/axios';

interface PaginationParams {
  page?: number;
  limit?: number;
}

// L<PERSON>y danh sách agent con
export async function getMultiAgents(agentId: string, params?: PaginationParams) {
  const res = await apiClient.get(`/user/agents/${agentId}/multi-agents`, {
    params: {
      page: params?.page || 1,
      limit: params?.limit || 10
    }
  });
  return res.result;
}

// Thêm agent con vào multi-agent system
export async function addMultiAgent(agentId: string, multiAgents: { agent_id: string; prompt: string }[]) {
  return apiClient.post(`/user/agents/${agentId}/multi-agents`, { multiAgents });
}

// Xóa agent con khỏi multi-agent system
export async function removeMultiAgents(agentId: string, childAgentIds: string[]) {
  return apiClient.delete(`/user/agents/${agentId}/multi-agents`, {
    data: { childAgentIds }
  });
} 