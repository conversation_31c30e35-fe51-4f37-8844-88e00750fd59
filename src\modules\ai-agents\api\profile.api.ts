import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * API functions cho Profile management
 * Tương ứng với ProfileUserController trong backend
 */

export interface ProfileResponseDto {
  gender?: string;
  birthDate?: string;
  position?: string;
  education?: string;
  skills?: string[];
  personality?: string[];
  languages?: string[];
  country?: string;
  updatedAt: number;
}

export interface UpdateProfileDto {
  gender?: string;
  birthDate?: string;
  position?: string;
  education?: string;
  skills?: string[];
  personality?: string[];
  languages?: string[];
  country?: string;
}

/**
 * Lấy thông tin profile của agent
 * GET /user/agents/{id}/profile
 */
export const getProfile = async (
  agentId: string
): Promise<ApiResponse<ProfileResponseDto>> => {
  return apiClient.get(`/user/agents/${agentId}/profile`);
};

/**
 * Cập nhật profile của agent
 * PUT /user/agents/{id}/profile
 */
export const updateProfile = async (
  agentId: string,
  data: UpdateProfileDto
): Promise<ApiResponse<ProfileResponseDto>> => {
  return apiClient.put(`/user/agents/${agentId}/profile`, data);
};
