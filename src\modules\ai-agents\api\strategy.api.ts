import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * API functions cho Strategy management
 * Tương ứng với AgentStrategyUserController trong backend
 */

export interface StrategyDto {
  id: string;
  name: string;
  description?: string;
  prompt: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface AgentStrategyDto {
  agentId: string;
  strategyId: string;
  strategy: StrategyDto;
  isActive: boolean;
  customPrompt?: string;
  createdAt: number;
}

export interface StrategyQueryDto extends QueryDto {
  search?: string;
  isActive?: boolean;
}

export interface AssignStrategyDto {
  strategyId: string;
  customPrompt?: string;
}

export interface UpdateAgentStrategyDto {
  isActive?: boolean;
  customPrompt?: string;
}

/**
 * Lấy danh sách strategies có sẵn
 * GET /user/strategies
 */
export const getAvailableStrategies = async (
  params?: StrategyQueryDto
): Promise<ApiResponse<PaginatedResult<StrategyDto>>> => {
  return apiClient.get('/user/strategies', { params });
};

/**
 * Lấy danh sách strategies được gán cho agent
 * GET /user/agents/{id}/strategies
 */
export const getAgentStrategies = async (
  agentId: string,
  params?: QueryDto
): Promise<ApiResponse<PaginatedResult<AgentStrategyDto>>> => {
  return apiClient.get(`/user/agents/${agentId}/strategies`, { params });
};

/**
 * Gán strategy cho agent
 * POST /user/agents/{id}/strategies
 */
export const assignStrategyToAgent = async (
  agentId: string,
  data: AssignStrategyDto
): Promise<ApiResponse<AgentStrategyDto>> => {
  return apiClient.post(`/user/agents/${agentId}/strategies`, data);
};

/**
 * Cập nhật strategy của agent
 * PUT /user/agents/{agentId}/strategies/{strategyId}
 */
export const updateAgentStrategy = async (
  agentId: string,
  strategyId: string,
  data: UpdateAgentStrategyDto
): Promise<ApiResponse<AgentStrategyDto>> => {
  return apiClient.put(`/user/agents/${agentId}/strategies/${strategyId}`, data);
};

/**
 * Xóa strategy khỏi agent
 * DELETE /user/agents/{agentId}/strategies/{strategyId}
 */
export const removeStrategyFromAgent = async (
  agentId: string,
  strategyId: string
): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${agentId}/strategies/${strategyId}`);
};
