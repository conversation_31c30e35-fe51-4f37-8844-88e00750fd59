import { ApiResponse, PaginatedResponse } from '@/shared/types/api';
import { apiClient } from '@/shared/utils/api-client';

/**
 * Interface cho Tool item
 */
export interface Tool {
  id: string;
  toolName: string;
  toolDescription: string;
  endpoint: string;
  method: string;
  active: boolean;
  authType: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Response type cho danh sách tools
 */
export type ToolsResponse = ApiResponse<PaginatedResponse<Tool>>;

/**
 * Lấy danh sách tools integration
 */
export const getToolsIntegration = async (): Promise<ToolsResponse> => {
  const response = await apiClient.get<ToolsResponse>('/user/tools/integration/tools');
  return response.data;
};
