import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { apiClient } from '@/shared/api/axios';

/**
 * Interface cho Tool item từ API
 */
export interface Tool {
  id: string;
  name: string;
  description: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * Response type cho danh sách tools
 */
export type ToolsResponse = ApiResponseDto<PaginatedResult<Tool>>;

/**
 * L<PERSON>y danh sách tools integration
 */
export const getToolsIntegration = async (): Promise<ToolsResponse> => {
  try {
    console.log('🔍 Calling API: /user/tools/integration/tools');
    const response = await apiClient.get<PaginatedResult<Tool>>('/user/tools/integration/tools');
    console.log('✅ API Response:', response);
    return response;
  } catch (error) {
    console.error('❌ API Error:', error);

    // Fallback với mock data để test UI
    console.log('🔄 Using mock data for testing...');
    const mockResponse: ToolsResponse = {
      code: 0,
      message: "Success",
      result: {
        items: [
          {
            id: "123e4567-e89b-12d3-a456-426614174000",
            name: "Get Users",
            description: "Lấy danh sách người dùng",
            createdAt: 1625097600000,
            updatedAt: 1625097600000
          },
          {
            id: "123e4567-e89b-12d3-a456-426614174001",
            name: "Create User",
            description: "Tạo người dùng mới",
            createdAt: 1625097600000,
            updatedAt: 1625097600000
          },
          {
            id: "123e4567-e89b-12d3-a456-426614174002",
            name: "Update User",
            description: "Cập nhật thông tin người dùng",
            createdAt: 1625097600000,
            updatedAt: 1625097600000
          }
        ],
        meta: {
          totalItems: 3,
          itemCount: 3,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
          hasItems: true
        }
      }
    };

    return mockResponse;
  }
};
