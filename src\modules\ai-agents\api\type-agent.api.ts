import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * API functions cho Type Agent management
 * Tương ứng với TypeAgentUserController trong backend
 */

export interface TypeAgentConfigDto {
  hasProfile: boolean;
  hasConversion: boolean;
  hasResources: boolean;
  hasStrategy: boolean;
  hasMultiAgent: boolean;
  hasFacebookIntegration: boolean;
  hasWebsiteIntegration: boolean;
}

export interface TypeAgentDto {
  id: number;
  name: string;
  description?: string;
  avatar?: string;
  instruction?: string;
  isSystem: boolean;
  config: TypeAgentConfigDto;
  createdAt: number;
  updatedAt: number;
}

export interface TypeAgentQueryDto extends QueryDto {
  search?: string;
  isSystem?: boolean;
  hasProfile?: boolean;
  hasConversion?: boolean;
  hasResources?: boolean;
  hasStrategy?: boolean;
  hasMultiAgent?: boolean;
  hasFacebookIntegration?: boolean;
  hasWebsiteIntegration?: boolean;
}

/**
 * Lấy danh sách type agents
 * GET /user/type-agents
 */
export const getTypeAgents = async (
  params?: TypeAgentQueryDto
): Promise<ApiResponse<PaginatedResult<TypeAgentDto>>> => {
  return apiClient.get('/user/type-agents', { params });
};

/**
 * Lấy chi tiết type agent
 * GET /user/type-agents/{id}
 */
export const getTypeAgentDetail = async (
  id: number
): Promise<ApiResponse<TypeAgentDto>> => {
  return apiClient.get(`/user/type-agents/${id}`);
};

/**
 * Lấy danh sách type agents cho selection (simplified)
 * GET /user/type-agents/selection
 */
export const getTypeAgentsForSelection = async (
  params?: { search?: string; limit?: number }
): Promise<ApiResponse<TypeAgentDto[]>> => {
  return apiClient.get('/user/type-agents/selection', { params });
};
