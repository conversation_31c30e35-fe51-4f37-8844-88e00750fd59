import React from 'react';
import { TypeAgentGrid, TypeAgent } from './';
import { CustomAgentFormData } from './CustomTypeAgentCard';
import CreateAgentForm from './CreateAgentForm';
import Pagination from '@/shared/components/common/Pagination/Pagination';

interface AgentCreationGridProps {
  // Các props cho phần tìm kiếm và lọc
  search: string;
  sortBy: string;
  sortDirection: 'ASC' | 'DESC';
  handleSearch: (value: string) => void;
  handleSortChange: (field: string) => void;
  setSortDirection: (direction: 'ASC' | 'DESC') => void;

  // Các props cho phân trang
  page: number;
  limit: number;
  totalItems: number;
  handlePageChange: (page: number) => void;
  handleLimitChange: (limit: number) => void;

  // Các props cho TypeAgentGrid
  agentTypes: TypeAgent[];
  selectedAgentType: number | null;
  handleAgentTypeSelect: (id: number) => void;
  handleCustomAgentClick: () => void;

  // Các props cho CustomAgentForm
  showCustomForm: boolean;
  handleSaveCustomAgent: (data: CustomAgentFormData) => void;
  handleCancelCustomAgent: () => void;

  // Các props cho CreateAgentForm
  isCustomAgentSelected: boolean;
  customAgentData: CustomAgentFormData;
  selectedAgent?: TypeAgent;
}

/**
 * Component hiển thị grid tạo agent
 */
const AgentCreationGrid: React.FC<AgentCreationGridProps> = ({
  // Các props cho phần tìm kiếm và lọc
  sortBy,
  sortDirection,
  handleSearch,
  handleSortChange,
  setSortDirection,

  // Các props cho phân trang
  page,
  limit,
  totalItems,
  handlePageChange,
  handleLimitChange,

  // Các props cho TypeAgentGrid
  agentTypes,
  selectedAgentType,
  handleAgentTypeSelect,

  // Các props cho CreateAgentForm
  isCustomAgentSelected,
  customAgentData,
  selectedAgent
}) => {
  return (
    <div className="w-full">
      {/* Phần tìm kiếm và lọc */}
      <div className="mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-grow">
            <input
              type="text"
              placeholder="Tìm kiếm agent..."
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
          <div className="flex gap-2">
            <select
              className="px-3 py-2 border rounded-md text-sm"
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value)}
            >
              <option value="name">Tên</option>
              <option value="createdAt">Ngày tạo</option>
            </select>
            <select
              className="px-3 py-2 border rounded-md text-sm"
              value={sortDirection}
              onChange={(e) => setSortDirection(e.target.value as 'ASC' | 'DESC')}
            >
              <option value="ASC">Tăng dần</option>
              <option value="DESC">Giảm dần</option>
            </select>
          </div>
        </div>
      </div>

      {/* Danh sách agent */}
      <TypeAgentGrid
        agents={agentTypes}
        selectedAgentId={selectedAgentType}
        onSelectAgent={handleAgentTypeSelect}
      />

      {/* Phân trang */}
      <div className="mt-6">
        <Pagination
          currentPage={page}
          totalItems={totalItems}
          itemsPerPage={limit}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleLimitChange}
          itemsPerPageOptions={[5, 10, 20, 50]}
          showItemsPerPageSelector={true}
          showPageInfo={true}
          variant="default"
        />
      </div>

      {/* Thông tin agent đã chọn hoặc đã tạo */}
      <CreateAgentForm
        isCustomAgentSelected={isCustomAgentSelected}
        customAgentData={customAgentData}
        selectedAgentName={selectedAgent?.name}
        selectedAgentDescription={selectedAgent?.description}
        onContinue={() => alert('Tiếp tục với agent đã chọn')}
      />
    </div>
  );
};

export default AgentCreationGrid;
