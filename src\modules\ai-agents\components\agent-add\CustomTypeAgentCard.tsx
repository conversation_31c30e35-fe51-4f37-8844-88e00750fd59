import React, { useState, useEffect, useMemo } from 'react';
import { Card, IconCard, Button, Input, Textarea } from '@/shared/components/common';
import { TypeAgent } from './TypeAgentCard';
import ConfigFieldsGrid from '../common/ConfigFieldsGrid';
import { FULL_TYPE_AGENT_CONFIG_FIELDS } from '../../utils/type-agent.utils';

export interface CustomAgentFormData {
  name: string;
  description: string;
  config: {
    hasProfile: boolean;
    hasOutput: boolean;
    hasConversion: boolean;
    hasResources: boolean;
    hasStrategy: boolean;
    hasMultiAgent: boolean;
  };
  groupToolIds?: number[]; // Optional để tương thích với CustomAgentForm
}

export interface CustomAgentCardProps {
  isSelected?: boolean;
  onClick?: () => void;
  editData?: TypeAgent | null; // Dữ liệu để edit
  onSave?: (data: CustomAgentFormData) => void;
  mode?: 'create' | 'edit'; // Mode hiện tại
}

/**
 * Component hiển thị card để tạo Custom Agent hoặc form để edit
 */
const CustomTypeAgentCard: React.FC<CustomAgentCardProps> = ({
  isSelected = false,
  onClick,
  editData = null,
  onSave,
  mode = 'create'
}) => {
  // Default form data
  const defaultFormData: CustomAgentFormData = useMemo(() => ({
    name: 'Custom Agent',
    description: 'Loại agent tùy chỉnh của người dùng',
    config: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
      hasStrategy: false,
      hasMultiAgent: false,
    },
  }), []);

  // Form state
  const [formData, setFormData] = useState<CustomAgentFormData>(defaultFormData);

  // Initial data for comparison
  const [initialData, setInitialData] = useState<CustomAgentFormData>(defaultFormData);

  // Load edit data when provided
  useEffect(() => {
    if (editData && mode === 'edit') {
      const editFormData: CustomAgentFormData = {
        name: editData.name,
        description: editData.description,
        config: editData.config,
      };
      setFormData(editFormData);
      setInitialData(editFormData);
    } else {
      setFormData(defaultFormData);
      setInitialData(defaultFormData);
    }
  }, [editData, mode, defaultFormData]);

  // Check if form has changes
  const hasChanges = useMemo(() => {
    return JSON.stringify(formData) !== JSON.stringify(initialData);
  }, [formData, initialData]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle config changes
  const handleConfigChange = (configKey: keyof CustomAgentFormData['config'], value: boolean) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [configKey]: value
      }
    }));
  };

  // Handle save
  const handleSave = () => {
    if (onSave) {
      onSave(formData);
    }
  };

  // If in create mode and not selected, show the simple card
  if (mode === 'create' && !isSelected) {
    return (
      <Card
        className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer border-dashed border-2 ${
          isSelected
            ? 'ring-2 ring-primary ring-offset-2 dark:ring-offset-gray-800 bg-primary-50 dark:bg-primary-900/20 border-primary'
            : 'border-gray-300 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-700'
        }`}
        variant="elevated"
        onClick={onClick}
      >
        <div className="p-4">
          <div className="flex flex-col space-y-4 items-center justify-center text-center h-full">
            {/* Icon */}
            <div className="relative w-12 h-12 flex-shrink-0">
              <div className="w-full h-full relative">
                <div className="absolute inset-0 flex items-center justify-center z-0 bg-primary-100 dark:bg-primary-900 rounded-full">
                  <IconCard
                    icon="plus"
                    variant="primary"
                    size="md"
                    className="text-primary-500"
                  />
                </div>
              </div>
            </div>

            {/* Tên */}
            <div className="min-w-0">
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Custom Type Agent
              </h3>
            </div>

            {/* Mô tả */}
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Tạo loại agent tùy chỉnh theo nhu cầu của bạn
            </div>
          </div>
        </div>
      </Card>
    );
  }

  // Show form when selected (create mode) or in edit mode
  return (
    <Card className="shadow-md" variant="elevated">
      <div className="p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {mode === 'edit' ? 'Chỉnh sửa Type Agent' : 'Tạo Custom Type Agent'}
            </h3>
          </div>

          {/* Form fields */}
          <div className="space-y-4">
            {/* Name field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tên Type Agent *
              </label>
              <Input
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Nhập tên type agent"
                className="w-full"
              />
            </div>

            {/* Description field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Mô tả
              </label>
              <Textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Nhập mô tả cho type agent"
                rows={3}
                className="w-full"
              />
            </div>

            {/* Config fields */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Cấu hình chức năng
              </label>
              <ConfigFieldsGrid
                fields={FULL_TYPE_AGENT_CONFIG_FIELDS}
                config={formData.config}
                onChange={(field) => {
                  const configKey = field as keyof CustomAgentFormData['config'];
                  const currentValue = formData.config[configKey];
                  handleConfigChange(configKey, !currentValue);
                }}
              />
            </div>
          </div>

          {/* Action buttons - only show save when there are changes */}
          {hasChanges && (
            <div className="flex justify-end">
              <Button
                variant="primary"
                onClick={handleSave}
                className="px-6"
              >
                Lưu
              </Button>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default CustomTypeAgentCard;
