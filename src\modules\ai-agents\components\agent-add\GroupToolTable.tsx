import { Table } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import React, { useMemo } from 'react';

export interface GroupTool {
  id: number;
  name: string;
  description: string;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

interface GroupToolTableProps {
  groups: GroupTool[];
  selectedGroupIds: number[];
  onSelectionChange: (selectedIds: number[]) => void;
}

/**
 * Component hiển thị bảng các group tool và cho phép chọn
 */
const GroupToolTable: React.FC<GroupToolTableProps> = ({
  groups,
  selectedGroupIds,
  onSelectionChange,
}) => {
  // Removed handleToggleGroup as we're using Table's built-in selection

  // Format ngày tháng
  const formatDate = (timestamp: string) => {
    const date = new Date(parseInt(timestamp));
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<GroupTool>[]>(() => [
    {
      key: 'id',
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      key: 'name',
      title: 'Tên',
      dataIndex: 'name',
    },
    {
      key: 'description',
      title: 'Mô tả',
      dataIndex: 'description',
    },
    {
      key: 'createdAt',
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      render: (value) => formatDate(value as string),
    },
  ], []);

  return (
    <Table
      data={groups}
      columns={columns}
      size="sm"
      bordered
      hoverable
      striped
      pagination={{
        total: 100,
        current: 1,
        pageSize: 10,
        onChange: (page) => console.log(`Chuyển đến trang ${page}`),
      }}
      selectable
      rowSelection={{
        selectedRowKeys: selectedGroupIds.map(id => id.toString()),
        onChange: (selectedRowKeys) => {
          // Convert string keys back to numbers
          const numberIds = selectedRowKeys.map(key => parseInt(key.toString(), 10));
          onSelectionChange(numberIds);
        },
      }}
    />
  );
};

export default GroupToolTable;
