/**
 * Agent Configuration Left Panel Component
 * Vertical layout: Avatar + Name + Model Config in left panel
 */

import { Card, Chip, Icon, InlineEditInput, Select } from '@/shared/components/common';
import React, { useState, useEffect } from 'react';
import { ModelConfigData, TypeProviderEnum } from '../../types';
import { ModelConfig } from './index';
import { useProviderModels } from '@/modules/integration/provider-model/hooks';
// Removed unused imports

// Types cho ProviderCard
interface ProviderCardProps {
  provider: string;
  name: string;
  isSelected: boolean;
  onClick: (provider: string) => void;
  disabled?: boolean;
  keyCount?: number; // Số lượng keys có sẵn
  isLoading?: boolean; // Trạng thái loading
}

// Helper function để lấy icon cho provider
const getProviderIcon = (provider: string): string => {
  switch (provider.toLowerCase()) {
    case 'openai':
      return 'brain';
    case 'anthropic':
      return 'cpu';
    case 'google':
      return 'search';
    case 'redai':
      return 'zap';
    case 'meta':
      return 'facebook';
    case 'deepseek':
      return 'eye';
    case 'xai':
    case 'grok':
      return 'zap';
    default:
      return 'server';
  }
};

interface AgentConfigLeftPanelProps {
  agentData: {
    avatar?: string;
    name: string;
  };
  typeAgentName?: string; // Tên của TypeAgent được chọn
  onAvatarUpload: () => void;
  onAvatarFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onNameUpdate: (name: string) => void;
  onModelConfigUpdate: (config: ModelConfigData) => void;
  validateAgentName: (name: string) => string | null;
  fileInputRef: React.RefObject<HTMLInputElement>;
  // Provider selection
  selectedProvider?: string;
  onProviderSelect?: (provider: string) => void;
  showProviderSelection?: boolean;
  // Plane Mode - Sequential selection
  usePlaneMode?: boolean;
  selectedKeyId?: string;
  selectedModelId?: string;
  onKeySelect?: (keyId: string) => void;
  onModelSelect?: (modelId: string) => void;
  // Action buttons
  onSave?: () => void;
  onCancel?: () => void;
  // XÓA: Không cần onDelete callback nữa
  isSaving?: boolean;
  hasChanges?: boolean;
  mode?: 'create' | 'edit'; // Thêm mode để hiển thị nút Delete
  // Agent ID for edit mode
  agentId?: string;
}

/**
 * Avatar Section Component - Centered for Left Panel
 */
const AvatarSection: React.FC<{
  avatar?: string;
  onUpload: () => void;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
}> = ({ avatar, onUpload, onFileChange, fileInputRef }) => (
  <div className="flex flex-col items-center mb-6">
    <div className="relative w-32 h-32 rounded-full overflow-hidden bg-gradient-to-br from-red-500 to-orange-300 flex items-center justify-center border-4 border-white shadow-lg group cursor-pointer">
      {avatar ? (
        <img src={avatar} alt="Avatar" className="w-full h-full object-cover" />
      ) : (
        <div className="text-white">
          <Icon name="user" size="xl" />
        </div>
      )}

      <div
        className="absolute bottom-0 left-0 right-0 h-1/3 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center"
        onClick={onUpload}
      >
        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Icon name="upload" size="sm" className="text-white" />
        </div>
      </div>
    </div>

    {/* Hidden file input */}
    <input
      ref={fileInputRef}
      type="file"
      accept="image/*"
      onChange={onFileChange}
      className="hidden"
    />
  </div>
);

/**
 * Name and Info Section Component - Centered for Left Panel
 */
const NameInfoSection: React.FC<{
  name: string;
  typeAgentName?: string;
  onNameUpdate: (name: string) => void;
  validateAgentName: (name: string) => string | null;
}> = ({ name, typeAgentName, onNameUpdate, validateAgentName }) => (
  <div className="flex flex-col items-center space-y-4 mb-6">
    {/* Agent Name - Centered */}
    <div className="w-full flex justify-center">
      <InlineEditInput
        value={name}
        onSave={onNameUpdate}
        placeholder="Nhập tên agent"
        className="font-semibold"
        maxLength={100}
        validate={validateAgentName}
        variant="h2"
        centerAligned={true}
        noUnderline={true}
        dynamicWidth={true}
        maxWidth="500px"
        minWidth="120px"
      />
    </div>

    {/* Agent Type Chip - Centered */}
    <div className="flex justify-center">
      <Chip
        variant="primary"
        size="md"
        leftIconName="bot"
        className="bg-gradient-to-r from-red-500  to-yellow-400 text-white shadow-md"
      >
        {typeAgentName || 'AI Agent'}
      </Chip>
    </div>
  </div>
);

/**
 * Model Config Section Component - Full Width for Left Panel
 */
const ModelConfigSection: React.FC<{
  onModelConfigUpdate: (config: ModelConfigData) => void;
  agentId?: string;
  mode?: 'create' | 'edit';
}> = ({ onModelConfigUpdate, agentId, mode }) => (
  <div className="w-full">
    <ModelConfig onSave={onModelConfigUpdate} agentId={agentId} mode={mode} />
  </div>
);

/**
 * Component ProviderCard để hiển thị các provider
 */
export const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false,
  keyCount,
  isLoading = false,
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center justify-between p-3">
        <div className="flex items-center">
          <div className="mr-3">
            <Icon name={getProviderIcon(provider)} size="md" />
          </div>
          <div className="font-medium text-gray-900 dark:text-gray-100">{name}</div>
        </div>

        {/* Key Count Badge */}
        <div className="flex items-center">
          {isLoading ? (
            <Icon name="loader-2" size="sm" className="animate-spin text-gray-400" />
          ) : keyCount !== undefined ? (
            <div
              className={`px-2 py-1 rounded-full text-xs font-medium ${
                keyCount > 0
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
              }`}
            >
              {keyCount > 0 ? `${keyCount} key${keyCount > 1 ? 's' : ''}` : 'No keys'}
            </div>
          ) : null}
        </div>
      </div>
    </Card>
  );
};

/**
 * Provider Selection Section Component
 */
export const ProviderSelectionSection: React.FC<{
  selectedProvider?: string;
  onProviderSelect?: (provider: string) => void;
}> = ({ selectedProvider, onProviderSelect }) => {
  // Danh sách providers với tên hiển thị
  const providers = [
    { id: 'openai', name: 'OpenAI', type: TypeProviderEnum.OPENAI },
    { id: 'anthropic', name: 'Anthropic', type: TypeProviderEnum.ANTHROPIC },
    { id: 'google', name: 'Google', type: TypeProviderEnum.GOOGLE },
    { id: 'meta', name: 'Meta', type: TypeProviderEnum.META },
    { id: 'deepseek', name: 'DeepSeek', type: TypeProviderEnum.DEEPSEEK },
    { id: 'xai', name: 'xAI (Grok)', type: TypeProviderEnum.XAI },
  ];

  // Lấy danh sách keys cho từng provider
  const providerQueries = providers.map(provider => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    return useProviderModels({
      type: provider.type,
      limit: 100,
      page: 1,
    });
  });

  return (
    <div className="w-full mb-4">
      <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Chọn Provider</h3>
      <div className="grid grid-cols-1 gap-2">
        {providers.map((provider, index) => {
          const query = providerQueries[index];
          const keyCount = query?.data?.result?.items?.length || 0;
          const isLoading = query?.isLoading;

          return (
            <ProviderCard
              key={provider.id}
              provider={provider.id}
              name={provider.name}
              isSelected={selectedProvider === provider.id}
              onClick={onProviderSelect || (() => {})}
              keyCount={keyCount}
              isLoading={isLoading}
              disabled={keyCount === 0 && !isLoading}
            />
          );
        })}
      </div>
    </div>
  );
};

/**
 * Plane Mode Provider Selection Component
 * Flow: Provider → Key → Model
 */
export const PlaneProviderSelection: React.FC<{
  selectedProvider?: string;
  selectedKeyId?: string;
  selectedModelId?: string;
  onProviderSelect?: (provider: string) => void;
  onKeySelect?: (keyId: string) => void;
  onModelSelect?: (modelId: string) => void;
}> = ({
  selectedProvider,
  selectedKeyId,
  selectedModelId,
  onProviderSelect,
  onKeySelect,
  onModelSelect,
}) => {
  // Danh sách providers
  const providers = [
    { id: 'redai', name: 'RedAI', type: TypeProviderEnum.REDAI },
    { id: 'openai', name: 'OpenAI', type: TypeProviderEnum.OPENAI },
    { id: 'anthropic', name: 'Anthropic', type: TypeProviderEnum.ANTHROPIC },
    { id: 'google', name: 'Google', type: TypeProviderEnum.GOOGLE },
    { id: 'meta', name: 'Meta', type: TypeProviderEnum.META },
    { id: 'deepseek', name: 'DeepSeek', type: TypeProviderEnum.DEEPSEEK },
    { id: 'xai', name: 'xAI (Grok)', type: TypeProviderEnum.XAI },
  ];

  // Lấy danh sách keys cho provider đã chọn
  const selectedProviderType = providers.find(p => p.id === selectedProvider)?.type;
  const keysQuery = useProviderModels(
    selectedProviderType
      ? {
          type: selectedProviderType,
          limit: 100,
          page: 1,
        }
      : undefined
  );

  // Mock data cho models (sẽ thay bằng API thực tế)
  const mockModels = selectedKeyId
    ? [
        { id: 'gpt-4', name: 'GPT-4' },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
        { id: 'claude-3', name: 'Claude 3' },
        { id: 'gemini-pro', name: 'Gemini Pro' },
      ]
    : [];

  // Reset selections khi thay đổi provider
  useEffect(() => {
    if (selectedProvider && onKeySelect) {
      onKeySelect('');
    }
  }, [selectedProvider, onKeySelect]);

  // Reset model selection khi thay đổi key
  useEffect(() => {
    if (selectedKeyId && onModelSelect) {
      onModelSelect('');
    }
  }, [selectedKeyId, onModelSelect]);

  return (
    <div className="w-full mb-4 space-y-4">
      <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        Cấu hình Model (Plane Mode)
      </h3>

      {/* Step 1: Provider Selection */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-gray-600 dark:text-gray-400">
          1. Chọn Provider
        </label>
        <div className="grid grid-cols-1 gap-2">
          {providers.map(provider => (
            <ProviderCard
              key={provider.id}
              provider={provider.id}
              name={provider.name}
              isSelected={selectedProvider === provider.id}
              onClick={onProviderSelect || (() => {})}
            />
          ))}
        </div>
      </div>

      {/* Step 2: Key Selection - Only show if provider is selected */}
      {selectedProvider && (
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-600 dark:text-gray-400">
            2. Chọn API Key
          </label>
          {keysQuery.isLoading ? (
            <div className="flex items-center justify-center py-4">
              <Icon name="loader-2" size="sm" className="animate-spin text-gray-400" />
              <span className="ml-2 text-sm text-gray-500">Đang tải keys...</span>
            </div>
          ) : keysQuery.data?.result?.items?.length ? (
            <Select
              value={selectedKeyId || ''}
              onChange={val => onKeySelect?.(val as string)}
              placeholder="Chọn API Key"
              options={keysQuery.data.result.items.map(key => ({
                value: key.id,
                label: `${key.name} (${new Date(key.createdAt * 1000).toLocaleDateString()})`,
              }))}
            />
          ) : (
            <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                Không có API key nào cho provider này. Vui lòng thêm key trước.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Step 3: Model Selection - Only show if key is selected */}
      {selectedProvider && selectedKeyId && (
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-600 dark:text-gray-400">
            3. Chọn Model
          </label>
          <Select
            value={selectedModelId || ''}
            onChange={val => onModelSelect?.(val as string)}
            placeholder="Chọn Model"
            options={mockModels.map(model => ({
              value: model.id,
              label: model.name,
            }))}
          />
        </div>
      )}

      {/* Progress Indicator */}
      <div className="flex items-center space-x-2 pt-2">
        <div
          className={`w-3 h-3 rounded-full ${selectedProvider ? 'bg-green-500' : 'bg-gray-300'}`}
        />
        <div className={`w-3 h-3 rounded-full ${selectedKeyId ? 'bg-green-500' : 'bg-gray-300'}`} />
        <div
          className={`w-3 h-3 rounded-full ${selectedModelId ? 'bg-green-500' : 'bg-gray-300'}`}
        />
        <span className="text-xs text-gray-500 ml-2">
          {selectedModelId
            ? 'Hoàn thành'
            : selectedKeyId
              ? 'Chọn model'
              : selectedProvider
                ? 'Chọn key'
                : 'Chọn provider'}
        </span>
      </div>
    </div>
  );
};

/**
 * Main Left Panel Component
 */
export const AgentConfigLeftPanel: React.FC<AgentConfigLeftPanelProps> = ({
  agentData,
  typeAgentName,
  onAvatarUpload,
  onAvatarFileChange,
  onNameUpdate,
  onModelConfigUpdate,
  validateAgentName,
  fileInputRef,
  selectedProvider,
  onProviderSelect,
  showProviderSelection = false,
  usePlaneMode = false,
  selectedKeyId,
  selectedModelId,
  onKeySelect,
  onModelSelect,
  onSave,
  onCancel,
  onDelete,
  isSaving = false,
  hasChanges = false,
  mode = 'create',
  agentId,
}) => {
  // Use mode parameter to avoid unused variable warning
  console.log('AgentConfigHeader mode:', mode);
  return (
    <div className="w-full h-full flex flex-col p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg sticky top-4 overflow-hidden min-h-[600px]">
      {/* Content */}
      <div className="flex flex-col h-full">
        {/* Avatar Section - Centered */}
        <AvatarSection
          avatar={agentData.avatar ?? undefined}
          onUpload={onAvatarUpload}
          onFileChange={onAvatarFileChange}
          fileInputRef={fileInputRef}
        />

        {/* Name Section - Centered */}
        <NameInfoSection
          name={agentData.name}
          typeAgentName={typeAgentName}
          onNameUpdate={onNameUpdate}
          validateAgentName={validateAgentName}
        />

        {/* Provider Selection Section - Conditional */}
        {showProviderSelection && onProviderSelect && (
          <>
            {usePlaneMode ? (
              <PlaneProviderSelection
                selectedProvider={selectedProvider}
                selectedKeyId={selectedKeyId}
                selectedModelId={selectedModelId}
                onProviderSelect={onProviderSelect}
                onKeySelect={onKeySelect}
                onModelSelect={onModelSelect}
              />
            ) : (
              <ProviderSelectionSection
                selectedProvider={selectedProvider}
                onProviderSelect={onProviderSelect}
              />
            )}
          </>
        )}

        {/* Model Config Section - Full Width */}
        <ModelConfigSection
          onModelConfigUpdate={onModelConfigUpdate}
          agentId={agentId}
          mode={mode}
        />

        {/* Action Buttons */}
        {(onSave || onCancel || onDelete) && (
          <div className="space-y-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            {/* Primary Actions Row */}
            <div className="flex gap-3">
              {onCancel && (
                <button
                  type="button"
                  onClick={onCancel}
                  disabled={isSaving}
                  className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Hủy
                </button>
              )}
              {onSave && (
                <button
                  type="button"
                  onClick={onSave}
                  disabled={isSaving || !hasChanges}
                  className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSaving ? (
                    <div className="flex items-center justify-center gap-2">
                      <Icon name="loader-2" size="sm" className="animate-spin" />
                      <span>Đang lưu...</span>
                    </div>
                  ) : (
                    'Lưu'
                  )}
                </button>
              )}
            </div>

            {/* XÓA: Đã remove nút "Xóa Agent" theo yêu cầu */}
          </div>
        )}
      </div>
    </div>
  );
};

// Export với tên cũ để backward compatibility
export const AgentConfigHeader = AgentConfigLeftPanel;

/**
 * Demo Component để test Plane Mode
 */
export const PlaneProviderSelectionDemo: React.FC = () => {
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedKeyId, setSelectedKeyId] = useState<string>('');
  const [selectedModelId, setSelectedModelId] = useState<string>('');

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md">
      <h2 className="text-lg font-semibold mb-4">Plane Mode Demo</h2>

      <PlaneProviderSelection
        selectedProvider={selectedProvider}
        selectedKeyId={selectedKeyId}
        selectedModelId={selectedModelId}
        onProviderSelect={setSelectedProvider}
        onKeySelect={setSelectedKeyId}
        onModelSelect={setSelectedModelId}
      />

      {/* Debug Info */}
      <div className="mt-6 p-3 bg-gray-50 dark:bg-gray-700 rounded text-xs">
        <div>
          <strong>Provider:</strong> {selectedProvider || 'None'}
        </div>
        <div>
          <strong>Key ID:</strong> {selectedKeyId || 'None'}
        </div>
        <div>
          <strong>Model ID:</strong> {selectedModelId || 'None'}
        </div>
      </div>
    </div>
  );
};
