import { Button, Icon } from '@/shared/components/common';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import ConvertFieldForm from './ConvertFieldForm';
import ConvertFieldItem, { ConvertField } from './ConvertFieldItem';
import { useAgentConversion, useUpdateAgentConversion } from '../../hooks/useAgentConversion';
import { NotificationUtil } from '@/shared/utils/notification';

// Interface cho dữ liệu cấu hình chuyển đổi
export interface ConvertConfigData {
  fields: ConvertField[];
}

interface ConvertConfigProps {
  agentId?: string;
  initialData?: ConvertConfigData;
  onSave?: (data: ConvertConfigData) => void;
  mode?: 'create' | 'edit';
}

/**
 * Component cấu hình chuyển đổi dữ liệu cho Agent
 */
const ConvertConfig: React.FC<ConvertConfigProps> = ({
  agentId,
  initialData,
  onSave,
  mode = 'create'
}) => {
  const { t } = useTranslation('aiAgents');

  // Fetch conversion data từ API nếu có agentId
  const { data: conversionResponse } = useAgentConversion(agentId && mode === 'edit' ? agentId : '');
  const updateConversionMutation = useUpdateAgentConversion();

  // State cho dữ liệu cấu hình
  const [configData, setConfigData] = useState<ConvertConfigData>(initialData || {
    fields: [
      {
        id: 'field-1',
        name: 'email',
        description: 'Địa chỉ email người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-2',
        name: 'phone',
        description: 'Số điện thoại người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-3',
        name: 'name',
        description: 'Họ tên đầy đủ của người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-4',
        name: 'address',
        description: 'Địa chỉ của người dùng',
        enabled: false,
        type: 'string',
        required: false
      },
      {
        id: 'field-5',
        name: 'birthday',
        description: 'Ngày sinh của người dùng',
        enabled: false,
        type: 'string',
        required: false
      }
    ]
  });



  // State để tránh override khi đang update
  const [isUpdating, setIsUpdating] = useState(false);

  // Update local state khi có data từ API - chỉ khi không đang update
  useEffect(() => {
    if (conversionResponse?.convertConfig && !isUpdating) {
      console.log('🔄 useEffect updating configData from API:', conversionResponse.convertConfig);
      const apiData = conversionResponse;
      // Convert API data to local format
      const convertedFields = apiData.convertConfig.map((field: { name: string; type: string; description?: string; required?: boolean }, index: number) => ({
        id: `field-${index + 1}`,
        name: field.name,
        description: field.description || '',
        enabled: field.required !== undefined ? field.required : true, // Map required -> enabled
        type: field.type as ConvertField['type'],
        required: field.required || false
      }));

      const newConfigData = {
        fields: convertedFields
      };

      setConfigData(newConfigData);
    }
  }, [conversionResponse, isUpdating]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingField, setEditingField] = useState<ConvertField | null>(null);



  // Xử lý khi thay đổi trạng thái bật/tắt của trường
  const handleToggleField = async (id: string) => {
    if (!agentId || mode !== 'edit') {
      // Create mode - chỉ cập nhật local state
      const updatedFields = configData.fields.map(field => {
        if (field.id === id) {
          return { ...field, enabled: !field.enabled };
        }
        return field;
      });
      const newData = {
        ...configData,
        fields: updatedFields
      };
      setConfigData(newData);
      if (onSave) onSave(newData);
      return;
    }

    // Edit mode - gọi API
    try {
      setIsUpdating(true); // Đặt flag để tránh useEffect override

      // Cập nhật UI ngay lập tức
      const updatedFields = configData.fields.map(field => {
        if (field.id === id) {
          return { ...field, enabled: !field.enabled };
        }
        return field;
      });
      const newData = {
        ...configData,
        fields: updatedFields
      };
      setConfigData(newData);

      // Gọi API để lưu vào database
      const convertConfig = updatedFields.map(f => ({
        name: f.name,
        type: f.type,
        description: f.description,
        required: f.required,
        defaultValue: f.type === 'boolean' ? false : f.type === 'number' ? 0 : ""
      }));

      await updateConversionMutation.mutateAsync({
        agentId,
        data: { convertConfig }
      });

      NotificationUtil.success({
        message: 'Cập nhật trạng thái trường thành công!',
      });
    } catch (error) {
      console.error('Error toggling field:', error);
      // Nếu API thất bại, rollback UI về trạng thái cũ
      setConfigData(configData);

      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi cập nhật trạng thái trường.',
      });
    } finally {
      setIsUpdating(false); // Reset flag
    }
  };

  // Xử lý khi thêm trường mới với API call
  const handleAddField = async (field: ConvertField) => {
    console.log('🔄 handleAddField called:', { agentId, mode, field });

    if (!agentId || mode !== 'edit') {
      // Create mode - chỉ cập nhật local state
      console.log('📝 Create mode - updating local state only');
      const newData = {
        ...configData,
        fields: [...configData.fields, field]
      };
      setConfigData(newData);
      setShowAddForm(false);
      if (onSave) onSave(newData);
      return;
    }

    // Edit mode - gọi API
    console.log('🚀 Edit mode - calling API');
    try {
      setIsUpdating(true); // Đặt flag để tránh useEffect override

      // Cập nhật UI ngay lập tức để user thấy field mới
      const updatedFields = [...configData.fields, field];
      const newData = {
        ...configData,
        fields: updatedFields
      };
      setConfigData(newData);
      setShowAddForm(false);

      // Gọi API để lưu vào database
      const convertConfig = updatedFields.map(f => ({
        name: f.name,
        type: f.type,
        description: f.description,
        required: f.required,
        defaultValue: f.type === 'boolean' ? false : f.type === 'number' ? 0 : ""
      }));

      await updateConversionMutation.mutateAsync({
        agentId,
        data: { convertConfig }
      });

      NotificationUtil.success({
        message: 'Thêm trường thành công!',
      });
    } catch (error) {
      console.error('Error adding field:', error);
      // Nếu API thất bại, rollback UI về trạng thái cũ
      setConfigData(configData);
      setShowAddForm(true); // Mở lại form để user thử lại

      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi thêm trường.',
      });
    } finally {
      setIsUpdating(false); // Reset flag
    }
  };

  // Xử lý khi chỉnh sửa trường với API call
  const handleEditField = async (field: ConvertField) => {
    if (!agentId || mode !== 'edit') {
      // Create mode - chỉ cập nhật local state
      const updatedFields = configData.fields.map(f => {
        if (f.id === field.id) {
          return field;
        }
        return f;
      });
      const newData = {
        ...configData,
        fields: updatedFields
      };
      setConfigData(newData);
      setEditingField(null);
      if (onSave) onSave(newData);
      return;
    }

    // Edit mode - gọi API
    try {
      setIsUpdating(true); // Đặt flag để tránh useEffect override

      // Cập nhật UI ngay lập tức
      const updatedFields = configData.fields.map(f => {
        if (f.id === field.id) {
          return field;
        }
        return f;
      });
      const newData = {
        ...configData,
        fields: updatedFields
      };
      setConfigData(newData);
      setEditingField(null);

      // Gọi API để lưu vào database
      const convertConfig = updatedFields.map(f => ({
        name: f.name,
        type: f.type,
        description: f.description,
        required: f.required,
        defaultValue: f.type === 'boolean' ? false : f.type === 'number' ? 0 : ""
      }));

      await updateConversionMutation.mutateAsync({
        agentId,
        data: { convertConfig }
      });

      NotificationUtil.success({
        message: 'Cập nhật trường thành công!',
      });
    } catch (error) {
      console.error('Error updating field:', error);
      // Nếu API thất bại, rollback UI về trạng thái cũ
      setConfigData(configData);
      setEditingField(field); // Mở lại form để user thử lại

      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi cập nhật trường.',
      });
    } finally {
      setIsUpdating(false); // Reset flag
    }
  };

  // Xử lý khi xóa trường
  const handleDeleteField = async (id: string) => {
    const confirmed = window.confirm('Bạn có chắc chắn muốn xóa trường này?');
    if (!confirmed) return;

    if (!agentId || mode !== 'edit') {
      // Create mode - chỉ cập nhật local state
      const updatedFields = configData.fields.filter(field => field.id !== id);
      const newData = {
        ...configData,
        fields: updatedFields
      };
      setConfigData(newData);
      if (onSave) onSave(newData);
      return;
    }

    // Edit mode - gọi API
    try {
      setIsUpdating(true); // Đặt flag để tránh useEffect override

      // Cập nhật UI ngay lập tức
      const updatedFields = configData.fields.filter(field => field.id !== id);
      const newData = {
        ...configData,
        fields: updatedFields
      };
      setConfigData(newData);

      // Gọi API để lưu vào database
      const convertConfig = updatedFields.map(f => ({
        name: f.name,
        type: f.type,
        description: f.description,
        required: f.required,
        defaultValue: f.type === 'boolean' ? false : f.type === 'number' ? 0 : ""
      }));

      await updateConversionMutation.mutateAsync({
        agentId,
        data: { convertConfig }
      });

      NotificationUtil.success({
        message: 'Xóa trường thành công!',
      });
    } catch (error) {
      console.error('Error deleting field:', error);
      // Nếu API thất bại, rollback UI về trạng thái cũ
      setConfigData(configData);

      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi xóa trường.',
      });
    } finally {
      setIsUpdating(false); // Reset flag
    }
  };

  return (
    <ConfigComponentWrapper
      componentId="convert"
      title={
        <div className="flex items-center">
          <Icon name="database" size="md" className="mr-2" />
          <span>Cấu hình chuyển đổi</span>
        </div>
      }
    >
      <div className="p-4 space-y-4 sm:space-y-6">
        {/* Tiêu đề chính */}
        <div className="mb-4 sm:mb-6 text-center">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {t('convertConfig.configureFields')}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t('convertConfig.configureFieldsDescription')}
          </p>
        </div>

        {/* Danh sách các trường */}
        <div className="space-y-3">
          {configData.fields.length > 0 ? (
            configData.fields.map(field => (
              <ConvertFieldItem
                key={field.id}
                field={field}
                onToggle={handleToggleField}
                onEdit={() => setEditingField(field)}
                onDelete={handleDeleteField}
              />
            ))
          ) : (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
              {t('convertConfig.noFields')}
            </div>
          )}
        </div>

        {/* Nút thêm trường mới */}
        {!showAddForm && !editingField && (
          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={() => setShowAddForm(true)}
              className="w-full sm:w-auto"
            >
              <Icon name="plus" size="sm" className="mr-1" />
              {t('convertConfig.addField')}
            </Button>
          </div>
        )}

        {/* Form thêm trường mới */}
        {showAddForm && (
          <ConvertFieldForm
            onSave={handleAddField}
            onCancel={() => setShowAddForm(false)}
            isLoading={agentId && mode === 'edit' ? updateConversionMutation.isPending : false}
          />
        )}

        {/* Form chỉnh sửa trường */}
        {editingField && (
          <ConvertFieldForm
            field={editingField}
            onSave={handleEditField}
            onCancel={() => setEditingField(null)}
            isLoading={agentId && mode === 'edit' ? updateConversionMutation.isPending : false}
          />
        )}


      </div>
    </ConfigComponentWrapper>
  );
};

export default ConvertConfig;
