import { Button, Icon } from '@/shared/components/common';
import React, { useState, useEffect } from 'react';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import ToolSlideInForm from './ToolSlideInForm';
import { Tool, useToolsIntegration } from '../../hooks/useTools';
import { NotificationUtil } from '@/shared/utils/notification';

// Interface cho dữ liệu cấu hình custom tools
interface CustomToolConfigData {
  customToolIds: string[];
}

interface CustomToolConfigProps {
  agentId?: string | undefined;
  initialData?: CustomToolConfigData;
  onSave?: (data: CustomToolConfigData) => void;
  mode?: 'create' | 'edit';
}

/**
 * Component hiển thị một tool item
 */
const ToolItemCard: React.FC<{
  tool: Tool;
  onRemove: (id: string) => void;
}> = ({ tool, onRemove }) => {
  return (
    <div className="flex items-center p-3 bg-orange-50 dark:bg-orange-900/10 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
      {/* Icon */}
      <div className="w-12 h-12 rounded-md overflow-hidden bg-white dark:bg-gray-800 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200 dark:border-gray-700">
        <Icon
          name="wrench"
          size="md"
          className="text-orange-600"
        />
      </div>

      {/* Thông tin */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{tool.name}</h4>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1 space-x-2">
          <span className="truncate">{tool.description}</span>
          <span className="px-1 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
            ID: {tool.id.substring(0, 8)}...
          </span>
          <span className="px-1 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
            {new Date(tool.createdAt).toLocaleDateString('vi-VN')}
          </span>
        </div>
      </div>

      {/* Nút xóa */}
      <Button
        variant="ghost"
        size="sm"
        className="ml-2 text-gray-400 hover:text-red-500"
        onClick={() => onRemove(tool.id)}
        aria-label="Xóa tool"
      >
        <Icon name="trash" size="sm" />
      </Button>
    </div>
  );
};

/**
 * Component cấu hình custom tools cho Agent
 */
const CustomToolConfig: React.FC<CustomToolConfigProps> = ({
  initialData,
  onSave,
  mode = 'create'
}) => {
  // State cho dữ liệu cấu hình
  const [configData, setConfigData] = useState<CustomToolConfigData>(initialData || {
    customToolIds: []
  });

  // API hook để lấy danh sách tools
  const { data: toolsResponse } = useToolsIntegration();

  // State cho form slide-in
  const [showToolForm, setShowToolForm] = useState(false);

  // Lấy danh sách tools từ API
  const allTools: Tool[] = toolsResponse?.result?.items || [];

  // Lấy danh sách tools đã chọn
  const selectedTools = allTools.filter(tool => configData.customToolIds.includes(tool.id));

  // Cập nhật dữ liệu từ initialData
  useEffect(() => {
    if (initialData) {
      setConfigData(initialData);
    }
  }, [initialData]);

  // Xử lý xóa tool
  const handleRemoveTool = (toolId: string) => {
    const newConfigData = {
      ...configData,
      customToolIds: configData.customToolIds.filter(id => id !== toolId)
    };

    setConfigData(newConfigData);

    // Gọi onSave để thông báo cho parent component
    if (onSave) {
      onSave(newConfigData);
    }

    NotificationUtil.success({
      message: 'Đã xóa tool khỏi danh sách!',
    });
  };

  // Xử lý khi thêm tools
  const handleAddTools = () => {
    setShowToolForm(true);
  };

  // Callback để nhận dữ liệu từ ToolSlideInForm
  const handleToolsAdded = (toolIds: string[]) => {
    const newConfigData = {
      ...configData,
      customToolIds: [...new Set([...configData.customToolIds, ...toolIds])] // Remove duplicates
    };

    setConfigData(newConfigData);

    // Gọi onSave để thông báo cho parent component
    if (onSave) {
      onSave(newConfigData);
    }
  };

  return (
    <>
      <ConfigComponentWrapper
        componentId="customTools"
        title={
          <div className="flex items-center">
            <Icon name="wrench" size="md" className="mr-2" />
            <span>Custom Tools</span>
          </div>
        }
      >
        <div className="p-4 space-y-6">
          {/* Tiêu đề chính */}
          <div className="mb-6 text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Cấu hình Custom Tools cho Agent
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Chọn các custom tools mà Agent có thể sử dụng
            </p>
          </div>

          {/* Custom Tools */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-orange-600 flex items-center justify-center mr-2">
                  <Icon name="wrench" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  Custom Tools
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddTools}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                Thêm
              </Button>
            </div>

            <div className="space-y-3">
              {selectedTools && selectedTools.length > 0 ? (
                selectedTools.map((tool, index) => (
                  <ToolItemCard
                    key={`tool-${tool.id}-${index}`}
                    tool={tool}
                    onRemove={handleRemoveTool}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  Chưa có Custom Tool nào
                </div>
              )}
            </div>
          </div>

          {/* Form slide-in cho Tools */}
          <ToolSlideInForm
            isVisible={showToolForm}
            onClose={() => setShowToolForm(false)}
            mode={mode}
            onToolAdded={handleToolsAdded}
            initialSelectedToolIds={configData.customToolIds}
          />
        </div>
      </ConfigComponentWrapper>
    </>
  );
};

export default CustomToolConfig;

// Export các interface để có thể sử dụng ở các file khác
export type { CustomToolConfigData, CustomToolConfigProps };
