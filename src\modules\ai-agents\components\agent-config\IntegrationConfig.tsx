import { Button, Icon, Modal } from '@/shared/components/common';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import React, { useState, useEffect } from 'react';
import FacebookSlideInForm from './FacebookSlideInForm';
import WebsiteSlideInForm from './WebsiteSlideInForm';
import {
  useAgentFacebookPages,
  useAgentWebsites,
  useRemoveFacebookPage,
  useRemoveWebsite,
} from '../../hooks/useAgentIntegration';
import { NotificationUtil } from '@/shared/utils/notification';

import { IntegrationItem, IntegrationsData } from '../../types/integration';

interface IntegrationConfigProps {
  agentId?: string;
  initialData?: IntegrationsData;
  onSave?: (data: IntegrationsData) => void;
  mode?: 'create' | 'edit';
}

/**
 * Component hiển thị một tích hợp (Facebook hoặc Website)
 */
const IntegrationItemCard: React.FC<{
  item: IntegrationItem;
  onRemove: (id: string) => void;
}> = ({ item, onRemove }) => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  // Xác định màu nền dựa trên loại tích hợp
  const bgColorClass = item.type === 'facebook'
    ? 'bg-blue-50 dark:bg-blue-900/10'
    : 'bg-green-50 dark:bg-green-900/10';

  return (
    <div className={`flex items-center p-3 ${bgColorClass} rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm`}>
      {/* Icon/Avatar */}
      <div className="w-12 h-12 rounded-md overflow-hidden bg-white dark:bg-gray-800 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200 dark:border-gray-700">
        {item.icon ? (
          <img src={item.icon} alt={item.name} className="w-full h-full object-cover" />
        ) : (
          <Icon
            name={item.type === 'facebook' ? 'facebook' : 'globe'}
            size="md"
            className={item.type === 'facebook' ? 'text-blue-600' : 'text-green-600'}
          />
        )}
      </div>

      {/* Thông tin */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{item.name}</h4>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
          <Icon name="document" size="sm" className="mr-1" />
          <span className="truncate">{item.id}</span>
        </div>
      </div>

      {/* Nút xóa */}
      <Button
        variant="ghost"
        size="sm"
        className="ml-2 text-gray-400 hover:text-red-500"
        onClick={() => setShowDeleteModal(true)}
        aria-label="Xóa tích hợp"
      >
        <Icon name="trash" size="sm" />
      </Button>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Xác nhận xóa"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-300">
            Bạn có chắc chắn muốn xóa tích hợp "{item.name}" khỏi Agent không?
          </p>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Hủy
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                onRemove(item.id);
                setShowDeleteModal(false);
              }}
            >
              Xóa
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

/**
 * Component cấu hình tích hợp cho Agent
 */
const IntegrationConfig: React.FC<IntegrationConfigProps> = ({
  agentId,
  initialData,
  onSave,
  mode = 'create'
}) => {
  // State cho dữ liệu tích hợp
  const [configData, setConfigData] = useState<IntegrationsData>(initialData || {
    integrations: []
  });



  // API hooks để lấy dữ liệu từ agent
  const {
    data: facebookPagesResponse,
    isLoading: isLoadingFacebook,
    error: facebookError
  } = useAgentFacebookPages(agentId && mode === 'edit' ? agentId : '');

  const {
    data: websitesResponse,
    isLoading: isLoadingWebsites,
    error: websitesError
  } = useAgentWebsites(agentId && mode === 'edit' ? agentId : '');

  // Mutation hooks
  const removeFacebookPageMutation = useRemoveFacebookPage();
  const removeWebsiteMutation = useRemoveWebsite();

  // State cho các form slide-in
  const [showFacebookForm, setShowFacebookForm] = useState(false);
  const [showWebsiteForm, setShowWebsiteForm] = useState(false);

  // Cập nhật dữ liệu từ API
  useEffect(() => {
    if (mode === 'edit' && agentId) {
      const integrations: IntegrationItem[] = [];

      // Thêm Facebook pages từ API
      if (facebookPagesResponse?.facebookPages) {
        const facebookIntegrations = facebookPagesResponse.facebookPages.map((page) => ({
          id: page.id,
          name: page.pageName,
          type: 'facebook' as const,
          icon: page.avatarPage || undefined,
          isConnected: true,
          status: 'active' as const
        }));
        integrations.push(...facebookIntegrations);
      }

      // Thêm Websites từ API
      if (websitesResponse?.websites) {
        const websiteIntegrations = websitesResponse.websites.map((website) => ({
          id: website.id,
          name: website.websiteName,
          type: 'website' as const,
          url: website.host,
          icon: undefined,
          isConnected: website.isActive,
          status: (website.verify ? 'active' : 'pending') as 'active' | 'pending' | 'error' | undefined
        }));
        integrations.push(...websiteIntegrations);
      }

      setConfigData({ integrations });
    } else if (!initialData) {
      // Create mode - empty state
      setConfigData({ integrations: [] });
    }
  }, [facebookPagesResponse, websitesResponse, initialData, mode, agentId]);

  // Lọc các tích hợp theo loại
  const facebookIntegrations = configData.integrations.filter(item => item.type === 'facebook');
  const websiteIntegrations = configData.integrations.filter(item => item.type === 'website');



  // Xử lý xóa Facebook Page với API call
  const handleRemoveFacebookPageWithAPI = async (pageId: string) => {
    if (!agentId || mode !== 'edit') return;

    try {
      await removeFacebookPageMutation.mutateAsync({
        agentId,
        pageId
      });

      NotificationUtil.success({
        message: 'Xóa tích hợp Facebook thành công!',
      });
    } catch {
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi xóa tích hợp Facebook.',
      });
    }
  };

  // Xử lý xóa Website với API call
  const handleRemoveWebsiteWithAPI = async (websiteId: string) => {
    if (!agentId || mode !== 'edit') return;

    try {
      await removeWebsiteMutation.mutateAsync({
        agentId,
        websiteId
      });

      NotificationUtil.success({
        message: 'Xóa tích hợp Website thành công!',
      });
    } catch {
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi xóa tích hợp Website.',
      });
    }
  };

  // Xử lý khi xóa một tích hợp - phân biệt theo mode
  const handleRemoveIntegration = (id: string) => {
    const item = configData.integrations.find(integration => integration.id === id);
    if (!item) return;

    if (mode === 'edit') {
      // Edit mode: gọi API
      if (item.type === 'facebook') {
        handleRemoveFacebookPageWithAPI(id);
      } else if (item.type === 'website') {
        handleRemoveWebsiteWithAPI(id);
      }
    } else {
      // Create mode: chỉ cập nhật local state
      const updatedIntegrations = configData.integrations.filter(integration => integration.id !== id);
      const newConfigData = { integrations: updatedIntegrations };
      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }
    }
  };

  // Xử lý khi thêm một tích hợp mới
  const handleAddIntegration = (type: 'facebook' | 'website') => {
    if (type === 'facebook') {
      setShowFacebookForm(true);
    } else {
      setShowWebsiteForm(true);
    }
  };

  // Callback để nhận dữ liệu từ FacebookSlideInForm
  const handleFacebookFormSave = (selectedIds: string[]) => {
    if (mode === 'create') {
      // Create mode: cập nhật local state
      const newFacebookIntegrations = selectedIds.map(id => ({
        id,
        name: `Facebook Page ${id}`,
        type: 'facebook' as const,
        isConnected: true,
        status: 'active' as const
      }));

      const updatedIntegrations = [
        ...configData.integrations.filter(item => item.type !== 'facebook'),
        ...newFacebookIntegrations
      ];

      const newConfigData = { integrations: updatedIntegrations };
      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }
    }
    setShowFacebookForm(false);
  };

  // Callback để nhận dữ liệu từ WebsiteSlideInForm
  const handleWebsiteFormSave = (selectedIds: string[]) => {
    if (mode === 'create') {
      // Create mode: cập nhật local state
      const newWebsiteIntegrations = selectedIds.map(id => ({
        id,
        name: `Website ${id}`,
        type: 'website' as const,
        isConnected: true,
        status: 'active' as const
      }));

      const updatedIntegrations = [
        ...configData.integrations.filter(item => item.type !== 'website'),
        ...newWebsiteIntegrations
      ];

      const newConfigData = { integrations: updatedIntegrations };
      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }
    }
    setShowWebsiteForm(false);
  };



  return (
    <>
      <ConfigComponentWrapper
        componentId="integration"
        title={
          <div className="flex items-center">
            <Icon name="link" size="md" className="mr-2" />
            <span>Tích hợp</span>
          </div>
        }
      >
        <div className="p-4 space-y-6">
          {/* Tiêu đề chính */}
          <div className="mb-6 text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Kết nối Agent với các nền tảng
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Kết nối Agent với các trang Facebook và Website để tương tác với người dùng
            </p>
          </div>

          {/* Tích hợp Facebook */}
          <div className="pb-6 mb-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center mr-2">
                  <Icon name="facebook" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  Tích hợp Facebook
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddIntegration('facebook')}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                Thêm
              </Button>
            </div>

            <div className="space-y-3">
              {isLoadingFacebook ? (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <Icon name="loader-circle" size="md" className="animate-spin mx-auto mb-2" />
                  Đang tải danh sách Facebook Pages...
                </div>
              ) : facebookError ? (
                <div className="text-center py-4 text-red-500 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800">
                  <Icon name="alert-circle" size="md" className="mx-auto mb-2" />
                  Lỗi khi tải danh sách Facebook Pages
                </div>
              ) : facebookIntegrations.length > 0 ? (
                facebookIntegrations.map(item => (
                  <IntegrationItemCard
                    key={item.id}
                    item={item}
                    onRemove={handleRemoveIntegration}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  Chưa có tích hợp Facebook nào
                </div>
              )}
            </div>
          </div>

          {/* Form slide-in cho Facebook */}
          <FacebookSlideInForm
            isVisible={showFacebookForm}
            onClose={() => setShowFacebookForm(false)}
            onSave={handleFacebookFormSave}
            agentId={agentId}
            mode={mode}
          />

          {/* Tích hợp Website */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center mr-2">
                  <Icon name="globe" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  Tích hợp Website
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddIntegration('website')}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                Thêm
              </Button>
            </div>

            <div className="space-y-3">
              {isLoadingWebsites ? (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <Icon name="loader-circle" size="md" className="animate-spin mx-auto mb-2" />
                  Đang tải danh sách Websites...
                </div>
              ) : websitesError ? (
                <div className="text-center py-4 text-red-500 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800">
                  <Icon name="alert-circle" size="md" className="mx-auto mb-2" />
                  Lỗi khi tải danh sách Websites
                </div>
              ) : websiteIntegrations.length > 0 ? (
                websiteIntegrations.map(item => (
                  <IntegrationItemCard
                    key={item.id}
                    item={item}
                    onRemove={handleRemoveIntegration}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  Chưa có tích hợp Website nào
                </div>
              )}
            </div>
          </div>
          {/* Form slide-in cho Website */}
          <WebsiteSlideInForm
            isVisible={showWebsiteForm}
            onClose={() => setShowWebsiteForm(false)}
            onSave={handleWebsiteFormSave}
            agentId={agentId}
            mode={mode}
          />


        </div>
      </ConfigComponentWrapper>
    </>
  );
};

export default IntegrationConfig;

// Export các interface để có thể sử dụng ở các file khác
export type { IntegrationConfigProps };

