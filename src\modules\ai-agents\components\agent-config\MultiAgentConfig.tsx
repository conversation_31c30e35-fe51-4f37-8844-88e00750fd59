import { Button, EmptyState, Icon } from '@/shared/components/common';
import React, { useState, useEffect } from 'react';
import { MultiAgentConfigData, MultiAgentItem as MultiAgentItemType } from '../../types/agent';
import { TypeAgent } from '../agent-add/TypeAgentCard';
import AgentSelectForm from './AgentSelectForm';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import MultiAgentItem from './MultiAgentItem';
import { useGetSimpleAgents } from '../../hooks/useAgent';
import { useParams } from 'react-router-dom';
import { getMultiAgents, addMultiAgent, removeMultiAgents } from '../../api/multiAgent.api';

interface MultiAgentConfigProps {
  /**
   * ID của agent cha
   */
  agentId?: string;

  /**
   * Dữ liệu cấu hình multi-agent ban đầu
   */
  initialData?: MultiAgentConfigData;

  /**
   * Callback khi lưu cấu hình
   */
  onSave?: (data: MultiAgentConfigData) => void;

  /**
   * <PERSON>h sách các type agent có thể chọn
   */
  availableAgents?: TypeAgent[];
}

/**
 * Component cấu hình multi-agent cho Agent
 */
const MultiAgentConfig: React.FC<MultiAgentConfigProps> = ({
  agentId: propAgentId,
  initialData,
  onSave
}) => {
  // Lấy agent ID từ URL params nếu không có propAgentId
  const { id: urlAgentId } = useParams<{ id: string }>();
  const agentId = propAgentId || urlAgentId;

  // Fetch simple agents cho mode create
  const { data: simpleAgentsData } = useGetSimpleAgents(
    { page: 1, limit: 50 }
  );

  const [configData, setConfigData] = useState<MultiAgentConfigData>(
    initialData || { agents: [] }
  );
  const [showAddForm, setShowAddForm] = useState(false);
  const [multiAgents, setMultiAgents] = useState<MultiAgentItemType[]>([]);

  // Fetch multi-agent relations từ API nếu có agentId (mode edit)
  useEffect(() => {
    if (agentId) {
      getMultiAgents(agentId, { page: 1, limit: 10 }).then((data: unknown) => {
        const typedData = data as { items: Array<{ childAgentId: string; name: string; avatar?: string; prompt?: string }> };
        // Convert API data to local format
        const convertedAgents: MultiAgentItemType[] = typedData.items.map((item) => ({
          id: item.childAgentId,
          name: item.name,
          avatar: item.avatar || '',
          agentTypeId: '1',
          description: '',
          prompt: item.prompt || ''
        }));
        setMultiAgents(convertedAgents);
      }).catch((error) => {
        console.error('Error fetching multi agents:', error);
      });
    }
  }, [agentId]);

  // Helper function để save data (chỉ dùng cho mode create)
  const saveMultiAgentData = (newData: MultiAgentConfigData) => {
    if (!agentId && onSave) {
      onSave(newData);
    }
  };

  // Thêm agent con ở mode edit
  const handleAddAgent = async (newAgent: MultiAgentItemType) => {
    if (agentId) {
      try {
        await addMultiAgent(agentId, [{ agent_id: newAgent.id, prompt: newAgent.prompt }]);
        const data = await getMultiAgents(agentId, { page: 1, limit: 10 });
        const typedData = data as { items: Array<{ childAgentId: string; name: string; avatar?: string; prompt?: string }> };
        const convertedAgents: MultiAgentItemType[] = typedData.items.map((item) => ({
          id: item.childAgentId,
          name: item.name,
          avatar: item.avatar || '',
          agentTypeId: '1',
          description: '',
          prompt: item.prompt || ''
        }));
        setMultiAgents(convertedAgents);
        setShowAddForm(false);
      } catch (error) {
        console.error('Error adding multi agent:', error);
      }
    } else {
      // Mode create: logic cũ
      const updatedData = {
        ...configData,
        agents: [...configData.agents, newAgent]
      };
      setConfigData(updatedData);
      setShowAddForm(false);
      saveMultiAgentData(updatedData);
    }
  };

  // Xóa agent con ở mode edit
  const handleRemoveAgent = async (childAgentId: string) => {
    if (agentId) {
      try {
        await removeMultiAgents(agentId, [childAgentId]);
        const data = await getMultiAgents(agentId, { page: 1, limit: 10 });
        const typedData = data as { items: Array<{ childAgentId: string; name: string; avatar?: string; prompt?: string }> };
        const convertedAgents: MultiAgentItemType[] = typedData.items.map((item) => ({
          id: item.childAgentId,
          name: item.name,
          avatar: item.avatar || '',
          agentTypeId: '1',
          description: '',
          prompt: item.prompt || ''
        }));
        setMultiAgents(convertedAgents);
      } catch (error) {
        console.error('Error removing multi agent:', error);
      }
    } else {
      // Mode create: logic cũ
      const updatedData = {
        ...configData,
        agents: configData.agents.filter(agent => agent.id !== childAgentId)
      };
      setConfigData(updatedData);
      saveMultiAgentData(updatedData);
    }
  };

  // Cập nhật prompt/mô tả agent con ở mode edit
  const handleUpdateAgent = async (updatedAgent: MultiAgentItemType) => {
    if (agentId) {
      try {
        await addMultiAgent(agentId, [{ agent_id: updatedAgent.id, prompt: updatedAgent.prompt }]);
        const data = await getMultiAgents(agentId, { page: 1, limit: 10 });
        const typedData = data as { items: Array<{ childAgentId: string; name: string; avatar?: string; prompt?: string }> };
        const convertedAgents: MultiAgentItemType[] = typedData.items.map((item) => ({
          id: item.childAgentId,
          name: item.name,
          avatar: item.avatar || '',
          agentTypeId: '1',
          description: '',
          prompt: item.prompt || ''
        }));
        setMultiAgents(convertedAgents);
      } catch (error) {
        console.error('Error updating multi agent:', error);
      }
    } else {
      // Mode create: logic cũ
      const updatedData = {
        ...configData,
        agents: configData.agents.map(agent =>
          agent.id === updatedAgent.id ? updatedAgent : agent
        )
      };
      setConfigData(updatedData);
      saveMultiAgentData(updatedData);
    }
  };

  // Xử lý di chuyển agent lên
  const handleMoveUp = (agentId: string) => {
    const currentIndex = configData.agents.findIndex(agent => agent.id === agentId);
    if (currentIndex <= 0) return;

    const newAgents = [...configData.agents];
    const temp = newAgents[currentIndex - 1];
    const current = newAgents[currentIndex];
    if (temp && current) {
      newAgents[currentIndex - 1] = current;
      newAgents[currentIndex] = temp;
    }

    const updatedData = {
      ...configData,
      agents: newAgents
    };

    setConfigData(updatedData);
    saveMultiAgentData(updatedData);
  };

  // Xử lý di chuyển agent xuống
  const handleMoveDown = (agentId: string) => {
    const currentIndex = configData.agents.findIndex(agent => agent.id === agentId);
    if (currentIndex >= configData.agents.length - 1) return;

    const newAgents = [...configData.agents];
    const current = newAgents[currentIndex];
    const next = newAgents[currentIndex + 1];
    if (current && next) {
      newAgents[currentIndex] = next;
      newAgents[currentIndex + 1] = current;
    }

    const updatedData = {
      ...configData,
      agents: newAgents
    };

    setConfigData(updatedData);
    saveMultiAgentData(updatedData);
  };

  // Tạo danh sách agents cho select (dùng cho cả edit và create)
  const agentsForSelect: MultiAgentItemType[] = simpleAgentsData?.result?.items
    ? simpleAgentsData.result.items.map((item) => ({
        id: item.id,
        name: item.name,
        avatar: item.avatar || '',
        description: '',
        agentTypeId: '1',
        prompt: ''
      }))
    : [];

  return (
    <ConfigComponentWrapper
      componentId="multiAgent"
      title={
        <div className="flex items-center">
          <span className="mr-2">
            <Icon name="users" size="sm" />
          </span>
          <span>Cấu hình Multi-Agent</span>
        </div>
      }
    >
      <div className="p-4 space-y-4">
        {/* Mô tả */}
        <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
          Cấu hình nhiều agent để làm việc cùng nhau. Mỗi agent sẽ có vai trò và chức năng riêng biệt.
        </div>

        {/* Form thêm agent mới */}
        <AgentSelectForm
          availableAgents={agentsForSelect}
          onAddAgent={handleAddAgent}
          onCancel={() => setShowAddForm(false)}
          isVisible={showAddForm}
          agentId={agentId || ''}
          mode={agentId ? 'edit' : 'create'}
        />

        {/* Nút thêm agent */}
        {!showAddForm && (
          <Button
            variant="outline"
            onClick={() => setShowAddForm(true)}
            leftIcon={<Icon name="plus" size="sm" />}
            className="w-full"
          >
            Thêm Agent
          </Button>
        )}

        {/* Danh sách agents */}
        {(agentId ? multiAgents.length > 0 : configData.agents.length > 0) ? (
          <div className="space-y-3">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Danh sách Agents ({agentId ? multiAgents.length : configData.agents.length})
            </div>
            {(agentId ? multiAgents : configData.agents).map((agent, index) => (
              <MultiAgentItem
                key={agent.id}
                agent={agent}
                onUpdate={handleUpdateAgent}
                onRemove={handleRemoveAgent}
                canMoveUp={index > 0}
                canMoveDown={index < (agentId ? multiAgents.length : configData.agents.length) - 1}
                onMoveUp={handleMoveUp}
                onMoveDown={handleMoveDown}
                agentId={agentId || ''}
                mode={agentId ? 'edit' : 'create'}
              />
            ))}
          </div>
        ) : (
          !showAddForm && (
            <EmptyState
              icon="users"
              title="Chưa có agent nào"
              description="Thêm agent đầu tiên để bắt đầu cấu hình multi-agent"
              className="py-8"
            />
          )
        )}
      </div>
    </ConfigComponentWrapper>
  );
};

export default MultiAgentConfig;
