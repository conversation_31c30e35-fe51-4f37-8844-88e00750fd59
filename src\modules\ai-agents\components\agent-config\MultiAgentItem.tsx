import { <PERSON><PERSON>, Card, Icon, Textarea } from '@/shared/components/common';
import React, { useState } from 'react';
import { MultiAgentItem as MultiAgentItemType } from '../../types/agent';

interface MultiAgentItemProps {
  /**
   * Dữ liệu agent item
   */
  agent: MultiAgentItemType;

  /**
   * Callback khi cập nhật agent
   */
  onUpdate?: (agent: MultiAgentItemType) => void;

  /**
   * Callback khi xóa agent
   */
  onRemove?: (agentId: string) => void;

  /**
   * <PERSON><PERSON> thể di chuyển lên không
   */
  canMoveUp?: boolean;

  /**
   * <PERSON><PERSON> thể di chuyển xuống không
   */
  canMoveDown?: boolean;

  /**
   * Callback khi di chuyển lên
   */
  onMoveUp?: (agentId: string) => void;

  /**
   * Callback khi di chuyển xuống
   */
  onMoveDown?: (agentId: string) => void;

  /**
   * ID của agent
   */
  agentId?: string;

  /**
   * Mode của component
   */
  mode?: 'edit' | 'create';
}

/**
 * Component hiển thị từng agent trong danh sách multi-agent
 */
const MultiAgentItem: React.FC<MultiAgentItemProps> = ({
  agent,
  onUpdate,
  onRemove,
  canMoveUp = false,
  canMoveDown = false,
  onMoveUp,
  onMoveDown,
  agentId,
  mode = 'create',
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editDescription, setEditDescription] = useState(agent.customDescription || agent.description);

  // Xử lý lưu mô tả
  const handleSaveDescription = () => {
    if (onUpdate) {
      onUpdate({
        ...agent,
        customDescription: editDescription
      });
    }
    setIsEditing(false);
  };

  // Xử lý hủy chỉnh sửa
  const handleCancelEdit = () => {
    setEditDescription(agent.customDescription || agent.description);
    setIsEditing(false);
  };

  // Xử lý xóa agent
  const handleRemove = async () => {
    if (window.confirm(`Bạn có chắc chắn muốn xóa agent "${agent.name}"?`)) {
      if (mode === 'edit' && agentId) {
        try {
          const { removeMultiAgents, getMultiAgents } = await import('../../api/multiAgent.api');
          await removeMultiAgents(agentId, [agent.id]);
          // Refetch lại danh sách agent con sau khi xóa
          await getMultiAgents(agentId);
          if (onRemove) onRemove(agent.id);
          return;
        } catch (error) {
          console.error('Error removing multi agent:', error);
          return;
        }
      }
      // Mode create: logic cũ
      if (onRemove) {
        onRemove(agent.id);
      }
    }
  };

  return (
    <Card className="p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start gap-4">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
            {agent.avatar ? (
              <img
                src={agent.avatar}
                alt={agent.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Icon name="user" size="md" className="text-white" />
            )}
          </div>
        </div>

        {/* Thông tin agent */}
        <div className="flex-grow min-w-0">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold text-gray-900 dark:text-white truncate">
              {agent.name}
            </h4>

            {/* Nút điều khiển */}
            <div className="flex items-center gap-1">
              {/* Nút di chuyển */}
              {canMoveUp && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onMoveUp?.(agent.id)}
                  className="p-1"
                  title="Di chuyển lên"
                >
                  <Icon name="chevron-up" size="sm" />
                </Button>
              )}

              {canMoveDown && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onMoveDown?.(agent.id)}
                  className="p-1"
                  title="Di chuyển xuống"
                >
                  <Icon name="chevron-down" size="sm" />
                </Button>
              )}

              {/* Nút chỉnh sửa */}
              {!isEditing && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                  className="p-1"
                  title="Chỉnh sửa mô tả"
                >
                  <Icon name="edit" size="sm" />
                </Button>
              )}

              {/* Nút xóa */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemove}
                className="p-1 text-red-500 hover:text-red-700"
                title="Xóa agent"
              >
                <Icon name="trash" size="sm" />
              </Button>
            </div>
          </div>

          {/* Mô tả */}
          <div className="text-sm text-gray-600 dark:text-gray-300">
            {isEditing ? (
              <div className="space-y-2">
                <Textarea
                  value={editDescription}
                  onChange={(e) => setEditDescription(e.target.value)}
                  placeholder="Nhập mô tả cho agent..."
                  className="w-full"
                  rows={3}
                />
                <div className="flex gap-2">
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleSaveDescription}
                  >
                    Lưu
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelEdit}
                  >
                    Hủy
                  </Button>
                </div>
              </div>
            ) : (
              <p>{agent.customDescription || agent.description}</p>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MultiAgentItem;
