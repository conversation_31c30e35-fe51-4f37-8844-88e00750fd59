import { Media, Product, Url } from '@/modules/ai-agents/types/response';
import { Button, Icon, Modal, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import React, { useState, useEffect } from 'react';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import MediaSlideInForm from './MediaSlideInForm';
import ProductSlideInForm from './ProductSlideInForm';
import UrlSlideInForm from './UrlSlideInForm';
import {
  useAgentMedia,
  useAgentProducts,
  useAgentUrls,
  useRemoveMedia,
  useRemoveProducts,
  useRemoveUrls
} from '../../hooks/useAgentResources';
import { NotificationUtil } from '@/shared/utils/notification';
import { useQueryClient } from '@tanstack/react-query';

// Interface cho dữ liệu cấu hình phản hồi
interface ResponseConfigData {
  media: Media[];
  urls: Url[];
  products: Product[];
}

interface ResponseConfigProps {
  agentId?: string | undefined;
  initialData?: ResponseConfigData;
  onSave?: (data: ResponseConfigData) => void;
  mode?: 'create' | 'edit';
}

/**
 * Component hiển thị một item tài nguyên (Media, URL, Product)
 */
const ResourceItemCard: React.FC<{
  item: Media | Url | Product;
  type: 'media' | 'url' | 'product';
  onRemove: (id: string) => void;
}> = ({ item, type, onRemove }) => {
  const { t } = useTranslation(['common', 'aiAgents']);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  // Xác định màu nền dựa trên loại tài nguyên
  const getBgColorClass = () => {
    switch (type) {
      case 'media':
        return 'bg-purple-50 dark:bg-purple-900/10';
      case 'url':
        return 'bg-blue-50 dark:bg-blue-900/10';
      case 'product':
        return 'bg-green-50 dark:bg-green-900/10';
      default:
        return 'bg-gray-50 dark:bg-gray-800/10';
    }
  };

  // Xác định icon dựa trên loại tài nguyên
  const getIconName = () => {
    if (type === 'media') {
      const mediaItem = item as Media;
      return mediaItem.type === 'image' ? 'image' :
        mediaItem.type === 'video' ? 'video' :
          mediaItem.type === 'audio' ? 'music' : 'file';
    } else if (type === 'url') {
      return 'link';
    } else if (type === 'product') {
      return 'box';
    }
    return 'document';
  };

  // Xác định tiêu đề và mô tả dựa trên loại tài nguyên
  const getTitle = () => {
    if (type === 'media') {
      return (item as Media).name;
    } else if (type === 'url') {
      return (item as Url).title;
    } else if (type === 'product') {
      return (item as Product).name;
    }
    return '';
  };

  const getDescription = () => {
    if (type === 'media') {
      const mediaItem = item as Media;
      return `${mediaItem.format?.toUpperCase() || ''} ${mediaItem.fileSize ? `• ${(mediaItem.fileSize / 1024 / 1024).toFixed(2)} MB` : ''}`;
    } else if (type === 'url') {
      return (item as Url).url;
    } else if (type === 'product') {
      const productItem = item as Product;
      const price = productItem.salePrice || productItem.price;
      return `${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price)} • ${productItem.category || ''}`;
    }
    return '';
  };

  // Xác định hình ảnh dựa trên loại tài nguyên
  const getImageUrl = () => {
    if (type === 'media') {
      return (item as Media).thumbnailUrl;
    } else if (type === 'product') {
      return (item as Product).imageUrl;
    }
    return undefined;
  };

  return (
    <div className={`flex items-center p-3 ${getBgColorClass()} rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm`}>
      {/* Icon/Avatar */}
      <div className="w-12 h-12 rounded-md overflow-hidden bg-white dark:bg-gray-800 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200 dark:border-gray-700">
        {getImageUrl() ? (
          <img src={getImageUrl()} alt={getTitle()} className="w-full h-full object-cover" />
        ) : (
          <Icon
            name={getIconName()}
            size="md"
            className={`text-${type === 'media' ? 'purple' : type === 'url' ? 'blue' : 'green'}-600`}
          />
        )}
      </div>

      {/* Thông tin */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{getTitle()}</h4>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span className="truncate">{getDescription()}</span>
        </div>
      </div>

      {/* Nút xóa */}
      <Button
        variant="ghost"
        size="sm"
        className="ml-2 text-gray-400 hover:text-red-500"
        onClick={() => setShowDeleteModal(true)}
        aria-label="Xóa tài nguyên"
      >
        <Icon name="trash" size="sm" />
      </Button>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              variant="danger"
              onClick={() => {
                onRemove(String(item.id));
                setShowDeleteModal(false);
              }}
            >
              {t('common:delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-4">
            {t('aiAgents:resources.confirmDeleteResource', 'Bạn có chắc chắn muốn xóa tài nguyên "{{resourceName}}" khỏi Agent không?', { resourceName: getTitle() })}
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            {t('aiAgents:resources.deleteResourceWarning', 'Hành động này không thể hoàn tác.')}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

/**
 * Component cấu hình tài nguyên phản hồi cho Agent
 */
const ResourcesConfig: React.FC<ResponseConfigProps> = ({
  agentId,
  initialData,
  onSave,
  mode = 'create'
}) => {
  // State cho dữ liệu cấu hình
  const [configData, setConfigData] = useState<ResponseConfigData>(initialData || {
    media: [],
    urls: [],
    products: []
  });

  // API hooks để lấy dữ liệu từ agent
  const {
    data: mediaResponse,
    // isLoading: isLoadingMedia, // Unused for now
    // error: mediaError // Unused for now
  } = useAgentMedia(agentId && mode === 'edit' ? agentId : '');

  const {
    data: urlsResponse,
    // isLoading: isLoadingUrls, // Unused for now
    // error: urlsError // Unused for now
  } = useAgentUrls(agentId && mode === 'edit' ? agentId : '');

  const {
    data: productsResponse,
    // isLoading: isLoadingProducts, // Unused for now
    // error: productsError // Unused for now
  } = useAgentProducts(agentId && mode === 'edit' ? agentId : '');

  // Mutation hooks
  const removeMediaMutation = useRemoveMedia();
  const removeUrlsMutation = useRemoveUrls();
  const removeProductsMutation = useRemoveProducts();

  // State cho các form slide-in
  const [showMediaForm, setShowMediaForm] = useState(false);
  const [showUrlForm, setShowUrlForm] = useState(false);
  const [showProductForm, setShowProductForm] = useState(false);

  const queryClient = useQueryClient();

  // Cập nhật dữ liệu từ API - chỉ khi có đủ dữ liệu
  useEffect(() => {
    if (mode === 'edit' && agentId && mediaResponse && urlsResponse && productsResponse) {
      const newConfigData: ResponseConfigData = {
        media: mediaResponse?.items || [],
        urls: urlsResponse?.items || [],
        products: productsResponse?.items || []
      };

      setConfigData(newConfigData);
    } else if (mode === 'create' && !initialData) {
      // Create mode - empty state
      setConfigData({ media: [], urls: [], products: [] });
    }
  }, [mediaResponse, urlsResponse, productsResponse, initialData, mode, agentId]);

  // Xử lý xóa media
  const handleRemoveMedia = async (id: string) => {
    if (mode === 'edit' && agentId) {
      // Edit mode: gọi API
      try {
        await removeMediaMutation.mutateAsync({
          agentId,
          data: { mediaIds: [id] }
        });

        NotificationUtil.success({
          message: 'Xóa media thành công!',
        });
      } catch {
        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi xóa media.',
        });
      }
    } else {
      // Create mode: xóa khỏi local state
      setConfigData(prev => ({
        ...prev,
        media: prev.media.filter(item => String(item.id) !== id)
      }));

      NotificationUtil.success({
        message: 'Đã xóa media khỏi danh sách!',
      });
    }
  };

  // Xử lý xóa URL
  const handleRemoveUrl = async (id: string) => {
    if (mode === 'edit' && agentId) {
      // Edit mode: gọi API
      try {
        await removeUrlsMutation.mutateAsync({
          agentId,
          data: { urlIds: [id] }
        });

        NotificationUtil.success({
          message: 'Xóa URL thành công!',
        });
      } catch {
        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi xóa URL.',
        });
      }
    } else {
      // Create mode: xóa khỏi local state
      setConfigData(prev => ({
        ...prev,
        urls: prev.urls.filter(item => String(item.id) !== id)
      }));

      NotificationUtil.success({
        message: 'Đã xóa URL khỏi danh sách!',
      });
    }
  };

  // Xử lý xóa product
  const handleRemoveProduct = async (id: string) => {
    if (mode === 'edit' && agentId) {
      try {
        await removeProductsMutation.mutateAsync({
          agentId,
          data: { productIds: [Number(id)] }
        });
        queryClient.invalidateQueries({
          queryKey: ['agent-resources-products', agentId],
        });
        NotificationUtil.success({
          message: 'Xóa sản phẩm thành công!',
        });
      } catch (error: any) {
        setConfigData(prev => ({
          ...prev,
          products: prev.products.filter(item => String(item.id) !== id)
        }));
        queryClient.invalidateQueries({
          queryKey: ['agent-resources-products', agentId],
        });
        NotificationUtil.info({
          message: 'Sản phẩm đã không còn trong agent, UI đã cập nhật lại!',
        });
      }
    } else {
      setConfigData(prev => ({
        ...prev,
        products: prev.products.filter(item => String(item.id) !== id)
      }));
      NotificationUtil.success({
        message: 'Đã xóa sản phẩm khỏi danh sách!',
      });
    }
  };

  // Xử lý khi thêm tài nguyên
  const handleAddResource = (type: 'media' | 'url' | 'product') => {
    if (type === 'media') {
      setShowMediaForm(true);
    } else if (type === 'url') {
      setShowUrlForm(true);
    } else if (type === 'product') {
      setShowProductForm(true);
    }
  };

  // Callback để nhận dữ liệu từ các form
  const handleResourceAdded = (type: 'media' | 'urls' | 'products', items: Media[] | Url[] | Product[]) => {
    if (mode === 'create') {
      const newConfigData = {
        ...configData,
        [type]: items
      };

      setConfigData(newConfigData);

      // Gọi onSave để thông báo cho parent component
      if (onSave) {
        onSave(newConfigData);
      }
    }
  };

  return (
    <>
      <ConfigComponentWrapper
        componentId="resources"
        title={
          <div className="flex items-center">
            <Icon name="message-square" size="md" className="mr-2" />
            <span>Tài nguyên phản hồi</span>
          </div>
        }
      >
        <div className="p-4 space-y-6">
          {/* Tiêu đề chính */}
          <div className="mb-6 text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Cấu hình tài nguyên phản hồi cho Agent
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Chọn các tài nguyên mà Agent có thể sử dụng để phản hồi người dùng
            </p>
          </div>

          {/* Tài nguyên Media */}
          <div className="pb-6 mb-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-purple-600 flex items-center justify-center mr-2">
                  <Icon name="image" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  Tài nguyên Media
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddResource('media')}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                Thêm
              </Button>
            </div>

            <div className="space-y-3">
              {configData.media && configData.media.length > 0 ? (
                configData.media.map((item, index) => (
                  <ResourceItemCard
                    key={`media-${item.id}-${index}`}
                    item={item}
                    type="media"
                    onRemove={handleRemoveMedia}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  Chưa có tài nguyên Media nào
                </div>
              )}
            </div>
          </div>

          {/* Form slide-in cho Media */}
          <MediaSlideInForm
            isVisible={showMediaForm}
            onClose={() => setShowMediaForm(false)}
            agentId={agentId || ''}
            mode={mode}
            onResourceAdded={(items) => handleResourceAdded('media', items)}
            initialSelectedItems={configData.media}
          />

          {/* Tài nguyên URL */}
          <div className="pb-6 mb-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center mr-2">
                  <Icon name="link" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  Tài nguyên URL
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddResource('url')}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                Thêm
              </Button>
            </div>

            <div className="space-y-3">
              {configData.urls && configData.urls.length > 0 ? (
                configData.urls.map((item, index) => (
                  <ResourceItemCard
                    key={`url-${item.id}-${index}`}
                    item={item}
                    type="url"
                    onRemove={handleRemoveUrl}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  Chưa có tài nguyên URL nào
                </div>
              )}
            </div>
          </div>

          {/* Form slide-in cho URL */}
          <UrlSlideInForm
            isVisible={showUrlForm}
            onClose={() => setShowUrlForm(false)}
            agentId={agentId || ''}
            mode={mode}
            onResourceAdded={(items) => handleResourceAdded('urls', items)}
            initialSelectedItems={configData.urls}
          />

          {/* Tài nguyên Product */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center mr-2">
                  <Icon name="box" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  Tài nguyên Sản phẩm
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddResource('product')}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                Thêm
              </Button>
            </div>

            <div className="space-y-3">
              {configData.products && configData.products.length > 0 ? (
                configData.products.map((item, index) => (
                  <ResourceItemCard
                    key={`product-${item.id}-${index}`}
                    item={item}
                    type="product"
                    onRemove={handleRemoveProduct}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  Chưa có tài nguyên Sản phẩm nào
                </div>
              )}
            </div>
          </div>

          {/* Form slide-in cho Product */}
          <ProductSlideInForm
            isVisible={showProductForm}
            onClose={() => setShowProductForm(false)}
            agentId={agentId || ''}
            mode={mode}
            onResourceAdded={(items) => handleResourceAdded('products', items)}
            initialSelectedItems={configData.products}
          />
        </div>
      </ConfigComponentWrapper>
    </>
  );
};

export default ResourcesConfig;

// Export các interface để có thể sử dụng ở các file khác
export type { Media, Product, ResponseConfigData, ResponseConfigProps, Url };

