import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  <PERSON>ton,
  Card,
  Icon,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { Tool, useToolsIntegration } from '../../hooks/useTools';
import { NotificationUtil } from '@/shared/utils/notification';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * Props cho component ToolSlideInForm
 */
interface ToolSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';

  /**
   * Callback khi có tool được thêm (chỉ dùng cho mode create)
   */
  onToolAdded?: (toolIds: string[]) => void;

  /**
   * Danh sách tool IDs đã chọn ban đầu (chỉ dùng cho mode create)
   */
  initialSelectedToolIds?: string[];
}

/**
 * Component form trượt để chọn các tools
 */
const ToolSlideInForm: React.FC<ToolSlideInFormProps> = ({
  isVisible,
  onClose,
  mode,
  onToolAdded,
  initialSelectedToolIds = [],
}) => {
  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');

  // API hooks
  const { data: toolsResponse, isLoading } = useToolsIntegration();

  // Khởi tạo selectedIds từ initialSelectedToolIds
  useEffect(() => {
    if (mode === 'create' && initialSelectedToolIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(initialSelectedToolIds);
    }
  }, [initialSelectedToolIds, mode, selectedIds.length]);

  // Debug API response
  console.log('🔍 ToolSlideInForm API Debug:', {
    toolsResponse,
    isLoading,
    timestamp: new Date().toISOString()
  });

  // Lấy dữ liệu từ API response
  const tools: Tool[] = toolsResponse?.result?.items || [];

  // Filter tools based on search term
  const filteredTools = tools.filter(tool =>
    tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tool.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Cấu hình cột cho bảng
  const columns: TableColumn<Tool>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'name',
      title: 'Tên Tool',
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div>
          <Typography variant="subtitle1">{record.name}</Typography>
          <Typography variant="caption" className="text-gray-500">
            {record.description}
          </Typography>
        </div>
      ),
    },
    {
      key: 'id',
      title: 'ID',
      dataIndex: 'id',
      width: '30%',
      render: (_, record) => (
        <Typography variant="body2" className="font-mono text-sm">
          {record.id}
        </Typography>
      ),
    },
    {
      key: 'createdAt',
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      width: '30%',
      render: (_, record) => (
        <Typography variant="body2" className="text-sm">
          {new Date(record.createdAt).toLocaleDateString('vi-VN')}
        </Typography>
      ),
    },
  ];

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Create mode: trả về danh sách tool IDs đã chọn
      if (onToolAdded) {
        onToolAdded(selectedIds);
      }

      NotificationUtil.success({
        message: 'Đã thêm tools vào danh sách!',
      });

      onClose();
    } catch {
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi thêm tools.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    setSearchTerm('');
    onClose();
  }, [onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'filter-active',
      label: 'Lọc theo trạng thái',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'active-only',
      label: 'Chỉ hoạt động',
      onClick: () => { },
    },
    {
      id: 'all',
      label: 'Tất cả',
      onClick: () => { },
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn Custom Tools</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <Card className="overflow-hidden mb-4">
          <Table<Tool>
            columns={columns}
            data={filteredTools}
            rowKey="id"
            loading={isLoading}
            sortable={false}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: 1,
              pageSize: 10,
              total: filteredTools.length,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
              onChange: () => {}, // Dummy handler
            }}
          />
        </Card>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || isSubmitting || selectedIds.length === 0}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default ToolSlideInForm;
