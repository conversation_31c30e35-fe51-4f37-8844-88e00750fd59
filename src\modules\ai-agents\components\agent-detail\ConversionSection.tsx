import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Button,
  Icon,
  Card,
  FormItem,
  Input,
  Select,
  Checkbox,
  Typography,
  EmptyState,
} from '@/shared/components/common';
import { ConversionField } from '../../types/agent.types';
import { useUpdateAgent } from '../../hooks';

/**
 * Props cho component ConversionSection
 */
interface ConversionSectionProps {
  /**
   * ID của Agent
   */
  agentId: string;

  /**
   * <PERSON>h sách các trường chuyển đổi hiện tại
   */
  conversionFields: ConversionField[];

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component hiển thị cấu hình chuyển đổi của Agent
 */
const ConversionSection: React.FC<ConversionSectionProps> = ({
  agentId,
  conversionFields,
  onToggle,
}) => {
  const { t } = useTranslation();
  const [fields, setFields] = useState<ConversionField[]>(conversionFields);
  const [originalFields, setOriginalFields] = useState<ConversionField[]>(conversionFields);
  const [newFieldType, setNewFieldType] = useState<string>('email');
  const [newFieldLabel, setNewFieldLabel] = useState<string>('');
  const [showAddField, setShowAddField] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // Sử dụng hooks
  const { mutate: updateAgent, isPending: isUpdating } = useUpdateAgent();

  // Cập nhật originalFields khi conversionFields thay đổi từ props
  useEffect(() => {
    setFields(conversionFields);
    setOriginalFields(conversionFields);
    setHasChanges(false);
  }, [conversionFields]);

  // Xử lý thêm trường mới
  const handleAddField = () => {
    if (newFieldType === 'custom' && !newFieldLabel.trim()) {
      return; // Không cho phép thêm trường tùy chỉnh không có nhãn
    }

    const newField: ConversionField = {
      id: `field-${Date.now()}`,
      name: newFieldType === 'custom' ? newFieldLabel : newFieldType,
      type: newFieldType as 'email' | 'phone' | 'address' | 'name' | 'custom',
      isRequired: false,
      customLabel: newFieldType === 'custom' ? newFieldLabel : undefined,
    };

    const updatedFields = [...fields, newField];
    setFields(updatedFields);
    setNewFieldType('email');
    setNewFieldLabel('');
    setShowAddField(false);

    // Kiểm tra thay đổi
    checkForChanges(updatedFields);
  };

  /**
   * Kiểm tra xem có thay đổi so với dữ liệu ban đầu không
   */
  const checkForChanges = (currentFields: ConversionField[] = fields) => {
    // Nếu số lượng trường khác nhau
    if (currentFields.length !== originalFields.length) {
      setHasChanges(true);
      return;
    }

    // Kiểm tra từng trường
    const hasFieldChanges = currentFields.some(field => {
      // Tìm trường tương ứng trong originalFields
      const originalField = originalFields.find(f => f.id === field.id);

      // Nếu không tìm thấy, đây là trường mới
      if (!originalField) return true;

      // So sánh các thuộc tính
      return (
        field.name !== originalField.name ||
        field.type !== originalField.type ||
        field.isRequired !== originalField.isRequired ||
        field.customLabel !== originalField.customLabel
      );
    });

    setHasChanges(hasFieldChanges);
  };

  // Xử lý xóa trường
  const handleRemoveField = (id: string) => {
    const updatedFields = fields.filter(field => field.id !== id);
    setFields(updatedFields);
    checkForChanges(updatedFields);
  };

  // Xử lý thay đổi trạng thái bắt buộc
  const handleToggleRequired = (id: string) => {
    const updatedFields = fields.map(field =>
      field.id === id ? { ...field, isRequired: !field.isRequired } : field
    );
    setFields(updatedFields);
    checkForChanges(updatedFields);
  };

  /**
   * Xử lý lưu thay đổi
   */
  const handleSave = () => {
    if (!hasChanges) return;

    updateAgent(
      {
        id: agentId,
        data: {
          conversionFields: fields,
        },
      },
      {
        onSuccess: () => {
          // Cập nhật original fields
          setOriginalFields([...fields]);
          setHasChanges(false);

          // Hiển thị thông báo thành công
          alert(t('aiAgents.conversion.saveSuccess', 'Lưu cấu hình chuyển đổi thành công!'));
        },
        onError: (error) => {
          // Hiển thị thông báo lỗi
          console.error('Update conversion fields error:', error);
          alert(t('aiAgents.conversion.saveError', 'Có lỗi xảy ra khi lưu cấu hình chuyển đổi. Vui lòng thử lại.'));
        }
      }
    );
  };

  // Xử lý khi đóng/mở card
  const handleCardToggle = (isOpen: boolean) => {
    if (onToggle) {
      onToggle(isOpen);
    }
  };

  // Danh sách loại trường
  const fieldTypeOptions = [
    { value: 'email', label: t('aiAgents.conversion.fieldTypes.email', 'Email') },
    { value: 'phone', label: t('aiAgents.conversion.fieldTypes.phone', 'Số điện thoại') },
    { value: 'name', label: t('aiAgents.conversion.fieldTypes.name', 'Họ tên') },
    { value: 'address', label: t('aiAgents.conversion.fieldTypes.address', 'Địa chỉ') },
    { value: 'custom', label: t('aiAgents.conversion.fieldTypes.custom', 'Tùy chỉnh') },
  ];

  // Lấy tên hiển thị cho loại trường
  const getFieldTypeLabel = (type: string) => {
    const option = fieldTypeOptions.find(opt => opt.value === type);
    return option ? option.label : type;
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center">
          <Icon name="exchange" className="mr-2 text-green-500" />
          <span>{t('aiAgents.conversion.title', 'Chuyển đổi')}</span>
        </div>
      }
      className="mb-6"
      onToggle={handleCardToggle}
    >
      <div className="mb-4">
        <Typography variant="body1">
          {t(
            'aiAgents.conversion.description',
            'Cấu hình các trường thông tin cần thu thập từ khách hàng để tạo chuyển đổi.'
          )}
        </Typography>
      </div>

      {/* Danh sách trường chuyển đổi */}
      {fields.length === 0 ? (
        <EmptyState
          icon="exchange"
          title={t('aiAgents.conversion.noFields', 'Chưa có trường chuyển đổi nào')}
          description={t(
            'aiAgents.conversion.addFieldDescription',
            'Thêm các trường thông tin để Agent có thể thu thập từ khách hàng.'
          )}
          actions={
            <Button
              variant="primary"
              leftIcon={<Icon name="plus" size="sm" />}
              onClick={() => setShowAddField(true)}
            >
              {t('aiAgents.conversion.addField', 'Thêm trường')}
            </Button>
          }
        />
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <Typography variant="subtitle1">
              {t('aiAgents.conversion.fields', 'Các trường chuyển đổi')}
            </Typography>
            <Button
              variant="outline"
              size="sm"
              leftIcon={<Icon name="plus" size="sm" />}
              onClick={() => setShowAddField(true)}
            >
              {t('aiAgents.conversion.addField', 'Thêm trường')}
            </Button>
          </div>

          <div className="space-y-4 mb-6">
            {fields.map(field => (
              <Card key={field.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="mr-4">
                      <Checkbox
                        checked={field.isRequired}
                        onChange={() => handleToggleRequired(field.id)}
                        label={t('aiAgents.conversion.required', 'Bắt buộc')}
                      />
                    </div>
                    <div>
                      <Typography variant="subtitle1">
                        {field.customLabel || getFieldTypeLabel(field.type)}
                      </Typography>
                      <Typography variant="caption" className="text-gray-500">
                        {field.type === 'custom'
                          ? t('aiAgents.conversion.customField', 'Trường tùy chỉnh')
                          : getFieldTypeLabel(field.type)}
                      </Typography>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveField(field.id)}
                  >
                    <Icon name="trash" className="text-red-500" />
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          <div className="flex justify-end">
            <Button
              variant="primary"
              onClick={handleSave}
              isLoading={isUpdating}
              disabled={!hasChanges}
              leftIcon={<Icon name="save" size="sm" />}
            >
              {t('common.save', 'Lưu')}
            </Button>
          </div>
        </>
      )}

      {/* Form thêm trường mới */}
      {showAddField && (
        <Card className="p-4 mt-4">
          <Typography variant="subtitle1" className="mb-4">
            {t('aiAgents.conversion.addNewField', 'Thêm trường mới')}
          </Typography>

          <div className="space-y-4">
            <FormItem label={t('aiAgents.conversion.fieldType', 'Loại trường')}>
              <Select
                value={newFieldType}
                onChange={(value) => setNewFieldType(value as string)}
                options={fieldTypeOptions}
                fullWidth
              />
            </FormItem>

            {newFieldType === 'custom' && (
              <FormItem label={t('aiAgents.conversion.fieldLabel', 'Nhãn trường')}>
                <Input
                  value={newFieldLabel}
                  onChange={e => setNewFieldLabel(e.target.value)}
                  placeholder={t(
                    'aiAgents.conversion.fieldLabelPlaceholder',
                    'Nhập nhãn cho trường tùy chỉnh'
                  )}
                  fullWidth
                />
              </FormItem>
            )}

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowAddField(false)}
              >
                {t('common.cancel', 'Hủy')}
              </Button>
              <Button
                variant="primary"
                onClick={handleAddField}
                disabled={newFieldType === 'custom' && !newFieldLabel.trim()}
              >
                {t('common.add', 'Thêm')}
              </Button>
            </div>
          </div>
        </Card>
      )}
    </CollapsibleCard>
  );
};

export default ConversionSection;
