import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Button,
  Icon,
  Card,
  EmptyState,
  Typography,
  Tooltip,
} from '@/shared/components/common';
import { FacebookPage } from '../../types/agent.types';
import IntegrationSlideInForm from './IntegrationSlideInForm';
import { useFacebookPages, useConnectFacebookPages, useDisconnectFacebookPage } from '../../hooks';

interface FacebookIntegrationSectionProps {
  /**
   * ID của Agent
   */
  agentId: string;

  /**
   * Danh sách Facebook Pages đã kết nối
   */
  facebookPages: FacebookPage[];

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component hiển thị và quản lý tích hợp Facebook Pages cho Agent
 */
const FacebookIntegrationSection: React.FC<FacebookIntegrationSectionProps> = ({
  agentId,
  facebookPages,
  onToggle,
}) => {
  const { t } = useTranslation();
  const [isFormVisible, setIsFormVisible] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');

  // Sử dụng hooks
  const { data: availablePages, isLoading } = useFacebookPages(search);
  const { mutate: connectPages, isPending: isConnecting } = useConnectFacebookPages(agentId);
  const { mutate: disconnectPage, isPending: isDisconnecting } = useDisconnectFacebookPage(agentId);

  // Xử lý mở form
  const handleOpenForm = () => {
    setIsFormVisible(true);
  };

  // Xử lý đóng form
  const handleCloseForm = () => {
    setIsFormVisible(false);
    setSearch('');
  };

  // Xử lý kết nối pages
  const handleConnectPages = (selectedIds: string[]) => {
    if (!selectedIds.length) return;

    connectPages(selectedIds, {
      onSuccess: () => {
        handleCloseForm();
        // Hiển thị thông báo thành công
        alert(t('aiAgents.facebook.connectSuccess', 'Kết nối Facebook Pages thành công!'));
      },
      onError: (error) => {
        // Hiển thị thông báo lỗi
        console.error('Connect Facebook Pages error:', error);
        alert(t('aiAgents.facebook.connectError', 'Có lỗi xảy ra khi kết nối Facebook Pages. Vui lòng thử lại.'));
      }
    });
  };

  // Xử lý ngắt kết nối page
  const handleDisconnectPage = (pageId: string) => {
    // Hiển thị xác nhận trước khi ngắt kết nối
    if (window.confirm(t('aiAgents.facebook.confirmDisconnect', 'Bạn có chắc chắn muốn ngắt kết nối Facebook Page này?'))) {
      disconnectPage(pageId, {
        onSuccess: () => {
          // Hiển thị thông báo thành công
          alert(t('aiAgents.facebook.disconnectSuccess', 'Ngắt kết nối Facebook Page thành công!'));
        },
        onError: (error) => {
          // Hiển thị thông báo lỗi
          console.error('Disconnect Facebook Page error:', error);
          alert(t('aiAgents.facebook.disconnectError', 'Có lỗi xảy ra khi ngắt kết nối Facebook Page. Vui lòng thử lại.'));
        }
      });
    }
  };

  // Xử lý khi đóng/mở card
  const handleCardToggle = (isOpen: boolean) => {
    if (onToggle) {
      onToggle(isOpen);
    }
  };

  return (
    <>
      <CollapsibleCard
        title={
          <div className="flex items-center">
            <Icon name="facebook" className="mr-2 text-blue-600" />
            <span>{t('aiAgents.facebook.title', 'Tích hợp Facebook')}</span>
          </div>
        }
        className="mb-6"
        onToggle={handleCardToggle}
      >
        <div className="mb-4">
          <Typography variant="body1">
            {t(
              'aiAgents.facebook.description',
              'Kết nối Agent với các Facebook Pages để tương tác với khách hàng trên Facebook.'
            )}
          </Typography>
        </div>

        {/* Danh sách Facebook Pages đã kết nối */}
        {facebookPages.length === 0 ? (
          <EmptyState
            icon="facebook"
            title={t('aiAgents.facebook.noPages', 'Chưa có Facebook Page nào được kết nối')}
            description={t(
              'aiAgents.facebook.addPageDescription',
              'Kết nối Facebook Pages để Agent có thể tương tác với khách hàng trên Facebook.'
            )}
            actions={
              <Button
                variant="primary"
                leftIcon={<Icon name="plus" size="sm" />}
                onClick={handleOpenForm}
              >
                {t('aiAgents.facebook.addPage', 'Thêm Facebook Page')}
              </Button>
            }
          />
        ) : (
          <>
            <div className="flex justify-between items-center mb-4">
              <Typography variant="subtitle1">
                {t('aiAgents.facebook.connectedPages', 'Facebook Pages đã kết nối')}
              </Typography>
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Icon name="plus" size="sm" />}
                onClick={handleOpenForm}
              >
                {t('aiAgents.facebook.addPage', 'Thêm Facebook Page')}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {facebookPages.map(page => (
                <Card key={page.id} className="p-4">
                  <div className="flex items-center">
                    {page.imageUrl && (
                      <img
                        src={page.imageUrl}
                        alt={page.name}
                        className="w-12 h-12 rounded-full mr-3 object-cover"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <Typography variant="subtitle1" className="truncate">
                        {page.name}
                      </Typography>
                      <div className="flex items-center text-green-500 text-sm">
                        <Icon name="check-circle" size="sm" className="mr-1" />
                        {t('common.connected', 'Đã kết nối')}
                      </div>
                    </div>
                    <Tooltip content={t('common.disconnect', 'Ngắt kết nối')} position="top">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDisconnectPage(page.id)}
                        isLoading={isDisconnecting}
                      >
                        <Icon name="x-circle" className="text-red-500" />
                      </Button>
                    </Tooltip>
                  </div>
                </Card>
              ))}
            </div>
          </>
        )}
      </CollapsibleCard>

      {/* Form trượt để chọn Facebook Pages */}
      <IntegrationSlideInForm
        isVisible={isFormVisible}
        onClose={handleCloseForm}
        title={t('aiAgents.facebook.selectPages', 'Chọn Facebook Pages')}
        items={availablePages || []}
        selectedItems={facebookPages.map(page => page.id)}
        onSelect={handleConnectPages}
        isLoading={isLoading}
        isSubmitting={isConnecting}
        onSearch={setSearch}
        searchPlaceholder={t('aiAgents.facebook.searchPlaceholder', 'Tìm kiếm Facebook Pages...')}
        emptyStateMessage={t(
          'aiAgents.facebook.noResults',
          'Không tìm thấy Facebook Pages. Vui lòng thử lại với từ khóa khác.'
        )}
      />
    </>
  );
};

export default FacebookIntegrationSection;
