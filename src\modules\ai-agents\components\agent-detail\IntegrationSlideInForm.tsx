import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Input,
  Button,
  Checkbox,
  Icon,
  Loading,
  Typography,
  EmptyState,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';

/**
 * Interface cho item tích hợp
 */
interface IntegrationItem {
  id: string;
  name: string;
  imageUrl?: string;
  url?: string;
  isConnected?: boolean;
}

/**
 * Props cho component IntegrationSlideInForm
 */
interface IntegrationSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Tiêu đề của form
   */
  title: string;

  /**
   * Danh sách các items
   */
  items: IntegrationItem[];

  /**
   * Danh sách ID của các items đã chọn
   */
  selectedItems: string[];

  /**
   * Callback khi chọn items
   */
  onSelect: (selectedIds: string[]) => void;

  /**
   * Trạng thái đang tải danh sách
   */
  isLoading: boolean;

  /**
   * Trạng thái đang xử lý lưu
   */
  isSubmitting: boolean;

  /**
   * Callback khi tìm kiếm
   */
  onSearch: (search: string) => void;

  /**
   * Placeholder cho ô tìm kiếm
   */
  searchPlaceholder: string;

  /**
   * Thông báo khi không có kết quả
   */
  emptyStateMessage: string;
}

/**
 * Component form trượt để chọn các tích hợp
 */
const IntegrationSlideInForm: React.FC<IntegrationSlideInFormProps> = ({
  isVisible,
  onClose,
  title,
  items,
  selectedItems,
  onSelect,
  isLoading,
  isSubmitting,
  onSearch,
  searchPlaceholder,
  emptyStateMessage,
}) => {
  const { t } = useTranslation();
  const [search, setSearch] = useState<string>('');
  const [localSelectedItems, setLocalSelectedItems] = useState<string[]>(selectedItems);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // Reset local state when selectedItems prop changes
  useEffect(() => {
    setLocalSelectedItems(selectedItems);
    setHasChanges(false);
  }, [selectedItems]);

  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    // So sánh mảng selectedItems và localSelectedItems
    const hasUnsavedChanges =
      localSelectedItems.length !== selectedItems.length ||
      localSelectedItems.some(id => !selectedItems.includes(id)) ||
      selectedItems.some(id => !localSelectedItems.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [localSelectedItems, selectedItems]);

  // Xử lý tìm kiếm
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearch(value);
    onSearch(value);
  };

  // Xử lý chọn/bỏ chọn item
  const handleToggleItem = (id: string) => {
    setLocalSelectedItems(prev => {
      if (prev.includes(id)) {
        return prev.filter(itemId => itemId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // Xử lý chọn tất cả
  const handleSelectAll = () => {
    if (localSelectedItems.length === items.length) {
      setLocalSelectedItems([]);
    } else {
      setLocalSelectedItems(items.map(item => item.id));
    }
  };

  // Xử lý lưu
  const handleSave = () => {
    onSelect(localSelectedItems);
  };

  /**
   * Xử lý đóng form với xác nhận nếu có thay đổi chưa lưu
   */
  const handleClose = useCallback(() => {
    // Nếu có thay đổi chưa lưu, hiển thị xác nhận
    if (hasChanges) {
      const confirmed = window.confirm(
        t('common.unsavedChanges', 'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?')
      );
      if (!confirmed) return;
    }

    setSearch('');
    onClose();
  }, [hasChanges, onClose, t]);

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-lg">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{title}</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            {t('common.close', 'Đóng')}
          </Button>
        </div>

        {/* Thanh tìm kiếm */}
        <div className="mb-4">
          <Input
            value={search}
            onChange={handleSearch}
            placeholder={searchPlaceholder}
            leftIcon={<Icon name="search" size="sm" />}
            fullWidth
          />
        </div>

        {/* Chọn tất cả */}
        <div className="mb-2">
          <Checkbox
            label={t('common.selectAll', 'Chọn tất cả')}
            checked={items.length > 0 && localSelectedItems.length === items.length}
            onChange={handleSelectAll}
            indeterminate={
              localSelectedItems.length > 0 && localSelectedItems.length < items.length
            }
          />
        </div>

        {/* Danh sách items */}
        <div className="max-h-96 overflow-y-auto mb-4 border border-gray-200 dark:border-gray-700 rounded-md">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loading size="md" />
            </div>
          ) : items.length === 0 ? (
            <EmptyState
              icon="search"
              title={t('common.noResults', 'Không có kết quả')}
              description={emptyStateMessage}
              className="py-8"
            />
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {items.map(item => (
                <div
                  key={item.id}
                  className="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                  onClick={() => handleToggleItem(item.id)}
                >
                  <Checkbox
                    checked={localSelectedItems.includes(item.id)}
                    onChange={() => handleToggleItem(item.id)}
                    className="mr-3"
                  />
                  {item.imageUrl && (
                    <img
                      src={item.imageUrl}
                      alt={item.name}
                      className="w-10 h-10 rounded-full mr-3 object-cover"
                    />
                  )}
                  <div className="flex-1">
                    <Typography variant="subtitle1">{item.name}</Typography>
                    {item.url && (
                      <Typography variant="caption" className="text-gray-500">
                        {item.url}
                      </Typography>
                    )}
                  </div>
                  {item.isConnected && (
                    <span className="text-green-500 text-sm flex items-center">
                      <Icon name="check-circle" size="sm" className="mr-1" />
                      {t('common.connected', 'Đã kết nối')}
                    </span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            {t('common.save', 'Lưu')}
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default IntegrationSlideInForm;
