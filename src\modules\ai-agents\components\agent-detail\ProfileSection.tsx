import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  FormItem,
  Input,
  Select,
  DatePicker,
  Avatar,
  Button,
  Icon,
  Grid,
} from '@/shared/components/common';
import { TypeaheadSelect } from '@/shared/components/common/Select';
import { AgentProfile, Gender, EducationLevel } from '../../types/agent.types';

interface ProfileSectionProps {
  profile: AgentProfile;
  onUpdate: (profile: Partial<AgentProfile>) => void;
  isLoading?: boolean;
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component hiển thị thông tin profile của Agent
 */
const ProfileSection: React.FC<ProfileSectionProps> = ({ profile, onUpdate, isLoading = false, onToggle }) => {
  const { t } = useTranslation();
  const [localProfile, setLocalProfile] = useState<AgentProfile>(profile);

  // Xử lý thay đổi giá trị
  const handleChange = (field: keyof AgentProfile, value: string | string[] | Gender | EducationLevel | undefined) => {
    setLocalProfile(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Xử lý lưu thay đổi
  const handleSave = () => {
    onUpdate(localProfile);
  };

  // Options cho select
  const genderOptions = [
    { value: Gender.MALE, label: t('common.gender.male', 'Nam') },
    { value: Gender.FEMALE, label: t('common.gender.female', 'Nữ') },
    { value: Gender.OTHER, label: t('common.gender.other', 'Khác') },
  ];

  const educationOptions = [
    { value: EducationLevel.HIGH_SCHOOL, label: t('common.education.highSchool', 'Trung học') },
    { value: EducationLevel.COLLEGE, label: t('common.education.college', 'Cao đẳng') },
    { value: EducationLevel.BACHELOR, label: t('common.education.bachelor', 'Đại học') },
    { value: EducationLevel.MASTER, label: t('common.education.master', 'Thạc sĩ') },
    { value: EducationLevel.PHD, label: t('common.education.phd', 'Tiến sĩ') },
  ];

  const languageOptions = [
    { value: 'vi', label: t('common.language.vietnamese', 'Tiếng Việt') },
    { value: 'en', label: t('common.language.english', 'Tiếng Anh') },
    { value: 'zh', label: t('common.language.chinese', 'Tiếng Trung') },
  ];

  const countryOptions = [
    { value: 'Vietnam', label: t('common.country.vietnam', 'Việt Nam') },
    { value: 'USA', label: t('common.country.usa', 'Hoa Kỳ') },
    { value: 'China', label: t('common.country.china', 'Trung Quốc') },
    { value: 'Japan', label: t('common.country.japan', 'Nhật Bản') },
    { value: 'Korea', label: t('common.country.korea', 'Hàn Quốc') },
  ];

  // Danh sách kỹ năng gợi ý
  const skillOptions = [
    'Sales',
    'Marketing',
    'Customer Support',
    'Technical Support',
    'Product Knowledge',
    'Communication',
    'Problem Solving',
    'Negotiation',
    'Leadership',
    'Teamwork',
  ];

  // Danh sách tính cách gợi ý
  const personalityOptions = [
    'Friendly',
    'Professional',
    'Helpful',
    'Patient',
    'Empathetic',
    'Assertive',
    'Analytical',
    'Creative',
    'Detail-oriented',
    'Adaptable',
  ];

  // Xử lý khi đóng/mở card
  const handleCardToggle = (isOpen: boolean) => {
    if (onToggle) {
      onToggle(isOpen);
    }
  };

  return (
    <CollapsibleCard
      onToggle={handleCardToggle}
      title={
        <div className="flex items-center">
          <Icon name="user" className="mr-2" />
          <span>{t('aiAgents.profile.title', 'Thông tin profile Agent')}</span>
        </div>
      }
      className="mb-6"
    >
      <div className="flex flex-col md:flex-row gap-6">
        {/* Avatar */}
        <div className="flex flex-col items-center">
          <Avatar
            src={profile.avatar || ''}
            alt={profile.name}
            size="xl"
            className="mb-4 w-32 h-32"
          />
          <Button
            variant="outline"
            size="sm"
            leftIcon={<Icon name="upload" size="sm" />}
            className="mb-2"
          >
            {t('common.upload', 'Tải lên')}
          </Button>
        </div>

        {/* Thông tin cơ bản */}
        <div className="flex-1">
          <Grid columns={{ xs: 1, sm: 2 }} columnGap="md" rowGap="md">
            {/* Tên */}
            <FormItem label={t('aiAgents.profile.name', 'Tên')}>
              <Input
                value={localProfile.name}
                onChange={e => handleChange('name', e.target.value)}
                placeholder={t('aiAgents.profile.namePlaceholder', 'Nhập tên Agent')}
                fullWidth
              />
            </FormItem>

            {/* Ngày sinh */}
            <FormItem label={t('aiAgents.profile.birthDate', 'Ngày sinh')}>
              <DatePicker
                value={localProfile.birthDate ? new Date(localProfile.birthDate) : null}
                onChange={date => handleChange('birthDate', date ? date.toISOString() : undefined)}
                placeholder={t('aiAgents.profile.birthDatePlaceholder', 'Chọn ngày sinh')}
                fullWidth
              />
            </FormItem>

            {/* Giới tính */}
            <FormItem label={t('aiAgents.profile.gender', 'Giới tính')}>
              <Select
                value={localProfile.gender}
                onChange={value => handleChange('gender', value as string)}
                options={genderOptions}
                placeholder={t('aiAgents.profile.genderPlaceholder', 'Chọn giới tính')}
                fullWidth
              />
            </FormItem>

            {/* Ngôn ngữ */}
            <FormItem label={t('aiAgents.profile.language', 'Ngôn ngữ')}>
              <Select
                value={localProfile.language}
                onChange={value => handleChange('language', value as string)}
                options={languageOptions}
                placeholder={t('aiAgents.profile.languagePlaceholder', 'Chọn ngôn ngữ')}
                fullWidth
              />
            </FormItem>

            {/* Trình độ học vấn */}
            <FormItem label={t('aiAgents.profile.educationLevel', 'Trình độ học vấn')}>
              <Select
                value={localProfile.educationLevel}
                onChange={value => handleChange('educationLevel', value as string)}
                options={educationOptions}
                placeholder={t('aiAgents.profile.educationLevelPlaceholder', 'Chọn trình độ')}
                fullWidth
              />
            </FormItem>

            {/* Quốc gia */}
            <FormItem label={t('aiAgents.profile.country', 'Quốc gia')}>
              <Select
                value={localProfile.country}
                onChange={value => handleChange('country', value as string)}
                options={countryOptions}
                placeholder={t('aiAgents.profile.countryPlaceholder', 'Chọn quốc gia')}
                fullWidth
              />
            </FormItem>

            {/* Chức vụ */}
            <FormItem label={t('aiAgents.profile.position', 'Chức vụ')}>
              <Input
                value={localProfile.position}
                onChange={e => handleChange('position', e.target.value)}
                placeholder={t('aiAgents.profile.positionPlaceholder', 'Nhập chức vụ')}
                fullWidth
              />
            </FormItem>
          </Grid>

          {/* Kỹ năng */}
          <FormItem label={t('aiAgents.profile.skills', 'Kỹ năng')} className="mt-4">
            <TypeaheadSelect
              value={localProfile.skills}
              onChange={value => handleChange('skills', value as string[])}
              options={skillOptions.map(skill => ({ value: skill, label: skill }))}
              placeholder={t('aiAgents.profile.skillsPlaceholder', 'Nhập kỹ năng và ấn Enter')}
              multiple
              highlightMatch
              fullWidth
            />
          </FormItem>

          {/* Tính cách */}
          <FormItem label={t('aiAgents.profile.personality', 'Tính cách')} className="mt-4">
            <TypeaheadSelect
              value={localProfile.personality}
              onChange={value => handleChange('personality', value as string[])}
              options={personalityOptions.map(trait => ({ value: trait, label: trait }))}
              placeholder={t('aiAgents.profile.personalityPlaceholder', 'Nhập tính cách và ấn Enter')}
              multiple
              highlightMatch
              fullWidth
            />
          </FormItem>

          {/* Nút lưu */}
          <div className="flex justify-end mt-4">
            <Button
              variant="primary"
              onClick={handleSave}
              isLoading={isLoading}
              leftIcon={<Icon name="save" size="sm" />}
            >
              {t('common.save', 'Lưu')}
            </Button>
          </div>
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default ProfileSection;
