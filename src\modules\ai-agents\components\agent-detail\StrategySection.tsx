import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Button,
  Icon,
  Card,
  Select,
  FormItem,
  Input,
  Typography,
  EmptyState,
} from '@/shared/components/common';
import { Strategy, StrategyParameter } from '../../types/agent.types';
import { useUpdateAgentStrategy } from '../../hooks';
import { useStrategies } from '../../hooks/useAgentStrategies';

/**
 * Kiểu dữ liệu cho giá trị tham số chiến lược
 */
type StrategyParameterValue = string | number | boolean;

/**
 * Props cho component StrategySection
 */
interface StrategySectionProps {
  /**
   * ID của Agent
   */
  agentId: string;

  /**
   * Chiến lược hiện tại của Agent
   */
  strategy?: Strategy;

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component hiển thị chiến lược của Agent
 */
const StrategySection: React.FC<StrategySectionProps> = ({ agentId, strategy, onToggle }) => {
  const { t } = useTranslation();
  const [selectedStrategyId, setSelectedStrategyId] = useState<string>(strategy?.id || '');
  const [parameters, setParameters] = useState<Record<string, StrategyParameterValue>>({});
  const [originalParameters, setOriginalParameters] = useState<Record<string, StrategyParameterValue>>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // Sử dụng hooks
  const { data: strategies, isLoading } = useStrategies();
  const { mutate: updateStrategy, isPending: isUpdating } = useUpdateAgentStrategy();

  // Cập nhật parameters khi strategy thay đổi
  useEffect(() => {
    if (strategy) {
      const paramValues: Record<string, StrategyParameterValue> = {};
      strategy.parameters.forEach(param => {
        paramValues[param.name] = param.value;
      });
      setParameters(paramValues);
      setOriginalParameters(paramValues);
      setSelectedStrategyId(strategy.id);
      setHasChanges(false);
    }
  }, [strategy]);

  // Kiểm tra thay đổi
  useEffect(() => {
    if (!selectedStrategyId) {
      setHasChanges(false);
      return;
    }

    // So sánh parameters hiện tại với original
    const hasParameterChanges = Object.keys(parameters).some(key => {
      return parameters[key] !== originalParameters[key];
    });

    // Kiểm tra nếu đã đổi chiến lược
    const hasStrategyChanged = strategy?.id !== selectedStrategyId;

    setHasChanges(hasParameterChanges || hasStrategyChanged);
  }, [parameters, originalParameters, selectedStrategyId, strategy]);

  // Xử lý thay đổi chiến lược
  const handleStrategyChange = (value: string) => {
    setSelectedStrategyId(value);

    // Reset parameters khi đổi chiến lược
    const selectedStrategy = strategies?.find(s => s.id === value);
    if (selectedStrategy) {
      const newParams: Record<string, StrategyParameterValue> = {};
      selectedStrategy.parameters.forEach(param => {
        newParams[param.name] = param.value;
      });
      setParameters(newParams);
    }
  };

  // Xử lý thay đổi tham số
  const handleParameterChange = (name: string, value: StrategyParameterValue) => {
    setParameters(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  /**
   * Xử lý lưu chiến lược
   */
  const handleSaveStrategy = () => {
    if (!selectedStrategyId || !hasChanges) return;

    updateStrategy(
      {
        agentId,
        strategyId: selectedStrategyId,
        data: {
          isActive: true,
          customPrompt: JSON.stringify(parameters) // Convert parameters to string for now
        },
      },
      {
        onSuccess: () => {
          // Cập nhật original parameters
          setOriginalParameters({...parameters});
          setHasChanges(false);

          // Hiển thị thông báo thành công
          alert(t('aiAgents.strategy.saveSuccess', 'Lưu chiến lược thành công!'));
        },
        onError: (error) => {
          // Hiển thị thông báo lỗi
          console.error('Update strategy error:', error);
          alert(t('aiAgents.strategy.saveError', 'Có lỗi xảy ra khi lưu chiến lược. Vui lòng thử lại.'));
        }
      }
    );
  };

  // Xử lý khi đóng/mở card
  const handleCardToggle = (isOpen: boolean) => {
    if (onToggle) {
      onToggle(isOpen);
    }
  };

  // Lấy chiến lược đã chọn
  const selectedStrategy = strategies?.find(s => s.id === selectedStrategyId);

  // Render tham số dựa trên loại
  const renderParameter = (param: StrategyParameter) => {
    switch (param.type) {
      case 'number':
        if (param.min !== undefined && param.max !== undefined) {
          return (
            <div className="mt-2">
              <input
                type="range"
                value={parameters[param.name] !== undefined ? String(parameters[param.name]) : String(param.value)}
                onChange={(e) => handleParameterChange(param.name, Number(e.target.value))}
                min={param.min}
                max={param.max}
                step={param.step || 1}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
          );
        } else {
          return (
            <Input
              type="number"
              value={typeof parameters[param.name] === 'number' ? parameters[param.name] as number : 
                     typeof param.value === 'number' ? param.value : 0}
              onChange={e => handleParameterChange(param.name, Number(e.target.value))}
              min={param.min}
              max={param.max}
              step={param.step || 1}
              fullWidth
            />
          );
        }
      case 'select':
        return (
          <Select
            value={String(parameters[param.name] || param.value)}
            onChange={value => {
              if (typeof value === 'string' || typeof value === 'number') {
                handleParameterChange(param.name, value);
              }
            }}
            options={param.options?.map(opt => ({ 
              value: typeof opt.value === 'boolean' ? String(opt.value) : opt.value, 
              label: opt.label 
            })) || []}
            fullWidth
          />
        );
      case 'boolean':
        return (
          <Select
            value={parameters[param.name] === true ? 'true' : 'false'}
            onChange={value => {
              if (typeof value === 'string') {
                handleParameterChange(param.name, value === 'true');
              }
            }}
            options={[
              { value: 'true', label: t('common.yes', 'Có') },
              { value: 'false', label: t('common.no', 'Không') },
            ]}
            fullWidth
          />
        );
      default:
        return (
          <Input
            value={typeof parameters[param.name] === 'string' ? parameters[param.name] as string : 
                   typeof param.value === 'string' ? param.value : ''}
            onChange={e => handleParameterChange(param.name, e.target.value)}
            fullWidth
          />
        );
    }
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center">
          <Icon name="target" className="mr-2 text-purple-500" />
          <span>{t('aiAgents.strategy.title', 'Chiến lược')}</span>
        </div>
      }
      className="mb-6"
      onToggle={handleCardToggle}
    >
      <div className="mb-4">
        <Typography variant="body1">
          {t(
            'aiAgents.strategy.description',
            'Chọn và tùy chỉnh chiến lược cho Agent để tối ưu hóa tương tác với khách hàng.'
          )}
        </Typography>
      </div>

      {isLoading ? (
        <Card className="p-6 flex justify-center">
          <div className="animate-pulse flex space-x-4">
            <div className="flex-1 space-y-4 py-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </Card>
      ) : !strategies || strategies.length === 0 ? (
        <EmptyState
          icon="target"
          title={t('aiAgents.strategy.noStrategies', 'Không có chiến lược nào')}
          description={t(
            'aiAgents.strategy.noStrategiesDescription',
            'Hiện tại không có chiến lược nào khả dụng.'
          )}
        />
      ) : (
        <Card className="p-6">
          {/* Chọn chiến lược */}
          <FormItem label={t('aiAgents.strategy.selectStrategy', 'Chọn chiến lược')}>
            <Select
              value={selectedStrategyId}
              onChange={(value: string | number | string[] | number[]) => {
                if (typeof value === 'string') {
                  handleStrategyChange(value);
                }
              }}
              options={strategies.map(s => ({ value: s.id, label: s.name }))}
              placeholder={t('aiAgents.strategy.selectPlaceholder', 'Chọn một chiến lược')}
              fullWidth
            />
          </FormItem>

          {/* Hiển thị thông tin chiến lược đã chọn */}
          {selectedStrategy && (
            <div className="mt-4">
              {selectedStrategy.description && (
                <Typography variant="body2" className="mb-4 text-gray-600 dark:text-gray-300">
                  {selectedStrategy.description}
                </Typography>
              )}

              <Typography variant="subtitle1" className="mb-2">
                {t('aiAgents.strategy.parameters', 'Tham số')}
              </Typography>

              <div className="space-y-4">
                {selectedStrategy.parameters.map(param => (
                  <FormItem key={param.id} label={param.name}>
                    {renderParameter(param)}
                  </FormItem>
                ))}
              </div>

              <div className="flex justify-end mt-6">
                <Button
                  variant="primary"
                  onClick={handleSaveStrategy}
                  isLoading={isUpdating}
                  disabled={!hasChanges}
                  leftIcon={<Icon name="save" size="sm" />}
                >
                  {t('common.save', 'Lưu')}
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}
    </CollapsibleCard>
  );
};

export default StrategySection;
