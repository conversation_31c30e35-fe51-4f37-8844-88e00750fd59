import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Button,
  Icon,
  Card,
  FormItem,
  Input,
  Select,
  Typography,
  EmptyState,
} from '@/shared/components/common';
import { Vector, ParameterValue } from '../../types/agent.types';
import { useUpdateAgent } from '../../hooks';

/**
 * Interface cho vector với cấu hình cụ thể
 */
interface VectorWithConfig {
  id: string;
  name: string;
  description?: string;
  configuration: Record<string, ParameterValue>;
}

/**
 * Props cho component VectorSection
 */
interface VectorSectionProps {
  /**
   * ID của Agent
   */
  agentId: string;

  /**
   * Vector hiện tại của Agent
   */
  vector?: Vector;

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component hiển thị cấu hình vector của Agent
 */
const VectorSection: React.FC<VectorSectionProps> = ({ agentId, vector, onToggle }) => {
  const { t } = useTranslation();
  const [selectedVector, setSelectedVector] = useState<VectorWithConfig | undefined>(
    vector as VectorWithConfig
  );
  const [originalVector, setOriginalVector] = useState<VectorWithConfig | undefined>(
    vector as VectorWithConfig
  );
  const [configuration, setConfiguration] = useState<Record<string, ParameterValue>>(
    vector?.configuration || {}
  );
  const [originalConfiguration, setOriginalConfiguration] = useState<Record<string, ParameterValue>>(
    vector?.configuration || {}
  );
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // Sử dụng hooks
  const { mutate: updateAgent, isPending: isUpdating } = useUpdateAgent();

  // Cập nhật state khi vector thay đổi từ props
  React.useEffect(() => {
    if (vector) {
      setSelectedVector(vector as VectorWithConfig);
      setOriginalVector(vector as VectorWithConfig);
      setConfiguration(vector.configuration || {});
      setOriginalConfiguration(vector.configuration || {});
      setHasChanges(false);
    }
  }, [vector]);

  /**
   * Kiểm tra xem có thay đổi so với dữ liệu ban đầu không
   */
  const checkForChanges = () => {
    // Nếu chưa chọn vector, không có thay đổi
    if (!selectedVector) {
      setHasChanges(false);
      return;
    }

    // Kiểm tra nếu đã đổi vector
    if (selectedVector.id !== originalVector?.id) {
      setHasChanges(true);
      return;
    }

    // Kiểm tra thay đổi trong cấu hình
    const configKeys = new Set([
      ...Object.keys(configuration),
      ...Object.keys(originalConfiguration)
    ]);

    const hasConfigChanges = Array.from(configKeys).some(key => {
      return configuration[key] !== originalConfiguration[key];
    });

    setHasChanges(hasConfigChanges);
  };

  /**
   * Xử lý thay đổi cấu hình
   */
  const handleConfigChange = (key: string, value: ParameterValue) => {
    setConfiguration(prev => ({
      ...prev,
      [key]: value,
    }));

    // Kiểm tra thay đổi sau khi cập nhật
    setTimeout(checkForChanges, 0);
  };

  /**
   * Xử lý lưu thay đổi
   */
  const handleSave = () => {
    if (!selectedVector || !hasChanges) return;

    updateAgent(
      {
        id: agentId,
        data: {
          vector: {
            ...selectedVector,
            configuration,
          },
        },
      },
      {
        onSuccess: () => {
          // Cập nhật original vector và configuration
          setOriginalVector(selectedVector);
          setOriginalConfiguration({...configuration});
          setHasChanges(false);

          // Hiển thị thông báo thành công
          alert(t('aiAgents.vector.saveSuccess', 'Lưu cấu hình vector thành công!'));
        },
        onError: (error) => {
          // Hiển thị thông báo lỗi
          console.error('Update vector error:', error);
          alert(t('aiAgents.vector.saveError', 'Có lỗi xảy ra khi lưu cấu hình vector. Vui lòng thử lại.'));
        }
      }
    );
  };

  // Xử lý khi đóng/mở card
  const handleCardToggle = (isOpen: boolean) => {
    if (onToggle) {
      onToggle(isOpen);
    }
  };

  // Danh sách vector mẫu
  const vectorOptions: VectorWithConfig[] = [
    {
      id: '1',
      name: 'Default Vector',
      description: 'Standard embedding vector',
      configuration: {
        dimensions: 1536,
        model: 'text-embedding-3-small',
      },
    },
    {
      id: '2',
      name: 'High Precision Vector',
      description: 'High precision embedding for complex tasks',
      configuration: {
        dimensions: 3072,
        model: 'text-embedding-3-large',
      },
    },
    {
      id: '3',
      name: 'Multilingual Vector',
      description: 'Optimized for multiple languages',
      configuration: {
        dimensions: 1536,
        model: 'text-embedding-3-small',
        multilingual: true,
      },
    },
  ];

  /**
   * Xử lý chọn vector
   */
  const handleSelectVector = (id: string) => {
    const selected = vectorOptions.find(v => v.id === id);
    if (selected) {
      setSelectedVector(selected);
      // Chuyển đổi configuration object thành record phù hợp
      setConfiguration({...selected.configuration});

      // Kiểm tra thay đổi sau khi cập nhật
      setTimeout(checkForChanges, 0);
    }
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center">
          <Icon name="vector" className="mr-2 text-indigo-500" />
          <span>{t('aiAgents.vector.title', 'Vector')}</span>
        </div>
      }
      className="mb-6"
      onToggle={handleCardToggle}
    >
      <div className="mb-4">
        <Typography variant="body1">
          {t(
            'aiAgents.vector.description',
            'Cấu hình vector embedding cho Agent để tối ưu hóa khả năng hiểu và xử lý ngôn ngữ.'
          )}
        </Typography>
      </div>

      <Card className="p-6">
        {/* Chọn vector */}
        <FormItem label={t('aiAgents.vector.selectVector', 'Chọn vector')}>
          <Select
            value={selectedVector?.id || ''}
            onChange={(value: string | number | string[] | number[]) => {
              if (typeof value === 'string') {
                handleSelectVector(value);
              }
            }}
            options={vectorOptions.map(v => ({ value: v.id, label: v.name }))}
            placeholder={t('aiAgents.vector.selectPlaceholder', 'Chọn một vector')}
            fullWidth
          />
        </FormItem>

        {/* Hiển thị thông tin vector đã chọn */}
        {selectedVector ? (
          <div className="mt-4">
            {selectedVector.description && (
              <Typography variant="body2" className="mb-4 text-gray-600 dark:text-gray-300">
                {selectedVector.description}
              </Typography>
            )}

            <Typography variant="subtitle1" className="mb-2">
              {t('aiAgents.vector.configuration', 'Cấu hình')}
            </Typography>

            <div className="space-y-4">
              {Object.entries(configuration).map(([key, value]) => (
                <FormItem key={key} label={key}>
                  {typeof value === 'boolean' ? (
                    <Select
                      value={value ? 'true' : 'false'}
                      onChange={val => {
                        if (typeof val === 'string') {
                          handleConfigChange(key, val === 'true');
                        }
                      }}
                      options={[
                        { value: 'true', label: t('common.yes', 'Có') },
                        { value: 'false', label: t('common.no', 'Không') },
                      ]}
                      fullWidth
                    />
                  ) : typeof value === 'number' ? (
                    <Input
                      type="number"
                      value={value}
                      onChange={e => handleConfigChange(key, Number(e.target.value))}
                      fullWidth
                    />
                  ) : (
                    <Input
                      value={value as string}
                      onChange={e => handleConfigChange(key, e.target.value)}
                      fullWidth
                    />
                  )}
                </FormItem>
              ))}
            </div>

            <div className="flex justify-end mt-6">
              <Button
                variant="primary"
                onClick={handleSave}
                isLoading={isUpdating}
                disabled={!hasChanges}
                leftIcon={<Icon name="save" size="sm" />}
              >
                {t('common.save', 'Lưu')}
              </Button>
            </div>
          </div>
        ) : (
          <EmptyState
            icon="vector"
            title={t('aiAgents.vector.noVector', 'Chưa chọn vector')}
            description={t(
              'aiAgents.vector.selectVectorDescription',
              'Vui lòng chọn một vector để cấu hình.'
            )}
          />
        )}
      </Card>
    </CollapsibleCard>
  );
};

export default VectorSection;
