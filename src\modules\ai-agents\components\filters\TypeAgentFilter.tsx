import React, { useState } from 'react';
import { Button, Card, Icon, Input, Select } from '@/shared/components/common';
import { SortDirection, TypeAgentSortBy } from '../../types';

export interface TypeAgentFilterProps {
  /**
   * Giá trị tìm kiếm hiện tại
   */
  search?: string;

  /**
   * Filter theo loại agent (system/user)
   */
  isSystem?: boolean;

  /**
   * Trường sắp xếp hiện tại
   */
  sortBy?: TypeAgentSortBy;

  /**
   * Hướng sắp xếp hiện tại
   */
  sortDirection?: SortDirection;

  /**
   * Callback khi thay đổi filter
   */
  onFilterChange?: (filters: TypeAgentFilterData) => void;

  /**
   * Callback khi reset filter
   */
  onReset?: () => void;

  /**
   * Hiển thị dạng compact
   */
  compact?: boolean;
}

export interface TypeAgentFilterData {
  search?: string;
  isSystem?: boolean;
  sortBy?: TypeAgentSortBy;
  sortDirection?: SortDirection;
}

/**
 * Component filter cho danh sách Type Agent
 */
const TypeAgentFilter: React.FC<TypeAgentFilterProps> = ({
  search = '',
  isSystem,
  sortBy = TypeAgentSortBy.CREATED_AT,
  sortDirection = SortDirection.DESC,
  onFilterChange,
  onReset,
  compact = false
}) => {
  // Local state cho form
  const [localFilters, setLocalFilters] = useState<TypeAgentFilterData>({
    search,
    isSystem,
    sortBy,
    sortDirection
  });

  // Options cho select
  const agentTypeOptions = [
    { value: '', label: 'Tất cả loại' },
    { value: 'true', label: 'System Agents' },
    { value: 'false', label: 'User Agents' }
  ];

  const sortByOptions = [
    { value: TypeAgentSortBy.NAME, label: 'Tên' },
    { value: TypeAgentSortBy.CREATED_AT, label: 'Ngày tạo' }
  ];

  const sortDirectionOptions = [
    { value: SortDirection.ASC, label: 'Tăng dần' },
    { value: SortDirection.DESC, label: 'Giảm dần' }
  ];

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof TypeAgentFilterData, value: string | boolean | TypeAgentSortBy | SortDirection | undefined) => {
    const newFilters = {
      ...localFilters,
      [field]: value
    };
    setLocalFilters(newFilters);

    // Gọi callback ngay lập tức cho search
    if (field === 'search') {
      onFilterChange?.(newFilters);
    }
  };

  // Xử lý apply filter
  const handleApplyFilter = () => {
    onFilterChange?.(localFilters);
  };

  // Xử lý reset filter
  const handleReset = () => {
    const resetFilters: TypeAgentFilterData = {
      search: '',
      isSystem: undefined,
      sortBy: TypeAgentSortBy.CREATED_AT,
      sortDirection: SortDirection.DESC
    };
    setLocalFilters(resetFilters);
    onFilterChange?.(resetFilters);
    onReset?.();
  };

  // Kiểm tra có filter nào được áp dụng không
  const hasActiveFilters = localFilters.search ||
                          localFilters.isSystem !== undefined ||
                          localFilters.sortBy !== TypeAgentSortBy.CREATED_AT ||
                          localFilters.sortDirection !== SortDirection.DESC;

  if (compact) {
    return (
      <div className="flex flex-wrap gap-2 items-center">
        {/* Search */}
        <div className="flex-1 min-w-[200px]">
          <Input
            placeholder="Tìm kiếm loại agent..."
            value={localFilters.search || ''}
            onChange={(e) => handleInputChange('search', e.target.value)}
            leftIcon={<Icon name="search" size="sm" />}
          />
        </div>

        {/* Agent Type */}
        <Select
          options={agentTypeOptions}
          value={localFilters.isSystem?.toString() || ''}
          onChange={(value) => handleInputChange('isSystem', value === '' ? undefined : value === 'true')}
          placeholder="Loại agent"
          className="min-w-[120px]"
        />

        {/* Sort */}
        <Select
          options={sortByOptions}
          value={localFilters.sortBy || TypeAgentSortBy.CREATED_AT}
          onChange={(value) => handleInputChange('sortBy', value as TypeAgentSortBy)}
          className="min-w-[100px]"
        />

        <Select
          options={sortDirectionOptions}
          value={localFilters.sortDirection || SortDirection.DESC}
          onChange={(value) => handleInputChange('sortDirection', value as SortDirection)}
          className="min-w-[100px]"
        />

        {/* Reset */}
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Reset
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Bộ lọc</h3>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            leftIcon={<Icon name="refresh-cw" size="sm" />}
          >
            Đặt lại
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div>
          <label className="block text-sm font-medium mb-2">Tìm kiếm</label>
          <Input
            placeholder="Nhập tên loại agent..."
            value={localFilters.search || ''}
            onChange={(e) => handleInputChange('search', e.target.value)}
            leftIcon={<Icon name="search" size="sm" />}
          />
        </div>

        {/* Agent Type */}
        <div>
          <label className="block text-sm font-medium mb-2">Loại Agent</label>
          <Select
            options={agentTypeOptions}
            value={localFilters.isSystem?.toString() || ''}
            onChange={(value) => handleInputChange('isSystem', value === '' ? undefined : value === 'true')}
            placeholder="Chọn loại agent"
          />
        </div>

        {/* Sort By */}
        <div>
          <label className="block text-sm font-medium mb-2">Sắp xếp theo</label>
          <Select
            options={sortByOptions}
            value={localFilters.sortBy || TypeAgentSortBy.CREATED_AT}
            onChange={(value) => handleInputChange('sortBy', value as TypeAgentSortBy)}
          />
        </div>

        {/* Sort Direction */}
        <div>
          <label className="block text-sm font-medium mb-2">Thứ tự</label>
          <Select
            options={sortDirectionOptions}
            value={localFilters.sortDirection || SortDirection.DESC}
            onChange={(value) => handleInputChange('sortDirection', value as SortDirection)}
          />
        </div>
      </div>

      <div className="flex justify-end gap-2 mt-4">
        <Button
          variant="outline"
          onClick={handleReset}
          disabled={!hasActiveFilters}
        >
          Đặt lại
        </Button>
        <Button
          variant="primary"
          onClick={handleApplyFilter}
        >
          Áp dụng
        </Button>
      </div>
    </Card>
  );
};

export default TypeAgentFilter;
