/**
 * Context definition cho Agent Configuration Accordion
 */

import { createContext } from 'react';

export type ConfigComponentId =
  | 'profile'
  | 'model'
  | 'integration'
  | 'strategy'
  | 'resources'
  | 'response'
  | 'convert'
  | 'multiAgent';

export interface AgentConfigAccordionContextType {
  openComponent: ConfigComponentId | null;
  setOpenComponent: (componentId: ConfigComponentId | null) => void;
  toggleComponent: (componentId: ConfigComponentId) => void;
  isComponentOpen: (componentId: ConfigComponentId) => boolean;
}

export const AgentConfigAccordionContext = createContext<AgentConfigAccordionContextType | undefined>(undefined);
