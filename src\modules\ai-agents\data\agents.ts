/**
 * Mock data for AI agents
 */
export interface AIAgent {
  id: string;
  name: string;
  description: string;
  avatar: string;
  category: string;
  type: 'assistant' | 'agent';
  model: string;
  isActive: boolean;
  level: number;
  experience: number;
  experienceToNextLevel: number;
}

export const aiAgents: AIAgent[] = [
  {
    id: '1',
    name: 'Assistant',
    description: 'General-purpose assistant that can help with a wide range of tasks.',
    avatar: '/assets/images/ai-agents/assistant-robot.svg',
    category: 'general',
    type: 'assistant',
    model: 'deepseek-r1',
    isActive: true,
    level: 5,
    experience: 72,
    experienceToNextLevel: 100,
  },
  {
    id: '2',
    name: 'Coder',
    description:
      'Specialized in writing, reviewing, and debugging code across multiple programming languages.',
    avatar: '/assets/images/ai-agents/coder-robot.svg',
    category: 'development',
    type: 'agent',
    model: 'o3-mini',
    isActive: true,
    level: 3,
    experience: 45,
    experienceToNextLevel: 100,
  },
  {
    id: '3',
    name: 'Writer',
    description: 'Helps with writing, editing, and improving various types of content.',
    avatar: '/assets/images/ai-agents/writer-robot.svg',
    category: 'content',
    type: 'agent',
    model: 'gemini-2.0-flash',
    isActive: true,
    level: 2,
    experience: 30,
    experienceToNextLevel: 100,
  },
  {
    id: '4',
    name: 'Researcher',
    description: 'Assists with research, data analysis, and information gathering.',
    avatar: '/assets/images/ai-agents/researcher-robot.svg',
    category: 'research',
    type: 'agent',
    model: 'claude-3.7-sonet',
    isActive: false,
    level: 1,
    experience: 10,
    experienceToNextLevel: 100,
  },
  {
    id: '5',
    name: 'Designer',
    description: 'Provides guidance on design principles, UI/UX, and visual aesthetics.',
    avatar: '/assets/images/ai-agents/designer-robot.svg',
    category: 'design',
    type: 'agent',
    model: 'gemini-1.5-flash',
    isActive: true,
    level: 4,
    experience: 60,
    experienceToNextLevel: 100,
  },
  {
    id: '6',
    name: 'Translator',
    description: 'Translates text between multiple languages with high accuracy.',
    avatar: '/assets/images/ai-agents/translator-robot.svg',
    category: 'language',
    type: 'agent',
    model: 'claude-3.5-sonet',
    isActive: true,
    level: 3,
    experience: 85,
    experienceToNextLevel: 100,
  },
];
