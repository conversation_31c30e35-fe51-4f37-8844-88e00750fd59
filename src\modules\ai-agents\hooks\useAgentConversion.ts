import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getAgentConversion,
  updateAgentConversion,
  // type AgentConversionResponse, // Unused for now
  type UpdateConversionDto
} from '../api/agent-conversion.api';

// Query keys
export const agentConversionKeys = {
  all: ['agent-conversion'] as const,
  byAgent: (agentId: string) => [...agentConversionKeys.all, agentId] as const,
};

// Hook để lấy conversion data
export const useAgentConversion = (agentId: string) => {
  return useQuery({
    queryKey: agentConversionKeys.byAgent(agentId),
    queryFn: () => getAgentConversion(agentId),
    enabled: !!agentId,
  });
};

// Hook để update conversion
export const useUpdateAgentConversion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: UpdateConversionDto }) =>
      updateAgentConversion(agentId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({
        queryKey: agentConversionKeys.byAgent(agentId),
      });
    },
  });
};
