import { useQuery } from '@tanstack/react-query';
import { useAgentBasicInfo } from './useAgentBasicInfo';
import { useAgentProfile } from './useAgentProfile';
// Enable lại các hooks sau khi đã cập nhật APIs
import { useAgentConversion } from './useAgentConversion';
import { useAgentFacebookPages, useAgentWebsites } from './useAgentIntegration';
import { useAgentMedia, useAgentProducts, useAgentUrls } from './useAgentResources';
import { useGetTypeAgentDetailWithService } from './useAgentService';
import { mapBasicInfoToAgentConfigData, createDefaultAgentConfigData } from '../utils/agent-data-mappers';
// import { AgentConfigData } from '../types'; // Unused for now

/**
 * Hook tổng hợp để load tất cả dữ liệu của agent cho edit mode
 * Nếu không tìm thấy agent, sẽ fallback về chế độ tạo mới với agentId
 * @param agentId - ID của agent cần edit
 * @param typeIdFromUrl - TypeId từ URL params (optional)
 */
export const useAgentEditData = (agentId: string, typeIdFromUrl?: string | null) => {
  // Debug re-render
  console.log('🔄 useAgentEditData RENDER:', {
    timestamp: new Date().toISOString(),
    agentId,
    typeIdFromUrl
  });
  // Load basic info
  const basicInfoQuery = useAgentBasicInfo(agentId);
  const {
    data: basicInfo,
    isLoading: isLoadingBasicInfo,
    error: basicInfoError
  } = basicInfoQuery;

  // Debug log cho basicInfo query
  console.log('useAgentEditData - BasicInfo Query:', {
    agentId,
    basicInfoQuery,
    basicInfo,
    isLoadingBasicInfo,
    basicInfoError
  });

  // Load profile
  const {
    data: profile,
    isLoading: isLoadingProfile,
    error: profileError
  } = useAgentProfile(agentId);

  // Enable lại các APIs sau khi đã cập nhật endpoints

  // Load conversion - chỉ khi đã có basicInfo
  const {
    data: conversion,
    isLoading: isLoadingConversion,
    error: conversionError
  } = useAgentConversion(agentId);

  // Load integrations - chỉ khi đã có basicInfo
  const {
    data: facebookPages,
    isLoading: isLoadingFacebook,
    error: facebookError
  } = useAgentFacebookPages(agentId);

  const {
    data: websites,
    isLoading: isLoadingWebsites,
    error: websitesError
  } = useAgentWebsites(agentId);

  // Load resources - chỉ khi đã có basicInfo
  const {
    data: media,
    isLoading: isLoadingMedia,
    error: mediaError
  } = useAgentMedia(agentId);

  const {
    data: products,
    isLoading: isLoadingProducts,
    error: productsError
  } = useAgentProducts(agentId);

  const {
    data: urls,
    isLoading: isLoadingUrls,
    error: urlsError
  } = useAgentUrls(agentId);

  // Chỉ quan tâm đến core loading states để tránh giật nhảy
  const isCoreLoading = isLoadingBasicInfo || isLoadingProfile;

  // Loading state cho tất cả APIs (để components con sử dụng)
  const isAllLoading = isLoadingBasicInfo || isLoadingProfile || isLoadingConversion ||
    isLoadingFacebook || isLoadingWebsites || isLoadingMedia || isLoadingProducts || isLoadingUrls;

  // Check if agent exists - nếu basic info load xong mà không có data và có lỗi 404
  const agentNotFound = !isLoadingBasicInfo && !basicInfo && basicInfoError;

  // Nếu có lỗi nghiêm trọng (không phải 404), hiển thị lỗi
  const hasCriticalError = basicInfoError && !agentNotFound;

  // Debug log cho agent existence check
  console.log('useAgentEditData - Agent existence check:', {
    agentId,
    isLoadingBasicInfo,
    basicInfo: !!basicInfo,
    basicInfoError,
    agentNotFound,
    hasCriticalError,
    mode: agentNotFound ? 'create' : 'edit'
  });

  // Load TypeAgent config dựa trên typeId từ URL hoặc basic info
  const typeId = typeIdFromUrl ? parseInt(typeIdFromUrl) : (basicInfo?.typeAgentId || 1);
  console.log('useAgentEditData - typeId logic:', {
    typeIdFromUrl,
    basicInfoTypeAgentId: basicInfo?.typeAgentId,
    finalTypeId: typeId
  });
  const {
    data: typeAgentDetailResponse,
    isLoading: isLoadingTypeAgent,
    error: typeAgentError
  } = useGetTypeAgentDetailWithService(typeId);

  // Debug log cho TypeAgent response
  console.log('useAgentEditData - TypeAgent response:', {
    typeId,
    typeAgentDetailResponse,
    typeAgentConfig: typeAgentDetailResponse?.result?.config,
    isLoadingTypeAgent,
    typeAgentError
  });

  // Map data to AgentConfigData format
  const agentConfigData = useQuery({
    queryKey: [
      'agent-edit-data',
      agentId,
      // Chỉ dùng primitive values thay vì objects để tránh reference changes
      !!basicInfo,
      !!profile,
      agentNotFound,
      basicInfo?.name, // Thêm một vài key fields để detect changes
      basicInfo?.updatedAt
    ],
    queryFn: () => {
      console.log('🔄 agentConfigData queryFn triggered:', {
        agentNotFound,
        hasBasicInfo: !!basicInfo,
        hasProfile: !!profile
      });

      // Nếu agent không tồn tại, tạo default data với agentId
      if (agentNotFound) {
        return createDefaultAgentConfigData(agentId);
      }

      // Nếu có basic info, map từ API data
      if (basicInfo) {
        return mapBasicInfoToAgentConfigData(basicInfo, profile);
      }

      return null;
    },
    enabled: !isLoadingBasicInfo, // Enable khi đã load xong basic info
    staleTime: 5 * 60 * 1000, // 5 minutes - tránh refetch liên tục
  });

  return {
    data: agentConfigData.data,
    // Chỉ dùng core loading để tránh giật nhảy màn hình
    isLoading: isCoreLoading || agentConfigData.isLoading || isLoadingTypeAgent,
    // Expose cả 2 loading states
    isCoreLoading,
    isAllLoading,
    error: hasCriticalError ? basicInfoError : agentConfigData.error,
    // Individual data for specific components
    basicInfo,
    profile,
    conversion,
    facebookPages,
    websites,
    media,
    products,
    urls,
    // TypeAgent data
    typeId,
    typeAgentDetailResponse,
    typeAgentConfig: typeAgentDetailResponse?.result?.config,
    // Individual loading states
    isLoadingBasicInfo,
    isLoadingProfile,
    isLoadingConversion,
    isLoadingFacebook,
    isLoadingWebsites,
    isLoadingMedia,
    isLoadingProducts,
    isLoadingUrls,
    isLoadingTypeAgent,
    // Individual errors
    basicInfoError,
    profileError,
    conversionError,
    facebookError,
    websitesError,
    mediaError,
    productsError,
    urlsError,
    typeAgentError,
    // Additional flags
    agentNotFound,
    hasCriticalError,
    mode: agentNotFound ? 'create' : 'edit' as 'create' | 'edit'
  };
};

/**
 * Hook đơn giản chỉ load basic info và profile cho các trường hợp cần ít dữ liệu
 */
export const useAgentBasicData = (agentId: string) => {
  const {
    data: basicInfo,
    isLoading: isLoadingBasicInfo,
    error: basicInfoError
  } = useAgentBasicInfo(agentId);

  const {
    data: profile,
    isLoading: isLoadingProfile,
    error: profileError
  } = useAgentProfile(agentId);

  const isLoading = isLoadingBasicInfo || isLoadingProfile;
  const error = basicInfoError || profileError;

  const agentData = useQuery({
    queryKey: ['agent-basic-data', agentId, basicInfo, profile],
    queryFn: () => {
      if (!basicInfo) return null;
      
      return mapBasicInfoToAgentConfigData(basicInfo, profile);
    },
    enabled: !!basicInfo,
  });

  return {
    data: agentData.data,
    isLoading: isLoading || agentData.isLoading,
    error: error || agentData.error,
    basicInfo,
    profile,
  };
};
