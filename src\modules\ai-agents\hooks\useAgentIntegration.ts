import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getAgentFacebookPages,
  addFacebookPagesToAgent,
  removeFacebookPageFromAgent,
  // AgentFacebookPagesResponse, // Unused for now
  AddFacebookPagesDto
} from '../api/agent-facebook.api';
import {
  getAgentWebsites,
  addWebsitesToAgent,
  removeWebsiteFromAgent,
  // AgentWebsitesResponse, // Unused for now
  AddWebsitesDto
} from '../api/agent-website.api';

// Query key factories
export const agentIntegrationKeys = {
  all: ['agent-integration'] as const,
  facebook: (agentId: string) => [...agentIntegrationKeys.all, 'facebook', agentId] as const,
  websites: (agentId: string) => [...agentIntegrationKeys.all, 'websites', agentId] as const,
};

// Facebook Pages hooks
export const useAgentFacebookPages = (agentId: string) => {
  return useQuery({
    queryKey: agentIntegrationKeys.facebook(agentId),
    queryFn: () => getAgentFacebookPages(agentId),
    enabled: !!agentId,
  });
};

export const useAddFacebookPages = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: AddFacebookPagesDto }) =>
      addFacebookPagesToAgent(agentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentIntegrationKeys.facebook(variables.agentId),
      });
    },
  });
};

export const useRemoveFacebookPage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, pageId }: { agentId: string; pageId: string }) =>
      removeFacebookPageFromAgent(agentId, pageId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentIntegrationKeys.facebook(variables.agentId),
      });
    },
  });
};

// Websites hooks
export const useAgentWebsites = (agentId: string) => {
  return useQuery({
    queryKey: agentIntegrationKeys.websites(agentId),
    queryFn: () => getAgentWebsites(agentId),
    enabled: !!agentId,
  });
};

export const useAddWebsites = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: AddWebsitesDto }) =>
      addWebsitesToAgent(agentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentIntegrationKeys.websites(variables.agentId),
      });
    },
  });
};

export const useRemoveWebsite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, websiteId }: { agentId: string; websiteId: string }) =>
      removeWebsiteFromAgent(agentId, websiteId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentIntegrationKeys.websites(variables.agentId),
      });
    },
  });
};
