import { useQuery } from '@tanstack/react-query';
import {
  getAgentList,
  getAgentById,
  // type AgentListResponse, // Unused for now
  // type AgentListItemDto, // Unused for now
  type GetAgentListParams
} from '../api/agent-list.api';

// Query keys
export const agentListKeys = {
  all: ['agent-list'] as const,
  lists: () => [...agentListKeys.all, 'list'] as const,
  list: (params: GetAgentListParams) => [...agentListKeys.lists(), params] as const,
  details: () => [...agentListKeys.all, 'detail'] as const,
  detail: (id: string) => [...agentListKeys.details(), id] as const,
};

// Hook để lấy danh sách agents
export const useAgentList = (params: GetAgentListParams = {}) => {
  return useQuery({
    queryKey: agentListKeys.list(params),
    queryFn: () => getAgentList(params),
  });
};

// Hook để lấy agent cụ thể từ danh sách (để có typeId)
export const useAgentFromList = (agentId: string) => {
  return useQuery({
    queryKey: agentListKeys.detail(agentId),
    queryFn: () => getAgentById(agentId),
    enabled: !!agentId,
  });
};
