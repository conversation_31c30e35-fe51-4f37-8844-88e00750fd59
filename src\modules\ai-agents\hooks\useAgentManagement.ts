import { useState, useCallback } from 'react';
import { useGetTypeAgents, useGetAgents, useCreateAgent, useUpdateAgent, useDeleteAgent } from './';
import { GetAgentsQueryDto, CreateAgentDto, UpdateAgentDto } from '../types';
import { TypeAgentQueryDto } from '../api/type-agent.api';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Hook tổng hợp để quản lý agents và type agents
 * Sử dụng trong AgentCreatePage và các component liên quan
 * Tuân thủ quy tắc ProductGuide.md - Section 4: REACT HOOKS
 */
export const useAgentManagement = () => {
  // State cho pagination và filtering
  const [agentParams, setAgentParams] = useState<GetAgentsQueryDto>({
    page: 1,
    limit: 10,
  });

  const [typeAgentParams, setTypeAgentParams] = useState<TypeAgentQueryDto>({
    page: 1,
    limit: 50, // L<PERSON>y nhiều hơn cho dropdown selection
    isSystem: true, // Chỉ lấy system type agents
  });

  // Queries
  const {
    data: agentsData,
    isLoading: isLoadingAgents,
    error: agentsError,
    refetch: refetchAgents,
  } = useGetAgents(agentParams);

  const {
    data: typeAgentsData,
    isLoading: isLoadingTypeAgents,
    error: typeAgentsError,
    refetch: refetchTypeAgents,
  } = useGetTypeAgents(typeAgentParams);

  // Mutations
  const createAgentMutation = useCreateAgent();
  const updateAgentMutation = useUpdateAgent();
  const deleteAgentMutation = useDeleteAgent();

  // Handlers cho Agent params
  const updateAgentParams = useCallback((newParams: Partial<GetAgentsQueryDto>) => {
    setAgentParams((prev: GetAgentsQueryDto) => ({ ...prev, ...newParams }));
  }, []);

  const updateTypeAgentParams = useCallback((newParams: Partial<TypeAgentQueryDto>) => {
    setTypeAgentParams((prev: TypeAgentQueryDto) => ({ ...prev, ...newParams }));
  }, []);

  // Handlers cho CRUD operations
  // ✅ ĐÚNG: Comprehensive error handling với notifications
  const handleCreateAgent = useCallback(
    async (data: CreateAgentDto) => {
      try {
        const result = await createAgentMutation.mutateAsync(data);

        NotificationUtil.success({
          message: 'Tạo agent thành công',
          duration: 3000,
        });

        return result;
      } catch (error) {
        console.error('Error creating agent:', error);

        NotificationUtil.error({
          message: 'Tạo agent thất bại. Vui lòng thử lại.',
          duration: 5000,
        });

        throw error;
      }
    },
    [createAgentMutation]
  );

  const handleUpdateAgent = useCallback(
    async (id: string, data: UpdateAgentDto) => {
      try {
        const result = await updateAgentMutation.mutateAsync({ id, data });

        NotificationUtil.success({
          message: 'Cập nhật agent thành công',
          duration: 3000,
        });

        return result;
      } catch (error) {
        console.error('Error updating agent:', error);

        NotificationUtil.error({
          message: 'Cập nhật agent thất bại. Vui lòng thử lại.',
          duration: 5000,
        });

        throw error;
      }
    },
    [updateAgentMutation]
  );

  const handleDeleteAgent = useCallback(
    async (id: string) => {
      try {
        await deleteAgentMutation.mutateAsync(id);

        NotificationUtil.success({
          message: 'Xóa agent thành công',
          duration: 3000,
        });
      } catch (error) {
        console.error('Error deleting agent:', error);

        NotificationUtil.error({
          message: 'Xóa agent thất bại. Vui lòng thử lại.',
          duration: 5000,
        });

        throw error;
      }
    },
    [deleteAgentMutation]
  );

  // Pagination handlers
  const handlePageChange = useCallback(
    (page: number) => {
      updateAgentParams({ page });
    },
    [updateAgentParams]
  );

  const handleLimitChange = useCallback(
    (limit: number) => {
      updateAgentParams({ limit, page: 1 });
    },
    [updateAgentParams]
  );

  // Search and filter handlers
  const handleSearch = useCallback(
    (search: string) => {
      updateAgentParams({ search, page: 1 });
    },
    [updateAgentParams]
  );

  const handleFilterByType = useCallback(
    (typeId?: number) => {
      updateAgentParams({ typeId, page: 1 });
    },
    [updateAgentParams]
  );

  const handleFilterByActive = useCallback(
    (active?: boolean) => {
      updateAgentParams({ active, page: 1 });
    },
    [updateAgentParams]
  );

  // Computed values
  const agents = agentsData?.result?.items || [];
  const agentsTotalItems = agentsData?.result?.meta?.totalItems || 0;
  const agentsCurrentPage = agentsData?.result?.meta?.currentPage || 1;
  const agentsLimit = agentsData?.result?.meta?.itemsPerPage || 10;

  const typeAgents = typeAgentsData?.result?.items || [];
  const typeAgentsTotalItems = typeAgentsData?.result?.meta?.totalItems || 0;

  // Loading states
  const isLoading = isLoadingAgents || isLoadingTypeAgents;
  const isMutating =
    createAgentMutation.isPending ||
    updateAgentMutation.isPending ||
    deleteAgentMutation.isPending;

  // Error states
  const error = agentsError || typeAgentsError;

  return {
    // Data
    agents,
    typeAgents,
    agentsTotalItems,
    typeAgentsTotalItems,
    agentsCurrentPage,
    agentsLimit,

    // Loading states
    isLoading,
    isLoadingAgents,
    isLoadingTypeAgents,
    isMutating,

    // Error states
    error,
    agentsError,
    typeAgentsError,

    // Mutation states
    createAgentMutation,
    updateAgentMutation,
    deleteAgentMutation,

    // Handlers
    handleCreateAgent,
    handleUpdateAgent,
    handleDeleteAgent,
    handlePageChange,
    handleLimitChange,
    handleSearch,
    handleFilterByType,
    handleFilterByActive,

    // Params
    agentParams,
    typeAgentParams,
    updateAgentParams,
    updateTypeAgentParams,

    // Refetch functions
    refetchAgents,
    refetchTypeAgents,
  };
};

export default useAgentManagement;
