import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getAgentProfile,
  updateAgentProfile,
  // AgentProfileDto, // Unused for now
  UpdateAgentProfileDto
} from '../api/agent-profile.api';

// Query key factory
export const agentProfileKeys = {
  all: ['agent-profile'] as const,
  detail: (agentId: string) => [...agentProfileKeys.all, agentId] as const,
};

// Hook để lấy profile
export const useAgentProfile = (agentId: string) => {
  return useQuery({
    queryKey: agentProfileKeys.detail(agentId),
    queryFn: () => getAgentProfile(agentId),
    enabled: !!agentId,
  });
};

// Hook để update profile
export const useUpdateAgentProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: UpdateAgentProfileDto }) =>
      updateAgentProfile(agentId, data),
    onSuccess: (data, variables) => {
      // Invalidate và update cache
      queryClient.invalidateQueries({
        queryKey: agentProfileKeys.detail(variables.agentId),
      });
      
      // Update cache trực tiếp
      queryClient.setQueryData(
        agentProfileKeys.detail(variables.agentId),
        data
      );
    },
  });
};
