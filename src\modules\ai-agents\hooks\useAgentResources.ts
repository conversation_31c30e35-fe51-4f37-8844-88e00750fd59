import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getAgentMedia,
  addMediaToAgent,
  removeMediaFromAgent,
  // AgentMediaResponse, // Unused for now
  AddMediaDto,
  RemoveMediaDto
} from '../api/agent-media.api';
import {
  getAgentProducts,
  addProductsToAgent,
  removeProductsFromAgent,
  // AgentProductsResponse, // Unused for now
  AddProductsDto,
  RemoveProductsDto
} from '../api/agent-product.api';
import {
  getAgentUrls,
  addUrlsToAgent,
  removeUrlsFromAgent,
  // AgentUrlsResponse, // Unused for now
  AddUrlsDto,
  RemoveUrlsDto
} from '../api/agent-url.api';

// Query key factories
export const agentResourcesKeys = {
  all: ['agent-resources'] as const,
  media: (agentId: string) => [...agentResourcesKeys.all, 'media', agentId] as const,
  products: (agentId: string) => [...agentResourcesKeys.all, 'products', agentId] as const,
  urls: (agentId: string) => [...agentResourcesKeys.all, 'urls', agentId] as const,
};

// Media hooks
export const useAgentMedia = (agentId: string) => {
  return useQuery({
    queryKey: agentResourcesKeys.media(agentId),
    queryFn: () => getAgentMedia(agentId),
    enabled: !!agentId,
  });
};

export const useAddMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: AddMediaDto }) =>
      addMediaToAgent(agentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentResourcesKeys.media(variables.agentId),
      });
    },
  });
};

export const useRemoveMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: RemoveMediaDto }) =>
      removeMediaFromAgent(agentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentResourcesKeys.media(variables.agentId),
      });
    },
  });
};

// Products hooks
export const useAgentProducts = (agentId: string) => {
  return useQuery({
    queryKey: agentResourcesKeys.products(agentId),
    queryFn: () => getAgentProducts(agentId),
    enabled: !!agentId,
  });
};

export const useAddProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: AddProductsDto }) =>
      addProductsToAgent(agentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentResourcesKeys.products(variables.agentId),
      });
    },
  });
};

export const useRemoveProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: RemoveProductsDto }) =>
      removeProductsFromAgent(agentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentResourcesKeys.products(variables.agentId),
      });
    },
  });
};

// URLs hooks
export const useAgentUrls = (agentId: string) => {
  return useQuery({
    queryKey: agentResourcesKeys.urls(agentId),
    queryFn: () => getAgentUrls(agentId),
    enabled: !!agentId,
  });
};

export const useAddUrls = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: AddUrlsDto }) =>
      addUrlsToAgent(agentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentResourcesKeys.urls(variables.agentId),
      });
    },
  });
};

export const useRemoveUrls = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, data }: { agentId: string; data: RemoveUrlsDto }) =>
      removeUrlsFromAgent(agentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: agentResourcesKeys.urls(variables.agentId),
      });
    },
  });
};
