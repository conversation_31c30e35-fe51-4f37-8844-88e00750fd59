import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  getAvailableFacebookPages,
  getAgentFacebookPages,
  connectFacebookPageToAgent,
  updateAgentFacebookPage,
  disconnectFacebookPageFromAgent,
  FacebookPageDto,
  AgentFacebookPageDto,
  FacebookPageQueryDto,
  ConnectFacebookPageDto,
  UpdateAgentFacebookPageDto
} from '../api/facebook-page.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Hook để lấy danh sách Facebook Pages có sẵn
 */
export const useGetAvailableFacebookPages = (
  params?: FacebookPageQueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<FacebookPageDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AVAILABLE_FACEBOOK_PAGES, params],
    queryFn: () => getAvailableFacebookPages(params),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy danh sách Facebook Pages được kết nối với agent
 */
export const useGetAgentFacebookPages = (
  agentId: string | undefined,
  params?: QueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<AgentFacebookPageDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_FACEBOOK_PAGES, agentId, params],
    queryFn: () => getAgentFacebookPages(agentId as string, params),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để kết nối Facebook Page với agent
 */
export const useConnectFacebookPageToAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.CONNECT_FACEBOOK_PAGE],
    mutationFn: ({ agentId, data }: { agentId: string; data: ConnectFacebookPageDto }) =>
      connectFacebookPageToAgent(agentId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_FACEBOOK_PAGES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};

/**
 * Hook để cập nhật cài đặt Facebook Page của agent
 */
export const useUpdateAgentFacebookPage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_AGENT_FACEBOOK_PAGE],
    mutationFn: ({ 
      agentId, 
      pageId, 
      data 
    }: { 
      agentId: string; 
      pageId: string; 
      data: UpdateAgentFacebookPageDto 
    }) => updateAgentFacebookPage(agentId, pageId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_FACEBOOK_PAGES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};

/**
 * Hook để ngắt kết nối Facebook Page khỏi agent
 */
export const useDisconnectFacebookPageFromAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.DISCONNECT_FACEBOOK_PAGE],
    mutationFn: ({ agentId, pageId }: { agentId: string; pageId: string }) =>
      disconnectFacebookPageFromAgent(agentId, pageId),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_FACEBOOK_PAGES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};
