import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { getProfile, updateProfile, ProfileResponseDto, UpdateProfileDto } from '../api/profile.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy thông tin profile của agent
 * @param agentId ID của agent
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetProfile = (
  agentId: string | undefined,
  options?: UseQueryOptions<ApiResponse<ProfileResponseDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.PROFILE, agentId],
    queryFn: () => getProfile(agentId as string),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để cập nhật profile của agent
 * @returns Mutation result
 */
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_PROFILE],
    mutationFn: ({ agentId, data }: { agentId: string; data: UpdateProfileDto }) =>
      updateProfile(agentId, data),
    onSuccess: (_, { agentId }) => {
      // Invalidate profile và agent detail
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.PROFILE, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};
