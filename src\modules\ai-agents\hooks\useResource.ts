import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  getResources,
  getAgentMedia,
  getAgentUrls,
  getAgentProducts,
  addAgentMedia,
  addAgentUrl,
  addAgentProduct,
  removeAgentMedia,
  removeAgentUrl,
  removeAgentProduct,
  ResourceResponseDto,
  AgentMediaDto,
  AgentUrlDto,
  AgentProductDto,
  CreateMediaDto,
  CreateUrlDto,
  CreateProductDto,
  ResourceQueryDto
} from '../api/resource.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Hook để lấy tổng quan resources của agent
 */
export const useGetResources = (
  agentId: string | undefined,
  params?: ResourceQueryDto,
  options?: UseQueryOptions<ApiResponse<ResourceResponseDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.RESOURCES, agentId, params],
    queryFn: () => getResources(agentId as string, params),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy danh sách media của agent
 */
export const useGetAgentMedia = (
  agentId: string | undefined,
  params?: QueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<AgentMediaDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_MEDIA, agentId, params],
    queryFn: () => getAgentMedia(agentId as string, params),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy danh sách URLs của agent
 */
export const useGetAgentUrls = (
  agentId: string | undefined,
  params?: QueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<AgentUrlDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_URLS, agentId, params],
    queryFn: () => getAgentUrls(agentId as string, params),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy danh sách products của agent
 */
export const useGetAgentProducts = (
  agentId: string | undefined,
  params?: QueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<AgentProductDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_PRODUCTS, agentId, params],
    queryFn: () => getAgentProducts(agentId as string, params),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để thêm media vào agent
 */
export const useAddAgentMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.ADD_AGENT_MEDIA],
    mutationFn: ({ agentId, data }: { agentId: string; data: CreateMediaDto }) =>
      addAgentMedia(agentId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.RESOURCES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_MEDIA, agentId] });
    },
  });
};

/**
 * Hook để thêm URL vào agent
 */
export const useAddAgentUrl = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.ADD_AGENT_URL],
    mutationFn: ({ agentId, data }: { agentId: string; data: CreateUrlDto }) =>
      addAgentUrl(agentId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.RESOURCES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_URLS, agentId] });
    },
  });
};

/**
 * Hook để thêm product vào agent
 */
export const useAddAgentProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.ADD_AGENT_PRODUCT],
    mutationFn: ({ agentId, data }: { agentId: string; data: CreateProductDto }) =>
      addAgentProduct(agentId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.RESOURCES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_PRODUCTS, agentId] });
    },
  });
};

/**
 * Hook để xóa media khỏi agent
 */
export const useRemoveAgentMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.REMOVE_AGENT_MEDIA],
    mutationFn: ({ agentId, mediaId }: { agentId: string; mediaId: string }) =>
      removeAgentMedia(agentId, mediaId),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.RESOURCES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_MEDIA, agentId] });
    },
  });
};

/**
 * Hook để xóa URL khỏi agent
 */
export const useRemoveAgentUrl = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.REMOVE_AGENT_URL],
    mutationFn: ({ agentId, urlId }: { agentId: string; urlId: string }) =>
      removeAgentUrl(agentId, urlId),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.RESOURCES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_URLS, agentId] });
    },
  });
};

/**
 * Hook để xóa product khỏi agent
 */
export const useRemoveAgentProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.REMOVE_AGENT_PRODUCT],
    mutationFn: ({ agentId, productId }: { agentId: string; productId: string }) =>
      removeAgentProduct(agentId, productId),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.RESOURCES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_PRODUCTS, agentId] });
    },
  });
};
