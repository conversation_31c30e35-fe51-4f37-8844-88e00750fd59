import { useQuery } from '@tanstack/react-query';
import { getToolsIntegration, Tool, ToolsResponse } from '../api/tools.api';

/**
 * Hook để lấy danh sách tools integration
 */
export const useToolsIntegration = () => {
  return useQuery<ToolsResponse, Error>({
    queryKey: ['tools-integration'],
    queryFn: getToolsIntegration,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export type { Tool, ToolsResponse };
