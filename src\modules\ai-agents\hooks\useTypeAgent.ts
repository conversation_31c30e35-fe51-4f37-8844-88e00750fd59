import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import {
  getTypeAgents,
  getTypeAgentDetail,
  getTypeAgentsForSelection,
  TypeAgentDto,
  TypeAgentQueryDto
} from '../api/type-agent.api';

import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';



/**
 * Hook để lấy danh sách type agents
 * @param params Query params
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetTypeAgents = (
  params?: TypeAgentQueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<TypeAgentDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_LIST, params],
    queryFn: () => getTypeAgents(params),
    staleTime: 10 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy chi tiết type agent
 */
export const useGetTypeAgentDetail = (
  id: number | undefined,
  options?: UseQueryOptions<ApiResponse<TypeAgentDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_DETAIL, id],
    queryFn: () => getTypeAgentDetail(id as number),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy danh sách type agents cho selection (simplified)
 */
export const useGetTypeAgentsForSelection = (
  params?: { search?: string; limit?: number },
  options?: UseQueryOptions<ApiResponse<TypeAgentDto[]>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_SELECTION, params],
    queryFn: () => getTypeAgentsForSelection(params),
    staleTime: 10 * 60 * 1000,
    ...options,
  });
};

// Removed: useCreateTypeAgent, useUpdateTypeAgent, useDeleteTypeAgent hooks
