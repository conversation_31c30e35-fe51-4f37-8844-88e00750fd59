import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  getAvailableWebsites,
  getAgentWebsites,
  connectWebsiteToAgent,
  updateAgentWebsite,
  disconnectWebsiteFromAgent,
  getWebsiteWidgetScript,
  WebsiteDto,
  AgentWebsiteDto,
  WebsiteQueryDto,
  ConnectWebsiteDto,
  UpdateAgentWebsiteDto
} from '../api/website.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Hook để lấy danh sách Websites có sẵn
 */
export const useGetAvailableWebsites = (
  params?: WebsiteQueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<WebsiteDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AVAILABLE_WEBSITES, params],
    queryFn: () => getAvailableWebsites(params),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy danh sách Websites được kết nối với agent
 */
export const useGetAgentWebsites = (
  agentId: string | undefined,
  params?: QueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<AgentWebsiteDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_WEBSITES, agentId, params],
    queryFn: () => getAgentWebsites(agentId as string, params),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy widget script cho website
 */
export const useGetWebsiteWidgetScript = (
  agentId: string | undefined,
  websiteId: string | undefined,
  options?: UseQueryOptions<ApiResponse<{ script: string }>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.WEBSITE_WIDGET_SCRIPT, agentId, websiteId],
    queryFn: () => getWebsiteWidgetScript(agentId as string, websiteId as string),
    enabled: !!agentId && !!websiteId,
    staleTime: 10 * 60 * 1000, // 10 minutes (script ít thay đổi)
    ...options,
  });
};

/**
 * Hook để kết nối Website với agent
 */
export const useConnectWebsiteToAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.CONNECT_WEBSITE],
    mutationFn: ({ agentId, data }: { agentId: string; data: ConnectWebsiteDto }) =>
      connectWebsiteToAgent(agentId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_WEBSITES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};

/**
 * Hook để cập nhật cài đặt Website của agent
 */
export const useUpdateAgentWebsite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_AGENT_WEBSITE],
    mutationFn: ({ 
      agentId, 
      websiteId, 
      data 
    }: { 
      agentId: string; 
      websiteId: string; 
      data: UpdateAgentWebsiteDto 
    }) => updateAgentWebsite(agentId, websiteId, data),
    onSuccess: (_, { agentId, websiteId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_WEBSITES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.WEBSITE_WIDGET_SCRIPT, agentId, websiteId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};

/**
 * Hook để ngắt kết nối Website khỏi agent
 */
export const useDisconnectWebsiteFromAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.DISCONNECT_WEBSITE],
    mutationFn: ({ agentId, websiteId }: { agentId: string; websiteId: string }) =>
      disconnectWebsiteFromAgent(agentId, websiteId),
    onSuccess: (_, { agentId, websiteId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_WEBSITES, agentId] });
      queryClient.removeQueries({ queryKey: [AGENT_QUERY_KEYS.WEBSITE_WIDGET_SCRIPT, agentId, websiteId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};
