{"aiAgents": {"common": {"save": "Save", "cancel": "Cancel", "add": "Add", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "sort": "Sort", "required": "Required", "update": "Update", "create": "Create", "select": "Select", "configure": "Configure", "name": "Name", "description": "Description", "type": "Type", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "noData": "No data", "loading": "Loading...", "error": "An error occurred", "success": "Success", "confirm": "Confirm", "confirmDelete": "Are you sure you want to delete?", "yes": "Yes", "no": "No", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish"}, "aiAgents": "AI Agents", "agentCreate": {"title": "Create Agent", "customAgentButton": "Create Custom Agent", "selectAgentType": "Select Agent Type", "selectAgentDescription": "Choose an Agent type from the list below or create your own custom Agent.", "configureAgent": "Configure {name}", "sortBy": "Sort by", "sortName": "Name", "sortDate": "Created Date", "order": "Order", "orderAsc": "Ascending", "orderDesc": "Descending", "agentTypeDescription": "Choose the agent type that suits your needs. Each agent type has different capabilities and characteristics."}, "profileConfig": {"title": "Profile Information", "name": "Name", "birthDate": "Birth Date", "gender": "Gender", "language": "Language", "education": "Education", "country": "Country", "position": "Position", "skills": "Skills", "personality": "Personality", "avatar": "Avatar", "male": "Male", "female": "Female", "other": "Other", "highSchool": "High School", "college": "College", "university": "University", "postgraduate": "Postgraduate", "addSkill": "<PERSON><PERSON>", "addPersonality": "Add Personality", "skillPlaceholder": "Enter label and press enter", "personalityPlaceholder": "Enter label and press enter"}, "modelConfig": {"title": "Model Configuration", "provider": "Provider", "model": "Model", "vectorStore": "Vector Store", "advancedSettings": "Advanced Settings", "maxTokens": "<PERSON>", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "instructions": "Instructions", "instructionsPlaceholder": "Enter instructions for the model..."}, "integrationConfig": {"title": "Integrations", "facebook": "Facebook", "website": "Website", "addFacebook": "Add Facebook Integration", "addWebsite": "Add Website Integration", "noFacebookIntegration": "No Facebook integrations yet", "noWebsiteIntegration": "No Website integrations yet", "selectFacebook": "Select Facebook Page", "selectWebsite": "Select Website", "facebookPageName": "Facebook Page Name", "websiteName": "Website Name", "websiteUrl": "Website URL"}, "strategyConfig": {"title": "Strategy", "selectStrategy": "Select a strategy for your Agent", "selectStrategyDescription": "Choose a strategy and configure the processing steps", "basicStrategy": "Basic Strategy", "basicStrategyDescription": "Simple strategy with default settings", "advancedStrategy": "Advanced Strategy", "advancedStrategyDescription": "Strategy with advanced options and complex processing", "customStrategy": "Custom Strategy", "customStrategyDescription": "Create your own strategy with fully customized settings", "configureStrategy": "Configure Strategy", "step": "Step {number}", "input": "Input"}, "convertConfig": {"title": "Convert Configuration", "configureFields": "Configure data fields to collect", "configureFieldsDescription": "Select the data fields that the Agent will collect from users", "noFields": "No data fields configured yet", "addField": "Add New Field", "editField": "Edit Field", "fieldName": "Field Name", "fieldDescription": "Description", "fieldType": "Data Type", "fieldRequired": "Required Field", "fieldNamePlaceholder": "Enter field name (e.g., email)", "fieldDescriptionPlaceholder": "Enter field description (e.g., Collect all user emails)", "pleaseEnterAllFields": "Please enter all field information", "text": "Text", "email": "Email", "phone": "Phone", "number": "Number", "date": "Date", "address": "Address", "name": "Name"}, "responseConfig": {"title": "Response Resources", "configureResources": "Configure response resources for the Agent", "configureResourcesDescription": "Select the resources that the Agent can use to respond to users", "media": "Media Resources", "url": "URL Resources", "product": "Product Resources", "noMedia": "No Media resources yet", "noUrl": "No URL resources yet", "noProduct": "No Product resources yet", "selectMedia": "Select Media", "selectUrl": "Select URL", "selectProduct": "Select Product"}, "editAgent": {"title": "Edit Agent", "subtitle": "Update Agent information and configuration", "basicInfo": "Basic Information", "agentName": "Agent Name", "agentNamePlaceholder": "Enter Agent name", "agentDescription": "Agent Description", "agentDescriptionPlaceholder": "Enter Agent description", "agentAvatar": "Avatar", "changeAvatar": "Change Avatar", "agentStatus": "Status", "active": "Active", "inactive": "Inactive", "agentLevel": "Level", "agentExp": "Experience", "agentModel": "AI Model", "agentType": "Agent Type", "saveChanges": "Save Changes", "cancelEdit": "Cancel Edit", "deleteAgent": "Delete Agent", "confirmDelete": "Are you sure you want to delete this <PERSON>?", "deleteWarning": "This action cannot be undone.", "updateSuccess": "Agent updated successfully!", "updateError": "Failed to update <PERSON>!", "deleteSuccess": "Agent deleted successfully!", "deleteError": "Failed to delete Agent!", "validation": {"nameRequired": "Agent name is required", "nameMinLength": "Agent name must be at least 2 characters", "nameMaxLength": "Agent name cannot exceed 100 characters", "descriptionMaxLength": "Description cannot exceed 500 characters"}}}}