import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination } from '@/shared/components/common';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentGrid } from '../components';
import { useGetAgents } from '../hooks/useAgent';
import { GetAgentsQueryDto, SortDirection } from '../types';
import PageWrapper from '@/shared/components/common/PageWrapper';

/**
 * Trang hiển thị danh sách AI Agents
 */
const AIAgentsPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(12);
  const [search, setSearch] = useState('');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);
  const [active] = useState<boolean | undefined>(undefined);

  // Query params cho API
  const queryParams = useMemo<GetAgentsQueryDto>(() => ({
    page,
    limit,
    search: search || undefined,
    sortBy,
    sortDirection,
    active,
  }), [page, limit, search, sortBy, sortDirection, active]);

  // Lấy danh sách agents từ API
  const {
    data: agentsResponse,
    isLoading,
    error,
    refetch
  } = useGetAgents(queryParams);

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleSortChange = useCallback((newSortBy: string, newSortDirection: SortDirection) => {
    setSortBy(newSortBy);
    setSortDirection(newSortDirection);
    setPage(1); // Reset về trang đầu khi thay đổi sort
  }, []);

  const handleAddAgent = () => {
    navigate('/ai-agents/add');
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Extract data từ API response
  const agents = agentsResponse?.result?.items || [];
  const totalItems = agentsResponse?.result?.meta?.totalItems || 0;

  // Tạo menu items cho sort options
  const sortMenuItems = useMemo(() => [
    {
      id: 'createdAt-desc',
      label: t('aiAgents.sort.newestFirst', 'Mới nhất trước'),
      icon: 'calendar-down',
      onClick: () => handleSortChange('createdAt', SortDirection.DESC),
      isActive: sortBy === 'createdAt' && sortDirection === SortDirection.DESC,
    },
    {
      id: 'createdAt-asc',
      label: t('aiAgents.sort.oldestFirst', 'Cũ nhất trước'),
      icon: 'calendar-up',
      onClick: () => handleSortChange('createdAt', SortDirection.ASC),
      isActive: sortBy === 'createdAt' && sortDirection === SortDirection.ASC,
    },
    {
      id: 'name-asc',
      label: t('aiAgents.sort.nameAZ', 'Tên A-Z'),
      icon: 'sort-alpha-down',
      onClick: () => handleSortChange('name', SortDirection.ASC),
      isActive: sortBy === 'name' && sortDirection === SortDirection.ASC,
    },
    {
      id: 'name-desc',
      label: t('aiAgents.sort.nameZA', 'Tên Z-A'),
      icon: 'sort-alpha-up',
      onClick: () => handleSortChange('name', SortDirection.DESC),
      isActive: sortBy === 'name' && sortDirection === SortDirection.DESC,
    },
  ], [sortBy, sortDirection, t, handleSortChange]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={sortMenuItems}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={sortMenuItems}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error', 'Lỗi')}
          description={t('aiAgents.list.loadError', 'Không thể tải danh sách AI Agents. Vui lòng thử lại.')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry', 'Thử lại')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <PageWrapper>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddAgent}
        items={sortMenuItems}
      />

      {agents.length > 0 ? (
        <>
          <AgentGrid agents={agents} />

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[6, 12, 24, 48]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="robot"
          title={t('aiAgents.list.noAgents', 'Không có AI Agents')}
          description={
            search
              ? t('aiAgents.list.noSearchResults', 'Không tìm thấy AI Agents phù hợp với từ khóa tìm kiếm.')
              : t('aiAgents.list.noAgentsDescription', 'Hiện tại chưa có AI Agents nào. Hãy tạo Agent đầu tiên của bạn.')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddAgent}
            >
              {t('aiAgents.list.createFirst', 'Tạo AI Agent đầu tiên')}
            </Button>
          }
        />
      )}


    </PageWrapper>
  );
};

export default AIAgentsPage;
