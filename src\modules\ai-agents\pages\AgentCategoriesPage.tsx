import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Avatar } from '@/shared/components/common';
import { Link } from 'react-router-dom';
import { aiAgents, AIAgent } from '../data/agents';

/**
 * Trang hiển thị AI Agents theo danh mục
 */
const AgentCategoriesPage: React.FC = () => {
  const { t } = useTranslation();

  // Nhóm agents theo danh mục
  const agentsByCategory = aiAgents.reduce(
    (acc, agent) => {
      // Đảm bảo category tồn tại trước khi sử dụng
      const category = agent.category || 'uncategorized';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(agent);
      return acc;
    },
    {} as Record<string, AIAgent[]>
  );

  return (
    <div>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold mb-2">
            {t('chat.aiAgents')} - {t('common.categories', 'Categories')}
          </h2>
          <p className="text-gray-500 dark:text-gray-400">{t('chat.selectAgent')}</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link to="/ai-agents" className="text-primary hover:underline">
            {t('common.viewAll', 'View All')}
          </Link>
        </div>
      </div>

      <div className="space-y-8">
        {Object.entries(agentsByCategory).map(([category, agents]) => (
          <div key={category} className="mb-8">
            <h3 className="text-lg font-semibold mb-4 capitalize">{category}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agents.map(agent => (
                <Link to={`/ai-agents/${agent.id}`} key={agent.id}>
                  <Card className="flex items-center p-4 hover:shadow-md transition-shadow">
                    <Avatar src={agent.avatar} alt={agent.name} size="md" className="mr-4" />
                    <div>
                      <h4 className="font-semibold">{agent.name}</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-1">
                        {agent.description}
                      </p>
                    </div>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AgentCategoriesPage;
