import React, { useMemo } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Loading, EmptyState, Button } from '@/shared/components/common';
import { AgentConfigurationForm } from '../components/agent-config/AgentConfigurationForm';
import { useAgentEditData } from '../hooks/useAgentEditData';

/**
 * Trang chỉnh sửa Agent - sử dụng AgentConfigurationForm giống AgentCreatePage
 */
const AgentEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();

  // Ref để track mounting time và tránh flashing
  // const mountTimeRef = useRef(Date.now()); // Unused for now
  // const [showContent, setShowContent] = React.useState(false); // Unused for now

  // Debug re-render
  console.log('🔄 AgentEditPage RENDER:', {
    timestamp: new Date().toISOString(),
    agentId: id,
    searchParams: Object.fromEntries(searchParams.entries())
  });

  // Lấy typeId từ URL params nếu có
  const typeIdFromUrl = searchParams.get('typeId');
  console.log('AgentEditPage - URL params:', { agentId: id, typeIdFromUrl });

  // Hooks để lấy dữ liệu agent từ các API riêng lẻ
  const {
    data: agentConfigData,
    isLoading: isLoadingAgent,
    error: agentError,
    agentNotFound,
    hasCriticalError,
    mode,
    // typeId, // Unused for now
    typeAgentConfig
  } = useAgentEditData(id || '', typeIdFromUrl);

  // Debug loading states
  console.log('🔄 AgentEditPage Loading States:', {
    timestamp: new Date().toISOString(),
    isLoadingAgent,
    hasAgentConfigData: !!agentConfigData,
    agentNotFound,
    hasTypeAgentConfig: !!typeAgentConfig,
    hasCriticalError,
    mode
  });

  // Xử lý khi quay lại trang chi tiết
  const handleBackToDetail = () => {
    navigate("/ai-agents");
  };

  // TRIỆT ĐỂ: Tạo một loading state duy nhất và ổn định
  const shouldShowLoading = useMemo(() => {
    // Chỉ hiển thị loading khi thực sự cần thiết
    // Không hiển thị loading nếu đã có đủ data để render form

    const hasMinimumData = agentConfigData && typeAgentConfig;
    const hasError = hasCriticalError || !id;

    // Nếu có lỗi, không hiển thị loading
    if (hasError) return false;

    // Nếu đã có đủ data minimum, không hiển thị loading
    if (hasMinimumData) return false;

    // Chỉ hiển thị loading khi thực sự đang load
    return isLoadingAgent;
  }, [agentConfigData, typeAgentConfig, hasCriticalError, id, isLoadingAgent]);

  // Hiển thị lỗi nghiêm trọng (không phải 404)
  if (hasCriticalError || !id) {
    return (
      <div className="py-6">
        <EmptyState
          icon="alert-circle"
          title={t('common.error')}
          description={agentError?.message || t('agents.errors.loadFailed')}
          actions={
            <Button variant="primary" onClick={() => navigate('/ai-agents')}>
              {t('common.backToList')}
            </Button>
          }
        />
      </div>
    );
  }

  // Hiển thị loading - chỉ một loading check duy nhất
  if (shouldShowLoading) {
    return (
        <Loading size="lg" />
      
    );
  }

  // Ensure typeAgentConfig is available before rendering
  if (!typeAgentConfig) {
    return (
        <Loading size="lg" />
    );
  }

  return (
    <AgentConfigurationForm
      mode={mode}
      agentId={id}
      initialData={agentConfigData || undefined} // Use proper type
      typeAgentConfig={typeAgentConfig}
      onSuccess={(agentId) => {
        console.log(`Agent ${mode === 'create' ? 'created' : 'updated'} successfully:`, agentId);
        if (mode === 'create') {
          // Create mode: navigate như cũ
          navigate(`/ai-agents/${agentId}`);
        } else {
          // Edit mode: không navigate, chỉ reload data
          console.log('Edit mode: staying on current page, data will be reloaded');
          // React Query sẽ tự động invalidate cache và reload data
        }
      }}
      onBack={handleBackToDetail}
      onCancel={handleBackToDetail}
      availableAgents={[]} // Có thể load từ API nếu cần
    />
  );
};

export default AgentEditPage;
