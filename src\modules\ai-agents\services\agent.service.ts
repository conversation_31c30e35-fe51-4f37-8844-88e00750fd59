import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  getSimpleAgents,
  getAgents,
  getAgentDetail,
  createAgent,
  createAgentModular,
  updateAgent,
  deleteAgent,
  toggleAgentActive,
  getAgentStatistics,
  updateAgentVectorStore,
  AgentSimpleListDto,
  AgentSimpleQueryDto,
  AgentListItemDto
} from '../api/agent.api';

import {
  AgentDetailDto,
  AgentStatisticsQueryDto,
  AgentStatisticsResponseDto,
  CreateAgentDto,
  CreateAgentModularDto,
  CreateAgentResponseDto,
  UpdateAgentDto,
  GetAgentsQueryDto,
  UpdateAgentVectorStoreDto,
} from '../types';

/**
 * Service layer cho Agent - chứa business logic
 * Theo pattern của blog module
 */

/**
 * L<PERSON>y danh sách agent đơn giản với business logic
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getSimpleAgentsWithBusinessLogic = async (
  params?: AgentSimpleQueryDto
): Promise<ApiResponse<PaginatedResult<AgentSimpleListDto>>> => {
  // Business logic có thể bao gồm:
  // - Validate params
  // - Transform data
  // - Cache logic
  // - Error handling

  return getSimpleAgents(params);
};

/**
 * Lấy danh sách agents đầy đủ với business logic
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getAgentsWithBusinessLogic = async (
  params?: GetAgentsQueryDto
): Promise<ApiResponse<PaginatedResult<AgentListItemDto>>> => {
  // Business logic có thể bao gồm:
  // - Validate params
  // - Transform data
  // - Cache logic
  // - Error handling

  return getAgents(params);
};

/**
 * Lấy chi tiết agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const getAgentDetailWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<AgentDetailDto>> => {
  // Business logic có thể bao gồm:
  // - Validate ID format
  // - Check permissions
  // - Transform response data

  return getAgentDetail(id);
};

/**
 * Tạo agent mới với business logic (Legacy)
 * @param data Dữ liệu tạo agent
 * @returns Promise với response từ API
 */
export const createAgentWithBusinessLogic = async (
  data: CreateAgentDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Transform data format
  // - Set default values
  // - Pre-processing

  // Ví dụ: Set default model config nếu không có
  const processedData = {
    ...data,
    modelConfig: {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 1000,
      ...data.modelConfig,
    },
  };

  return createAgent(processedData);
};

/**
 * Tạo agent mới với cấu trúc modular (Recommended)
 * @param data Dữ liệu tạo agent modular
 * @returns Promise với response từ API
 */
export const createAgentModularWithBusinessLogic = async (
  data: CreateAgentModularDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data theo AGENT_USER_CREATION_GUIDE.md
  // - Transform data format
  // - Set default values
  // - Pre-processing

  // Validate required fields
  if (!data.name || data.name.trim().length === 0) {
    throw new Error('Tên agent là bắt buộc');
  }

  if (data.name.length > 255) {
    throw new Error('Tên agent không được quá 255 ký tự');
  }

  if (!data.typeId || data.typeId <= 0) {
    throw new Error('Type ID không hợp lệ');
  }

  if (!data.modelConfig) {
    throw new Error('Model config là bắt buộc');
  }

  // Set default model config values
  const processedData = {
    ...data,
    modelConfig: {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 1000,
      ...data.modelConfig,
    },
  };

  return createAgentModular(processedData);
};

/**
 * Cập nhật agent với business logic
 * @param id ID của agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateAgentWithBusinessLogic = async (
  id: string,
  data: UpdateAgentDto
): Promise<ApiResponse<AgentDetailDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Transform data format
  // - Check permissions
  // - Pre-processing

  // Validate ID
  if (!id || id.trim().length === 0) {
    throw new Error('ID agent là bắt buộc');
  }

  // Validate name if provided
  if (data.name !== undefined) {
    if (data.name.trim().length === 0) {
      throw new Error('Tên agent không được để trống');
    }
    if (data.name.length > 255) {
      throw new Error('Tên agent không được quá 255 ký tự');
    }
  }

  // Process model config if provided
  const processedData = {
    ...data,
  };

  if (data.modelConfig) {
    processedData.modelConfig = {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 1000,
      ...data.modelConfig,
    };
  }

  return updateAgent(id, processedData);
};

/**
 * Xóa agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const deleteAgentWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Check dependencies
  // - Cleanup related data
  // - Audit logging

  return deleteAgent(id);
};

/**
 * Bật/tắt agent với business logic
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const toggleAgentActiveWithBusinessLogic = async (
  id: string
): Promise<ApiResponse<{ active: boolean }>> => {
  // Business logic có thể bao gồm:
  // - Check business rules
  // - Update related systems
  // - Notifications

  return toggleAgentActive(id);
};

/**
 * Lấy thống kê agent với business logic
 * @param id ID của agent
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getAgentStatisticsWithBusinessLogic = async (
  id: string,
  params?: AgentStatisticsQueryDto
): Promise<ApiResponse<AgentStatisticsResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Calculate additional metrics
  // - Apply filters
  // - Format data for display

  return getAgentStatistics(id, params);
};

/**
 * Cập nhật vector store với business logic
 * @param id ID của agent
 * @param data Dữ liệu vector store
 * @returns Promise với response từ API
 */
export const updateAgentVectorStoreWithBusinessLogic = async (
  id: string,
  data: UpdateAgentVectorStoreDto
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Validate vector store exists
  // - Check compatibility
  // - Update indexes

  return updateAgentVectorStore(id, data);
};
