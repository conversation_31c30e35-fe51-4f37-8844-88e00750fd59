import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import {
  getBasicInfo,
  updateBasicInfo,
  BasicInfoResponseDto,
  UpdateBasicInfoDto
} from '../api/basic-info.api';

/**
 * Service layer cho Basic Info - chứa business logic
 * Theo pattern của blog module
 */

/**
 * L<PERSON>y thông tin basic info với business logic
 * @param agentId ID của agent
 * @returns Promise với response từ API
 */
export const getBasicInfoWithBusinessLogic = async (
  agentId: string
): Promise<ApiResponse<BasicInfoResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate agentId format
  // - Check permissions
  // - Transform response data
  // - Cache logic

  return getBasicInfo(agentId);
};

/**
 * Cập nhật basic info với business logic
 * @param agentId ID của agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateBasicInfoWithBusinessLogic = async (
  agentId: string,
  data: UpdateBasicInfoDto
): Promise<ApiResponse<BasicInfoResponseDto & { avatarUpload?: { uploadUrl: string; publicUrl: string } }>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Transform data format
  // - Set default values
  // - Pre-processing

  // Validate model configuration
  if (data.modelConfig) {
    const processedModelConfig = {
      temperature: 0.7,
      topP: 0.9,
      topK: 40,
      maxTokens: 1000,
      ...data.modelConfig, // Override defaults với data từ user
    };

    // Validate ranges
    if (processedModelConfig.temperature < 0 || processedModelConfig.temperature > 2) {
      throw new Error('Temperature must be between 0 and 2');
    }
    if (processedModelConfig.topP < 0 || processedModelConfig.topP > 1) {
      throw new Error('Top P must be between 0 and 1');
    }
    if (processedModelConfig.topK < 1 || processedModelConfig.topK > 100) {
      throw new Error('Top K must be between 1 and 100');
    }
    if (processedModelConfig.maxTokens < 1 || processedModelConfig.maxTokens > 4000) {
      throw new Error('Max tokens must be between 1 and 4000');
    }

    data.modelConfig = processedModelConfig;
  }

  // Validate model selection logic
  const modelSelectionCount = [
    data.userModelId,
    data.systemModelId,
    data.modelFineTuneId
  ].filter(Boolean).length;

  if (modelSelectionCount > 1) {
    throw new Error('Only one model type can be selected at a time');
  }

  // Validate keyLlmId logic
  if (data.keyLlmId && !data.userModelId) {
    throw new Error('keyLlmId can only be used with userModelId');
  }

  // Validate avatar file
  if (data.avatarFile) {
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedMimeTypes.includes(data.avatarFile.mimeType)) {
      throw new Error('Invalid avatar file type. Only JPEG, PNG, GIF, and WebP are allowed');
    }
  }

  // Validate instruction length
  if (data.instruction && data.instruction.length > 5000) {
    throw new Error('Instruction must be less than 5000 characters');
  }

  return updateBasicInfo(agentId, data);
};
