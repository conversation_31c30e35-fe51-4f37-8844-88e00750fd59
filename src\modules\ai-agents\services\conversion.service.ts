import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import {
  getConversion,
  updateConversion,
  ConversionResponseDto,
  UpdateConversionDto,
  ConversionFieldDto
} from '../api/conversion.api';

/**
 * Service layer cho Conversion - chứa business logic
 */

/**
 * Lấy thông tin conversion với business logic
 * @param agentId ID của agent
 * @returns Promise với response từ API
 */
export const getConversionWithBusinessLogic = async (
  agentId: string
): Promise<ApiResponse<ConversionResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate agentId format
  // - Check permissions
  // - Transform response data

  return getConversion(agentId);
};

/**
 * Cập nhật conversion với business logic
 * @param agentId ID của agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateConversionWithBusinessLogic = async (
  agentId: string,
  data: UpdateConversionDto
): Promise<ApiResponse<ConversionResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Transform data format
  // - Set default values
  // - Pre-processing

  // Validate fields array
  if (!Array.isArray(data.fields)) {
    throw new Error('Fields must be an array');
  }

  if (data.fields.length > 50) {
    throw new Error('Maximum 50 fields allowed');
  }

  // Validate each field
  const processedFields: ConversionFieldDto[] = [];
  const fieldNames = new Set<string>();

  for (const field of data.fields) {
    // Validate required properties
    if (!field.name || typeof field.name !== 'string') {
      throw new Error('Field name is required and must be a string');
    }

    if (!field.type || typeof field.type !== 'string') {
      throw new Error('Field type is required and must be a string');
    }

    // Validate field name uniqueness
    if (fieldNames.has(field.name)) {
      throw new Error(`Duplicate field name: ${field.name}`);
    }
    fieldNames.add(field.name);

    // Validate field name format
    const nameRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/;
    if (!nameRegex.test(field.name)) {
      throw new Error(`Invalid field name: ${field.name}. Must start with letter and contain only letters, numbers, and underscores`);
    }

    // Validate field type
    const allowedTypes = ['string', 'number', 'boolean', 'array', 'object'];
    if (!allowedTypes.includes(field.type)) {
      throw new Error(`Invalid field type: ${field.type}. Must be one of: ${allowedTypes.join(', ')}`);
    }

    // Validate description length
    if (field.description && field.description.length > 500) {
      throw new Error(`Field description for ${field.name} must be less than 500 characters`);
    }

    // Validate options for enum fields
    if (field.options) {
      if (!Array.isArray(field.options)) {
        throw new Error(`Field options for ${field.name} must be an array`);
      }
      if (field.options.length > 20) {
        throw new Error(`Maximum 20 options allowed for field ${field.name}`);
      }
      // Remove duplicates and empty strings
      field.options = [...new Set(field.options.filter(option => option.trim().length > 0))];
    }

    // Validate validation rules
    if (field.validation) {
      if (field.type === 'number') {
        if (field.validation.min !== undefined && field.validation.max !== undefined) {
          if (field.validation.min >= field.validation.max) {
            throw new Error(`Invalid validation range for field ${field.name}: min must be less than max`);
          }
        }
      }
      
      if (field.validation.pattern) {
        try {
          new RegExp(field.validation.pattern);
        } catch {
          throw new Error(`Invalid regex pattern for field ${field.name}: ${field.validation.pattern}`);
        }
      }
    }

    processedFields.push({
      name: field.name,
      type: field.type,
      description: field.description,
      required: field.required || false,
      options: field.options,
      validation: field.validation
    });
  }

  // Update data with processed fields
  data.fields = processedFields;

  return updateConversion(agentId, data);
};
