import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import {
  addAgentMedia,
  addAgentProduct,
  addAgentUrl,
  AgentMediaDto,
  AgentProductDto,
  AgentUrlDto,
  CreateMediaDto,
  CreateProductDto,
  CreateUrlDto,
  getResources,
  removeAgentMedia,
  removeAgentProduct,
  removeAgentUrl,
  ResourceQueryDto,
  ResourceResponseDto
} from '../api/resource.api';

/**
 * Service layer cho Resource - chứa business logic
 */

/**
 * Lấy tổng quan resources với business logic
 * @param agentId ID của agent
 * @param params Query params
 * @returns Promise với response từ API
 */
export const getResourcesWithBusinessLogic = async (
  agentId: string,
  params?: ResourceQueryDto
): Promise<ApiResponse<ResourceResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate agentId format
  // - Check permissions
  // - Transform response data

  return getResources(agentId, params);
};

/**
 * Thêm media với business logic
 * @param agentId ID của agent
 * @param data Dữ liệu media
 * @returns Promise với response từ API
 */
export const addAgentMediaWithBusinessLogic = async (
  agentId: string,
  data: CreateMediaDto
): Promise<ApiResponse<AgentMediaDto & { uploadUrl: string; publicUrl: string }>> => {
  // Business logic có thể bao gồm:
  // - Validate file type
  // - Check file size limits
  // - Generate unique file names

  // Validate file name
  if (!data.fileName || typeof data.fileName !== 'string') {
    throw new Error('File name is required');
  }

  // Validate MIME type
  const allowedMimeTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/webm', 'video/ogg',
    'audio/mp3', 'audio/wav', 'audio/ogg',
    'application/pdf', 'text/plain', 'text/csv',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];

  if (!allowedMimeTypes.includes(data.mimeType)) {
    throw new Error('Unsupported file type');
  }

  // Validate file size (50MB limit)
  const maxSize = 50 * 1024 * 1024; // 50MB in bytes
  if (data.size > maxSize) {
    throw new Error('File size exceeds 50MB limit');
  }

  // Validate file name length
  if (data.fileName.length > 255) {
    throw new Error('File name must be less than 255 characters');
  }

  return addAgentMedia(agentId, data);
};

/**
 * Thêm URL với business logic
 * @param agentId ID của agent
 * @param data Dữ liệu URL
 * @returns Promise với response từ API
 */
export const addAgentUrlWithBusinessLogic = async (
  agentId: string,
  data: CreateUrlDto
): Promise<ApiResponse<AgentUrlDto>> => {
  // Business logic có thể bao gồm:
  // - Validate URL format
  // - Check URL accessibility
  // - Extract metadata

  // Validate URL format
  if (!data.url || typeof data.url !== 'string') {
    throw new Error('URL is required');
  }

  try {
    new URL(data.url);
  } catch {
    throw new Error('Invalid URL format');
  }

  // Validate URL protocol
  const url = new URL(data.url);
  if (!['http:', 'https:'].includes(url.protocol)) {
    throw new Error('Only HTTP and HTTPS URLs are allowed');
  }

  // Validate title length
  if (data.title && data.title.length > 200) {
    throw new Error('Title must be less than 200 characters');
  }

  // Validate description length
  if (data.description && data.description.length > 1000) {
    throw new Error('Description must be less than 1000 characters');
  }

  return addAgentUrl(agentId, data);
};

/**
 * Thêm product với business logic
 * @param agentId ID của agent
 * @param data Dữ liệu product
 * @returns Promise với response từ API
 */
export const addAgentProductWithBusinessLogic = async (
  agentId: string,
  data: CreateProductDto
): Promise<ApiResponse<AgentProductDto>> => {
  // Business logic có thể bao gồm:
  // - Validate product ID
  // - Check product exists
  // - Validate permissions

  // Validate product ID
  if (!data.productId || typeof data.productId !== 'string') {
    throw new Error('Product ID is required');
  }

  // Validate product ID format (assuming UUID)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(data.productId)) {
    throw new Error('Invalid product ID format');
  }

  return addAgentProduct(agentId, data);
};

/**
 * Xóa media với business logic
 * @param agentId ID của agent
 * @param mediaId ID của media
 * @returns Promise với response từ API
 */
export const removeAgentMediaWithBusinessLogic = async (
  agentId: string,
  mediaId: string
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Check permissions
  // - Cleanup related data
  // - Delete from storage

  return removeAgentMedia(agentId, mediaId);
};

/**
 * Xóa URL với business logic
 * @param agentId ID của agent
 * @param urlId ID của URL
 * @returns Promise với response từ API
 */
export const removeAgentUrlWithBusinessLogic = async (
  agentId: string,
  urlId: string
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Check permissions
  // - Cleanup related data

  return removeAgentUrl(agentId, urlId);
};

/**
 * Xóa product với business logic
 * @param agentId ID của agent
 * @param productId ID của product
 * @returns Promise với response từ API
 */
export const removeAgentProductWithBusinessLogic = async (
  agentId: string,
  productId: string
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Check permissions
  // - Cleanup related data

  return removeAgentProduct(agentId, productId);
};
