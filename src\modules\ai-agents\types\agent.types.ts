/**
 * Types liên quan đến Agent
 */

// Giới tính
export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

// Trình độ học vấn
export enum EducationLevel {
  HIGH_SCHOOL = 'high_school',
  COLLEGE = 'college',
  BACHELOR = 'bachelor',
  MASTER = 'master',
  PHD = 'phd',
}

// Thông tin cơ bản của Agent
export interface AgentProfile {
  id: string;
  name: string;
  birthDate?: string;
  gender?: Gender;
  language?: string;
  educationLevel?: EducationLevel;
  country?: string;
  position?: string;
  skills: string[];
  personality: string[];
  avatar?: string;
}

// Thông tin tích hợp Facebook
export interface FacebookPage {
  id: string;
  name: string;
  imageUrl?: string;
  isConnected: boolean;
}

// Thông tin tích hợp Website
export interface Website {
  id: string;
  url: string;
  name: string;
  favicon?: string;
  isConnected: boolean;
}

/**
 * <PERSON><PERSON><PERSON> dữ liệu cho giá trị tham số chiến lượ<PERSON> và cấu hình
 */
export type ParameterValue = string | number | boolean;

// Kiểu giá trị tham số cho input
export type InputParameterValue = string | number;

// Kiểu giá trị chỉ cho số
export type NumberParameterValue = number;

// Thông tin chiến lược
export interface Strategy {
  id: string;
  name: string;
  description?: string;
  parameters: StrategyParameter[];
}

export interface StrategyParameter {
  id: string;
  name: string;
  type: 'number' | 'string' | 'boolean' | 'select';
  value: ParameterValue;
  options?: { label: string; value: ParameterValue }[];
  min?: number;
  max?: number;
  step?: number;
}

// Thông tin chuyển đổi
export interface ConversionField {
  id: string;
  name: string;
  type: 'email' | 'phone' | 'address' | 'name' | 'custom';
  isRequired: boolean;
  customLabel?: string;
}

// Thông tin vector
export interface Vector {
  id: string;
  name: string;
  description?: string;
  configuration: Record<string, ParameterValue>;
}

// Thông tin tài nguyên
export interface Resource {
  id: string;
  name: string;
  type: 'document' | 'image' | 'video' | 'audio';
  url?: string;
  size?: number;
  isSelected: boolean;
}

// Thông tin module
export interface Module {
  id: string;
  name: string;
  description?: string;
  isEnabled: boolean;
  configuration: ModuleConfiguration;
}

export interface ModuleConfiguration {
  maxTokens?: number;
  topP?: number;
  topK?: number;
  temperature?: number;
  [key: string]: ParameterValue | undefined;
}

// Thông tin chi tiết Agent
export interface AgentDetail {
  profile: AgentProfile;
  facebookPages: FacebookPage[];
  websites: Website[];
  strategy?: Strategy;
  conversionFields: ConversionField[];
  vector?: Vector;
  resources: Resource[];
  modules: Module[];
}

// Tham số truy vấn
export interface AgentQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}
