/**
 * Interface cho một trường dữ liệu chuyển đổi
 */
export interface ConvertField {
    id: string;
    name: string;
    description: string;
    enabled: boolean; // UI state - để hiển thị checkbox
    required: boolean;
    type: 'string' | 'number' | 'boolean' | 'array_number' | 'array_string' | 'enum';
}

/**
 * Interface cho dữ liệu chuyển đổi
 */
export interface ConvertData {
    fields: ConvertField[];
}
