/**
 * Interface cho một item tích hợp
 */
export interface IntegrationItem {
    id: string;
    name: string;
    icon?: string | undefined;
    type: 'facebook' | 'website';
    url?: string | undefined;
    imageUrl?: string | undefined;
    category?: string | undefined;
    followers?: number | undefined;
    isConnected?: boolean | undefined;
    status?: 'active' | 'pending' | 'error' | undefined;
}

/**
 * Interface cho dữ liệu tích hợp
 */
export interface IntegrationsData {
    integrations: IntegrationItem[];
}
