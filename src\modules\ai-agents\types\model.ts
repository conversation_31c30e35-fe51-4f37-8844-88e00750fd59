import { TypeProviderEnum } from './index';

/**
 * Interface cho dữ liệu cấu hình model
 */
export interface ModelConfigData {
    provider: TypeProviderEnum | 'redai'; // Hỗ trợ RedAI provider mặc định
    providerId?: string; // ID của user provider (nếu không phải RedAI)
    keyLlmId?: string; // ID của key LLM được chọn
    modelId: string;
    vectorStore: string;
    maxTokens: number;
    temperature: number;
    topP: number;
    topK: number;
    instruction?: string;
    showAdvancedConfig?: boolean; // Flag để xác định có gửi modelConfig không
}
