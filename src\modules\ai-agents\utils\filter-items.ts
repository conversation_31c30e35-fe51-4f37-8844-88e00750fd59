import { ModernMenuItem } from '@/shared/components/common/ModernMenu';
import { SortDirection, TypeAgentSortBy } from '../types';

/**
 * Interface cho các handler của filter
 */
export interface TypeAgentFilterHandlers {
  onAgentTypeChange: (isSystem: boolean | undefined) => void;
  onSortByChange: (sortBy: TypeAgentSortBy) => void;
  onSortDirectionChange: (direction: SortDirection) => void;
}

/**
 * Interface cho trạng thái hiện tại của filter
 */
export interface TypeAgentFilterState {
  isSystem?: boolean;
  sortBy: TypeAgentSortBy;
  sortDirection: SortDirection;
}

/**
 * Tạo filter items cho MenuIconBar dành cho Type Agent
 */
export const createTypeAgentFilterItems = (
  handlers: TypeAgentFilterHandlers,
  state: TypeAgentFilterState
): ModernMenuItem[] => {
  const { onAgentTypeChange, onSortByChange, onSortDirectionChange } = handlers;
  const { isSystem, sortBy, sortDirection } = state;

  return [
    // Agent Type Section
    {
      id: 'agent-type-header',
      label: 'Loại Agent',
      icon: 'users',
      onClick: () => { },
      disabled: true
    },
    {
      id: 'system-agents',
      label: isSystem === true ? '✓ System Agents' : 'System Agents',
      onClick: () => onAgentTypeChange(true)
    },
    {
      id: 'user-agents',
      label: isSystem === false ? '✓ User Agents' : 'User Agents',
      onClick: () => onAgentTypeChange(false)
    },
    {
      id: 'all-agents',
      label: isSystem === undefined ? '✓ Tất cả loại' : 'Tất cả loại',
      onClick: () => onAgentTypeChange(undefined)
    },
    {
      id: 'divider-1',
      divider: true
    },

    // Sort By Section
    {
      id: 'sort-header',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
      disabled: true
    },
    {
      id: 'sort-name',
      label: sortBy === TypeAgentSortBy.NAME ? '✓ Tên' : 'Tên',
      onClick: () => onSortByChange(TypeAgentSortBy.NAME)
    },
    {
      id: 'sort-date',
      label: sortBy === TypeAgentSortBy.CREATED_AT ? '✓ Ngày tạo' : 'Ngày tạo',
      onClick: () => onSortByChange(TypeAgentSortBy.CREATED_AT)
    },
    {
      id: 'divider-2',
      divider: true
    },

    // Sort Direction Section
    {
      id: 'order-header',
      label: 'Thứ tự',
      icon: 'arrow-up-down',
      onClick: () => { },
      disabled: true
    },
    {
      id: 'order-asc',
      label: sortDirection === SortDirection.ASC ? '✓ Tăng dần (A-Z, Cũ-Mới)' : 'Tăng dần (A-Z, Cũ-Mới)',
      onClick: () => onSortDirectionChange(SortDirection.ASC)
    },
    {
      id: 'order-desc',
      label: sortDirection === SortDirection.DESC ? '✓ Giảm dần (Z-A, Mới-Cũ)' : 'Giảm dần (Z-A, Mới-Cũ)',
      onClick: () => onSortDirectionChange(SortDirection.DESC)
    },
    {
      id: 'divider-3',
      divider: true
    },
    {
      id: 'reset-all-filters',
      label: 'Đặt lại bộ lọc',
      icon: 'refresh-cw',
      onClick: () => {
        onAgentTypeChange(undefined); // Reset về tất cả
        onSortByChange(TypeAgentSortBy.CREATED_AT); // Mặc định sort by date
        onSortDirectionChange(SortDirection.DESC); // Mặc định desc
      }
    }
  ];
};

/**
 * Tạo filter items đơn giản cho MenuIconBar
 */
export const createSimpleTypeAgentFilterItems = (
  handlers: TypeAgentFilterHandlers,
  state: TypeAgentFilterState
): ModernMenuItem[] => {
  const { onAgentTypeChange, onSortByChange, onSortDirectionChange } = handlers;
  const { isSystem, sortBy, sortDirection } = state;

  return [
    // Quick filters
    {
      id: 'system-agents',
      label: 'System Agents',
      icon: isSystem === true ? 'check-circle' : 'shield',
      onClick: () => onAgentTypeChange(true)
    },
    {
      id: 'user-agents',
      label: 'User Agents',
      icon: isSystem === false ? 'check-circle' : 'user',
      onClick: () => onAgentTypeChange(false)
    },
    {
      id: 'divider-1',
      divider: true
    },
    {
      id: 'sort-name',
      label: 'Sắp xếp theo tên',
      icon: sortBy === TypeAgentSortBy.NAME ? 'check-square' : 'sort-alpha',
      onClick: () => onSortByChange(TypeAgentSortBy.NAME)
    },
    {
      id: 'sort-date',
      label: 'Sắp xếp theo ngày',
      icon: sortBy === TypeAgentSortBy.CREATED_AT ? 'check-square' : 'calendar',
      onClick: () => onSortByChange(TypeAgentSortBy.CREATED_AT)
    },
    {
      id: 'divider-2',
      divider: true
    },
    {
      id: 'toggle-order',
      label: sortDirection === SortDirection.ASC ? 'Đổi sang giảm dần' : 'Đổi sang tăng dần',
      icon: sortDirection === SortDirection.ASC ? 'arrow-down' : 'arrow-up',
      onClick: () => onSortDirectionChange(
        sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC
      )
    }
  ];
};

/**
 * Tạo filter items với preset options
 */
export const createPresetTypeAgentFilterItems = (
  handlers: TypeAgentFilterHandlers
): ModernMenuItem[] => {
  const { onAgentTypeChange, onSortByChange, onSortDirectionChange } = handlers;

  return [
    // Preset filters
    {
      id: 'preset-system-name-asc',
      label: 'System Agents (A-Z)',
      icon: 'shield',
      onClick: () => {
        onAgentTypeChange(true);
        onSortByChange(TypeAgentSortBy.NAME);
        onSortDirectionChange(SortDirection.ASC);
      }
    },
    {
      id: 'preset-system-date-desc',
      label: 'System Agents (Mới nhất)',
      icon: 'shield',
      onClick: () => {
        onAgentTypeChange(true);
        onSortByChange(TypeAgentSortBy.CREATED_AT);
        onSortDirectionChange(SortDirection.DESC);
      }
    },
    {
      id: 'preset-user-name-asc',
      label: 'User Agents (A-Z)',
      icon: 'user',
      onClick: () => {
        onAgentTypeChange(false);
        onSortByChange(TypeAgentSortBy.NAME);
        onSortDirectionChange(SortDirection.ASC);
      }
    },
    {
      id: 'preset-user-date-desc',
      label: 'User Agents (Mới nhất)',
      icon: 'user',
      onClick: () => {
        onAgentTypeChange(false);
        onSortByChange(TypeAgentSortBy.CREATED_AT);
        onSortDirectionChange(SortDirection.DESC);
      }
    },
    {
      id: 'divider-1',
      divider: true
    },
    {
      id: 'reset-filters',
      label: 'Đặt lại bộ lọc',
      icon: 'refresh-cw',
      onClick: () => {
        onAgentTypeChange(true); // Default to system agents
        onSortByChange(TypeAgentSortBy.CREATED_AT);
        onSortDirectionChange(SortDirection.DESC);
      }
    }
  ];
};

/**
 * Utility function để tạo filter state từ component state
 */
export const createFilterState = (
  isSystem: boolean | undefined,
  sortBy: TypeAgentSortBy,
  sortDirection: SortDirection
): TypeAgentFilterState => ({
  isSystem,
  sortBy,
  sortDirection
});

/**
 * Utility function để tạo filter handlers
 */
export const createFilterHandlers = (
  setIsSystem: (value: boolean | undefined) => void,
  setSortBy: (value: TypeAgentSortBy) => void,
  setSortDirection: (value: SortDirection) => void,
  setPage: (value: number) => void
): TypeAgentFilterHandlers => ({
  onAgentTypeChange: (isSystem) => {
    setIsSystem(isSystem);
    setPage(1);
  },
  onSortByChange: (sortBy) => {
    setSortBy(sortBy);
    setPage(1);
  },
  onSortDirectionChange: (direction) => {
    setSortDirection(direction);
    setPage(1);
  }
});
