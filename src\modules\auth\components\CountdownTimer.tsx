import React, { useState, useEffect } from 'react';
import { Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

interface CountdownTimerProps {
  /**
   * Thời điểm hết hạn (timestamp)
   */
  expiresAt: number;

  /**
   * Callback khi hết thời gian
   */
  onExpire?: () => void;

  /**
   * Class name cho component
   */
  className?: string;
}

/**
 * Component hiển thị đồng hồ đếm ngược thời gian còn lại
 */
const CountdownTimer: React.FC<CountdownTimerProps> = ({ expiresAt, onExpire, className }) => {
  const { t } = useTranslation('auth');
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isExpired, setIsExpired] = useState<boolean>(false);

  useEffect(() => {
    console.log('CountdownTimer mounted with expiresAt:', expiresAt, 'type:', typeof expiresAt);

    // Kiểm tra xem expiresAt có phải là số hợp lệ không
    if (!expiresAt || isNaN(expiresAt)) {
      console.error('CountdownTimer: expiresAt is not a valid number:', expiresAt);
      return;
    }

    // Tính toán thời gian còn lại ban đầu
    const calculateTimeLeft = () => {
      const now = Date.now();
      const diff = expiresAt - now;

      console.log('CountdownTimer calculating time left:', {
        now,
        expiresAt,
        diff,
        formattedExpiresAt: new Date(expiresAt).toLocaleString(),
        formattedNow: new Date(now).toLocaleString(),
      });

      // Nếu đã hết hạn
      if (diff <= 0) {
        setTimeLeft(0);
        setIsExpired(true);
        if (onExpire) {
          onExpire();
        }
        return;
      }

      // Còn thời gian
      setTimeLeft(diff);
      setIsExpired(false);
    };

    // Tính toán thời gian còn lại ngay lập tức
    calculateTimeLeft();

    // Cập nhật mỗi giây
    const timer = setInterval(() => {
      calculateTimeLeft();
    }, 1000);

    // Cleanup khi component unmount
    return () => clearInterval(timer);
  }, [expiresAt, onExpire]);

  // Format thời gian còn lại
  const formatTimeLeft = () => {
    if (isExpired) {
      return t('time.expired', 'Mã OTP đã hết hạn');
    }

    // Chuyển đổi từ mili giây sang giây
    const totalSeconds = Math.floor(timeLeft / 1000);

    // Tính toán ngày, giờ, phút, giây
    const days = Math.floor(totalSeconds / (24 * 60 * 60));
    const hours = Math.floor((totalSeconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
    const seconds = totalSeconds % 60;

    // Format chuỗi thời gian dựa trên các đơn vị có giá trị
    let formattedTime = '';

    if (days > 0) {
      formattedTime += `${days}${t('time.days')} `;
    }

    if (days > 0 || hours > 0) {
      formattedTime += `${hours}${t('time.hours')}:`;
    }

    // Phút luôn hiển thị, thêm số 0 đứng trước nếu < 10 và có giờ
    if ((days > 0 || hours > 0) && minutes < 10) {
      formattedTime += `0${minutes}:`;
    } else {
      formattedTime += `${minutes}:`;
    }

    // Giây luôn hiển thị với số 0 đứng trước nếu < 10
    formattedTime += `${seconds < 10 ? '0' : ''}${seconds}`;

    return formattedTime;
  };

  return (
    <Typography variant="body2" className={className} color={isExpired ? 'error' : 'muted'}>
      {isExpired ? (
        t('time.expired', 'Mã OTP đã hết hạn')
      ) : (
        <span className="flex items-center">
          <span className="mr-1">{t('otpExpiresIn.prefix', 'Mã OTP hết hạn sau:')}</span>
          <span className="text-error font-semibold">{formatTimeLeft()}</span>
        </span>
      )}
    </Typography>
  );
};

export default CountdownTimer;
