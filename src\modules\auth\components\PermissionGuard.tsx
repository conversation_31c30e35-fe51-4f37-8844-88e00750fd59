import React, { ReactNode } from 'react';
import { usePermission } from '../hooks/usePermission';
import { Permission } from '../types/permission.types';

interface PermissionGuardProps {
  /**
   * Quyền cần kiểm tra
   */
  permission?: Permission | string;

  /**
   * <PERSON>h sách quyền cần kiểm tra (kiểm tra OR - có ít nhất một quyền)
   */
  anyPermissions?: (Permission | string)[];

  /**
   * <PERSON>h sách quyền cần kiểm tra (kiểm tra AND - có tất cả các quyền)
   */
  allPermissions?: (Permission | string)[];

  /**
   * Module cần kiểm tra quyền truy cập
   */
  module?: string;

  /**
   * Hành động cần kiểm tra trong module
   */
  action?: string;

  /**
   * Nội dung hiển thị khi có quyền
   */
  children: ReactNode;

  /**
   * Nội dung hiển thị khi không c<PERSON> quyền (mặc định: null)
   */
  fallback?: ReactNode;
}

/**
 * Component bảo vệ nội dung dựa trên quyền hạn của người dùng
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  anyPermissions,
  allPermissions,
  module,
  action,
  children,
  fallback = null,
}) => {
  const { can, canAny, canAll, canAccessModule, canPerformAction } = usePermission();

  // Kiểm tra quyền dựa trên các prop được cung cấp
  let hasRequiredPermission = true;

  if (permission) {
    hasRequiredPermission = can(permission);
  }

  if (hasRequiredPermission && anyPermissions && anyPermissions.length > 0) {
    hasRequiredPermission = canAny(anyPermissions);
  }

  if (hasRequiredPermission && allPermissions && allPermissions.length > 0) {
    hasRequiredPermission = canAll(allPermissions);
  }

  if (hasRequiredPermission && module && !action) {
    hasRequiredPermission = canAccessModule(module);
  }

  if (hasRequiredPermission && module && action) {
    hasRequiredPermission = canPerformAction(module, action);
  }

  // Hiển thị nội dung dựa trên kết quả kiểm tra quyền
  return hasRequiredPermission ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGuard;
