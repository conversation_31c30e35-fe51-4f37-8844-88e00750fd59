# Hướng dẫn sử dụng hệ thống phân quyền

Hệ thống phân quyền trong ứng dụng được xây dựng dựa trên enum `Permission` từ backend. <PERSON>ệ thống này cung cấp các công cụ để kiểm tra và bảo vệ các component, route dựa trên quyền hạn của người dùng.

## Cấu trúc

- `Permission` enum: Đ<PERSON>nh nghĩa các quyền hạn trong hệ thống
- `usePermission` hook: Cung cấp các hàm kiểm tra quyền hạn
- `PermissionGuard` component: Bảo vệ nội dung dựa trên quyền hạn
- `withPermission` HOC: Bảo vệ route dựa trên quyền hạn

## Sử dụng `usePermission` hook

```tsx
import { usePermission } from '@/modules/auth/hooks/usePermission';
import { Permission } from '@/modules/auth/types/permission.types';

const MyComponent = () => {
  const { can, canView, canCreate, canUpdate, canDelete } = usePermission();

  // Kiểm tra quyền cụ thể
  const canViewUsers = can(Permission.USER_VIEW);

  // Kiểm tra quyền trong module
  const canViewBlogs = canView('blog');
  const canCreateBlogs = canCreate('blog');
  const canUpdateBlogs = canUpdate('blog');
  const canDeleteBlogs = canDelete('blog');

  return (
    <div>
      {canViewUsers && <div>User list...</div>}
      {canCreateBlogs && <button>Create Blog</button>}
    </div>
  );
};
```

## Sử dụng `PermissionGuard` component

```tsx
import { PermissionGuard } from '@/modules/auth/components/permission';
import { Permission } from '@/modules/auth/types/permission.types';

const MyComponent = () => {
  return (
    <div>
      {/* Kiểm tra một quyền cụ thể */}
      <PermissionGuard permission={Permission.USER_VIEW}>
        <div>User list...</div>
      </PermissionGuard>

      {/* Kiểm tra ít nhất một trong các quyền */}
      <PermissionGuard anyPermissions={[Permission.BLOG_CREATE, Permission.BLOG_UPDATE]}>
        <button>Manage Blog</button>
      </PermissionGuard>

      {/* Kiểm tra tất cả các quyền */}
      <PermissionGuard allPermissions={[Permission.BLOG_VIEW, Permission.BLOG_UPDATE]}>
        <button>View and Edit Blog</button>
      </PermissionGuard>

      {/* Kiểm tra quyền trong module */}
      <PermissionGuard module="blog" action="create">
        <button>Create Blog</button>
      </PermissionGuard>

      {/* Với fallback */}
      <PermissionGuard
        permission={Permission.INVOICE_EXPORT}
        fallback={<div>You don't have permission to export invoices</div>}
      >
        <button>Export Invoice</button>
      </PermissionGuard>
    </div>
  );
};
```

## Sử dụng `withPermission` HOC

```tsx
import { withPermission } from '@/modules/auth/components/permission';
import { Permission } from '@/modules/auth/types/permission.types';

const UserManagementPage = () => {
  return <div>User Management Page</div>;
};

// Bảo vệ route với một quyền cụ thể
export default withPermission(UserManagementPage, {
  permission: Permission.USER_VIEW,
  redirectTo: '/dashboard', // Tùy chọn, mặc định là '/unauthorized'
});

// Hoặc với nhiều quyền
export default withPermission(UserManagementPage, {
  anyPermissions: [Permission.USER_VIEW, Permission.USER_CREATE],
});

// Hoặc với module và action
export default withPermission(UserManagementPage, {
  module: 'user',
  action: 'view',
});
```

## Sử dụng trong React Router

```tsx
import { Route } from 'react-router-dom';
import { withPermission } from '@/modules/auth/components/permission';
import { Permission } from '@/modules/auth/types/permission.types';

import UserManagementPage from './pages/UserManagementPage';
import BlogManagementPage from './pages/BlogManagementPage';

// Bảo vệ route với HOC
const ProtectedUserManagementPage = withPermission(UserManagementPage, {
  permission: Permission.USER_VIEW,
});

const ProtectedBlogManagementPage = withPermission(BlogManagementPage, {
  module: 'blog',
  action: 'view',
});

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/users" element={<ProtectedUserManagementPage />} />
      <Route path="/blogs" element={<ProtectedBlogManagementPage />} />
    </Routes>
  );
};
```

## Lưu ý

- Quyền hạn được lấy từ thông tin người dùng trong Redux store
- Nếu người dùng không có quyền, component sẽ không hiển thị hoặc route sẽ chuyển hướng
- Các quyền được định nghĩa trong enum `Permission` phải khớp với backend
