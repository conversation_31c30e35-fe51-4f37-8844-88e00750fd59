import { ComponentType } from 'react';
import { Navigate } from 'react-router-dom';
import { usePermission } from '../hooks/usePermission';
import { Permission } from '../types/permission.types';

interface WithPermissionOptions {
  /**
   * Quyền cần kiểm tra
   */
  permission?: Permission | string;

  /**
   * <PERSON>h sách quyền cần kiểm tra (kiểm tra OR - có ít nhất một quyền)
   */
  anyPermissions?: (Permission | string)[];

  /**
   * Danh sách quyền cần kiểm tra (kiểm tra AND - c<PERSON> tất cả các quyền)
   */
  allPermissions?: (Permission | string)[];

  /**
   * Module cần kiểm tra quyền truy cập
   */
  module?: string;

  /**
   * Hành động cần kiểm tra trong module
   */
  action?: string;

  /**
   * Đường dẫn chuyển hướng khi không có quyền (mặc định: '/unauthorized')
   */
  redirectTo?: string;
}

/**
 * HOC bảo vệ component dựa trên quyền hạn của người dùng
 * @param Component Component cần bảo vệ
 * @param options Tùy chọn kiểm tra quyền
 * @returns Component đã được bảo vệ
 */
export const withPermission = <P extends object>(
  Component: ComponentType<P>,
  options: WithPermissionOptions
) => {
  const {
    permission,
    anyPermissions,
    allPermissions,
    module,
    action,
    redirectTo = '/unauthorized',
  } = options;

  const WithPermissionComponent = (props: P) => {
    const { can, canAny, canAll, canAccessModule, canPerformAction } = usePermission();

    // Kiểm tra quyền dựa trên các options được cung cấp
    let hasRequiredPermission = true;

    if (permission) {
      hasRequiredPermission = can(permission);
    }

    if (hasRequiredPermission && anyPermissions && anyPermissions.length > 0) {
      hasRequiredPermission = canAny(anyPermissions);
    }

    if (hasRequiredPermission && allPermissions && allPermissions.length > 0) {
      hasRequiredPermission = canAll(allPermissions);
    }

    if (hasRequiredPermission && module && !action) {
      hasRequiredPermission = canAccessModule(module);
    }

    if (hasRequiredPermission && module && action) {
      hasRequiredPermission = canPerformAction(module, action);
    }

    // Chuyển hướng nếu không có quyền
    if (!hasRequiredPermission) {
      return <Navigate to={redirectTo} replace />;
    }

    // Render component nếu có quyền
    return <Component {...props} />;
  };

  // Đặt tên cho component để dễ debug
  const displayName = Component.displayName || Component.name || 'Component';
  WithPermissionComponent.displayName = `withPermission(${displayName})`;

  return WithPermissionComponent;
};
