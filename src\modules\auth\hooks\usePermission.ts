import { useAuthCommon } from '@/shared/hooks';
import { Permission } from '../types/permission.types';
import {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  hasModulePermission,
  hasModuleActionPermission,
  filterPermissionsByModule,
} from '../utils/permission.utils';

/**
 * Hook cung cấp các hàm kiểm tra quyền hạn của người dùng hiện tại
 */
export const usePermission = () => {
  const { user } = useAuthCommon();
  const userPermissions = user?.permissions || [];

  /**
   * Kiểm tra xem người dùng có quyền cụ thể không
   * @param permission Quyền cần kiểm tra
   * @returns true nếu người dùng có quyền, false nếu không
   */
  const can = (permission: Permission | string): boolean => {
    return hasPermission(userPermissions, permission);
  };

  /**
   * Kiểm tra xem người dùng có ít nhất một trong các quyền được yêu cầu không
   * @param permissions Mảng quyền cần kiểm tra
   * @returns true nếu người dùng có ít nhất một quyền, false nếu không
   */
  const canAny = (permissions: (Permission | string)[]): boolean => {
    return hasAnyPermission(userPermissions, permissions);
  };

  /**
   * Kiểm tra xem người dùng có tất cả các quyền được yêu cầu không
   * @param permissions Mảng quyền cần kiểm tra
   * @returns true nếu người dùng có tất cả các quyền, false nếu không
   */
  const canAll = (permissions: (Permission | string)[]): boolean => {
    return hasAllPermissions(userPermissions, permissions);
  };

  /**
   * Kiểm tra xem người dùng có quyền trong một module cụ thể không
   * @param module Tên module (ví dụ: 'user', 'point', 'blog')
   * @returns true nếu người dùng có ít nhất một quyền trong module, false nếu không
   */
  const canAccessModule = (module: string): boolean => {
    return hasModulePermission(userPermissions, module);
  };

  /**
   * Kiểm tra xem người dùng có quyền thực hiện một hành động cụ thể trong module không
   * @param module Tên module (ví dụ: 'user', 'point', 'blog')
   * @param action Tên hành động (ví dụ: 'create', 'view', 'update', 'delete')
   * @returns true nếu người dùng có quyền, false nếu không
   */
  const canPerformAction = (module: string, action: string): boolean => {
    return hasModuleActionPermission(userPermissions, module, action);
  };

  /**
   * Lọc danh sách quyền của người dùng theo module
   * @param module Tên module (ví dụ: 'user', 'point', 'blog')
   * @returns Mảng các quyền thuộc module
   */
  const getModulePermissions = (module: string): string[] => {
    return filterPermissionsByModule(userPermissions, module);
  };

  /**
   * Kiểm tra xem người dùng có quyền xem trong một module cụ thể không
   * @param module Tên module (ví dụ: 'user', 'point', 'blog')
   * @returns true nếu người dùng có quyền xem, false nếu không
   */
  const canView = (module: string): boolean => {
    return hasModuleActionPermission(userPermissions, module, 'view');
  };

  /**
   * Kiểm tra xem người dùng có quyền tạo trong một module cụ thể không
   * @param module Tên module (ví dụ: 'user', 'point', 'blog')
   * @returns true nếu người dùng có quyền tạo, false nếu không
   */
  const canCreate = (module: string): boolean => {
    return hasModuleActionPermission(userPermissions, module, 'create');
  };

  /**
   * Kiểm tra xem người dùng có quyền cập nhật trong một module cụ thể không
   * @param module Tên module (ví dụ: 'user', 'point', 'blog')
   * @returns true nếu người dùng có quyền cập nhật, false nếu không
   */
  const canUpdate = (module: string): boolean => {
    return hasModuleActionPermission(userPermissions, module, 'update');
  };

  /**
   * Kiểm tra xem người dùng có quyền xóa trong một module cụ thể không
   * @param module Tên module (ví dụ: 'user', 'point', 'blog')
   * @returns true nếu người dùng có quyền xóa, false nếu không
   */
  const canDelete = (module: string): boolean => {
    return hasModuleActionPermission(userPermissions, module, 'delete');
  };

  return {
    can,
    canAny,
    canAll,
    canAccessModule,
    canPerformAction,
    getModulePermissions,
    canView,
    canCreate,
    canUpdate,
    canDelete,
    userPermissions,
  };
};
