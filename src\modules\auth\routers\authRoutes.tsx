import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components';
import AuthLayout from '@/shared/layouts/AuthLayout';
import i18n from '@/lib/i18n';

// Import User Auth pages with lazy loading
const LoginPage = lazy(() => import('@/modules/auth/pages/LoginPage'));
const ForgotPasswordPage = lazy(() => import('@/modules/auth/pages/ForgotPasswordPage'));
const VerifyEmailPage = lazy(() => import('@/modules/auth/pages/VerifyEmailPage'));
const VerifyForgotPasswordPage = lazy(
  () => import('@/modules/auth/pages/VerifyForgotPasswordPage')
);
const TwoFactorAuthPage = lazy(() => import('@/modules/auth/pages/TwoFactorAuthPage'));
const SocialLoginCallbackPage = lazy(() => import('@/modules/auth/pages/SocialLoginCallbackPage'));

// Import Admin Auth pages with lazy loading
const AdminLoginPage = lazy(() => import('@/modules/admin/auth/pages/AdminLoginPage'));
const AdminForgotPasswordPage = lazy(
  () => import('@/modules/admin/auth/pages/AdminForgotPasswordPage')
);
const AdminVerifyForgotPasswordPage = lazy(
  () => import('@/modules/admin/auth/pages/AdminVerifyForgotPasswordPage')
);
const AdminResetPasswordPage = lazy(
  () => import('@/modules/admin/auth/pages/AdminResetPasswordPage')
);

/**
 * Auth module routes
 */
const authRoutes: RouteObject[] = [
  // User Auth Routes
  {
    path: '/auth',
    element: (
      <AuthLayout title="Authentication">
        <Suspense fallback={<Loading />}>
          <LoginPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/auth/forgot-password',
    element: (
      <AuthLayout title="Forgot Password">
        <Suspense fallback={<Loading />}>
          <ForgotPasswordPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/auth/verify-email',
    element: (
      <AuthLayout title="Verify Email">
        <Suspense fallback={<Loading />}>
          <VerifyEmailPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/auth/verify-forgot-password',
    element: (
      <AuthLayout title="Verify Password Reset">
        <Suspense fallback={<Loading />}>
          <VerifyForgotPasswordPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/auth/two-factor',
    element: (
      <AuthLayout title="Two-Factor Authentication">
        <Suspense fallback={<Loading />}>
          <TwoFactorAuthPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/auth/callback',
    element: (
      <Suspense fallback={<Loading />}>
        <SocialLoginCallbackPage />
      </Suspense>
    ),
  },

  // Admin Auth Routes
  {
    path: '/admin/auth',
    element: (
      <AuthLayout title={i18n.t('auth:admin.login')}>
        <Suspense fallback={<Loading />}>
          <AdminLoginPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/admin/auth/forgot-password',
    element: (
      <AuthLayout title={i18n.t('auth:admin.forgotPassword')}>
        <Suspense fallback={<Loading />}>
          <AdminForgotPasswordPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/admin/auth/verify-forgot-password',
    element: (
      <AuthLayout title={i18n.t('auth:admin.verifyPasswordReset')}>
        <Suspense fallback={<Loading />}>
          <AdminVerifyForgotPasswordPage />
        </Suspense>
      </AuthLayout>
    ),
  },
  {
    path: '/admin/auth/reset-password',
    element: (
      <AuthLayout title={i18n.t('auth:admin.resetPassword')}>
        <Suspense fallback={<Loading />}>
          <AdminResetPasswordPage />
        </Suspense>
      </AuthLayout>
    ),
  },
];

export default authRoutes;
