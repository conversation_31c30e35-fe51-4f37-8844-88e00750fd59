/**
 * <PERSON><PERSON><PERSON> nghĩa kiểu cho result khi code = 202 (xác thực email/số điện thoại)
 */
export interface VerifyEmailResult {
  verifyToken: string;
  expiresIn: number;
  info: Array<{ platform: string; value: string }>;
}

/**
 * Đ<PERSON>nh nghĩa kiểu cho result khi code = 203 (xác thực hai lớp)
 */
export interface TwoFactorAuthResult {
  verifyToken: string;
  expiresAt: number;
  enabledMethods: Array<{ type: string; value: string }>;
}

/**
 * Định nghĩa kiểu cho result khi đăng nhập thành công
 */
export interface LoginSuccessResult {
  accessToken: string;
  user: {
    id: string;
    email: string;
    [key: string]: unknown;
  };
}
