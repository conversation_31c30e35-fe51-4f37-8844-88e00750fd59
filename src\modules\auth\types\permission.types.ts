/**
 * Enum định nghĩa các quyền hạn trong hệ thống.
 * <PERSON><PERSON><PERSON> trị của mỗi enum member theo định dạng "module:action".
 * Đồng bộ với backend/auth/enum/permission.enum.ts
 */
export enum Permission {
  // Point Module
  POINT_VIEW_LIST = 'point:view_list',
  POINT_CREATE = 'point:create',
  POINT_UPDATE_POINT = 'point:update_point',

  // Agent Module
  AGENT_CREATE = 'agent:create',

  // User Dashboard Module
  USER_DASHBOARD_VIEW_INFO = 'user_dashboard:view_info',

  // Affiliate Module
  AFFILIATE_CREATE = 'affiliate:create',
  AFFILIATE_VIEW = 'affiliate:view',
  AFFILIATE_UPDATE = 'affiliate:update',
  AFFILIATE_DELETE = 'affiliate:delete',

  // Auth Module
  AUTH_LOGIN = 'auth:login',
  AUTH_LOGOUT = 'auth:logout',
  AUTH_RESET_PASSWORD = 'auth:reset-password',

  // Blog Module
  BLOG_CREATE = 'blog:create',
  BLOG_VIEW = 'blog:view',
  BLOG_UPDATE = 'blog:update',
  BLOG_DELETE = 'blog:delete',
  BLOG_PUBLISH = 'blog:publish',

  // Business Module
  BUSINESS_CREATE = 'business:create',
  BUSINESS_VIEW = 'business:view',
  BUSINESS_UPDATE = 'business:update',
  BUSINESS_DELETE = 'business:delete',

  // Data Module
  DATA_IMPORT = 'data:import',
  DATA_EXPORT = 'data:export',
  DATA_VIEW = 'data:view',
  DATA_DELETE = 'data:delete',

  // Database Module
  DATABASE_BACKUP = 'database:backup',
  DATABASE_RESTORE = 'database:restore',
  DATABASE_VIEW = 'database:view',

  // Email Module
  EMAIL_SEND = 'email:send',
  EMAIL_CREATE_TEMPLATE = 'email:create-template',
  EMAIL_UPDATE_TEMPLATE = 'email:update-template',
  EMAIL_DELETE_TEMPLATE = 'email:delete-template',
  EMAIL_VIEW_TEMPLATE = 'email:view-template',

  // Employee Module
  EMPLOYEE_CREATE = 'employee:create',
  EMPLOYEE_VIEW = 'employee:view',
  EMPLOYEE_UPDATE = 'employee:update',
  EMPLOYEE_DELETE = 'employee:delete',
  EMPLOYEE_ASSIGN_ROLE = 'employee:assign-role',

  // Generic Module
  GENERIC_MANAGE_SETTINGS = 'generic:manage-settings',
  GENERIC_VIEW_LOGS = 'generic:view-logs',

  // Google Module
  GOOGLE_CONNECT = 'google:connect',
  GOOGLE_DISCONNECT = 'google:disconnect',

  // Integration Module
  INTEGRATION_CREATE = 'integration:create',
  INTEGRATION_VIEW = 'integration:view',
  INTEGRATION_UPDATE = 'integration:update',
  INTEGRATION_DELETE = 'integration:delete',

  // Invoice Module
  INVOICE_CREATE = 'invoice:create',
  INVOICE_VIEW = 'invoice:view',
  INVOICE_UPDATE = 'invoice:update',
  INVOICE_DELETE = 'invoice:delete',
  INVOICE_EXPORT = 'invoice:export',

  // Marketplace Module
  MARKETPLACE_CREATE_PRODUCT = 'marketplace:create-product',
  MARKETPLACE_VIEW_PRODUCT = 'marketplace:view-product',
  MARKETPLACE_UPDATE_PRODUCT = 'marketplace:update-product',
  MARKETPLACE_DELETE_PRODUCT = 'marketplace:delete-product',
  MARKETPLACE_VIEW_ORDER = 'marketplace:view-order',
  MARKETPLACE_UPDATE_ORDER = 'marketplace:update-order',

  // Marketing Module
  MARKETING_CREATE_CAMPAIGN = 'marketing:create-campaign',
  MARKETING_VIEW_CAMPAIGN = 'marketing:view-campaign',
  MARKETING_UPDATE_CAMPAIGN = 'marketing:update-campaign',
  MARKETING_DELETE_CAMPAIGN = 'marketing:delete-campaign',

  // Model Training Module
  MODEL_TRAINING_CREATE = 'model-training:create',
  MODEL_TRAINING_VIEW = 'model-training:view',
  MODEL_TRAINING_UPDATE = 'model-training:update',
  MODEL_TRAINING_DELETE = 'model-training:delete',

  // PDF Module
  PDF_CREATE = 'pdf:create',
  PDF_VIEW = 'pdf:view',
  PDF_DELETE = 'pdf:delete',

  // R-Point Module
  R_POINT_CREATE = 'r-point:create',
  R_POINT_VIEW = 'r-point:view',
  R_POINT_UPDATE = 'r-point:update',
  R_POINT_DELETE = 'r-point:delete',

  // Recaptcha Module
  RECAPTCHA_CONFIGURE = 'recaptcha:configure',
  RECAPTCHA_VIEW = 'recaptcha:view',

  // SMS Module
  SMS_SEND = 'sms:send',
  SMS_CREATE_TEMPLATE = 'sms:create-template',
  SMS_UPDATE_TEMPLATE = 'sms:update-template',
  SMS_DELETE_TEMPLATE = 'sms:delete-template',
  SMS_VIEW_TEMPLATE = 'sms:view-template',

  // Strategy Module
  STRATEGY_CREATE = 'strategy:create',
  STRATEGY_VIEW = 'strategy:view',
  STRATEGY_UPDATE = 'strategy:update',
  STRATEGY_DELETE = 'strategy:delete',

  // Subscription Module
  SUBSCRIPTION_CREATE = 'subscription:create',
  SUBSCRIPTION_VIEW = 'subscription:view',
  SUBSCRIPTION_UPDATE = 'subscription:update',
  SUBSCRIPTION_DELETE = 'subscription:delete',
  SUBSCRIPTION_ASSIGN_USER = 'subscription:assign-user',

  // System Configuration Module
  SYSTEM_CONFIGURATION_VIEW = 'system-configuration:view',
  SYSTEM_CONFIGURATION_UPDATE = 'system-configuration:update',

  // Task Module
  TASK_CREATE = 'task:create',
  TASK_VIEW = 'task:view',
  TASK_UPDATE = 'task:update',
  TASK_DELETE = 'task:delete',
  TASK_ASSIGN = 'task:assign',

  // User Module
  USER_CREATE = 'user:create',
  USER_VIEW = 'user:view',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_BLOCK = 'user:block',
  USER_UNBLOCK = 'user:unblock',
}
