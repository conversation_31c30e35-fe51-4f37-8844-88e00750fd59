/**
 * <PERSON><PERSON><PERSON> nghĩa kiểu cho Google reCAPTCHA Enterprise
 */
export interface GrecaptchaEnterprise {
  ready: (callback: () => void) => void;
  render: (container: string, options: GrecaptchaOptions) => number;
  reset: (id: number) => void;
}

/**
 * Đ<PERSON><PERSON> nghĩa kiểu cho các tùy chọn reCAPTCHA
 */
export interface GrecaptchaOptions {
  sitekey: string;
  callback: (token: string) => void;
}

/**
 * Đ<PERSON>nh nghĩa kiểu cho Window với reCAPTCHA
 */
export interface WindowWithGrecaptcha extends Window {
  grecaptcha?: {
    enterprise: GrecaptchaEnterprise;
  };
}
