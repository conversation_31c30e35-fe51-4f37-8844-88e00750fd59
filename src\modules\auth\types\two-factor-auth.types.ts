/**
 * Enum định nghĩa các phương thức xác thực hai lớp
 */
export enum TwoFactorAuthMethod {
  /**
   * <PERSON><PERSON><PERSON> thực qua email
   */
  EMAIL = 'EMAIL',

  /**
   * <PERSON><PERSON><PERSON> thực qua SMS
   */
  SMS = 'SMS',

  /**
   * <PERSON><PERSON><PERSON> thực qua ứng dụng
   */
  APP = 'APP',
}

/**
 * Interface định nghĩa thông tin phương thức xác thực hai lớp
 */
export interface TwoFactorAuthMethodInfo {
  /**
   * Loại phương thức xác thực
   */
  type: TwoFactorAuthMethod;

  /**
   * Tên hiển thị
   */
  displayName: string;

  /**
   * Mô tả
   */
  description: string;

  /**
   * Tên icon
   */
  icon: string;

  /**
   * Thông tin bị che (ví dụ: email bị che một phần)
   */
  maskedInfo?: string;
}
