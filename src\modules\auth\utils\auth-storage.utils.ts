import { SavedCredentials } from '../types/auth.types';

const CREDENTIALS_KEY = 'auth_credentials';
const REMEMBER_ME_KEY = 'auth_remember_me'; // Thêm key mới để lưu trạng thái Remember me
const CREDENTIALS_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

/**
 * Mã hóa mật khẩu đơn giản bằng Base64
 * Lưu ý: Đây không phải là phương pháp mã hóa an toàn, chỉ để minh họa
 * Trong thực tế, nên sử dụng thư viện mã hóa chuyên dụng
 * @param password Mật khẩu cần mã hóa
 * @returns Mật khẩu đã mã hóa
 */
const encryptPassword = (password: string): string => {
  try {
    console.log('Encrypting password...');

    if (!password) {
      console.log('Password is empty, returning empty string');
      return '';
    }

    // Sử dụng phương pháp mã hóa đơn giản hơn để tránh lỗi
    // Chỉ sử dụng Base64 encoding
    const encoded = btoa(password);
    console.log('Password encrypted successfully, length:', encoded.length);

    return encoded;
  } catch (error) {
    console.error('Error encrypting password:', error);
    // Fallback to a simpler method if btoa fails
    try {
      // Sử dụng encodeURIComponent thay vì escape (đã deprecated)
      const encoded = encodeURIComponent(password);
      console.log('Fallback encryption used, length:', encoded.length);
      return encoded;
    } catch (fallbackError) {
      console.error('Fallback encryption also failed:', fallbackError);
      return '';
    }
  }
};

/**
 * Giải mã mật khẩu đã được mã hóa
 * @param encryptedPassword Mật khẩu đã mã hóa
 * @returns Mật khẩu gốc
 */
const decryptPassword = (encryptedPassword: string): string => {
  try {
    console.log('Decrypting password...');

    if (!encryptedPassword) {
      console.log('Encrypted password is empty, returning empty string');
      return '';
    }

    // Giải mã Base64
    const decoded = atob(encryptedPassword);
    console.log('Password decrypted successfully, length:', decoded.length);

    return decoded;
  } catch (error) {
    console.error('Error decrypting password:', error);
    // Fallback to a simpler method if atob fails
    try {
      // Sử dụng decodeURIComponent thay vì unescape (đã deprecated)
      const decoded = decodeURIComponent(encryptedPassword);
      console.log('Fallback decryption used, length:', decoded.length);
      return decoded;
    } catch (fallbackError) {
      console.error('Fallback decryption also failed:', fallbackError);
      return '';
    }
  }
};

/**
 * Save user credentials to localStorage
 * @param username Tên đăng nhập
 * @param password Mật khẩu (chưa mã hóa)
 */
export const saveCredentials = (username: string, password: string): void => {
  try {
    console.log('==========================================');
    console.log('SAVING CREDENTIALS TO LOCALSTORAGE');
    console.log('==========================================');

    if (!username) {
      console.error('Cannot save credentials: username is empty');
      console.log('==========================================');
      return;
    }

    // Mã hóa mật khẩu trước khi lưu
    const encryptedPassword = encryptPassword(password);
    console.log('Encrypted password length:', encryptedPassword.length);

    const credentials: SavedCredentials = {
      username,
      encryptedPassword,
      timestamp: Date.now(),
    };

    console.log('Saving credentials to localStorage:', {
      username,
      hasPassword: !!password,
      passwordLength: password.length,
      timestamp: credentials.timestamp,
      date: new Date(credentials.timestamp).toLocaleString(),
    });

    // Lưu thông tin đăng nhập
    const credentialsJson = JSON.stringify(credentials);
    console.log('Credentials JSON length:', credentialsJson.length);
    localStorage.setItem(CREDENTIALS_KEY, credentialsJson);

    // Lưu trạng thái Remember me
    localStorage.setItem(REMEMBER_ME_KEY, 'true');
    console.log('Remember me state saved to localStorage');

    // Kiểm tra xem đã lưu thành công chưa
    const savedCredentials = localStorage.getItem(CREDENTIALS_KEY);
    const rememberMe = localStorage.getItem(REMEMBER_ME_KEY);
    console.log('Verification after saving:', {
      credentialsSaved: !!savedCredentials,
      credentialsLength: savedCredentials ? savedCredentials.length : 0,
      rememberMeSaved: rememberMe === 'true',
    });

    console.log('==========================================');
  } catch (error) {
    console.error('Error saving credentials:', error);
    console.log('==========================================');
  }
};

/**
 * Get saved credentials from localStorage
 * @returns Saved credentials with decrypted password or null if not found
 */
export const getCredentials = (): { username: string; password: string } | null => {
  try {
    console.log('==========================================');
    console.log('GETTING CREDENTIALS FROM LOCALSTORAGE');
    console.log('==========================================');

    // Kiểm tra xem Remember me có được chọn không
    const rememberMe = localStorage.getItem(REMEMBER_ME_KEY);
    console.log('Remember me value in localStorage:', rememberMe);

    if (rememberMe !== 'true') {
      console.log('Remember me is not checked, not retrieving credentials');
      console.log('==========================================');
      return null;
    }

    const savedCredentials = localStorage.getItem(CREDENTIALS_KEY);
    console.log('Raw credentials in localStorage:', {
      exists: !!savedCredentials,
      length: savedCredentials ? savedCredentials.length : 0,
    });

    if (!savedCredentials) {
      console.log('No saved credentials found in localStorage');
      console.log('==========================================');
      return null;
    }

    try {
      const parsedCredentials = JSON.parse(savedCredentials) as SavedCredentials;
      console.log('Parsed credentials:', {
        hasUsername: !!parsedCredentials.username,
        username: parsedCredentials.username,
        hasEncryptedPassword: !!parsedCredentials.encryptedPassword,
        encryptedPasswordLength: parsedCredentials.encryptedPassword
          ? parsedCredentials.encryptedPassword.length
          : 0,
        timestamp: parsedCredentials.timestamp,
        date: new Date(parsedCredentials.timestamp).toLocaleString(),
      });

      if (!parsedCredentials || !parsedCredentials.username) {
        console.log('Invalid credentials format in localStorage');
        console.log('==========================================');
        return null;
      }

      // Giải mã mật khẩu
      const password = decryptPassword(parsedCredentials.encryptedPassword || '');
      console.log('Decrypted password length:', password.length);

      console.log('Retrieved credentials from localStorage:', {
        username: parsedCredentials.username,
        passwordLength: password.length, // Không log mật khẩu thật
        timestamp: parsedCredentials.timestamp,
        expiresAt: new Date(parsedCredentials.timestamp + CREDENTIALS_EXPIRY).toLocaleString(),
      });

      console.log('==========================================');
      return {
        username: parsedCredentials.username,
        password,
      };
    } catch (parseError) {
      console.error('Error parsing credentials from localStorage:', parseError);
      // Xóa thông tin không hợp lệ
      localStorage.removeItem(CREDENTIALS_KEY);
      console.log('==========================================');
      return null;
    }
  } catch (error) {
    console.error('Error getting credentials:', error);
    console.log('==========================================');
    return null;
  }
};

/**
 * Clear saved credentials from localStorage
 */
export const clearCredentials = (): void => {
  try {
    console.log('Clearing credentials from localStorage');
    localStorage.removeItem(CREDENTIALS_KEY);

    // Xóa trạng thái Remember me
    localStorage.removeItem(REMEMBER_ME_KEY);
    console.log('Remember me state cleared from localStorage');
  } catch (error) {
    console.error('Error clearing credentials:', error);
  }
};

/**
 * Check if saved credentials are valid (not expired)
 * @returns True if credentials are valid, false otherwise
 */
export const areCredentialsValid = (): boolean => {
  try {
    console.log('==========================================');
    console.log('CHECKING CREDENTIALS VALIDITY');
    console.log('==========================================');

    // Kiểm tra xem Remember me có được chọn không
    const rememberMe = localStorage.getItem(REMEMBER_ME_KEY);
    console.log('Remember me value:', rememberMe);

    if (rememberMe !== 'true') {
      console.log('Remember me is not checked, credentials are not valid');
      console.log('==========================================');
      return false;
    }

    // Lấy thông tin đăng nhập từ localStorage trực tiếp để kiểm tra timestamp
    const savedCredentialsJson = localStorage.getItem(CREDENTIALS_KEY);
    console.log('Credentials in localStorage:', {
      exists: !!savedCredentialsJson,
      length: savedCredentialsJson ? savedCredentialsJson.length : 0,
    });

    if (!savedCredentialsJson) {
      console.log('No credentials found in localStorage');
      console.log('==========================================');
      return false;
    }

    try {
      const savedCredentialsRaw = JSON.parse(savedCredentialsJson) as SavedCredentials;
      console.log('Parsed credentials:', {
        hasUsername: !!savedCredentialsRaw.username,
        username: savedCredentialsRaw.username,
        hasEncryptedPassword: !!savedCredentialsRaw.encryptedPassword,
        hasTimestamp: !!savedCredentialsRaw.timestamp,
      });

      if (!savedCredentialsRaw || !savedCredentialsRaw.username || !savedCredentialsRaw.timestamp) {
        console.log('Invalid credentials format in localStorage');
        console.log('==========================================');
        return false;
      }

      const now = Date.now();
      const expiryTime = savedCredentialsRaw.timestamp + CREDENTIALS_EXPIRY;

      const isValid = now < expiryTime;
      console.log('Credentials validity check:', {
        username: savedCredentialsRaw.username,
        now: new Date(now).toLocaleString(),
        expiryTime: new Date(expiryTime).toLocaleString(),
        expiresIn: Math.floor((expiryTime - now) / 1000 / 60), // minutes remaining
        isValid,
      });

      console.log('==========================================');
      return isValid;
    } catch (parseError) {
      console.error('Error parsing credentials from localStorage:', parseError);
      // Xóa thông tin không hợp lệ
      localStorage.removeItem(CREDENTIALS_KEY);
      console.log('==========================================');
      return false;
    }
  } catch (error) {
    console.error('Error checking credentials validity:', error);
    console.log('==========================================');
    return false;
  }
};

/**
 * Kiểm tra xem người dùng đã chọn "Remember me" chưa
 * @returns True nếu người dùng đã chọn "Remember me", false nếu chưa
 */
export const isRememberMeChecked = (): boolean => {
  try {
    const rememberMe = localStorage.getItem(REMEMBER_ME_KEY);
    const isChecked = rememberMe === 'true';

    // Kiểm tra xem có thông tin đăng nhập không
    const hasCredentials = !!localStorage.getItem(CREDENTIALS_KEY);

    console.log('Remember me state check:', {
      isChecked,
      hasCredentials,
      result: isChecked && hasCredentials,
    });

    // Chỉ trả về true nếu cả hai điều kiện đều đúng
    return isChecked && hasCredentials;
  } catch (error) {
    console.error('Error checking remember me state:', error);
    return false;
  }
};
