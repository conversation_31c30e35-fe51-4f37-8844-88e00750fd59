import { Permission } from '../types/permission.types';

/**
 * Kiểm tra xem người dùng có quyền cụ thể không
 * @param userPermissions Mảng quyền của người dùng
 * @param requiredPermission Quyền cần kiểm tra
 * @returns true nếu người dùng có quyền, false nếu không
 */
export const hasPermission = (
  userPermissions: string[] | undefined,
  requiredPermission: Permission | string
): boolean => {
  if (!userPermissions || userPermissions.length === 0) {
    return false;
  }

  return userPermissions.includes(requiredPermission);
};

/**
 * Kiểm tra xem người dùng có ít nhất một trong các quyền được yêu cầu không
 * @param userPermissions Mảng quyền của người dùng
 * @param requiredPermissions Mảng quyền cần kiểm tra
 * @returns true nếu người dùng có ít nhất một quyền, false nếu không
 */
export const hasAnyPermission = (
  userPermissions: string[] | undefined,
  requiredPermissions: (Permission | string)[]
): boolean => {
  if (!userPermissions || userPermissions.length === 0) {
    return false;
  }

  return requiredPermissions.some(permission => userPermissions.includes(permission));
};

/**
 * Kiểm tra xem người dùng có tất cả các quyền được yêu cầu không
 * @param userPermissions Mảng quyền của người dùng
 * @param requiredPermissions Mảng quyền cần kiểm tra
 * @returns true nếu người dùng có tất cả các quyền, false nếu không
 */
export const hasAllPermissions = (
  userPermissions: string[] | undefined,
  requiredPermissions: (Permission | string)[]
): boolean => {
  if (!userPermissions || userPermissions.length === 0) {
    return false;
  }

  return requiredPermissions.every(permission => userPermissions.includes(permission));
};

/**
 * Kiểm tra xem người dùng có quyền trong một module cụ thể không
 * @param userPermissions Mảng quyền của người dùng
 * @param module Tên module (ví dụ: 'user', 'point', 'blog')
 * @returns true nếu người dùng có ít nhất một quyền trong module, false nếu không
 */
export const hasModulePermission = (
  userPermissions: string[] | undefined,
  module: string
): boolean => {
  if (!userPermissions || userPermissions.length === 0) {
    return false;
  }

  const modulePrefix = `${module}:`;
  return userPermissions.some(permission => permission.startsWith(modulePrefix));
};

/**
 * Lọc danh sách quyền của người dùng theo module
 * @param userPermissions Mảng quyền của người dùng
 * @param module Tên module (ví dụ: 'user', 'point', 'blog')
 * @returns Mảng các quyền thuộc module
 */
export const filterPermissionsByModule = (
  userPermissions: string[] | undefined,
  module: string
): string[] => {
  if (!userPermissions || userPermissions.length === 0) {
    return [];
  }

  const modulePrefix = `${module}:`;
  return userPermissions.filter(permission => permission.startsWith(modulePrefix));
};

/**
 * Kiểm tra xem người dùng có quyền thực hiện một hành động cụ thể trong module không
 * @param userPermissions Mảng quyền của người dùng
 * @param module Tên module (ví dụ: 'user', 'point', 'blog')
 * @param action Tên hành động (ví dụ: 'create', 'view', 'update', 'delete')
 * @returns true nếu người dùng có quyền, false nếu không
 */
export const hasModuleActionPermission = (
  userPermissions: string[] | undefined,
  module: string,
  action: string
): boolean => {
  if (!userPermissions || userPermissions.length === 0) {
    return false;
  }

  const permissionString = `${module}:${action}`;
  return userPermissions.includes(permissionString);
};
