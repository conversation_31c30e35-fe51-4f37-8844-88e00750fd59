import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Interface cho reply của bình luận
 */
export interface CommentReply {
  id: number | string;
  blog_id: number;
  user_id: number;
  created_at: number;
  content: string;
  author_type: string;
  employee_id: number | null;
  parent_comment_id: number | string;
}

/**
 * Interface cho item trong danh sách bình luận
 */
export interface BlogCommentItem {
  id: number | string;
  blog_id: number;
  user_id: number;
  created_at: number;
  content: string;
  author_type: string;
  employee_id: number | null;
  parent_comment_id: number | null;
  replies: CommentReply[];
}

/**
 * Interface cho response của API lấy danh sách bình luận
 */
export type BlogCommentsResponse = PaginatedResult<BlogCommentItem>

/**
 * Interface cho query params của API lấy danh sách bình luận
 */
export interface GetBlogCommentsQueryDto {
  page?: number;
  limit?: number;
}

/**
 * Interface cho request tạo bình luận mới
 */
export interface CreateCommentDto {
  content: string;
  parent_comment_id?: number | string;
}

/**
 * Lấy danh sách bình luận của một bài viết
 * @param blogId ID của bài viết
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getBlogComments = async (
  blogId: number,
  params?: GetBlogCommentsQueryDto
): Promise<ApiResponse<BlogCommentsResponse>> => {
  return apiClient.get(`/user/blogs/${blogId}/comments`, { params });
};

/**
 * Tạo bình luận mới cho một bài viết
 * @param blogId ID của bài viết
 * @param data Dữ liệu bình luận
 * @returns Promise với response từ API
 */
export const createBlogComment = async (
  blogId: number,
  data: CreateCommentDto
): Promise<ApiResponse<BlogCommentItem>> => {
  return apiClient.post(`/user/blogs/${blogId}/comments`, data);
};

/**
 * Xóa một bình luận
 * @param blogId ID của bài viết
 * @param commentId ID của bình luận
 * @returns Promise với response từ API
 */
export const deleteBlogComment = async (
  blogId: number,
  commentId: number | string
): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/blogs/${blogId}/comments/${commentId}`);
};
