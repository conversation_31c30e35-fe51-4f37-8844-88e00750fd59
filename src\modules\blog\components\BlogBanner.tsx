import React from 'react';
import { useTranslation } from 'react-i18next';
import { Chip, Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

interface BlogBannerProps {
  /**
   * URL hình ảnh banner
   */
  imageUrl: string;

  /**
   * Số lượt xem
   */
  viewCount: number;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị banner của blog với số lượt xem
 */
const BlogBanner: React.FC<BlogBannerProps> = ({ imageUrl, viewCount, className = '' }) => {
  const { t } = useTranslation();
  // Sử dụng hook theme mới
  useTheme();

  // Format số lượt xem
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <div className={`relative w-full overflow-hidden ${className}`} data-testid="blog-banner">
      {/* Banner image */}
      <div className="aspect-w-16 aspect-h-9 md:aspect-h-8 lg:aspect-h-7 max-h-[400px]">
        <img
          src={imageUrl}
          alt={t('blog.bannerAlt', 'Blog banner')}
          className="w-full h-full object-cover"
        />
      </div>

      {/* View count chip */}
      <div className="absolute bottom-4 right-4">
        <Chip variant="primary" className="flex items-center gap-3 px-4 py-1.5 text-sm font-medium">
          <span>{formatViewCount(viewCount)}</span>
          <Icon name="eye" size="sm" />
        </Chip>
      </div>
    </div>
  );
};

export default BlogBanner;
