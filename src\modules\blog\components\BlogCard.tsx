import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, Icon, Chip, ResponsiveImage } from '@/shared/components/common';
import { BlogApiItem } from '../types/blog.types';

export interface BlogCardProps {
  /**
   * Dữ liệu blog
   */
  blog: BlogApiItem;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Callback khi click vào tag
   */
  onTagClick?: (tag: string, e: React.MouseEvent) => void;
}

/**
 * Component hiển thị card blog với thumbnail, tiêu đề, tag và thông tin tác giả
 */
const BlogCard: React.FC<BlogCardProps> = ({ blog, className = '', onTagClick }) => {
  const navigate = useNavigate();

  // Xử lý khi click vào tag
  const handleTagClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onTagClick && blog.tags?.[0]) {
      onTagClick(blog.tags[0], e);
    } else if (blog.tags?.[0]) {
      navigate(`/blog/tag/${blog.tags[0]}`);
    }
  };
  // Format date with weekday - xử lý an toàn với timestamp dạng chuỗi
  const formatDate = (timestamp: number | string | undefined | null): string => {
    try {
      // Kiểm tra timestamp có hợp lệ không
      if (!timestamp) {
        return '';
      }

      // Chuyển đổi timestamp từ string sang number nếu cần
      const timeValue = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;

      // Kiểm tra xem giá trị đã parse có phải là số hợp lệ không
      if (isNaN(timeValue)) {
        return '';
      }

      // Tạo đối tượng Date
      const date = new Date(timeValue);

      // Kiểm tra date có hợp lệ không
      if (isNaN(date.getTime())) {
        return '';
      }

      // Lấy tên thứ trong tuần
      const weekday = new Intl.DateTimeFormat('vi-VN', { weekday: 'long' }).format(date);

      // Định dạng ngày tháng
      const dateFormatted = new Intl.DateTimeFormat('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).format(date);

      // Viết hoa chữ cái đầu của tên thứ
      const capitalizedWeekday = weekday.charAt(0).toUpperCase() + weekday.slice(1);

      return `${capitalizedWeekday}, ${dateFormatted}`;
    } catch (error) {
      console.error('Error formatting date:', error, timestamp);
      return '';
    }
  };

  return (
    <Card
      className={`flex flex-col h-full overflow-hidden p-0 rounded-xl hover:shadow-lg transition-shadow duration-300 cursor-pointer ${className}`}
      noPadding={true}
      allowOverflow={false}
      style={{ display: 'flex', flexDirection: 'column' }}
    >
      {/* Blog thumbnail */}
      <div className="relative w-full">
        <div className="w-full h-48 overflow-hidden rounded-t-xl">
          <ResponsiveImage
            src={blog.thumbnailUrl}
            alt={blog.title}
            className="h-full w-full object-cover"
            aspectRatio="16/9"
            lazy={true}
            style={{ display: 'block' }}
          />
        </div>

        {/* Chips row - phía dưới banner nhưng vẫn trong banner */}
        <div className="absolute bottom-2 left-0 right-0 flex justify-between px-2">
          {/* Coin chip - bên trái */}
          <div className="bg-primary text-primary-foreground rounded-full px-3 py-1 text-xs font-medium flex items-center shadow-sm">
            <Typography variant="caption" className="mr-0.5 text-primary-foreground">
              {blog.point}
            </Typography>
            <Icon name="star" size="xs" className="text-primary-foreground" />
          </div>

          {/* View count - bên phải */}
          <div className="bg-background rounded-full px-3 py-1 text-xs font-medium flex items-center shadow-sm">
            <Typography variant="caption" className="text-primary mr-1.5">
              {blog.viewCount.toLocaleString()}
            </Typography>
            <Icon name="eye" size="xs" className="text-primary" />
          </div>
        </div>
      </div>

      {/* Blog content */}
      <div className="p-4 flex-grow flex flex-col mt-0">
        {/* Title - chỉ hiển thị 1 dòng với dấu ba chấm, căn trái */}
        <Typography
          variant="body1"
          className="text-base font-semibold whitespace-nowrap overflow-hidden text-ellipsis text-left text-foreground hover:text-primary transition-colors mb-4"
          truncate
        >
          {blog.title}
        </Typography>

        {/* Tags - đặt bên trái */}
        <div className="flex justify-start mb-4">
          <div onClick={handleTagClick} className="z-10">
            <Chip
              variant="primary"
              size="sm"
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              {blog.tags?.[0] || 'Môi trường'}
            </Chip>
          </div>
        </div>

        {/* Author and date */}
        <div className="flex items-center justify-between mt-auto">
          <div className="flex items-center">
            <div className="w-6 h-6 rounded-full overflow-hidden mr-2">
              <ResponsiveImage
                src={
                  blog.author?.avatar || `https://i.pravatar.cc/150?img=${(blog.id) % 70}`
                }
                alt={blog.author?.name || 'Author'}
                className="w-full h-full object-cover"
              />
            </div>
            <Typography variant="caption" className="text-xs font-medium text-foreground">
              {blog.author?.name || 'Nguyễn Mạnh Cường'}
            </Typography>
          </div>
          <Typography variant="caption" className="text-xs font-medium text-muted">
            {formatDate(blog.createdAt)}
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default BlogCard;
