import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Avatar,
  Button,
  Card,
  Input,
  Typography,
  Tooltip,
  Icon,
  Dropdown
} from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';
import { BlogCommentItem, ReactionType } from '../types/blog-comment.types';

// Props cho component BlogComment
interface BlogCommentProps {
  /**
   * Danh sách bình luận
   */
  comments: BlogCommentItem[];

  /**
   * Callback khi gửi bình luận mới
   */
  onSubmitComment: (content: string, parentId?: string) => void;

  /**
   * Callback khi chỉnh sửa bình luận
   */
  onEditComment?: (commentId: string, content: string) => void;

  /**
   * Callback khi xóa bình luận
   */
  onDeleteComment?: (commentId: string) => void;

  /**
   * Callback khi thích bình luận
   */
  onLikeComment?: (commentId: string, reactionType: ReactionType) => void;

  /**
   * Đang tải danh sách bình luận
   */
  loading?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị danh sách bình luận và form gửi bình luận
 */
const BlogComment: React.FC<BlogCommentProps> = ({
  comments,
  onSubmitComment,
  onEditComment,
  onDeleteComment,
  onLikeComment,
  loading = false,
  className = ''
}) => {
  const { t } = useTranslation();
  useTheme();

  // State cho nội dung bình luận mới
  const [newComment, setNewComment] = useState('');

  // State cho ID bình luận đang trả lời (nếu có)
  const [replyingTo, setReplyingTo] = useState<string | null>(null);

  // State cho nội dung trả lời
  const [replyContent, setReplyContent] = useState('');

  // Xử lý khi gửi bình luận mới
  const handleSubmitComment = () => {
    if (!newComment.trim()) return;

    onSubmitComment(newComment);
    setNewComment('');
  };

  // Xử lý khi gửi trả lời
  const handleSubmitReply = (parentId: string) => {
    if (!replyContent.trim()) return;

    onSubmitComment(replyContent, parentId);
    setReplyContent('');
    setReplyingTo(null);
  };

  // Xử lý khi bấm nút trả lời
  const handleReplyClick = (commentId: string) => {
    setReplyingTo(commentId);
    setReplyContent('');
  };

  // Xử lý khi hủy trả lời
  const handleCancelReply = () => {
    setReplyingTo(null);
    setReplyContent('');
  };

  // Format thời gian
  const formatTime = (timestamp: string) => {
    try {
      // Kiểm tra timestamp có hợp lệ không
      if (!timestamp) {
        return 'Không xác định';
      }

      // Nếu timestamp là số (unix timestamp), chuyển đổi thành Date
      const date = typeof timestamp === 'number'
        ? new Date(timestamp)
        : new Date(timestamp);

      // Kiểm tra date có hợp lệ không
      if (isNaN(date.getTime())) {
        return 'Không xác định';
      }

      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: vi
      });
    } catch (error) {
      console.error('Error formatting time:', error, timestamp);
      return 'Không xác định';
    }
  };

  // Lấy avatar dựa trên user_id
  const getAvatarUrl = (userId: number) => {
    return `https://i.pravatar.cc/150?img=${userId % 70}`;
  };

  // Lấy tên người dùng dựa trên user_id (giả lập)
  const getUserName = (userId: number, authorType: string) => {
    if (authorType === 'EMPLOYEE') {
      return `Nhân viên #${userId}`;
    }
    return `Người dùng #${userId}`;
  };

  return (
    <div className={`blog-comments ${className}`}>

      {/* Form gửi bình luận mới */}
      <Card className="mb-8 p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
        <div className="flex gap-4">
          <Avatar
            src={getAvatarUrl(10)} // Giả sử user hiện tại có ID là 10
            alt="Your avatar"
            size="md"
          />
          <div className="flex-1">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 mb-2">
              <Input
                type="textarea"
                placeholder={t('blog.comments.placeholder', 'Viết bình luận của bạn...')}
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                className="bg-transparent border-0 focus:ring-0 p-0"
                fullWidth
              />
              {newComment.trim() && (
                <div className="flex justify-between items-center mt-3">
                  <div className="flex items-center gap-2">
                    <Tooltip content={t('blog.comments.addEmoji', 'Thêm biểu tượng cảm xúc')}>
                      <Button variant="ghost" size="sm" className="p-1">
                        <Icon name="smile" size="sm" />
                      </Button>
                    </Tooltip>
                    <Tooltip content={t('blog.comments.addImage', 'Thêm hình ảnh')}>
                      <Button variant="ghost" size="sm" className="p-1">
                        <Icon name="image" size="sm" />
                      </Button>
                    </Tooltip>
                  </div>
                  <Button
                    variant="primary"
                    onClick={handleSubmitComment}
                    disabled={!newComment.trim()}
                  >
                    {t('blog.comments.submit', 'Gửi bình luận')}
                  </Button>
                </div>
              )}
            </div>
            <Typography variant="caption" color="muted" className="text-center block">
              {t('blog.comments.pressEnter', 'Nhấn Enter để gửi bình luận')}
            </Typography>
          </div>
        </div>
      </Card>

      {/* Danh sách bình luận */}
      {loading ? (
        <div className="text-center py-8">
          <Typography variant="body1" color="muted">
            {t('blog.comments.loading', 'Đang tải bình luận...')}
          </Typography>
        </div>
      ) : comments.length === 0 ? (
        <div className="text-center py-8">
          <Typography variant="body1" color="muted">
            {t('blog.comments.empty', 'Chưa có bình luận nào. Hãy là người đầu tiên bình luận!')}
          </Typography>
        </div>
      ) : (
        <div className="space-y-6">
          {comments.map((comment) => (
            <div key={comment.id} className="comment-item">
              <Card className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                {/* Thông tin người bình luận */}
                <div className="flex gap-3 mb-3">
                  <Avatar
                    src={getAvatarUrl(comment.userid)}
                    alt="User avatar"
                    size="md"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <Typography variant="subtitle1" className="font-medium">
                          {getUserName(comment.userid, comment.authortype)}
                        </Typography>
                        <Typography variant="caption" color="muted">
                          {formatTime(comment.createdat)}
                        </Typography>
                      </div>

                      {/* Dropdown menu cho edit/delete */}
                      <Dropdown
                        trigger={
                          <Button variant="ghost" size="sm" className="p-1 h-auto">
                            <Icon name="more-horizontal" size="sm" />
                          </Button>
                        }
                        items={[
                          {
                            id: 'edit',
                            label: t('blog.comments.edit', 'Chỉnh sửa'),
                            icon: 'edit',
                            onClick: () => {
                              if (onEditComment) {
                                // Giả lập mở form chỉnh sửa
                                const newContent = prompt('Chỉnh sửa bình luận:', comment.content);
                                if (newContent && newContent !== comment.content) {
                                  onEditComment(comment.id, newContent);
                                }
                              }
                            }
                          },
                          {
                            id: 'delete',
                            label: t('blog.comments.delete', 'Xóa'),
                            icon: 'trash',
                            onClick: () => {
                              if (onDeleteComment) {
                                if (confirm(t('blog.comments.confirmDelete', 'Bạn có chắc chắn muốn xóa bình luận này?'))) {
                                  onDeleteComment(comment.id);
                                }
                              }
                            }
                          }
                        ]}
                      />
                    </div>

                    {/* Nội dung bình luận */}
                    <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 mt-2">
                      <Typography variant="body1">
                        {comment.content}
                      </Typography>
                    </div>

                    {/* Reactions và actions */}
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center gap-1">
                        <Tooltip content={t('blog.comments.likes', 'Lượt thích')}>
                          <div className="flex items-center bg-blue-100 dark:bg-blue-900 rounded-full px-2 py-1">
                            <Icon name="thumbs-up" size="sm" className="text-blue-500" />
                            <Typography variant="caption" className="ml-1">
                              {Math.floor(Math.random() * 10)} {/* Giả lập số lượt thích */}
                            </Typography>
                          </div>
                        </Tooltip>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="flex items-center gap-1"
                          onClick={() => onLikeComment && onLikeComment(comment.id, ReactionType.LIKE)}
                        >
                          <Icon name="thumbs-up" size="sm" />
                          {t('blog.comments.like', 'Thích')}
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          className="flex items-center gap-1"
                          onClick={() => handleReplyClick(comment.id)}
                        >
                          <Icon name="message-circle" size="sm" />
                          {t('blog.comments.reply', 'Trả lời')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form trả lời */}
                {replyingTo === comment.id && (
                  <div className="mt-3 ml-12">
                    <div className="flex items-start gap-2">
                      <Avatar
                        src={getAvatarUrl(10)} // Giả sử user hiện tại có ID là 10
                        alt="Your avatar"
                        size="sm"
                      />
                      <div className="flex-1">
                        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-2">
                          <Input
                            type="textarea"
                            placeholder={t('blog.comments.replyPlaceholder', 'Viết trả lời của bạn...')}
                            value={replyContent}
                            onChange={(e) => setReplyContent(e.target.value)}
                            className="mb-2 bg-transparent border-0 focus:ring-0 p-0"
                          />
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleCancelReply}
                            >
                              {t('blog.comments.cancel', 'Hủy')}
                            </Button>
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => handleSubmitReply(comment.id)}
                              disabled={!replyContent.trim()}
                            >
                              {t('blog.comments.submitReply', 'Gửi')}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Danh sách trả lời */}
                {comment.replies && comment.replies.length > 0 && (
                  <div className="mt-4 ml-12 space-y-4">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="reply-item">
                        <div className="flex items-start gap-3">
                          <Avatar
                            src={getAvatarUrl(reply.userid)}
                            alt="User avatar"
                            size="sm"
                          />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div>
                                <Typography variant="subtitle2" className="font-medium">
                                  {getUserName(reply.userid, reply.authortype)}
                                </Typography>
                                <Typography variant="caption" color="muted">
                                  {formatTime(reply.createdat)}
                                </Typography>
                              </div>

                              {/* Dropdown menu cho reply */}
                              <Dropdown
                                trigger={
                                  <Button variant="ghost" size="sm" className="p-1 h-auto">
                                    <Icon name="more-horizontal" size="sm" />
                                  </Button>
                                }
                                items={[
                                  {
                                    id: 'edit-reply',
                                    label: t('blog.comments.edit', 'Chỉnh sửa'),
                                    icon: 'edit',
                                    onClick: () => {
                                      console.log('Edit reply', reply.id);
                                    }
                                  },
                                  {
                                    id: 'delete-reply',
                                    label: t('blog.comments.delete', 'Xóa'),
                                    icon: 'trash',
                                    onClick: () => {
                                      console.log('Delete reply', reply.id);
                                    }
                                  }
                                ]}
                              />
                            </div>

                            {/* Nội dung reply */}
                            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 mt-2">
                              <Typography variant="body2">
                                {reply.content}
                              </Typography>
                            </div>

                            {/* Reactions và actions */}
                            <div className="flex items-center justify-between mt-2">
                              <div className="flex items-center gap-1">
                                {Math.random() > 0.5 && (
                                  <Tooltip content={t('blog.comments.likes', 'Lượt thích')}>
                                    <div className="flex items-center bg-blue-100 dark:bg-blue-900 rounded-full px-2 py-1">
                                      <Icon name="thumbs-up" size="sm" className="text-blue-500" />
                                      <Typography variant="caption" className="ml-1">
                                        {Math.floor(Math.random() * 5)} {/* Giả lập số lượt thích */}
                                      </Typography>
                                    </div>
                                  </Tooltip>
                                )}
                              </div>

                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="flex items-center gap-1 text-xs"
                                  onClick={() => onLikeComment && onLikeComment(reply.id, ReactionType.LIKE)}
                                >
                                  <Icon name="thumbs-up" size="sm" />
                                  {t('blog.comments.like', 'Thích')}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </Card>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BlogComment;
