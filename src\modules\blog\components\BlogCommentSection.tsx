import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Icon, Dropdown } from '@/shared/components/common';
import BlogComment from './BlogComment';
import { useGetBlogComments } from '../hooks/useBlogComment';
import { apiClient } from '@/shared/api';
import { BlogCommentItem as ApiCommentItem } from '../api/blog-comment.api';
import { BlogCommentItem, ReactionType } from '../types/blog-comment.types';
import { useSmartNotification } from '@/shared/hooks/common';

interface BlogCommentSectionProps {
  /**
   * ID của bài viết
   */
  blogId: number;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị phần bình luận của bài viết
 */
const BlogCommentSection: React.FC<BlogCommentSectionProps> = ({
  blogId,
  className = ''
}) => {
  const { t } = useTranslation();

  // State để lưu trữ trạng thái
  const [submitting, setSubmitting] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Sử dụng hook thông báo
  const notification = useSmartNotification();

  // Sử dụng hook để lấy danh sách bình luận
  const { data, isLoading, refetch } = useGetBlogComments(blogId, { page, limit: 10 });

  // Chuyển đổi từ định dạng API sang định dạng component
  const mapApiCommentToComponentFormat = (apiComment: ApiCommentItem): BlogCommentItem => {
    return {
      id: String(apiComment.id),
      blogid: apiComment.blog_id,
      userid: apiComment.user_id,
      createdat: String(apiComment.created_at),
      updatedat: undefined, // API hiện tại không có trường này
      content: apiComment.content,
      authortype: apiComment.author_type,
      employeeid: apiComment.employee_id,
      parentcommentid: apiComment.parent_comment_id ? String(apiComment.parent_comment_id) : null,
      // Giả lập reactions cho demo
      reactions: Array(Math.floor(Math.random() * 5))
        .fill(0)
        .map((_, i) => ({
          id: `reaction-${apiComment.id}-${i}`,
          userid: Math.floor(Math.random() * 100),
          type: Object.values(ReactionType)[Math.floor(Math.random() * Object.values(ReactionType).length)],
          createdat: new Date().toISOString()
        })),
      // Thêm username và avatar giả lập
      username: `User ${apiComment.user_id}`,
      useravatar: `https://i.pravatar.cc/150?img=${apiComment.user_id % 70}`,
      isEdited: false, // API hiện tại không có trường này
      replies: apiComment.replies.map(reply => ({
        id: String(reply.id),
        blogid: reply.blog_id,
        userid: reply.user_id,
        createdat: String(reply.created_at),
        updatedat: undefined, // API hiện tại không có trường này
        content: reply.content,
        authortype: reply.author_type,
        employeeid: reply.employee_id,
        parentcommentid: String(reply.parent_comment_id),
        // Giả lập reactions cho demo
        reactions: Array(Math.floor(Math.random() * 3))
          .fill(0)
          .map((_, i) => ({
            id: `reaction-${reply.id}-${i}`,
            userid: Math.floor(Math.random() * 100),
            type: Object.values(ReactionType)[Math.floor(Math.random() * Object.values(ReactionType).length)],
            createdat: new Date().toISOString()
          })),
        // Thêm username và avatar giả lập
        username: `User ${reply.user_id}`,
        useravatar: `https://i.pravatar.cc/150?img=${reply.user_id % 70}`,
        isEdited: false // API hiện tại không có trường này
      }))
    };
  };

  // Lấy danh sách bình luận từ response và chuyển đổi định dạng
  const comments = useMemo(() => {
    return (data?.result?.items || []).map(mapApiCommentToComponentFormat);
  }, [data?.result?.items]);

  // Lấy tổng số bình luận từ response
  const totalComments = data?.result?.meta?.totalItems || 0;

  // Hàm gửi bình luận mới
  const submitComment = async (content: string, parentId?: string) => {
    if (!content.trim()) return;

    try {
      setSubmitting(true);

      // Chuẩn bị dữ liệu bình luận
      const commentData = {
        content,
        ...(parentId && { parent_comment_id: parentId }),
      };

      // Gọi API để tạo bình luận mới
      await apiClient.post(`/user/blogs/${blogId}/comments`, commentData);

      // Tải lại danh sách bình luận
      refetch();

      // Hiển thị thông báo thành công
      notification.success({
        message: t('blog.comments.submitSuccess', 'Đã gửi bình luận thành công')
      });
    } catch (error) {
      console.error('Error submitting comment:', error);
      notification.error({
        message: t('blog.comments.submitError', 'Không thể gửi bình luận. Vui lòng thử lại.')
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Hàm chỉnh sửa bình luận
  const editComment = useCallback(async (commentId: string, content: string) => {
    if (!content.trim()) return;

    try {
      setSubmitting(true);

      // Gọi API để cập nhật bình luận
      await apiClient.put(`/user/blogs/${blogId}/comments/${commentId}`, { content });

      // Tải lại danh sách bình luận
      refetch();

      // Hiển thị thông báo thành công
      notification.success({
        message: t('blog.comments.editSuccess', 'Đã cập nhật bình luận thành công')
      });
    } catch (error) {
      console.error('Error editing comment:', error);
      notification.error({
        message: t('blog.comments.editError', 'Không thể cập nhật bình luận. Vui lòng thử lại.')
      });
    } finally {
      setSubmitting(false);
    }
  }, [blogId, notification, refetch, t]);

  // Hàm xóa bình luận
  const deleteComment = useCallback(async (commentId: string) => {
    try {
      setSubmitting(true);

      // Gọi API để xóa bình luận
      await apiClient.delete(`/user/blogs/${blogId}/comments/${commentId}`);

      // Tải lại danh sách bình luận
      refetch();

      // Hiển thị thông báo thành công
      notification.success({
        message: t('blog.comments.deleteSuccess', 'Đã xóa bình luận thành công')
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      notification.error({
        message: t('blog.comments.deleteError', 'Không thể xóa bình luận. Vui lòng thử lại.')
      });
    } finally {
      setSubmitting(false);
    }
  }, [blogId, notification, refetch, t]);

  // Hàm thích bình luận
  const likeComment = useCallback(async (commentId: string, reactionType: ReactionType) => {
    try {
      // Gọi API để thích bình luận
      await apiClient.post(`/user/blogs/${blogId}/comments/${commentId}/reactions`, { type: reactionType });

      // Tải lại danh sách bình luận
      refetch();
    } catch (error) {
      console.error('Error liking comment:', error);
      notification.error({
        message: t('blog.comments.likeError', 'Không thể thích bình luận. Vui lòng thử lại.')
      });
    }
  }, [blogId, notification, refetch, t]);

  // Hàm tải thêm bình luận
  const loadMoreComments = useCallback(() => {
    if (hasMore && !isLoading) {
      setPage(prevPage => prevPage + 1);
    }
  }, [hasMore, isLoading]);

  // Kiểm tra xem có thêm bình luận để tải không
  useEffect(() => {
    if (data?.result?.meta) {
      const { currentPage, totalPages } = data.result.meta;
      setHasMore(currentPage < totalPages);
    }
  }, [data?.result?.meta]);

  // Cập nhật API để hỗ trợ các trường mới
  useEffect(() => {
    // Đây chỉ là một giải pháp tạm thời để demo
    // Trong thực tế, cần cập nhật API để hỗ trợ các trường mới
    console.log('API cần được cập nhật để hỗ trợ các trường: updatedat, reactions, username, useravatar, isEdited');
  }, []);

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <Typography variant="h4">
          {t('blog.comments.sectionTitle', 'Bình luận')} ({totalComments})
        </Typography>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => refetch()}
          >
            <Icon name="refresh-cw" size="sm" />
            {t('blog.comments.refresh', 'Làm mới')}
          </Button>

          <Dropdown
            trigger={
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Icon name="filter" size="sm" />
                {t('blog.comments.filter', 'Lọc')}
              </Button>
            }
            items={[
              {
                id: 'newest',
                label: t('blog.comments.newest', 'Mới nhất'),
                icon: 'clock',
                onClick: () => console.log('Filter by newest')
              },
              {
                id: 'oldest',
                label: t('blog.comments.oldest', 'Cũ nhất'),
                icon: 'calendar',
                onClick: () => console.log('Filter by oldest')
              },
              {
                id: 'most-liked',
                label: t('blog.comments.mostLiked', 'Nhiều lượt thích nhất'),
                icon: 'thumbs-up',
                onClick: () => console.log('Filter by most liked')
              }
            ]}
          />
        </div>
      </div>

      <BlogComment
        comments={comments}
        onSubmitComment={submitComment}
        onEditComment={editComment}
        onDeleteComment={deleteComment}
        onLikeComment={likeComment}
        loading={isLoading || submitting}
      />

      {hasMore && (
        <div className="mt-6 text-center">
          <Button
            variant="outline"
            onClick={loadMoreComments}
            disabled={isLoading || submitting}
            className="flex items-center gap-2 mx-auto"
          >
            {isLoading ? (
              <Icon name="loader" size="sm" className="animate-spin" />
            ) : (
              <Icon name="chevron-down" size="sm" />
            )}
            {t('blog.comments.loadMore', 'Tải thêm bình luận')}
          </Button>
        </div>
      )}
    </Card>
  );
};

export default BlogCommentSection;
