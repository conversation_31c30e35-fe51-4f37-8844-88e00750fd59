import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Loading } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

interface BlogContentProps {
  /**
   * Tiêu đề blog
   */
  title: string;

  /**
   * <PERSON><PERSON> tả blog (HTML)
   */
  description: string;

  /**
   * Nội dung đầy đủ của blog (HTML)
   */
  content: string;

  /**
   * Ngày tạo
   */
  createdAt: string;

  /**
   * <PERSON><PERSON> hiển thị nội dung đầy đủ không
   */
  showFullContent: boolean;

  /**
   * Có phải là nội dung premium không
   */
  isPremium: boolean;

  /**
   * Đ<PERSON> mua chưa
   */
  purchased: boolean;

  /**
   * Giá (nếu là premium)
   */
  price?: number;

  /**
   * Callback khi bấm nút Mua ngay
   */
  onPurchase: () => void;

  /**
   * Callback khi bấm nút Hủy
   */
  onCancel: () => void;

  /**
   * <PERSON><PERSON> xử lý mua
   */
  purchaseLoading: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị nội dung blog
 */
const BlogContent: React.FC<BlogContentProps> = ({
  title,
  description,
  content,
  createdAt,
  showFullContent,
  isPremium,
  purchased,
  // price không được sử dụng
  onPurchase,
  onCancel,
  purchaseLoading,
  className = '',
}) => {
  const { t } = useTranslation();
  // Sử dụng hook theme mới
  useTheme();

  // Format date with weekday
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const weekday = new Intl.DateTimeFormat('vi-VN', { weekday: 'long' }).format(date);
    const dateFormatted = new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    }).format(date);

    // Capitalize first letter of weekday
    const capitalizedWeekday = weekday.charAt(0).toUpperCase() + weekday.slice(1);

    return `${capitalizedWeekday}, ${dateFormatted}`;
  };

  // Không cần hàm formatPrice vì không hiển thị giá

  return (
    <div className={`${className}`} data-testid="blog-content">
      {/* Date */}
      <div className="text-sm text-muted-foreground mb-4 text-center font-medium">
        {formatDate(createdAt)}
      </div>

      {/* Title */}
      <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-foreground mb-6 text-center">
        {title}
      </h1>

      {/* Description */}
      <div
        className="prose prose-primary max-w-none mb-8"
        dangerouslySetInnerHTML={{ __html: description }}
      />

      {/* Purchase buttons for premium content */}
      {isPremium && !purchased && !showFullContent && (
        <div className="flex justify-end gap-4 mt-8 mb-8">
          <Button variant="outline" onClick={onCancel} disabled={purchaseLoading}>
            {t('blog.cancel', 'Hủy')}
          </Button>
          <Button variant="primary" onClick={onPurchase} isLoading={purchaseLoading}>
            {t('blog.buyNow', 'Mua ngay')}
          </Button>
        </div>
      )}

      {/* Full content */}
      {showFullContent && (
        <>
          <hr className="my-8 border-t border-border" />
          <div
            className="prose prose-primary max-w-none"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </>
      )}

      {/* Loading state */}
      {purchaseLoading && (
        <div className="flex justify-center my-8">
          <Loading />
        </div>
      )}
    </div>
  );
};

export default BlogContent;
