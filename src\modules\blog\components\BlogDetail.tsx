import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Loading } from '@/shared/components/common';
import { BlogResponseDto } from '../types/blog.types';
import BlogBanner from './BlogBanner';
import BlogTags from './BlogTags';
import BlogContent from './BlogContent';
import BlogCommentSection from './BlogCommentSection';
import { useTheme } from '@/shared/contexts/theme';
import Alert from '@/shared/components/common/Alert';
import { useHtmlContent } from '@/shared/hooks/common';

interface BlogDetailProps {
  /**
   * Dữ liệu blog
   */
  blog: BlogResponseDto | null;

  /**
   * Đang tải
   */
  loading: boolean;

  /**
   * Lỗi (nếu có)
   */
  error: string | null;

  /**
   * Có hiển thị nội dung đầy đủ không
   */
  showFullContent: boolean;

  /**
   * Callback khi bấm nút <PERSON>a ngay
   */
  onPurchase: () => void;

  /**
   * Callback khi bấm nút Hủy
   */
  onCancel: () => void;

  /**
   * <PERSON>ang xử lý mua
   */
  purchaseLoading: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị chi tiết blog
 */
const BlogDetail: React.FC<BlogDetailProps> = ({
  blog,
  loading,
  error,
  showFullContent,
  onPurchase,
  onCancel,
  purchaseLoading,
  className = ''
}) => {
  const { t } = useTranslation();
  // Sử dụng hook theme mới
  useTheme();

  // Sử dụng hook để lấy nội dung HTML từ URL
  const { content: htmlContent, status: htmlStatus } = useHtmlContent(
    blog?.content || '',
    {
      // Chỉ tự động lấy nội dung khi có blog và blog.content là URL
      autoFetch: !!blog?.content && blog.content.startsWith('http'),
      // Xóa các thẻ script để tránh XSS
      transformContent: (html) => html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    }
  );
  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loading />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert
        type="error"
        title={t('blog.errorTitle', 'Đã xảy ra lỗi')}
        message={error}
        className="my-4"
      />
    );
  }
  // No data
  if (!blog) {
    return (
      <Alert
        type="info"
        title={t('blog.notFoundTitle', 'Không tìm thấy')}
        message={t('blog.notFoundMessage', 'Không tìm thấy thông tin blog này.')}
        className="my-4"
      />
    );
  }

  return (
    <div className={`${className}`} data-testid="blog-detail">
      <Card className="p-0 overflow-hidden">
        {/* Banner with view count */}
        <div className="w-full">
          <BlogBanner
            imageUrl={blog.thumbnailUrl}
            viewCount={blog.viewCount}
            className="rounded-none"
          />
        </div>

        <div className="p-8">
          {/* Tags */}
          <BlogTags
            tags={blog.tags}
            size="md"
            variant="default"
            className="mb-8"
          />

          {/* Content */}
          <BlogContent
            title={blog.title}
            description={blog.content ? blog.content.substring(0, 200) + '...' : ''}
            content={htmlStatus === 'success' && blog.content && blog.content.startsWith('http') ? htmlContent : blog.content || ''}
            createdAt={new Date(blog.createdAt).toISOString()}
            showFullContent={showFullContent}
            isPremium={blog.point > 0}
            purchased={blog.isPurchased || false}
            price={blog.point}
            onPurchase={onPurchase}
            onCancel={onCancel}
            purchaseLoading={purchaseLoading}
          />
          <BlogCommentSection
            blogId={blog.id}
            className="mt-6"
          />
        </div>
      </Card>
    </div>
  );
};

export default BlogDetail;
