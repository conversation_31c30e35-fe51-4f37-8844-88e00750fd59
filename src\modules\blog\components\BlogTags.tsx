import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Chip } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import { BlogTag } from '../types/blog.types';

interface BlogTagsProps {
  /**
   * Danh sách tag
   */
  tags: BlogTag[] | string[];

  /**
   * Kích thước của tag
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Variant của tag
   */
  variant?: 'default' | 'primary' | 'secondary' | 'outline';

  /**
   * <PERSON><PERSON> lượng tag tối đa hiển thị
   */
  maxTags?: number;

  /**
   * Có hiển thị số lượng tag còn lại không
   */
  showMore?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị danh sách tag của blog
 */
const BlogTags: React.FC<BlogTagsProps> = ({
  tags,
  size = 'md',
  // variant không được sử dụng
  maxTags,
  showMore = true,
  className = '',
}) => {
  const navigate = useNavigate();
  // Sử dụng hook theme mới
  useTheme();

  if (!tags || tags.length === 0) {
    return null;
  }

  // Lọc số lượng tag hiển thị
  const visibleTags = maxTags ? tags.slice(0, maxTags) : tags;
  const hasMoreTags = maxTags && tags.length > maxTags;

  // Kiểm tra xem tags có phải là mảng string không
  const isStringArray = tags.length > 0 && typeof tags[0] === 'string';

  return (
    <div
      className={`flex flex-wrap gap-2 justify-center ${className}`}
      data-testid="blog-tags"
    >
      {visibleTags.map((tag, index) => {
        // Nếu là mảng string, tạo một đối tượng BlogTag tạm thời
        const tagObj = isStringArray
          ? { id: `tag-${index}`, name: tag as string, slug: tag as string }
          : tag as BlogTag;

        return (
          <Link
            key={tagObj.id}
            to={`/blog/tag/${tagObj.slug}`}
          >
            <Chip
              variant="primary"
              size={size}
            >
              {tagObj.name}
            </Chip>
          </Link>
        );
      })}

      {/* Hiển thị số lượng tag còn lại */}
      {hasMoreTags && showMore && (
        <Chip variant="primary" size={size} onClick={() => navigate(`/blog`)}>
          +{tags.length - maxTags}
        </Chip>
      )}
    </div>
  );
};

export default BlogTags;
