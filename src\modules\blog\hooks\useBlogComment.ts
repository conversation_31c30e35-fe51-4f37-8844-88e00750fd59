import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { BlogCommentsResponse, GetBlogCommentsQueryDto, getBlogComments } from '../api/blog-comment.api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

// Query keys cho các API liên quan đến Blog Comments
export const BLOG_COMMENT_QUERY_KEYS = {
  BLOG_COMMENTS: 'blogComments',
};

/**
 * Hook để lấy danh sách bình luận của bài viết
 * @param blogId ID của bài viết
 * @param params Tham số truy vấn
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBlogComments = (
  blogId: number | undefined,
  params?: GetBlogCommentsQueryDto,
  options?: UseQueryOptions<ApiResponseDto<BlogCommentsResponse>>
) => {
  return useQuery({
    queryKey: [BLOG_COMMENT_QUERY_KEYS.BLOG_COMMENTS, blogId, params],
    queryFn: () => getBlogComments(blogId as number, params),
    enabled: !!blogId,
    ...options,
  });
};
