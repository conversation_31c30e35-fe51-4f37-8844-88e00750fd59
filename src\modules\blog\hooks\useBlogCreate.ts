import { useMutation, UseMutationOptions, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { createBlog } from '../services/blog-create.service';
import { CreateBlogDto, CreateBlogApiResponse } from '../types/blog-create.types';
import { BLOG_QUERY_KEYS } from '../constants/blog-query-key';

/**
 * Hook để tạo bài viết mới
 * 
 * @param options TanStack Query mutation options
 * @returns Mutation object
 * 
 * @example
 * // Sử dụng hook trong component
 * const { mutate, isLoading, isError, error, isSuccess, data } = useCreateBlog({
 *   onSuccess: (data) => {
 *     // Xử lý khi tạo bài viết thành công
 *     console.log('Blog created successfully:', data);
 *     
 *     // Sử dụng URL để upload nội dung và thumbnail
 *     const { contentUploadUrl, thumbnailUploadUrl } = data.result;
 *     uploadContent(contentUploadUrl, htmlContent);
 *     uploadThumbnail(thumbnailUploadUrl, thumbnailFile);
 *   },
 *   onError: (error) => {
 *     // Xử lý khi có lỗi
 *     console.error('Failed to create blog:', error);
 *   }
 * });
 * 
 * // Gọi mutation để tạo bài viết
 * mutate({
 *   title: 'Tiêu đề bài viết',
 *   description: 'Mô tả ngắn về bài viết',
 *   contentMediaType: 'text/html',
 *   thumbnailMediaType: 'image/jpeg',
 *   point: 100,
 *   tags: ['tag1', 'tag2'],
 *   status: BlogStatus.DRAFT,
 *   authorType: AuthorType.SYSTEM
 * });
 */
export const useCreateBlog = (
  options?: UseMutationOptions<
    CreateBlogApiResponse,
    AxiosError,
    CreateBlogDto
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateBlogDto) => createBlog(data),
    onSuccess: (data, variables, context) => {
      // Invalidate các query liên quan để cập nhật danh sách blog
      queryClient.invalidateQueries({ queryKey: [BLOG_QUERY_KEYS.BLOG_LIST] });
      queryClient.invalidateQueries({ queryKey: [BLOG_QUERY_KEYS.MY_BLOG_LIST] });
      
      // Gọi callback onSuccess từ options nếu có
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    ...options,
  });
};
