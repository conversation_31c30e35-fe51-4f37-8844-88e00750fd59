import { useQuery, useMutation, UseQueryOptions, useQueryClient } from '@tanstack/react-query';
import {
  ApiResponse,
  BlogResponseDto,
  GetBlogsQueryDto,
  BlogListResponse,
} from '../types/blog.types';
import {
  UpdateBlogMediaDto
} from '../types/blog-update.types';
import { getBlogs, getBlogDetail, deleteBlog } from '../services/blog.service';
import { updateBlogMedia, submitBlogForReview, cancelBlogSubmit } from '../services/blog-update.service';
import { BLOG_QUERY_KEYS } from '../constants/blog-query-key';

/**
 * Hook để lấy danh sách bài viết đã được phê duyệt
 * @param params Query params
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBlogs = (
  params: GetBlogsQueryDto,
  options?: UseQueryOptions<ApiResponse<BlogListResponse>>
) => {
  return useQuery({
    queryKey: [BLOG_QUERY_KEYS.BLOG_LIST, params],
    queryFn: () => getBlogs(params),
    ...options,
  });
};

/**
 * Hook để lấy thông tin chi tiết của một bài viết
 * @param id ID của bài viết
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBlogDetail = (
  id: number | undefined,
  options?: UseQueryOptions<ApiResponse<BlogResponseDto>>
) => {
  return useQuery({
    queryKey: [BLOG_QUERY_KEYS.BLOG_DETAIL, id],
    queryFn: () => getBlogDetail(id as number),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook để xóa một bài viết
 * @returns Mutation result
 *
 * @example
 * const { mutate, isLoading } = useDeleteBlog();
 *
 * // Xóa blog
 * mutate(123, {
 *   onSuccess: () => {
 *     // Xử lý khi xóa thành công
 *     toast.success('Xóa bài viết thành công');
 *   },
 *   onError: (error) => {
 *     // Xử lý khi xóa thất bại
 *     toast.error('Xóa bài viết thất bại');
 *   }
 * });
 */
export const useDeleteBlog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [BLOG_QUERY_KEYS.DELETE_BLOG],
    mutationFn: (id: number) => deleteBlog(id),
    onSuccess: () => {
      // Invalidate các query liên quan để cập nhật dữ liệu
      queryClient.invalidateQueries({ queryKey: [BLOG_QUERY_KEYS.BLOG_LIST] });
    },
  });
};

/**
 * Hook để cập nhật media (nội dung hoặc thumbnail) cho bài viết
 * @returns Mutation result
 *
 * @example
 * const { mutate, isLoading } = useUpdateBlogMedia();
 *
 * // Cập nhật nội dung bài viết
 * mutate(
 *   {
 *     id: 123,
 *     data: {
 *       media_type: 'content',
 *       media_content_type: 'text/html'
 *     }
 *   },
 *   {
 *     onSuccess: (data) => {
 *       // Xử lý khi cập nhật thành công
 *       console.log('URL để upload nội dung:', data.result.uploadUrl);
 *     },
 *     onError: (error) => {
 *       // Xử lý khi cập nhật thất bại
 *       console.error('Lỗi khi cập nhật media:', error);
 *     }
 *   }
 * );
 */
export const useUpdateBlogMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [BLOG_QUERY_KEYS.UPDATE_BLOG_MEDIA],
    mutationFn: ({ id, data }: { id: number; data: UpdateBlogMediaDto }) =>
      updateBlogMedia(id, data),
    onSuccess: (_, variables) => {
      // Invalidate các query liên quan để cập nhật dữ liệu
      queryClient.invalidateQueries({
        queryKey: [BLOG_QUERY_KEYS.BLOG_DETAIL, variables.id]
      });
    },
  });
};

/**
 * Hook để gửi bài viết để kiểm duyệt (chuyển trạng thái từ DRAFT sang PENDING)
 * @returns Mutation result
 *
 * @example
 * const { mutate, isLoading } = useSubmitBlogForReview();
 *
 * // Gửi bài viết để kiểm duyệt
 * mutate(123, {
 *   onSuccess: () => {
 *     // Xử lý khi gửi thành công
 *     toast.success('Bài viết đã được gửi để kiểm duyệt');
 *   },
 *   onError: (error) => {
 *     // Xử lý khi gửi thất bại
 *     toast.error('Gửi bài viết để kiểm duyệt thất bại');
 *   }
 * });
 */
export const useSubmitBlogForReview = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [BLOG_QUERY_KEYS.SUBMIT_BLOG_FOR_REVIEW],
    mutationFn: (id: number) => submitBlogForReview(id),
    onSuccess: (_, id) => {
      // Invalidate các query liên quan để cập nhật dữ liệu
      queryClient.invalidateQueries({
        queryKey: [BLOG_QUERY_KEYS.BLOG_DETAIL, id]
      });
      queryClient.invalidateQueries({
        queryKey: [BLOG_QUERY_KEYS.MY_BLOG_LIST]
      });
    },
  });
};

/**
 * Hook để hủy gửi kiểm duyệt bài viết (chuyển trạng thái từ PENDING về DRAFT)
 * @returns Mutation result
 *
 * @example
 * const { mutate, isLoading } = useCancelBlogSubmit();
 *
 * // Hủy gửi kiểm duyệt bài viết
 * mutate(123, {
 *   onSuccess: () => {
 *     // Xử lý khi hủy thành công
 *     toast.success('Đã hủy gửi kiểm duyệt bài viết');
 *   },
 *   onError: (error) => {
 *     // Xử lý khi hủy thất bại
 *     toast.error('Hủy gửi kiểm duyệt bài viết thất bại');
 *   }
 * });
 */
export const useCancelBlogSubmit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [BLOG_QUERY_KEYS.CANCEL_BLOG_SUBMIT],
    mutationFn: (id: number) => cancelBlogSubmit(id),
    onSuccess: (_, id) => {
      // Invalidate các query liên quan để cập nhật dữ liệu
      queryClient.invalidateQueries({
        queryKey: [BLOG_QUERY_KEYS.BLOG_DETAIL, id]
      });
      queryClient.invalidateQueries({
        queryKey: [BLOG_QUERY_KEYS.MY_BLOG_LIST]
      });
    },
  });
};
