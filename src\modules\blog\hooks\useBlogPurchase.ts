import { useQuery, useMutation, UseQueryOptions, useQueryClient } from '@tanstack/react-query';
import { checkBlogPurchaseStatus, getPurchasedBlogs, purchaseBlog } from '../services/blog-purchase.service';
import {
  BlogPurchaseStatusApiResponse,
  GetPurchasedBlogsQueryDto,
} from '../types/blog-purchase.types';
import { ApiResponse, BlogListResponse } from '../types/blog.types';

// Query keys cho các API liên quan đến Blog Purchases
export const BLOG_PURCHASE_QUERY_KEYS = {
  PURCHASED_BLOGS: 'purchasedBlogs',
  BLOG_PURCHASE_STATUS: 'blogPurchaseStatus',
  PURCHASE_BLOG: 'purchaseBlog',
};

/**
 * Hook để lấy danh sách blog đã mua của người dùng
 * @param params Tham số truy vấn
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetPurchasedBlogs = (
  params?: GetPurchasedBlogsQueryDto,
  options?: UseQueryOptions<ApiResponse<BlogListResponse>>
) => {
  return useQuery({
    queryKey: [BLOG_PURCHASE_QUERY_KEYS.PURCHASED_BLOGS, params],
    queryFn: () => getPurchasedBlogs(params),
    ...options,
  });
};

/**
 * Hook để kiểm tra trạng thái mua blog của người dùng
 * @param blogId ID của blog cần kiểm tra
 * @param options TanStack Query options
 * @returns Query result
 */
export const useCheckBlogPurchaseStatus = (
  blogId: number | undefined,
  options?: UseQueryOptions<BlogPurchaseStatusApiResponse>
) => {
  return useQuery({
    queryKey: [BLOG_PURCHASE_QUERY_KEYS.BLOG_PURCHASE_STATUS, blogId],
    queryFn: () => checkBlogPurchaseStatus(blogId as number),
    enabled: !!blogId,
    ...options,
  });
};

/**
 * Hook để mua bài viết bằng point
 *
 * @returns Mutation result
 *
 * @example
 * const { mutate, isLoading } = usePurchaseBlog();
 *
 * // Mua bài viết
 * mutate(123, {
 *   onSuccess: () => {
 *     // Xử lý khi mua thành công
 *     toast.success('Mua bài viết thành công');
 *     // Cập nhật lại trạng thái mua
 *     queryClient.invalidateQueries([BLOG_PURCHASE_QUERY_KEYS.BLOG_PURCHASE_STATUS, 123]);
 *   },
 *   onError: (error) => {
 *     // Xử lý khi mua thất bại
 *     toast.error('Mua bài viết thất bại');
 *   }
 * });
 */
export const usePurchaseBlog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [BLOG_PURCHASE_QUERY_KEYS.PURCHASE_BLOG],
    mutationFn: (blogId: number) => purchaseBlog(blogId),
    onSuccess: (_, blogId) => {
      // Invalidate các query liên quan để cập nhật dữ liệu
      queryClient.invalidateQueries({
        queryKey: [BLOG_PURCHASE_QUERY_KEYS.BLOG_PURCHASE_STATUS, blogId]
      });
      queryClient.invalidateQueries({
        queryKey: [BLOG_PURCHASE_QUERY_KEYS.PURCHASED_BLOGS]
      });
    },
  });
};
