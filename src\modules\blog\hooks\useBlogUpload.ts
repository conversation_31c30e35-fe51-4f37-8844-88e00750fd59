import { useState } from 'react';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { useFileUpload } from '@/shared/hooks/common';
import { AxiosError } from 'axios';

/**
 * Tham số cho hook useBlogContentUpload
 */
export interface BlogContentUploadParams {
  /**
   * Nội dung HTML của bài viết
   */
  content: string;

  /**
   * URL để upload nội dung
   */
  contentUploadUrl: string;

  /**
   * Callback khi tiến trình upload thay đổi
   */
  onProgress?: (progress: number) => void;
}

/**
 * Tham số cho hook useBlogThumbnailUpload
 */
export interface BlogThumbnailUploadParams {
  /**
   * File hình ảnh thumbnail
   */
  thumbnailFile: File;

  /**
   * URL để upload thumbnail
   */
  thumbnailUploadUrl: string;

  /**
   * Callback khi tiến trình upload thay đổi
   */
  onProgress?: (progress: number) => void;
}

/**
 * Tham số cho hook useBlogUpload
 */
export interface BlogUploadParams {
  /**
   * Nội dung HTML của bài viết
   */
  content: string;

  /**
   * File hình ảnh thumbnail
   */
  thumbnailFile: File;

  /**
   * URL để upload nội dung
   */
  contentUploadUrl: string;

  /**
   * URL để upload thumbnail
   */
  thumbnailUploadUrl: string;

  /**
   * Callback khi tiến trình upload nội dung thay đổi
   */
  onContentProgress?: (progress: number) => void;

  /**
   * Callback khi tiến trình upload thumbnail thay đổi
   */
  onThumbnailProgress?: (progress: number) => void;
}

/**
 * Kết quả upload blog
 */
export interface BlogUploadResult {
  /**
   * URL của nội dung đã upload
   */
  contentUrl: string;

  /**
   * URL của thumbnail đã upload
   */
  thumbnailUrl: string;
}

/**
 * Hook để upload nội dung bài viết
 * 
 * @returns Mutation object
 * 
 * @example
 * const { mutate, isLoading, error } = useBlogContentUpload();
 * 
 * // Upload nội dung
 * mutate({
 *   content: '<p>Nội dung HTML của bài viết</p>',
 *   contentUploadUrl: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...',
 *   onProgress: (progress) => console.log(`Upload progress: ${progress}%`)
 * });
 */
export const useBlogContentUpload = (
  options?: UseMutationOptions<string, AxiosError, BlogContentUploadParams>
) => {
  const { uploadToUrl } = useFileUpload();
  const [uploadProgress, setUploadProgress] = useState(0);

  const mutation = useMutation({
    mutationFn: async ({ content, contentUploadUrl, onProgress }: BlogContentUploadParams) => {
      // Tạo Blob từ nội dung HTML
      const blob = new Blob([content], { type: 'text/html' });
      
      // Tạo File từ Blob
      const file = new File([blob], 'content.html', { type: 'text/html' });
      
      // Upload file lên contentUploadUrl
      return uploadToUrl({
        file,
        presignedUrl: contentUploadUrl,
        onUploadProgress: (progress) => {
          setUploadProgress(progress);
          if (onProgress) {
            onProgress(progress);
          }
        }
      });
    },
    ...options
  });

  return {
    ...mutation,
    uploadProgress
  };
};

/**
 * Hook để upload thumbnail của bài viết
 * 
 * @returns Mutation object
 * 
 * @example
 * const { mutate, isLoading, error } = useBlogThumbnailUpload();
 * 
 * // Upload thumbnail
 * mutate({
 *   thumbnailFile: file, // File từ input type="file"
 *   thumbnailUploadUrl: 'https://cdn-storage.example.com/temp/uploads/123456789/thumbnail.jpg?signature=def456...',
 *   onProgress: (progress) => console.log(`Upload progress: ${progress}%`)
 * });
 */
export const useBlogThumbnailUpload = (
  options?: UseMutationOptions<string, AxiosError, BlogThumbnailUploadParams>
) => {
  const { uploadToUrl } = useFileUpload();
  const [uploadProgress, setUploadProgress] = useState(0);

  const mutation = useMutation({
    mutationFn: async ({ thumbnailFile, thumbnailUploadUrl, onProgress }: BlogThumbnailUploadParams) => {
      // Upload file lên thumbnailUploadUrl
      return uploadToUrl({
        file: thumbnailFile,
        presignedUrl: thumbnailUploadUrl,
        onUploadProgress: (progress) => {
          setUploadProgress(progress);
          if (onProgress) {
            onProgress(progress);
          }
        }
      });
    },
    ...options
  });

  return {
    ...mutation,
    uploadProgress
  };
};

/**
 * Hook để upload cả nội dung và thumbnail của bài viết
 * 
 * @returns Mutation object
 * 
 * @example
 * const { mutate, isLoading, error, contentProgress, thumbnailProgress } = useBlogUpload();
 * 
 * // Upload cả nội dung và thumbnail
 * mutate({
 *   content: '<p>Nội dung HTML của bài viết</p>',
 *   thumbnailFile: file, // File từ input type="file"
 *   contentUploadUrl: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...',
 *   thumbnailUploadUrl: 'https://cdn-storage.example.com/temp/uploads/123456789/thumbnail.jpg?signature=def456...',
 *   onContentProgress: (progress) => console.log(`Content upload progress: ${progress}%`),
 *   onThumbnailProgress: (progress) => console.log(`Thumbnail upload progress: ${progress}%`)
 * });
 */
export const useBlogUpload = (
  options?: UseMutationOptions<BlogUploadResult, AxiosError, BlogUploadParams>
) => {
  const { uploadToUrl } = useFileUpload();
  const [contentProgress, setContentProgress] = useState(0);
  const [thumbnailProgress, setThumbnailProgress] = useState(0);

  const mutation = useMutation({
    mutationFn: async ({
      content,
      thumbnailFile,
      contentUploadUrl,
      thumbnailUploadUrl,
      onContentProgress,
      onThumbnailProgress
    }: BlogUploadParams) => {
      try {
        // Tạo Blob từ nội dung HTML
        const blob = new Blob([content], { type: 'text/html' });
        
        // Tạo File từ Blob
        const contentFile = new File([blob], 'content.html', { type: 'text/html' });
        
        // Upload nội dung và thumbnail song song
        const [contentUrl, thumbnailUrl] = await Promise.all([
          // Upload nội dung
          uploadToUrl({
            file: contentFile,
            presignedUrl: contentUploadUrl,
            onUploadProgress: (progress) => {
              setContentProgress(progress);
              if (onContentProgress) {
                onContentProgress(progress);
              }
            }
          }),
          
          // Upload thumbnail
          uploadToUrl({
            file: thumbnailFile,
            presignedUrl: thumbnailUploadUrl,
            onUploadProgress: (progress) => {
              setThumbnailProgress(progress);
              if (onThumbnailProgress) {
                onThumbnailProgress(progress);
              }
            }
          })
        ]);
        
        // Trả về kết quả
        return {
          contentUrl,
          thumbnailUrl
        };
      } catch (error) {
        console.error('Error uploading blog content and thumbnail:', error);
        throw error;
      }
    },
    ...options
  });

  return {
    ...mutation,
    contentProgress,
    thumbnailProgress
  };
};
