import { useState } from 'react';
import { useMutation, UseMutationOptions, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useCreateBlog } from './useBlogCreate';
import { useBlogUpload } from './useBlogUpload';
import { CreateBlogDto, CreateBlogApiResponse } from '../types/blog-create.types';
import { BLOG_QUERY_KEYS } from '../constants/blog-query-key';

/**
 * Trạng thái của quá trình tạo và upload blog
 */
export enum CreateBlogStatus {
  IDLE = 'idle',
  CREATING = 'creating',
  UPLOADING_CONTENT = 'uploading_content',
  UPLOADING_THUMBNAIL = 'uploading_thumbnail',
  COMPLETED = 'completed',
  ERROR = 'error'
}

/**
 * Tham số cho hook useCreateAndUploadBlog
 */
export interface CreateAndUploadBlogParams {
  /**
   * Dữ liệu blog cần tạo
   */
  blogData: CreateBlogDto;

  /**
   * Nội dung HTML của bài viết
   */
  content: string;

  /**
   * File hình ảnh thumbnail
   */
  thumbnailFile: File;
}

/**
 * Kết quả của quá trình tạo và upload blog
 */
export interface CreateAndUploadBlogResult {
  /**
   * Kết quả từ API tạo blog
   */
  createResult: CreateBlogApiResponse;

  /**
   * URL của nội dung đã upload
   */
  contentUrl: string;

  /**
   * URL của thumbnail đã upload
   */
  thumbnailUrl: string;
}

/**
 * Hook để tạo blog mới và upload nội dung/thumbnail trong một quy trình duy nhất
 * 
 * @returns Mutation object và thông tin về trạng thái
 * 
 * @example
 * const {
 *   mutate,
 *   isLoading,
 *   error,
 *   status,
 *   contentProgress,
 *   thumbnailProgress
 * } = useCreateAndUploadBlog();
 * 
 * // Tạo blog và upload nội dung/thumbnail
 * mutate({
 *   blogData: {
 *     title: 'Tiêu đề bài viết',
 *     description: 'Mô tả ngắn về bài viết',
 *     contentMediaType: 'text/html',
 *     thumbnailMediaType: 'image/jpeg',
 *     point: 100,
 *     tags: ['tag1', 'tag2'],
 *     status: BlogStatus.DRAFT,
 *     authorType: AuthorType.SYSTEM
 *   },
 *   content: '<p>Nội dung HTML của bài viết</p>',
 *   thumbnailFile: file // File từ input type="file"
 * });
 */
export const useCreateAndUploadBlog = (
  options?: UseMutationOptions<CreateAndUploadBlogResult, AxiosError, CreateAndUploadBlogParams>
) => {
  const queryClient = useQueryClient();
  const [status, setStatus] = useState<CreateBlogStatus>(CreateBlogStatus.IDLE);
  const [contentProgress, setContentProgress] = useState(0);
  const [thumbnailProgress, setThumbnailProgress] = useState(0);

  // Hook để tạo blog
  const createBlogMutation = useCreateBlog();
  
  // Hook để upload nội dung và thumbnail
  const uploadBlogMutation = useBlogUpload();

  const mutation = useMutation({
    mutationFn: async ({ blogData, content, thumbnailFile }: CreateAndUploadBlogParams) => {
      try {
        // Bước 1: Tạo blog mới
        setStatus(CreateBlogStatus.CREATING);
        const createResult = await createBlogMutation.mutateAsync(blogData);

        // Lấy URL để upload nội dung và thumbnail
        if (!createResult.result) {
          throw new Error('Failed to get upload URLs from API response');
        }
        const { contentUploadUrl, thumbnailUploadUrl } = createResult.result;

        // Bước 2: Upload nội dung và thumbnail
        setStatus(CreateBlogStatus.UPLOADING_CONTENT);
        const { contentUrl, thumbnailUrl } = await uploadBlogMutation.mutateAsync({
          content,
          thumbnailFile,
          contentUploadUrl,
          thumbnailUploadUrl,
          onContentProgress: (progress) => {
            setContentProgress(progress);
            setStatus(CreateBlogStatus.UPLOADING_CONTENT);
          },
          onThumbnailProgress: (progress) => {
            setThumbnailProgress(progress);
            setStatus(CreateBlogStatus.UPLOADING_THUMBNAIL);
          }
        });

        // Bước 3: Hoàn thành
        setStatus(CreateBlogStatus.COMPLETED);

        // Trả về kết quả
        return {
          createResult,
          contentUrl,
          thumbnailUrl
        };
      } catch (error) {
        console.error('Error creating and uploading blog:', error);
        setStatus(CreateBlogStatus.ERROR);
        throw error;
      }
    },
    onSuccess: (data, variables, context) => {
      // Invalidate các query liên quan để cập nhật danh sách blog
      queryClient.invalidateQueries({ queryKey: [BLOG_QUERY_KEYS.BLOG_LIST] });
      queryClient.invalidateQueries({ queryKey: [BLOG_QUERY_KEYS.MY_BLOG_LIST] });
      
      // Gọi callback onSuccess từ options nếu có
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    ...options
  });

  return {
    ...mutation,
    status,
    contentProgress,
    thumbnailProgress,
    isCreating: status === CreateBlogStatus.CREATING,
    isUploadingContent: status === CreateBlogStatus.UPLOADING_CONTENT,
    isUploadingThumbnail: status === CreateBlogStatus.UPLOADING_THUMBNAIL,
    isCompleted: status === CreateBlogStatus.COMPLETED
  };
};
