{"title": "Blog", "description": "Share and discover knowledge", "myBlogs": "My Blogs", "createBlog": "Create Blog", "editBlog": "Edit Blog", "purchasedBlogs": "Purchased Blogs", "noResults": "No blogs found.", "purchaseManagement": {"title": "Blog Management", "description": "Manage your purchased blogs, create new blogs, and track your blog purchase history.", "purchasedBlogs": {"title": "Purchased Blogs", "description": "View the list of blogs you have purchased.", "totalBlogs": "Total blogs", "manage": "View list", "recent": "Recent"}, "allBlogs": {"title": "All Blogs", "description": "View the list of all available blogs.", "totalBlogs": "Total blogs", "manage": "View list"}, "personalBlogs": {"title": "Personal Blogs", "description": "View and manage your personal blogs.", "totalBlogs": "Total blogs", "manage": "View list"}}, "status": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected", "draft": "Draft", "unknown": "Unknown"}, "table": {"title": "Title", "content": "Content", "point": "Points", "viewCount": "Views", "tags": "Tags", "createdAt": "Created At", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "author": "Author", "like": "<PERSON>s"}, "deleteConfirm": {"title": "Confirm Delete Blog", "message": "Are you sure you want to delete this blog?", "confirm": "Delete", "cancel": "Cancel"}, "detail": {"author": "Author", "createdAt": "Created at", "views": "Views", "likes": "<PERSON>s", "tags": "Tags", "relatedBlogs": "Related Blogs", "comments": "Comments", "writeComment": "Write a comment", "submitComment": "Submit", "deleteComment": "Delete", "replyComment": "Reply", "purchaseToView": "Purchase to view full content", "purchaseButton": "Purchase for {{points}} points", "purchaseSuccess": "Blog purchased successfully", "purchaseError": "Failed to purchase blog"}, "form": {"title": "Title", "titlePlaceholder": "Enter blog title", "content": "Content", "contentPlaceholder": "Write your blog content here", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "thumbnailPlaceholder": "Upload thumbnail image", "tags": "Tags", "tagsPlaceholder": "Add tags", "point": "Points", "pointPlaceholder": "Set points required to access", "submit": "Submit", "save": "Save", "cancel": "Cancel", "draft": "Save as Draft"}, "validation": {"titleRequired": "Title is required", "contentRequired": "Content is required", "thumbnailRequired": "Thumbnail is required", "pointRequired": "Points value is required", "pointMin": "Points must be at least {{min}}", "pointMax": "Points cannot exceed {{max}}"}}