import React, { useState, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Typography,
  Textarea,
  Loading
} from '@/shared/components/common';
import { EmailBuilder } from '@/shared/components/email-builder';
import { useCreateBlog } from '../hooks/useBlogCreate';
import { useBlogUpload } from '../hooks/useBlogUpload';
import { AuthorType, BlogStatus } from '../types/blog.types';
import { useFormErrors } from '@/shared/hooks';
import { CreateBlogSchema } from '../schemas';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang tạo blog mới
 */
const BlogCreatePage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { formRef, setFormErrors } = useFormErrors();

  // State cho nội dung HTML và file thumbnail
  const [content, setContent] = useState<string>('');
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string>('');
  const [tags, setTags] = useState<string[]>([]);
  const [showEmailBuilder, setShowEmailBuilder] = useState<boolean>(false);
  const [showForm, setShowForm] = useState<boolean>(true);

  // State cho tiến trình upload
  const [uploadingContent, setUploadingContent] = useState(false);
  const [uploadingThumbnail, setUploadingThumbnail] = useState(false);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Sử dụng hook để tạo blog
  const {
    mutate: createBlog,
    isPending: isCreating,
    error: createError
  } = useCreateBlog({
    onSuccess: (data) => {
      if (!data.result) {
        NotificationUtil.error({
          title: t('blog.createError', 'Lỗi khi tạo bài viết'),
          message: t('blog.createErrorNoResult', 'Không nhận được kết quả từ API')
        });
        return;
      }

      const { contentUploadUrl, thumbnailUploadUrl } = data.result;

      // Upload nội dung và thumbnail
      uploadContent(contentUploadUrl, thumbnailUploadUrl);
    },
    onError: (error) => {
      NotificationUtil.error({
        title: t('blog.createError', 'Lỗi khi tạo bài viết'),
        message: error.message || t('blog.createErrorDesc', 'Đã xảy ra lỗi khi tạo bài viết. Vui lòng thử lại.')
      });

      // Hiển thị lỗi form nếu có
      if (error.response?.data) {
        const errorData = error.response.data as Record<string, unknown>;
        if (errorData.errors && typeof errorData.errors === 'object') {
          setFormErrors(errorData.errors as Record<string, string>);
        }
      }
    }
  });

  // Sử dụng hook để upload nội dung và thumbnail
  const {
    mutate: uploadContentMutation,
    isPending: isUploadingContent,
    contentProgress: uploadContentProgress,
    thumbnailProgress: uploadThumbnailProgress
  } = useBlogUpload();

  // Xử lý khi chọn file thumbnail
  const handleThumbnailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      setThumbnailFile(null);
      setThumbnailPreview('');
      return;
    }

    const file = files[0];
    setThumbnailFile(file);

    // Tạo URL preview cho thumbnail
    const previewUrl = URL.createObjectURL(file);
    setThumbnailPreview(previewUrl);
  }, []);

  // Xử lý khi thêm tag
  const handleAddTag = useCallback((tag: string) => {
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag]);
    }
  }, [tags]);

  // Xử lý khi xóa tag
  const handleRemoveTag = useCallback((tag: string) => {
    setTags(tags.filter(t => t !== tag));
  }, [tags]);

  // Xử lý khi thay đổi nội dung email builder
  const handleContentChange = useCallback((html: string) => {
    setContent(html);
    // Log nội dung đã lưu được
    console.log('Nội dung đã lưu:', html);
  }, []);

  // Xử lý khi mở email builder
  const handleOpenEmailBuilder = useCallback(() => {
    // Ẩn form và hiển thị email builder
    setShowForm(false);
    setShowEmailBuilder(true);
  }, []);

  // Xử lý upload nội dung và thumbnail
  const uploadContent = useCallback((contentUploadUrl: string, thumbnailUploadUrl: string) => {
    if (!content || !thumbnailFile) {
      NotificationUtil.error({
        title: t('blog.uploadError', 'Lỗi khi tải lên'),
        message: t('blog.uploadErrorMissingData', 'Thiếu nội dung hoặc hình ảnh thumbnail')
      });
      return;
    }

    uploadContentMutation({
      content,
      thumbnailFile,
      contentUploadUrl,
      thumbnailUploadUrl,
      onContentProgress: (progress) => {
        setUploadingContent(true);
        console.log('Content upload progress:', progress);
      },
      onThumbnailProgress: (progress) => {
        setUploadingThumbnail(true);
        console.log('Thumbnail upload progress:', progress);
      }
    }, {
      onSuccess: () => {
        NotificationUtil.success({
          title: t('blog.createSuccess', 'Tạo bài viết thành công'),
          message: t('blog.createSuccessDesc', 'Bài viết của bạn đã được tạo thành công.')
        });

        // Chuyển hướng đến trang danh sách blog cá nhân
        navigate('/blog/personal');
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('blog.uploadError', 'Lỗi khi tải lên'),
          message: error.message || t('blog.uploadErrorDesc', 'Đã xảy ra lỗi khi tải lên nội dung hoặc hình ảnh. Vui lòng thử lại.')
        });
      }
    });
  }, [content, thumbnailFile, uploadContentMutation, navigate, t]);

  // Xử lý khi submit form
  const handleSubmit = useCallback((values: Record<string, unknown>) => {
    // Validate dữ liệu
    try {
      // Kiểm tra nội dung và thumbnail
      if (!content || content === '<p></p>' || content === '<p><br></p>' ||
          (content.includes('<!DOCTYPE html>') && !content.includes('<body>') && !content.includes('</body>'))) {
        NotificationUtil.error({
          title: t('blog.validationError', 'Lỗi dữ liệu'),
          message: t('blog.contentRequired', 'Vui lòng nhập nội dung bài viết')
        });
        return;
      }

      // Nếu content là HTML đầy đủ với CSS nhúng, trích xuất phần body
      let processedContent = content;
      if (content.includes('<!DOCTYPE html>') && content.includes('<body>') && content.includes('</body>')) {
        const bodyMatch = content.match(/<body>([\s\S]*?)<\/body>/i);
        if (bodyMatch && bodyMatch[1]) {
          processedContent = bodyMatch[1].trim();
          console.log('Đã trích xuất nội dung từ body:', processedContent);
        }
      }

      if (!thumbnailFile) {
        NotificationUtil.error({
          title: t('blog.validationError', 'Lỗi dữ liệu'),
          message: t('blog.thumbnailRequired', 'Vui lòng chọn hình ảnh thumbnail')
        });
        return;
      }

      // Tạo dữ liệu blog
      const blogData = {
        title: String(values.title || ''),
        description: String(values.description || ''),
        contentMediaType: 'text/html',
        thumbnailMediaType: thumbnailFile.type,
        point: Number(values.point) || 0,
        tags: Array.isArray(tags) ? tags : [],
        status: (values.status as string || BlogStatus.DRAFT) as BlogStatus,
        authorType: (values.authorType as string || AuthorType.USER) as AuthorType
      };

      // Validate dữ liệu với Zod schema
      CreateBlogSchema.parse(blogData);

      // Lưu nội dung đã xử lý vào biến state để sử dụng khi upload
      if (processedContent !== content) {
        console.log('Đã cập nhật nội dung đã xử lý:', processedContent);
        setContent(processedContent);
      }

      // Gọi mutation để tạo blog
      createBlog(blogData);
    } catch (error: unknown) {
      // Hiển thị lỗi validation
      const errorMessage = error instanceof Error
        ? error.message
        : t('blog.validationErrorDesc', 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại.');

      NotificationUtil.error({
        title: t('blog.validationError', 'Lỗi dữ liệu'),
        message: errorMessage
      });

      // Set form errors nếu có
      if (error && typeof error === 'object' && 'errors' in error && error.errors && typeof error.errors === 'object') {
        setFormErrors(error.errors as Record<string, string>);
      }
    }
  }, [content, thumbnailFile, tags, createBlog, t, setFormErrors]);

  // Xử lý khi hủy
  const handleCancel = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  // Kiểm tra trạng thái loading
  const isLoading = isCreating || isUploadingContent || uploadingContent || uploadingThumbnail;

  // Thêm CSS cho animation
  const animationStyles = `
    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    .email-builder-enter {
      animation: slideDown 0.5s ease-in-out;
    }

    .email-builder-exit {
      animation: fadeIn 0.5s ease-in-out reverse;
    }
  `;

  return (
    <div>
      <style>{animationStyles}</style>

      {/* Email Builder */}
      {showEmailBuilder && (
        <div className="fixed inset-0 bg-white z-50 email-builder-container email-builder-enter">
          <div className="flex flex-col h-full">
            <div className="bg-gray-100 p-4 flex justify-between items-center shadow-md">
              <Typography variant="h5">
                {t('blog.contentEditor', 'Trình soạn thảo nội dung')}
              </Typography>
              <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Thêm class animation trước khi đóng
                    const emailBuilderElement = document.querySelector('.email-builder-container');
                    if (emailBuilderElement) {
                      emailBuilderElement.classList.remove('email-builder-enter');
                      emailBuilderElement.classList.add('email-builder-exit');

                      // Đợi animation hoàn thành rồi mới ẩn
                      setTimeout(() => {
                        setShowEmailBuilder(false);
                        setShowForm(true);
                      }, 300);
                    } else {
                      setShowEmailBuilder(false);
                      setShowForm(true);
                    }
                  }}
                  title="Lưu"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                    <polyline points="7 3 7 8 15 8"></polyline>
                  </svg>
                  {t('common.saveAndClose', 'Lưu')}
                </Button>
            </div>
            <div className="flex-grow overflow-hidden">
              <div className="relative h-full">
                <EmailBuilder
                  initialValue={content}
                  onContentChange={handleContentChange}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form tạo bài viết */}
      {showForm && (
        <Card className="shadow-md">
          <div>
            <Form
              ref={formRef}
              onSubmit={handleSubmit}
              className="space-y-6"
            >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                {/* Tiêu đề */}
                <FormItem
                  label={t('blog.title', 'Tiêu đề')}
                  name="title"
                  required
                >
                  <Input
                    placeholder={t('blog.enterTitle', 'Nhập tiêu đề bài viết')}
                    fullWidth
                  />
                </FormItem>

                {/* Mô tả */}
                <FormItem
                  label={t('blog.description', 'Mô tả')}
                  name="description"
                  required
                >
                  <Textarea
                    placeholder={t('blog.enterDescription', 'Nhập mô tả ngắn về bài viết')}
                    rows={3}
                  />
                </FormItem>

                {/* Tags */}
                <FormItem
                  label={t('blog.tags', 'Thẻ')}
                >
                  <div className="space-y-2">
                    <div className="flex">
                      <Input
                        id="tag-input"
                        placeholder={t('blog.enterTags', 'Nhập thẻ và nhấn Enter')}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            const input = e.target as HTMLInputElement;
                            handleAddTag(input.value);
                            input.value = '';
                          }
                        }}
                        fullWidth
                      />
                      <Button
                        variant="outline"
                        className="ml-2"
                        onClick={() => {
                          const input = document.getElementById('tag-input') as HTMLInputElement;
                          if (input.value) {
                            handleAddTag(input.value);
                            input.value = '';
                          }
                        }}
                      >
                        {t('blog.addTag', 'Thêm')}
                      </Button>
                    </div>

                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {tags.map((tag) => (
                          <div
                            key={tag}
                            className="bg-primary-100 text-primary-700 px-2 py-1 rounded-md flex items-center"
                          >
                            <span>{tag}</span>
                            <button
                              type="button"
                              className="ml-1 text-primary-500 hover:text-primary-700"
                              onClick={() => handleRemoveTag(tag)}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </FormItem>

                {/* Điểm */}
                <FormItem
                  label={t('blog.point', 'Điểm')}
                  name="point"
                >
                  <Input
                    type="number"
                    min={0}
                    placeholder={t('blog.enterPoint', 'Nhập số điểm (0 nếu miễn phí)')}
                    defaultValue="0"
                    fullWidth
                  />
                </FormItem>

                {/* Trạng thái */}
                <FormItem
                  label={t('blog.status', 'Trạng thái')}
                  name="status"
                >
                  <select
                    className="w-full border border-gray-300 rounded-md p-2"
                    defaultValue={BlogStatus.DRAFT}
                  >
                    <option value={BlogStatus.DRAFT}>{t('blog.status.draft', 'Bản nháp')}</option>
                    <option value={BlogStatus.PENDING}>{t('blog.status.pending', 'Chờ duyệt')}</option>
                  </select>
                </FormItem>

                {/* Loại tác giả */}
                <FormItem
                  label={t('blog.authorType', 'Loại tác giả')}
                  name="authorType"
                >
                  <select
                    className="w-full border border-gray-300 rounded-md p-2"
                    defaultValue={AuthorType.USER}
                  >
                    <option value={AuthorType.USER}>{t('blog.authorType.user', 'Người dùng')}</option>
                    <option value={AuthorType.SYSTEM}>{t('blog.authorType.system', 'Hệ thống')}</option>
                  </select>
                </FormItem>
              </div>

              <div>
                {/* Thumbnail */}
                <FormItem
                  label={t('blog.thumbnail', 'Hình ảnh thumbnail')}
                  required
                >
                  <div className="flex flex-col items-start">
                    <div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleThumbnailChange}
                        className="hidden"
                      />
                      <Button
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        {thumbnailFile ? t('blog.changeImage', 'Đổi ảnh') : t('blog.selectImage', 'Chọn ảnh')}
                      </Button>
                    </div>

                    {thumbnailPreview && (
                      <div className="w-32 h-32 relative mt-4">
                        <img
                          src={thumbnailPreview}
                          alt="Thumbnail preview"
                          className="w-full h-full object-cover rounded-md"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute top-0 right-0 bg-white/80 rounded-full p-1"
                          onClick={() => {
                            setThumbnailFile(null);
                            setThumbnailPreview('');
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                          </svg>
                        </Button>
                      </div>
                    )}
                  </div>
                </FormItem>

                {/* Nội dung */}
                <FormItem
                  label={t('blog.content', 'Nội dung')}
                  required
                >
                  <div className="flex flex-col items-center justify-center border rounded-md p-6" style={{ height: '300px' }}>
                    <Typography variant="body1" className="mb-4 text-center">
                      {content ? t('blog.contentCreated', 'Nội dung đã được tạo') : t('blog.noContent', 'Chưa có nội dung')}
                    </Typography>
                    <Button
                      variant="primary"
                      onClick={handleOpenEmailBuilder}
                      className="flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {content ? t('blog.editContent', 'Chỉnh sửa nội dung') : t('blog.createContent', 'Tạo nội dung')}
                    </Button>
                    {content && (
                      <div className="mt-4 text-center">
                        <Typography variant="caption" className="text-green-600">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          {t('blog.contentSaved', 'Nội dung đã được lưu')}
                        </Typography>
                      </div>
                    )}
                  </div>
                </FormItem>
              </div>
            </div>

            {/* Trạng thái upload */}
            {isLoading && (
              <div className="bg-gray-50 p-4 rounded-md mb-4">
                <div className="flex items-center mb-2">
                  <Loading size="sm" className="mr-2" />
                  <Typography variant="body2">
                    {isCreating && t('blog.creating', 'Đang tạo bài viết...')}
                    {isUploadingContent && t('blog.uploadingContent', 'Đang tải lên nội dung...')}
                    {uploadingContent && t('blog.uploadingContent', 'Đang tải lên nội dung...')}
                    {uploadingThumbnail && t('blog.uploadingThumbnail', 'Đang tải lên hình ảnh thumbnail...')}
                  </Typography>
                </div>

                {(uploadingContent || isUploadingContent) && (
                  <div className="mb-2">
                    <Typography variant="caption" className="mb-1">
                      {t('blog.contentProgress', 'Tiến trình tải lên nội dung')}
                    </Typography>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-500 h-2 rounded-full"
                        style={{ width: `${uploadContentProgress || 0}%` }}
                      />
                    </div>
                  </div>
                )}

                {(uploadingThumbnail || isUploadingContent) && (
                  <div>
                    <Typography variant="caption" className="mb-1">
                      {t('blog.thumbnailProgress', 'Tiến trình tải lên thumbnail')}
                    </Typography>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-500 h-2 rounded-full"
                        style={{ width: `${uploadThumbnailProgress || 0}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Lỗi */}
            {createError && (
              <div className="bg-red-50 text-red-700 p-4 rounded-md mb-4">
                <Typography variant="body2">
                  {createError.message || t('blog.createError', 'Đã xảy ra lỗi khi tạo bài viết. Vui lòng thử lại.')}
                </Typography>
              </div>
            )}

            {/* Buttons */}
            <div className="flex justify-end space-x-4 pt-4">
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
              >
                {t('common.cancel', 'Hủy')}
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isLoading}
              >
                {t('common.create', 'Tạo')}
              </Button>
            </div>
          </Form>
        </div>
      </Card>
      )}
    </div>
  );
};

export default BlogCreatePage;
