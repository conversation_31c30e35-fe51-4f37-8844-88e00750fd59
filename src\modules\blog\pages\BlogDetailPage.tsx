import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import BlogDetail from '../components/BlogDetail';
import { useGetBlogDetail } from '../hooks/useBlogList';
import { usePurchaseBlog } from '../hooks/useBlogPurchase';
import { useTheme } from '@/shared/contexts';
import { useSmartNotification } from '@/shared/hooks/common';

/**
 * Trang hiển thị chi tiết blog
 */
const BlogDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // State để quản lý trạng thái hiển thị nội dung đầy đủ và mua blog
  const [showFullContent, setShowFullContent] = useState<boolean>(false);
  const [purchaseLoading, setPurchaseLoading] = useState<boolean>(false);

  // Sử dụng hook theme mới
  useTheme();

  // Sử dụng hook useSmartNotification
  const notification = useSmartNotification();

  // Chuyển đổi id từ string sang number
  const blogId = id ? parseInt(id) : undefined;

  // Sử dụng hook useGetBlogDetail từ useBlogList.ts
  const { data, isLoading, error } = useGetBlogDetail(blogId);

  // Sử dụng hook usePurchaseBlog để mua blog
  const { mutate: purchaseBlogMutation } = usePurchaseBlog();

  // Xử lý khi bấm nút Mua ngay
  const handlePurchase = () => {
    if (!blogId) return;

    setPurchaseLoading(true);

    purchaseBlogMutation(blogId, {
      onSuccess: () => {
        // Cập nhật trạng thái hiển thị nội dung đầy đủ
        setShowFullContent(true);
        setPurchaseLoading(false);

        // Hiển thị thông báo thành công
        notification.success({
          title: t('blog.purchaseSuccess', 'Mua blog thành công'),
          message: t(
            'blog.purchaseSuccessDesc',
            'Bạn đã có thể xem nội dung đầy đủ của blog này.'
          )
        });
      },
      onError: (error) => {
        setPurchaseLoading(false);

        // Hiển thị thông báo lỗi
        notification.error({
          title: t('blog.purchaseError', 'Mua blog thất bại'),
          message: error instanceof Error
            ? error.message
            : t('blog.purchaseErrorDesc', 'Có lỗi xảy ra khi mua blog. Vui lòng thử lại sau.')
        });
      }
    });
  };

  // Xử lý khi bấm nút Hủy
  const handleCancel = () => {
    navigate('/blog');
  };

  // Sử dụng dữ liệu từ API trực tiếp, không cần chuyển đổi
  const blog = data?.result || null;

  return (
    <div>
      {/* Blog detail */}
      <BlogDetail
        blog={blog}
        loading={isLoading}
        error={error ? 'Không thể tải thông tin blog. Vui lòng thử lại sau.' : null}
        showFullContent={showFullContent}
        onPurchase={handlePurchase}
        onCancel={handleCancel}
        purchaseLoading={purchaseLoading}
      />
    </div>
  );
};

export default BlogDetailPage;
