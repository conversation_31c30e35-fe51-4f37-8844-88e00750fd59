import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

interface BlogListPageProps {
  initialTag?: string;
}
/**
 * Trang tổng quan quản lý Blog
 * Hiển thị các card cho blog đã mua, tất cả blog và blog cá nhân
 */
const BlogManagementPage: React.FC<BlogListPageProps> = () => {
  const { t } = useTranslation(['blog', 'common']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Purchased Blogs Card */}
        <ModuleCard
          title={t('purchaseManagement.purchasedBlogs.title', { ns: 'blog' })}
          description={t('purchaseManagement.purchasedBlogs.description', { ns: 'blog' })}
          icon="shopping-bag"
          linkTo="/blog/purchased"
          className="border-l-4 border-l-primary"
        />

        <ModuleCard
          title={t('purchaseManagement.allBlogs.title', { ns: 'blog' })}
          description={t('purchaseManagement.allBlogs.description', { ns: 'blog' })}
          icon="book-open"
          linkTo="/blog/list"
          className="border-l-4 border-l-secondary"
        />

        <ModuleCard
          title={t('purchaseManagement.personalBlogs.title', { ns: 'blog' })}
          description={t('purchaseManagement.personalBlogs.description', { ns: 'blog' })}
          icon="edit"
          linkTo="/blog/personal"
          className="border-l-4 border-l-success"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default BlogManagementPage;
