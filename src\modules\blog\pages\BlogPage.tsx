import React from 'react';
import { useParams } from 'react-router-dom';
import BlogListPage from './BlogListPage';

/**
 * Component bọc BlogListPage để tránh render lại khi chat panel thay đổi
 */
const BlogPage: React.FC = () => {
  // Lấy tag từ params nếu có
  const { tag } = useParams<{ tag?: string }>();

  return <BlogListPage initialTag={tag} />;
};

// Không sử dụng memo để đảm bảo component luôn được render lại khi cần
export default BlogPage;
