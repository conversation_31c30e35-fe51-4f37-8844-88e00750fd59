import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Card, Table, Typography, Loading, Tooltip, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import DeleteConfirmModal from '@/shared/components/common/DeleteConfirmModal/DeleteConfirmModal';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { useTheme } from '@/shared/contexts';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useGetBlogs, useDeleteBlog } from '../hooks/useBlogList';
import { BlogApiItem, OwnershipType, BlogStatus, GetBlogsQueryDto } from '../types/blog.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { formatDate } from '@/shared/utils/date';

interface BlogListPageProps {
    initialTag?: string;
}

/**
 * Trang hiển thị danh sách blog cá nhân (do người dùng sở hữu) dạng bảng
 */
const BlogPersonalListPage: React.FC<BlogListPageProps> = ({ initialTag }) => {
    const { t } = useTranslation(['blog', 'common']);
    const navigate = useNavigate();
    const [tag, setTag] = useState<string | undefined>(initialTag);
    const [blogToDelete, setBlogToDelete] = useState<number | null>(null);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [blogTitleToDelete, setBlogTitleToDelete] = useState<string>('');

    // Sử dụng hook xóa blog
    const { mutate: deleteBlog, isPending: isDeleting } = useDeleteBlog();

    // Sử dụng hook theme
    useTheme();

    // Xử lý khi click vào blog để xem chi tiết
    const handleViewBlog = useCallback((blogId: number) => {
        navigate(`/blog/detail/${blogId}`);
    }, [navigate]);

    // Định nghĩa columns cho bảng
    const columns = useMemo<TableColumn<BlogApiItem>[]>(
        () => [
            {
                key: 'index',
                title: t('common:no'),
                width: '5%',
                render: (_, __, index) => index + 1
            },
            {
                key: 'title',
                title: t('blog:table.title', 'Tiêu đề'),
                dataIndex: 'title',
                width: '15%',
                sortable: true,
                render: (value) => (
                    <Tooltip content={value as string} position="top">
                        <div className="truncate max-w-[200px]">{value as string}</div>
                    </Tooltip>
                )
            },
            {
                key: 'content',
                title: t('blog:table.content', 'Nội dung'),
                dataIndex: 'content',
                width: '15%',
                render: (value) => (
                    <Tooltip content={value as string} position="top">
                        <div className="truncate max-w-[200px]">{value as string}</div>
                    </Tooltip>
                )
            },
            {
                key: 'point',
                title: t('blog:table.point', 'Điểm'),
                dataIndex: 'point',
                width: '5%',
                sortable: true,
                render: (value) => <div className="text-center">{value as number}</div>
            },
            {
                key: 'viewCount',
                title: t('blog:table.viewCount', 'Lượt xem'),
                dataIndex: 'viewCount',
                width: '8%',
                sortable: true,
                render: (value) => <div className="text-center">{(value as number).toLocaleString()}</div>
            },
            {
                key: 'tags',
                title: t('blog:table.tags', 'Thẻ'),
                dataIndex: 'tags',
                width: '10%',
                render: (value) => {
                    const tags = value as string[];
                    return (
                        <div className="flex flex-wrap gap-1">
                            {tags.map((tag, index) => (
                                <span key={index} className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs">
                                    {tag}
                                </span>
                            ))}
                        </div>
                    );
                }
            },
            {
                key: 'createdAt',
                title: t('blog:table.createdAt', 'Ngày tạo'),
                dataIndex: 'createdAt',
                width: '10%',
                sortable: true,
                render: (value) => formatDate(value as number)
            },
            {
                key: 'thumbnailUrl',
                title: t('blog:table.thumbnail', 'Ảnh'),
                dataIndex: 'thumbnailUrl',
                width: '8%',
                render: (value) => (
                    <div className="w-12 h-12 rounded overflow-hidden">
                        <img src={value as string} alt="Thumbnail" className="w-full h-full object-cover" />
                    </div>
                )
            },
            {
                key: 'status',
                title: t('blog:table.status', 'Trạng thái'),
                dataIndex: 'status',
                width: '8%',
                sortable: true,
                render: (value) => {
                    const status = value as BlogStatus;
                    let statusClass = '';
                    let statusText = '';

                    switch(status) {
                        case BlogStatus.APPROVED:
                            statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                            statusText = t('blog:status.approved', 'Đã duyệt');
                            break;
                        case BlogStatus.PENDING:
                            statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                            statusText = t('blog:status.pending', 'Chờ duyệt');
                            break;
                        case BlogStatus.REJECTED:
                            statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                            statusText = t('blog:status.rejected', 'Từ chối');
                            break;
                        case BlogStatus.DRAFT:
                            statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
                            statusText = t('blog:status.draft', 'Bản nháp');
                            break;
                        default:
                            statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
                            statusText = t('blog:status.unknown', 'Không xác định');
                    }

                    return (
                        <div className={`px-2 py-1 rounded-full text-center text-xs font-medium ${statusClass}`}>
                            {statusText}
                        </div>
                    );
                }
            },
            {
                key: 'author',
                title: t('blog:table.author', 'Tác giả'),
                dataIndex: 'author',
                width: '10%',
                render: (value) => {
                    const author = value as BlogApiItem['author'];
                    return author?.name || '';
                }
            },
            {
                key: 'like',
                title: t('blog:table.like', 'Lượt thích'),
                dataIndex: 'like',
                width: '5%',
                sortable: true,
                render: (value) => <div className="text-center">{value as number}</div>
            },
            {
                key: 'actions',
                title: t('common:actions'),
                width: '10%',
                render: (_, record) => {
                    // Tạo danh sách các action items
                    const actionItems: ActionMenuItem[] = [
                        {
                            id: 'view',
                            label: t('common:view'),
                            icon: 'eye',
                            onClick: () => handleViewBlog(record.id),
                        }
                    ];

                    // Chỉ thêm nút xóa nếu trạng thái không phải là APPROVED
                    if (record.status !== BlogStatus.APPROVED) {
                        actionItems.push({
                            id: 'delete',
                            label: t('common:delete'),
                            icon: 'trash',
                            onClick: () => {
                                // Thiết lập state và mở modal
                                setBlogToDelete(record.id);
                                setBlogTitleToDelete(record.title);
                                setIsDeleteModalOpen(true);
                            },
                            // Không có thuộc tính để đánh dấu là nguy hiểm, sẽ xử lý bằng cách khác
                        });
                    }

                    // Thêm nút chỉnh sửa nếu trạng thái là DRAFT
                    if (record.status === BlogStatus.DRAFT) {
                        actionItems.push({
                            id: 'edit',
                            label: t('common:edit'),
                            icon: 'edit',
                            onClick: () => navigate(`/blog/edit/${record.id}`),
                        });
                    }

                    return (
                        <div className="flex justify-center" onClick={(e) => e.stopPropagation()}>
                            <ActionMenu
                                items={actionItems}
                                menuTooltip={t('common:moreActions', 'Thêm hành động')}
                                iconSize="sm"
                                iconVariant="default"
                                placement="bottom"
                                menuWidth="180px"
                                menuIcon="more-horizontal"
                                showAllInMenu={true}
                                preferRight={true}
                                preferTop={true}
                            />
                        </div>
                    );
                },
            },
        ],
        [t, handleViewBlog, navigate]
    );

    // Sử dụng hook tạo filterOptions
    const filterOptions = useMemo(
        () => [
            { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
            { id: 'approved', label: t('blog:status.approved', 'Đã duyệt'), icon: 'check', value: BlogStatus.APPROVED },
            { id: 'pending', label: t('blog:status.pending', 'Chờ duyệt'), icon: 'clock', value: BlogStatus.PENDING },
            { id: 'rejected', label: t('blog:status.rejected', 'Từ chối'), icon: 'x', value: BlogStatus.REJECTED },
            { id: 'draft', label: t('blog:status.draft', 'Bản nháp'), icon: 'file', value: BlogStatus.DRAFT },
        ],
        [t]
    );

    // Tạo hàm createQueryParams
    const createQueryParams = (params: {
        page: number;
        pageSize: number;
        searchTerm: string;
        sortBy: string | null;
        sortDirection: SortDirection | null;
        filterValue: string | number | boolean | undefined;
        dateRange: [Date | null, Date | null];
    }): GetBlogsQueryDto => {
        const queryParams: GetBlogsQueryDto = {
            page: params.page,
            limit: params.pageSize,
            search: params.searchTerm || undefined,
            sort: params.sortBy || undefined,
            order: params.sortDirection === SortDirection.DESC ? 'DESC' : 'ASC',
            ownership_type: OwnershipType.OWN,
            tags: tag
        };

        if (params.filterValue !== 'all') {
            queryParams.status = params.filterValue as BlogStatus;
        }

        return queryParams;
    };

    // Sử dụng hook useDataTable với cấu hình mặc định
    const dataTable = useDataTable(
        useDataTableConfig<BlogApiItem, GetBlogsQueryDto>({
            columns,
            filterOptions,
            showDateFilter: true,
            createQueryParams,
        })
    );

    // Gọi API lấy danh sách blog với queryParams từ dataTable
    const { data, isLoading } = useGetBlogs(dataTable.queryParams as GetBlogsQueryDto);

    // Xử lý khi thêm blog mới
    const handleAddBlog = () => {
        navigate('/blog/create');
    };

    // Reset page when tag changes
    useEffect(() => {
        if (dataTable.tableData.handlePageChange) {
            dataTable.tableData.handlePageChange(1, dataTable.tableData.pageSize);
        }
    }, [tag, dataTable.tableData]);

    // Update tag when initialTag changes
    useEffect(() => {
        setTag(initialTag);
    }, [initialTag]);

    // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
    const handleSortChangeWrapper = useCallback(
        (sortBy: string | null, sortDirection: SortOrder) => {
            dataTable.tableData.handleSortChange(sortBy, sortDirection);
        },
        [dataTable.tableData]
    );

    // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
    const {
        handleClearSearch,
        handleClearFilter,
        handleClearDateRange,
        handleClearSort,
        handleClearAll,
        getFilterLabel,
    } = useActiveFilters({
        handleSearch: dataTable.tableData.handleSearch,
        setSelectedFilterId: dataTable.filter.setSelectedId,
        setDateRange: dataTable.dateRange.setDateRange,
        handleSortChange: handleSortChangeWrapper,
        selectedFilterValue: dataTable.filter.selectedValue,
        filterValueLabelMap: {
            [BlogStatus.APPROVED]: t('blog:status.approved', 'Đã duyệt'),
            [BlogStatus.PENDING]: t('blog:status.pending', 'Chờ duyệt'),
            [BlogStatus.REJECTED]: t('blog:status.rejected', 'Từ chối'),
            [BlogStatus.DRAFT]: t('blog:status.draft', 'Bản nháp'),
        },
        t,
    });

    return (
        <div>
            <MenuIconBar
                onSearch={dataTable.tableData.handleSearch}
                onAdd={handleAddBlog}
                items={dataTable.menuItems}
                onDateRangeChange={dataTable.dateRange.setDateRange}
                onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
                columns={dataTable.columnVisibility.visibleColumns}
                showDateFilter={true}
                showColumnFilter={true}
            />

            {/* Thêm component ActiveFilters */}
            <ActiveFilters
                searchTerm={dataTable.tableData.searchTerm}
                onClearSearch={handleClearSearch}
                filterValue={dataTable.filter.selectedValue}
                filterLabel={getFilterLabel()}
                onClearFilter={handleClearFilter}
                dateRange={dataTable.dateRange.dateRange}
                onClearDateRange={handleClearDateRange}
                sortBy={dataTable.tableData.sortBy}
                sortDirection={dataTable.tableData.sortDirection}
                onClearSort={handleClearSort}
                onClearAll={handleClearAll}
            />

            {/* Loading state */}
            {isLoading && (
                <div className="flex justify-center items-center min-h-[400px]">
                    <Loading />
                </div>
            )}

            {/* Blog table */}
            {!isLoading && data?.result && (
                <Card className="overflow-hidden">
                    <Table
                        columns={dataTable.columnVisibility.visibleTableColumns}
                        data={data.result.content || []}
                        rowKey="id"
                        loading={isLoading}
                        sortable={true}
                        onSortChange={dataTable.tableData.handleSortChange}
                        pagination={{
                            current: data.result.currentPage || 1,
                            pageSize: dataTable.tableData.pageSize,
                            total: data.result.totalItems || 0,
                            onChange: dataTable.tableData.handlePageChange,
                            showSizeChanger: true,
                            pageSizeOptions: [5, 10, 15, 20],
                            showFirstLastButtons: true,
                            showPageInfo: true,
                        }}
                        onRow={(record: BlogApiItem) => ({
                            onClick: () => handleViewBlog(record.id)
                        })}
                    />
                </Card>
            )}

            {/* Empty state */}
            {!isLoading && data?.result && data.result.content.length === 0 && (
                <div className="text-center py-12">
                    <Typography variant="body1" color="muted" className="text-lg">
                        {t('blog.noResults', 'Không tìm thấy bài viết nào.')}
                    </Typography>
                </div>
            )}

            {/* Modal xác nhận xóa blog */}
            <DeleteConfirmModal
                isOpen={isDeleteModalOpen}
                onClose={() => {
                    setIsDeleteModalOpen(false);
                    setBlogToDelete(null);
                    setBlogTitleToDelete('');
                }}
                onConfirm={() => {
                    if (blogToDelete) {
                        deleteBlog(blogToDelete, {
                            onSuccess: () => {
                                setIsDeleteModalOpen(false);
                                setBlogToDelete(null);
                                setBlogTitleToDelete('');
                            },
                            onError: (error) => {
                                console.error('Error deleting blog:', error);
                            }
                        });
                    }
                }}
                title={t('blog:deleteConfirm.title', 'Xác nhận xóa bài viết')}
                message={t('blog:deleteConfirm.message', 'Bạn có chắc chắn muốn xóa bài viết này không?')}
                itemName={blogTitleToDelete}
                isLoading={isDeleting}
            />
        </div>
    );
};

export default BlogPersonalListPage;
