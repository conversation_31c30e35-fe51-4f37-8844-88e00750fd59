import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Loading, Pagination, Typography, Alert } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useGetPurchasedBlogs } from '../hooks/useBlogPurchase';
import { useNavigate } from 'react-router-dom';
import { useSmartNotification } from '@/shared/hooks/common';
import BlogGrid from '../components/BlogGrid';


/**
 * Trang hiển thị danh sách blog đã mua
 */
const BlogPurchaseListPage: React.FC = () => {
  const { t } = useTranslation(['blog']);
  const navigate = useNavigate();
  const notification = useSmartNotification();

  // State cho phân trang
  const [page, setPage] = useState(1);

  // Sử dụng hook theme
  useTheme();

  // Hằng số cho limit
  const limit = 10;

  // Sử dụng TanStack Query để lấy dữ liệu blog đã mua
  const { data, isLoading, error } = useGetPurchasedBlogs({
    page,
    limit,
  });

  // Xử lý khi thay đổi trang
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  // Xử lý khi tìm kiếm
  const handleSearch = useCallback(() => {
    // Hiện tại API không hỗ trợ tìm kiếm, nhưng chúng ta vẫn giữ code này
    // để dễ dàng thêm tính năng sau này
    setPage(1);

    notification.info({
      title: t('common:notification.info', 'Thông báo'),
      message: t('common:notification.featureInDevelopment', 'Tính năng đang được phát triển')
    });
  }, [notification, t]);

  // Xử lý khi thêm blog mới
  const handleAddBlog = useCallback(() => {
    navigate('/blog/create');
  }, [navigate]);

  // Không cần hàm handleFilterChange vì đã xử lý trực tiếp trong onClick của MenuIconBar

  // Hiển thị thông báo lỗi nếu có
  useEffect(() => {
    if (error) {
      notification.error({
        title: t('common:notification.error', 'Lỗi'),
        message: error instanceof Error
          ? error.message
          : t('common:notification.unknownError', 'Đã xảy ra lỗi không xác định')
      });
    }
  }, [error, notification, t]);


  return (
    <div>
      <Typography variant="h1" className="mb-6">
        {t('blog:purchasedBlogs')}
      </Typography>

      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddBlog}
        items={[
          {
            id: 'all',
            label: t('common:all', 'Tất cả'),
            icon: 'list',
            onClick: () => setPage(1),
          },
          {
            id: 'recent',
            label: t('blog:purchaseManagement.purchasedBlogs.recent', 'Gần đây'),
            icon: 'clock',
            onClick: () => notification.info({
              title: t('common:notification.info', 'Thông báo'),
              message: t('common:notification.featureInDevelopment', 'Tính năng đang được phát triển')
            }),
          },
        ]}
      />

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center min-h-[400px]">
          <Loading />
        </div>
      )}

      {/* Error state */}
      {error && (
        <Alert
          type="error"
          title={t('common:notification.error', 'Đã xảy ra lỗi')}
          message={t('common:notification.fetchError', 'Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.')}
          className="my-4"
        />
      )}

      {/* Blog list */}
      {!isLoading && !error && data?.result && (
        <>
          {/* Sử dụng BlogGrid để hiển thị danh sách blog */}
          <BlogGrid blogs={data.result.content} />

          {/* Pagination */}
          {data.result.totalItems > 0 && (
            <div className="flex justify-end">
              <Pagination
                variant="compact"
                currentPage={page}
                totalPages={data.result.totalPages}
                onPageChange={handlePageChange}
                showFirstLastButtons={false}
                showItemsPerPageSelector={false}
                showPageInfo={false}
                maxPageButtons={5}
                size="md"
                borderless={true}
              />
            </div>
          )}

          {/* Empty state */}
          {data.result.content.length === 0 && (
            <div className="text-center py-12">
              <Typography variant="body1" color="muted" className="text-lg">
                {t('blog:noResults')}
              </Typography>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default BlogPurchaseListPage;
