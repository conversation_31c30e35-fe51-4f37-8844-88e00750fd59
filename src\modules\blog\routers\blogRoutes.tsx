import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';
import { lazy, Suspense } from 'react';
import { RouteObject } from 'react-router-dom';

// Lazy load pages
const BlogDetailPage = lazy(() => import('../pages/BlogDetailPage'));
const BlogPage = lazy(() => import('../pages/BlogPage'));
const BlogPurchaseListPage = lazy(() => import('../pages/BlogPurchaseListPage'));
const BlogManagementPage = lazy(() => import('../pages/BlogManagementPage'));
const BlogPersonalListPage = lazy(() => import('../pages/BlogPersonalListPage'));
const BlogCreatePage = lazy(() => import('../pages/BlogCreatePage'));

/**
 * Blog module routes
 */
const blogRoutes: RouteObject[] = [
  {
    path: '/blog/tag/:tag',
    element: (
      <MainLayout title="Blog - Thẻ" key="blog-tag-layout">
        <Suspense fallback={<Loading />} key="blog-tag">
          <BlogPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/blog/detail/:id',
    element: (
      <MainLayout title="Chi tiết bài viết" key="blog-detail-layout">
        <Suspense fallback={<Loading />} key="blog-detail">
          <BlogDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/blog',
    element: (
      <MainLayout title="Quản lý Blog" key="blog-purchase-management-layout">
        <Suspense fallback={<Loading />} key="blog-purchase-management">
          <BlogManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/blog/purchased',
    element: (
      <MainLayout title="Blog đã mua" key="blog-purchase-list-layout">
        <Suspense fallback={<Loading />} key="blog-purchase-list">
          <BlogPurchaseListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/blog/list',
    element: (
      <MainLayout title="Blog" key="blog-list-layout">
        <Suspense fallback={<Loading />} key="blog-list">
          <BlogPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/blog/personal',
    element: (
      <MainLayout title="Blog Personal" key="blog-personal-layout">
        <Suspense fallback={<Loading />} key="blog-personal-list">
          <BlogPersonalListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/blog/create',
    element: (
      <MainLayout title="Tạo bài viết mới" key="blog-create-layout">
        <Suspense fallback={<Loading />} key="blog-create">
          <BlogCreatePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default blogRoutes;
