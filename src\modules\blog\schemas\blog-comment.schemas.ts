import { z } from 'zod';

/**
 * Schema cho reply của bình luận
 */
export const commentReplySchema = z.object({
  id: z.string(),
  blogid: z.number(),
  userid: z.number(),
  createdat: z.string(),
  content: z.string(),
  authortype: z.string(),
  employeeid: z.number().nullable(),
  parentcommentid: z.string(),
});

/**
 * Schema cho item trong danh sách bình luận
 */
export const blogCommentItemSchema = z.object({
  id: z.string(),
  blogid: z.number(),
  userid: z.number(),
  createdat: z.string(),
  content: z.string(),
  authortype: z.string(),
  employeeid: z.number().nullable(),
  parentcommentid: z.string().nullable(),
  replies: z.array(commentReplySchema),
});

/**
 * Schema cho response của API lấy danh sách bình luận
 */
export const blogCommentsResponseSchema = z.object({
  content: z.array(blogCommentItemSchema),
  totalItems: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalPages: z.number(),
  currentPage: z.number(),
});

/**
 * Schema cho query params của API lấy danh sách bình luận
 */
export const getBlogCommentsQuerySchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
});

/**
 * Schema cho API response của danh sách bình luận
 */
export const blogCommentsApiResponseSchema = z.object({
  code: z.number(),
  message: z.string(),
  result: blogCommentsResponseSchema,
});
