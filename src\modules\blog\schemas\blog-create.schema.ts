import { z } from 'zod';
import { AuthorType, BlogStatus } from '../types/blog.types';

/**
 * <PERSON>hema cho request body của API tạo blog mới
 */
export const CreateBlogSchema = z.object({
  /**
   * Tiêu đề bài viết
   */
  title: z.string().min(1, 'Tiêu đề không được để trống').max(255, 'Tiêu đề không được vượt quá 255 ký tự'),

  /**
   * <PERSON>ô tả ngắn về bài viết
   */
  description: z.string().min(1, '<PERSON>ô tả không được để trống').max(500, '<PERSON><PERSON> tả không được vượt quá 500 ký tự'),

  /**
   * Loại media của nội dung (thường là text/html)
   */
  contentMediaType: z.string().default('text/html'),

  /**
   * Loại media của thumbnail (thường là image/jpeg hoặc image/png)
   */
  thumbnailMediaType: z.string().default('image/jpeg'),

  /**
   * Số điểm để mua bài viết (0 nếu miễn phí)
   */
  point: z.number().min(0, 'Số điểm không được âm').default(0),

  /**
   * Danh sách tags của bài viết
   */
  tags: z.array(z.string()).default([]),

  /**
   * Trạng thái bài viết (DRAFT, PENDING, APPROVED, REJECTED)
   */
  status: z.nativeEnum(BlogStatus).default(BlogStatus.DRAFT),

  /**
   * Loại tác giả (USER, EMPLOYEE, SYSTEM)
   */
  authorType: z.nativeEnum(AuthorType).default(AuthorType.USER),
});

/**
 * Schema cho response của API tạo blog mới
 */
export const CreateBlogResponseSchema = z.object({
  /**
   * URL để upload nội dung bài viết
   */
  contentUploadUrl: z.string(),

  /**
   * URL để upload thumbnail của bài viết
   */
  thumbnailUploadUrl: z.string(),
});

/**
 * Type cho dữ liệu đã được validate bởi CreateBlogSchema
 */
export type CreateBlogSchemaType = z.infer<typeof CreateBlogSchema>;

/**
 * Type cho dữ liệu đã được validate bởi CreateBlogResponseSchema
 */
export type CreateBlogResponseSchemaType = z.infer<typeof CreateBlogResponseSchema>;
