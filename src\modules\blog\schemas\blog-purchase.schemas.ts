import { z } from 'zod';

/**
 * Schema cho thông tin blog trong danh sách đã mua
 */
export const purchasedBlogItemSchema = z.object({
  id: z.number(),
  title: z.string(),
  thumbnail_url: z.string(),
  tags: z.array(z.string()),
  created_at: z.number(),
});

/**
 * Schema cho item trong danh sách blog đã mua
 */
export const blogPurchaseItemSchema = z.object({
  id: z.number(),
  blog: purchasedBlogItemSchema,
  point: z.number(),
  purchased_at: z.number().nullable(),
});

/**
 * Schema cho response của API lấy danh sách blog đã mua
 */
export const purchasedBlogsResponseSchema = z.object({
  content: z.array(blogPurchaseItemSchema),
  totalItems: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalPages: z.number(),
  currentPage: z.number(),
});

/**
 * Schema cho query params của API lấy danh sách blog đã mua
 */
export const getPurchasedBlogsQuerySchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
  blog_id: z.number().optional(),
  user_id: z.number().optional(),
  start_date: z.number().optional(),
  end_date: z.number().optional(),
});

/**
 * Schema cho response của API kiểm tra trạng thái mua blog
 */
export const blogPurchaseStatusResponseSchema = z.object({
  purchased: z.boolean(),
  purchased_at: z.number().nullable().optional(),
});

/**
 * Schema cho API response của danh sách blog đã mua
 */
export const purchasedBlogsApiResponseSchema = z.object({
  code: z.number(),
  message: z.string(),
  result: purchasedBlogsResponseSchema,
});

/**
 * Schema cho API response của trạng thái mua blog
 */
export const blogPurchaseStatusApiResponseSchema = z.object({
  code: z.number(),
  message: z.string(),
  result: blogPurchaseStatusResponseSchema,
});
