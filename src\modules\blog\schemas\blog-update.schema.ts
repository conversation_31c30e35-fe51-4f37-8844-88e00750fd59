/**
 * Schemas cho các API cập nhật blog
 */
import { z } from 'zod';
import { BlogMediaType } from '../types/blog-update.types';

/**
 * Schema cho request body của API cập nhật media
 */
export const UpdateBlogMediaSchema = z.object({
  /**
   * Loại media (content hoặc thumbnail)
   */
  media_type: z.nativeEnum(BlogMediaType),

  /**
   * Loại nội dung của media
   * Nếu media_type là "content" thì media_content_type phải là "text/html"
   * Nếu media_type là "thumbnail" thì media_content_type phải là một trong các định dạng hình ảnh
   */
  media_content_type: z.string()
});

/**
 * Schema cho response của API cập nhật media
 */
export const UpdateBlogMediaResponseSchema = z.object({
  /**
   * URL để upload media
   */
  uploadUrl: z.string().url()
});

/**
 * Type cho dữ liệu đã được validate bởi UpdateBlogMediaSchema
 */
export type UpdateBlogMediaSchemaType = z.infer<typeof UpdateBlogMediaSchema>;

/**
 * Type cho dữ liệu đã được validate bởi UpdateBlogMediaResponseSchema
 */
export type UpdateBlogMediaResponseSchemaType = z.infer<typeof UpdateBlogMediaResponseSchema>;
