import { z } from 'zod';
import { AuthorType, BlogStatus } from '../types/blog.types';

/**
 * Schema cho blog moderator
 */
export const BlogModeratorSchema = z.object({
  id: z.number(),
  name: z.string(),
  avatar: z.string().optional(),
});

/**
 * Schema cho blog author
 */
export const BlogAuthorSchema = z.object({
  id: z.number(),
  name: z.string(),
  type: z.nativeEnum(AuthorType),
  avatar: z.string().optional(),
});

/**
 * Schema cho blog API item
 */
export const BlogApiItemSchema = z.object({
  id: z.number(),
  title: z.string(),
  content: z.string(),
  contentUploadUrl: z.string().optional(),
  thumbnailUploadUrl: z.string().optional(),
  point: z.number(),
  viewCount: z.number(),
  thumbnailUrl: z.string(),
  tags: z.array(z.string()),
  createdAt: z.number(),
  updatedAt: z.number(),
  userId: z.number().optional(),
  employeeId: z.number().optional(),
  authorType: z.nativeEnum(AuthorType),
  author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  employeeModerator: BlogModeratorSchema.nullable(),
  status: z.nativeEnum(BlogStatus),
  enable: z.boolean(),
  like: z.number(),
});

/**
 * Schema cho blog list item
 */
export const BlogListItemSchema = z.object({
  id: z.number(),
  title: z.string(),
  thumbnailUrl: z.string(),
  viewCount: z.number(),
  tags: z.array(z.string()),
  createdAt: z.number(),
  author: BlogAuthorSchema,
  status: z.nativeEnum(BlogStatus),
  enable: z.boolean(),
  like: z.number(),
});

/**
 * Schema cho blog detail
 */
export const BlogDetailSchema = z.object({
  id: z.number(),
  title: z.string(),
  content: z.string(),
  thumbnailUrl: z.string(),
  point: z.number(),
  viewCount: z.number(),
  tags: z.array(z.string()),
  createdAt: z.number(),
  updatedAt: z.number(),
  author: BlogAuthorSchema,
  employeeModerator: BlogModeratorSchema.nullable(),
  status: z.nativeEnum(BlogStatus),
  enable: z.boolean(),
  like: z.number(),
});

/**
 * Schema cho pagination meta
 */
export const PaginationMetaSchema = z.object({
  totalItems: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalPages: z.number(),
  currentPage: z.number(),
});

/**
 * Schema cho paginated blog response
 */
export const PaginatedBlogResponseSchema = z.object({
  content: z.array(BlogApiItemSchema),
  totalItems: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalPages: z.number(),
  currentPage: z.number(),
});

/**
 * Schema cho blog response
 */
export const BlogResponseSchema = z.object({
  id: z.number(),
  title: z.string(),
  content: z.string(),
  point: z.number(),
  viewCount: z.number(),
  thumbnailUrl: z.string(),
  tags: z.array(z.string()),
  createdAt: z.number(),
  updatedAt: z.number(),
  userId: z.number().nullable(),
  employeeId: z.number().nullable(),
  authorType: z.nativeEnum(AuthorType),
  author: BlogAuthorSchema,
  employeeModerator: BlogModeratorSchema.nullable(),
  status: z.nativeEnum(BlogStatus),
  enable: z.boolean(),
  like: z.number(),
  isPurchased: z.boolean()
});

/**
 * Schema cho query params của API lấy danh sách blog
 */
export const GetBlogsQuerySchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
  status: z.nativeEnum(BlogStatus).optional(),
  authorType: z.nativeEnum(AuthorType).optional(),
  author_type: z.nativeEnum(AuthorType).optional(),
  tags: z.union([z.string(), z.array(z.string())]).optional(),
  search: z.string().optional(),
  sort: z.string().optional().default('createdAt'),
  order: z.enum(['ASC', 'DESC']).optional().default('DESC'),
});
