import { apiClient } from '@/shared/api';
import { BlogCommentsApiResponse, GetBlogCommentsQueryDto } from '../types/blog-comment.types';

/**
 * Base URL cho API blog
 */
const API_BASE_URL = '';
/**
 * <PERSON><PERSON><PERSON> danh sách bình luận của bài viết
 * @param blogId ID của bài viết
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getBlogComments = async (
  blogId: number,
  params?: GetBlogCommentsQueryDto
): Promise<BlogCommentsApiResponse> => {
  return apiClient.get(`${API_BASE_URL}/user/blogs/${blogId}/comments`, { params });
};
