import { apiClient } from '@/shared/api';
import { CreateBlogDto, CreateBlogApiResponse } from '../types/blog-create.types';

/**
 * Base URL cho API blog
 */
const API_BASE_URL = '';

/**
 * Tạo bài viết mới và nhận URL để upload nội dung và thumbnail
 * 
 * @param data Dữ liệu bài viết cần tạo
 * @returns Promise với response từ API chứa URL để upload nội dung và thumbnail
 * 
 * @example
 * // Tạo bài viết mới
 * const response = await createBlog({
 *   title: 'Tiêu đề bài viết',
 *   description: 'Mô tả ngắn về bài viết',
 *   contentMediaType: 'text/html',
 *   thumbnailMediaType: 'image/jpeg',
 *   point: 100,
 *   tags: ['tag1', 'tag2'],
 *   status: BlogStatus.DRAFT,
 *   authorType: AuthorType.SYSTEM
 * });
 * 
 * // Sử dụng URL để upload nội dung và thumbnail
 * const { contentUploadUrl, thumbnailUploadUrl } = response.result;
 */
export const createBlog = async (data: CreateBlogDto): Promise<CreateBlogApiResponse> => {
  return apiClient.post<CreateBlogApiResponse['result']>(`${API_BASE_URL}/user/blogs`, data);
};
