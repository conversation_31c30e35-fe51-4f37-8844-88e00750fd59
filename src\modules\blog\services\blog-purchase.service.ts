import { apiClient } from '@/shared/api';
import {
  BlogPurchaseApiResponse,
  BlogPurchaseStatusApiResponse,
  GetPurchasedBlogsQueryDto
} from '../types/blog-purchase.types';
import { ApiResponse, BlogListResponse } from '../types/blog.types';

/**
 * Base URL cho API blog
 */
const API_BASE_URL = '';
/**
 * Lấy danh sách blog đã mua của người dùng
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getPurchasedBlogs = async (
  params?: GetPurchasedBlogsQueryDto
): Promise<ApiResponse<BlogListResponse>> => {
  return apiClient.get(`${API_BASE_URL}/user/blogs/purchased`, {
    params,
  });
};

/**
 * Kiểm tra trạng thái mua blog của người dùng
 * @param blogId ID của blog cần kiểm tra
 * @returns Promise với response từ API
 */
export const checkBlogPurchaseStatus = async (
  blogId: number
): Promise<BlogPurchaseStatusApiResponse> => {
  return apiClient.get(`${API_BASE_URL}/user/blogs/${blogId}/purchased`);
};

/**
 * Mua bài viết bằng point
 * @param blogId ID của bài viết cần mua
 * @returns Promise với response từ API
 *
 * @example
 * // Mua bài viết với ID 123
 * const response = await purchaseBlog(123);
 *
 * // Kiểm tra kết quả
 * if (response.code === 201) {
 *   console.log('Mua bài viết thành công');
 * }
 */
export const purchaseBlog = async (
  blogId: number
): Promise<BlogPurchaseApiResponse> => {
  return apiClient.post(`${API_BASE_URL}/user/blogs/${blogId}/purchase`);
};
