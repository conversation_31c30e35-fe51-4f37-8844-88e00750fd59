/**
 * Service cho các API cập nhật blog
 */
import { apiClient } from '@/shared/api';
import {
  UpdateBlogMediaDto,
  UpdateBlogMediaApiResponse,
  SubmitBlogForReviewApiResponse,
  CancelBlogSubmitApiResponse
} from '../types/blog-update.types';

/**
 * Base URL cho API blog
 */
const API_BASE_URL = '';

/**
 * Cập nhật media (nội dung hoặc thumbnail) cho bài viết
 * 
 * @param id ID của bài viết
 * @param data Dữ liệu cập nhật media
 * @returns Promise với response từ API chứa URL để upload media
 * 
 * @example
 * // Cập nhật nội dung bài viết
 * const response = await updateBlogMedia(123, {
 *   media_type: 'content',
 *   media_content_type: 'text/html'
 * });
 * 
 * // Sử dụng URL để upload nội dung
 * const { uploadUrl } = response.result;
 * 
 * @example
 * // Cập nhật thumbnail bài viết
 * const response = await updateBlogMedia(123, {
 *   media_type: 'thumbnail',
 *   media_content_type: 'image/jpeg'
 * });
 * 
 * // Sử dụng URL để upload thumbnail
 * const { uploadUrl } = response.result;
 */
export const updateBlogMedia = async (
  id: number,
  data: UpdateBlogMediaDto
): Promise<UpdateBlogMediaApiResponse> => {
  return apiClient.put(`${API_BASE_URL}/user/blogs/${id}/media`, data);
};

/**
 * Gửi bài viết để kiểm duyệt (chuyển trạng thái từ DRAFT sang PENDING)
 * 
 * @param id ID của bài viết
 * @returns Promise với response từ API
 * 
 * @example
 * // Gửi bài viết để kiểm duyệt
 * const response = await submitBlogForReview(123);
 * 
 * // Kiểm tra kết quả
 * if (response.code === 200) {
 *   console.log('Bài viết đã được gửi để kiểm duyệt');
 * }
 */
export const submitBlogForReview = async (id: number): Promise<SubmitBlogForReviewApiResponse> => {
  return apiClient.put(`${API_BASE_URL}/user/blogs/${id}/submit`);
};

/**
 * Hủy gửi kiểm duyệt bài viết (chuyển trạng thái từ PENDING về DRAFT)
 * 
 * @param id ID của bài viết
 * @returns Promise với response từ API
 * 
 * @example
 * // Hủy gửi kiểm duyệt bài viết
 * const response = await cancelBlogSubmit(123);
 * 
 * // Kiểm tra kết quả
 * if (response.code === 200) {
 *   console.log('Đã hủy gửi kiểm duyệt bài viết');
 * }
 */
export const cancelBlogSubmit = async (id: number): Promise<CancelBlogSubmitApiResponse> => {
  return apiClient.put(`${API_BASE_URL}/user/blogs/${id}/cancel-submit`);
};
