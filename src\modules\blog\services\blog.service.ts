import { apiClient } from '@/shared/api';
import {
  ApiResponse,
  BlogResponseDto,
  GetBlogsQueryDto,
  BlogListResponse,
  DeleteBlogResponse
} from '../types/blog.types';

/**
 * Base URL cho API blog
 */
const API_BASE_URL = '';

/**
 * <PERSON><PERSON><PERSON> danh sách tất cả bài viết đã được phê duyệt
 * @param params Query params
 * @returns Danh sách bài viết đã được phân trang
 */
export const getBlogs = async (params: GetBlogsQueryDto): Promise<ApiResponse<BlogListResponse>> => {
  // Xử lý tham số tags nếu là mảng
  const queryParams = { ...params };
  if (Array.isArray(queryParams.tags)) {
    queryParams.tags = queryParams.tags.join(',');
  }

  return apiClient.get<BlogListResponse>(`${API_BASE_URL}/user/blogs`, {
    params: queryParams,
  });
};

/**
 * <PERSON><PERSON>y thông tin chi tiết của một bài viết theo ID
 * @param id ID của bài viết
 * @returns Thông tin chi tiết bài viết
 */
export const getBlogDetail = async (id: number): Promise<ApiResponse<BlogResponseDto>> => {
  return apiClient.get<BlogResponseDto>(`${API_BASE_URL}/user/blogs/detail/${id}`);
};

/**
 * Xóa mềm bài viết bằng cách đặt enable = false
 * @param id ID của bài viết cần xóa
 * @returns Thông báo kết quả xóa
 */
export const deleteBlog = async (id: number): Promise<ApiResponse<DeleteBlogResponse>> => {
  return apiClient.delete<DeleteBlogResponse>(`${API_BASE_URL}/user/blogs/${id}`);
};
