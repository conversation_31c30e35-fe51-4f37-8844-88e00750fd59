import { ApiResponse, PaginatedResult } from './blog.types';

/**
 * Enum cho các lo<PERSON> phản <PERSON>ng
 */
export enum ReactionType {
  LIKE = 'like',
  LOVE = 'love',
  HAHA = 'haha',
  WOW = 'wow',
  SAD = 'sad',
  ANGRY = 'angry'
}

/**
 * Interface cho phản ứng của bình luận
 */
export interface CommentReaction {
  id: string;
  userid: number;
  type: ReactionType;
  createdat: string;
}

/**
 * Interface cho reply của bình luận
 */
export interface CommentReply {
  id: string;
  blogid: number;
  userid: number;
  createdat: string;
  updatedat?: string;
  content: string;
  authortype: string;
  employeeid: number | null;
  parentcommentid: string;
  reactions: CommentReaction[];
  username?: string;
  useravatar?: string;
  isEdited?: boolean;
}

/**
 * Interface cho item trong danh sách bình luận
 */
export interface BlogCommentItem {
  id: string;
  blogid: number;
  userid: number;
  createdat: string;
  updatedat?: string;
  content: string;
  authortype: string;
  employeeid: number | null;
  parentcommentid: string | null;
  replies: CommentReply[];
  reactions: CommentReaction[];
  username?: string;
  useravatar?: string;
  isEdited?: boolean;
}

/**
 * Interface cho response của API lấy danh sách bình luận
 */
export type BlogCommentsResponse = PaginatedResult<BlogCommentItem>

/**
 * Interface cho query params của API lấy danh sách bình luận
 */
export interface GetBlogCommentsQueryDto {
  page?: number;
  limit?: number;
}

/**
 * Interface cho API response của danh sách bình luận
 */
export type BlogCommentsApiResponse = ApiResponse<BlogCommentsResponse>;
