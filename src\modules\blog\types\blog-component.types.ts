/**
 * Types for blog components
 */

/**
 * Interface for blog detail component
 */
export interface BlogDetailComponent {
  /**
   * ID của blog
   */
  id: number;

  /**
   * Tiêu đề blog
   */
  title: string;

  /**
   * <PERSON><PERSON> tả ngắn
   */
  description: string;

  /**
   * Nội dung blog
   */
  content: string;

  /**
   * URL hình ảnh thumbnail
   */
  thumbnail: string;

  /**
   * Số lượt xem
   */
  views: number;

  /**
   * Danh sách tag
   */
  tags: Array<{ id: string; name: string; slug: string }>;

  /**
   * Thời gian tạo
   */
  createdAt: string;

  /**
   * Có phải là nội dung premium không
   */
  isPremium: boolean;

  /**
   * Đ<PERSON> mua chưa
   */
  purchased?: boolean;

  /**
   * Giá (nếu là premium)
   */
  price?: number;
}

/**
 * Interface for blog list item component
 */
export interface BlogListItemComponent {
  /**
   * ID của blog
   */
  id: number;

  /**
   * Tiêu đề blog
   */
  title: string;

  /**
   * <PERSON><PERSON> tả ngắn
   */
  description: string;

  /**
   * URL hình ảnh thumbnail
   */
  thumbnail: string;

  /**
   * <PERSON><PERSON> lượt xem
   */
  views: number;

  /**
   * Danh sách tag
   */
  tags: Array<{ id: string; name: string; slug: string }>;

  /**
   * Thời gian tạo
   */
  createdAt: string;

  /**
   * Có phải là nội dung premium không
   */
  isPremium: boolean;
}
