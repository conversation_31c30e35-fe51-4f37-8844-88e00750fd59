/**
 * <PERSON><PERSON><PERSON> nghĩa các kiểu dữ liệu cho việc tạo blog
 */
import { ApiResponse } from './blog.types';
import { AuthorType, BlogStatus } from './blog.types';

/**
 * Interface cho request body của API tạo blog mới
 */
export interface CreateBlogDto {
  /**
   * Tiêu đề bài viết
   */
  title: string;

  /**
   * Mô tả ngắn về bài viết
   */
  description: string;

  /**
   * Loại media của nội dung (thường là text/html)
   */
  contentMediaType: string;

  /**
   * Loại media của thumbnail (thường là image/jpeg hoặc image/png)
   */
  thumbnailMediaType: string;

  /**
   * Số điểm để mua bài viết (0 nếu miễn phí)
   */
  point: number;

  /**
   * Danh sách tags của bài viết
   */
  tags: string[];

  /**
   * Trạng thái bài viết (DRAFT, PENDING, APPROVED, REJECTED)
   */
  status: BlogStatus;

  /**
   * <PERSON><PERSON><PERSON> tác giả (USER, EMPLOYEE, SYSTEM)
   */
  authorType: AuthorType;
}

/**
 * Interface cho response của API tạo blog mới
 */
export interface CreateBlogResponseDto {
  /**
   * URL để upload nội dung bài viết
   */
  contentUploadUrl: string;

  /**
   * URL để upload thumbnail của bài viết
   */
  thumbnailUploadUrl: string;
}

/**
 * Type cho API response của tạo blog mới
 */
export type CreateBlogApiResponse = ApiResponse<CreateBlogResponseDto>;
