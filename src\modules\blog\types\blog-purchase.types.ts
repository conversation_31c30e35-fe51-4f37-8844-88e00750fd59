import { ApiResponse, PaginatedResult } from './blog.types';

/**
 * Interface cho response của API mua blog
 * API trả về null trong result, không có dữ liệu cụ thể
 */
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface BlogPurchaseResponse {}

/**
 * Interface cho API response của mua blog
 */
export type BlogPurchaseApiResponse = ApiResponse<BlogPurchaseResponse>;

/**
 * Interface cho thông tin blog trong danh sách đã mua
 */
export interface PurchasedBlogItem {
  id: number;
  title: string;
  thumbnail_url: string;
  tags: string[];
  created_at: number;
}

/**
 * Interface cho item trong danh sách blog đã mua
 */
export interface BlogPurchaseItem {
  id: number;
  blog: PurchasedBlogItem;
  point: number;
  purchased_at: number | null;
}

/**
 * Interface cho response của API lấy danh sách blog đã mua
 */
export type PurchasedBlogsResponse = PaginatedResult<BlogPurchaseItem>

/**
 * Interface cho query params của API lấy danh sách blog đã mua
 */
export interface GetPurchasedBlogsQueryDto {
  page?: number;
  limit?: number;
  blog_id?: number;
  user_id?: number;
  start_date?: number;
  end_date?: number;
}

/**
 * Interface cho response của API kiểm tra trạng thái mua blog
 */
export interface BlogPurchaseStatusResponse {
  purchased: boolean;
  purchased_at?: number | null;
}

/**
 * Interface cho API response của danh sách blog đã mua
 */
export type PurchasedBlogsApiResponse = ApiResponse<PurchasedBlogsResponse>;

/**
 * Interface cho API response của trạng thái mua blog
 */
export type BlogPurchaseStatusApiResponse = ApiResponse<BlogPurchaseStatusResponse>;
