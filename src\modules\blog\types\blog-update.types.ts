/**
 * Đ<PERSON><PERSON> nghĩa các kiểu dữ liệu cho các API cập nhật blog
 */
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Enum cho loại media
 */
export enum BlogMediaType {
  CONTENT = 'content',
  THUMBNAIL = 'thumbnail'
}

/**
 * Interface cho request body của API cập nhật media
 */
export interface UpdateBlogMediaDto {
  /**
   * Loại media (content hoặc thumbnail)
   */
  media_type: BlogMediaType;

  /**
   * Loại nội dung của media
   * Nếu media_type là "content" thì media_content_type phải là "text/html"
   * Nếu media_type là "thumbnail" thì media_content_type phải là một trong các định dạng hình ảnh
   */
  media_content_type: string;
}

/**
 * Interface cho response của API cập nhật media
 */
export interface UpdateBlogMediaResponseDto {
  /**
   * URL để upload media
   */
  uploadUrl: string;
}

/**
 * Type cho API response của cập nhật media
 */
export type UpdateBlogMediaApiResponse = ApiResponse<UpdateBlogMediaResponseDto>;

/**
 * Type cho API response của gửi bài viết để kiểm duyệt
 */
export type SubmitBlogForReviewApiResponse = ApiResponse<void>;

/**
 * Type cho API response của hủy gửi kiểm duyệt bài viết
 */
export type CancelBlogSubmitApiResponse = ApiResponse<void>;
