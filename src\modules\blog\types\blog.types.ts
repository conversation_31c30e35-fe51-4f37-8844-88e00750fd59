/**
 * <PERSON><PERSON><PERSON> nghĩa các kiểu dữ liệu cho module blog
 */

/**
 * Enum cho trạng thái blog
 */
export enum BlogStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

/**
 * Enum cho loại tác giả
 */
export enum AuthorType {
  USER = 'USER',
  EMPLOYEE = 'EMPLOYEE',
  SYSTEM = 'SYSTEM'
}

/**
 * Enum cho loại sở hữu blog
 */
export enum OwnershipType {
  ALL = 'ALL',         // Tất cả blog
  OWN = 'CREATED',         // Blog do người dùng sở hữu
  PURCHASED = 'PURCHASED', // Blog đã mua
  NOT_OWN = 'NOT_OWNED' // Blog không sở hữu
}

/**
 * Interface cho tác giả blog
 */
export interface BlogAuthor {
  id: number;
  name: string;
  type: AuthorType;
  avatar?: string;
}

/**
 * Interface cho người kiểm duyệt blog (nhân viên)
 */
export interface BlogModerator {
  id: number;
  name: string;
  avatar?: string;
}

/**
 * Interface cho tag blog
 */
export interface BlogTag {
  id: string;
  name: string;
  slug: string;
}

/**
 * Interface cho blog item từ API
 */
export interface BlogApiItem {
  id: number;
  title: string;
  content: string;
  contentUploadUrl?: string;
  thumbnailUploadUrl?: string;
  point: number;
  viewCount: number;
  thumbnailUrl: string;
  tags: string[];
  createdAt: number;
  updatedAt: number;
  userId?: number;
  employeeId?: number;
  authorType: AuthorType;
  author: BlogAuthor;
  employeeModerator: BlogModerator | null;
  status: BlogStatus;
  enable: boolean;
  like: number;
}

/**
 * Interface cho chi tiết blog
 */
export interface BlogDetail {
  id: number;
  title: string;
  content: string;
  thumbnailUrl: string;
  point: number;
  viewCount: number;
  tags: string[];
  createdAt: number;
  updatedAt: number;
  author: BlogAuthor;
  employeeModerator: BlogModerator | null;
  status: BlogStatus;
  enable: boolean;
  like: number;
}

/**
 * Interface cho item trong danh sách blog
 */
export interface BlogListItem {
  id: number;
  title: string;
  thumbnailUrl: string;
  viewCount: number;
  tags: string[];
  createdAt: number;
  author: BlogAuthor;
  status: BlogStatus;
  enable: boolean;
  like: number;
}

/**
 * Interface cho response của danh sách blog
 */
export interface BlogListResponse {
  content: BlogApiItem[];
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Interface cho response của chi tiết blog
 */
export interface BlogDetailResponse {
  data: BlogDetail;
}

/**
 * Interface cho request mua blog
 */
export interface PurchaseBlogRequest {
  blogId: number;
}

/**
 * Interface cho response mua blog
 */
export interface PurchaseBlogResponse {
  success: boolean;
  message: string;
}

/**
 * Interface cho response xóa blog
 */
export interface DeleteBlogResponse {
  code: number;
  message: string;
}

/**
 * Interface cho API response
 */
export interface ApiResponse<T> {
  code: number;
  message: string;
  result?: T;
}

/**
 * Interface cho phân trang meta
 */
export interface PaginationMeta {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  content: T[];
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Interface cho query params của API lấy danh sách blog
 */
export interface GetBlogsQueryDto {
  page?: number;
  limit?: number;
  status?: BlogStatus;
  authorType?: AuthorType;
  author_type?: AuthorType;
  tags?: string | string[];
  search?: string;
  sort?: string;
  order?: 'ASC' | 'DESC';
  ownership_type?: OwnershipType;
}

/**
 * Interface cho API response của danh sách blog
 */
export interface PaginatedBlogResponseDto {
  content: BlogApiItem[];
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Interface cho API response của chi tiết blog
 */
export interface BlogResponseDto {
  id: number;
  title: string;
  content: string;
  point: number;
  viewCount: number;
  thumbnailUrl: string;
  tags: string[];
  createdAt: number;
  updatedAt: number;
  userId: number | null;
  employeeId: number | null;
  authorType: AuthorType;
  author: BlogAuthor;
  employeeModerator: BlogModerator | null;
  status: BlogStatus;
  enable: boolean;
  like: number;
  isPurchased: boolean;
}
