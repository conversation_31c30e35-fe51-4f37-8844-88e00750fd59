# AdvancedCustomFieldSelector

Component nâng cấp để chọn trường tùy chỉnh với MenuIconBar, filter nâng cao và ActiveFilter.

## Tính năng mới

### 🔍 MenuIconBar
- **Icon tìm kiếm**: Cho phép tìm kiếm trường tùy chỉnh theo từ khóa
- **Icon filter**: Menu dropdown để lọc theo nhóm trường tùy chỉnh
- **Loading indicator**: Hiển thị trạng thái loading khi gọi API
- **Responsive design**: Tự động điều chỉnh theo kích thước màn hình

### 🎯 Filter nâng cao
- **Lọc theo nhóm**: Lấy danh sách nhóm trường tùy chỉnh từ API và cho phép lọc
- **Tìm kiếm từ khóa**: Debounce search, chỉ gọi API khi nhấn Enter
- **Sắp xếp**: <PERSON>, ng<PERSON>y tạo với hướng ASC/DESC
- **Infinite scroll**: Pagination với lazy loading

### ✨ ActiveFilter
- **Hiển thị filter**: Các filter đang được áp dụng
- **Xóa riêng lẻ**: Có thể xóa từng filter một cách độc lập
- **Xóa tất cả**: Button để clear tất cả filter cùng lúc
- **Badge đếm**: Hiển thị số lượng filter đang active

### 🚀 API Integration
- **Custom Group API**: Tích hợp với `/user/custom-group-forms` để lấy nhóm
- **Custom Field API**: Tích hợp với `/user/custom-fields` với filter parameters
- **Cache & Performance**: Sử dụng TanStack Query cho caching
- **Error Handling**: Xử lý lỗi và fallback states

## Cách sử dụng

### Import
```typescript
import { AdvancedCustomFieldSelector } from '@/modules/business/components';
```

### Basic Usage
```typescript
const [selectedFields, setSelectedFields] = useState<CustomFieldData[]>([]);

const handleFieldSelect = (fieldData: CustomFieldData) => {
  // Toggle field selection
  setSelectedFields(prev => {
    const existingIndex = prev.findIndex(field => field.id === fieldData.id);
    if (existingIndex !== -1) {
      return prev.filter((_, index) => index !== existingIndex);
    } else {
      return [...prev, fieldData];
    }
  });
};

<AdvancedCustomFieldSelector
  onFieldSelect={handleFieldSelect}
  selectedFieldIds={selectedFields.map(f => f.id)}
  placeholder="Nhập từ khóa để tìm kiếm..."
  className="mb-4"
/>
```

### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `onFieldSelect` | `(fieldData: CustomFieldData) => void` | ✅ | Callback khi chọn field |
| `selectedFieldIds` | `number[]` | ✅ | Danh sách ID các field đã chọn |
| `placeholder` | `string` | ❌ | Placeholder cho input search |
| `className` | `string` | ❌ | CSS class bổ sung |

### CustomFieldData Interface
```typescript
interface CustomFieldData {
  id: number;
  label: string;
  component: string;
  configId?: string;
  type: string;
  required: boolean;
}
```

## So sánh với SimpleCustomFieldSelector

| Tính năng | SimpleCustomFieldSelector | AdvancedCustomFieldSelector |
|-----------|---------------------------|----------------------------|
| Tìm kiếm cơ bản | ✅ | ✅ |
| MenuIconBar | ❌ | ✅ |
| Filter theo nhóm | ❌ | ✅ |
| ActiveFilter | ❌ | ✅ |
| Sắp xếp | ❌ | ✅ |
| Loading states | ✅ | ✅ |
| Infinite scroll | ✅ | ✅ |
| API integration | ✅ | ✅ (nâng cao) |

## API Endpoints

### Custom Group Forms
```
GET /user/custom-group-forms
Query params:
- page: number
- limit: number
- search: string
```

### Custom Fields
```
GET /user/custom-fields
Query params:
- page: number
- limit: number
- search: string
- groupId: number (mới)
- sortBy: string (mới)
- sortDirection: 'ASC' | 'DESC' (mới)
```

## Hook: useCustomFieldWithFilter

Hook mới quản lý state và logic cho component:

```typescript
const {
  items,
  loading,
  hasMore,
  groupOptions,
  filters,
  search,
  filterByGroup,
  sort,
  loadMore,
  clearFilters,
  getActiveFiltersCount,
} = useCustomFieldWithFilter({
  pageSize: 20,
  initialFilters: {
    search: '',
    groupId: null,
    sortBy: 'label',
    sortDirection: 'ASC',
  },
});
```

## Demo

Xem demo tại: `src/modules/business/pages/AdvancedCustomFieldSelectorDemo.tsx`

## Migration từ SimpleCustomFieldSelector

1. **Import mới**:
```typescript
// Cũ
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';

// Mới
import AdvancedCustomFieldSelector from '../AdvancedCustomFieldSelector';
```

2. **Props không đổi**: Các props cơ bản giữ nguyên
3. **Tính năng mới**: Tự động có MenuIconBar và filter mà không cần config thêm

## Performance

- **Debounced search**: Giảm số lượng API calls
- **Cached groups**: Nhóm trường tùy chỉnh được cache
- **Lazy loading**: Chỉ load khi cần thiết
- **Optimized re-renders**: Sử dụng useCallback và useMemo

## Responsive Design

- **Mobile**: Menu dropdown tự động điều chỉnh width
- **Tablet**: Grid layout cho selected fields
- **Desktop**: Full features với optimal spacing
