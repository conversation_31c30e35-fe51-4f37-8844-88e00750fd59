import React from 'react';
import { GenericCustomFieldSelector, GenericCustomFieldData } from '@/shared/components/common';
import { useCustomFieldSearch } from '../hooks/useCustomFieldSearch';

export interface BusinessCustomFieldData extends GenericCustomFieldData {}

interface BusinessCustomFieldSelectorProps {
  onFieldSelect: (fieldData: BusinessCustomFieldData) => void;
  selectedFieldIds: number[];
  placeholder?: string;
}

/**
 * Business Custom Field Selector - wrapper cho GenericCustomFieldSelector
 * Sử dụng business API và logic
 */
const BusinessCustomFieldSelector: React.FC<BusinessCustomFieldSelectorProps> = ({
  onFieldSelect,
  selectedFieldIds,
  placeholder = 'Nhập từ khóa và nhấn Enter để tìm kiếm...',
}) => {
  const businessCustomFieldSearch = useCustomFieldSearch();

  // Wrapper function để transform business search function
  const searchFunction = async (params: { search?: string; page?: number; limit?: number }) => {
    try {
      const response = await businessCustomFieldSearch({
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 20,
      });

      // Transform business response to generic format
      return {
        items: response.items.map(item => ({
          id: item.id,
          label: item.label,
          component: item.component,
          configId: item.configId,
          type: item.type,
          required: item.required,
        })),
        totalItems: response.totalItems,
        totalPages: response.totalPages,
        currentPage: response.currentPage,
        hasNextPage: response.hasNextPage,
      };
    } catch (error) {
      console.error('Error in business custom field search:', error);
      throw error;
    }
  };

  return (
    <GenericCustomFieldSelector
      onFieldSelect={onFieldSelect}
      selectedFieldIds={selectedFieldIds}
      placeholder={placeholder}
      searchFunction={searchFunction}
      title="Trường tùy chỉnh"
      translationNamespace="business"
    />
  );
};

export default BusinessCustomFieldSelector;
