import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Loading,
  Icon,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
} from '@/shared/components/common';
import { useConversion } from '../hooks/useConversionQuery';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

interface ConversionDetailFormProps {
  id: number;
  onClose: () => void;
}

/**
 * Component hiển thị chi tiết conversion trong SlideInForm theo phong cách ProductForm
 */
const ConversionDetailForm: React.FC<ConversionDetailFormProps> = ({ id, onClose }) => {
  const { t } = useTranslation(['business', 'common']);
  const { data: conversion, isLoading, error } = useConversion(id);

  // Hàm format date an toàn
  const formatDate = (timestamp: number | string) => {
    try {
      if (!timestamp) return 'N/A';

      let date: Date;

      // Nếu là string, kiểm tra xem có phải timestamp số không
      if (typeof timestamp === 'string') {
        if (/^\d+$/.test(timestamp)) {
          date = new Date(Number(timestamp));
        } else {
          date = new Date(timestamp);
        }
      } else {
        date = new Date(timestamp);
      }

      // Kiểm tra date có hợp lệ không
      if (isNaN(date.getTime())) {
        return 'N/A';
      }

      return format(date, 'dd/MM/yyyy HH:mm', { locale: vi });
    } catch {
      return 'N/A';
    }
  };

  if (isLoading) {
    return (
      <FormMultiWrapper title={t('business:conversion.detail.title', 'Chi tiết chuyển đổi')}>
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" size="sm" onClick={onClose} className="ml-auto">
            <Icon name="x" size="sm" />
          </Button>
        </div>
        <Loading />
      </FormMultiWrapper>
    );
  }

  if (error || !conversion) {
    return (
      <FormMultiWrapper title={t('business:conversion.detail.title', 'Chi tiết chuyển đổi')}>
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" size="sm" onClick={onClose} className="ml-auto">
            <Icon name="x" size="sm" />
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <Icon name="alert-circle" size="lg" className="text-red-500" />
          <Typography variant="h6" className="text-red-500">
            {t('business:conversion.detail.error', 'Không thể tải thông tin chuyển đổi')}
          </Typography>
        </div>
      </FormMultiWrapper>
    );
  }

  return (
    <FormMultiWrapper
      title={`${t('business:conversion.detail.title', 'Chi tiết chuyển đổi')} #${conversion.id}`}
    >
      <div className="space-y-4">
        {/* 1. Thông tin chuyển đổi */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:conversion.detail.info', '1. Thông tin chuyển đổi')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.id', 'ID')}
                </Typography>
                <Typography variant="body1">{conversion.id}</Typography>
              </div>

              <div>
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.type', 'Loại chuyển đổi')}
                </Typography>
                <Typography variant="body1">{conversion.conversionType || 'N/A'}</Typography>
              </div>

              <div>
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.source', 'Nguồn')}
                </Typography>
                <Typography variant="body1">{conversion.source || 'N/A'}</Typography>
              </div>

              <div>
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.userId', 'ID người dùng')}
                </Typography>
                <Typography variant="body1">{conversion.userId || 'N/A'}</Typography>
              </div>

              <div>
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.convertCustomerId', 'ID khách hàng chuyển đổi')}
                </Typography>
                <Typography variant="body1">{conversion.convertCustomerId || 'N/A'}</Typography>
              </div>

              <div>
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.createdAt', 'Thời gian tạo')}
                </Typography>
                <Typography variant="body1">{formatDate(conversion.createdAt)}</Typography>
              </div>

              <div>
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.updatedAt', 'Cập nhật lần cuối')}
                </Typography>
                <Typography variant="body1">{formatDate(conversion.updatedAt)}</Typography>
              </div>
            </div>

            {conversion.notes && (
              <div className="col-span-1 md:col-span-2">
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.notes', 'Ghi chú')}
                </Typography>
                <Typography variant="body1">{conversion.notes}</Typography>
              </div>
            )}

            {conversion.content && (
              <div className="col-span-1 md:col-span-2">
                <Typography variant="caption" className="text-gray-500 mb-1">
                  {t('business:conversion.content', 'Nội dung')}
                </Typography>
                <Typography variant="body1">
                  {JSON.stringify(conversion.content, null, 2)}
                </Typography>
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* 2. Thông tin khách hàng */}
        {conversion.convertCustomer && (
          <CollapsibleCard
            title={
              <Typography variant="h6" className="font-medium">
                {t('business:conversion.customer.info', '2. Thông tin khách hàng')}
              </Typography>
            }
            defaultOpen={true}
            className="mb-4"
          >
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:customer.id', 'ID khách hàng')}
                  </Typography>
                  <Typography variant="body1">{conversion.convertCustomer.id}</Typography>
                </div>

                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:customer.name', 'Tên khách hàng')}
                  </Typography>
                  <Typography variant="body1">{conversion.convertCustomer.name}</Typography>
                </div>

                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:customer.email', 'Email')}
                  </Typography>
                  <Typography variant="body1">
                    {typeof conversion.convertCustomer.email === 'string'
                      ? conversion.convertCustomer.email
                      : conversion.convertCustomer.email?.primary || 'N/A'}
                  </Typography>
                </div>

                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:customer.phone', 'Số điện thoại')}
                  </Typography>
                  <Typography variant="body1">
                    {conversion.convertCustomer.phone || 'N/A'}
                  </Typography>
                </div>

                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:customer.platform', 'Nền tảng')}
                  </Typography>
                  <Typography variant="body1">
                    {conversion.convertCustomer.platform || 'N/A'}
                  </Typography>
                </div>

                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:customer.createdAt', 'Ngày tạo')}
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(conversion.convertCustomer.createdAt)}
                  </Typography>
                </div>
              </div>
            </div>
          </CollapsibleCard>
        )}
        <div className="flex justify-end pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:close', 'Đóng')}
            onClick={onClose}
          />
        </div>
      </div>
    </FormMultiWrapper>
  );
};

export default ConversionDetailForm;
