import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  SearchableDropdown,
  SearchableDropdownItem,
} from '@/shared/components/common';
import { CustomFieldService } from '../services/custom-field.service';

export interface CustomFieldData extends Record<string, unknown> {
  id: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
}

interface CustomFieldSelectorProps {
  onFieldSelect: (fieldData: CustomFieldData) => void;
  selectedFieldIds: number[];
  placeholder?: string;
}

/**
 * Component để chọn custom fields với dropdown và checkbox
 */
const CustomFieldSelector: React.FC<CustomFieldSelectorProps> = ({
  onFieldSelect,
  selectedFieldIds,
  placeholder = 'Tìm trường tùy chỉnh',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Load custom fields with search and pagination
  const loadCustomFields = useCallback(async (searchTerm: string, page: number): Promise<SearchableDropdownItem[]> => {
    try {
      const response = await CustomFieldService.getCustomFields({
        search: searchTerm,
        page,
        limit: 20, // Load 20 items per page
      });

      return response.result.items.map(item => ({
        id: item.id,
        label: item.label,
        description: item.type,
        badge: item.component,
        required: item.required,
        data: {
          id: item.id,
          label: item.label,
          component: item.component,
          type: item.type,
          required: item.required,
        } as CustomFieldData,
      }));
    } catch (error) {
      console.error('Error loading custom fields:', error);
      return [];
    }
  }, []);

  // Handle item selection
  const handleItemSelect = useCallback((item: SearchableDropdownItem) => {
    const fieldData = item.data as unknown as CustomFieldData;
    console.log('Custom field selected:', fieldData);
    onFieldSelect(fieldData);
  }, [onFieldSelect]);

  return (
    <SearchableDropdown
      items={[]}
      selectedIds={selectedFieldIds}
      onItemSelect={handleItemSelect}
      onSearch={loadCustomFields}
      placeholder={placeholder}
      title={t('business:product.form.customFields.title', 'Trường tùy chỉnh')}
      multiSelect={true}
      showSearch={true}
      maxHeight="300px"
      className="mb-4"
    />
  );
};

export default CustomFieldSelector;
