import React, { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { Controller } from 'react-hook-form';
import { WarehouseService } from '../../services/warehouse.service';

import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  UpdateProductDto,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';

import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useProduct, useUpdateProduct } from '../../hooks/useProductQuery';

// Note: Response interfaces are now defined in product.types.ts

interface PhysicalProductEditFormProps {
  productId: number;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho form values
interface PhysicalProductFormValues {
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  inventory?: {
    warehouseId?: string | number;
    availableQuantity?: string | number;
    sku?: string;
    barcode?: string;
  };
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  classifications?: ProductVariant[];
}

// Extended FileWithMetadata interface for variant images
interface ExtendedFileWithMetadata extends FileWithMetadata {
  url?: string | undefined;
  name?: string | undefined;
  size?: number | undefined;
  type?: string | undefined;
  metadata?: {
    key?: string;
    url?: string;
    isExisting?: boolean;
  };
}

// Interface cho biến thể sản phẩm trong form
interface ProductVariant {
  id: number; // ID tạm thời cho UI
  name: string;
  priceDescription?: string; // Mô tả giá cho biến thể (khi typePrice = STRING_PRICE)
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  // ✅ Quản lý tồn kho và ảnh cho mỗi biến thể
  sku?: string; // SKU riêng cho biến thể
  availableQuantity?: string | number; // Số lượng có sẵn
  images?: ExtendedFileWithMetadata[]; // Nhiều ảnh cho biến thể
  customFields: SelectedCustomField[];
}

/**
 * Form chỉnh sửa sản phẩm vật lý
 */
const PhysicalProductEditForm: React.FC<PhysicalProductEditFormProps> = ({
  productId,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Gọi API lấy chi tiết sản phẩm
  const { data: product, isLoading: isLoadingProduct, error: productError } = useProduct(productId);

  // Hook để cập nhật sản phẩm
  const updateProductMutation = useUpdateProduct();

  // Schema validation với giá cố định
  const productSchema = z
    .object({
      name: z.string().min(1, 'Tên sản phẩm không được để trống'),
      listPrice: z.union([z.string(), z.number()]),
      salePrice: z.union([z.string(), z.number()]),
      currency: z.string(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      shipmentConfig: z
        .object({
          lengthCm: z.union([z.string(), z.number()]).optional(),
          widthCm: z.union([z.string(), z.number()]).optional(),
          heightCm: z.union([z.string(), z.number()]).optional(),
          weightGram: z.union([z.string(), z.number()]).optional(),
        })
        .optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      classifications: z.any().optional(),
      inventory: z
        .object({
          warehouseId: z.union([z.string(), z.number()]).optional(),
          availableQuantity: z.union([z.string(), z.number()]).optional(),
          sku: z.string().optional(),
          barcode: z.string().optional(),
        })
        .optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra listPrice
      if (!data.listPrice || data.listPrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá niêm yết',
          path: ['listPrice'],
        });
      } else {
        const listPrice = Number(data.listPrice);
        if (isNaN(listPrice) || listPrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá niêm yết phải là số >= 0',
            path: ['listPrice'],
          });
        }
      }

      // Kiểm tra salePrice
      if (!data.salePrice || data.salePrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá bán',
          path: ['salePrice'],
        });
      } else {
        const salePrice = Number(data.salePrice);
        if (isNaN(salePrice) || salePrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá bán phải là số >= 0',
            path: ['salePrice'],
          });
        }
      }

      // Kiểm tra currency
      if (!data.currency || data.currency.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng chọn đơn vị tiền tệ',
          path: ['currency'],
        });
      }

      // Kiểm tra giá niêm yết phải lớn hơn giá bán
      if (data.listPrice && data.salePrice && data.listPrice !== '' && data.salePrice !== '') {
        const listPrice = Number(data.listPrice);
        const salePrice = Number(data.salePrice);

        if (!isNaN(listPrice) && !isNaN(salePrice) && listPrice > 0 && salePrice > 0) {
          if (listPrice <= salePrice) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Giá niêm yết phải lớn hơn giá bán',
              path: ['listPrice'],
            });
          }
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<ExtendedFileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State để track ảnh bị xóa
  const [deletedImages, setDeletedImages] = useState<Array<{ key: string; url: string }>>([]);

  // State cho phân loại sản phẩm
  const [productClassifications, setProductClassifications] = useState<ProductVariant[]>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Note: Cache invalidation is handled by useUpdateProduct hook

  // Khởi tạo giá trị mặc định từ product data
  const defaultValues = useMemo(() => {
    if (!product) return {
      name: '',
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      description: '',
      tags: [],
      shipmentConfig: {
        lengthCm: '',
        widthCm: '',
        heightCm: '',
        weightGram: '',
      },
      inventory: {
        warehouseId: '',
        availableQuantity: '',
        sku: '',
        barcode: '',
      },
      customFields: [],
      media: [],
      classifications: [],
    };

    console.log('🔍 [PhysicalProductEditForm] Product data:', product);
    console.log('🏪 [PhysicalProductEditForm] Inventory data:', product.inventory);

    // Xử lý giá sản phẩm dựa trên typePrice
    const hasPrice = product.typePrice === PriceTypeEnum.HAS_PRICE;
    let listPrice = '';
    let salePrice = '';
    let currency = 'VND';

    if (hasPrice && product.price) {
      const priceData = product.price as HasPriceDto;
      listPrice = priceData.listPrice?.toString() || '';
      salePrice = priceData.salePrice?.toString() || '';
      currency = priceData.currency || 'VND';
    }

    return {
      name: product.name || '',
      listPrice,
      salePrice,
      currency,
      description: product.description || '',
      tags: product.tags || [],
      shipmentConfig: {
        lengthCm: product.shipmentConfig?.lengthCm?.toString() || '',
        widthCm: product.shipmentConfig?.widthCm?.toString() || '',
        heightCm: product.shipmentConfig?.heightCm?.toString() || '',
        weightGram: product.shipmentConfig?.weightGram?.toString() || '',
      },
      inventory: {
        warehouseId: product.inventory?.warehouseId?.toString() || '',
        availableQuantity: product.inventory?.availableQuantity?.toString() || '',
        sku: product.inventory?.sku || '',
        barcode: product.inventory?.barcode || '',
      },
      customFields: [],
      media: [],
      classifications: [],
    };
  }, [product]);

  // Sync tags với state khi product thay đổi
  useEffect(() => {
    if (product?.tags) {
      setTempTags(product.tags);
    }
  }, [product]);

  // Sync existing images với state khi product thay đổi
  useEffect(() => {
    if (product?.images && product.images.length > 0) {
      const productImages = product.images;
      console.log('🔄 [PhysicalProductEditForm] Syncing existing images:', productImages);

      // Convert existing images to ExtendedFileWithMetadata format for display
      const existingImages: ExtendedFileWithMetadata[] = productImages.map((img, index) => {
        // Extract file name from URL or use a default name
        const urlParts = img.url.split('/');
        const fileName = urlParts[urlParts.length - 1]?.split('?')[0] || `image-${index + 1}`;

        console.log(`📷 [PhysicalProductEditForm] Processing image ${index}:`, {
          key: img.key,
          url: img.url,
          fileName,
          position: img.position,
        });

        const existingImageFile = {
          id: `existing-${img.key}`,
          file: new File([], fileName, { type: 'image/jpeg' }), // Placeholder file with proper name
          preview: img.url, // This is the key - set preview to the image URL for display
          // Thêm metadata để track key và url cho việc xóa
          metadata: {
            key: img.key,
            url: img.url,
            isExisting: true,
          },
        };

        console.log(`📷 [PhysicalProductEditForm] Created existing image file:`, {
          id: existingImageFile.id,
          fileName,
          hasMetadata: !!existingImageFile.metadata,
          key: img.key,
        });

        return existingImageFile;
      });

      // Chỉ set lại mediaFiles nếu chưa có ảnh nào hoặc số lượng ảnh khác nhau
      // Điều này tránh ghi đè ảnh mới đã được thêm vào
      setMediaFiles(prev => {
        console.log('🔄 [PhysicalProductEditForm] Syncing existing images:', {
          existingImagesFromAPI: existingImages.length,
          currentExistingInState: prev.filter(file => file.id.startsWith('existing-')).length,
          currentNewInState: prev.filter(file => !file.id.startsWith('existing-')).length,
          totalInState: prev.length,
        });

        // Nếu chưa có ảnh existing nào, set toàn bộ
        const currentExistingCount = prev.filter(file => file.id.startsWith('existing-')).length;

        if (currentExistingCount === 0) {
          console.log('📷 [PhysicalProductEditForm] No existing images in state, setting all from API');
          return existingImages;
        }

        // Nếu số lượng ảnh từ API khác với số ảnh existing hiện tại, cập nhật
        if (existingImages.length !== currentExistingCount) {
          // Giữ lại ảnh mới (không phải existing) và thêm ảnh existing mới
          const newFiles = prev.filter(file => !file.id.startsWith('existing-'));
          const result = [...existingImages, ...newFiles];
          console.log('📷 [PhysicalProductEditForm] Merging existing and new images:', {
            existingFromAPI: existingImages.length,
            newInState: newFiles.length,
            totalResult: result.length,
          });
          return result;
        }

        // Nếu số lượng giống nhau, chỉ cập nhật URL cho ảnh existing (có thể URL đã thay đổi)
        // nhưng preserve metadata hiện tại để tránh mất thông tin
        const result: ExtendedFileWithMetadata[] = prev.map(file => {
          if (file.id.startsWith('existing-')) {
            const matchingImage = existingImages.find(img => img.id === file.id);
            if (matchingImage) {
              return {
                ...file,
                preview: file.preview || matchingImage.preview, // Giữ preview hiện tại, fallback to API
                metadata: file.metadata || matchingImage.metadata, // Giữ metadata hiện tại, fallback to API
              } as ExtendedFileWithMetadata;
            }
          }
          return file;
        });

        console.log('📷 [PhysicalProductEditForm] Updated existing images URLs, preserved metadata');
        return result;
      });
    } else {
      // Nếu không có ảnh từ API, chỉ giữ lại ảnh mới
      setMediaFiles(prev => prev.filter(file => !file.id.startsWith('existing-')));
    }
  }, [product]);

  // Sync existing custom fields với state khi product thay đổi
  useEffect(() => {
    if (product?.metadata?.customFields && product.metadata.customFields.length > 0) {
      const existingCustomFields: SelectedCustomField[] = product.metadata.customFields.map((field) => {
        return {
          id: field.id,
          fieldId: field.id, // Sử dụng id làm fieldId
          label: field.label,
          component: field.type, // component = type từ API
          type: field.type,
          required: field.required || false,
          configJson: field.configJson || {},
          value: field.value || { value: '' },
        };
      });

      setProductCustomFields(existingCustomFields);
    } else {
      setProductCustomFields([]);
    }
  }, [product?.metadata?.customFields]);

  // Sync existing classifications với state khi product thay đổi
  useEffect(() => {
    if (product?.classifications && product.classifications.length > 0) {
      const productClassifications = product.classifications as Array<{
        id: number;
        type: string;
        price?: { listPrice?: number; salePrice?: number; currency?: string; priceDescription?: string };
        images?: Array<{ url: string; key: string }>;
        customFields?: Array<{ customFieldId: number; value: unknown }>;
      }>;
      const existingClassifications: ProductVariant[] = productClassifications.map((classification) => {
        // Convert classification images to ExtendedFileWithMetadata format
        const classificationImages: ExtendedFileWithMetadata[] = classification.images?.map((img, index) => {
          // Extract file name from URL or use a default name
          const urlParts = img.url.split('/');
          const fileName = urlParts[urlParts.length - 1]?.split('?')[0] || `classification-image-${index + 1}`;

          const classificationImageFile = {
            id: `classification-${classification.id}-existing-${img.key}`,
            file: new File([], fileName, { type: 'image/jpeg' }),
            preview: img.url, // Set preview to the image URL for display
            metadata: {
              key: img.key,
              url: img.url,
              isExisting: true,
            },
          };

          console.log(`📷 [PhysicalProductEditForm] Created classification image file:`, {
            classificationId: classification.id,
            id: classificationImageFile.id,
            fileName,
            hasMetadata: !!classificationImageFile.metadata,
            isExisting: classificationImageFile.metadata.isExisting,
            key: img.key,
          });

          return classificationImageFile;
        }) || [];

        // Convert classification custom fields
        const classificationCustomFields: SelectedCustomField[] = classification.customFields?.map((field) => {
          return {
            id: Date.now() + Math.random(), // Generate temporary ID
            fieldId: field.customFieldId,
            label: `Custom Field ${field.customFieldId}`, // We might need to fetch field details
            component: 'text', // Default component type
            type: 'text', // Default type
            required: false,
            configJson: {},
            value: typeof field.value === 'object' && field.value !== null ? field.value as Record<string, unknown> : { value: field.value },
          };
        }) || [];

        return {
          id: classification.id,
          name: classification.type,
          priceDescription: classification.price?.priceDescription || '',
          listPrice: classification.price?.listPrice?.toString() || '',
          salePrice: classification.price?.salePrice?.toString() || '',
          currency: classification.price?.currency || 'VND',
          sku: '', // Not available in API response for physical products
          availableQuantity: '', // Not available in API response for physical products
          images: classificationImages,
          customFields: classificationCustomFields,
        };
      });

      // Chỉ sync classifications nếu chưa có data hoặc số lượng thay đổi
      // Tránh ghi đè state khi đang trong quá trình upload
      setProductClassifications(prev => {
        if (prev.length === 0) {
          console.log('📷 [PhysicalProductEditForm] No classifications in state, setting from API');
          return existingClassifications;
        }

        if (prev.length !== existingClassifications.length) {
          console.log('📷 [PhysicalProductEditForm] Classification count changed, updating');
          return existingClassifications;
        }

        // Merge strategy: preserve existing state but update basic info (name, price, etc.)
        // without touching images to avoid overwriting uploads
        console.log('📷 [PhysicalProductEditForm] Merging classifications while preserving images state');
        return prev.map(prevVariant => {
          const apiVariant = existingClassifications.find(api => api.id === prevVariant.id);
          if (apiVariant) {
            // Update basic info but preserve images from state
            return {
              ...apiVariant,
              images: prevVariant.images || [], // Preserve images from state with fallback
            } as ProductVariant;
          }
          return prevVariant;
        });
      });
    } else {
      setProductClassifications([]);
    }
  }, [product]);

  // Handler để xử lý khi ảnh bị xóa hoặc thêm mới
  const handleMediaFilesChange = useCallback((files: ExtendedFileWithMetadata[]) => {
    // Tìm những ảnh existing đã bị xóa
    const currentExistingFiles = mediaFiles.filter(file => file.id.startsWith('existing-'));
    const newExistingFiles = files.filter(file => file.id.startsWith('existing-'));

    const removedFiles = currentExistingFiles.filter(
      currentFile => !newExistingFiles.find(newFile => newFile.id === currentFile.id)
    );

    // Thêm vào danh sách ảnh bị xóa
    if (removedFiles.length > 0) {
      const removedImageInfo = removedFiles
        .filter(file => file.metadata?.key && file.metadata?.url)
        .map(file => ({
          key: file.metadata!.key as string,
          url: file.metadata!.url as string,
        }));

      setDeletedImages(prev => [...prev, ...removedImageInfo]);
    }

    // Preserve metadata for existing files khi merge với files mới
    const preservedFiles: ExtendedFileWithMetadata[] = files.map(file => {
      // Nếu là existing file, tìm và preserve metadata từ state hiện tại
      if (file.id.startsWith('existing-')) {
        const existingFile = mediaFiles.find(f => f.id === file.id);
        if (existingFile && existingFile.metadata) {
          return {
            ...file,
            metadata: existingFile.metadata, // Preserve existing metadata
            preview: existingFile.preview || file.preview || '', // Preserve preview URL with fallback
          } as ExtendedFileWithMetadata;
        }
      }
      return file;
    });

    console.log('💾 [PhysicalProductEditForm] Setting preserved files:', {
      preservedCount: preservedFiles.length,
      existingWithMetadata: preservedFiles.filter(f => f.id.startsWith('existing-') && f.metadata).length,
    });

    setMediaFiles(preservedFiles);
  }, [mediaFiles]);

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    // Test đơn giản trước
    if (!values['name']) {
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sản phẩm',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as PhysicalProductFormValues;
      setIsUploading(true);
      // Chuyển đổi giá trị form thành dữ liệu API (đã sync với Backend)
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Chỉ gửi những trường cần thiết cho sản phẩm vật lý theo request body format
      const productData: UpdateProductDto = {
        name: formValues.name,
        price: priceData,
        typePrice: PriceTypeEnum.HAS_PRICE, // Bắt buộc cho physical products
      };

      console.log('🚀 [PhysicalProductEditForm] Updating physical product with data:', productData);

      // Chỉ thêm các thuộc tính optional khi có giá trị
      if (formValues.description && formValues.description.trim()) {
        productData.description = formValues.description.trim();
      }

      if (tempTags && tempTags.length > 0) {
        productData.tags = tempTags;
      }

      const shipmentConfig = getShipmentConfig(formValues);
      if (shipmentConfig && Object.keys(shipmentConfig).length > 0) {
        productData.shipmentConfig = shipmentConfig;
      }

      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
          const fieldValue = field.value?.['value'];

          // Nếu là undefined hoặc null thì loại bỏ
          if (fieldValue === undefined || fieldValue === null) {
            return false;
          }

          // Nếu là string rỗng thì loại bỏ
          if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
            return false;
          }

          // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
          return true;
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'],
          },
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      // Xử lý ảnh theo format yêu cầu: DELETE existing images + ADD new images
      const imageOperations: Array<{
        operation: 'DELETE' | 'ADD';
        key?: string;
        mimeType?: string;
      }> = [];

      // Thêm operations DELETE cho ảnh bị xóa
      if (deletedImages.length > 0) {
        console.log('🗑️ [PhysicalProductEditForm] Processing deleted images:', deletedImages);
        deletedImages.forEach(deletedImage => {
          imageOperations.push({
            operation: 'DELETE',
            key: deletedImage.key,
          });
        });
      }

      // Thêm operations ADD cho ảnh mới
      const newMediaFiles = mediaFiles.filter(file => !file.id.startsWith('existing-'));
      if (newMediaFiles.length > 0) {
        console.log('📷 [PhysicalProductEditForm] Processing new images:', newMediaFiles);
        newMediaFiles.forEach((file) => {
          imageOperations.push({
            operation: 'ADD',
            mimeType: file.file.type || 'image/jpeg',
          });
        });
      }

      // Chỉ thêm images vào request nếu có operations
      if (imageOperations.length > 0) {
        productData.images = imageOperations;
      }

      if (productClassifications.length > 0) {
        console.log('📦 [PhysicalProductEditForm] Processing classifications:', productClassifications);

        productData.classifications = productClassifications.map(variant => {
          const classification: {
            id?: number;
            type: string;
            price?: {
              listPrice?: number;
              salePrice?: number;
              currency?: string;
              priceDescription?: string;
            };
            customFields?: Array<{
              customFieldId: number;
              value: {
                value: unknown;
              };
            }>;
            imagesMediaTypes?: string[];
          } = {
            type: variant.name,
          };

          // Add ID if it exists (for existing classifications)
          if (variant.id && typeof variant.id === 'number' && variant.id > 0) {
            classification.id = variant.id;
          }

          // Add price information
          if (variant.listPrice || variant.salePrice || variant.currency) {
            classification.price = {};

            // Only add properties if they have valid values
            const listPriceNum = Number(variant.listPrice);
            if (!isNaN(listPriceNum) && listPriceNum > 0) {
              classification.price.listPrice = listPriceNum;
            }

            const salePriceNum = Number(variant.salePrice);
            if (!isNaN(salePriceNum) && salePriceNum > 0) {
              classification.price.salePrice = salePriceNum;
            }

            if (variant.currency && variant.currency.trim()) {
              classification.price.currency = variant.currency.trim();
            }

            // Thêm mô tả giá nếu có
            if (variant.priceDescription && variant.priceDescription.trim()) {
              classification.price.priceDescription = variant.priceDescription.trim();
            }
          }

          // Add custom fields
          const filteredCustomFields = variant.customFields
            .filter(field => {
              // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
              const fieldValue = field.value?.['value'];

              // Nếu là undefined hoặc null thì loại bỏ
              if (fieldValue === undefined || fieldValue === null) {
                return false;
              }

              // Nếu là string rỗng thì loại bỏ
              if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
                return false;
              }

              // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
              return true;
            })
            .map(field => ({
              customFieldId: field.fieldId,
              value: {
                value: field.value?.['value'],
              },
            }));

          if (filteredCustomFields.length > 0) {
            classification.customFields = filteredCustomFields;
          }

          // Add imagesMediaTypes only for new variant images (not existing ones)
          if (variant.images && variant.images.length > 0) {
            // Filter out existing images to only get new images that need upload
            const newVariantImages = variant.images.filter(file =>
              !file.id.startsWith('existing-') &&
              !file.id.startsWith('classification-') &&
              !file.metadata?.isExisting
            );

            // Only add imagesMediaTypes if there are actually new images to upload
            if (newVariantImages.length > 0) {
              classification.imagesMediaTypes = newVariantImages.map(img => img.file?.type || 'image/jpeg');
            }
          }
          return classification;
        });
      }

      // Gọi API cập nhật sản phẩm
      const response = await updateProductMutation.mutateAsync({
        id: productId,
        data: productData,
      });

      const newMediaFilesForUpload = mediaFiles.filter(file => !file.id.startsWith('existing-'));

      if (newMediaFilesForUpload.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls && response.uploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              // Tạo mapping giữa new media files và upload URLs từ backend
              const uploadTasks = newMediaFilesForUpload.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index] as Record<string, unknown>;
                if (!uploadInfo) {
                  throw new Error(`Upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo['url'] as string,
                  key: uploadInfo['key'] as string,
                  index: uploadInfo['index'] as number,
                };
              });
              // Tạo array các file và URLs để upload cùng lúc
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh mới cùng lúc, skip cache invalidation trong hook
              // Cache invalidation đã được xử lý trong useUpdateProduct hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              // Cập nhật state để reflect ảnh sản phẩm chính đã được upload thành công
              // Chuyển ảnh từ trạng thái "uploading" sang "existing" với key từ API
              setMediaFiles(prev =>
                prev.map((img, index) => {
                  // Nếu là ảnh mới vừa upload, chuyển thành existing với key từ API
                  if (!img.id.startsWith('existing-')) {
                    // Lấy key từ upload task tương ứng
                    const uploadTask = uploadTasks[index];
                    const apiKey = uploadTask?.key;

                    if (apiKey) {
                      return {
                        ...img,
                        id: `existing-${apiKey}`,
                        metadata: {
                          ...img.metadata,
                          key: apiKey,
                          uploaded: true,
                          uploadedAt: Date.now(),
                          isExisting: true,
                        },
                      };
                    }
                  }
                  return img;
                })
              );

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            } else {
              NotificationUtil.warning({
                message: t(
                  'business:product.mediaUploadWarning',
                  'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'
                ),
                duration: 5000,
              });
            }
          } else {
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch {
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      // 2. Upload ảnh cho các phân loại (classifications)
      // Xử lý cả hai trường hợp: response có wrapper 'result' hoặc response trực tiếp
      let dataToCheck: Record<string, unknown> | null = null;

      if (response && typeof response === 'object') {
        // Trường hợp 1: Response có wrapper 'result'
        if ('result' in response && response.result) {
          dataToCheck = response.result as Record<string, unknown>;
        }
        // Trường hợp 2: Response trực tiếp là data
        else if ('classifications' in response) {
          dataToCheck = response as unknown as Record<string, unknown>;
        }
      }

      if (dataToCheck) {
        if (dataToCheck['classifications'] && Array.isArray(dataToCheck['classifications'])) {
          for (const classification of dataToCheck['classifications']) {
            const classificationObj = classification as Record<string, unknown>;
            if (classificationObj['uploadUrls'] &&
                typeof classificationObj['uploadUrls'] === 'object' &&
                classificationObj['uploadUrls'] !== null) {
              const uploadUrlsObj = classificationObj['uploadUrls'] as Record<string, unknown>;

              if (uploadUrlsObj['imagesUploadUrls'] && Array.isArray(uploadUrlsObj['imagesUploadUrls'])) {
                const classificationId = classificationObj['id'];
                const uploadUrls = uploadUrlsObj['imagesUploadUrls'];

                // Tìm variant tương ứng trong state để lấy ảnh mới
                // Có thể mapping theo ID thực hoặc theo thứ tự (cho variant mới)
                let variant = productClassifications.find(v => v.id === classificationId);

                // Nếu không tìm thấy theo ID (có thể là variant mới với ID tạm thời),
                // thử tìm theo thứ tự trong danh sách classifications
                if (!variant) {
                  const classificationIndex = (dataToCheck['classifications'] as Record<string, unknown>[]).findIndex((c) => c['id'] === classificationId);
                  if (classificationIndex >= 0 && classificationIndex < productClassifications.length) {
                    variant = productClassifications[classificationIndex];
                  }
                }

                if (variant && variant.images) {
                  // Filter out existing images (both regular existing and classification existing)
                  const newVariantImages = variant.images.filter(file =>
                    !file.id.startsWith('existing-') &&
                    !file.id.startsWith('classification-') &&
                    !file.metadata?.isExisting
                  );

                  if (newVariantImages.length > 0 && uploadUrls.length > 0) {
                    try {
                      // Chỉ upload số lượng ảnh mới thực tế, không phụ thuộc vào số upload URLs
                      const actualUploadCount = Math.min(newVariantImages.length, uploadUrls.length);

                      const variantUploadTasks = newVariantImages.slice(0, actualUploadCount).map((fileData, index) => {
                        const uploadInfo = uploadUrls[index] as Record<string, unknown>;
                        if (!uploadInfo) {
                          throw new Error(`Classification upload info not found for index ${index}`);
                        }
                        return {
                          file: fileData.file,
                          uploadUrl: uploadInfo['url'] as string,
                          key: uploadInfo['key'] as string,
                          index: uploadInfo['index'] as number,
                        };
                      });

                      const variantFilesToUpload = variantUploadTasks.map((task, index) => ({
                        file: task.file,
                        id: `variant_${classificationId}_${Date.now()}_${index}`,
                      }));
                      const variantUrlsToUpload = variantUploadTasks.map(task => task.uploadUrl);

                      // Upload ảnh cho phân loại này
                      await uploadProductImages(variantFilesToUpload, variantUrlsToUpload, {
                        skipCacheInvalidation: true,
                      });
                      // Cập nhật state để reflect ảnh đã được upload thành công
                      // Chỉ update ảnh thực sự được upload
                      setProductClassifications(prev =>
                        prev.map(v => {
                          if (v.id === classificationId) {
                            let uploadedCount = 0;
                            const updatedImages = (v.images || []).map((img) => {
                              // Nếu là ảnh mới vừa upload và chưa vượt quá số lượng upload thực tế
                              if (!img.id.startsWith('existing-') &&
                                  !img.id.startsWith('classification-') &&
                                  uploadedCount < variantUploadTasks.length) {

                                // Lấy key từ upload task tương ứng
                                const uploadTask = variantUploadTasks[uploadedCount];
                                const apiKey = uploadTask?.key;
                                uploadedCount++;

                                if (apiKey) {
                                  console.log(`📷 [PhysicalProductEditForm] Updating uploaded image with key: ${apiKey}`);
                                  return {
                                    ...img,
                                    id: `classification-${classificationId}-existing-${apiKey}`,
                                    metadata: {
                                      ...img.metadata,
                                      key: apiKey,
                                      uploaded: true,
                                      uploadedAt: Date.now(),
                                      isExisting: true,
                                    },
                                  };
                                }
                              }
                              return img;
                            });
                            return { ...v, images: updatedImages };
                          }
                          return v;
                        })
                      );

                      NotificationUtil.success({
                        message: t(
                          'business:product.variantImageUploadSuccess',
                          `Tải lên ảnh phân loại ${variant.name || classificationId} thành công`
                        ),
                        duration: 3000,
                      });
                    } catch (variantUploadError) {
                      console.error(`❌ [PhysicalProductEditForm] Error uploading images for classification ${classificationId}:`, variantUploadError);
                      NotificationUtil.warning({
                        message: t(
                          'business:product.variantImageUploadError',
                          `Có lỗi xảy ra khi tải lên ảnh phân loại ${variant.name || classificationId}`
                        ),
                        duration: 5000,
                      });
                    }
                  } else {
                    console.log(`ℹ️ [PhysicalProductEditForm] No new images to upload for classification ${classificationId}`);
                  }
                } else {
                  console.log(`⚠️ [PhysicalProductEditForm] No variant found for classification ${classificationId}`);
                }
              } else {
                console.log(`⚠️ [PhysicalProductEditForm] No imagesUploadUrls found for classification ${classificationObj['id']}`);
              }
            } else {
              console.log(`⚠️ [PhysicalProductEditForm] No uploadUrls found for classification ${classificationObj['id']}`);
            }
          }
        } else {
          console.log('⚠️ [PhysicalProductEditForm] No classifications found in response result');
        }
      } else {
        console.log('⚠️ [PhysicalProductEditForm] No result found in response for classifications upload');
      }

      setIsUploading(false);

      // Reset deletedImages sau khi update thành công
      setDeletedImages([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.updateSuccess', 'Cập nhật sản phẩm vật lý thành công'),
        duration: 3000,
      });

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      setIsUploading(false);
      // Kiểm tra nếu là lỗi validation
      if (error && typeof error === 'object' && 'issues' in error) {
        NotificationUtil.error({
          message: t('business:product.validationError', 'Lỗi validation dữ liệu'),
          duration: 3000,
        });
      } else {
        NotificationUtil.error({
          message: t('business:product.updateError', 'Có lỗi xảy ra khi cập nhật sản phẩm vật lý'),
          duration: 3000,
        });
      }
    }
  };

  // Hàm lấy dữ liệu giá cố định
  const getPriceData = (values: PhysicalProductFormValues): HasPriceDto => {
    // Kiểm tra đầy đủ các trường bắt buộc
    if (!values.listPrice || values.listPrice === '') {
      throw new Error('Vui lòng nhập giá niêm yết');
    }

    if (!values.salePrice || values.salePrice === '') {
      throw new Error('Vui lòng nhập giá bán');
    }

    if (!values.currency || values.currency.trim() === '') {
      throw new Error('Vui lòng chọn đơn vị tiền tệ');
    }

    const listPrice = Number(values.listPrice);
    const salePrice = Number(values.salePrice);

    if (isNaN(listPrice) || listPrice < 0) {
      throw new Error('Giá niêm yết phải là số >= 0');
    }

    if (isNaN(salePrice) || salePrice < 0) {
      throw new Error('Giá bán phải là số >= 0');
    }

    // Kiểm tra giá niêm yết phải lớn hơn giá bán
    if (listPrice <= salePrice) {
      throw new Error('Giá niêm yết phải lớn hơn giá bán');
    }

    return {
      listPrice,
      salePrice,
      currency: values.currency.trim(),
    };
  };

  // Hàm lấy dữ liệu cấu hình vận chuyển
  const getShipmentConfig = (values: PhysicalProductFormValues) => {
    if (!values.shipmentConfig) return undefined;

    const config = values.shipmentConfig;
    const hasAnyValue = config.lengthCm || config.widthCm || config.heightCm || config.weightGram;

    if (!hasAnyValue) return undefined;

    const result: {
      lengthCm?: number;
      widthCm?: number;
      heightCm?: number;
      weightGram?: number;
    } = {};

    if (config.lengthCm) {
      const lengthCm = Number(config.lengthCm);
      if (!isNaN(lengthCm) && lengthCm > 0) {
        result.lengthCm = lengthCm;
      }
    }

    if (config.widthCm) {
      const widthCm = Number(config.widthCm);
      if (!isNaN(widthCm) && widthCm > 0) {
        result.widthCm = widthCm;
      }
    }

    if (config.heightCm) {
      const heightCm = Number(config.heightCm);
      if (!isNaN(heightCm) && heightCm > 0) {
        result.heightCm = heightCm;
      }
    }

    if (config.weightGram) {
      const weightGram = Number(config.weightGram);
      if (!isNaN(weightGram) && weightGram > 0) {
        result.weightGram = weightGram;
      }
    }

    return result;
  };
  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        // Kiểm tra xem trường đã tồn tại chưa
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          // Nếu đã tồn tại, xóa nó (bỏ chọn)
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Xác định giá trị mặc định dựa trên kiểu dữ liệu
        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        let defaultValue: string | number | boolean = '';

        // Xác định giá trị mặc định dựa trên type hoặc component
        if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0;
        } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
          defaultValue = false;
        } else {
          defaultValue = '';
        }

        // Thêm trường mới với thông tin đầy đủ
        const newField: SelectedCustomField = {
          id: Date.now(), // ID tạm thời
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue }, // Giá trị đúng kiểu
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm phân loại mới
  const handleAddVariant = useCallback(() => {
    const newVariant: ProductVariant = {
      id: Date.now(), // ID tạm thời cho UI
      name: '',
      priceDescription: '', // Mô tả giá cho biến thể
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      // ✅ Giá trị mặc định cho tồn kho và ảnh
      sku: '',
      availableQuantity: '',
      images: [], // Khởi tạo mảng ảnh rỗng
      customFields: [],
    };

    setProductClassifications(prev => [...prev, newVariant]);
  }, []);

  // Xóa phân loại
  const handleRemoveVariant = useCallback((variantId: number) => {
    setProductClassifications(prev => prev.filter(variant => variant.id !== variantId));
  }, []);

  // Cập nhật phân loại
  const handleUpdateVariant = useCallback(
    (variantId: number, field: string, value: string | number) => {
      // ✅ Validation cho số lượng
      let validatedValue = value;

      // Validate số lượng không được âm
      if (field === 'availableQuantity') {
        const numValue = Number(value);
        if (numValue < 0) {
          validatedValue = 0;
        }
      }

      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return { ...variant, [field]: validatedValue };
          }
          return variant;
        })
      );
    },
    []
  );

  // ✅ THÊM MỚI: Xử lý upload nhiều ảnh cho biến thể
  const handleVariantImagesChange = useCallback(
    (variantId: number, files: ExtendedFileWithMetadata[]) => {
      console.log(`🔄 [PhysicalProductEditForm] handleVariantImagesChange called for variant ${variantId}:`, {
        filesCount: files.length,
        fileIds: files.map(f => ({ id: f.id, hasMetadata: !!f.metadata, isExisting: f.metadata?.isExisting })),
      });

      // Preserve metadata for existing classification images
      const preservedFiles: ExtendedFileWithMetadata[] = files.map(file => {
        // Nếu là existing classification file, preserve metadata
        if (file.id.startsWith('classification-') || file.metadata?.isExisting) {
          return file; // Giữ nguyên existing files
        }
        return file; // Giữ nguyên new files
      });

      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            console.log(`📷 [PhysicalProductEditForm] Updating images for variant ${variantId}:`, {
              oldImagesCount: variant.images?.length || 0,
              newImagesCount: preservedFiles.length,
            });
            return { ...variant, images: preservedFiles };
          }
          return variant;
        })
      );
    },
    []
  );

  // Thêm/xóa trường tùy chỉnh vào phân loại
  const handleToggleCustomFieldToVariant = useCallback(
    (variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            // Kiểm tra xem trường đã tồn tại trong phân loại chưa
            const existingFieldIndex = variant.customFields.findIndex(
              field => field.fieldId === fieldId
            );

            if (existingFieldIndex !== -1) {
              // Nếu đã tồn tại, xóa nó (bỏ chọn)
              return {
                ...variant,
                customFields: variant.customFields.filter(
                  (_, index) => index !== existingFieldIndex
                ),
              };
            }

            // Xác định giá trị mặc định dựa trên kiểu dữ liệu
            const fieldType = (fieldData?.['type'] as string) || 'text';
            const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

            let defaultValue: string | number | boolean = '';

            // Xác định giá trị mặc định dựa trên type hoặc component
            if (fieldType === 'number' || fieldComponent === 'number') {
              defaultValue = 0;
            } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
              defaultValue = false;
            } else {
              defaultValue = '';
            }

            // Thêm trường mới vào phân loại với thông tin đầy đủ
            return {
              ...variant,
              customFields: [
                ...variant.customFields,
                {
                  id: Date.now(), // ID tạm thời
                  fieldId,
                  label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
                  component: fieldComponent,
                  type: fieldType,
                  required: (fieldData?.['required'] as boolean) || false,
                  configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
                  value: { value: defaultValue }, // Giá trị đúng kiểu
                },
              ],
            };
          }
          return variant;
        })
      );
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi phân loại
  const handleRemoveCustomFieldFromVariant = useCallback(
    (variantId: number, customFieldId: number) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return {
              ...variant,
              customFields: variant.customFields.filter(field => field.id !== customFieldId),
            };
          }
          return variant;
        })
      );
    },
    []
  );

  // Cập nhật giá trị trường tùy chỉnh trong phân loại
  const handleUpdateCustomFieldInVariant = useCallback(
    (variantId: number, customFieldId: number, value: string | number | boolean) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return {
              ...variant,
              customFields: variant.customFields.map(field => {
                if (field.id === customFieldId) {
                  return {
                    ...field,
                    value: { value },
                  };
                }
                return field;
              }),
            };
          }
          return variant;
        })
      );
    },
    []
  );

  // Hiển thị loading khi đang fetch chi tiết sản phẩm
  if (isLoadingProduct) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="flex justify-center items-center py-8">
          <Typography variant="body1">
            {t('business:product.form.loadingProduct', 'Đang tải thông tin sản phẩm...')}
          </Typography>
        </div>
      </FormMultiWrapper>
    );
  }

  // Hiển thị lỗi nếu không tải được sản phẩm
  if (productError || !product) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-red-500">
            {t('business:product.form.loadError', 'Không thể tải thông tin sản phẩm')}
          </Typography>
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            {t('common:back', 'Quay lại')}
          </button>
        </div>
      </FormMultiWrapper>
    );
  }



  return (
    <FormMultiWrapper title={t('business:product.form.editPhysicalTitle', 'Chỉnh sửa sản phẩm vật lý')}>
      <Form
        ref={formRef}
        schema={productSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          // Log chi tiết từng field error
          Object.keys(errors).forEach(field => {
            if (errors[field]?.message) {
              console.error(`   Message: ${errors[field].message}`);
            }
            if (errors[field] && typeof errors[field] === 'object' && 'type' in errors[field]) {
              console.error(`   Type: ${(errors[field] as { type: string }).type}`);
            }
          });
          // Hiển thị error đầu tiên để user biết
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';

          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.descriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();

                          // Lấy tag mới
                          const newTag = e.currentTarget.value.trim();

                          // Thêm tag mới nếu chưa tồn tại
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags); // Đồng bộ với form
                          }

                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags); // Đồng bộ với form
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Giá sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '2. Giá sản phẩm')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {/* Hiển thị trực tiếp các trường giá cố định */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem name="listPrice" label={t('business:product.listPrice')} required>
                <Input fullWidth type="number" min="0" placeholder={t('business:product.enterListPrice')} />
              </FormItem>
              <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                <Input fullWidth type="number" min="0" placeholder={t('business:product.enterSalePrice')} />
              </FormItem>
              <FormItem name="currency" label={t('business:product.currency')} required>
                <Controller
                  name="currency"
                  render={({ field }) => (
                    <Select
                      fullWidth
                      value={field.value || 'VND'}
                      onChange={value => field.onChange(value)}
                      options={[
                        { value: 'VND', label: 'VND' },
                        { value: 'USD', label: 'USD' },
                        { value: 'EUR', label: 'EUR' },
                      ]}
                    />
                  )}
                />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 3. Hình ảnh sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.images', '3. Hình ảnh sản phẩm')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <FormItem name="media" label={t('business:product.form.media')}>
            <MultiFileUpload
              mediaOnly={true}
              accept="image/*,video/*"
              placeholder={t(
                'business:product.form.mediaPlaceholder',
                'Kéo thả hoặc click để tải lên ảnh/video'
              )}
              onChange={handleMediaFilesChange}
              value={mediaFiles}
            />
          </FormItem>
        </CollapsibleCard>

        {/* 4. Vận chuyển */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.shipping', '4. Vận chuyển')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="shipmentConfig.widthCm"
                label={t('business:product.form.shipmentConfig.widthCm', 'Chiều rộng (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.heightCm"
                label={t('business:product.form.shipmentConfig.heightCm', 'Chiều cao (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.lengthCm"
                label={t('business:product.form.shipmentConfig.lengthCm', 'Chiều dài (cm)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
              <FormItem
                name="shipmentConfig.weightGram"
                label={t('business:product.form.shipmentConfig.weightGram', 'Khối lượng (gram)')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 5. Quản lý tồn kho */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.inventory', '5. Quản lý tồn kho')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="inventory.warehouseId"
                label={t('business:product.form.inventory.warehouse', 'Kho')}
                required
              >
                <Controller
                  name="inventory.warehouseId"
                  render={({ field }) => (
                    <AsyncSelectWithPagination
                      fullWidth
                      value={field.value}
                      onChange={field.onChange}
                      placeholder={t(
                        'business:product.form.inventory.warehousePlaceholder',
                        'Chọn kho'
                      )}
                      loadOptions={async ({
                        search,
                        page,
                        limit,
                      }: {
                        search?: string;
                        page?: number;
                        limit?: number;
                      }) => {
                        // Gọi API thực tế để lấy danh sách kho
                        const params: { page: number; limit: number; search?: string } = {
                          page: page || 1,
                          limit: limit || 20,
                        };

                        // Chỉ thêm search nếu có giá trị
                        if (search) {
                          params.search = search;
                        }

                        const response = await WarehouseService.getWarehousesForSelect(params);

                        return response;
                      }}
                      searchOnEnter={true}
                    />
                  )}
                />
              </FormItem>

              <FormItem
                name="inventory.availableQuantity"
                label={t('business:product.form.inventory.availableQuantity', 'Số lượng có sẵn')}
              >
                <Input fullWidth type="number" min="0" placeholder="0" />
              </FormItem>

              <FormItem
                name="inventory.sku"
                label={t('business:product.form.inventory.sku', 'SKU')}
              >
                <Input fullWidth placeholder="SKU-001" />
              </FormItem>

              <FormItem
                name="inventory.barcode"
                label={t('business:product.form.inventory.barcode', 'Barcode')}
              >
                <Input fullWidth placeholder="1234567890123" />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 6. Mẫu mã */}
        <CollapsibleCard
          title={
            <div className="flex items-center justify-between w-full">
              <Typography variant="h6" className="font-medium">
                {productClassifications.length > 0
                  ? `${t('business:product.form.sections.variants', '6. Mẫu mã')} (${productClassifications.length})`
                  : t('business:product.form.sections.variants', '6. Mẫu mã')
                }
              </Typography>
              <div
                onClick={e => {
                  e.preventDefault();
                  e.stopPropagation(); // Ngăn không cho toggle card
                }}
              >
                <IconCard
                  icon="plus"
                  variant="primary"
                  size="sm"
                  title={t('business:product.form.variants.addVariant', 'Thêm biến thể')}
                  onClick={handleAddVariant}
                />
              </div>
            </div>
          }
          defaultOpen={false}
          className="mb-4"
        >
          {/* Danh sách phân loại (đổi tên từ biến thể) */}
          {productClassifications.length > 0 ? (
            <div className="space-y-4">
              {productClassifications.map((variant, index) => (
                <CollapsibleCard
                  key={variant.id}
                  title={
                    <div className="flex justify-between items-center w-full">
                      <div className="flex items-center space-x-4">
                        <Typography variant="body2" className="font-medium">
                          {variant.name || `${t('business:product.form.variants.variant', 'Phân loại')} ${index + 1}`}
                        </Typography>
                        {variant.listPrice && variant.salePrice && (
                          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                            {Number(variant.salePrice) > 0 ? `${Number(variant.salePrice).toLocaleString()} ${variant.currency}` : '0 VND'}
                          </Typography>
                        )}
                        {variant.availableQuantity && (
                          <Typography variant="body2" className="text-gray-500 dark:text-gray-500">
                            {variant.availableQuantity} sản phẩm
                          </Typography>
                        )}
                        {variant.id && (
                          <Typography variant="body2" className="text-gray-400 dark:text-gray-600 text-xs">
                            ID: {variant.id.toString().slice(-4)}
                          </Typography>
                        )}
                      </div>
                      <div
                        onClick={e => {
                          e.stopPropagation();
                          handleRemoveVariant(variant.id);
                        }}
                        className="cursor-pointer"
                      >
                        <IconCard
                          icon="trash"
                          variant="danger"
                          size="sm"
                          title={t('common:delete', 'Xóa')}
                        />
                      </div>
                    </div>
                  }
                  defaultOpen={true}
                >

                  {/* Thông tin cơ bản */}
                  <div className="mb-6">
                    <Typography
                      variant="subtitle2"
                      className="mb-3 font-medium text-gray-700 dark:text-gray-300"
                    >
                      {t('business:product.form.variants.basicInfo', 'Thông tin cơ bản')}
                    </Typography>
                    <div className="space-y-4">
                      {/* Tên và mô tả biến thể */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItem label={t('business:product.form.variants.name', 'Tên biến thể')}>
                          <Input
                            fullWidth
                            value={variant.name}
                            onChange={e => handleUpdateVariant(variant.id, 'name', e.target.value)}
                            placeholder={t(
                              'business:product.form.variants.namePlaceholder',
                              'Nhập tên biến thể'
                            )}
                          />
                        </FormItem>

                        <FormItem label={t('business:product.form.variants.priceDescription', 'Mô tả giá')}>
                          <Textarea
                            fullWidth
                            rows={3}
                            value={variant.priceDescription || ''}
                            onChange={e => handleUpdateVariant(variant.id, 'priceDescription', e.target.value)}
                            placeholder={t(
                              'business:product.form.variants.priceDescriptionPlaceholder',
                              'Nhập mô tả giá cho biến thể này'
                            )}
                          />
                        </FormItem>
                      </div>

                      {/* Các trường giá cố định */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormItem
                          label={t('business:product.form.variants.currency', 'Đơn vị tiền tệ')}
                        >
                          <Select
                            fullWidth
                            value={variant.currency}
                            onChange={val =>
                              handleUpdateVariant(variant.id, 'currency', val as string)
                            }
                            options={[
                              { value: 'VND', label: 'VND' },
                              { value: 'USD', label: 'USD' },
                              { value: 'EUR', label: 'EUR' },
                            ]}
                          />
                        </FormItem>

                        <FormItem
                          label={t('business:product.form.variants.listPrice', 'Giá niêm yết')}
                        >
                          <Input
                            fullWidth
                            type="number"
                            min="0"
                            value={variant.listPrice}
                            onChange={e =>
                              handleUpdateVariant(variant.id, 'listPrice', e.target.value)
                            }
                            placeholder="0"
                          />
                        </FormItem>

                        <FormItem label={t('business:product.form.variants.salePrice', 'Giá bán')}>
                          <Input
                            fullWidth
                            type="number"
                            min="0"
                            value={variant.salePrice}
                            onChange={e =>
                              handleUpdateVariant(variant.id, 'salePrice', e.target.value)
                            }
                            placeholder="0"
                          />
                        </FormItem>
                      </div>
                    </div>
                  </div>

                  {/* ✅ Quản lý tồn kho và ảnh cho biến thể */}
                  <div className="mb-6">
                    <Typography
                      variant="subtitle2"
                      className="mb-3 font-medium text-gray-700 dark:text-gray-300"
                    >
                      {t('business:product.form.variants.inventory', 'Quản lý tồn kho & Ảnh')}
                    </Typography>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormItem label={t('business:product.form.variants.sku', 'SKU biến thể')}>
                        <Input
                          fullWidth
                          value={variant.sku || ''}
                          onChange={e => handleUpdateVariant(variant.id, 'sku', e.target.value)}
                          placeholder={t(
                            'business:product.form.variants.skuPlaceholder',
                            'SKU-VARIANT-001'
                          )}
                        />
                      </FormItem>

                      <FormItem
                        label={t(
                          'business:product.form.variants.availableQuantity',
                          'Số lượng có sẵn'
                        )}
                        helpText={t(
                          'business:product.form.variants.availableQuantityHelper',
                          'Số lượng hiện có trong kho'
                        )}
                      >
                        <Input
                          fullWidth
                          type="number"
                          min="0"
                          value={variant.availableQuantity || ''}
                          onChange={e =>
                            handleUpdateVariant(variant.id, 'availableQuantity', e.target.value)
                          }
                          placeholder="0"
                        />
                      </FormItem>

                      <FormItem
                        label={t('business:product.form.variants.images', 'Ảnh biến thể')}
                        helpText={t(
                          'business:product.form.variants.imagesHelper',
                          'Tải lên nhiều ảnh cho biến thể này'
                        )}
                      >
                        <MultiFileUpload
                          value={variant.images || []}
                          onChange={(files: FileWithMetadata[]) => {
                            // Convert FileWithMetadata to ExtendedFileWithMetadata
                            const extendedFiles: ExtendedFileWithMetadata[] = files.map(file => ({
                              ...file,
                              url: file.preview || undefined, // Use preview as URL since FileWithMetadata doesn't have url
                              name: file.file.name || undefined,
                              size: file.file.size || undefined,
                              type: file.file.type || undefined,
                            }));
                            handleVariantImagesChange(variant.id, extendedFiles);
                          }}
                          accept="image/*"
                          mediaOnly={true}
                          placeholder={t(
                            'business:product.form.variants.imagesPlaceholder',
                            'Chọn ảnh cho biến thể'
                          )}
                          className="w-full"
                        />
                      </FormItem>
                    </div>
                  </div>

                  {/* Trường tùy chỉnh cho biến thể */}
                  <div className="mb-4">
                    <Typography
                      variant="subtitle2"
                      className="mb-3 font-medium text-gray-700 dark:text-gray-300"
                    >
                      {t('business:product.form.variants.customFields', 'Thuộc tính biến thể')}
                    </Typography>

                    <SimpleCustomFieldSelector
                      onFieldSelect={fieldData => {
                        handleToggleCustomFieldToVariant(
                          variant.id,
                          fieldData.id,
                          fieldData as unknown as Record<string, unknown>
                        );
                      }}
                      selectedFieldIds={variant.customFields.map(f => f.fieldId)}
                      placeholder={t(
                        'business:product.form.variants.searchCustomField',
                        'Nhập từ khóa và nhấn Enter để tìm thuộc tính...'
                      )}
                    />

                    {variant.customFields.length > 0 && (
                      <div className="space-y-3">
                        {variant.customFields.map(field => (
                          <CustomFieldRenderer
                            key={field.id}
                            field={field}
                            value={(field.value?.['value'] as string) || ''}
                            onChange={value =>
                              handleUpdateCustomFieldInVariant(
                                variant.id,
                                field.id,
                                value as string
                              )
                            }
                            onRemove={() =>
                              handleRemoveCustomFieldFromVariant(variant.id, field.id)
                            }
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </CollapsibleCard>
              ))}
            </div>
          ) : (
            <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
                {t(
                  'business:product.form.variants.noVariants',
                  'Chưa có biến thể nào. Nhấn "Thêm biến thể" để bắt đầu.'
                )}
              </Typography>
            </div>
          )}
        </CollapsibleCard>

        {/* 7. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '7. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(f => f.fieldId)}
              placeholder={t(
                'business:product.form.customFields.searchPlaceholder',
                'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
              )}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-3">
                {productCustomFields.map(field => (
                  <CustomFieldRenderer
                    key={field.id}
                    field={field}
                    value={(field.value?.['value'] as string) || ''}
                    onChange={value => handleUpdateCustomFieldInProduct(field.id, value as string)}
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        <div className="flex flex-row justify-end gap-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            disabled={updateProductMutation.isPending || isUploading}
          />
          <IconCard
            icon="check"
            variant="primary"
            size="md"
            title={isUploading ? t('business:product.uploading') : t('common:save')}
            onClick={() => {
              // Trigger form submit programmatically
              formRef.current?.submit();
            }}
            disabled={updateProductMutation.isPending || isUploading}
            isLoading={updateProductMutation.isPending || isUploading}
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default PhysicalProductEditForm;
