import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Icon, Table } from '@/shared/components/common';
import { CustomerDetailData, CustomerActivity } from './types';
import { TableColumn } from '@/shared/components/common/Table/types';

interface CustomerActivitiesProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị hoạt động của khách hàng
 */
const CustomerActivities: React.FC<CustomerActivitiesProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format date for timeline
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return t('customer.activity.justNow');
    } else if (diffInHours < 24) {
      return t('customer.activity.hoursAgo', { hours: diffInHours });
    } else if (diffInHours < 48) {
      return t('customer.activity.yesterday');
    } else {
      return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    }
  };

  // Get activity icon
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return 'shopping-cart';
      case 'login':
        return 'log-in';
      case 'profile_update':
        return 'user';
      case 'support':
        return 'help-circle';
      case 'review':
        return 'star';
      case 'payment':
        return 'credit-card';
      default:
        return 'activity';
    }
  };

  // Get activity color
  const getActivityColor = (type: string) => {
    switch (type) {
      case 'order':
        return 'text-green-600';
      case 'login':
        return 'text-blue-600';
      case 'profile_update':
        return 'text-purple-600';
      case 'support':
        return 'text-orange-600';
      case 'review':
        return 'text-yellow-600';
      case 'payment':
        return 'text-emerald-600';
      default:
        return 'text-gray-600';
    }
  };

  // Get activity background color
  const getActivityBgColor = (type: string) => {
    switch (type) {
      case 'order':
        return 'bg-green-50';
      case 'login':
        return 'bg-blue-50';
      case 'profile_update':
        return 'bg-purple-50';
      case 'support':
        return 'bg-orange-50';
      case 'review':
        return 'bg-yellow-50';
      case 'payment':
        return 'bg-emerald-50';
      default:
        return 'bg-gray-50';
    }
  };

  // Get activity type text
  const getActivityTypeText = (type: string) => {
    return t(`customer.activity.type.${type}`, type);
  };

  // Check if customer has activities
  const hasActivities = customer.activities && customer.activities.length > 0;

  // Sort activities by date (newest first)
  const sortedActivities = hasActivities && customer.activities
    ? [...customer.activities].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    : [];

  // Table columns for activities
  const columns: TableColumn<CustomerActivity>[] = [
    {
      key: 'type',
      title: t('customer.activity.type'),
      dataIndex: 'type',
      render: (value: unknown) => {
        const typeValue = String(value);
        return (
          <div className="flex items-center space-x-2">
            <div className={`p-2 rounded-full ${getActivityBgColor(typeValue)}`}>
              <Icon name={getActivityIcon(typeValue)} size="sm" className={getActivityColor(typeValue)} />
            </div>
            <Typography variant="body2" className="text-foreground capitalize">
              {getActivityTypeText(typeValue)}
            </Typography>
          </div>
        );
      },
    },
    {
      key: 'title',
      title: t('customer.activity.title'),
      dataIndex: 'title',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground font-medium">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'description',
      title: t('customer.activity.description'),
      dataIndex: 'description',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {value ? String(value) : '-'}
        </Typography>
      ),
    },
    {
      key: 'date',
      title: t('customer.activity.date'),
      dataIndex: 'date',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {formatDate(String(value))}
        </Typography>
      ),
    },
    {
      key: 'metadata',
      title: t('customer.activity.details'),
      dataIndex: 'metadata',
      render: (_, record: CustomerActivity) => {
        if (!record.metadata || Object.keys(record.metadata).length === 0) {
          return <Typography variant="body2" className="text-muted">-</Typography>;
        }

        return (
          <div className="space-y-1">
            {Object.entries(record.metadata).slice(0, 2).map(([key, val]) => (
              <div key={key} className="text-xs">
                <span className="text-muted">{key}:</span>{' '}
                <span className="text-foreground">{String(val)}</span>
              </div>
            ))}
            {Object.keys(record.metadata).length > 2 && (
              <Typography variant="caption" className="text-muted">
                +{Object.keys(record.metadata).length - 2} {t('customer.activity.moreDetails')}
              </Typography>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.activities')}
          </Typography>
          {hasActivities && (
            <Typography variant="body2" className="text-muted">
              {customer.activities?.length} {t('customer.detail.activitiesCount')}
            </Typography>
          )}
        </div>
      }
      defaultOpen={false}
    >
      {hasActivities ? (
        <div className="space-y-6">
          {/* Activity stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['order', 'login', 'support', 'review'].map((type) => {
              const count = sortedActivities.filter(activity => activity.type === type).length;
              return (
                <div key={type} className="text-center p-3 bg-muted/20 rounded-lg">
                  <div className={`inline-flex p-2 rounded-full ${getActivityBgColor(type)} mb-2`}>
                    <Icon name={getActivityIcon(type)} size="sm" className={getActivityColor(type)} />
                  </div>
                  <Typography variant="h6" className="text-foreground">
                    {count}
                  </Typography>
                  <Typography variant="caption" className="text-muted">
                    {getActivityTypeText(type)}
                  </Typography>
                </div>
              );
            })}
          </div>

          {/* Activities Table */}
          <div>
            <Typography variant="subtitle1" className="text-foreground mb-4">
              {t('customer.detail.allActivities')}
            </Typography>
            <Table
              data={sortedActivities}
              columns={columns}
              rowKey="id"
              pagination={sortedActivities.length > 10}
              size="sm"
              className="border border-border rounded-lg"
            />
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="mb-4">
            <Icon name="activity" size="lg" className="text-muted mx-auto" />
          </div>
          <Typography variant="body1" className="text-muted mb-2">
            {t('customer.detail.noActivities')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('customer.detail.noActivitiesDesc')}
          </Typography>
        </div>
      )}
    </CollapsibleCard>
  );
};

export default CustomerActivities;
