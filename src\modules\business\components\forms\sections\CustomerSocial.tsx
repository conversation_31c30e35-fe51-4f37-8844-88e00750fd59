import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, IconCard, Input, FormItem, Icon } from '@/shared/components/common';
import { CustomerDetailData } from './types';
import { useUpdateCustomerSocialLinks } from '../../../hooks/useCustomerQuery';
import { createUpdateCustomerSocialLinksSchema } from '../../../schemas/customer.schema';
import { NotificationUtil } from '@/shared/utils/notification';

interface CustomerSocialProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị thông tin mạng xã hội của khách hàng
 */
const CustomerSocial: React.FC<CustomerSocialProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Hook để cập nhật social links
  const updateSocialLinksMutation = useUpdateCustomerSocialLinks();

  const [formData, setFormData] = useState({
    facebook: customer.socialProfiles?.facebook || '',
    twitter: customer.socialProfiles?.twitter || '',
    linkedin: customer.socialProfiles?.linkedin || '',
    zalo: customer.socialProfiles?.zalo || '',
    website: customer.socialProfiles?.website || '',
  });

  // Derived state
  const isSaving = updateSocialLinksMutation.isPending;

  // Social platform configurations với Icon component thông thường
  const socialPlatforms = [
    {
      key: 'facebook',
      name: 'Facebook',
      icon: 'facebook',
      color: '#1877F2',
      bgColor: 'bg-blue-50',
      placeholder: 'https://facebook.com/username',
    },
    {
      key: 'twitter',
      name: 'Twitter',
      icon: 'message-square',
      color: '#1DA1F2',
      bgColor: 'bg-blue-50',
      placeholder: 'https://twitter.com/username',
    },
    {
      key: 'linkedin',
      name: 'LinkedIn',
      icon: 'users',
      color: '#0A66C2',
      bgColor: 'bg-blue-50',
      placeholder: 'https://linkedin.com/in/username',
    },
    {
      key: 'zalo',
      name: 'Zalo',
      icon: 'message-square',
      color: '#0088CC',
      bgColor: 'bg-blue-50',
      placeholder: 'Zalo ID hoặc số điện thoại',
    },
    {
      key: 'website',
      name: 'Website',
      icon: 'website',
      color: '#10B981',
      bgColor: 'bg-green-50',
      placeholder: 'https://website.com',
    },
  ];

  // Handle input change
  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle save
  const handleSave = async () => {
    try {
      // Validation schema
      const socialLinksSchema = createUpdateCustomerSocialLinksSchema(t);

      // Map form data to API format
      const socialLinksData = {
        facebookLink: formData.facebook || '',
        twitterLink: formData.twitter || '',
        linkedinLink: formData.linkedin || '',
        zaloLink: formData.zalo || '',
        websiteLink: formData.website || '',
      };

      // Validate data
      const validatedData = socialLinksSchema.parse(socialLinksData);

      // Remove empty values
      const cleanedData = Object.fromEntries(
        Object.entries(validatedData).filter(([, value]) => value !== '')
      );

      // Call API
      await updateSocialLinksMutation.mutateAsync({
        id: parseInt(customer.id),
        data: cleanedData,
      });

      console.log('Social links updated successfully');
    } catch (error: unknown) {
      console.error('Error updating social links:', error);

      // Handle validation errors
      if (error && typeof error === 'object' && 'errors' in error) {
        const validationError = error as { errors: Array<{ message: string }> };
        const errorMessages = validationError.errors.map((err) => err.message).join(', ');
        NotificationUtil.error({
          message: `Lỗi validation: ${errorMessages}`
        });
      }
    }
  };

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('customer.detail.social')}
        </Typography>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        <Typography variant="body2" className="text-muted mb-4">
          {t('customer.social.editDescription')}
        </Typography>

        <div className="grid grid-cols-1 gap-4">
          {socialPlatforms.map((platform) => (
            <FormItem
              key={platform.key}
              label={platform.name}
              className="mb-0"
            >
              <div className="flex items-center space-x-3">
                <div
                  className={`p-2 rounded-full ${platform.bgColor} flex items-center justify-center w-10 h-10`}
                  style={{ backgroundColor: platform.color }}
                >
                  <Icon name={platform.icon} size="sm" className="text-white" />
                </div>
                <div className="flex-1">
                  <Input
                    value={formData[platform.key as keyof typeof formData]}
                    onChange={(e) => handleInputChange(platform.key, e.target.value)}
                    placeholder={platform.placeholder}
                    className="w-full"
                  />
                </div>
              </div>
            </FormItem>
          ))}
        </div>

        {/* Save button moved to bottom */}
        <div className="flex justify-end pt-4 border-t border-border">
          <IconCard
            icon={isSaving ? "loader" : "save"}
            variant="primary"
            size="md"
            title={isSaving ? 'Đang lưu...' : t('common:save')}
            onClick={handleSave}
            disabled={isSaving}
            className={isSaving ? "animate-spin" : ""}
          />
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerSocial;
