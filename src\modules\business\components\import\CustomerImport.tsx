import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import { Typography, Card, Stepper, IconCard, Tooltip } from '@/shared/components/common';
import { ColumnMapping, ExcelData } from '../../types/customer-import.types';
import { useCustomerImport, CUSTOMER_QUERY_KEYS } from '../../hooks';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';
import { CustomerService, CreateBulkUserConvertCustomerDto } from '../../services/customer.service';
import ExcelUploadStep from './steps/ExcelUploadStep';
import ColumnMappingStep from './steps/ColumnMappingStep';
import ImportPreviewStep from './steps/ImportPreviewStep';

interface CustomerImportProps {
  onClose: () => void;
}

/**
 * Component cho import khách hàng từ Excel sử dụng SlideInForm
 */
const CustomerImport: React.FC<CustomerImportProps> = ({ onClose }) => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  // Use customer import hook
  const { importState, resetImportState, goToStep, previousStep, updateImportState } =
    useCustomerImport();

  // Use task queue context
  const { addCustomTask } = useTaskQueueContext();

  // State cho tab hiện tại (file upload hoặc URL)
  const [activeTab, setActiveTab] = useState<'file' | 'url'>('file');

  // State cho selected rows từ preview step
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Reset state khi đóng
  const handleClose = () => {
    resetImportState();
    setActiveTab('file');
    onClose();
  };

  // Xử lý khi upload Excel thành công
  const handleExcelUploaded = (excelData: ExcelData) => {
    console.log('handleExcelUploaded called with:', excelData);
    // Cập nhật state với dữ liệu Excel đã parse và chuyển sang mapping step
    updateImportState({
      step: 'mapping',
      excelData: excelData,
    });
  };

  // Xử lý khi mapping columns hoàn thành
  const handleMappingComplete = (mappings: ColumnMapping[]) => {
    console.log('handleMappingComplete called with:', mappings);
    // Update mappings và chuyển sang preview
    updateImportState({ mappings });
    goToStep('preview');
  };

  // Xử lý khi mapping được update
  const handleMappingUpdate = (mappings: ColumnMapping[]) => {
    console.log('handleMappingUpdate called with:', mappings);
    updateImportState({ mappings });
  };

  // Transform data theo mappings cho API
  const transformDataForAPI = (row: (string | number | boolean | null)[]) => {
    const activeMappings = importState.mappings.filter(m => m.customerField);
    const customerData: {
      name: string;
      phone: string;
      email?: string;
      platform?: string;
      timezone?: string;
      tags?: string[];
      metadata?: Array<{
        configId: string;
        value: string;
      }>;
    } = {
      name: '',
      phone: '',
    };

    const customFields: Array<{
      configId: string;
      value: string;
    }> = [];

    activeMappings.forEach(mapping => {
      const columnIndex = importState.excelData!.headers.indexOf(mapping.excelColumn);
      const value = row[columnIndex];

      switch (mapping.customerField) {
        case 'name':
          customerData.name = value ? String(value).trim() : '';
          break;
        case 'email':
          if (value && String(value).trim()) {
            customerData.email = String(value).trim();
          }
          break;
        case 'phone':
          customerData.phone = value ? String(value).trim() : '';
          break;
        case 'platform':
          if (value && String(value).trim()) {
            customerData.platform = String(value).trim();
          }
          break;
        case 'timezone':
          if (value && String(value).trim()) {
            customerData.timezone = String(value).trim();
          }
          break;
        case 'tags':
          if (value) {
            customerData.tags = String(value)
              .split(',')
              .map(tag => tag.trim())
              .filter(tag => tag);
          }
          break;
        default:
          // Custom fields - tất cả các trường khác đều là custom fields trong metadata
          if (value !== null && value !== undefined && value !== '') {
            customFields.push({
              configId: mapping.customerField,
              value: String(value),
            });
          }
          break;
      }
    });

    if (customFields.length > 0) {
      customerData.metadata = customFields;
    }

    return customerData;
  };

  // Xử lý khi preview hoàn thành và bắt đầu import
  const handleStartImport = () => {
    if (!importState.excelData || !importState.mappings.length) return;

    // Lọc chỉ những dòng được chọn
    const selectedRows = importState.excelData.rows.filter((_, index) => {
      const rowIndex = index + 2; // +2 vì bắt đầu từ dòng 2 (sau header)
      return selectedRowKeys.length === 0 || selectedRowKeys.includes(rowIndex);
    });

    // Transform dữ liệu đã chọn
    const customers = selectedRows.map(row => transformDataForAPI(row));

    // Validate dữ liệu cơ bản
    const validCustomers = customers.filter(customer => {
      return (
        customer.name &&
        customer.name.trim() !== '' &&
        customer.phone &&
        customer.phone.trim() !== ''
      );
    });

    if (validCustomers.length === 0) {
      // Hiển thị thông báo lỗi
      return;
    }

    // Chuẩn bị request bulk create theo format API
    const bulkRequest: CreateBulkUserConvertCustomerDto = {
      customers: validCustomers,
      skipDuplicates: true,
      continueOnError: true,
    };

    // Thêm task vào queue
    addCustomTask({
      title: t('business:customer.import.task.title', 'Import khách hàng'),
      description: `Import ${validCustomers.length} khách hàng từ Excel`,
      execute: async onProgress => {
        onProgress(10);

        // Gọi API endpoint: POST /v1/user/convert-customers/bulk
        const response = await CustomerService.createBulkConvertCustomers(bulkRequest);

        onProgress(90);

        if (response.result) {
          onProgress(100);
          return response.result;
        } else {
          throw new Error(response.message || 'Import failed');
        }
      },
      onSuccess: (result: unknown) => {
        // Xử lý kết quả thành công - TaskQueue sẽ hiển thị notification
        const bulkResult = result as { successCount: number };
        const importedCount = bulkResult.successCount || 0;
        console.log(`Import task completed: ${importedCount} customers imported`);

        // Invalidate và refetch danh sách khách hàng để cập nhật UI
        queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });

        // Không gọi onImportComplete vì form đã đóng và TaskQueue đã xử lý
      },
      onError: (error: Error) => {
        console.error('Import failed:', error);
        // Có thể hiển thị thông báo lỗi
      },
      retryable: true,
      maxRetries: 2,
    });

    // Đóng form import sau khi thêm task
    handleClose();
  };

  // Xử lý quay lại step trước
  const handleGoBack = () => {
    previousStep();
  };

  // Render step content
  const renderStepContent = () => {
    console.log('Current step:', importState.step);
    console.log('Import state:', {
      step: importState.step,
      hasExcelData: !!importState.excelData,
      mappingsLength: importState.mappings.length,
      mappings: importState.mappings,
    });
    switch (importState.step) {
      case 'upload':
        return (
          <ExcelUploadStep
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onExcelUploaded={handleExcelUploaded}
          />
        );
      case 'mapping':
        if (!importState.excelData) return null;
        return (
          <ColumnMappingStep
            excelData={importState.excelData}
            existingMappings={importState.mappings}
            onMappingComplete={handleMappingComplete}
            onMappingUpdate={handleMappingUpdate}
          />
        );
      case 'preview':
        if (!importState.excelData || !importState.mappings.length) return null;
        return (
          <ImportPreviewStep
            excelData={importState.excelData}
            mappings={importState.mappings}
            onSelectedRowsChange={setSelectedRowKeys}
          />
        );
      case 'importing':
        return (
          <div className="w-full space-y-6">
            <div className="p-6 bg-card rounded-lg text-center">
              <Typography variant="h6" className="mb-4">
                {t('business:customer.import.importing.title', 'Đang xử lý import')}
              </Typography>
              <Typography variant="body2" className="text-muted">
                {t(
                  'business:customer.import.importing.description',
                  'Tác vụ import đã được thêm vào hàng đợi. Bạn có thể theo dõi tiến trình trong panel Task Queue.'
                )}
              </Typography>
            </div>
          </div>
        );
      case 'complete':
        return (
          <div className="w-full space-y-6">
            <div className="p-6 bg-card rounded-lg text-center">
              <Typography variant="h6" className="mb-4 text-green-600">
                {t('business:customer.import.complete.title', 'Import hoàn thành')}
              </Typography>
              <Typography variant="body2" className="text-muted">
                {t(
                  'business:customer.import.complete.description',
                  'Quá trình import khách hàng đã hoàn thành thành công.'
                )}
              </Typography>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      {
        id: 'upload',
        title: t('customer.import.steps.upload'),
        icon: 'upload',
      },
      {
        id: 'mapping',
        title: t('customer.import.steps.mapping'),
        icon: 'link',
      },
      {
        id: 'preview',
        title: t('customer.import.steps.preview'),
        icon: 'search',
      },
      {
        id: 'importing',
        title: t('customer.import.steps.importing'),
        icon: 'database',
      },
    ];

    const currentStepIndex = steps.findIndex(step => step.id === importState.step);

    return (
      <Stepper
        steps={steps}
        currentStep={currentStepIndex}
        variant="filled"
        colorScheme="primary"
        showStepIcons={true}
        showConnector={true}
        orientation="horizontal"
        responsive={true}
        animated={true}
        className="w-full"
      />
    );
  };

  // Render action bar
  const renderActionBar = () => {
    switch (importState.step) {
      case 'upload':
        return (
          <div className="flex justify-end items-center pt-4 bg-card">
            <Tooltip content={t('common:close', 'Đóng')} position="top">
              <IconCard
                icon="x"
                variant="ghost"
                onClick={handleClose}
                title={t('common:close', 'Đóng')}
              />
            </Tooltip>
          </div>
        );

      case 'mapping':
        return (
          <div className="flex justify-between items-center pt-4 bg-card">
            <div className="flex items-center space-x-2">
              <Tooltip content={t('common:back', 'Quay lại')} position="top">
                <IconCard
                  icon="arrow-left"
                  variant="ghost"
                  onClick={handleGoBack}
                  title={t('common:back', 'Quay lại')}
                />
              </Tooltip>

              <Tooltip content={t('common:close', 'Đóng')} position="top">
                <IconCard
                  icon="x"
                  variant="ghost"
                  onClick={handleClose}
                  title={t('common:close', 'Đóng')}
                />
              </Tooltip>
            </div>

            <Tooltip content={t('common:continue', 'Tiếp tục')} position="top">
              <IconCard
                icon="chevron-right"
                variant="primary"
                size="lg"
                onClick={() => {
                  // Chuyển trực tiếp sang preview step vì mappings đã được update qua onMappingUpdate
                  goToStep('preview');
                }}
                disabled={!importState.mappings.length}
                title={t('common:continue', 'Tiếp tục')}
              />
            </Tooltip>
          </div>
        );

      case 'preview': {
        const selectedCount = selectedRowKeys.length;

        return (
          <div className="flex justify-between items-center pt-4 bg-card">
            <div className="flex items-center space-x-2">
              <Tooltip content={t('common:back', 'Quay lại')} position="top">
                <IconCard
                  icon="arrow-left"
                  variant="ghost"
                  onClick={handleGoBack}
                  title={t('common:back', 'Quay lại')}
                />
              </Tooltip>

              <Tooltip content={t('common:close', 'Đóng')} position="top">
                <IconCard
                  icon="x"
                  variant="ghost"
                  onClick={handleClose}
                  title={t('common:close', 'Đóng')}
                />
              </Tooltip>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-right">
                <Typography variant="body2" className="text-muted">
                  {t('business:customer.import.preview.readyToImport', 'Sẵn sàng import')}
                </Typography>
                <Typography variant="h6" className="text-green-600 font-semibold">
                  {selectedCount} {t('business:customer.import.preview.customers', 'khách hàng')}
                </Typography>
              </div>

              <Tooltip
                content={
                  selectedCount === 0
                    ? t(
                        'business:customer.import.preview.noValidData',
                        'Không có dữ liệu hợp lệ để import'
                      )
                    : t(
                        'business:customer.import.preview.startImportTooltip',
                        'Bắt đầu import khách hàng'
                      )
                }
                position="top"
              >
                <IconCard
                  icon="upload"
                  variant={selectedCount === 0 ? 'default' : 'primary'}
                  onClick={handleStartImport}
                  disabled={selectedCount === 0}
                  title={t('business:customer.import.preview.startImport', 'Bắt đầu import')}
                />
              </Tooltip>
            </div>
          </div>
        );
      }

      case 'importing':
        return (
          <div className="flex justify-center items-center pt-4 bg-card">
            <Tooltip content={t('common:close', 'Đóng')} position="top">
              <IconCard
                icon="x"
                variant="ghost"
                onClick={handleClose}
                title={t('common:close', 'Đóng')}
              />
            </Tooltip>
          </div>
        );

      case 'complete':
        return (
          <div className="flex justify-center items-center pt-4 bg-card">
            <div className="flex space-x-4">
              <Tooltip content={t('common:close', 'Đóng')} position="top">
                <IconCard
                  icon="x"
                  variant="ghost"
                  onClick={handleClose}
                  title={t('common:close', 'Đóng')}
                />
              </Tooltip>

              <Tooltip
                content={t('business:customer.import.complete.viewCustomers', 'Xem khách hàng')}
                position="top"
              >
                <IconCard
                  icon="users"
                  variant="primary"
                  onClick={() => window.location.reload()}
                  title={t('business:customer.import.complete.viewCustomers', 'Xem khách hàng')}
                />
              </Tooltip>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      {/* Header */}
      <div className="pb-4">
        <Typography variant="h4">{t('customer.import.title')}</Typography>
      </div>

      {/* Step Indicator */}
      <div>{renderStepIndicator()}</div>

      {/* Content */}
      <div className="overflow-y-auto">{renderStepContent()}</div>

      {/* Action Bar */}
      {renderActionBar()}
    </Card>
  );
};

export default CustomerImport;
