import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
} from '@/shared/components/common';
import { ColumnMapping } from '../../types/customer-import.types';
import { useCustomerImport } from '../../hooks';
import ExcelUploadStep from './steps/ExcelUploadStep';
import ColumnMappingStep from './steps/ColumnMappingStep';
import ImportPreviewStep from './steps/ImportPreviewStep';
import ImportProgressStep from './steps/ImportProgressStep';

interface CustomerImportModalProps {
  isVisible: boolean;
  onClose: () => void;
  onImportComplete: (importedCount: number) => void;
}

/**
 * Modal component cho import khách hàng từ Excel
 */
const CustomerImportModal: React.FC<CustomerImportModalProps> = ({
  isVisible,
  onClose,
  onImportComplete,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Use customer import hook
  const {
    importState,
    startImport,
    resetImportState,
    goToStep,
    previousStep,
    updateImportState,
  } = useCustomerImport();

  // State cho tab hiện tại (file upload hoặc URL)
  const [activeTab, setActiveTab] = useState<'file' | 'url'>('file');

  // Reset state khi đóng modal
  const handleClose = () => {
    resetImportState();
    setActiveTab('file');
    onClose();
  };

  // Xử lý khi upload Excel thành công
  const handleExcelUploaded = () => {
    // Hook sẽ tự động chuyển sang mapping step
    // Không cần manual state update
  };

  // Xử lý khi mapping columns hoàn thành
  const handleMappingComplete = (mappings: ColumnMapping[]) => {
    // Update mappings và chuyển sang preview
    updateImportState({ mappings });
    goToStep('preview');
  };

  // Xử lý khi mapping được update
  const handleMappingUpdate = (mappings: ColumnMapping[]) => {
    updateImportState({ mappings });
  };

  // Xử lý khi preview hoàn thành và bắt đầu import
  const handleStartImport = () => {
    startImport();
  };

  // Xử lý khi import hoàn thành
  const handleImportComplete = (importedCount: number) => {
    goToStep('complete');
    onImportComplete(importedCount);
  };

  // Xử lý quay lại step trước
  const handleGoBack = () => {
    previousStep();
  };

  // Render step content
  const renderStepContent = () => {
    switch (importState.step) {
      case 'upload':
        return (
          <ExcelUploadStep
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onExcelUploaded={handleExcelUploaded}
          />
        );
      case 'mapping':
        if (!importState.excelData) return null;
        return (
          <ColumnMappingStep
            excelData={importState.excelData}
            existingMappings={importState.mappings}
            onMappingComplete={handleMappingComplete}
            onMappingUpdate={handleMappingUpdate}
            onGoBack={handleGoBack}
          />
        );
      case 'preview':
        if (!importState.excelData || !importState.mappings.length) return null;
        return (
          <ImportPreviewStep
            excelData={importState.excelData}
            mappings={importState.mappings}
            onStartImport={handleStartImport}
            onGoBack={handleGoBack}
          />
        );
      case 'importing':
      case 'complete':
        if (!importState.excelData || !importState.mappings.length) return null;
        return (
          <ImportProgressStep
            excelData={importState.excelData}
            mappings={importState.mappings}
            isComplete={importState.step === 'complete'}
            onImportComplete={handleImportComplete}
            onClose={handleClose}
          />
        );
      default:
        return null;
    }
  };

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      { key: 'upload', label: t('customer.import.steps.upload') },
      { key: 'mapping', label: t('customer.import.steps.mapping') },
      { key: 'preview', label: t('customer.import.steps.preview') },
      { key: 'importing', label: t('customer.import.steps.importing') },
    ];

    const currentStepIndex = steps.findIndex(step => step.key === importState.step);

    return (
      <div className="flex items-center justify-center mb-6">
        {steps.map((step, index) => (
          <React.Fragment key={step.key}>
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                index <= currentStepIndex
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted text-muted-foreground'
              }`}
            >
              {index + 1}
            </div>
            <div className="ml-2 mr-4 text-sm">
              {step.label}
            </div>
            {index < steps.length - 1 && (
              <div
                className={`w-8 h-0.5 mr-4 ${
                  index < currentStepIndex ? 'bg-primary' : 'bg-muted'
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-background rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <Card className="w-full h-full">
          {/* Header */}
          <div className="mb-6">
            <Typography variant="h4">
              {t('customer.import.title')}
            </Typography>
          </div>

          {/* Step Indicator */}
          <div className="mb-6">
            {renderStepIndicator()}
          </div>

          {/* Content */}
          <div className="overflow-y-auto mb-6 max-h-[50vh]">
            {renderStepContent()}
          </div>

          {/* Footer with Close Button */}
          <div className="flex justify-end pt-4 border-t border-border">
            <Button
              variant="outline"
              onClick={handleClose}
            >
              {t('common.close')}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CustomerImportModal;
