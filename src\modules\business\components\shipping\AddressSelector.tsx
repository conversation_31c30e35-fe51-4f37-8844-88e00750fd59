import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  SearchableSelect,
  Input,
  Icon,
  Divider,
  Radio,
} from '@/shared/components/common';
import { DeliveryAddressDto } from '../../services/shipping-calculator.service';
import { useUserAddresses, useCreateUserAddress } from '../../hooks/useUserAddress';
import { useAddressOptions } from '../../hooks/useVietnamAddress';

interface AddressSelectorProps {
  selectedAddress?: DeliveryAddressDto;
  onAddressChange: (address: DeliveryAddressDto | undefined) => void;
  customerId?: number;
  className?: string;
}

/**
 * Component chọn địa chỉ giao hàng
 */
const AddressSelector: React.FC<AddressSelectorProps> = ({
  onAddressChange,
  customerId,
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // State
  const [addressMode, setAddressMode] = useState<'existing' | 'new'>('existing');
  const [selectedExistingAddressId, setSelectedExistingAddressId] = useState<number | null>(null);
  const [newAddress, setNewAddress] = useState({
    recipientName: '',
    recipientPhone: '',
    address: '',
    province: '',
    district: '',
    ward: '',
    postalCode: '',
    isDefault: false,
    addressType: 'home' as const,
    note: '',
  });

  // API hooks
  const { data: userAddressesData = [], isLoading: isLoadingAddresses } = useUserAddresses();
  const createAddressMutation = useCreateUserAddress();

  // Vietnam Address API hooks
  const addressOptions = useAddressOptions(newAddress.province, newAddress.district);

  // Đảm bảo userAddresses luôn là array và memoize để tránh re-render
  const userAddresses = useMemo(() =>
    Array.isArray(userAddressesData) ? userAddressesData : [],
    [userAddressesData]
  );

  // Ref để debounce việc cập nhật delivery address
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Xử lý chọn địa chỉ có sẵn
  const handleExistingAddressSelect = useCallback((addressId: number) => {
    const selectedAddress = userAddresses.find(addr => addr.id === addressId);
    if (selectedAddress) {
      setSelectedExistingAddressId(addressId);
      const deliveryAddress: DeliveryAddressDto = {
        addressId: Number(selectedAddress.id), // Đảm bảo addressId là number
      };
      onAddressChange(deliveryAddress);
    }
  }, [userAddresses, onAddressChange]);

  // Auto-select default address when addresses are loaded
  useEffect(() => {
    if (userAddresses.length > 0 && !selectedExistingAddressId) {
      const defaultAddress = userAddresses.find(addr => addr.isDefault) || userAddresses[0];
      if (defaultAddress) {
        setSelectedExistingAddressId(defaultAddress.id);
        handleExistingAddressSelect(defaultAddress.id);
      }
    }
  }, [userAddresses, selectedExistingAddressId, handleExistingAddressSelect]);

  // Cleanup timeout khi component unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Xử lý thay đổi mode
  const handleModeChange = useCallback((mode: 'existing' | 'new') => {
    // Clear timeout khi chuyển mode
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
      updateTimeoutRef.current = null;
    }

    setAddressMode(mode);
    if (mode === 'existing') {
      // Chọn địa chỉ có sẵn
      if (selectedExistingAddressId) {
        handleExistingAddressSelect(selectedExistingAddressId);
      } else {
        onAddressChange(undefined);
      }
    } else {
      // Reset form địa chỉ mới
      onAddressChange(undefined);
    }
  }, [selectedExistingAddressId, handleExistingAddressSelect, onAddressChange]);

  // Debounced function để cập nhật delivery address
  const updateDeliveryAddress = useCallback((addressData: typeof newAddress) => {
    // Clear timeout cũ nếu có
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    // Chỉ cập nhật nếu có đủ thông tin cần thiết
    if (addressData.recipientName && addressData.address && addressData.province) {
      // Debounce 300ms để tránh cập nhật quá thường xuyên
      updateTimeoutRef.current = setTimeout(() => {
        const deliveryAddress: DeliveryAddressDto = {
          newAddress: {
            recipientName: addressData.recipientName,
            recipientPhone: addressData.recipientPhone,
            address: addressData.address,
            province: addressData.province,
            district: addressData.district,
            ward: addressData.ward,
            postalCode: addressData.postalCode,
            isDefault: addressData.isDefault,
            addressType: addressData.addressType,
            note: addressData.note,
          }
        };
        onAddressChange(deliveryAddress);
      }, 300);
    }
  }, [onAddressChange]);

  // Xử lý thay đổi địa chỉ mới
  const handleNewAddressChange = useCallback((field: string, value: string | boolean | number) => {
    setNewAddress(prev => {
      const updated = { ...prev, [field]: value };

      // Reset dependent fields when parent changes
      if (field === 'province') {
        updated.district = '';
        updated.ward = '';
      } else if (field === 'district') {
        updated.ward = '';
      }

      // Chỉ cập nhật delivery address với debounce, không cập nhật ngay lập tức khi thay đổi note
      // Điều này tránh việc tạo ra nhiều địa chỉ trùng lặp khi người dùng đang gõ ghi chú
      updateDeliveryAddress(updated);

      return updated;
    });
  }, [updateDeliveryAddress]);

  // Xử lý lưu địa chỉ mới vào database
  const handleSaveNewAddress = useCallback(async () => {
    if (newAddress.recipientName && newAddress.address && newAddress.province) {
      try {
        // Clear timeout để tránh cập nhật delivery address trong lúc đang lưu
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
          updateTimeoutRef.current = null;
        }

        await createAddressMutation.mutateAsync({
          recipientName: newAddress.recipientName,
          recipientPhone: newAddress.recipientPhone,
          address: newAddress.address,
          province: newAddress.province,
          district: newAddress.district,
          ward: newAddress.ward,
          postalCode: newAddress.postalCode,
          isDefault: newAddress.isDefault,
          addressType: newAddress.addressType,
          note: newAddress.note,
        });

        // Reset form sau khi lưu thành công
        setNewAddress({
          recipientName: '',
          recipientPhone: '',
          address: '',
          province: '',
          district: '',
          ward: '',
          postalCode: '',
          isDefault: false,
          addressType: 'home',
          note: '',
        });

        // Clear delivery address để reset form
        onAddressChange(undefined);

        // Chuyển về mode existing
        setAddressMode('existing');
      } catch (error) {
        console.error('Error saving address:', error);
      }
    }
  }, [newAddress, createAddressMutation, onAddressChange]);

  // Render danh sách địa chỉ có sẵn
  const renderExistingAddresses = () => {
    if (isLoadingAddresses) {
      return (
        <div className="text-center py-12">
          <Icon name="loader" size="lg" className="mx-auto mb-4 text-primary animate-spin" />
          <Typography variant="body2" className="text-muted-foreground">
            {t('business:order.loadingAddresses', 'Đang tải danh sách địa chỉ...')}
          </Typography>
        </div>
      );
    }

    if (userAddresses.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
            <Icon name="map-pin" size="lg" className="text-muted-foreground" />
          </div>
          <Typography variant="h6" className="text-foreground mb-2">
            {t('business:order.noAddressesFound', 'Chưa có địa chỉ nào được lưu')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mb-6 max-w-sm mx-auto">
            {t('business:order.addFirstAddress', 'Hãy thêm địa chỉ đầu tiên của bạn để tiếp tục')}
          </Typography>
          <Button
            variant="primary"
            size="md"
            onClick={() => setAddressMode('new')}
            className="shadow-sm"
          >
            <Icon name="plus" size="sm" className="mr-2" />
            {t('business:order.addNewAddress')}
          </Button>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <Typography variant="body2" className="text-muted-foreground mb-4">
          {t('business:order.selectAddressInstruction', 'Chọn một địa chỉ giao hàng từ danh sách bên dưới')}
        </Typography>

        {userAddresses.map((address) => (
          <div
            key={address.id}
            className={`
              group relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-200
              ${selectedExistingAddressId === address.id
                ? 'border-primary bg-primary/5 shadow-sm'
                : 'border-border bg-card hover:border-primary/50 hover:bg-primary/2'
              }
            `}
            onClick={() => handleExistingAddressSelect(address.id)}
          >
            <div className="flex items-start gap-4">
              <div className="mt-1">
                <Radio
                  checked={selectedExistingAddressId === address.id}
                  onChange={() => handleExistingAddressSelect(address.id)}
                  className="text-primary"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <Icon name="map-pin" size="sm" className="text-muted-foreground mt-0.5 flex-shrink-0" />
                    <Typography variant="body2" className="text-foreground leading-relaxed">
                      {address.address}
                    </Typography>
                  </div>

                  <div className="flex items-center gap-2 ml-6">
                    <Icon name="map" size="xs" className="text-muted-foreground" />
                    <Typography variant="caption" className="text-muted-foreground">
                      {[address.ward, address.district, address.province].filter(Boolean).join(', ')}
                    </Typography>
                  </div>

                  {address.note && (
                    <div className="flex items-start gap-2 ml-6">
                      <Icon name="message-circle" size="xs" className="text-muted-foreground mt-0.5" />
                      <Typography variant="caption" className="text-muted-foreground italic">
                        {address.note}
                      </Typography>
                    </div>
                  )}
                </div>
              </div>

              {selectedExistingAddressId === address.id && (
                <div className="absolute top-3 right-3">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                    <Icon name="check" size="xs" className="text-primary-foreground" />
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render form địa chỉ mới
  const renderNewAddressForm = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-foreground mb-1">
            {t('business:order.recipientName')} <span className="text-red-500">*</span>
          </label>
          <Input
            value={newAddress.recipientName}
            onChange={(e) => handleNewAddressChange('recipientName', e.target.value)}
            placeholder={t('business:order.recipientNamePlaceholder')}
            fullWidth
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-1">
            {t('business:order.recipientPhone')} <span className="text-red-500">*</span>
          </label>
          <Input
            value={newAddress.recipientPhone}
            onChange={(e) => handleNewAddressChange('recipientPhone', e.target.value)}
            placeholder={t('business:order.recipientPhonePlaceholder')}
            fullWidth
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-foreground mb-1">
            {t('business:order.province')} <span className="text-red-500">*</span>
          </label>
          <SearchableSelect
            {...(newAddress.province && { value: newAddress.province })}
            onChange={(value) => handleNewAddressChange('province', String(value))}
            placeholder={addressOptions.provinces.isLoading ? 'Đang tải...' : t('business:order.selectProvince')}
            options={addressOptions.provinces.data}
            loading={addressOptions.provinces.isLoading}
            clearable
            fullWidth
          />
          {addressOptions.provinces.error && (
            <Typography variant="caption" className="text-destructive mt-1">
              Lỗi tải danh sách tỉnh/thành phố
            </Typography>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-1">
            {t('business:order.district')} <span className="text-red-500">*</span>
          </label>
          <SearchableSelect
            {...(newAddress.district && { value: newAddress.district })}
            onChange={(value) => handleNewAddressChange('district', String(value))}
            placeholder={
              !newAddress.province
                ? 'Chọn tỉnh/thành phố trước'
                : addressOptions.districts.isLoading
                  ? 'Đang tải...'
                  : t('business:order.selectDistrict')
            }
            options={addressOptions.districts.data}
            loading={addressOptions.districts.isLoading}
            disabled={!newAddress.province}
            clearable
            fullWidth
          />
          {addressOptions.districts.error && (
            <Typography variant="caption" className="text-destructive mt-1">
              Lỗi tải danh sách quận/huyện
            </Typography>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-1">
            {t('business:order.ward')} <span className="text-red-500">*</span>
          </label>
          <SearchableSelect
            {...(newAddress.ward && { value: newAddress.ward })}
            onChange={(value) => handleNewAddressChange('ward', String(value))}
            placeholder={
              !newAddress.district
                ? 'Chọn quận/huyện trước'
                : addressOptions.wards.isLoading
                  ? 'Đang tải...'
                  : t('business:order.selectWard')
            }
            options={addressOptions.wards.data}
            loading={addressOptions.wards.isLoading}
            disabled={!newAddress.district}
            clearable
            fullWidth
          />
          {addressOptions.wards.error && (
            <Typography variant="caption" className="text-destructive mt-1">
              Lỗi tải danh sách phường/xã
            </Typography>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-1">
          Địa chỉ chi tiết <span className="text-red-500">*</span>
        </label>
        <Input
          value={newAddress.address}
          onChange={(e) => handleNewAddressChange('address', e.target.value)}
          placeholder="Số nhà, ngách, đường, phố..."
          fullWidth
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-1">
          {t('business:customer.form.addressNote')}
        </label>
        <Input
          value={newAddress.note}
          onChange={(e) => handleNewAddressChange('note', e.target.value)}
          placeholder={t('business:customer.form.addressNotePlaceholder')}
          fullWidth
        />
      </div>
    </div>
  );

  return (
    <div className={className}>
      <div>

        {/* Mode Selection - Always side by side */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          <Button
            variant={addressMode === 'existing' ? 'primary' : 'outline'}
            size="md"
            onClick={() => handleModeChange('existing')}
            disabled={!customerId}
            className={`
              justify-start h-auto p-4 transition-all duration-200
              ${addressMode === 'existing'
                ? 'bg-primary text-primary-foreground border-primary shadow-sm'
                : 'bg-card text-foreground border-border hover:border-primary/50 hover:bg-primary/5'
              }
              ${!customerId ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <div className="flex items-center space-x-3">
              <Icon
                name="map-pin"
                size="md"
                className={addressMode === 'existing' ? 'text-primary-foreground' : 'text-primary'}
              />
              <div className="text-left">
                <div className="font-medium">
                  {t('business:order.useExistingAddress')}
                </div>
                <div className={`text-sm ${addressMode === 'existing' ? 'text-primary-foreground/80' : 'text-muted-foreground'}`}>
                  {t('business:order.selectFromSaved', 'Chọn từ địa chỉ đã lưu')}
                </div>
              </div>
            </div>
          </Button>

          <Button
            variant={addressMode === 'new' ? 'primary' : 'outline'}
            size="md"
            onClick={() => handleModeChange('new')}
            className={`
              justify-start h-auto p-4 transition-all duration-200
              ${addressMode === 'new'
                ? 'bg-primary text-primary-foreground border-primary shadow-sm'
                : 'bg-card text-foreground border-border hover:border-primary/50 hover:bg-primary/5'
              }
            `}
          >
            <div className="flex items-center space-x-3">
              <Icon
                name="plus"
                size="md"
                className={addressMode === 'new' ? 'text-primary-foreground' : 'text-primary'}
              />
              <div className="text-left">
                <div className="font-medium">
                  {t('business:order.addNewAddress')}
                </div>
                <div className={`text-sm ${addressMode === 'new' ? 'text-primary-foreground/80' : 'text-muted-foreground'}`}>
                  {t('business:order.createNewAddress', 'Tạo địa chỉ mới')}
                </div>
              </div>
            </div>
          </Button>
        </div>

        {!customerId && (
          <div className="mb-6 p-4 bg-warning/10 border border-warning/20 rounded-lg">
            <div className="flex items-start space-x-3">
              <Icon name="alert-triangle" size="sm" className="text-warning mt-0.5" />
              <div>
                <Typography variant="subtitle2" className="text-warning font-medium">
                  {t('business:order.customerRequired', 'Cần chọn khách hàng')}
                </Typography>
                <Typography variant="body2" className="text-warning/80 mt-1">
                  {t('business:order.customerRequiredDesc', 'Vui lòng chọn khách hàng trước để sử dụng địa chỉ có sẵn')}
                </Typography>
              </div>
            </div>
          </div>
        )}

        <Divider className="my-6 border-border" />

        {addressMode === 'existing' ? (
          <div className="space-y-4">
            {renderExistingAddresses()}
          </div>
        ) : (
          <div className="space-y-6">
            {renderNewAddressForm()}
            <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-border">
              <Button
                variant="primary"
                size="md"
                onClick={handleSaveNewAddress}
                disabled={!newAddress.recipientName || !newAddress.address || !newAddress.province || createAddressMutation.isPending}
                className="flex-1 sm:flex-none"
              >
                {createAddressMutation.isPending ? (
                  <>
                    <Icon name="loader" size="sm" className="mr-2 animate-spin" />
                    {t('business:order.saving', 'Đang lưu...')}
                  </>
                ) : (
                  <>
                    <Icon name="save" size="sm" className="mr-2" />
                    {t('business:order.saveAddress', 'Lưu địa chỉ')}
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="md"
                onClick={() => setAddressMode('existing')}
                className="flex-1 sm:flex-none"
              >
                <Icon name="x" size="sm" className="mr-2" />
                {t('business:order.cancel', 'Hủy')}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddressSelector;
