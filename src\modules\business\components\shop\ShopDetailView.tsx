import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Loading,
  Icon,
  Grid,
} from '@/shared/components/common';
import { useShopById } from '../../hooks/useShopQuery';
import { formatTimestamp } from '@/shared/utils/date';

interface ShopDetailViewProps {
  /**
   * ID của shop cần hiển thị
   */
  shopId: string;
}

/**
 * Component hiển thị chi tiết thông tin shop
 * Sử dụng hook useShopById để lấy dữ liệu từ API GET /v1/user/shop-info/shop/{id}
 */
const ShopDetailView: React.FC<ShopDetailViewProps> = ({ shopId }) => {
  const { t } = useTranslation('business');
  
  // Sử dụng hook mới để lấy thông tin shop theo ID
  const { data: shop, isLoading, error } = useShopById(shopId);

  // Debug logging
  console.log('🔍 [ShopDetailView] shopId:', shopId);
  console.log('🔍 [ShopDetailView] shop data:', shop);
  console.log('🔍 [ShopDetailView] isLoading:', isLoading);
  console.log('🔍 [ShopDetailView] error:', error);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Loading />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <Icon name="alert-circle" className="text-red-500 mx-auto mb-4" size="lg" />
          <Typography variant="h6" className="text-red-600 mb-2">
            {t('business:shop.messages.loadError')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {error instanceof Error ? error.message : 'Unknown error'}
          </Typography>
        </div>
      </Card>
    );
  }

  if (!shop) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <Icon name="marketplace" className="text-muted-foreground mx-auto mb-4" size="lg" />
          <Typography variant="h6" className="text-muted-foreground mb-2">
            {t('business:shop.empty.title')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            Không tìm thấy thông tin shop với ID: {shopId}
          </Typography>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <Icon name="marketplace" className="text-primary" size="lg" />
        <div>
          <Typography variant="h4" className="mb-1">
            {shop.shopName}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            ID: {shop.id}
          </Typography>
        </div>
      </div>

      {/* Shop Information */}
      <Grid columns={{ xs: 1, md: 2 }} columnGap="lg" rowGap="md">
        <div>
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('business:shop.form.shopName')}
          </Typography>
          <Typography variant="body1" className="font-medium">
            {shop.shopName}
          </Typography>
        </div>

        <div>
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('business:shop.form.shopPhone')}
          </Typography>
          <Typography variant="body1" className="font-medium">
            {shop.shopPhone}
          </Typography>
        </div>

        <div className="md:col-span-2">
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('business:shop.form.shopAddress')}
          </Typography>
          <Typography variant="body1" className="font-medium">
            {shop.shopAddress}
          </Typography>
        </div>

        <div>
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('business:shop.form.shopProvince')}
          </Typography>
          <Typography variant="body1" className="font-medium">
            {shop.shopProvince}
          </Typography>
        </div>

        <div>
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('business:shop.form.shopDistrict')}
          </Typography>
          <Typography variant="body1" className="font-medium">
            {shop.shopDistrict}
          </Typography>
        </div>

        <div>
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('business:shop.form.shopWard')}
          </Typography>
          <Typography variant="body1" className="font-medium">
            {shop.shopWard}
          </Typography>
        </div>

        <div>
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('common:createdAt')}
          </Typography>
          <Typography variant="body1" className="font-medium">
            {formatTimestamp(Number(shop.createdAt))}
          </Typography>
        </div>

        <div>
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('common:updatedAt')}
          </Typography>
          <Typography variant="body1" className="font-medium">
            {formatTimestamp(Number(shop.updatedAt))}
          </Typography>
        </div>
      </Grid>
    </Card>
  );
};

export default ShopDetailView;
