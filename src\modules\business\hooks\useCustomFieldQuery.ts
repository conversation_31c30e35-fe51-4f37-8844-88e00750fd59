import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CustomFieldService, CustomFieldQueryParams, CreateCustomFieldData, UpdateCustomFieldData } from '../services/custom-field.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';
import { useTranslation } from 'react-i18next';

/**
 * Query keys cho custom field API
 */
export const CUSTOM_FIELD_QUERY_KEYS = {
  all: ['business', 'custom-fields'] as const,
  list: (params: CustomFieldQueryParams) => [...CUSTOM_FIELD_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...CUSTOM_FIELD_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook lấy danh sách trường tùy chỉnh
 */
export const useCustomFields = (params: CustomFieldQueryParams = {}) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  return useQuery({
    queryKey: CUSTOM_FIELD_QUERY_KEYS.list(params),
    queryFn: () => CustomFieldService.getCustomFields(params),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết trường tùy chỉnh theo ID
 */
export const useCustomField = (id: number) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  return useQuery({
    queryKey: CUSTOM_FIELD_QUERY_KEYS.detail(id),
    queryFn: () => CustomFieldService.getCustomFieldById(id),
    select: (data) => data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook tạo trường tùy chỉnh mới
 */
export const useCreateCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (data: CreateCustomFieldData) => CustomFieldService.createCustomField(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:customField.createSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customField.createError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook cập nhật trường tùy chỉnh
 */
export const useUpdateCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomFieldData }) =>
      CustomFieldService.updateCustomField(id, data),
    onSuccess: (_, variables) => {
      NotificationUtil.success({
        message: t('business:customField.updateSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.detail(variables.id),
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customField.updateError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook xóa trường tùy chỉnh
 */
export const useDeleteCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (id: number) => CustomFieldService.deleteCustomField(id),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:customField.deleteSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customField.deleteError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook xóa nhiều trường tùy chỉnh
 */
export const useDeleteMultipleCustomFields = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (customFieldIds: number[]) => CustomFieldService.deleteMultipleCustomFields(customFieldIds),
    onSuccess: (_, customFieldIds) => {
      NotificationUtil.success({
        message: t('business:customField.bulkDeleteSuccess', { count: customFieldIds.length }),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customField.bulkDeleteError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook tìm custom field theo configId
 */
export const useCustomFieldByConfigId = (configId: string) => {
  const { t } = useTranslation(['business', 'common']);

  return useQuery({
    queryKey: [...CUSTOM_FIELD_QUERY_KEYS.all, 'by-config-id', configId],
    queryFn: async () => {
      // Tìm kiếm custom field bằng configId
      const searchResponse = await CustomFieldService.getCustomFields({
        search: configId,
        limit: 50 // Tăng limit để đảm bảo tìm được
      });

      // Tìm field có configId chính xác
      const exactMatch = searchResponse.result.items.find(item =>
        (item as any).configId === configId
      );

      if (!exactMatch) {
        throw new Error(`Custom field with configId "${configId}" not found`);
      }

      // Lấy chi tiết field để có đầy đủ configJson
      const detailResponse = await CustomFieldService.getCustomFieldById(exactMatch.id);
      return detailResponse.result;
    },
    enabled: !!configId,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Chỉ retry 1 lần nếu không tìm thấy
  });
};
