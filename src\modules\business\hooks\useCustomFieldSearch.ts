import { useState, useCallback, useRef } from 'react';
import { CustomFieldService } from '../services/custom-field.service';

export interface CustomFieldSearchItem {
  id: number;
  label: string;
  component: string;
  configId?: string | undefined;
  type: string;
  required: boolean;
  configJson?: any; // Thêm configJson để chứa options cho select
}

interface UseCustomFieldSearchOptions {
  pageSize?: number;
}

export const useCustomFieldSearch = (options: UseCustomFieldSearchOptions = {}) => {
  const { pageSize = 20 } = options;

  const [items, setItems] = useState<CustomFieldSearchItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Cache để tránh gọi API trùng lặp
  const cacheRef = useRef<Map<string, { items: CustomFieldSearchItem[]; hasMore: boolean }>>(new Map());
  const abortControllerRef = useRef<AbortController>();

  // Tạo cache key
  const getCacheKey = useCallback((search: string, page: number) => {
    return `${search}:${page}`;
  }, []);

  // Load items với cache
  const loadItems = useCallback(async (search: string, page: number, reset = false) => {
    console.log('🔍 loadItems called with:', { search, page, reset });

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const cacheKey = getCacheKey(search, page);
    console.log('🔍 Cache key:', cacheKey);

    // Check cache first
    if (cacheRef.current.has(cacheKey)) {
      console.log('🔍 Using cached data for:', cacheKey);
      const cached = cacheRef.current.get(cacheKey)!;
      if (reset) {
        setItems(cached.items);
      } else {
        setItems(prev => [...prev, ...cached.items]);
      }
      setHasMore(cached.hasMore);
      return;
    }

    console.log('🔍 No cache found, making API call');
    try {
      setLoading(true);

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      const response = await CustomFieldService.getCustomFields({
        search,
        page,
        limit: pageSize,
      });

      console.log('🔍 API Response:', response);
      console.log('🔍 Raw API items:', response.result.items);

      // Kiểm tra từng item một cách chi tiết
      response.result.items.forEach((item, index) => {
        const itemWithConfigJson = item as any; // Type assertion để access configJson
        console.log(`🔍 Item ${index}:`, {
          id: item.id,
          label: item.label,
          type: item.type,
          hasConfigJson: 'configJson' in itemWithConfigJson,
          configJson: itemWithConfigJson.configJson,
          configJsonType: typeof itemWithConfigJson.configJson,
          configJsonKeys: itemWithConfigJson.configJson ? Object.keys(itemWithConfigJson.configJson) : 'N/A',
          fullItem: itemWithConfigJson
        });
      });

      // Check if request was aborted
      if (abortControllerRef.current.signal.aborted) {
        console.log('🔍 Request was aborted');
        return;
      }

      const newItems: CustomFieldSearchItem[] = response.result.items.map(item => {
        const itemWithConfigJson = item as any; // Type assertion để access configJson
        return {
          id: item.id,
          label: item.label,
          component: item.component,
          configId: item.configId,
          type: item.type,
          required: item.required,
          configJson: itemWithConfigJson.configJson, // Lấy configJson từ API response
        };
      });

      console.log('🔍 Mapped items:', newItems);

      // Cache the result
      cacheRef.current.set(cacheKey, {
        items: newItems,
        hasMore: newItems.length === pageSize,
      });

      if (reset) {
        console.log('🔍 Setting new items (reset):', newItems);
        setItems(newItems);
        setCurrentPage(page + 1);
      } else {
        console.log('🔍 Appending items:', newItems);
        setItems(prev => {
          const updated = [...prev, ...newItems];
          console.log('🔍 Updated items:', updated);
          return updated;
        });
        setCurrentPage(prev => prev + 1);
      }

      setHasMore(newItems.length === pageSize);
      console.log('🔍 Has more:', newItems.length === pageSize);
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Error loading custom fields:', error);
        setHasMore(false);
      }
    } finally {
      setLoading(false);
    }
  }, [pageSize, getCacheKey]);

  // Search ngay lập tức - không debounce
  const search = useCallback((term: string) => {
    console.log('🔍 Search called with term:', term);
    setSearchTerm(term);

    // Cancel previous request nếu có
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Gọi API ngay lập tức
    setCurrentPage(1);
    loadItems(term, 1, true);
  }, [loadItems]);

  // Load more items
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      loadItems(searchTerm, currentPage, false);
    }
  }, [loading, hasMore, searchTerm, currentPage, loadItems]);

  // Initial load
  const initialLoad = useCallback(() => {
    if (items.length === 0 && !loading) {
      loadItems('', 1, true);
    }
  }, [items.length, loading, loadItems]);

  // Clear cache
  const clearCache = useCallback(() => {
    cacheRef.current.clear();
  }, []);

  return {
    items,
    loading,
    hasMore,
    searchTerm,
    search,
    loadMore,
    initialLoad,
    clearCache,
  };
};
