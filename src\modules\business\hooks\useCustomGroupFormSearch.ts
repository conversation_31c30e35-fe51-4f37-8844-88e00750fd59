import { useState, useCallback, useRef } from 'react';
import { CustomGroupFormService } from '../services/custom-group-form.service';

export interface CustomGroupFormSearchItem {
  id: number;
  label: string;
  productId: number | null;
  createAt: number;
  fieldCount?: number;
}

/**
 * Hook để search nhóm trường tùy chỉnh với lazy loading
 */
export const useCustomGroupFormSearch = () => {
  const [items, setItems] = useState<CustomGroupFormSearchItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Ref để track current search params
  const currentSearchRef = useRef<string>('');
  const currentPageRef = useRef<number>(1);

  /**
   * Search nhóm trường tùy chỉnh
   */
  const searchCustomGroupForms = useCallback(async (searchTerm: string, loadMore: boolean = false) => {
    try {
      // Nếu là search mới, reset state
      if (!loadMore || currentSearchRef.current !== searchTerm) {
        currentSearchRef.current = searchTerm;
        currentPageRef.current = 1;
        setItems([]);
        setHasMore(true);
        setError(null);
      }

      setIsLoading(true);

      const response = await CustomGroupFormService.getCustomGroupForms({
        search: searchTerm || undefined,
        page: currentPageRef.current,
        limit: 20,
        sortBy: 'createAt',
        sortDirection: 'DESC',
      });

      if (response.result) {
        const newItems: CustomGroupFormSearchItem[] = response.result.items.map(item => ({
          id: item.id,
          label: item.label,
          productId: item.productId,
          createAt: typeof item.createAt === 'number' && item.createAt > 0 ? item.createAt : Date.now(),
          fieldCount: item.fieldCount,
        }));

        if (loadMore && currentSearchRef.current === searchTerm) {
          // Append to existing items
          setItems(prev => [...prev, ...newItems]);
        } else {
          // Replace items
          setItems(newItems);
        }

        // Update pagination
        const { currentPage, totalPages } = response.result.meta;
        currentPageRef.current = currentPage + 1;
        setHasMore(currentPage < totalPages);
      } else {
        setError('Có lỗi xảy ra khi tìm kiếm');
        setHasMore(false);
      }
    } catch (err) {
      console.error('Error searching custom group forms:', err);
      setError('Có lỗi xảy ra khi tìm kiếm');
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Load more items
   */
  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      searchCustomGroupForms(currentSearchRef.current, true);
    }
  }, [isLoading, hasMore, searchCustomGroupForms]);

  /**
   * Clear search results
   */
  const clearSearch = useCallback(() => {
    setItems([]);
    setError(null);
    setHasMore(true);
    currentSearchRef.current = '';
    currentPageRef.current = 1;
  }, []);

  return {
    items,
    isLoading,
    hasMore,
    error,
    searchCustomGroupForms,
    loadMore,
    clearSearch,
  };
};
