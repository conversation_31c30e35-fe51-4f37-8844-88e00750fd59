import { useCallback } from 'react';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { useQueryClient } from '@tanstack/react-query';
import { UserFileService, CreateFileResponseDto } from '../services/user-file.service';
import { USER_FILE_KEYS } from './user-file.hooks';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Tham số cho upload file
 */
export interface FileUploadParams {
  /**
   * File cần upload
   */
  file: File;

  /**
   * ID thư mục cha
   */
  folderId?: number;

  /**
   * ID kho ảo
   */
  warehouseId?: number;

  /**
   * Callback khi upload thành công
   */
  onSuccess?: (fileInfo: CreateFileResponseDto) => void;

  /**
   * Callback khi upload thất bại
   */
  onError?: (error: Error) => void;

  /**
   * Callback theo dõi tiến trình upload
   */
  onProgress?: (progress: number) => void;
}

/**
 * Hook để upload file với TaskQueue pattern
 * 1. Tạo file record trong database
 * 2. Lấy presigned URL
 * 3. Upload file lên cloud với TaskQueue
 */
export const useFileUploadWithQueue = () => {
  // Query client để cập nhật cache
  const queryClient = useQueryClient();

  // Hook để upload file với TaskQueue và xử lý lỗi CORS
  const fileUploadWithQueue = useCorsAwareFileUpload({
    defaultTaskTitle: 'Upload file',
    autoAddToQueue: true,
  });

  /**
   * Upload single file
   */
  const uploadFile = useCallback(
    async ({
      file,
      folderId,
      warehouseId,
      onSuccess,
      onError,
      onProgress,
    }: FileUploadParams): Promise<void> => {
      try {
        // Bước 1: Tạo file record và lấy presigned URL
        const createFileResponse = await UserFileService.createFileForUpload(
          file,
          folderId,
          warehouseId
        );

        // Bước 2: Upload file lên cloud với TaskQueue
        await fileUploadWithQueue.uploadToUrlWithQueue({
          file,
          presignedUrl: createFileResponse,
          taskTitle: `Upload: ${file.name}`,
          taskDescription: `Kích thước: ${(file.size / 1024).toFixed(1)} KB`,
          onUploadProgress: onProgress,
        });

        // Bước 3: Invalidate cache và gọi callback success
        queryClient.invalidateQueries({
          queryKey: USER_FILE_KEYS.lists(),
        });

        if (folderId) {
          queryClient.invalidateQueries({
            queryKey: USER_FILE_KEYS.folder(folderId),
          });
        }

        if (onSuccess) {
          onSuccess(createFileResponse);
        }

        NotificationUtil.success({
          message: `Upload file "${file.name}" thành công`,
        });

      } catch (error) {
        console.error('Upload file error:', error);

        if (onError) {
          onError(error as Error);
        }

        NotificationUtil.error({
          message: `Có lỗi xảy ra khi upload file "${file.name}"`,
        });
      }
    },
    [fileUploadWithQueue, queryClient]
  );

  /**
   * Upload multiple files
   */
  const uploadFiles = useCallback(
    async (
      files: File[],
      folderId?: number,
      warehouseId?: number,
      onProgress?: (fileIndex: number, progress: number) => void
    ): Promise<void> => {
      try {
        // Upload từng file một cách tuần tự
        for (let i = 0; i < files.length; i++) {
          const file = files[i];

          await uploadFile({
            file,
            folderId,
            warehouseId,
            onProgress: (progress) => {
              if (onProgress) {
                onProgress(i, progress);
              }
            },
          });
        }

        NotificationUtil.success({
          message: `Upload ${files.length} file(s) thành công`,
        });

      } catch (error) {
        console.error('Upload multiple files error:', error);

        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi upload files',
        });
      }
    },
    [uploadFile]
  );

  return {
    uploadFile,
    uploadFiles,
  };
};
