import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CustomerImportService } from '../services';
import { CUSTOMER_IMPORT_QUERY_KEYS } from '../constants/customer-import.constants';

/**
 * Interface cho import jobs query parameters
 */
export interface ImportJobsQueryParams extends Record<string, unknown> {
  page?: number;
  limit?: number;
  status?: string;
}

/**
 * Hook để quản lý import jobs
 */
export const useImportJobs = (params?: ImportJobsQueryParams) => {
  const queryClient = useQueryClient();

  // Query để lấy danh sách import jobs
  const {
    data: jobsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: CUSTOMER_IMPORT_QUERY_KEYS.JOBS(params),
    queryFn: () => CustomerImportService.getImportJobs(params),
    select: (response) => response.result,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: (data) => {
      // Auto-refetch if there are active jobs
      const hasActiveJobs = (data as { items?: { status: string }[] })?.items?.some(
        (job: { status: string }) => job.status === 'pending' || job.status === 'processing'
      );
      return hasActiveJobs ? 5000 : false; // 5 seconds for active jobs
    },
  });

  // Mutation để cancel import job
  const cancelJobMutation = useMutation({
    mutationFn: (jobId: string) => CustomerImportService.cancelImportJob(jobId),
    onSuccess: () => {
      // Invalidate jobs query to refresh list
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.JOBS() });
    },
  });

  const cancelJob = (jobId: string) => {
    cancelJobMutation.mutate(jobId);
  };

  return {
    // Data
    jobs: jobsData?.items || [],
    total: jobsData?.total || 0,
    page: jobsData?.page || 1,
    limit: jobsData?.limit || 10,
    
    // States
    isLoading,
    error,
    isCancelling: cancelJobMutation.isPending,
    cancelError: cancelJobMutation.error,
    
    // Actions
    refetch,
    cancelJob,
  };
};

/**
 * Hook để theo dõi single import job
 */
export const useImportJob = (jobId: string | null) => {
  const queryClient = useQueryClient();

  const {
    data: jobData,
    isLoading,
    error,
  } = useQuery({
    queryKey: CUSTOMER_IMPORT_QUERY_KEYS.JOB(jobId || ''),
    queryFn: () => CustomerImportService.getImportJobStatus(jobId!),
    select: (response) => response.result,
    enabled: !!jobId,
    refetchInterval: (data) => {
      // Auto-refetch if job is still active
      const isActive = (data as { status?: string })?.status === 'pending' || (data as { status?: string })?.status === 'processing';
      return isActive ? 2000 : false; // 2 seconds for active job
    },
    staleTime: 1000, // 1 second
  });

  // Mutation để cancel job
  const cancelJobMutation = useMutation({
    mutationFn: () => CustomerImportService.cancelImportJob(jobId!),
    onSuccess: () => {
      // Invalidate both single job and jobs list
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.JOB(jobId!) });
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.JOBS() });
    },
  });

  const cancelJob = () => {
    if (jobId) {
      cancelJobMutation.mutate();
    }
  };

  return {
    // Data
    job: jobData,
    
    // States
    isLoading,
    error,
    isCancelling: cancelJobMutation.isPending,
    cancelError: cancelJobMutation.error,
    
    // Actions
    cancelJob,
  };
};

/**
 * Hook để lấy import job statistics
 */
export const useImportJobStats = () => {
  const { jobs, isLoading } = useImportJobs();

  const stats = {
    total: jobs.length,
    pending: jobs.filter((job: { status: string }) => job.status === 'pending').length,
    processing: jobs.filter((job: { status: string }) => job.status === 'processing').length,
    completed: jobs.filter((job: { status: string }) => job.status === 'completed').length,
    failed: jobs.filter((job: { status: string }) => job.status === 'failed').length,
    cancelled: jobs.filter((job: { status: string }) => job.status === 'cancelled').length,
  };

  const successRate = stats.total > 0 
    ? Math.round((stats.completed / (stats.completed + stats.failed)) * 100) || 0
    : 0;

  const activeJobs = stats.pending + stats.processing;

  return {
    stats,
    successRate,
    activeJobs,
    isLoading,
  };
};

export default useImportJobs;
