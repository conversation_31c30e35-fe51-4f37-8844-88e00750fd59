import { useCallback } from 'react';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { PRODUCT_QUERY_KEYS } from './useProductQuery';
import { useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';

/**
 * Interface cho dữ liệu upload ảnh sản phẩm
 */
export interface ProductImageUploadData {
  file: File;
  uploadUrl: string;
  fileName: string;
}

/**
 * Hook để upload ảnh sản phẩm với TaskQueue theo pattern của MediaPage
 */
export const useProductImageUpload = () => {
  // Query client để cập nhật cache
  const queryClient = useQueryClient();

  // Hook để upload file với TaskQueue và xử lý lỗi CORS
  const fileUploadWithQueue = useCorsAwareFileUpload({
    defaultTaskTitle: 'Upload product images',
    autoAddToQueue: true,
  });

  /**
   * Upload ảnh sản phẩm với TaskQueue theo pattern MediaPage
   */
  const uploadProductImages = useCallback(
    async (mediaFiles: FileWithMetadata[], uploadUrls: string[], options?: { skipCacheInvalidation?: boolean }) => {
      try {
        if (!mediaFiles || !Array.isArray(mediaFiles) || mediaFiles.length === 0) {
          console.log('No media files to upload');
          return;
        }

        if (!uploadUrls || !Array.isArray(uploadUrls) || uploadUrls.length === 0) {
          console.log('No upload URLs provided');
          return;
        }

        if (mediaFiles.length !== uploadUrls.length) {
          throw new Error(`Số lượng file (${mediaFiles.length}) không khớp với số lượng URL (${uploadUrls.length})`);
        }

        // Chuẩn bị dữ liệu upload theo pattern MediaPage
        const uploadPromises = mediaFiles.map((fileData, index) => {
          const uploadUrl = uploadUrls[index];
          const { file } = fileData;

          if (!file || !(file instanceof File)) {
            throw new Error(`Không tìm thấy file hợp lệ cho ảnh ${index + 1}`);
          }

          if (!uploadUrl) {
            throw new Error(`Không tìm thấy upload URL cho ảnh ${index + 1}`);
          }

          console.log(`📤 Adding upload task for: ${file.name}`);

          // Upload file lên presigned URL với TaskQueue theo pattern MediaPage
          return fileUploadWithQueue.uploadToUrlWithQueue({
            file,
            presignedUrl: uploadUrl,
            taskTitle: `Upload: ${file.name}`,
            taskDescription: `Kích thước: ${(file.size / 1024).toFixed(1)} KB`,
            onUploadProgress: (progress) => {
              console.log(`📊 Upload progress for ${file.name}: ${progress}%`);
            },
          });
        });

        // Chờ tất cả các file upload xong theo pattern MediaPage
        await Promise.all(uploadPromises);

        // Cập nhật lại danh sách sản phẩm sau khi upload xong (tương tự MediaPage)
        // Chỉ invalidate cache nếu không skip
        if (!options?.skipCacheInvalidation) {
          queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

          // Hiển thị thông báo thành công theo pattern MediaPage
          NotificationUtil.success({
            message: 'Tải lên ảnh sản phẩm thành công',
            duration: 3000,
          });
        }

        return uploadUrls;
      } catch (error) {
        // Hiển thị thông báo lỗi theo pattern MediaPage
        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi tải lên ảnh sản phẩm. Vui lòng thử lại sau.',
          duration: 5000,
        });

        throw error;
      }
    },
    [fileUploadWithQueue, queryClient]
  );

  /**
   * Upload single image với TaskQueue
   */
  const uploadSingleImage = useCallback(
    async (file: File, uploadUrl: string) => {
      try {
        await fileUploadWithQueue.uploadToUrlWithQueue({
          file,
          presignedUrl: uploadUrl,
          taskTitle: `Upload: ${file.name}`,
          taskDescription: `Kích thước: ${(file.size / 1024).toFixed(1)} KB`,
        });
        // Cập nhật cache
        queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

        return uploadUrl;
      } catch (error) {
        console.error(`❌ Lỗi khi upload ảnh ${file.name}:`, error);
        throw error;
      }
    },
    [fileUploadWithQueue, queryClient]
  );

  return {
    uploadProductImages,
    uploadSingleImage,
    isUploading: false, // TaskQueue đã xử lý trạng thái upload
  };
};

export default useProductImageUpload;
