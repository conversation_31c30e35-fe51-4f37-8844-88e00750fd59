import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { VirtualWarehouseService } from '../services/virtual-warehouse.service';
import {
  VirtualWarehouseQueryParams,
  CreateVirtualWarehouseDto,
  UpdateVirtualWarehouseDto,
} from '../types/virtual-warehouse.types';
import { NotificationUtil } from '@/shared/utils/notification';
import { useTranslation } from 'react-i18next';

// Query keys
export const VIRTUAL_WAREHOUSE_QUERY_KEYS = {
  all: ['virtual-warehouses'] as const,
  lists: () => [...VIRTUAL_WAREHOUSE_QUERY_KEYS.all, 'list'] as const,
  list: (params: VirtualWarehouseQueryParams) =>
    [...VIRTUAL_WAREHOUSE_QUERY_KEYS.lists(), params] as const,
  details: () => [...VIRTUAL_WAREHOUSE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...VIRTUAL_WAREHOUSE_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách kho ảo
 */
export function useVirtualWarehouses(params: VirtualWarehouseQueryParams) {
  return useQuery({
    queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.list(params),
    queryFn: () => VirtualWarehouseService.getVirtualWarehouses(params),
    staleTime: 5 * 60 * 1000, // 5 phút
  });
}

/**
 * Hook để lấy chi tiết kho ảo
 */
export function useVirtualWarehouse(id: number) {
  return useQuery({
    queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.detail(id),
    queryFn: () => VirtualWarehouseService.getVirtualWarehouseById(id),
    enabled: !!id,
  });
}

/**
 * Hook để tạo mới kho ảo
 */
export function useCreateVirtualWarehouse() {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ warehouseId, data }: { warehouseId: number; data: CreateVirtualWarehouseDto }) =>
      VirtualWarehouseService.createVirtualWarehouse(warehouseId, data),
    onSuccess: () => {
      // Invalidate và refetch danh sách kho ảo
      queryClient.invalidateQueries({
        queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.lists(),
      });

      NotificationUtil.success({
        message: t('business:virtualWarehouse.createSuccess'),
      });
    },
    onError: (error) => {
      console.error('Create virtual warehouse error:', error);
      NotificationUtil.error({
        message: t('business:virtualWarehouse.createError'),
      });
    },
  });
}

/**
 * Hook để cập nhật kho ảo
 */
export function useUpdateVirtualWarehouse() {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ warehouseId, data }: { warehouseId: number; data: UpdateVirtualWarehouseDto }) =>
      VirtualWarehouseService.updateVirtualWarehouse(warehouseId, data),
    onSuccess: (_, { warehouseId }) => {
      // Invalidate danh sách và chi tiết
      queryClient.invalidateQueries({
        queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.detail(warehouseId),
      });

      NotificationUtil.success({
        message: t('business:virtualWarehouse.updateSuccess'),
      });
    },
    onError: (error) => {
      console.error('Update virtual warehouse error:', error);
      NotificationUtil.error({
        message: t('business:virtualWarehouse.updateError'),
      });
    },
  });
}

/**
 * Hook để xóa kho ảo
 */
export function useDeleteVirtualWarehouse() {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (warehouseId: number) => VirtualWarehouseService.deleteVirtualWarehouse(warehouseId),
    onSuccess: () => {
      // Invalidate danh sách kho ảo
      queryClient.invalidateQueries({
        queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.lists(),
      });

      NotificationUtil.success({
        message: t('business:virtualWarehouse.deleteSuccess'),
      });
    },
    onError: (error) => {
      console.error('Delete virtual warehouse error:', error);
      NotificationUtil.error({
        message: t('business:virtualWarehouse.deleteError'),
      });
    },
  });
}

/**
 * Hook để xóa nhiều kho ảo
 */
export function useDeleteMultipleVirtualWarehouses() {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (warehouseIds: number[]) =>
      VirtualWarehouseService.deleteMultipleVirtualWarehouses(warehouseIds),
    onSuccess: () => {
      // Invalidate danh sách kho ảo
      queryClient.invalidateQueries({
        queryKey: VIRTUAL_WAREHOUSE_QUERY_KEYS.lists(),
      });

      NotificationUtil.success({
        message: t('business:virtualWarehouse.deleteMultipleSuccess'),
      });
    },
    onError: (error) => {
      console.error('Delete multiple virtual warehouses error:', error);
      NotificationUtil.error({
        message: t('business:virtualWarehouse.deleteMultipleError'),
      });
    },
  });
}
