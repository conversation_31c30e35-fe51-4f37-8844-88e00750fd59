import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  UserFileService,
  QueryFileDto,
  CreateFileDto,
  UpdateFileDto,
} from '../services/user-file.service';

// Query Keys
export const USER_FILE_KEYS = {
  all: ['user-files'] as const,
  lists: () => [...USER_FILE_KEYS.all, 'list'] as const,
  list: (params: QueryFileDto) => [...USER_FILE_KEYS.lists(), params] as const,
  folders: () => [...USER_FILE_KEYS.all, 'folder'] as const,
  folder: (folderId: number) => [...USER_FILE_KEYS.folders(), folderId] as const,
  details: () => [...USER_FILE_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...USER_FILE_KEYS.details(), id] as const,
};

/**
 * Hook lấy danh sách file với phân trang
 */
export const useFiles = (params: QueryFileDto) => {
  return useQuery({
    queryKey: USER_FILE_KEYS.list(params),
    queryFn: () => UserFileService.getFiles(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy danh sách file theo thư mục
 */
export const useFilesByFolder = (folderId: number, enabled = true) => {
  return useQuery({
    queryKey: USER_FILE_KEYS.folder(folderId),
    queryFn: () => UserFileService.getFilesByFolderId(folderId),
    enabled: enabled && !!folderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy thông tin chi tiết file
 */
export const useFile = (id: number, enabled = true) => {
  return useQuery({
    queryKey: USER_FILE_KEYS.detail(id),
    queryFn: () => UserFileService.getFileById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook tạo mới file
 */
export const useCreateFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFileDto) => UserFileService.createFile(data),
    onSuccess: () => {
      // Invalidate danh sách file
      queryClient.invalidateQueries({
        queryKey: USER_FILE_KEYS.lists(),
      });

      // Invalidate file theo thư mục - newFile là string URL, không có folderId
      // TODO: Cần refactor để có thông tin folderId
    },
  });
};

/**
 * Hook cập nhật file
 */
export const useUpdateFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateFileDto }) =>
      UserFileService.updateFile(id, data),
    onSuccess: (updatedFile, variables) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: USER_FILE_KEYS.lists(),
      });

      // Update cache cho detail
      queryClient.setQueryData(
        USER_FILE_KEYS.detail(variables.id),
        updatedFile
      );

      // Invalidate file theo thư mục
      if (updatedFile.folderId) {
        queryClient.invalidateQueries({
          queryKey: USER_FILE_KEYS.folder(updatedFile.folderId),
        });
      }
    },
  });
};

/**
 * Hook xóa file
 */
export const useDeleteFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => UserFileService.deleteFile(id),
    onSuccess: (_, fileId) => {
      // Invalidate tất cả queries liên quan
      queryClient.invalidateQueries({
        queryKey: USER_FILE_KEYS.all,
      });

      // Remove cache cho detail
      queryClient.removeQueries({
        queryKey: USER_FILE_KEYS.detail(fileId),
      });
    },
  });
};

/**
 * Hook upload file với TaskQueue - deprecated, sử dụng useFileUploadWithQueue thay thế
 * @deprecated Sử dụng useFileUploadWithQueue từ './useFileUploadWithQueue'
 */
export const useUploadFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ file, folderId, warehouseId }: {
      file: File;
      folderId?: number;
      warehouseId?: number;
    }) => {
      // Tạo file record và lấy presigned URL
      const createFileResponse = await UserFileService.createFileForUpload(
        file,
        folderId,
        warehouseId
      );

      // Trả về URL upload (createFileResponse là string)
      return createFileResponse;
    },
    onSuccess: () => {
      // Invalidate danh sách file
      queryClient.invalidateQueries({
        queryKey: USER_FILE_KEYS.lists(),
      });

      // Invalidate file theo thư mục - newFile là string URL, không có folderId
      // TODO: Cần refactor để có thông tin folderId
    },
  });
};

/**
 * Hook upload nhiều file với TaskQueue - deprecated, sử dụng useFileUploadWithQueue thay thế
 * @deprecated Sử dụng useFileUploadWithQueue từ './useFileUploadWithQueue'
 */
export const useUploadFiles = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ files, folderId, warehouseId }: {
      files: File[];
      folderId?: number;
      warehouseId?: number;
    }) => {
      // Tạo file records cho tất cả files
      const createFilePromises = files.map(file =>
        UserFileService.createFileForUpload(file, folderId, warehouseId)
      );

      const createFileResponses = await Promise.all(createFilePromises);

      // Trả về URLs upload (createFileResponses là array of strings)
      return createFileResponses;
    },
    onSuccess: () => {
      // Invalidate danh sách file
      queryClient.invalidateQueries({
        queryKey: USER_FILE_KEYS.lists(),
      });

      // Invalidate file theo thư mục - newFiles là array of strings, không có folderId
      // TODO: Cần refactor để có thông tin folderId
    },
  });
};
