import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  UserFolderService,
  QueryFolderDto,
  CreateFolderDto,
  UpdateFolderDto,
} from '../services/user-folder.service';

// Query Keys
export const USER_FOLDER_KEYS = {
  all: ['user-folders'] as const,
  lists: () => [...USER_FOLDER_KEYS.all, 'list'] as const,
  list: (params: QueryFolderDto) => [...USER_FOLDER_KEYS.lists(), params] as const,
  roots: () => [...USER_FOLDER_KEYS.all, 'root'] as const,
  children: () => [...USER_FOLDER_KEYS.all, 'children'] as const,
  child: (parentId: number) => [...USER_FOLDER_KEYS.children(), parentId] as const,
  details: () => [...USER_FOLDER_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...USER_FOLDER_KEYS.details(), id] as const,
};

/**
 * Hook lấy danh sách thư mục với phân trang
 */
export const useFolders = (params: QueryFolderDto) => {
  return useQuery({
    queryKey: USER_FOLDER_KEYS.list(params),
    queryFn: () => UserFolderService.getFolders(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy danh sách thư mục gốc
 */
export const useRootFolders = () => {
  return useQuery({
    queryKey: USER_FOLDER_KEYS.roots(),
    queryFn: () => UserFolderService.getRootFolders(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy danh sách thư mục con
 */
export const useChildFolders = (parentId: number, enabled = true) => {
  return useQuery({
    queryKey: USER_FOLDER_KEYS.child(parentId),
    queryFn: () => UserFolderService.getChildFolders(parentId),
    enabled: enabled && !!parentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy thông tin chi tiết thư mục
 */
export const useFolder = (id: number, enabled = true) => {
  return useQuery({
    queryKey: USER_FOLDER_KEYS.detail(id),
    queryFn: () => UserFolderService.getFolderById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook tạo mới thư mục
 */
export const useCreateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFolderDto) => UserFolderService.createFolder(data),
    onSuccess: (newFolder) => {
      // Invalidate danh sách thư mục
      queryClient.invalidateQueries({
        queryKey: USER_FOLDER_KEYS.lists(),
      });

      // Invalidate thư mục gốc nếu là thư mục gốc
      if (!newFolder.parentId) {
        queryClient.invalidateQueries({
          queryKey: USER_FOLDER_KEYS.roots(),
        });
      } else {
        // Invalidate thư mục con của thư mục cha
        queryClient.invalidateQueries({
          queryKey: USER_FOLDER_KEYS.child(newFolder.parentId),
        });
      }
    },
  });
};

/**
 * Hook cập nhật thư mục
 */
export const useUpdateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateFolderDto }) =>
      UserFolderService.updateFolder(id, data),
    onSuccess: (updatedFolder, variables) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: USER_FOLDER_KEYS.lists(),
      });

      // Update cache cho detail
      queryClient.setQueryData(
        USER_FOLDER_KEYS.detail(variables.id),
        updatedFolder
      );

      // Invalidate thư mục gốc hoặc con tùy thuộc vào parentId
      if (!updatedFolder.parentId) {
        queryClient.invalidateQueries({
          queryKey: USER_FOLDER_KEYS.roots(),
        });
      } else {
        queryClient.invalidateQueries({
          queryKey: USER_FOLDER_KEYS.child(updatedFolder.parentId),
        });
      }
    },
  });
};

/**
 * Hook xóa thư mục
 */
export const useDeleteFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => UserFolderService.deleteFolder(id),
    onSuccess: (_, folderId) => {
      // Invalidate tất cả queries liên quan
      queryClient.invalidateQueries({
        queryKey: USER_FOLDER_KEYS.all,
      });

      // Remove cache cho detail
      queryClient.removeQueries({
        queryKey: USER_FOLDER_KEYS.detail(folderId),
      });
    },
  });
};
