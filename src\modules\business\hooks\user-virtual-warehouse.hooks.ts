import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  UserVirtualWarehouseService,
  QueryVirtualWarehouseDto,
  CreateVirtualWarehouseDto,
  UpdateVirtualWarehouseDto,
} from '../services/user-virtual-warehouse.service';

// Query Keys
export const USER_VIRTUAL_WAREHOUSE_KEYS = {
  all: ['user-virtual-warehouses'] as const,
  lists: () => [...USER_VIRTUAL_WAREHOUSE_KEYS.all, 'list'] as const,
  list: (params: QueryVirtualWarehouseDto) => [...USER_VIRTUAL_WAREHOUSE_KEYS.lists(), params] as const,
  details: () => [...USER_VIRTUAL_WAREHOUSE_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...USER_VIRTUAL_WAREHOUSE_KEYS.details(), id] as const,
};

/**
 * Hook lấy danh sách kho ảo với phân trang
 */
export const useVirtualWarehouses = (params: QueryVirtualWarehouseDto) => {
  return useQuery({
    queryKey: USER_VIRTUAL_WAREHOUSE_KEYS.list(params),
    queryFn: () => UserVirtualWarehouseService.getVirtualWarehouses(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy thông tin chi tiết kho ảo
 */
export const useVirtualWarehouse = (warehouseId: number, enabled = true) => {
  return useQuery({
    queryKey: USER_VIRTUAL_WAREHOUSE_KEYS.detail(warehouseId),
    queryFn: () => UserVirtualWarehouseService.getVirtualWarehouseById(warehouseId),
    enabled: enabled && !!warehouseId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook tạo mới kho ảo
 */
export const useCreateVirtualWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ warehouseId, data }: { warehouseId: number; data: CreateVirtualWarehouseDto }) =>
      UserVirtualWarehouseService.createVirtualWarehouse(warehouseId, data),
    onSuccess: () => {
      // Invalidate và refetch danh sách kho ảo
      queryClient.invalidateQueries({
        queryKey: USER_VIRTUAL_WAREHOUSE_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook cập nhật kho ảo
 */
export const useUpdateVirtualWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ warehouseId, data }: { warehouseId: number; data: UpdateVirtualWarehouseDto }) =>
      UserVirtualWarehouseService.updateVirtualWarehouse(warehouseId, data),
    onSuccess: (data, variables) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: USER_VIRTUAL_WAREHOUSE_KEYS.lists(),
      });
      // Update cache cho detail
      queryClient.setQueryData(
        USER_VIRTUAL_WAREHOUSE_KEYS.detail(variables.warehouseId),
        data
      );
    },
  });
};

/**
 * Hook xóa kho ảo
 */
export const useDeleteVirtualWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (warehouseId: number) =>
      UserVirtualWarehouseService.deleteVirtualWarehouse(warehouseId),
    onSuccess: (_, warehouseId) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: USER_VIRTUAL_WAREHOUSE_KEYS.lists(),
      });
      // Remove cache cho detail
      queryClient.removeQueries({
        queryKey: USER_VIRTUAL_WAREHOUSE_KEYS.detail(warehouseId),
      });
    },
  });
};
