/**
 * Business Module
 *
 * Module quản lý các chức năng liên quan đến kinh doanh
 */

export * from './pages';
export * from './components';
export * from './types';
export * from './hooks';
export * from './routers/businessRouters';

// Export services with specific exports to avoid conflicts
export {
  ConversionService,
  CustomerService,
  CustomFieldService,
  CustomGroupFormService,
  InventoryService,
  OrderService,
  ProductService,
  ReportService,
  WarehouseCustomFieldService,
  WarehouseService,
  UserVirtualWarehouseService,
  UserFolderService,
  UserFileService,
  PhysicalWarehouseService,
  ShopService,
} from './services';

// Export service types with aliases to avoid conflicts
export type {
  CreateVirtualWarehouseDto as ServiceCreateVirtualWarehouseDto,
  UpdateVirtualWarehouseDto as ServiceUpdateVirtualWarehouseDto,
} from './services/user-virtual-warehouse.service';
