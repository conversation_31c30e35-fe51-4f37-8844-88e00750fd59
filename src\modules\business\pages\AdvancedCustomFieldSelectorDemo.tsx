import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
} from '@/shared/components/common';
import { AdvancedCustomFieldSelector } from '../components';
import { CustomFieldData } from '../components/SimpleCustomFieldSelector';

/**
 * Trang demo cho AdvancedCustomFieldSelector
 */
const AdvancedCustomFieldSelectorDemo: React.FC = () => {
  useTranslation(['business', 'common']);
  const [selectedFields, setSelectedFields] = useState<CustomFieldData[]>([]);

  // Handle field selection
  const handleFieldSelect = (fieldData: CustomFieldData) => {
    setSelectedFields(prev => {
      const existingIndex = prev.findIndex(field => field.id === fieldData.id);

      if (existingIndex !== -1) {
        // Remove if already selected
        return prev.filter((_, index) => index !== existingIndex);
      } else {
        // Add if not selected
        return [...prev, fieldData];
      }
    });
  };

  // Clear all selections
  const handleClearAll = () => {
    setSelectedFields([]);
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <Typography variant="h1" className="mb-2">
            Advanced Custom Field Selector Demo
          </Typography>
          <Typography variant="body1" className="text-muted">
            Demo component nâng cấp với MenuIconBar, filter và ActiveFilter
          </Typography>
        </div>

        {/* Main Demo */}
        <Card title="Advanced Custom Field Selector" className="p-6">
          <div className="space-y-4">
            <Typography variant="h6">
              Chọn trường tùy chỉnh với filter nâng cao
            </Typography>

            <AdvancedCustomFieldSelector
              onFieldSelect={handleFieldSelect}
              selectedFieldIds={selectedFields.map(f => f.id)}
              placeholder="Nhập từ khóa và nhấn Enter để tìm kiếm trường tùy chỉnh..."
              className="mb-4"
            />

            {/* Selected Fields Display */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-4">
                <Typography variant="h6">
                  Trường đã chọn ({selectedFields.length})
                </Typography>
                {selectedFields.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearAll}
                  >
                    Xóa tất cả
                  </Button>
                )}
              </div>

              {selectedFields.length === 0 ? (
                <div className="text-center py-8 text-gray-500 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Typography variant="body2">
                    Chưa có trường nào được chọn
                  </Typography>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {selectedFields.map((field) => (
                    <div
                      key={field.id}
                      className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <Typography variant="subtitle2" className="font-medium">
                          {field.label}
                        </Typography>
                        {field.required && (
                          <span className="text-red-500 text-xs">*</span>
                        )}
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                            {field.component}
                          </span>
                          <span className="text-xs text-gray-500">
                            {field.type}
                          </span>
                        </div>

                        {field.configId && (
                          <div className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded inline-block">
                            {field.configId}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Features */}
        <Card title="Tính năng mới" className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Typography variant="h6" className="mb-3">
                🔍 MenuIconBar
              </Typography>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Icon tìm kiếm với search bar</li>
                <li>• Icon filter với menu dropdown</li>
                <li>• Loading indicator</li>
                <li>• Responsive design</li>
              </ul>
            </div>

            <div>
              <Typography variant="h6" className="mb-3">
                🎯 Filter nâng cao
              </Typography>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Lọc theo nhóm trường tùy chỉnh</li>
                <li>• Tìm kiếm theo từ khóa</li>
                <li>• Sắp xếp theo tên/ngày tạo</li>
                <li>• Pagination với infinite scroll</li>
              </ul>
            </div>

            <div>
              <Typography variant="h6" className="mb-3">
                ✨ ActiveFilter
              </Typography>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Hiển thị filter đang áp dụng</li>
                <li>• Xóa từng filter riêng lẻ</li>
                <li>• Xóa tất cả filter</li>
                <li>• Badge đếm số filter</li>
              </ul>
            </div>

            <div>
              <Typography variant="h6" className="mb-3">
                🚀 API Integration
              </Typography>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Gọi API nhóm trường tùy chỉnh</li>
                <li>• Cache và debounce search</li>
                <li>• Error handling</li>
                <li>• Loading states</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Usage */}
        <Card title="Cách sử dụng" className="p-6">
          <Typography variant="body2" className="mb-4">
            Component này thay thế SimpleCustomFieldSelector với các tính năng nâng cao:
          </Typography>

          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <pre className="text-sm overflow-x-auto">
{`import { AdvancedCustomFieldSelector } from '@/modules/business/components';

<AdvancedCustomFieldSelector
  onFieldSelect={(fieldData) => {
    // Handle field selection
    console.log('Selected field:', fieldData);
  }}
  selectedFieldIds={[1, 2, 3]}
  placeholder="Nhập từ khóa để tìm kiếm..."
  className="mb-4"
/>`}
            </pre>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AdvancedCustomFieldSelectorDemo;
