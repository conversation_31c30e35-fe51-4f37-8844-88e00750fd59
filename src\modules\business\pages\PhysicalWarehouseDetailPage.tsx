import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Loading,
  Icon,
  Table,
  ConfirmDeleteModal,
  ActionMenu,
  ActionMenuItem,
  IconCard,
  Input,
  Textarea,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { usePhysicalWarehouse, useUpdatePhysicalWarehouse } from '../hooks/usePhysicalWarehouseQuery';
import { useWarehouseProducts, useBulkDeleteInventories } from '../hooks/useInventoryQuery';
import UpdateInventoryQuantityForm from '../components/forms/UpdateInventoryQuantityForm';
import { ProductInventoryResponseDto, WarehouseProductsQueryParams } from '../types/inventory.types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang chi tiết kho vật lý sử dụng usePhysicalWarehouse hook
 */
const PhysicalWarehouseDetailPage: React.FC = () => {
  const { warehouseId } = useParams<{ warehouseId: string }>();
  const { t } = useTranslation(['business', 'common']);

  // Parse warehouseId
  const warehouseIdNum = warehouseId ? parseInt(warehouseId) : 0;

  // State cho bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedInventoryItem, setSelectedInventoryItem] = useState<ProductInventoryResponseDto | null>(null);
  const [isEditingWarehouse, setIsEditingWarehouse] = useState(false);
  const [warehouseFormData, setWarehouseFormData] = useState({
    name: '',
    address: '',
    description: '',
    capacity: 0 as number,
  });
  const [warehouseFormErrors, setWarehouseFormErrors] = useState<Record<string, string>>({});

  // SlideInForm hooks
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();

  // Sử dụng hook usePhysicalWarehouse
  const { data: warehouseResponse, isLoading, error } = usePhysicalWarehouse(warehouseIdNum);

  // Hook để update warehouse
  const { mutateAsync: updateWarehouse, isPending: isUpdatingWarehouse } = useUpdatePhysicalWarehouse();

  // Initialize form data when warehouse data is loaded
  useEffect(() => {
    if (warehouseResponse?.result) {
      const warehouse = warehouseResponse.result;
      setWarehouseFormData({
        name: warehouse.name || '',
        address: warehouse.address || '',
        description: warehouse.description || '',
        capacity: Number(warehouse.capacity) || 0,
      });
    }
  }, [warehouseResponse?.result]);
  const warehouse = warehouseResponse?.result;

  // Hook để xóa nhiều inventory items
  const { mutateAsync: bulkDeleteInventories, isPending: isDeleting } = useBulkDeleteInventories();

  // Xử lý bulk delete
  const handleBulkDelete = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({ message: t('business:inventory.selectToDelete') });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys.length, t]);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      await bulkDeleteInventories(selectedRowKeys as number[]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:inventory.bulkDeleteSuccess', { count: selectedRowKeys.length })
      });

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('Error deleting inventories:', error);

      // Hiển thị thông báo lỗi
      const errorMessage = error instanceof Error ? error.message : t('business:inventory.bulkDeleteError');
      NotificationUtil.error({ message: errorMessage });
    }
  }, [selectedRowKeys, bulkDeleteInventories, t]);

  // Xử lý hủy bulk delete
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý cài đặt số lượng tồn kho
  const handleSetStockQuantity = useCallback((record: ProductInventoryResponseDto) => {
    setSelectedInventoryItem(record);
    showForm();
  }, [showForm]);

  // Xử lý submit form
  const handleFormSubmit = useCallback(() => {
    hideForm();
    setSelectedInventoryItem(null);
  }, [hideForm]);

  // Xử lý cancel form
  const handleFormCancel = useCallback(() => {
    hideForm();
    setSelectedInventoryItem(null);
  }, [hideForm]);

  // Xử lý save warehouse info
  const handleSaveWarehouse = useCallback(async () => {
    try {
      // Clear previous errors
      setWarehouseFormErrors({});

      // Validate required fields
      if (!warehouseFormData.name.trim()) {
        setWarehouseFormErrors({ name: t('business:physicalWarehouse.errors.nameRequired', 'Tên kho là bắt buộc') });
        return;
      }

      // Prepare data with proper types
      const updateData = {
        name: warehouseFormData.name.trim(),
        address: warehouseFormData.address.trim() || undefined,
        description: warehouseFormData.description.trim() || undefined,
        capacity: warehouseFormData.capacity > 0 ? warehouseFormData.capacity : undefined,
      };

      await updateWarehouse({
        id: warehouseIdNum,
        data: updateData,
      });
      setIsEditingWarehouse(false);
    } catch (error: unknown) {
      console.error('Error updating warehouse:', error);

      // Type guard for axios error
      const isAxiosError = (err: unknown): err is { response?: { data?: { code?: number; message?: string } } } => {
        return typeof err === 'object' && err !== null && 'response' in err;
      };

      // Xử lý lỗi code 30043 - tên kho đã tồn tại
      if (isAxiosError(error) && error.response?.data?.code === 30043) {
        setWarehouseFormErrors({
          name: error.response.data.message || t('business:physicalWarehouse.errors.nameExists', 'Tên kho đã tồn tại'),
        });
        return;
      }

      // Xử lý các lỗi khác
      NotificationUtil.error({
        message: isAxiosError(error) ? error.response?.data?.message || t('business:physicalWarehouse.updateError', 'Lỗi khi cập nhật kho') : t('business:physicalWarehouse.updateError', 'Lỗi khi cập nhật kho')
      });
    }
  }, [updateWarehouse, warehouseIdNum, warehouseFormData, t]);

  // Xử lý cancel edit warehouse
  const handleCancelEditWarehouse = useCallback(() => {
    if (warehouseResponse?.result) {
      const warehouse = warehouseResponse.result;
      setWarehouseFormData({
        name: warehouse.name || '',
        address: warehouse.address || '',
        description: warehouse.description || '',
        capacity: Number(warehouse.capacity) || 0,
      });
    }
    setWarehouseFormErrors({});
    setIsEditingWarehouse(false);
  }, [warehouseResponse?.result]);



  // Columns cho bảng sản phẩm warehouse
  const warehouseProductColumns = useMemo<TableColumn<ProductInventoryResponseDto>[]>(
    () => [
      {
        key: 'productName',
        title: t('business:product.name'),
        render: (_, record) => (
          <div className="flex items-center space-x-3">
            {record.productImages && record.productImages.length > 0 && (
              <img
                src={record.productImages[0]?.url || '/placeholder-image.png'}
                alt={record.productName}
                className="w-10 h-10 rounded object-cover"
              />
            )}
            <div>
              <Typography variant="body2" className="font-medium">
                {record.productName}
              </Typography>
              {record.sku && (
                <Typography variant="caption" className="text-gray-500">
                  SKU: {record.sku}
                </Typography>
              )}
            </div>
          </div>
        ),
        sortable: false,
      },
      {
        key: 'productDescription',
        title: t('business:product.description'),
        dataIndex: 'productDescription',
        render: (value: unknown) => (
          <Typography variant="body2" className="max-w-xs truncate">
            {(value as string) || 'N/A'}
          </Typography>
        ),
        sortable: false,
      },
      {
        key: 'currentQuantity',
        title: t('business:inventory.currentQuantity'),
        dataIndex: 'currentQuantity',
        render: (value: unknown) => (
          <Typography variant="body2" className="font-medium">
            {(value as number) || 0}
          </Typography>
        ),
        sortable: true,
      },
      {
        key: 'availableQuantity',
        title: t('business:inventory.availableQuantity'),
        dataIndex: 'availableQuantity',
        render: (value: unknown) => (
          <Typography variant="body2" className="text-green-600">
            {(value as number) || 0}
          </Typography>
        ),
        sortable: true,
      },
      {
        key: 'reservedQuantity',
        title: t('business:inventory.reservedQuantity'),
        dataIndex: 'reservedQuantity',
        render: (value: unknown) => (
          <Typography variant="body2" className="text-orange-600">
            {(value as number) || 0}
          </Typography>
        ),
        sortable: true,
      },
      {
        key: 'defectiveQuantity',
        title: t('business:inventory.defectiveQuantity'),
        dataIndex: 'defectiveQuantity',
        render: (value: unknown) => (
          <Typography variant="body2" className="text-red-600">
            {(value as number) || 0}
          </Typography>
        ),
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_, record) => {
          const actionItems: ActionMenuItem[] = [
            {
              id: 'set-stock',
              label: t('business:inventory.setStockQuantity', 'Cài đặt số lượng tồn kho'),
              icon: 'settings',
              onClick: () => handleSetStockQuantity(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="250px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      }
    ],
    [t, handleSetStockQuantity]
  );

  // Tạo hàm createQueryParams cho warehouse products
  const createWarehouseProductsQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    return {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
      warehouseId: warehouseIdNum,
    };
  };

  // Sử dụng hook useDataTable cho warehouse products
  const warehouseProductsDataTable = useDataTable(
    useDataTableConfig<ProductInventoryResponseDto, Record<string, unknown>>({
      columns: warehouseProductColumns,
      filterOptions: [],
      createQueryParams: createWarehouseProductsQueryParams,
    })
  );

  // Tạo query params cho warehouse products API
  const warehouseProductsQueryParams = useMemo((): WarehouseProductsQueryParams => {
    const baseParams = warehouseProductsDataTable.queryParams;
    return {
      page: (baseParams['page'] as number) || 1,
      limit: (baseParams['limit'] as number) || 10,
      search: baseParams['search'] as string | undefined,
      sortBy: baseParams['sortBy'] as string | undefined,
      sortDirection: baseParams['sortDirection'] as SortDirection | undefined,
      warehouseId: warehouseIdNum,
      ...(baseParams['isActive'] !== undefined && { isActive: baseParams['isActive'] as boolean }),
    };
  }, [warehouseProductsDataTable.queryParams, warehouseIdNum]);

  // Lấy danh sách sản phẩm warehouse
  const { data: warehouseProductsData, isLoading: warehouseProductsLoading } = useWarehouseProducts(
    warehouseProductsQueryParams
  );

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Icon name="alert-circle" className="w-16 h-16 text-red-300 mb-4 mx-auto" />
          <Typography variant="h6" className="text-red-600 mb-2">
            {t('business:physicalWarehouse.loadError')}
          </Typography>
          <Typography variant="body2" className="text-gray-500">
            {error.message}
          </Typography>
        </div>
      </div>
    );
  }

  // Not found state
  if (!warehouse) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Icon name="package" className="w-16 h-16 text-gray-300 mb-4 mx-auto" />
          <Typography variant="h6" className="text-gray-500 mb-2">
            {t('business:physicalWarehouse.notFound')}
          </Typography>
          <Typography variant="body2" className="text-gray-400">
            {t('business:physicalWarehouse.notFoundDescription')}
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" className="mt-2">
            {warehouse.name}
          </Typography>
          {warehouse.description && (
            <Typography variant="body2" className="text-gray-600 mt-1">
              {warehouse.description}
            </Typography>
          )}
        </div>
      </div>

      {/* Warehouse Info */}
      <Card>
        <div>
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6">
              {t('business:physicalWarehouse.information', 'Thông tin kho vật lý')}
            </Typography>
            <div className="flex space-x-2">
              {isEditingWarehouse ? (
                <>
                  <IconCard
                    icon="x"
                    variant="secondary"
                    size="sm"
                    title={t('common:cancel')}
                    onClick={handleCancelEditWarehouse}
                    disabled={isUpdatingWarehouse}
                  />
                  <IconCard
                    icon="save"
                    variant="primary"
                    size="sm"
                    title={t('common:save')}
                    onClick={handleSaveWarehouse}
                    disabled={isUpdatingWarehouse}
                    isLoading={isUpdatingWarehouse}
                  />
                </>
              ) : (
                <IconCard
                  icon="edit"
                  variant="primary"
                  size="sm"
                  title={t('business:physicalWarehouse.edit', 'Chỉnh sửa thông tin kho')}
                  onClick={() => setIsEditingWarehouse(true)}
                />
              )}
            </div>
          </div>

          {isEditingWarehouse ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <Typography variant="subtitle2" className="text-gray-500 mb-1">
                  {t('business:physicalWarehouse.name', 'Tên kho vật lý')} *
                </Typography>
                <Input
                  fullWidth
                  value={warehouseFormData.name}
                  onChange={(e) => {
                    setWarehouseFormData(prev => ({ ...prev, name: e.target.value }));
                    // Clear error when user starts typing
                    if (warehouseFormErrors['name']) {
                      setWarehouseFormErrors(prev => ({ ...prev, name: '' }));
                    }
                  }}
                  placeholder={t('business:physicalWarehouse.namePlaceholder', 'Nhập tên kho vật lý')}
                  className={warehouseFormErrors['name'] ? 'border-red-500' : ''}
                />
                {warehouseFormErrors['name'] && (
                  <Typography variant="caption" className="text-red-500 mt-1">
                    {warehouseFormErrors['name']}
                  </Typography>
                )}
              </div>

              <div>
                <Typography variant="subtitle2" className="text-gray-500 mb-1">
                  {t('business:physicalWarehouse.address', 'Địa chỉ')}
                </Typography>
                <Input
                  fullWidth
                  value={warehouseFormData.address}
                  onChange={(e) => setWarehouseFormData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder={t('business:physicalWarehouse.addressPlaceholder', 'Nhập địa chỉ kho')}
                />
              </div>

              <div>
                <Typography variant="subtitle2" className="text-gray-500 mb-1">
                  {t('business:physicalWarehouse.capacity', 'Sức chứa')}
                </Typography>
                <Input
                  type="number"
                  min="0"
                  fullWidth
                  value={warehouseFormData.capacity.toString()}
                  onChange={(e) => setWarehouseFormData(prev => ({
                    ...prev,
                    capacity: e.target.value === '' ? 0 : Number(e.target.value)
                  }))}
                  placeholder={t('business:physicalWarehouse.capacityPlaceholder', 'Nhập sức chứa')}
                />
              </div>

              <div className="md:col-span-2 lg:col-span-3">
                <Typography variant="subtitle2" className="text-gray-500 mb-1">
                  {t('business:physicalWarehouse.description', 'Mô tả')}
                </Typography>
                <Textarea
                  fullWidth
                  rows={3}
                  value={warehouseFormData.description}
                  onChange={(e) => setWarehouseFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder={t('business:physicalWarehouse.descriptionPlaceholder', 'Nhập mô tả kho')}
                />
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <Typography variant="subtitle2" className="text-gray-500 mb-1">
                  {t('business:physicalWarehouse.name', 'Tên kho vật lý')}
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {warehouse.name}
                </Typography>
              </div>

              <div>
                <Typography variant="subtitle2" className="text-gray-500 mb-1">
                  {t('business:physicalWarehouse.address', 'Địa chỉ')}
                </Typography>
                <Typography variant="body1">
                  {warehouse.address || t('common:notSet', 'Chưa thiết lập')}
                </Typography>
              </div>

              <div>
                <Typography variant="subtitle2" className="text-gray-500 mb-1">
                  {t('business:physicalWarehouse.capacity', 'Sức chứa')}
                </Typography>
                <Typography variant="body1">
                  {warehouse.capacity ? `${warehouse.capacity}` : t('common:notSet', 'Chưa thiết lập')}
                </Typography>
              </div>

              {warehouse.description && (
                <div className="md:col-span-2 lg:col-span-3">
                  <Typography variant="subtitle2" className="text-gray-500 mb-1">
                    {t('business:physicalWarehouse.description', 'Mô tả')}
                  </Typography>
                  <Typography variant="body1">
                    {warehouse.description}
                  </Typography>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>

      {/* Custom Fields */}
      {warehouse.customFields && warehouse.customFields.length > 0 && (
        <Card>
          <div className="p-6">
            <Typography variant="h6" className="mb-4">
              {t('business:physicalWarehouse.customFields')}
            </Typography>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {warehouse.customFields.map((field) => (
                <div key={field.id} className="border rounded-lg p-4">
                  <Typography variant="subtitle2" className="text-gray-500 mb-1">
                    {field.name}
                  </Typography>
                  <Typography variant="body1">
                    {String(field.value) || t('common:notSet')}
                  </Typography>
                  <Typography variant="caption" className="text-gray-400 mt-1 block">
                    {t('business:customField.type')}: {field.type}
                  </Typography>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* Warehouse Products Section */}
      <div className="space-y-4">
        <Typography variant="h5" className="font-semibold">
          {t('business:inventory.warehouseProducts')}
        </Typography>

        {/* Menu Bar for Warehouse Products */}
        <MenuIconBar
          onSearch={warehouseProductsDataTable.tableData.handleSearch}
          items={warehouseProductsDataTable.menuItems}
          onColumnVisibilityChange={warehouseProductsDataTable.columnVisibility.setVisibleColumns}
          columns={warehouseProductsDataTable.columnVisibility.visibleColumns}
          showDateFilter={false}
          showColumnFilter={true}
          additionalIcons={[
            {
              icon: 'trash',
              tooltip: t('common:delete'),
              variant: 'primary',
              onClick: handleBulkDelete,
              className: 'text-red-500',
              condition: selectedRowKeys.length > 0,
            },
          ]}
        />

        {/* SlideInForm for Update Inventory Quantity */}
        <SlideInForm isVisible={isFormVisible}>
          {selectedInventoryItem && (
            <UpdateInventoryQuantityForm
              inventoryData={selectedInventoryItem}
              onSubmit={handleFormSubmit}
              onCancel={handleFormCancel}
            />
          )}
        </SlideInForm>



        {/* Warehouse Products Table */}
        <Card className="overflow-hidden">
          <Table
            columns={warehouseProductsDataTable.columnVisibility.visibleTableColumns}
            data={warehouseProductsData?.items || []}
            rowKey="inventoryId"
            loading={warehouseProductsLoading}
            sortable={true}
            onSortChange={warehouseProductsDataTable.tableData.handleSortChange}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
            }}
            pagination={{
              current: warehouseProductsData?.meta.currentPage || 1,
              pageSize: warehouseProductsDataTable.tableData.pageSize,
              total: warehouseProductsData?.meta.totalItems || 0,
              onChange: warehouseProductsDataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Bulk Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('business:inventory.confirmBulkDeleteTitle')}
        message={t('business:inventory.confirmBulkDeleteMessage', {
          count: selectedRowKeys.length
        })}
        itemCount={selectedRowKeys.length}
        isSubmitting={isDeleting}
        confirmButtonText={t('common:delete')}
        cancelButtonText={t('common:cancel')}
      />
    </div>
  );
};

export default PhysicalWarehouseDetailPage;
