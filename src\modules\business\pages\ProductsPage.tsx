import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, Icon, StatusBadge } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import { formatTimestamp } from '@/shared/utils/date';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';

import {
  useProducts,
  useDeleteProduct,
  useDeleteMultipleProducts,
  useCreateProduct,
} from '../hooks/useProductQuery';
import {
  ProductDto,
  PriceTypeEnum,
  ProductQueryParams,
  CreateProductDto,
  ProductTypeEnum,
} from '../types/product.types';
import {
  ProductForm,
  ProductEditForm,
  ProductImport,
  ProductTypeSelector,
  DigitalProductForm,
  ServiceProductForm,
  EventProductForm,
  ComboProductForm,
} from '../components';

/**
 * Trang quản lý sản phẩm
 */
const ProductsPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);

  // State cho form và modal
  const [selectedProduct, setSelectedProduct] = useState<ProductDto | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  // const [formType, setFormType] = useState<'create' | 'edit'>('create');

  // State cho product type selection
  const [selectedProductType, setSelectedProductType] = useState<ProductTypeEnum | null>(null);
  const [showTypeSelector, setShowTypeSelector] = useState(false);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form import
  const {
    isVisible: isImportFormVisible,
    showForm: showImportForm,
    hideForm: hideImportForm,
  } = useSlideForm();

  // Định nghĩa các tùy chọn filter
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), value: 'all' },
      // Filter theo loại giá
      {
        id: 'hasPrice',
        label: t('business:product.priceType.hasPrice'),
        value: PriceTypeEnum.HAS_PRICE,
      },
      {
        id: 'stringPrice',
        label: t('business:product.priceType.stringPrice'),
        value: PriceTypeEnum.STRING_PRICE,
      },
      {
        id: 'noPrice',
        label: t('business:product.priceType.noPrice'),
        value: PriceTypeEnum.NO_PRICE,
      },
      // Filter theo loại sản phẩm
      {
        id: 'physical',
        label: t('business:product.types.physical.title', 'Sản phẩm vật lý'),
        value: ProductTypeEnum.PHYSICAL,
      },
      {
        id: 'digital',
        label: t('business:product.types.digital.title', 'Sản phẩm số'),
        value: ProductTypeEnum.DIGITAL,
      },
      {
        id: 'service',
        label: t('business:product.types.service.title', 'Dịch vụ'),
        value: ProductTypeEnum.SERVICE,
      },
      {
        id: 'event',
        label: t('business:product.types.event.title', 'Sự kiện'),
        value: ProductTypeEnum.EVENT,
      },
      {
        id: 'combo',
        label: t('business:product.types.combo.title', 'Combo sản phẩm'),
        value: ProductTypeEnum.COMBO,
      },
    ],
    [t]
  );

  // Xử lý chỉnh sửa sản phẩm
  const handleEdit = useCallback(
    (product: ProductDto) => {
      setSelectedProduct(product);
      showEditForm();
    },
    [showEditForm]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<ProductDto>[]>(
    () => [
      {
        key: 'name',
        title: t('business:product.name'),
        dataIndex: 'name',
        sortable: true,
        width: '18%',
      },
      {
        key: 'image',
        title: t('business:product.image'),
        dataIndex: 'images',
        width: '10%',
        render: (value: unknown) => {
          const images = value as Array<{ key: string; position: number; url: string }> | undefined;

          if (!images || images.length === 0) {
            return (
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <Icon name="image" size="sm" className="text-gray-400" />
              </div>
            );
          }

          // Lấy ảnh đầu tiên
          const firstImage = images[0];

          // Kiểm tra nếu không có ảnh
          if (!firstImage) {
            return (
              <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
            );
          }

          return (
            <div className="w-12 h-12 rounded-lg overflow-hidden">
              <img
                src={firstImage.url}
                alt="Product"
                className="w-full h-full object-cover"
                onError={e => {
                  // Nếu ảnh lỗi, hiển thị icon placeholder
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    parent.innerHTML = `
                      <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                        <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                        </svg>
                      </div>
                    `;
                  }
                }}
              />
            </div>
          );
        },
      },
      {
        key: 'productType',
        title: t('business:product.productType.title', 'Loại sản phẩm'),
        dataIndex: 'productType',
        width: '12%',
        render: (value: unknown) => {
          const productTypeValue = value as ProductTypeEnum;
          let chipVariant: 'primary' | 'success' | 'warning' | 'danger' | 'info' =
            'info';
          let label = '';

          switch (productTypeValue) {
            case ProductTypeEnum.PHYSICAL:
              chipVariant = 'primary';
              label = t('business:product.productType.physical', 'Vật lý');
              break;
            case ProductTypeEnum.DIGITAL:
              chipVariant = 'info';
              label = t('business:product.productType.digital', 'Số');
              break;
            case ProductTypeEnum.SERVICE:
              chipVariant = 'warning';
              label = t('business:product.productType.service', 'Dịch vụ');
              break;
            case ProductTypeEnum.EVENT:
              chipVariant = 'danger';
              label = t('business:product.productType.event', 'Sự kiện');
              break;
            case ProductTypeEnum.COMBO:
              chipVariant = 'success';
              label = t('business:product.productType.combo', 'Combo');
              break;
            default:
              label = String(productTypeValue);
          }

          return <StatusBadge text={label} variant={chipVariant} />;
        },
      },
      {
        key: 'typePrice',
        title: t('business:product.priceType.title'),
        dataIndex: 'typePrice',
        width: '12%',
        render: (value: unknown) => {
          const typePriceValue = value as PriceTypeEnum;
          let chipVariant: 'primary' | 'success' | 'warning' | 'danger' | 'info' =
            'info';
          let label = '';

          switch (typePriceValue) {
            case PriceTypeEnum.HAS_PRICE:
              chipVariant = 'success';
              label = t('business:product.priceType.hasPrice');
              break;
            case PriceTypeEnum.STRING_PRICE:
              chipVariant = 'warning';
              label = t('business:product.priceType.stringPrice');
              break;
            case PriceTypeEnum.NO_PRICE:
              chipVariant = 'danger';
              label = t('business:product.priceType.noPrice');
              break;
            default:
              label = String(typePriceValue);
          }

          return <StatusBadge text={label} variant={chipVariant} />;
        },
      },
      {
        key: 'tags',
        title: t('business:product.tags'),
        dataIndex: 'tags',
        width: '18%',
        render: (value: unknown) => {
          const tags = value as string[] | undefined;
          if (!tags || tags.length === 0) return '-';
          return tags.join(', ');
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt'),
        dataIndex: 'createdAt',
        sortable: true,
        width: '13%',
        render: (value: unknown) => formatTimestamp(value as number),
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '12%',
        render: (_: unknown, record: ProductDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEdit]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedProduct(null);
  }, []);

  // Mutation để xóa sản phẩm
  const { mutateAsync: deleteProduct } = useDeleteProduct();

  // Mutation để xóa nhiều sản phẩm
  const { mutateAsync: deleteMultipleProducts } = useDeleteMultipleProducts();

  // Mutation để tạo sản phẩm mới
  const { mutateAsync: createProduct, isPending: isCreating } = useCreateProduct();



  // ProductEditForm sẽ tự gọi API để lấy chi tiết sản phẩm nếu cần

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!selectedProduct) return;

    try {
      // Gọi API xóa sản phẩm
      await deleteProduct(selectedProduct.id);

      // Đóng popup
      setIsDeleteModalOpen(false);
      setSelectedProduct(null);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.deleteSuccess'),
        duration: 3000,
      });
    } catch {
      NotificationUtil.error({
        message: t('business:product.deleteError'),
        duration: 3000,
      });
    }
  }, [selectedProduct, deleteProduct, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('business:product.selectToDelete'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Gọi API xóa nhiều sản phẩm cùng lúc
      await deleteMultipleProducts(selectedRowKeys as number[]);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.bulkDeleteSuccess', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch {
      NotificationUtil.error({
        message: t('business:product.bulkDeleteError'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleProducts, t]);

  // Xử lý thêm mới - hiển thị ProductTypeSelector trước
  const handleAdd = () => {
    setSelectedProduct(null);
    setSelectedProductType(null);
    setShowTypeSelector(true);
    showAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
    hideEditForm();
    setSelectedProduct(null);
    setSelectedProductType(null);
    setShowTypeSelector(false);
  };

  // Xử lý khi chọn loại sản phẩm
  const handleTypeSelect = (type: ProductTypeEnum) => {
    setSelectedProductType(type);
    setShowTypeSelector(false);
  };

  // Xử lý hủy chọn loại sản phẩm
  const handleTypeSelectorCancel = () => {
    setShowTypeSelector(false);
    setSelectedProductType(null);
    hideAddForm();
  };

  // Render form component dựa trên loại sản phẩm đã chọn
  const renderProductForm = () => {
    if (showTypeSelector) {
      return (
        <ProductTypeSelector
          onTypeSelect={handleTypeSelect}
          onCancel={handleTypeSelectorCancel}
        />
      );
    }

    if (!selectedProductType) {
      return null;
    }

    const commonProps = {
      onSubmit: handleSubmit,
      onCancel: handleCancel,
      isSubmitting: isCreating,
    };

    switch (selectedProductType) {
      case ProductTypeEnum.PHYSICAL:
        return <ProductForm {...commonProps} />;
      case ProductTypeEnum.DIGITAL:
        return <DigitalProductForm {...commonProps} />;
      case ProductTypeEnum.SERVICE:
        return <ServiceProductForm {...commonProps} />;
      case ProductTypeEnum.EVENT:
        return <EventProductForm {...commonProps} />;
      case ProductTypeEnum.COMBO:
        return <ComboProductForm {...commonProps} />;
      default:
        return <ProductForm {...commonProps} />;
    }
  };

  // Xử lý import
  const handleImport = () => {
    showImportForm();
  };

  // Xử lý đóng import form
  const handleImportClose = () => {
    hideImportForm();
  };

  // Xử lý hoàn thành import
  const handleImportComplete = () => {
    hideImportForm();
  };

  // Xử lý submit form thêm mới
  const handleSubmit = async (values: CreateProductDto) => {
    // Gọi API tạo sản phẩm và trả về response cho ProductForm
    const response = await createProduct(values);
    // Đóng form
    hideAddForm();
    // Trả về response để ProductForm có thể xử lý upload media
    return response;
  };



  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): ProductQueryParams => {
    const queryParams: ProductQueryParams = {
      page: params.page,
      limit: params.pageSize,
    };

    // Chỉ thêm các property khi có giá trị thực sự
    if (params.searchTerm) {
      queryParams.search = params.searchTerm;
    }
    if (params.sortBy) {
      queryParams.sortBy = params.sortBy;
    }
    if (params.sortDirection) {
      queryParams.sortDirection = params.sortDirection;
    }

    if (params.filterValue !== 'all') {
      // Kiểm tra xem filterValue có phải là PriceTypeEnum không
      if (Object.values(PriceTypeEnum).includes(params.filterValue as PriceTypeEnum)) {
        queryParams.typePrice = params.filterValue as PriceTypeEnum;
      }
      // Kiểm tra xem filterValue có phải là ProductTypeEnum không
      else if (Object.values(ProductTypeEnum).includes(params.filterValue as ProductTypeEnum)) {
        queryParams.productType = params.filterValue as ProductTypeEnum;
      }
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTableConfig = useDataTableConfig<ProductDto, ProductQueryParams>({
    columns,
    filterOptions,
    showDateFilter: false,
    createQueryParams,
  });

  // Tùy chỉnh allOptionLabel để đảm bảo đa ngôn ngữ
  const dataTable = useDataTable({
    ...dataTableConfig,
    columnVisibilityOptions: {
      ...dataTableConfig.columnVisibilityOptions,
      allOptionLabel: t('common:selectAll', 'Chọn tất cả'),
    },
  });

  // Gọi API lấy danh sách sản phẩm với queryParams từ dataTable
  const { data: productData, isLoading } = useProducts(dataTable.queryParams);

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback(
    (column: string | null, order: SortOrder | null) => {
      // Nếu column hoặc order là null, reset sort
      if (column === null || order === null) {
        dataTable.tableData.handleSortChange(null, null);
        return;
      }
      dataTable.tableData.handleSortChange(column, order);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      // Price type labels
      [PriceTypeEnum.HAS_PRICE]: t('business:product.priceType.hasPrice'),
      [PriceTypeEnum.STRING_PRICE]: t('business:product.priceType.stringPrice'),
      [PriceTypeEnum.NO_PRICE]: t('business:product.priceType.noPrice'),
      // Product type labels
      [ProductTypeEnum.PHYSICAL]: t('business:product.types.physical.title', 'Sản phẩm vật lý'),
      [ProductTypeEnum.DIGITAL]: t('business:product.types.digital.title', 'Sản phẩm số'),
      [ProductTypeEnum.SERVICE]: t('business:product.types.service.title', 'Dịch vụ'),
      [ProductTypeEnum.EVENT]: t('business:product.types.event.title', 'Sự kiện'),
      [ProductTypeEnum.COMBO]: t('business:product.types.combo.title', 'Combo sản phẩm'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'upload',
            tooltip: t('business:product.import.title'),
            variant: 'primary',
            onClick: handleImport,
            className: 'text-blue-500',
          },
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới với type selection */}
      <SlideInForm isVisible={isAddFormVisible}>
        {renderProductForm()}
      </SlideInForm>

      {/* Form chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {selectedProduct && (
          <ProductEditForm
            productId={selectedProduct.id}
            onCancel={handleCancel}
            onSuccess={() => {
              hideEditForm();
              setSelectedProduct(null);
            }}
          />
        )}
      </SlideInForm>

      {/* Form import */}
      <SlideInForm isVisible={isImportFormVisible}>
        <ProductImport
          onClose={handleImportClose}
          onImportComplete={handleImportComplete}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={productData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: productData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: productData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete')}
        message={t('business:product.confirmDeleteMessage')}
        {...(selectedProduct?.name && { itemName: selectedProduct.name })}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t('business:product.confirmBulkDeleteMessage', { count: selectedRowKeys.length })}
      />
    </div>
  );
};

export default ProductsPage;
