import { z } from 'zod';

/**
 * Schema cho việc tạo kho vật lý
 */
export const createPhysicalWarehouseSchema = z.object({
  warehouseId: z
    .union([
      z.string().min(1, 'ID kho không được để trống'),
      z.number().min(1, 'ID kho phải lớn hơn 0')
    ])
    .refine((val) => {
      if (typeof val === 'string') {
        const num = Number(val);
        return !isNaN(num) && num > 0;
      }
      return val > 0;
    }, 'ID kho phải là số hợp lệ và lớn hơn 0'),

  address: z
    .string({
      required_error: 'Địa chỉ kho không được để trống',
      invalid_type_error: 'Địa chỉ kho phải là chuỗi',
    })
    .min(1, 'Địa chỉ kho không được để trống')
    .max(255, 'Địa chỉ kho không được vượt quá 255 ký tự'),

  capacity: z
    .union([
      z.string(),
      z.number()
    ])
    .optional()
    .refine((val) => {
      if (!val || val === '') return true; // Optional field
      const num = typeof val === 'string' ? Number(val) : val;
      return !isNaN(num) && num >= 0;
    }, 'Sức chứa kho phải là số hợp lệ và lớn hơn hoặc bằng 0'),
});

/**
 * Schema cho việc cập nhật kho vật lý
 */
export const updatePhysicalWarehouseSchema = z.object({
  address: z
    .string({
      invalid_type_error: 'Địa chỉ kho phải là chuỗi',
    })
    .min(1, 'Địa chỉ kho không được để trống')
    .max(255, 'Địa chỉ kho không được vượt quá 255 ký tự')
    .optional(),

  capacity: z
    .number({
      invalid_type_error: 'Sức chứa kho phải là số',
    })
    .min(0, 'Sức chứa kho phải lớn hơn hoặc bằng 0')
    .optional(),
});

/**
 * Schema cho tham số truy vấn kho vật lý
 */
export const queryPhysicalWarehouseSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  sortBy: z.enum(['warehouseId', 'address', 'capacity']).optional(),
  sortDirection: z.enum(['asc', 'desc']).optional(),
  offset: z.number().min(0).optional(),
});

// Export types từ schemas
export type CreatePhysicalWarehouseFormValues = z.infer<typeof createPhysicalWarehouseSchema>;
export type UpdatePhysicalWarehouseFormValues = z.infer<typeof updatePhysicalWarehouseSchema>;
export type QueryPhysicalWarehouseFormValues = z.infer<typeof queryPhysicalWarehouseSchema>;
