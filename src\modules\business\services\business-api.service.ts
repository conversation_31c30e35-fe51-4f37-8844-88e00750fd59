import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Base API paths cho business
 */
const USER_API_PATH = '/user';

/**
 * Enum cho trạng thái đơn hàng (đồng bộ với backend)
 */
export enum OrderStatusEnum {
  PENDING = 'pending',
  CONFIRMED = 'confirmed', 
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

/**
 * Enum cho trạng thái vận chuyển (đồng bộ với backend)
 */
export enum ShippingStatusEnum {
  PENDING = 'pending',
  PREPARING = 'preparing',
  SHIPPED = 'shipped',
  IN_TRANSIT = 'in_transit',
  SORTING = 'sorting',
  DELIVERED = 'delivered',
  DELIVERY_FAILED = 'delivery_failed',
  RETURNING = 'returning',
  CANCELLED = 'cancelled'
}

/**
 * Enum cho phương thức vận chuyển (đồng bộ với backend)
 */
export enum ShippingMethodEnum {
  GHN_STANDARD = 'Chuẩn',
  GHN_EXPRESS = 'Nhanh',
  GHN_URGENT = 'Hỏa tốc',
  GHTK_ROAD = 'Đường bộ',
  GHTK_FLY = 'Đường hàng không',
  AHAMOVE_SAME_DAY = 'Giao hàng trong ngày',
  JT_STANDARD = 'Tiêu chuẩn'
}

/**
 * Enum cho trạng thái thanh toán (đồng bộ với backend)
 */
export enum PaymentStatusEnum {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

/**
 * Enum cho phương thức thanh toán (đồng bộ với backend)
 */
export enum PaymentMethodEnum {
  CASH = 'CASH',
  BANKING = 'BANKING',
  CREDIT_CARD = 'CREDIT_CARD',
  E_WALLET = 'E_WALLET'
}

/**
 * Interface cho Customer từ backend API
 */
export interface ApiUserConvertCustomer {
  id: number;
  name: string;
  email: {
    primary: string;
    secondary?: string;
  };
  phone: string;
  address?: string;
  avatar?: string;
  platform?: string;
  timezone?: string;
  metadata?: Record<string, unknown>;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho Order từ backend API
 */
export interface ApiUserOrder {
  id: number;
  userConvertCustomerId: number;
  userId: number;
  productInfo: Record<string, unknown>;
  billInfo: {
    subtotal: number;
    tax?: number;
    shippingFee: number;
    discount?: number;
    total: number;
    paymentMethod: PaymentMethodEnum;
    paymentStatus: PaymentStatusEnum;
    selectedCarrier?: string;
    shippingServiceType?: string;
  };
  hasShipping: boolean;
  shippingStatus: ShippingStatusEnum;
  logisticInfo: Record<string, unknown>;
  createdAt: number;
  updatedAt: number;
  source: string;
  orderStatus: OrderStatusEnum;
  userConvertCustomer?: ApiUserConvertCustomer;
}

/**
 * Interface cho Order List Item từ backend API
 */
export interface ApiUserOrderListItem {
  id: number;
  userConvertCustomer: ApiUserConvertCustomer;
  billInfo: {
    total: number;
    subtotal: number;
    shippingFee: number;
    paymentMethod: PaymentMethodEnum;
    paymentStatus: PaymentStatusEnum;
    selectedCarrier?: string;
    shippingServiceType?: string;
  };
  shippingStatus: ShippingStatusEnum;
  createdAt: number;
  source: string;
  orderStatus: OrderStatusEnum;
}

/**
 * Interface cho Order Status Stats từ backend API
 */
export interface ApiOrderStatusStats {
  pending: number;
  confirmed: number;
  processing: number;
  completed: number;
  cancelled: number;
  total: number;
}

/**
 * Interface cho Shipping Status Stats từ backend API
 */
export interface ApiShippingStatusStats {
  pending: number;
  preparing: number;
  shipped: number;
  inTransit: number;
  sorting: number;
  delivered: number;
  deliveryFailed: number;
  returning: number;
  cancelled: number;
  total: number;
}

/**
 * Interface cho Order Status Response từ backend API
 */
export interface ApiUserOrderStatusResponse {
  orderStatus: ApiOrderStatusStats;
  shippingStatus: ApiShippingStatusStats;
}

/**
 * Query parameters cho orders
 */
export interface OrderQueryParams {
  page?: number | undefined;
  limit?: number | undefined;
  userConvertCustomerId?: number | undefined;
  shippingStatus?: ShippingStatusEnum | undefined;
  source?: string | undefined;
  orderStatus?: OrderStatusEnum | undefined;
  sortBy?: 'createdAt' | 'updatedAt' | 'shippingStatus' | 'orderStatus' | undefined;
  sortDirection?: 'ASC' | 'DESC' | undefined;
}

/**
 * DTO cho tạo đơn hàng (updated to match API specification)
 */
export interface CreateUserOrderDto {
  shopId: number;
  customerInfo: {
    customerId: number;
  };
  products: Array<{
    productId: number;
    quantity: number;
  }>;
  billInfo: {
    subtotal: number;
    shippingFee?: number | undefined;
    selectedCarrier?: string | undefined;
    shippingServiceType?: string | undefined;
    discount?: number | undefined;
    paymentMethod: PaymentMethodEnum;
    paymentStatus?: PaymentStatusEnum | undefined;
    total?: number | undefined;
  };
  hasShipping?: boolean | undefined;
  shippingStatus?: ShippingStatusEnum | undefined;
  logisticInfo?: {
    shippingMethod?: ShippingMethodEnum | undefined;
    carrier?: string | undefined;
    shippingNote?: string | undefined;
    deliveryAddress?: {
      addressId?: number | undefined;
      newAddress?: Record<string, unknown> | undefined;
    } | undefined;
  } | undefined;
  orderStatus?: OrderStatusEnum | undefined;
  source?: string | undefined;
  note?: string | undefined;
  tags?: string[] | undefined;
}

/**
 * Service chính cho Business API
 */
export const BusinessApiService = {
  /**
   * === ORDER APIs ===
   */

  /**
   * Lấy danh sách đơn hàng
   */
  getOrders: async (params?: OrderQueryParams): Promise<PaginatedResult<ApiUserOrderListItem>> => {
    try {
      console.log('🔍 [BusinessApiService] Fetching orders with params:', params);
      console.log('🔍 [BusinessApiService] API URL:', `${USER_API_PATH}/orders`);

      const response = await apiClient.get<PaginatedResult<ApiUserOrderListItem>>(
        `${USER_API_PATH}/orders`,
        { params }
      );

      console.log('✅ [BusinessApiService] Orders response:', response);
      console.log('✅ [BusinessApiService] Orders result:', response.result);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [BusinessApiService] Error fetching orders:', error);
      console.error('❌ [BusinessApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/orders`,
        params
      });
      throw error;
    }
  },

  /**
   * Lấy chi tiết đơn hàng
   */
  getOrderDetail: async (orderId: number): Promise<ApiUserOrder> => {
    try {
      console.log('🔍 [BusinessApiService] Fetching order detail for ID:', orderId);
      console.log('🔍 [BusinessApiService] API URL:', `${USER_API_PATH}/orders/detail/${orderId}`);

      const response = await apiClient.get<ApiUserOrder>(
        `${USER_API_PATH}/orders/detail/${orderId}`
      );

      console.log('✅ [BusinessApiService] Order detail response:', response);
      console.log('✅ [BusinessApiService] Order detail result:', response.result);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [BusinessApiService] Error fetching order detail:', error);
      console.error('❌ [BusinessApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/orders/detail/${orderId}`,
        orderId
      });
      throw error;
    }
  },

  /**
   * Tạo đơn hàng mới
   */
  createOrder: async (data: CreateUserOrderDto): Promise<ApiUserOrder> => {
    try {
      console.log('🔍 [BusinessApiService] Creating new order...');
      console.log('🔍 [BusinessApiService] Create data:', data);
      console.log('🔍 [BusinessApiService] API URL:', `${USER_API_PATH}/orders`);

      const response = await apiClient.post<ApiUserOrder>(
        `${USER_API_PATH}/orders`,
        data
      );

      console.log('✅ [BusinessApiService] Create response:', response);
      console.log('✅ [BusinessApiService] Created order:', response.result);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [BusinessApiService] Error creating order:', error);
      console.error('❌ [BusinessApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/orders`,
        requestData: data
      });
      throw error;
    }
  },

  /**
   * Lấy thống kê trạng thái đơn hàng
   */
  getOrderStatusStats: async (): Promise<ApiUserOrderStatusResponse> => {
    try {
      console.log('🔍 [BusinessApiService] Fetching order status stats...');
      console.log('🔍 [BusinessApiService] API URL:', `${USER_API_PATH}/orders/status-stats`);

      const response = await apiClient.get<ApiUserOrderStatusResponse>(
        `${USER_API_PATH}/orders/status-stats`
      );

      console.log('✅ [BusinessApiService] Order status stats response:', response);
      console.log('✅ [BusinessApiService] Order status stats result:', response.result);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [BusinessApiService] Error fetching order status stats:', error);
      console.error('❌ [BusinessApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/orders/status-stats`
      });
      throw error;
    }
  },

  /**
   * Tracking đơn hàng
   */
  trackOrder: async (orderId: number): Promise<unknown> => {
    try {
      console.log('🔍 [BusinessApiService] Tracking order ID:', orderId);
      console.log('🔍 [BusinessApiService] API URL:', `${USER_API_PATH}/orders/${orderId}/track`);

      const response = await apiClient.get<unknown>(
        `${USER_API_PATH}/orders/${orderId}/track`
      );

      console.log('✅ [BusinessApiService] Track order response:', response);
      console.log('✅ [BusinessApiService] Track order result:', response.result);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [BusinessApiService] Error tracking order:', error);
      console.error('❌ [BusinessApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/orders/${orderId}/track`,
        orderId
      });
      throw error;
    }
  },

  /**
   * In đơn hàng
   */
  printOrder: async (orderId: number, options?: Record<string, unknown>): Promise<unknown> => {
    try {
      console.log('🖨️ [BusinessApiService] Printing order ID:', orderId);
      console.log('🖨️ [BusinessApiService] Print options:', options);
      console.log('🖨️ [BusinessApiService] API URL:', `${USER_API_PATH}/orders/${orderId}/print`);

      const response = await apiClient.post<unknown>(
        `${USER_API_PATH}/orders/${orderId}/print`,
        options || {}
      );

      console.log('✅ [BusinessApiService] Print order response:', response);
      console.log('✅ [BusinessApiService] Print order result:', response.result);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [BusinessApiService] Error printing order:', error);
      console.error('❌ [BusinessApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/orders/${orderId}/print`,
        orderId,
        options
      });
      throw error;
    }
  },
};
