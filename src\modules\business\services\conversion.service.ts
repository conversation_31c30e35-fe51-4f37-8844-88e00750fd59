import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  ConversionDetailDto,
  ConversionListItemDto,
  ConversionQueryParams,
} from '../types/conversion.types';

/**
 * Service xử lý API liên quan đến chuyển đổi
 */
export const ConversionService = {
  /**
   * Lấy danh sách bản ghi chuyển đổi
   * @param params Tham số truy vấn
   * @returns Danh sách bản ghi chuyển đổi với phân trang
   */
  getConverts: async (params?: ConversionQueryParams): Promise<ApiResponseDto<PaginatedResult<ConversionListItemDto>>> => {
    return apiRequest.get('/user/converts', { params });
  },

  /**
   * <PERSON><PERSON>y chi tiết bản ghi chuyển đổi theo ID
   * @param id ID của bản ghi chuyển đổi
   * @returns Chi tiết bản ghi chuyển đổi
   */
  getConvertById: async (id: number): Promise<ApiResponseDto<ConversionDetailDto>> => {
    return apiRequest.get(`/user/converts/detail/${id}`);
  },
};
