import {
  CustomerImportService,
  UploadFileRequest,
  ValidateImportRequest,
  StartImportRequest,
} from './customer-import.service';
import {
  ExcelData,
  ColumnMapping,
  MappingTemplate,
  AutoMappingSuggestion,
  DataQualityMetrics,
  ImportJob,
  ValidationRule,
  ImportValidationResult,
  ImportSourceType,
} from '../types/customer-import.types';
import {
  DEFAULT_VALIDATION_RULES,
  CUSTOMER_FIELD_OPTIONS,
  AUTO_MAPPING_CONFIDENCE,
  DATA_QUALITY_THRESHOLDS,
  DEFAULT_IMPORT_CONFIG,
} from '../constants/customer-import.constants';

/**
 * Interface cho upload result
 */
export interface UploadResult {
  excelData: ExcelData;
  autoMappingSuggestions: AutoMappingSuggestion[];
  dataQualityMetrics: DataQualityMetrics;
}

/**
 * Interface cho import configuration
 */
export interface ImportConfiguration {
  skipFirstRow: boolean;
  validateEmail: boolean;
  validatePhone: boolean;
  allowDuplicates: boolean;
  batchSize: number;
  validationRules: ValidationRule[];
}

/**
 * Services Layer cho Customer Import - chứa business logic
 */
export const CustomerImportBusinessService = {
  /**
   * Upload và xử lý file với business logic
   * @param file File cần upload
   * @param hasHeader Có header hay không
   * @returns Upload result với suggestions và metrics
   */
  uploadAndProcessFile: async (
    file: File,
    hasHeader: boolean = true
  ): Promise<UploadResult> => {
    // Validate file trước khi upload
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = ['.xlsx', '.xls', '.csv'];
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

    if (file.size > maxSize) {
      throw new Error(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
    }

    if (!allowedTypes.includes(fileExtension)) {
      throw new Error(`Unsupported file format. Allowed: ${allowedTypes.join(', ')}`);
    }

    // Upload và parse file
    const uploadRequest: UploadFileRequest = { file, hasHeader };
    const uploadResponse = await CustomerImportService.uploadAndParseFile(uploadRequest);
    
    if (!uploadResponse.result) {
      throw new Error('Failed to parse file');
    }

    const { data: excelData, suggestions = [] } = uploadResponse.result;

    // Tính toán data quality metrics
    const qualityRequest: ValidateImportRequest = {
      excelData,
      mappings: [], // Empty mappings for initial quality check
      validationRules: DEFAULT_VALIDATION_RULES.map(rule => rule.id),
    };

    const qualityResponse = await CustomerImportService.calculateDataQuality(qualityRequest);
    const dataQualityMetrics = qualityResponse.result || {
      totalRows: excelData.rows.length,
      validRows: 0,
      invalidRows: 0,
      duplicateRows: 0,
      emptyRows: 0,
      qualityScore: 0,
      fieldQuality: {},
    };

    return {
      excelData,
      autoMappingSuggestions: suggestions,
      dataQualityMetrics,
    };
  },

  /**
   * Generate smart column mappings
   * @param excelData Excel data
   * @param existingMappings Existing mappings (optional)
   * @returns Suggested mappings
   */
  generateSmartMappings: async (
    excelData: ExcelData,
    existingMappings: ColumnMapping[] = []
  ): Promise<ColumnMapping[]> => {
    // Get AI suggestions
    const suggestionsResponse = await CustomerImportService.getAutoMappingSuggestions(excelData);
    const suggestions = suggestionsResponse.result || [];

    // Create mappings based on suggestions and existing mappings
    const mappings: ColumnMapping[] = [];
    const existingMappingMap = new Map(
      existingMappings.map(mapping => [mapping.excelColumn, mapping])
    );

    excelData.headers.forEach(header => {
      // Check if mapping already exists
      const existingMapping = existingMappingMap.get(header);
      if (existingMapping) {
        mappings.push(existingMapping);
        return;
      }

      // Find AI suggestion
      const suggestion = suggestions.find((s: { excelColumn: string }) => s.excelColumn === header);
      
      if (suggestion && suggestion.confidence >= AUTO_MAPPING_CONFIDENCE.MEDIUM) {
        // Use AI suggestion if confidence is high enough
        mappings.push({
          excelColumn: header,
          customerField: suggestion.suggestedField,
          isRequired: CUSTOMER_FIELD_OPTIONS.find(
            field => field.value === suggestion.suggestedField
          )?.required || false,
        });
      } else {
        // Create unmapped entry
        mappings.push({
          excelColumn: header,
          customerField: '',
          isRequired: false,
        });
      }
    });

    return mappings;
  },

  /**
   * Validate mappings và data
   * @param excelData Excel data
   * @param mappings Column mappings
   * @param validationRules Custom validation rules
   * @returns Validation result
   */
  validateMappingsAndData: async (
    excelData: ExcelData,
    mappings: ColumnMapping[],
    validationRules: ValidationRule[] = DEFAULT_VALIDATION_RULES
  ): Promise<ImportValidationResult> => {
    // Check required mappings
    const requiredFields = CUSTOMER_FIELD_OPTIONS.filter(field => field.required);
    const mappedFields = mappings
      .filter(mapping => mapping.customerField)
      .map(mapping => mapping.customerField);

    const missingRequiredFields = requiredFields.filter(
      field => !mappedFields.includes(field.value)
    );

    if (missingRequiredFields.length > 0) {
      return {
        isValid: false,
        errors: missingRequiredFields.map(field => ({
          row: -1,
          column: '',
          field: field.value,
          value: '',
          message: `Required field "${field.label}" is not mapped`,
        })),
        warnings: [],
        summary: {
          totalRows: excelData.rows.length,
          validRows: 0,
          invalidRows: excelData.rows.length,
          duplicateRows: 0,
        },
      };
    }

    // Validate with API
    const validateRequest: ValidateImportRequest = {
      excelData,
      mappings,
      validationRules: validationRules.filter(rule => rule.enabled).map(rule => rule.id),
    };

    const validationResponse = await CustomerImportService.validateImportData(validateRequest);
    return validationResponse.result || {
      isValid: false,
      errors: [],
      warnings: [],
      summary: {
        totalRows: excelData.rows.length,
        validRows: 0,
        invalidRows: 0,
        duplicateRows: 0,
      },
    };
  },

  /**
   * Start import process với business logic
   * @param excelData Excel data
   * @param mappings Column mappings
   * @param config Import configuration
   * @returns Import job
   */
  startImportProcess: async (
    excelData: ExcelData,
    mappings: ColumnMapping[],
    config: Partial<ImportConfiguration> = {}
  ): Promise<ImportJob> => {
    const finalConfig = { ...DEFAULT_IMPORT_CONFIG, ...config };

    const startRequest: StartImportRequest = {
      excelData,
      mappings,
      config: finalConfig,
    };

    const importResponse = await CustomerImportService.startImport(startRequest);

    if (!importResponse.result) {
      throw new Error('Failed to start import process');
    }

    return importResponse.result;
  },

  /**
   * Calculate data quality score
   * @param metrics Data quality metrics
   * @returns Quality score và level
   */
  calculateQualityScore: (metrics: DataQualityMetrics) => {
    const { totalRows, validRows, duplicateRows, emptyRows } = metrics;
    
    if (totalRows === 0) return { score: 0, level: 'poor' as const };

    const validRate = validRows / totalRows;
    const duplicateRate = duplicateRows / totalRows;
    const emptyRate = emptyRows / totalRows;

    // Calculate weighted score
    const score = Math.round(
      (validRate * 70) + // 70% weight for valid data
      ((1 - duplicateRate) * 20) + // 20% weight for no duplicates
      ((1 - emptyRate) * 10) // 10% weight for no empty rows
    );

    let level: 'excellent' | 'good' | 'fair' | 'poor';
    if (score >= DATA_QUALITY_THRESHOLDS.EXCELLENT) level = 'excellent';
    else if (score >= DATA_QUALITY_THRESHOLDS.GOOD) level = 'good';
    else if (score >= DATA_QUALITY_THRESHOLDS.FAIR) level = 'fair';
    else level = 'poor';

    return { score, level };
  },

  /**
   * Create mapping template từ current mappings
   * @param name Template name
   * @param mappings Current mappings
   * @param description Template description
   * @param isPublic Is template public
   * @returns Created template
   */
  createMappingTemplate: async (
    name: string,
    mappings: ColumnMapping[],
    description?: string,
    isPublic: boolean = false
  ): Promise<MappingTemplate> => {
    const template: Omit<MappingTemplate, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'> = {
      name,
      mappings,
      isPublic,
      tags: [],
    };

    // Chỉ thêm description khi có giá trị
    if (description) {
      template.description = description;
    }

    const response = await CustomerImportService.createMappingTemplate(template);

    if (!response.result) {
      throw new Error('Failed to create template');
    }

    return response.result;
  },

  /**
   * Apply template mappings to current data
   * @param template Mapping template
   * @param excelHeaders Current Excel headers
   * @returns Applied mappings
   */
  applyTemplate: (
    template: MappingTemplate,
    excelHeaders: string[]
  ): ColumnMapping[] => {
    const mappings: ColumnMapping[] = [];

    excelHeaders.forEach(header => {
      // Find exact match first
      const exactMatch = template.mappings.find(
        mapping => mapping.excelColumn.toLowerCase() === header.toLowerCase()
      );

      if (exactMatch) {
        mappings.push({
          excelColumn: header,
          customerField: exactMatch.customerField,
          isRequired: exactMatch.isRequired,
        });
      } else {
        // Find partial match
        const partialMatch = template.mappings.find(mapping =>
          header.toLowerCase().includes(mapping.excelColumn.toLowerCase()) ||
          mapping.excelColumn.toLowerCase().includes(header.toLowerCase())
        );

        if (partialMatch) {
          mappings.push({
            excelColumn: header,
            customerField: partialMatch.customerField,
            isRequired: partialMatch.isRequired,
          });
        } else {
          // No match found
          mappings.push({
            excelColumn: header,
            customerField: '',
            isRequired: false,
          });
        }
      }
    });

    return mappings;
  },

  /**
   * Get import source by type
   * @param sourceType Source type
   * @returns Import source configuration
   */
  getImportSourceConfig: (sourceType: ImportSourceType) => {
    const sources = {
      excel: {
        acceptedTypes: ['.xlsx', '.xls'],
        maxSize: 50 * 1024 * 1024, // 50MB
        requiresAuth: false,
      },
      csv: {
        acceptedTypes: ['.csv'],
        maxSize: 50 * 1024 * 1024, // 50MB
        requiresAuth: false,
      },
      'google-sheets': {
        acceptedTypes: [],
        maxSize: 0,
        requiresAuth: true,
      },
      api: {
        acceptedTypes: [],
        maxSize: 0,
        requiresAuth: false,
      },
      crm: {
        acceptedTypes: [],
        maxSize: 0,
        requiresAuth: true,
      },
    };

    return sources[sourceType];
  },
};

export default CustomerImportBusinessService;
