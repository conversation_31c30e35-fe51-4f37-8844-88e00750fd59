import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  InventoryItemDto,
  InventoryQueryParams,
  UpdateInventoryQuantityDto,
  CreateInventoryDto,
  ProductInventoryResponseDto,
  WarehouseProductsQueryParams,
} from '../types/inventory.types';

/**
 * Service xử lý API liên quan đến inventory
 */
export const InventoryService = {
  /**
   * L<PERSON>y danh sách sản phẩm trong kho
   * @param params Tham số truy vấn
   * @returns Danh sách sản phẩm trong kho với phân trang
   */
  getInventoryItems: async (params?: InventoryQueryParams): Promise<ApiResponseDto<PaginatedResult<InventoryItemDto>>> => {
    return apiClient.get('/user/inventories', { params });
  },

  /**
   * <PERSON><PERSON><PERSON> danh sách sản phẩm trong kho theo warehouseId
   * @param warehouseId ID của kho
   * @param params Tham số truy vấn bổ sung
   * @returns Danh sách sản phẩm trong kho với phân trang
   */
  getInventoryItemsByWarehouse: async (
    warehouseId: number,
    params?: Omit<InventoryQueryParams, 'warehouseId'>
  ): Promise<ApiResponseDto<PaginatedResult<InventoryItemDto>>> => {
    return apiClient.get('/user/inventories', {
      params: {
        ...params,
        warehouseId,
      },
    });
  },

  /**
   * Lấy chi tiết một item inventory theo ID
   * @param id ID của inventory item
   * @returns Chi tiết inventory item
   */
  getInventoryItemById: async (id: number): Promise<ApiResponseDto<InventoryItemDto>> => {
    return apiClient.get(`/user/inventories/${id}`);
  },

  /**
   * Cập nhật số lượng sản phẩm trong kho
   * @param id ID của inventory item
   * @param data Dữ liệu cập nhật
   * @returns Inventory item đã cập nhật
   */
  updateInventoryQuantity: async (
    id: number,
    data: UpdateInventoryQuantityDto
  ): Promise<ApiResponseDto<InventoryItemDto>> => {
    return apiClient.put(`/user/inventory/${id}`, data);
  },

  /**
   * Tạo mới inventory item
   * @param data Dữ liệu tạo mới
   * @returns Inventory item đã tạo
   */
  createInventoryItem: async (data: CreateInventoryDto): Promise<ApiResponseDto<InventoryItemDto>> => {
    return apiClient.post('/user/inventories', data);
  },

  /**
   * Xóa inventory item
   * @param id ID của inventory item
   * @returns Kết quả xóa
   */
  deleteInventoryItem: async (id: number): Promise<ApiResponseDto<void>> => {
    return apiClient.delete(`/user/inventories/${id}`);
  },

  /**
   * Cập nhật số lượng hàng loạt
   * @param updates Danh sách cập nhật
   * @returns Kết quả cập nhật
   */
  bulkUpdateQuantities: async (
    updates: Array<{ id: number; data: UpdateInventoryQuantityDto }>
  ): Promise<ApiResponseDto<InventoryItemDto[]>> => {
    return apiClient.put('/user/inventories/bulk-update', { updates });
  },

  /**
   * Xóa nhiều inventory items
   * @param inventoryIds Danh sách ID inventory cần xóa
   * @returns Kết quả xóa
   */
  bulkDeleteInventories: async (inventoryIds: number[]): Promise<ApiResponseDto<void>> => {
    return apiClient.delete('/user/inventory/devele/bulk', {
      data: { inventoryIds }
    });
  },

  /**
   * Lấy danh sách sản phẩm trong warehouse với thông tin chi tiết
   * @param params Tham số truy vấn bao gồm warehouseId
   * @returns Danh sách sản phẩm trong warehouse với phân trang
   */
  getWarehouseProducts: async (
    params: WarehouseProductsQueryParams
  ): Promise<ApiResponseDto<PaginatedResult<ProductInventoryResponseDto>>> => {
    return apiClient.get('/user/inventory/warehouse/products', { params });
  },

  /**
   * Xóa sản phẩm khỏi warehouse
   * @param warehouseId ID của warehouse
   * @param productId ID của sản phẩm
   * @returns Kết quả xóa
   */
  removeProductFromWarehouse: async (
    warehouseId: number,
    productId: number
  ): Promise<ApiResponseDto<void>> => {
    return apiClient.delete(`/user/inventory/warehouse/${warehouseId}/products/${productId}`);
  },

  /**
   * Xóa nhiều sản phẩm khỏi warehouse
   * @param warehouseId ID của warehouse
   * @param productIds Danh sách ID sản phẩm cần xóa
   * @returns Kết quả xóa
   */
  removeMultipleProductsFromWarehouse: async (
    warehouseId: number,
    productIds: number[]
  ): Promise<ApiResponseDto<void>> => {
    return apiClient.delete(`/user/inventory/warehouse/${warehouseId}/products/bulk`, {
      data: { productIds }
    });
  },
};
