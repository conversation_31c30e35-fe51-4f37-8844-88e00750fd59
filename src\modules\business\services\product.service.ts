import { apiClient } from '@/shared/api';
import {
  ProductDto,
  ProductQueryParams,
  CreateProductDto,
  UpdateProductDto,
  UpdateProductResponse,
  PaginatedResult,
} from '../types/product.types';

/**
 * Service xử lý các API liên quan đến sản phẩm
 */
export const ProductService = {
  /**
   * L<PERSON>y danh sách sản phẩm
   * @param params Tham số truy vấn
   * @returns Promise với danh sách sản phẩm và thông tin phân trang
   */
  getProducts: async (params?: ProductQueryParams): Promise<PaginatedResult<ProductDto>> => {
    const response = await apiClient.get<PaginatedResult<ProductDto>>('/user/products', {
      params,
    });
    return response.result;
  },

  /**
   * Lấy chi tiết sản phẩm theo ID
   * @param id ID của sản phẩm
   * @returns Promise với thông tin chi tiết sản phẩm
   */
  getProductById: async (id: number): Promise<ProductDto> => {
    const response = await apiClient.get<ProductDto>(`/user/products/${id}`);
    return response.result;
  },

  /**
   * Tạo sản phẩm mới
   * @param data Dữ liệu sản phẩm mới
   * @returns Promise với thông tin sản phẩm đã tạo
   */
  createProduct: async (data: CreateProductDto): Promise<ProductDto> => {
    console.log('🔍 ProductService.createProduct called with data:', JSON.stringify(data, null, 2));
    console.log('🔍 Inventory data in request:', data.inventory);

    const response = await apiClient.post<ProductDto>('/user/products', data);

    console.log('✅ ProductService.createProduct response:', response);
    return response.result;
  },

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm
   * @param data Dữ liệu cập nhật
   * @returns Promise với thông tin sản phẩm đã cập nhật và upload URLs (nếu có)
   */
  updateProduct: async (id: number, data: UpdateProductDto): Promise<UpdateProductResponse> => {
    const response = await apiClient.put<UpdateProductResponse>(`/user/products/${id}`, data);
    return response.result;
  },

  /**
   * Xóa sản phẩm
   * @param id ID của sản phẩm
   * @returns Promise với kết quả xóa
   */
  deleteProduct: async (id: number): Promise<void> => {
    await apiClient.delete(`/user/products/${id}`);
  },

  /**
   * Tạo URL presigned để upload hình ảnh sản phẩm
   * @param mimeType MIME type của file
   * @returns Promise với URL presigned và key
   */
  getPresignedUrl: async (mimeType: string): Promise<{ url: string; key: string; index: number }> => {
    const response = await apiClient.post<{ url: string; key: string; index: number }>(
      '/user/products/presigned-url',
      { mimeType }
    );
    return response.result;
  },

  /**
   * Xóa nhiều sản phẩm
   * @param ids Danh sách ID sản phẩm cần xóa
   * @returns Promise với kết quả xóa
   */
  deleteMultipleProducts: async (ids: number[]): Promise<void> => {
    await apiClient.delete('/user/products/bulk', { data: { productIds: ids } });
  },
};

export default ProductService;
