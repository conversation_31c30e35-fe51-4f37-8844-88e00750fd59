/**
 * Types cho import khách hàng từ Excel
 */

/**
 * Interface cho dữ liệu Excel đã parse
 */
export interface ExcelData {
  headers: string[];
  rows: (string | number | boolean | null)[][];
  fileName: string;
}

/**
 * Interface cho mapping cột Excel với trường khách hàng
 */
export interface ColumnMapping {
  excelColumn: string;
  customerField: string;
  isRequired: boolean;
}

/**
 * Interface cho trường khách hàng có thể map
 */
export interface CustomerField {
  key: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'date' | 'number' | 'boolean' | 'select';
  required: boolean;
  isCustomField: boolean;
  options?: string[]; // Cho select field
}

/**
 * Interface cho dữ liệu khách hàng sau khi import
 */
export interface ImportedCustomerData {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  tags?: string[];
  customFields?: Record<string, unknown>;
  rowIndex: number; // Để track lỗi
}

/**
 * Interface cho kết quả validation import
 */
export interface ImportValidationResult {
  isValid: boolean;
  errors: ImportValidationError[];
  warnings: ImportValidationError[];
  summary: {
    totalRows: number;
    validRows: number;
    invalidRows: number;
    duplicateRows: number;
  };
}

/**
 * Interface cho lỗi validation import
 */
export interface ImportValidationError {
  row: number;
  column: string;
  field: string;
  value: string;
  message: string;
}

/**
 * Interface cho cấu hình import
 */
export interface ImportConfig {
  skipFirstRow: boolean; // Bỏ qua dòng đầu (header)
  mappings: ColumnMapping[];
  validateEmail: boolean;
  validatePhone: boolean;
  allowDuplicates: boolean;
}

/**
 * Interface cho trạng thái import nâng cao
 */
export interface ImportState {
  step: 'upload' | 'mapping' | 'preview' | 'importing' | 'complete';
  excelData: ExcelData | null;
  mappings: ColumnMapping[];
  validationResult: ImportValidationResult | null;
  importProgress: number;
  importedCount: number;
  errorCount: number;
  // New fields for enhanced features
  selectedSource: ImportSource | null;
  selectedTemplate: MappingTemplate | null;
  autoMappingSuggestions: AutoMappingSuggestion[];
  dataQualityMetrics: DataQualityMetrics | null;
  validationRules: ValidationRule[];
  batchConfig: BatchProcessingConfig;
  currentJob: ImportJob | null;
}

/**
 * Interface cho response import từ API
 */
export interface ImportResponse {
  success: boolean;
  importedCount: number;
  errorCount: number;
  errors: ImportValidationError[];
}

/**
 * Enum cho các trường khách hàng cơ bản
 */
export enum CustomerFieldKey {
  NAME = 'name',
  EMAIL = 'email',
  PHONE = 'phone',
  ADDRESS = 'address',
  TAGS = 'tags',
  AVATAR = 'avatar',
}

/**
 * Interface cho import từ URL
 */
export interface ImportFromUrlData {
  url: string;
  hasHeader: boolean;
  sheetName?: string;
}

/**
 * Interface cho template mapping
 */
export interface MappingTemplate {
  id: string;
  name: string;
  description?: string | undefined;
  mappings: ColumnMapping[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  isPublic: boolean;
  tags?: string[] | undefined;
}

/**
 * Interface cho multi-source import
 */
export interface ImportSource {
  type: 'excel' | 'csv' | 'google-sheets' | 'api' | 'crm';
  name: string;
  icon: string;
  description: string;
  supportedFormats?: string[];
  requiresAuth?: boolean;
}

/**
 * Interface cho Google Sheets import
 */
export interface GoogleSheetsImportData {
  spreadsheetId: string;
  sheetName: string;
  range?: string;
  hasHeader: boolean;
}

/**
 * Interface cho API import
 */
export interface ApiImportData {
  endpoint: string;
  method: 'GET' | 'POST';
  headers?: Record<string, string>;
  body?: unknown;
  dataPath?: string; // JSONPath to data array
}

/**
 * Interface cho validation rules
 */
export interface ValidationRule {
  id: string;
  field: string;
  type: 'required' | 'email' | 'phone' | 'regex' | 'length' | 'custom';
  value?: unknown;
  message: string;
  enabled: boolean;
}

/**
 * Interface cho data quality metrics
 */
export interface DataQualityMetrics {
  totalRows: number;
  validRows: number;
  invalidRows: number;
  duplicateRows: number;
  emptyRows: number;
  qualityScore: number; // 0-100
  fieldQuality: Record<string, {
    validCount: number;
    invalidCount: number;
    emptyCount: number;
    score: number;
  }>;
}

/**
 * Interface cho batch processing
 */
export interface BatchProcessingConfig {
  batchSize: number;
  maxConcurrency: number;
  retryAttempts: number;
  retryDelay: number;
  enableQueue: boolean;
}

/**
 * Interface cho import job
 */
export interface ImportJob {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  totalRows: number;
  processedRows: number;
  successRows: number;
  errorRows: number;
  startTime: Date;
  endTime?: Date;
  errors: ImportValidationError[];
  config: ImportConfig;
  source: ImportSource;
}

/**
 * Interface cho AI auto-mapping
 */
export interface AutoMappingSuggestion {
  excelColumn: string;
  suggestedField: string;
  confidence: number; // 0-1
  reason: string;
  alternatives?: Array<{
    field: string;
    confidence: number;
  }>;
}

/**
 * Interface cho analytics data
 */
export interface ImportAnalytics {
  totalImports: number;
  successRate: number;
  averageProcessingTime: number;
  mostUsedFields: Array<{
    field: string;
    usage: number;
  }>;
  errorPatterns: Array<{
    error: string;
    frequency: number;
  }>;
  performanceMetrics: {
    averageRowsPerSecond: number;
    peakRowsPerSecond: number;
    averageFileSize: number;
  };
}

/**
 * Type cho step của import process
 */
export type ImportStep = 'upload' | 'mapping' | 'preview' | 'importing' | 'complete';

/**
 * Type cho import source types
 */
export type ImportSourceType = 'excel' | 'csv' | 'google-sheets' | 'api' | 'crm';

/**
 * Type cho validation rule types
 */
export type ValidationRuleType = 'required' | 'email' | 'phone' | 'regex' | 'length' | 'custom';

/**
 * Type cho import job status
 */
export type ImportJobStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';

/**
 * Constants cho default values
 */
export const DEFAULT_BATCH_CONFIG: BatchProcessingConfig = {
  batchSize: 100,
  maxConcurrency: 3,
  retryAttempts: 3,
  retryDelay: 1000,
  enableQueue: true,
};

export const SUPPORTED_FILE_FORMATS = [
  '.xlsx',
  '.xls',
  '.csv',
  '.json',
] as const;

export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

export const DEFAULT_VALIDATION_RULES: ValidationRule[] = [
  {
    id: 'name-required',
    field: 'name',
    type: 'required',
    message: 'Name is required',
    enabled: true,
  },
  {
    id: 'email-format',
    field: 'email',
    type: 'email',
    message: 'Invalid email format',
    enabled: true,
  },
  {
    id: 'phone-format',
    field: 'phone',
    type: 'phone',
    message: 'Invalid phone format',
    enabled: false,
  },
];

/**
 * Interface cho component props
 */
export interface CustomerImportProps {
  onClose: () => void;
  onImportComplete: (importedCount: number) => void;
  initialTemplate?: MappingTemplate;
  enabledSources?: ImportSourceType[];
  customValidationRules?: ValidationRule[];
}

/**
 * Interface cho step component props
 */
export interface ImportStepProps {
  onNext: () => void;
  onBack: () => void;
  onCancel: () => void;
  importState: ImportState;
  onStateChange: (updates: Partial<ImportState>) => void;
}
