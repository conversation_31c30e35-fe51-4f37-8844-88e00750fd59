/**
 * Đ<PERSON><PERSON> nghĩa các types cho Shop trong module business
 */

/**
 * Interface cho thông tin cửa hàng
 */
export interface ShopInfoDto {
  /**
   * ID của cửa hàng
   */
  id: string;

  /**
   * ID của người dùng sở hữu cửa hàng
   */
  userId: number;

  /**
   * Tên cửa hàng
   */
  shopName: string;

  /**
   * Số điện thoại cửa hàng
   */
  shopPhone: string;

  /**
   * Địa chỉ cửa hàng
   */
  shopAddress: string;

  /**
   * Tỉnh/Thành phố
   */
  shopProvince: string;

  /**
   * Quận/Huyện
   */
  shopDistrict: string;

  /**
   * Phường/Xã
   */
  shopWard: string;

  /**
   * Thời gian tạo
   */
  createdAt: string;

  /**
   * Thời gian cập nhật
   */
  updatedAt: string;
}

/**
 * DTO cho tạo hoặc cập nhật thông tin cửa hàng
 */
export interface CreateOrUpdateShopInfoDto {
  /**
   * Tên cửa hàng
   */
  shopName: string;

  /**
   * Số điện thoại cửa hàng
   */
  shopPhone: string;

  /**
   * Địa chỉ cửa hàng
   */
  shopAddress: string;

  /**
   * Tỉnh/Thành phố
   */
  shopProvince: string;

  /**
   * Quận/Huyện
   */
  shopDistrict: string;

  /**
   * Phường/Xã
   */
  shopWard: string;
}

/**
 * Response type cho API shop info
 */
export interface ShopInfoResponse {
  success: boolean;
  message: string;
  data: ShopInfoDto;
}
