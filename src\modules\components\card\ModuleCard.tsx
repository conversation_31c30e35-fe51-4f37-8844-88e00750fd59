import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, Icon, IconName } from '@/shared/components/common';

interface ModuleCardProps {
  /**
   * Tiêu đề của card
   */
  title: string;

  /**
   * <PERSON><PERSON> tả ngắn gọn
   */
  description: string;

  /**
   * Tên icon hiển thị
   */
  icon: IconName;



  /**
   * Đường dẫn khi click vào card
   */
  linkTo: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Màu gradient chủ đạo
   */
  gradientColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';

  /**
   * Hiển thị loading state
   */
  loading?: boolean;

  /**
   * <PERSON><PERSON>ch thước card
   */
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Component hiển thị card cho các module trong hệ thống
 * Dùng chung cho các module như Marketing, Business, v.v.
 */
const ModuleCard: React.FC<ModuleCardProps> = ({
  title,
  description,
  icon,
  linkTo,
  className = '',
  gradientColor = 'primary',
  loading = false,
  size = 'md',
}) => {
  const navigate = useNavigate();

  // Gradient color mappings
  const gradientClasses = {
    primary: 'from-primary/10 via-primary/5 to-transparent',
    secondary: 'from-secondary/10 via-secondary/5 to-transparent',
    success: 'from-success/10 via-success/5 to-transparent',
    warning: 'from-warning/10 via-warning/5 to-transparent',
    error: 'from-error/10 via-error/5 to-transparent',
    info: 'from-info/10 via-info/5 to-transparent',
  };

  // Icon color mappings
  const iconColorClasses = {
    primary: 'text-primary',
    secondary: 'text-secondary',
    success: 'text-success',
    warning: 'text-warning',
    error: 'text-error',
    info: 'text-info',
  };

  // Size mappings
  const sizeClasses = {
    sm: 'min-h-[140px]',
    md: 'min-h-[160px]',
    lg: 'min-h-[180px]',
  };

  const iconSizes = {
    sm: 'md' as const,
    md: 'lg' as const,
    lg: 'xl' as const,
  };

  if (loading) {
    return (
      <Card className={`${sizeClasses[size]} ${className} animate-pulse`}>
        <div className="p-6 space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
            <div className="flex-1 space-y-2">
              <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      onClick={() => {
        navigate(linkTo);
      }}
      className={`
        ${sizeClasses[size]}
        ${className}
        cursor-pointer
        group
        relative
        overflow-hidden
        transition-all
        duration-300
        ease-in-out
        hover:shadow-xl
        hover:shadow-primary/10
        hover:-translate-y-1
        border-0
        bg-gradient-to-br
        ${gradientClasses[gradientColor]}
        backdrop-blur-sm
      `}
      variant="elevated"
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-32 h-32 opacity-5 transform translate-x-8 -translate-y-8 transition-transform duration-500 group-hover:scale-110">
        <Icon name={icon} size="xl" className="w-full h-full" />
      </div>

      {/* Content */}
      <div className="relative z-10 p-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-start space-x-3 mb-4">
          <div className={`
            p-3
            rounded-xl
            bg-gradient-to-br
            ${gradientClasses[gradientColor]}
            border
            border-white/20
            shadow-lg
            transition-transform
            duration-300
            group-hover:scale-110
            group-hover:rotate-3
          `}>
            <Icon
              name={icon}
              size={iconSizes[size]}
              className={`${iconColorClasses[gradientColor]} transition-colors duration-300`}
            />
          </div>
          <div className="flex-1 min-w-0">
            <Typography
              variant="h6"
              className="font-semibold text-foreground group-hover:text-primary transition-colors duration-300 truncate"
            >
              {title}
            </Typography>
            <Typography
              variant="body2"
              className="text-muted mt-1 line-clamp-2 leading-relaxed"
            >
              {description}
            </Typography>
          </div>
        </div>


      </div>
    </Card>
  );
};

export default ModuleCard;
