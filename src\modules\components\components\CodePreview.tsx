import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface CodePreviewProps {
  /**
   * Code cần hiển thị
   */
  code: string;

  /**
   * Ngôn ngữ của code
   */
  language?: 'jsx' | 'tsx' | 'css' | 'html' | 'javascript';

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị code với khả năng copy
 */
const CodePreview: React.FC<CodePreviewProps> = ({ code, language = 'tsx', className = '' }) => {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);

    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  return (
    <div className={`relative bg-gray-900 rounded-lg overflow-hidden ${className}`}>
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800">
        <span className="text-xs font-medium text-gray-400">{language.toUpperCase()}</span>
        <button
          onClick={handleCopy}
          className="text-xs text-gray-400 hover:text-white transition-colors"
        >
          {copied ? t('components.copied') : t('components.copy')}
        </button>
      </div>
      <div className="p-4 overflow-x-auto">
        <pre className="text-sm text-gray-300 font-mono">
          <code>{code}</code>
        </pre>
      </div>
    </div>
  );
};

export default CodePreview;
