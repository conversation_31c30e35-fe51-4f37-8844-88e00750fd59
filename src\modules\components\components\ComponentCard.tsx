import React, { ReactNode } from 'react';
import { Card } from '@/shared/components/common';

interface ComponentCardProps {
  /**
   * Tiêu đề của component
   */
  title: string;

  /**
   * Mô tả ngắn về component
   */
  description?: string;

  /**
   * Component demo
   */
  children: ReactNode;

  /**
   * Code mẫu để hiển thị
   */
  code?: string;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Card hiển thị một component với tiêu đề, mô tả và code mẫu
 */
const ComponentCard: React.FC<ComponentCardProps> = ({
  title,
  description,
  children,
  code,
  className = '',
}) => {
  return (
    <Card
      title={title}
      subtitle={description}
      className={`overflow-hidden ${className}`}
      variant="bordered"
    >
      <div className="p-4 bg-gray-50 dark:bg-gray-900/50 flex items-center justify-center min-h-[120px]">
        {children}
      </div>

      {code && (
        <div className="p-4 bg-gray-900 overflow-x-auto">
          <pre className="text-sm text-gray-300 font-mono">
            <code>{code}</code>
          </pre>
        </div>
      )}
    </Card>
  );
};

export default ComponentCard;
