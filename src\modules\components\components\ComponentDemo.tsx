import React, { ReactNode, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Icon } from '@/shared/components/common';
import { CodePreview } from '.';

interface ComponentDemoProps {
  /**
   * Tiêu đề của component
   */
  title: string;

  /**
   * Mô tả ngắn về component
   */
  description?: string;

  /**
   * Component demo
   */
  children: ReactNode;

  /**
   * Code mẫu để hiển thị
   */
  code?: string;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị demo và code của một component
 */
const ComponentDemo: React.FC<ComponentDemoProps> = ({
  title,
  description,
  children,
  code,
  className = '',
}) => {
  const { t } = useTranslation();
  const [showCode, setShowCode] = useState(false);

  return (
    <Card
      className={`mb-8 ${className}`}
      variant="bordered"
      allowOverflow={true}
      title={title}
      subtitle={description}
      extra={
        code && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCode(!showCode)}
            rightIcon={<Icon name={showCode ? 'chevron-up' : 'chevron-down'} />}
          >
            {showCode ? t('components.hideCode') : t('components.showCode')}
          </Button>
        )
      }
    >
      <div className="p-6 bg-gray-50 dark:bg-gray-900/50 flex items-center justify-center min-h-[350px] overflow-visible">
        {children}
      </div>

      {showCode && code && (
        <div className="border-t border-gray-100 dark:border-gray-700">
          <CodePreview code={code} />
        </div>
      )}
    </Card>
  );
};

export default ComponentDemo;
