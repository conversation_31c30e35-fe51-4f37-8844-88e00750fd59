{"components": {"library": {"title": "Components Library", "description": "Available components in RedAI Frontend Template."}, "form": {"wizard": {"title": "Form Wizard", "description": "FormWizard component manages multi-step forms with validation and navigation.", "basic": {"title": "Basic Form Wizard", "description": "Basic form wizard with 3 steps and validation."}}}, "charts": {"demo": {"title": "Chart Demo", "description": "Chart components with responsive design, multilingual support, and theme compatibility."}, "lineChart": {"title": "Line Chart", "description": "LineChart component displays data as lines, supporting multiple data lines, tooltips, and legends.", "basic": {"title": "Basic Line Chart", "description": "Basic line chart with a single data line."}, "multiLine": {"title": "Multi-line Chart", "description": "Line chart with multiple data lines."}, "customized": {"title": "Customized Line Chart", "description": "Line chart with customizations like line type, stroke width, and dot display."}}}, "animation": {"title": "Animation", "description": "Dynamic animations available in RedAI Frontend Template."}, "notification": {"title": "Notification", "description": "Display notifications to users about the status of actions or important information.", "basic": {"title": "Basic Notification", "description": "Basic notification types with different styles."}, "hook": {"title": "Using useNotification Hook", "description": "Manage notifications with the useNotification hook."}, "hideCode": "Hide code", "showCode": "Show code"}, "banner": {"title": "Banner", "description": "Banner component displays highlighted content with various options.", "basic": {"title": "Basic Banner", "description": "Basic banner with title and description."}, "withBackground": {"title": "Banner with Background", "description": "Banner with background image and overlay."}, "gradient": {"title": "Banner with <PERSON><PERSON><PERSON>", "description": "Banner with gradient background and action buttons."}, "wave": {"title": "Banner with Wave Effect", "description": "Banner with wave effect at the bottom."}, "custom": {"title": "Banner with Custom Content", "description": "Banner with custom content instead of using title and description.", "exploreButton": "Explore Now"}, "borderRadius": {"title": "Banner with Border Radius", "description": "Banner with different border radius options.", "topCorners": {"title": "Banner with Top Corners Radius", "description": "Using borderRadius='rounded-t-xl' property"}, "allCorners": {"title": "Banner with All Corners Radius", "description": "Using borderRadius='rounded-xl' property"}, "bottomCorners": {"title": "Banner with Bottom Corners Radius", "description": "Using borderRadius='rounded-b-xl' property"}}}, "categories": {"buttons": {"title": "Buttons", "description": "Different types of buttons: primary, secondary, outline, icon buttons..."}, "cards": {"title": "Cards", "description": "Various card types for displaying content, information, data..."}, "chips": {"title": "Chips", "description": "Chips are compact elements that represent an input, attribute, or action..."}, "inputs": {"title": "Inputs", "description": "Various input types: text, number, checkbox, radio, select..."}, "layout": {"title": "Layout Components", "description": "Layout components: container, grid, flex, resizer..."}, "theme": {"title": "Theme Components", "description": "Theme-related components: theme toggle, language selector...", "system": {"title": "Theme System", "description": "New theme system with customization and extension capabilities"}}, "form": {"title": "Form Components", "description": "Form components: input, select, checkbox, radio...", "theme": {"title": "Form with Theme System", "description": "Demo of form components using the new theme system"}}, "typography": {"title": "Typography", "description": "Text formatting components for consistent display in applications."}}, "grid": {"title": "Grid", "description": "Grid component helps create flexible and responsive grid layouts."}, "responsiveGrid": {"title": "Responsive Grid", "description": "Advanced responsive grid that automatically adjusts based on screen size and chat panel state."}, "menu": {"title": "<PERSON><PERSON>", "description": "Menu with multiple features: submenu, different modes, collapsed state"}, "tooltip": {"title": "<PERSON><PERSON><PERSON>", "description": "Tooltip displays additional information when hovering over an element"}, "searchBar": {"title": "Search Bar", "description": "Search bar with animation effects and different styles"}, "modernMenu": {"title": "Modern Menu", "description": "Modern menu with various styles and positions"}, "cards": {"title": "Cards", "description": "Various card types for displaying content, information, and data in the RedAI system."}, "avatar": {"title": "Avatar", "description": "Avatar component displays user profile images."}, "imageGallery": {"title": "Image Gallery", "description": "Component for displaying image collections with various options."}, "topCard": {"title": "Top Card", "description": "Component for displaying overview information in card format."}, "simpleChart": {"title": "Simple Chart", "description": "Simple chart using the Recharts library directly"}, "moduleGallery": {"title": "Module Gallery", "description": "Explore and access modules in the system", "search": {"placeholder": "Search modules...", "noResults": "No modules found", "noResultsDescription": "Try changing your search keywords or filters"}, "filters": {"category": "Category", "size": "Size", "all": "All"}, "categories": {"all": "All", "business": "Business", "marketing": "Marketing", "data": "Data", "tools": "Tools", "rpoint": "R-Point"}, "sizes": {"sm": "Small", "md": "Medium", "lg": "Large"}, "stats": {"showing": "Showing {{count}} / {{total}} modules", "sizeLabel": "Size: {{size}}"}}}}