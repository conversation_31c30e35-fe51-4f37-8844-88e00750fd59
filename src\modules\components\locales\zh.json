{"components": {"library": {"title": "组件库", "description": "RedAI前端模板中可用的组件。"}, "form": {"wizard": {"title": "表单向导", "description": "FormWizard组件管理多步骤表单，支持验证和导航。", "basic": {"title": "基本表单向导", "description": "带有3个步骤和验证的基本表单向导。"}}}, "charts": {"demo": {"title": "图表演示", "description": "具有响应式设计、多语言支持和主题兼容性的图表组件。"}, "lineChart": {"title": "折线图", "description": "折线图组件以线条形式显示数据，支持多条数据线、工具提示和图例。", "basic": {"title": "基本折线图", "description": "带有单条数据线的基本折线图。"}, "multiLine": {"title": "多线折线图", "description": "带有多条数据线的折线图。"}, "customized": {"title": "自定义折线图", "description": "带有自定义线型、线宽和点显示的折线图。"}}}, "animation": {"title": "动画效果", "description": "RedAI前端模板中可用的动态动画效果。"}, "notification": {"title": "通知", "description": "向用户显示有关操作状态或重要信息的通知。", "basic": {"title": "基本通知", "description": "具有不同样式的基本通知类型。"}, "hook": {"title": "使用 useNotification Hook", "description": "使用 useNotification hook 管理通知。"}, "hideCode": "隐藏代码", "showCode": "显示代码"}, "banner": {"title": "横幅", "description": "横幅组件用于显示具有各种选项的突出内容。", "basic": {"title": "基本横幅", "description": "带有标题和描述的基本横幅。"}, "withBackground": {"title": "带背景的横幅", "description": "带有背景图片和覆盖层的横幅。"}, "gradient": {"title": "带渐变的横幅", "description": "带有渐变背景和操作按钮的横幅。"}, "wave": {"title": "带波浪效果的横幅", "description": "底部带有波浪效果的横幅。"}, "custom": {"title": "带自定义内容的横幅", "description": "使用自定义内容而不是标题和描述的横幅。", "exploreButton": "立即探索"}, "borderRadius": {"title": "带圆角的横幅", "description": "具有不同圆角选项的横幅。", "topCorners": {"title": "顶部圆角横幅", "description": "使用 borderRadius='rounded-t-xl' 属性"}, "allCorners": {"title": "全圆角横幅", "description": "使用 borderRadius='rounded-xl' 属性"}, "bottomCorners": {"title": "底部圆角横幅", "description": "使用 borderRadius='rounded-b-xl' 属性"}}}, "categories": {"buttons": {"title": "按钮", "description": "不同类型的按钮：主要、次要、轮廓、图标按钮..."}, "cards": {"title": "卡片", "description": "用于显示内容、信息、数据的各种卡片类型..."}, "chips": {"title": "标签", "description": "标签是表示输入、属性或操作的紧凑元素..."}, "inputs": {"title": "输入框", "description": "各种输入类型：文本、数字、复选框、单选按钮、选择框..."}, "layout": {"title": "布局组件", "description": "布局组件：容器、网格、弹性布局、调整器..."}, "theme": {"title": "主题组件", "description": "与主题相关的组件：主题切换、语言选择器...", "system": {"title": "主题系统", "description": "具有自定义和扩展功能的新主题系统"}}, "form": {"title": "表单组件", "description": "表单组件：输入框、选择框、复选框、单选按钮...", "theme": {"title": "使用主题系统的表单", "description": "使用新主题系统的表单组件演示"}}, "typography": {"title": "排版", "description": "用于在应用程序中一致显示的文本格式化组件。"}}, "grid": {"title": "网格", "description": "网格组件帮助创建灵活和响应式的网格布局。"}, "responsiveGrid": {"title": "响应式网格", "description": "高级响应式网格，根据屏幕大小和聊天面板状态自动调整。"}, "menu": {"title": "菜单", "description": "具有多种功能的菜单：子菜单、不同模式、折叠状态"}, "tooltip": {"title": "工具提示", "description": "当悬停在元素上时显示附加信息的工具提示"}, "searchBar": {"title": "搜索栏", "description": "带有动画效果和不同样式的搜索栏"}, "modernMenu": {"title": "现代菜单", "description": "具有各种样式和位置的现代菜单"}, "cards": {"title": "卡片", "description": "在RedAI系统中用于显示内容、信息和数据的各种卡片类型。"}, "avatar": {"title": "头像", "description": "头像组件显示用户个人资料图片。"}, "imageGallery": {"title": "图片库", "description": "用于显示具有各种选项的图片集合的组件。"}, "topCard": {"title": "顶部卡片", "description": "以卡片格式显示概览信息的组件。"}, "simpleChart": {"title": "简单图表", "description": "直接使用Recharts库的简单图表"}, "moduleGallery": {"title": "模块画廊", "description": "探索和访问系统中的模块", "search": {"placeholder": "搜索模块...", "noResults": "未找到模块", "noResultsDescription": "尝试更改搜索关键词或筛选器"}, "filters": {"category": "类别", "size": "大小", "all": "全部"}, "categories": {"all": "全部", "business": "业务", "marketing": "营销", "data": "数据", "tools": "工具", "rpoint": "R积分"}, "sizes": {"sm": "小", "md": "中", "lg": "大"}, "stats": {"showing": "显示 {{count}} / {{total}} 个模块", "sizeLabel": "大小：{{size}}"}}}}