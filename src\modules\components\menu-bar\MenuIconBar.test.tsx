import React from 'react';
import { render, screen } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/shared/i18n';
import MenuIconBar from './MenuIconBar';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu/ModernMenu';

// Mock components
jest.mock('@/shared/components/common', () => ({
  IconCard: ({ icon, onClick }: { icon: string; onClick: () => void }) => (
    <button onClick={onClick} data-testid={`icon-${icon}`}>
      {icon}
    </button>
  ),
  Tooltip: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SearchBar: ({ visible }: { visible: boolean }) => (
    visible ? <input data-testid="search-bar" /> : null
  ),
  Checkbox: ({ checked }: { checked: boolean }) => (
    <input type="checkbox" checked={checked} readOnly />
  ),
  ModernMenuTrigger: ({ items }: { items: ModernMenuItem[] }) => (
    <div data-testid="modern-menu-trigger">
      {items.length} items
    </div>
  ),
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <I18nextProvider i18n={i18n}>
    {children}
  </I18nextProvider>
);

describe('MenuIconBar', () => {
  const mockOnSearch = jest.fn();
  const mockOnAdd = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders search icon', () => {
    render(
      <TestWrapper>
        <MenuIconBar onSearch={mockOnSearch} />
      </TestWrapper>
    );

    expect(screen.getByTestId('icon-search')).toBeInTheDocument();
  });

  it('renders add button when onAdd is provided', () => {
    render(
      <TestWrapper>
        <MenuIconBar onSearch={mockOnSearch} onAdd={mockOnAdd} />
      </TestWrapper>
    );

    expect(screen.getByTestId('icon-plus')).toBeInTheDocument();
  });

  it('renders filter menu when items are provided', () => {
    const items: ModernMenuItem[] = [
      {
        id: 'all',
        label: 'All',
        icon: 'list',
        onClick: jest.fn(),
      },
      {
        id: 'active',
        label: 'Active',
        icon: 'check',
        onClick: jest.fn(),
      },
    ];

    render(
      <TestWrapper>
        <MenuIconBar onSearch={mockOnSearch} items={items} />
      </TestWrapper>
    );

    expect(screen.getByTestId('modern-menu-trigger')).toBeInTheDocument();
    expect(screen.getByText('2 items')).toBeInTheDocument();
  });

  it('does not render filter menu when no items provided', () => {
    render(
      <TestWrapper>
        <MenuIconBar onSearch={mockOnSearch} />
      </TestWrapper>
    );

    expect(screen.queryByTestId('modern-menu-trigger')).not.toBeInTheDocument();
  });

  it('renders column filter when showColumnFilter is true and columns provided', () => {
    const columns = [
      { id: 'name', label: 'Name', visible: true },
      { id: 'status', label: 'Status', visible: true },
    ];

    render(
      <TestWrapper>
        <MenuIconBar 
          onSearch={mockOnSearch} 
          columns={columns}
          showColumnFilter={true}
        />
      </TestWrapper>
    );

    expect(screen.getByTestId('icon-filter-v2')).toBeInTheDocument();
  });
});
