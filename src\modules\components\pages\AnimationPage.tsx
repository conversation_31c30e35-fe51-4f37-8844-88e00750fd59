import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Tooltip, Modal, Notification } from '@/shared/components/common';

const AnimationPage = () => {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [notificationType, setNotificationType] = useState<
    'info' | 'success' | 'warning' | 'error'
  >('info');

  // Show notification with specified type
  const handleShowNotification = (type: 'info' | 'success' | 'warning' | 'error') => {
    setNotificationType(type);
    setShowNotification(true);
  };

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.animation.title', 'Animation')}
        </h1>
        <p className="text-muted">
          {t(
            'components.animation.description',
            'Various animations available in the RedAI Frontend Template'
          )}
        </p>
      </div>

      <Card
        title={t('components.animation.fadeSlide.title', 'Fade & Slide Animations')}
        className="mb-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold mb-2">{t('components.animation.fadeIn', 'Fade In')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <div className="animate-fade-in bg-primary text-white p-4 rounded">
                {t('components.animation.fadeInAnimation', 'Fade In Animation')}
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('components.animation.slideIn', 'Slide In')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <div className="animate-slide-in bg-secondary text-white p-4 rounded">
                {t('components.animation.slideInAnimation', 'Slide In Animation')}
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">
              {t('components.animation.slideInLeft', 'Slide In Left')}
            </h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <div className="animate-slide-in-left bg-blue-500 text-white p-4 rounded">
                {t('components.animation.slideInLeftAnimation', 'Slide In Left Animation')}
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">
              {t('components.animation.slideInRight', 'Slide In Right')}
            </h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <div className="animate-slide-in-right bg-green-500 text-white p-4 rounded">
                {t('components.animation.slideInRightAnimation', 'Slide In Right Animation')}
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Card
        title={t('components.animation.durationTiming.title', 'Duration & Timing')}
        className="mb-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="font-semibold mb-2">{t('components.animation.fast', 'Fast (200ms)')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <div className="animate-fade-in duration-200 bg-primary text-white p-4 rounded">
                {t('components.animation.fastAnimation', 'Fast Animation')}
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">
              {t('components.animation.medium', 'Medium (500ms)')}
            </h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <div className="animate-fade-in duration-500 bg-primary text-white p-4 rounded">
                {t('components.animation.mediumAnimation', 'Medium Animation')}
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">
              {t('components.animation.slow', 'Slow (1000ms)')}
            </h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <div className="animate-fade-in duration-1000 bg-primary text-white p-4 rounded">
                {t('components.animation.slowAnimation', 'Slow Animation')}
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Card
        title={t('components.animation.continuous.title', 'Continuous Animations')}
        className="mb-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold mb-2">{t('components.animation.pulse', 'Pulse')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <div className="animate-pulse bg-primary text-white p-4 rounded">
                {t('components.animation.pulseAnimation', 'Pulse Animation')}
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('components.animation.spin', 'Spin')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <div className="animate-spin h-12 w-12 border-4 border-primary border-t-transparent rounded-full"></div>
            </div>
          </div>
        </div>
      </Card>

      <Card
        title={t('components.animation.component.title', 'Component Animations')}
        className="mb-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="font-semibold mb-2">{t('components.tooltip.title', 'Tooltip')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Tooltip
                content={t(
                  'components.animation.tooltipContent',
                  'This is a tooltip with animation'
                )}
              >
                <Button variant="primary">{t('components.animation.hoverMe', 'Hover Me')}</Button>
              </Tooltip>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('components.modal.title', 'Modal')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Button variant="primary" onClick={() => setIsModalOpen(true)}>
                {t('components.animation.openModal', 'Open Modal')}
              </Button>
              <Modal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={t('components.animation.animatedModal', 'Animated Modal')}
              >
                <p>
                  {t(
                    'components.animation.modalDescription',
                    'This modal has entrance and exit animations.'
                  )}
                </p>
              </Modal>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">
              {t('components.notification.title', 'Notification')}
            </h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex flex-col gap-2">
              <Button variant="primary" onClick={() => handleShowNotification('info')}>
                {t('components.animation.infoNotification', 'Info Notification')}
              </Button>
              <Button variant="success" onClick={() => handleShowNotification('success')}>
                {t('components.animation.successNotification', 'Success Notification')}
              </Button>
              <Button variant="warning" onClick={() => handleShowNotification('warning')}>
                {t('components.animation.warningNotification', 'Warning Notification')}
              </Button>
              <Button variant="danger" onClick={() => handleShowNotification('error')}>
                {t('components.animation.errorNotification', 'Error Notification')}
              </Button>
              {showNotification && (
                <Notification
                  onClose={() => setShowNotification(false)}
                  type={notificationType}
                  title={`${notificationType.charAt(0).toUpperCase() + notificationType.slice(1)} Notification`}
                  message={t(
                    'components.animation.notificationDescription',
                    'This notification has entrance and exit animations.'
                  )}
                  duration={3000}
                />
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AnimationPage;
