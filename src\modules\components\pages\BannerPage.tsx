import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Banner, Typography } from '@/shared/components/common';
import ComponentDemo from '../components/ComponentDemo';

const BannerPage: React.FC = () => {
  const { t } = useTranslation(['components']);
  const navigate = useNavigate();

  // Hàm xử lý khi click vào nút
  const handlePrimaryAction = () => {
    navigate('/components');
  };

  const handleSecondaryAction = () => {
    navigate('/components/buttons');
  };

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-semibold mb-2 text-foreground">
          {t('components.banner.title', 'Banner')}
        </h1>
        <p className="text-muted">
          {t(
            'components.banner.description',
            'Component Banner hiển thị nội dung nổi bật với nhiều tùy chọn.'
          )}
        </p>
      </div>

      {/* Banner cơ bản */}
      <ComponentDemo
        title={t('components.banner.basic.title', 'Banner cơ bản')}
        description={t(
          'components.banner.basic.description',
          'Banner cơ bản với tiêu đề và mô tả.'
        )}
        code={`<Banner
  title="Chào mừng đến với RedAI"
  description="Nền tảng AI hiện đại cho doanh nghiệp của bạn"
  variant="primary"
  size="md"
/>`}
      >
        <div className="w-full">
          <Banner
            title="Chào mừng đến với RedAI"
            description="Nền tảng AI hiện đại cho doanh nghiệp của bạn"
            variant="primary"
            size="md"
          />
        </div>
      </ComponentDemo>

      {/* Banner với hình nền */}
      <ComponentDemo
        title={t('components.banner.withBackground.title', 'Banner với hình nền')}
        description={t(
          'components.banner.withBackground.description',
          'Banner với hình nền và overlay.'
        )}
        code={`<Banner
  title="Khám phá tính năng mới"
  description="Trải nghiệm sức mạnh của AI trong việc tạo nội dung"
  backgroundImage="https://images.unsplash.com/photo-1620641788421-7a1c342ea42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
  overlayOpacity={60}
  size="lg"
/>`}
      >
        <div className="w-full">
          <Banner
            title="Khám phá tính năng mới"
            description="Trải nghiệm sức mạnh của AI trong việc tạo nội dung"
            backgroundImage="https://images.unsplash.com/photo-1620641788421-7a1c342ea42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
            overlayOpacity={60}
            size="lg"
          />
        </div>
      </ComponentDemo>

      {/* Banner với gradient */}
      <ComponentDemo
        title={t('components.banner.gradient.title', 'Banner với gradient')}
        description={t(
          'components.banner.gradient.description',
          'Banner với nền gradient và các nút hành động.'
        )}
        code={`<Banner
  title="Bắt đầu hành trình AI của bạn"
  description="Khám phá các công cụ AI mạnh mẽ được thiết kế cho doanh nghiệp"
  variant="gradient"
  size="md"
  primaryAction={{
    label: "Bắt đầu ngay",
    onClick: () => navigate('/components')
  }}
  secondaryAction={{
    label: "Tìm hiểu thêm",
    onClick: () => navigate('/components/buttons'),
    variant: "outline"
  }}
/>`}
      >
        <div className="w-full">
          <Banner
            title="Bắt đầu hành trình AI của bạn"
            description="Khám phá các công cụ AI mạnh mẽ được thiết kế cho doanh nghiệp"
            variant="gradient"
            size="md"
            primaryAction={{
              label: 'Bắt đầu ngay',
              onClick: handlePrimaryAction,
            }}
            secondaryAction={{
              label: 'Tìm hiểu thêm',
              onClick: handleSecondaryAction,
              variant: 'outline',
            }}
          />
        </div>
      </ComponentDemo>

      {/* Banner với hiệu ứng sóng */}
      <ComponentDemo
        title={t('components.banner.wave.title', 'Banner với hiệu ứng sóng')}
        description={t(
          'components.banner.wave.description',
          'Banner với hiệu ứng sóng ở dưới cùng.'
        )}
        code={`<Banner
  title="Tạo trải nghiệm người dùng tuyệt vời"
  description="Sử dụng các component hiện đại để xây dựng giao diện người dùng ấn tượng"
  variant="secondary"
  size="lg"
  showWave={true}
/>`}
      >
        <div className="w-full">
          <Banner
            title="Tạo trải nghiệm người dùng tuyệt vời"
            description="Sử dụng các component hiện đại để xây dựng giao diện người dùng ấn tượng"
            variant="secondary"
            size="lg"
            showWave={true}
          />
        </div>
      </ComponentDemo>

      {/* Banner với border radius */}
      <ComponentDemo
        title={t('components.banner.borderRadius.title', 'Banner với border radius')}
        description={t(
          'components.banner.borderRadius.description',
          'Banner với các tùy chọn border radius khác nhau.'
        )}
        code={`// Banner với border radius ở 2 góc trên
<Banner
  title="Banner với border radius ở 2 góc trên"
  description="Sử dụng thuộc tính borderRadius='rounded-t-xl'"
  variant="primary"
  size="md"
  borderRadius="rounded-t-xl"
/>

// Banner với border radius ở tất cả các góc
<Banner
  title="Banner với border radius ở tất cả các góc"
  description="Sử dụng thuộc tính borderRadius='rounded-xl'"
  variant="secondary"
  size="md"
  borderRadius="rounded-xl"
/>

// Banner với border radius ở 2 góc dưới
<Banner
  title="Banner với border radius ở 2 góc dưới"
  description="Sử dụng thuộc tính borderRadius='rounded-b-xl'"
  backgroundImage="https://images.unsplash.com/photo-1620641788421-7a1c342ea42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
  overlayOpacity={60}
  size="md"
  borderRadius="rounded-b-xl"
/>`}
      >
        <div className="w-full space-y-4 sm:space-y-6 md:space-y-8">
          <Banner
            title={t(
              'components.banner.borderRadius.topCorners.title',
              'Banner với border radius ở 2 góc trên'
            )}
            description={t(
              'components.banner.borderRadius.topCorners.description',
              "Sử dụng thuộc tính borderRadius='rounded-t-xl'"
            )}
            variant="primary"
            size="md"
            borderRadius="rounded-t-xl"
          />

          <Banner
            title={t(
              'components.banner.borderRadius.allCorners.title',
              'Banner với border radius ở tất cả các góc'
            )}
            description={t(
              'components.banner.borderRadius.allCorners.description',
              "Sử dụng thuộc tính borderRadius='rounded-xl'"
            )}
            variant="secondary"
            size="md"
            borderRadius="rounded-xl"
          />

          <Banner
            title={t(
              'components.banner.borderRadius.bottomCorners.title',
              'Banner với border radius ở 2 góc dưới'
            )}
            description={t(
              'components.banner.borderRadius.bottomCorners.description',
              "Sử dụng thuộc tính borderRadius='rounded-b-xl'"
            )}
            backgroundImage="https://images.unsplash.com/photo-1620641788421-7a1c342ea42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
            overlayOpacity={60}
            size="md"
            borderRadius="rounded-b-xl"
          />
        </div>
      </ComponentDemo>

      {/* Banner với nội dung tùy chỉnh */}
      <ComponentDemo
        title={t('components.banner.custom.title', 'Banner với nội dung tùy chỉnh')}
        description={t(
          'components.banner.custom.description',
          'Banner với nội dung tùy chỉnh thay vì sử dụng title và description.'
        )}
        code={`<Banner
  variant="primary"
  size="md"
  alignment="left"
>
  <div className="max-w-2xl">
    <Typography variant="h2" className="mb-4">
      Thiết kế <span className="text-secondary">hiện đại</span>
    </Typography>
    <Typography variant="body1" className="mb-6">
      Sử dụng các component được thiết kế tỉ mỉ để tạo ra trải nghiệm người dùng tuyệt vời.
      Hỗ trợ đầy đủ các tính năng responsive và theme light/dark.
    </Typography>
    <div className="flex gap-4">
      <Button variant="secondary">
        {t('components.banner.custom.exploreButton', 'Khám phá ngay')}
      </Button>
    </div>
  </div>
</Banner>`}
      >
        <div className="w-full">
          <Banner variant="primary" size="md" alignment="left">
            <div className="max-w-2xl">
              <Typography variant="h2" className="mb-4">
                Thiết kế <span className="text-secondary">hiện đại</span>
              </Typography>
              <Typography variant="body1" className="mb-6">
                Sử dụng các component được thiết kế tỉ mỉ để tạo ra trải nghiệm người dùng tuyệt
                vời. Hỗ trợ đầy đủ các tính năng responsive và theme light/dark.
              </Typography>
              <div className="flex gap-4">
                <button className="btn btn-secondary">
                  {t('components.banner.custom.exploreButton', 'Khám phá ngay')}
                </button>
              </div>
            </div>
          </Banner>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default BannerPage;
