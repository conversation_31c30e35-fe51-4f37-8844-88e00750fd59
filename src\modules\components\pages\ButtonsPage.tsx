import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ComponentDemo } from '../components';
import { Button, Icon } from '@/shared/components/common';

const ButtonsPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.buttons.title')}
        </h1>
        <p className="text-muted">{t('components.buttons.description')}</p>
      </div>

      <ComponentDemo
        title={t('components.buttons.variants.title')}
        description={t('components.buttons.variants.description')}
        code={`import { Button } from '@/shared/components/common';

// Primary Button
<Button variant="primary">{t('common.primary')}</Button>

// Secondary Button
<Button variant="secondary">{t('common.secondary')}</Button>

// Outline Button
<Button variant="outline">{t('common.outline')}</Button>

// Success Button
<Button variant="success">{t('common.success')}</Button>

// Warning Button
<Button variant="warning">{t('common.warning')}</Button>

// Danger Button
<Button variant="danger">{t('common.danger')}</Button>`}
      >
        <div className="flex flex-wrap gap-4">
          <Button variant="primary">{t('common.primary')}</Button>
          <Button variant="secondary">{t('common.secondary')}</Button>
          <Button variant="outline">{t('common.outline')}</Button>
          <Button variant="success">{t('common.success')}</Button>
          <Button variant="warning">{t('common.warning')}</Button>
          <Button variant="danger">{t('common.danger')}</Button>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.buttons.sizes.title')}
        description={t('components.buttons.sizes.description')}
        code={`import { Button } from '@/shared/components/common';

// Small Button
<Button size="sm">{t('common.small')}</Button>

// Medium Button (default)
<Button size="md">{t('common.medium')}</Button>

// Large Button
<Button size="lg">{t('common.large')}</Button>`}
      >
        <div className="flex items-center gap-4">
          <Button size="sm">{t('common.small')}</Button>
          <Button size="md">{t('common.medium')}</Button>
          <Button size="lg">{t('common.large')}</Button>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.buttons.withIcons.title')}
        description={t('components.buttons.withIcons.description')}
        code={`import { Button, Icon } from '@/shared/components/common';

// Button with left icon
<Button leftIcon={<Icon name="plus" />}>
  {t('common.createNew')}
</Button>

// Button with right icon
<Button rightIcon={<Icon name="chevron-right" />}>
  {t('common.nextStep')}
</Button>`}
      >
        <div className="flex flex-wrap gap-4">
          <Button leftIcon={<Icon name="plus" />}>{t('common.createNew')}</Button>
          <Button rightIcon={<Icon name="chevron-right" />}>{t('common.nextStep')}</Button>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.buttons.fullWidth.title')}
        description={t('components.buttons.fullWidth.description')}
        code={`import { Button } from '@/shared/components/common';

// Full width button
<Button fullWidth>{t('components.buttons.fullWidth.button')}</Button>`}
      >
        <div className="w-full max-w-md">
          <Button fullWidth>{t('components.buttons.fullWidth.button')}</Button>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.buttons.loading.title')}
        description={t('components.buttons.loading.description')}
        code={`import { Button } from '@/shared/components/common';

// Loading button
<Button isLoading>{t('common.loading')}</Button>`}
      >
        <div className="flex flex-col gap-4">
          <Button isLoading>{t('common.loading')}</Button>
          <div className="mt-2">
            <Link to="/components/buttons/loading" className="text-primary hover:underline">
              {t('components.buttons.loading.viewMore', 'View more loading button examples')} →
            </Link>
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.buttons.disabled.title')}
        description={t('components.buttons.disabled.description')}
        code={`import { Button } from '@/shared/components/common';

// Disabled button
<Button disabled>{t('common.disabled')}</Button>`}
      >
        <Button disabled>{t('common.disabled')}</Button>
      </ComponentDemo>
    </div>
  );
};

export default ButtonsPage;
