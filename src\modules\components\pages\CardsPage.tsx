import React from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import { Card, Button, Icon } from '@/shared/components/common';

const CardsPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.categories.cards.title')}
        </h1>
        <p className="text-muted">{t('components.categories.cards.description')}</p>
      </div>

      <ComponentDemo
        title={t('components.cards.basic.title')}
        description={t('components.cards.basic.description')}
        code={`import { Card } from '@/shared/components/common';

<Card title={t('common.ui.card.title')}>
  <p>{t('common.ui.card.content')}</p>
</Card>`}
      >
        <div className="w-full max-w-md">
          <Card title={t('common.ui.card.title')}>
            <p>{t('common.ui.card.content')}</p>
          </Card>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.cards.withFooter.title')}
        description={t('components.cards.withFooter.description')}
        code={`import { Card, Button } from '@/shared/components/common';

<Card
  title={t('components.cards.withFooter.title')}
  footer={
    <div className="flex justify-end space-x-2">
      <Button variant="outline" size="sm">{t('common.cancel')}</Button>
      <Button size="sm">{t('common.save')}</Button>
    </div>
  }
>
  <p>{t('common.ui.card.withFooter.content')}</p>
</Card>`}
      >
        <div className="w-full max-w-md">
          <Card
            title={t('components.cards.withFooter.title')}
            footer={
              <div className="flex justify-end space-x-2">
                <Button variant="outline" size="sm">
                  {t('common.cancel')}
                </Button>
                <Button size="sm">{t('common.save')}</Button>
              </div>
            }
          >
            <p>{t('common.ui.card.withFooter.content')}</p>
          </Card>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.cards.withIcon.title')}
        description={t('components.cards.withIcon.description')}
        code={`import { Card, Icon } from '@/shared/components/common';

<Card
  title={t('components.cards.withIcon.title')}
  icon={<Icon name="star" />}
>
  <p>{t('common.ui.card.withIcon.content')}</p>
</Card>`}
      >
        <div className="w-full max-w-md">
          <Card
            title={t('components.cards.withIcon.title')}
            icon={<Icon name="star" className="text-yellow-500" />}
          >
            <p>{t('common.ui.card.withIcon.content')}</p>
          </Card>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.cards.bordered.title')}
        description={t('components.cards.bordered.description')}
        code={`import { Card } from '@/shared/components/common';

<Card
  title={t('components.cards.bordered.title')}
  variant="elevated"
>
  <p>{t('common.ui.card.bordered.content')}</p>
</Card>`}
      >
        <div className="w-full max-w-md">
          <Card title={t('components.cards.bordered.title')} variant="elevated">
            <p>{t('common.ui.card.bordered.content')}</p>
          </Card>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.cards.customHeader.title')}
        description={t('components.cards.customHeader.description')}
        code={`import { Card, Button } from '@/shared/components/common';

<Card
  customHeader={
    <div className="flex justify-between items-center p-4 bg-primary-50 dark:bg-primary-900/20">
      <h3 className="font-medium">{t('components.cards.customHeader.title')}</h3>
      <Button size="sm" variant="outline">{t('common.action')}</Button>
    </div>
  }
>
  <p>{t('common.ui.card.customHeader.content')}</p>
</Card>`}
      >
        <div className="w-full max-w-md">
          <Card
            customHeader={
              <div className="flex justify-between items-center p-4 bg-primary-50 dark:bg-primary-900/20">
                <h3 className="font-medium">{t('components.cards.customHeader.title')}</h3>
                <Button size="sm" variant="outline">
                  {t('common.action')}
                </Button>
              </div>
            }
          >
            <p>{t('common.ui.card.customHeader.content')}</p>
          </Card>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default CardsPage;
