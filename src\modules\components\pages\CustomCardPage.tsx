import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Card, Typography, Grid, Button } from '@/shared/components/common';
import { ProductInfoCard } from '@/modules/data';
import { fetchProducts } from '@/modules/data/api/productMockData';
import { ProductInfo } from '@/modules/data/types/product';

const CustomCardPage = () => {
  const { t } = useTranslation();
  const [products, setProducts] = useState<ProductInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedProduct, setSelectedProduct] = useState<ProductInfo | null>(null);

  // Fetch products data
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setIsLoading(true);
        const response = await fetchProducts(1, 6);
        setProducts(response.items);
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProducts();
  }, []);

  // Handle product click
  const handleProductClick = (product: ProductInfo) => {
    setSelectedProduct(product);
  };

  return (
    <Container>
      <Card className="mb-6">
        <Typography variant="h4" className="mb-2">
          Product Info Card
        </Typography>
        <Typography variant="body1" className="mb-4">
          {t(
            'data.product.description',
            'Component hiển thị thông tin sản phẩm với logo, tên, cấp độ và điểm số.'
          )}
        </Typography>
      </Card>

      {/* Basic example */}
      <Card className="mb-6">
        <Typography variant="h5" className="mb-4">
          {t('data.product.basicExample', 'Ví dụ cơ bản')}
        </Typography>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : (
          <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="md">
            {products.slice(0, 2).map(product => (
              <ProductInfoCard
                key={product.id}
                product={product}
                onClick={handleProductClick}
                hoverable
              />
            ))}
          </Grid>
        )}
      </Card>

      {/* Size variants */}
      <Card className="mb-6">
        <Typography variant="h5" className="mb-4">
          {t('data.product.sizeVariants', 'Các kích thước khác nhau')}
        </Typography>

        <div className="space-y-4">
          <div>
            <Typography variant="h6" className="mb-2">
              {t('common.small', 'Nhỏ')}
            </Typography>
            <ProductInfoCard
              product={
                products[0] || {
                  id: 'sample-1',
                  name: 'GetProductInfo - Tư vấn sản phẩm chi tiết',
                  description: 'Mô tả sản phẩm',
                  level: 1,
                  points: 1248,
                  status: 'active',
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                }
              }
              size="sm"
            />
          </div>

          <div>
            <Typography variant="h6" className="mb-2">
              {t('common.medium', 'Vừa')}
            </Typography>
            <ProductInfoCard
              product={
                products[1] || {
                  id: 'sample-2',
                  name: 'DataAnalyzer - Phân tích dữ liệu thông minh',
                  description: 'Mô tả sản phẩm',
                  level: 2,
                  points: 1875,
                  status: 'active',
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                }
              }
              size="md"
            />
          </div>

          <div>
            <Typography variant="h6" className="mb-2">
              {t('common.large', 'Lớn')}
            </Typography>
            <ProductInfoCard
              product={
                products[2] || {
                  id: 'sample-3',
                  name: 'CloudStorage - Lưu trữ đám mây an toàn',
                  description: 'Mô tả sản phẩm',
                  level: 3,
                  points: 2340,
                  status: 'active',
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                }
              }
              size="lg"
            />
          </div>
        </div>
      </Card>

      {/* Grid layout */}
      <Card className="mb-6">
        <Typography variant="h5" className="mb-4">
          {t('data.product.gridLayout', 'Bố cục lưới')}
        </Typography>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : (
          <Grid columns={{ xs: 1, sm: 2, md: 3 }} columnGap="md" rowGap="md">
            {products.slice(0, 6).map(product => (
              <ProductInfoCard
                key={product.id}
                product={product}
                onClick={handleProductClick}
                hoverable
                size="sm"
              />
            ))}
          </Grid>
        )}
      </Card>

      {/* Selected product details */}
      {selectedProduct && (
        <Card className="mb-6">
          <Typography variant="h5" className="mb-4">
            {t('data.product.selectedProduct', 'Sản phẩm đã chọn')}
          </Typography>

          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
            <Typography variant="h6" className="mb-2">
              {selectedProduct.name}
            </Typography>
            <Typography variant="body2" className="mb-2">
              {selectedProduct.description}
            </Typography>
            <div className="flex flex-wrap gap-2">
              <div className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 px-2 py-1 rounded text-sm">
                {t('data.product.level', 'Cấp {{level}}', { level: selectedProduct.level })}
              </div>
              <div className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 px-2 py-1 rounded text-sm">
                {t('data.product.points', 'Điểm: {{points}}', { points: selectedProduct.points })}
              </div>
              <div
                className={`px-2 py-1 rounded text-sm ${
                  selectedProduct.status === 'active'
                    ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100'
                    : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-100'
                }`}
              >
                {selectedProduct.status === 'active'
                  ? t('common.active', 'Hoạt động')
                  : t('common.inactive', 'Không hoạt động')}
              </div>
            </div>
          </div>

          <div className="mt-4">
            <Button variant="outline" onClick={() => setSelectedProduct(null)}>
              {t('common.close', 'Đóng')}
            </Button>
          </div>
        </Card>
      )}

      {/* Usage example */}
      <Card>
        <Typography variant="h5" className="mb-4">
          {t('data.product.usage', 'Cách sử dụng')}
        </Typography>

        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-x-auto">
          <pre className="text-sm">
            {`import { ProductInfoCard } from '@/modules/data';
import { ProductInfo } from '@/modules/data/types/product';

// Basic usage
const MyComponent = () => {
  const product: ProductInfo = {
    id: 'product-1',
    name: 'GetProductInfo - Tư vấn sản phẩm chi tiết',
    description: 'Mô tả sản phẩm',
    level: 1,
    points: 1248,
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const handleProductClick = (product: ProductInfo) => {
    console.log('Product clicked:', product);
  };

  return (
    <ProductInfoCard
      product={product}
      size="md"
      bordered
      hoverable
      onClick={handleProductClick}
    />
  );
};`}
          </pre>
        </div>
      </Card>
    </Container>
  );
};

export default CustomCardPage;
