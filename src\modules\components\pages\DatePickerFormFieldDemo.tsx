/**
 * Demo trang cho DatePickerFormField component
 */
import React, { useRef } from 'react';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import { 
  DatePickerFormField, 
  Typography, 
  Form, 
  FormItem, 
  Button,
  Card
} from '@/shared/components/common';
import { ValidationSchemas } from '@/shared/validation/schemas';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FieldValues } from 'react-hook-form';

const DatePickerFormFieldDemo: React.FC = () => {
  const { t } = useTranslation(['common', 'components']);
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Schema validation với flexibleDate
  const schema = z.object({
    birthDate: ValidationSchemas.flexibleDate({ t })
      .refine((date) => {
        const birthDate = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        return age >= 18 && age <= 100;
      }, 'Tuổi phải từ 18 đến 100'),
    
    startDate: ValidationSchemas.flexibleDate({ t }),
    
    endDate: ValidationSchemas.flexibleDate({ t }).optional(),
    
    appointmentDate: ValidationSchemas.flexibleDate({ t })
      .refine((date) => {
        const appointmentDate = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        return appointmentDate >= today;
      }, 'Ngày hẹn phải từ hôm nay trở đi'),
  });

  const handleSubmit = (data: FieldValues) => {
    console.log('Form submitted with data:', data);
    alert(`Form submitted successfully!\n\nData:\n${JSON.stringify(data, null, 2)}`);
  };

  const basicCode = `import { DatePickerFormField, Form, FormItem } from '@/shared/components/common';
import { ValidationSchemas } from '@/shared/validation/schemas';
import { z } from 'zod';

// Schema với flexibleDate
const schema = z.object({
  birthDate: ValidationSchemas.flexibleDate({ t })
    .refine((date) => {
      const birthDate = typeof date === 'string' ? new Date(date) : date;
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 18 && age <= 100;
    }, 'Tuổi phải từ 18 đến 100'),
});

// Sử dụng trong form
<Form schema={schema} onSubmit={handleSubmit}>
  <FormItem name="birthDate" label="Ngày sinh" required>
    <DatePickerFormField
      placeholder="Chọn ngày sinh"
      format="dd/MM/yyyy"
      maxDate={new Date()}
    />
  </FormItem>
</Form>`;

  const validationCode = `// Các loại validation với DatePickerFormField

// 1. Ngày sinh (18-100 tuổi)
birthDate: ValidationSchemas.flexibleDate({ t })
  .refine((date) => {
    const birthDate = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    return age >= 18 && age <= 100;
  }, 'Tuổi phải từ 18 đến 100'),

// 2. Ngày hẹn (từ hôm nay trở đi)
appointmentDate: ValidationSchemas.flexibleDate({ t })
  .refine((date) => {
    const appointmentDate = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    return appointmentDate >= today;
  }, 'Ngày hẹn phải từ hôm nay trở đi'),

// 3. Ngày kết thúc (sau ngày bắt đầu)
endDate: ValidationSchemas.flexibleDate({ t })
  .refine((date, ctx) => {
    if (!date) return true; // Optional field
    const endDate = typeof date === 'string' ? new Date(date) : date;
    const startDate = ctx.parent.startDate;
    if (!startDate) return true;
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    return endDate >= start;
  }, 'Ngày kết thúc phải sau ngày bắt đầu')`;

  return (
    <div className="w-full bg-background text-foreground p-4 sm:p-6">
      <div className="mb-8">
        <Typography variant="h1" className="mb-4">
          DatePickerFormField Component Demo
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          Component DatePickerFormField để sử dụng với Form validation, tự động xử lý chuyển đổi giữa Date object và string.
        </Typography>
      </div>

      {/* Basic Example */}
      <ComponentDemo
        title="Ví dụ cơ bản với Form Validation"
        description="DatePickerFormField với schema validation sử dụng flexibleDate"
        code={basicCode}
      >
        <Card className="p-6">
          <Form
            ref={formRef}
            schema={schema}
            onSubmit={handleSubmit}
            defaultValues={{
              birthDate: '',
              startDate: '',
              endDate: '',
              appointmentDate: '',
            }}
          >
            <div className="space-y-4">
              {/* Ngày sinh */}
              <FormItem name="birthDate" label="Ngày sinh" required>
                <DatePickerFormField
                  placeholder="Chọn ngày sinh"
                  format="dd/MM/yyyy"
                  maxDate={new Date()}
                />
              </FormItem>

              {/* Ngày bắt đầu */}
              <FormItem name="startDate" label="Ngày bắt đầu" required>
                <DatePickerFormField
                  placeholder="Chọn ngày bắt đầu"
                  format="dd/MM/yyyy"
                />
              </FormItem>

              {/* Ngày kết thúc */}
              <FormItem name="endDate" label="Ngày kết thúc">
                <DatePickerFormField
                  placeholder="Chọn ngày kết thúc (tùy chọn)"
                  format="dd/MM/yyyy"
                />
              </FormItem>

              {/* Ngày hẹn */}
              <FormItem name="appointmentDate" label="Ngày hẹn" required>
                <DatePickerFormField
                  placeholder="Chọn ngày hẹn"
                  format="dd/MM/yyyy"
                  minDate={new Date()}
                />
              </FormItem>

              <Button type="submit" variant="primary" className="w-full">
                Submit Form
              </Button>
            </div>
          </Form>
        </Card>
      </ComponentDemo>

      {/* Validation Examples */}
      <ComponentDemo
        title="Các loại validation"
        description="Ví dụ về các loại validation khác nhau với DatePickerFormField"
        code={validationCode}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="p-4">
            <Typography variant="h4" className="mb-2">Validation Rules</Typography>
            <ul className="text-sm space-y-1">
              <li>• <strong>Ngày sinh:</strong> Tuổi từ 18-100</li>
              <li>• <strong>Ngày bắt đầu:</strong> Bắt buộc</li>
              <li>• <strong>Ngày kết thúc:</strong> Tùy chọn</li>
              <li>• <strong>Ngày hẹn:</strong> Từ hôm nay trở đi</li>
            </ul>
          </Card>

          <Card className="p-4">
            <Typography variant="h4" className="mb-2">Schema Type</Typography>
            <div className="text-sm">
              <code className="bg-gray-100 dark:bg-gray-800 p-2 rounded block">
                ValidationSchemas.flexibleDate()
              </code>
              <p className="mt-2 text-muted-foreground">
                Accepts both string and Date objects
              </p>
            </div>
          </Card>
        </div>
      </ComponentDemo>

      {/* Comparison */}
      <ComponentDemo
        title="So sánh DatePicker vs DatePickerFormField"
        description="Sự khác biệt giữa DatePicker thông thường và DatePickerFormField"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="p-4">
            <Typography variant="h4" className="mb-3 text-red-600">❌ DatePicker (Lỗi)</Typography>
            <div className="space-y-2 text-sm">
              <p><strong>Trả về:</strong> Date object</p>
              <p><strong>Schema expect:</strong> string</p>
              <p><strong>Kết quả:</strong> "Expected string, received date"</p>
              <div className="bg-red-50 dark:bg-red-900/20 p-2 rounded">
                <code className="text-red-700 dark:text-red-300">
                  dateOfBirth: z.string()
                </code>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <Typography variant="h4" className="mb-3 text-green-600">✅ DatePickerFormField (Đúng)</Typography>
            <div className="space-y-2 text-sm">
              <p><strong>Trả về:</strong> string (ISO format)</p>
              <p><strong>Schema accept:</strong> string | Date</p>
              <p><strong>Kết quả:</strong> Validation thành công</p>
              <div className="bg-green-50 dark:bg-green-900/20 p-2 rounded">
                <code className="text-green-700 dark:text-green-300">
                  ValidationSchemas.flexibleDate()
                </code>
              </div>
            </div>
          </Card>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default DatePickerFormFieldDemo;
