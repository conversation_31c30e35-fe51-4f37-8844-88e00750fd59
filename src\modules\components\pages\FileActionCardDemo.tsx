/**
 * Demo trang cho FileActionCard component
 */
import React, { useState } from 'react';
import { ComponentDemo } from '../components';
import { FileActionCard, Typography, ResponsiveGrid } from '@/shared/components/common';

const FileActionCardDemo: React.FC = () => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = () => {
    console.log('Tải xuống file');
    // Simulate download
    const link = document.createElement('a');
    link.href = 'https://cdn.redai.vn/contract/HDRULEBusiness';
    link.download = 'contract-demo.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleUpload = (file: File) => {
    console.log('Tải lên file:', file.name);
    setIsLoading(true);
    setUploadedFile(file);
    
    // Simulate upload process
    setTimeout(() => {
      setIsLoading(false);
      alert(`Đã tải lên thành công: ${file.name}`);
    }, 2000);
  };

  const basicCode = `import { FileActionCard } from '@/shared/components/common';

const BasicExample = () => {
  const handleDownload = () => {
    console.log('Tải xuống file');
  };

  const handleUpload = (file: File) => {
    console.log('Tải lên file:', file.name);
  };

  return (
    <FileActionCard
      title="Quản lý tệp"
      description="Tải xuống hoặc tải lên tệp của bạn"
      onDownload={handleDownload}
      onUpload={handleUpload}
    />
  );
};`;

  const customCode = `import { FileActionCard } from '@/shared/components/common';

const CustomExample = () => {
  return (
    <FileActionCard
      title="Hợp đồng PDF"
      description="Tải xuống hợp đồng mẫu hoặc tải lên hợp đồng đã ký"
      downloadUrl="https://cdn.redai.vn/contract/HDRULEBusiness"
      downloadFileName="hop-dong-mau.pdf"
      acceptedFileTypes=".pdf"
      maxFileSize={5}
      onUpload={(file) => console.log('Uploaded:', file.name)}
    />
  );
};`;

  const downloadOnlyCode = `import { FileActionCard } from '@/shared/components/common';

const DownloadOnlyExample = () => {
  return (
    <FileActionCard
      title="Tài liệu hướng dẫn"
      description="Tải xuống tài liệu hướng dẫn sử dụng"
      downloadUrl="https://cdn.redai.vn/contract/HDRULEBusiness"
      showUpload={false}
    />
  );
};`;

  const uploadOnlyCode = `import { FileActionCard } from '@/shared/components/common';

const UploadOnlyExample = () => {
  return (
    <FileActionCard
      title="Tải lên hình ảnh"
      description="Chọn hình ảnh để tải lên"
      acceptedFileTypes=".jpg,.jpeg,.png,.gif"
      maxFileSize={2}
      showDownload={false}
      onUpload={(file) => console.log('Uploaded image:', file.name)}
    />
  );
};`;

  return (
    <div className="w-full bg-background text-foreground p-4 sm:p-6">
      <div className="mb-8">
        <Typography variant="h1" className="mb-4">
          FileActionCard Component Demo
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          Component card với button tải xuống và tải lên file, hỗ trợ nhiều định dạng và tùy chỉnh linh hoạt.
        </Typography>
      </div>

      {/* Basic Example */}
      <ComponentDemo
        title="Ví dụ cơ bản"
        description="FileActionCard với cả chức năng tải xuống và tải lên"
        code={basicCode}
      >
        <FileActionCard
          title="Quản lý tệp"
          description="Tải xuống hoặc tải lên tệp của bạn"
          onDownload={handleDownload}
          onUpload={handleUpload}
          isLoading={isLoading}
        />
      </ComponentDemo>

      {/* Custom Example */}
      <ComponentDemo
        title="Ví dụ tùy chỉnh"
        description="FileActionCard với URL tải xuống và giới hạn file"
        code={customCode}
      >
        <FileActionCard
          title="Hợp đồng PDF"
          description="Tải xuống hợp đồng mẫu hoặc tải lên hợp đồng đã ký"
          downloadUrl="https://cdn.redai.vn/contract/HDRULEBusiness"
          downloadFileName="hop-dong-mau.pdf"
          acceptedFileTypes=".pdf"
          maxFileSize={5}
          onUpload={handleUpload}
          isLoading={isLoading}
        />
      </ComponentDemo>

      {/* Multiple Cards Grid */}
      <ComponentDemo
        title="Lưới nhiều card"
        description="Hiển thị nhiều FileActionCard với các chức năng khác nhau"
      >
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3 }} gap={{ xs: 4, md: 6 }}>
          {/* Download Only */}
          <FileActionCard
            title="Tài liệu hướng dẫn"
            description="Tải xuống tài liệu hướng dẫn sử dụng"
            downloadUrl="https://cdn.redai.vn/contract/HDRULEBusiness"
            downloadFileName="huong-dan.pdf"
            showUpload={false}
          />

          {/* Upload Only */}
          <FileActionCard
            title="Tải lên hình ảnh"
            description="Chọn hình ảnh để tải lên"
            acceptedFileTypes=".jpg,.jpeg,.png,.gif"
            maxFileSize={2}
            showDownload={false}
            onUpload={(file) => {
              console.log('Uploaded image:', file.name);
              alert(`Đã tải lên hình ảnh: ${file.name}`);
            }}
          />

          {/* Document Upload */}
          <FileActionCard
            title="Tài liệu văn bản"
            description="Tải lên tài liệu Word hoặc PDF"
            acceptedFileTypes=".pdf,.doc,.docx"
            maxFileSize={10}
            showDownload={false}
            onUpload={(file) => {
              console.log('Uploaded document:', file.name);
              alert(`Đã tải lên tài liệu: ${file.name}`);
            }}
          />
        </ResponsiveGrid>
      </ComponentDemo>

      {/* Download Only Example */}
      <ComponentDemo
        title="Chỉ tải xuống"
        description="FileActionCard chỉ có chức năng tải xuống"
        code={downloadOnlyCode}
      >
        <FileActionCard
          title="Tài liệu hướng dẫn"
          description="Tải xuống tài liệu hướng dẫn sử dụng"
          downloadUrl="https://cdn.redai.vn/contract/HDRULEBusiness"
          downloadFileName="huong-dan.pdf"
          showUpload={false}
        />
      </ComponentDemo>

      {/* Upload Only Example */}
      <ComponentDemo
        title="Chỉ tải lên"
        description="FileActionCard chỉ có chức năng tải lên"
        code={uploadOnlyCode}
      >
        <FileActionCard
          title="Tải lên hình ảnh"
          description="Chọn hình ảnh để tải lên"
          acceptedFileTypes=".jpg,.jpeg,.png,.gif"
          maxFileSize={2}
          showDownload={false}
          onUpload={(file) => {
            console.log('Uploaded image:', file.name);
            alert(`Đã tải lên hình ảnh: ${file.name}`);
          }}
        />
      </ComponentDemo>

      {/* Status Display */}
      {uploadedFile && (
        <div className="mt-8 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <Typography variant="h4" className="text-green-700 dark:text-green-300 mb-2">
            File đã tải lên
          </Typography>
          <Typography variant="body2" className="text-green-600 dark:text-green-400">
            Tên file: {uploadedFile.name}<br />
            Kích thước: {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB<br />
            Loại: {uploadedFile.type}
          </Typography>
        </div>
      )}
    </div>
  );
};

export default FileActionCardDemo;
