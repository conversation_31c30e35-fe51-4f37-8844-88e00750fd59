import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import {
  Form,
  FormItem,
  Input,
  Button,
  FormGrid,
  FormInline,
  FormHorizontal,
  ConditionalField,
  Toggle,
  Icon,
} from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { useFormErrors, useApiForm, ApiResponse } from '@/shared/hooks';
import { z } from 'zod';

const FormComponentsPage: React.FC = () => {
  const { t } = useTranslation();

  // Basic form schema
  const basicFormSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
  });

  // Form with conditional fields schema
  const conditionalFormSchema = z.object({
    userType: z.enum(['personal', 'business']),
    companyName: z.string().optional(),
    hasBillingAddress: z.boolean().optional(),
    billingAddress: z.string().optional(),
  });

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.form.title', 'Form Components')}
        </h1>
        <p className="text-muted">
          {t(
            'components.form.description',
            'A collection of form components for building forms with various layouts and behaviors.'
          )}
        </p>
      </div>

      {/* Basic Form */}
      <ComponentDemo
        title={t('components.form.basic.title', 'Basic Form')}
        description={t(
          'components.form.basic.description',
          'A basic form with validation using Zod schema.'
        )}
        code={`import { Form, FormItem, Input, Button } from '@/shared/components/common';
import { z } from 'zod';

// Define schema
const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
});

// Use in component
<Form
  schema={schema}
  onSubmit={(values) => console.log(values)}
  className="space-y-4"
>
  <FormItem name="name" label="Name" required>
    <Input placeholder="Enter your name" />
  </FormItem>

  <FormItem name="email" label="Email" required>
    <Input type="email" placeholder="Enter your email" />
  </FormItem>

  <Button type="submit">Submit</Button>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={basicFormSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
          >
            <FormItem name="name" label="Name" required>
              <Input placeholder="Enter your name" fullWidth />
            </FormItem>

            <FormItem name="email" label="Email" required>
              <Input type="email" placeholder="Enter your email" fullWidth />
            </FormItem>

            <Button type="submit">Submit</Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* Form Grid Layout */}
      <ComponentDemo
        title={t('components.form.grid.title', 'Form Grid Layout')}
        description={t(
          'components.form.grid.description',
          'Organize form fields in a responsive grid layout.'
        )}
        code={`import { Form, FormItem, Input, Button, FormGrid } from '@/shared/components/common';

<Form schema={schema} onSubmit={handleSubmit}>
  <FormGrid columns={2} gap="md">
    <FormItem name="firstName" label="First Name" required>
      <Input placeholder="Enter first name" />
    </FormItem>

    <FormItem name="lastName" label="Last Name" required>
      <Input placeholder="Enter last name" />
    </FormItem>

    <FormItem name="email" label="Email" required className="col-span-2">
      <Input type="email" placeholder="Enter email" />
    </FormItem>
  </FormGrid>

  <Button type="submit">Submit</Button>
</Form>`}
      >
        <div className="w-full max-w-2xl mx-auto">
          <Form
            schema={basicFormSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
          >
            <FormGrid columns={2} gap="md">
              <FormItem name="firstName" label="First Name" required>
                <Input placeholder="Enter first name" fullWidth />
              </FormItem>

              <FormItem name="lastName" label="Last Name" required>
                <Input placeholder="Enter last name" fullWidth />
              </FormItem>

              <FormItem name="email" label="Email" required className="col-span-2">
                <Input type="email" placeholder="Enter email" fullWidth />
              </FormItem>
            </FormGrid>

            <Button type="submit">Submit</Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* Form Inline Layout */}
      <ComponentDemo
        title={t('components.form.inline.title', 'Form Inline Layout')}
        description={t(
          'components.form.inline.description',
          'Create inline forms for search or simple inputs.'
        )}
        code={`import { Form, FormItem, Input, Button, FormInline } from '@/shared/components/common';

<Form schema={schema} onSubmit={handleSubmit}>
  <FormInline gap="md" align="end">
    <FormItem name="search" label="Search" required>
      <Input placeholder="Search..." />
    </FormItem>

    <Button type="submit">Search</Button>
  </FormInline>
</Form>`}
      >
        <div className="w-full max-w-2xl mx-auto">
          <Form
            schema={z.object({ search: z.string().min(1) })}
            onSubmit={values => console.log(values)}
          >
            <FormInline gap="md" align="end">
              <FormItem name="search" label="Search" required>
                <Input placeholder="Search..." />
              </FormItem>

              <Button type="submit">Search</Button>
            </FormInline>
          </Form>
        </div>
      </ComponentDemo>

      {/* Form Horizontal Layout */}
      <ComponentDemo
        title={t('components.form.horizontal.title', 'Form Horizontal Layout')}
        description={t(
          'components.form.horizontal.description',
          'Create forms with labels on the left and fields on the right.'
        )}
        code={`import { Form, FormItem, Input, Button, FormHorizontal } from '@/shared/components/common';

<Form schema={schema} onSubmit={handleSubmit}>
  <FormHorizontal labelWidth="md" gap="md">
    <FormItem name="name" label="Name" required>
      <Input placeholder="Enter your name" />
    </FormItem>

    <FormItem name="email" label="Email" required>
      <Input type="email" placeholder="Enter your email" />
    </FormItem>

    <div className="flex justify-end mt-4 ml-40">
      <Button type="submit">Submit</Button>
    </div>
  </FormHorizontal>
</Form>`}
      >
        <div className="w-full max-w-2xl mx-auto">
          <Form schema={basicFormSchema} onSubmit={values => console.log(values)}>
            <FormHorizontal labelWidth="md" gap="md">
              <FormItem name="name" label="Name" required>
                <Input placeholder="Enter your name" fullWidth />
              </FormItem>

              <FormItem name="email" label="Email" required>
                <Input type="email" placeholder="Enter your email" fullWidth />
              </FormItem>

              <div className="flex justify-end mt-4 ml-40">
                <Button type="submit">Submit</Button>
              </div>
            </FormHorizontal>
          </Form>
        </div>
      </ComponentDemo>

      {/* Conditional Fields */}
      <ComponentDemo
        title={t('components.form.conditional.title', 'Conditional Fields')}
        description={t(
          'components.form.conditional.description',
          'Show or hide fields based on conditions.'
        )}
        code={`import { Form, FormItem, Input, Toggle, ConditionalField } from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

<Form schema={schema} onSubmit={handleSubmit}>
  <FormItem name="userType" label="Account Type" required>
    <select>
      <option value="personal">Personal</option>
      <option value="business">Business</option>
    </select>
  </FormItem>

  <ConditionalField
    condition={{
      field: 'userType',
      type: ConditionType.EQUALS,
      value: 'business'
    }}
  >
    <FormItem name="companyName" label="Company Name" required>
      <Input placeholder="Enter company name" />
    </FormItem>
  </ConditionalField>

  <FormItem name="hasBillingAddress" label="Different Billing Address?" inline>
    <Toggle />
  </FormItem>

  <ConditionalField
    condition={{
      field: 'hasBillingAddress',
      type: ConditionType.IS_TRUE
    }}
  >
    <FormItem name="billingAddress" label="Billing Address" required>
      <Input placeholder="Enter billing address" />
    </FormItem>
  </ConditionalField>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={conditionalFormSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
            defaultValues={{
              userType: 'personal',
              hasBillingAddress: false,
            }}
          >
            <FormItem name="userType" label="Account Type" required>
              <select className="w-full p-2 border rounded">
                <option value="personal">Personal</option>
                <option value="business">Business</option>
              </select>
            </FormItem>

            <ConditionalField
              condition={{
                field: 'userType',
                type: ConditionType.EQUALS,
                value: 'business',
              }}
            >
              <FormItem name="companyName" label="Company Name" required>
                <Input placeholder="Enter company name" fullWidth />
              </FormItem>
            </ConditionalField>

            <FormItem name="hasBillingAddress" label="Different Billing Address?" inline>
              <Toggle />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'hasBillingAddress',
                type: ConditionType.IS_TRUE,
              }}
            >
              <FormItem name="billingAddress" label="Billing Address" required>
                <Input placeholder="Enter billing address" fullWidth />
              </FormItem>
            </ConditionalField>

            <Button type="submit">Submit</Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* API Form Integration */}
      <ComponentDemo
        title={t('components.form.apiForm.title', 'API Form Integration')}
        description={t(
          'components.form.apiForm.description',
          'Integrate forms with API calls using the useApiForm hook.'
        )}
        code={`import { useApiForm } from '@/shared/hooks';
import { Form, FormItem, Input, Button } from '@/shared/components/common';

// Define your API function
const loginApi = async (data) => {
  // API call implementation
  return { success: true, data: { user: { name: 'User' } } };
};

// Use the hook
const {
  formRef,
  handleSubmit,
  isSubmitting,
  isSuccess,
  error,
  data
} = useApiForm({
  apiCall: loginApi,
  onSuccess: (data) => console.log('Success:', data),
  onError: (errors, message) => console.log('Error:', errors, message)
});

// Use in your component
<Form
  ref={formRef}
  schema={loginSchema}
  onSubmit={handleSubmit}
>
  <FormItem name="email" label="Email" required>
    <Input type="email" />
  </FormItem>

  <FormItem name="password" label="Password" required>
    <Input type="password" />
  </FormItem>

  {error && <div className="error">{error}</div>}

  <Button type="submit" isLoading={isSubmitting}>
    Login
  </Button>
</Form>

{isSuccess && <div>Login successful!</div>}`}
      >
        <div className="w-full max-w-md mx-auto">
          <ApiFormExample />
        </div>
      </ComponentDemo>

      {/* Form API Error Handling */}
      <ComponentDemo
        title={t('components.form.apiError.title', 'Form API Error Handling')}
        description={t(
          'components.form.apiError.description',
          'Handle API errors in forms using the useFormErrors hook.'
        )}
        code={`import { useFormErrors } from '@/shared/hooks';
import { Form, FormItem, Input, Button } from '@/shared/components/common';

// Use the hook
const { formRef, setFormErrors, resetForm } = useFormErrors();

// Handle form submission
const handleSubmit = async (values) => {
  try {
    const result = await apiCall(values);

    if (result.success) {
      // Success handling
      resetForm();
    } else if (result.errors) {
      // Set errors from API
      setFormErrors(result.errors);
    }
  } catch (error) {
    console.error('Error:', error);
  }
};

// Use in your component
<Form
  ref={formRef}
  schema={schema}
  onSubmit={handleSubmit}
>
  <FormItem name="username" label="Username" required>
    <Input />
  </FormItem>

  <FormItem name="email" label="Email" required>
    <Input type="email" />
  </FormItem>

  <Button type="submit">
    Register
  </Button>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <FormApiErrorExample />
        </div>
      </ComponentDemo>
    </div>
  );
};

// API Form Example Component
const ApiFormExample: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);

  // Login schema
  const loginSchema = z.object({
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    password: z
      .string()
      .min(1, 'Password is required')
      .min(6, 'Password must be at least 6 characters'),
  });

  // Mock API call
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mockLoginApi = async (data: any): Promise<ApiResponse<any>> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simulate API errors
    if (data.email === '<EMAIL>') {
      return {
        success: false,
        errors: {
          email: 'This email does not exist in our system',
        },
        message: 'Login failed',
      };
    }

    if (data.password === 'wrongpass') {
      return {
        success: false,
        errors: {
          password: 'Incorrect password',
        },
      };
    }

    // Success
    return {
      success: true,
      data: {
        user: {
          email: data.email,
          name: 'Demo User',
        },
        token: 'demo-token-123',
      },
    };
  };

  // Use the hook
  const { formRef, handleSubmit, isSubmitting, isSuccess, error, data } = useApiForm({
    apiCall: mockLoginApi,
    onSuccess: data => console.log('Login successful:', data),
    onError: (errors, message) => console.log('Login failed:', { errors, message }),
  });

  return (
    <div className="space-y-4">
      <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded text-sm">
        <p className="font-medium mb-2">Test with:</p>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li>
            <code><EMAIL></code> - to see email field error
          </li>
          <li>
            <code>wrongpass</code> - to see password field error
          </li>
          <li>Any other values - to see successful login</li>
        </ul>
      </div>

      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={loginSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        className="space-y-4"
      >
        <FormItem name="email" label="Email" required>
          <Input
            type="email"
            placeholder="<EMAIL>"
            leftIcon={<Icon name="user" size="sm" />}
            fullWidth
          />
        </FormItem>

        <FormItem name="password" label="Password" required>
          <Input
            type={showPassword ? 'text' : 'password'}
            placeholder="Enter password"
            leftIcon={<Icon name="settings" size="sm" />}
            rightIcon={
              <div className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" onClick={() => setShowPassword(!showPassword)}>
                <Icon name={showPassword ? 'eye-off' : 'eye'} size="sm" />
              </div>
            }
            fullWidth
          />
        </FormItem>

        {error && (
          <div className="p-3 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100 rounded">
            <p>{error}</p>
          </div>
        )}

        <Button type="submit" variant="primary" fullWidth isLoading={isSubmitting}>
          {isSubmitting ? 'Logging in...' : 'Login'}
        </Button>
      </Form>

      {isSuccess && data && (
        <div className="p-3 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 rounded">
          <p className="font-medium">Login successful!</p>
          <div className="mt-2 text-sm">
            <p>
              <strong>Email:</strong> {data.user.email}
            </p>
            <p>
              <strong>Name:</strong> {data.user.name}
            </p>
            <p>
              <strong>Token:</strong> {data.token}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

// Form API Error Example Component
const FormApiErrorExample: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [registerSuccess, setRegisterSuccess] = useState(false);

  // Register schema
  const registerSchema = z.object({
    username: z
      .string()
      .min(1, 'Username is required')
      .min(3, 'Username must be at least 3 characters'),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    password: z
      .string()
      .min(1, 'Password is required')
      .min(6, 'Password must be at least 6 characters'),
  });

  // Use the hook
  const { formRef, setFormErrors, resetForm } = useFormErrors();

  // Mock API call
  const mockRegisterApi = async (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any
  ): Promise<{ success: boolean; errors?: Record<string, string> }> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simulate API errors
    if (data.email === '<EMAIL>') {
      return {
        success: false,
        errors: {
          email: 'This email is already in use',
        },
      };
    }

    if (data.username === 'admin') {
      return {
        success: false,
        errors: {
          username: 'This username is already taken',
        },
      };
    }

    // Success
    return { success: true };
  };

  // Handle form submission
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleRegisterSubmit = async (values: any) => {
    setIsLoading(true);
    setRegisterSuccess(false);

    try {
      // Call API (simulated)
      const result = await mockRegisterApi(values);

      if (result.success) {
        // Registration successful
        setRegisterSuccess(true);
        resetForm(); // Reset form
      } else if (result.errors) {
        // API returned errors, display them on form
        setFormErrors(result.errors);
      }
    } catch (error) {
      console.error('Registration failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded text-sm">
        <p className="font-medium mb-2">Test with:</p>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li>
            <code><EMAIL></code> - to see email field error
          </li>
          <li>
            <code>admin</code> (username) - to see username field error
          </li>
          <li>Any other values - to see successful registration</li>
        </ul>
      </div>

      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={registerSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleRegisterSubmit as any}
        className="space-y-4"
      >
        <FormItem name="username" label="Username" required>
          <Input placeholder="Enter username" leftIcon={<Icon name="user" size="sm" />} fullWidth />
        </FormItem>

        <FormItem name="email" label="Email" required>
          <Input
            type="email"
            placeholder="<EMAIL>"
            leftIcon={<Icon name="chat" size="sm" />}
            fullWidth
          />
        </FormItem>

        <FormItem name="password" label="Password" required>
          <Input
            type={showPassword ? 'text' : 'password'}
            placeholder="Enter password"
            leftIcon={<Icon name="settings" size="sm" />}
            rightIcon={
              <div className="cursor-pointer" onClick={() => setShowPassword(!showPassword)}>
                <Icon name={showPassword ? 'close' : 'search'} size="sm" />
              </div>
            }
            fullWidth
          />
        </FormItem>

        <Button type="submit" variant="primary" fullWidth isLoading={isLoading}>
          {isLoading ? 'Registering...' : 'Register'}
        </Button>
      </Form>

      {registerSuccess && (
        <div className="p-3 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 rounded">
          <p className="font-medium">Registration successful!</p>
          <p className="text-sm mt-1">Your account has been created successfully.</p>
        </div>
      )}
    </div>
  );
};

export default FormComponentsPage;
