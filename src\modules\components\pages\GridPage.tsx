import React from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import { Grid, Card } from '@/shared/components/common';

const GridPage: React.FC = () => {
  const { t } = useTranslation();

  // Helper function to create grid items
  const renderGridItems = (count: number) => {
    return Array.from({ length: count }).map((_, index) => (
      <Card key={index} className="p-4 h-24 flex items-center justify-center">
        Item {index + 1}
      </Card>
    ));
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.grid.title')}
        </h1>
        <p className="text-muted">{t('components.grid.description')}</p>
      </div>

      <ComponentDemo
        title={t('components.grid.basic.title')}
        description={t('components.grid.basic.description')}
        code={`import { Grid, Card } from '@/shared/components/common';

<Grid columns={3} columnGap="md" rowGap="md">
  <Card className="p-4 h-24 flex items-center justify-center">Item 1</Card>
  <Card className="p-4 h-24 flex items-center justify-center">Item 2</Card>
  <Card className="p-4 h-24 flex items-center justify-center">Item 3</Card>
  <Card className="p-4 h-24 flex items-center justify-center">Item 4</Card>
  <Card className="p-4 h-24 flex items-center justify-center">Item 5</Card>
  <Card className="p-4 h-24 flex items-center justify-center">Item 6</Card>
</Grid>`}
      >
        <div className="w-full">
          <Grid columns={3} columnGap="md" rowGap="md">
            {renderGridItems(6)}
          </Grid>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.grid.responsive.title')}
        description={t('components.grid.responsive.description')}
        code={`import { Grid, Card } from '@/shared/components/common';

<Grid columns={{ xs: 1, sm: 2, md: 3, lg: 4 }} columnGap="md" rowGap="md">
  <Card className="p-4 h-24 flex items-center justify-center">Item 1</Card>
  <Card className="p-4 h-24 flex items-center justify-center">Item 2</Card>
  <Card className="p-4 h-24 flex items-center justify-center">Item 3</Card>
  <Card className="p-4 h-24 flex items-center justify-center">Item 4</Card>
</Grid>`}
      >
        <div className="w-full">
          <Grid columns={{ xs: 1, sm: 2, md: 3, lg: 4 }} columnGap="md" rowGap="md">
            {renderGridItems(4)}
          </Grid>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.grid.gaps.title')}
        description={t('components.grid.gaps.description')}
        code={`import { Grid, Card } from '@/shared/components/common';

// Small gap
<Grid columns={2} columnGap="sm" rowGap="sm">
  {/* Grid items */}
</Grid>

// Medium gap
<Grid columns={2} columnGap="md" rowGap="md">
  {/* Grid items */}
</Grid>

// Large gap
<Grid columns={2} columnGap="lg" rowGap="lg">
  {/* Grid items */}
</Grid>`}
      >
        <div className="w-full space-y-8">
          <div>
            <p className="mb-2 text-sm font-medium">{t('components.grid.gaps.small')}</p>
            <Grid columns={2} columnGap="sm" rowGap="sm">
              {renderGridItems(4)}
            </Grid>
          </div>

          <div>
            <p className="mb-2 text-sm font-medium">{t('components.grid.gaps.medium')}</p>
            <Grid columns={2} columnGap="md" rowGap="md">
              {renderGridItems(4)}
            </Grid>
          </div>

          <div>
            <p className="mb-2 text-sm font-medium">{t('components.grid.gaps.large')}</p>
            <Grid columns={2} columnGap="lg" rowGap="lg">
              {renderGridItems(4)}
            </Grid>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default GridPage;
