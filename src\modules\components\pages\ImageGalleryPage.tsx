import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Tabs } from '@/shared/components/common';
import { ComponentDemo } from '@/modules/components/components';
import ImageGallery, { GalleryImage } from '@/shared/components/common/ImageGallery';

// Mock data cho gallery
const galleryImages: GalleryImage[] = [
  {
    src: 'https://images.unsplash.com/photo-1682687220063-4742bd7fd538?q=80&w=1000',
    thumbnail: 'https://images.unsplash.com/photo-1682687220063-4742bd7fd538?q=80&w=300',
    alt: 'Mountain landscape',
    caption: 'Beautiful mountain landscape with clouds',
    width: 1000,
    height: 667,
  },
  {
    src: 'https://images.unsplash.com/photo-1682695796954-bad0d0f59ff1?q=80&w=1000',
    thumbnail: 'https://images.unsplash.com/photo-1682695796954-bad0d0f59ff1?q=80&w=300',
    alt: 'Coastal view',
    caption: 'Stunning coastal view with clear blue water',
    width: 1000,
    height: 667,
  },
  {
    src: 'https://images.unsplash.com/photo-1682686581221-c126206d12f0?q=80&w=1000',
    thumbnail: 'https://images.unsplash.com/photo-1682686581221-c126206d12f0?q=80&w=300',
    alt: 'Forest path',
    caption: 'Serene forest path with sunlight filtering through trees',
    width: 1000,
    height: 1500,
  },
  {
    src: 'https://images.unsplash.com/photo-1682687220208-22d7a2543e88?q=80&w=1000',
    thumbnail: 'https://images.unsplash.com/photo-1682687220208-22d7a2543e88?q=80&w=300',
    alt: 'Desert landscape',
    caption: 'Vast desert landscape with sand dunes',
    width: 1000,
    height: 667,
  },
  {
    src: 'https://images.unsplash.com/photo-1682685797366-715d29e33f9d?q=80&w=1000',
    thumbnail: 'https://images.unsplash.com/photo-1682685797366-715d29e33f9d?q=80&w=300',
    alt: 'City skyline',
    caption: 'Modern city skyline at sunset',
    width: 1000,
    height: 667,
  },
  {
    src: 'https://images.unsplash.com/photo-1682685797208-c741d58c2eff?q=80&w=1000',
    thumbnail: 'https://images.unsplash.com/photo-1682685797208-c741d58c2eff?q=80&w=300',
    alt: 'Autumn forest',
    caption: 'Colorful autumn forest with fallen leaves',
    width: 1000,
    height: 1500,
  },
];

const ImageGalleryPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('grid');

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('components.imageGallery.title', 'Image Gallery')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t(
            'components.imageGallery.description',
            'Component hiển thị bộ sưu tập hình ảnh với nhiều tính năng nâng cao như grid layout, lightbox, thumbnails, zoom và lazy loading.'
          )}
        </p>
      </div>

      {/* Basic Usage */}
      <div className="mb-10">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-4">
          {t('components.imageGallery.basicUsage', 'Sử dụng cơ bản')}
        </h2>

        <ComponentDemo
          title={t('components.imageGallery.basicExample', 'Ví dụ cơ bản')}
          description={t(
            'components.imageGallery.basicDescription',
            'Image Gallery với các tùy chọn mặc định.'
          )}
          code={`import { ImageGallery } from '@/shared/components/common';

const images = [
  {
    src: 'https://example.com/image1.jpg',
    thumbnail: 'https://example.com/image1-thumb.jpg',
    alt: 'Image 1',
    caption: 'Caption for image 1'
  },
  {
    src: 'https://example.com/image2.jpg',
    thumbnail: 'https://example.com/image2-thumb.jpg',
    alt: 'Image 2',
    caption: 'Caption for image 2'
  }
];

<ImageGallery images={images} />`}
        >
          <div className="w-full">
            <ImageGallery images={galleryImages} />
          </div>
        </ComponentDemo>
      </div>

      {/* Layouts */}
      <div className="mb-10">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-4">
          {t('components.imageGallery.layouts', 'Các kiểu layout')}
        </h2>

        <Card className="mb-6">
          <Tabs
            items={[
              {
                key: 'grid',
                label: 'Grid',
                children: <div></div>,
              },
              {
                key: 'masonry',
                label: 'Masonry',
                children: <div></div>,
              },
              {
                key: 'carousel',
                label: 'Carousel',
                children: <div></div>,
              },
            ]}
            activeKey={activeTab}
            onChange={setActiveTab}
            className="mb-6"
          />

          {activeTab === 'grid' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Grid Layout</h3>
              <ImageGallery
                images={galleryImages}
                layout="grid"
                columns={{ xs: 1, sm: 2, md: 3 }}
              />
              <div className="mt-4 bg-gray-50 dark:bg-gray-800 p-4 rounded">
                <pre className="text-sm overflow-x-auto">
                  {`<ImageGallery
  images={images}
  layout="grid"
  columns={{ xs: 1, sm: 2, md: 3 }}
/>`}
                </pre>
              </div>
            </div>
          )}

          {activeTab === 'masonry' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Masonry Layout</h3>
              <ImageGallery
                images={galleryImages}
                layout="masonry"
                columns={{ xs: 1, sm: 2, md: 3 }}
              />
              <div className="mt-4 bg-gray-50 dark:bg-gray-800 p-4 rounded">
                <pre className="text-sm overflow-x-auto">
                  {`<ImageGallery
  images={images}
  layout="masonry"
  columns={{ xs: 1, sm: 2, md: 3 }}
/>`}
                </pre>
              </div>
            </div>
          )}

          {activeTab === 'carousel' && (
            <div>
              <h3 className="text-lg font-medium mb-4">Carousel Layout</h3>
              <ImageGallery images={galleryImages} layout="carousel" />
              <div className="mt-4 bg-gray-50 dark:bg-gray-800 p-4 rounded">
                <pre className="text-sm overflow-x-auto">
                  {`<ImageGallery
  images={images}
  layout="carousel"
/>`}
                </pre>
              </div>
            </div>
          )}
        </Card>
      </div>

      {/* Advanced Features */}
      <div className="mb-10">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-4">
          {t('components.imageGallery.advancedFeatures', 'Tính năng nâng cao')}
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Lightbox Configuration */}
          <ComponentDemo
            title={t('components.imageGallery.lightboxConfig', 'Cấu hình Lightbox')}
            description={t(
              'components.imageGallery.lightboxDescription',
              'Tùy chỉnh cấu hình lightbox.'
            )}
            code={`<ImageGallery
  images={images}
  lightbox={true}
  lightboxConfig={{
    showCaption: true,
    showCounter: true,
    showNavigation: true,
    closeOnBackdropClick: true,
    backdropOpacity: 0.9
  }}
/>`}
          >
            <div className="w-full">
              <ImageGallery
                images={galleryImages.slice(0, 3)}
                columns={{ xs: 1, sm: 3 }}
                lightbox={true}
                lightboxConfig={{
                  showCaption: true,
                  showCounter: true,
                  showNavigation: true,
                  closeOnBackdropClick: true,
                  backdropOpacity: 0.9,
                }}
              />
            </div>
          </ComponentDemo>

          {/* Thumbnails Configuration */}
          <ComponentDemo
            title={t('components.imageGallery.thumbnailsConfig', 'Cấu hình Thumbnails')}
            description={t(
              'components.imageGallery.thumbnailsDescription',
              'Tùy chỉnh cấu hình thumbnails.'
            )}
            code={`<ImageGallery
  images={images}
  thumbnails={true}
  thumbnailsConfig={{
    position: 'bottom',
    size: 60,
    gap: 8,
    visibleItems: 5,
    autoScroll: true
  }}
/>`}
          >
            <div className="w-full">
              <ImageGallery
                images={galleryImages.slice(0, 3)}
                columns={{ xs: 1, sm: 3 }}
                thumbnails={true}
                thumbnailsConfig={{
                  position: 'bottom',
                  size: 60,
                  gap: 8,
                  visibleItems: 5,
                  autoScroll: true,
                }}
              />
            </div>
          </ComponentDemo>

          {/* Zoom Configuration */}
          <ComponentDemo
            title={t('components.imageGallery.zoomConfig', 'Cấu hình Zoom')}
            description={t('components.imageGallery.zoomDescription', 'Tùy chỉnh cấu hình zoom.')}
            code={`<ImageGallery
  images={images}
  zoom={true}
  zoomConfig={{
    maxZoom: 3,
    minZoom: 1,
    zoomStep: 0.5,
    showControls: true
  }}
/>`}
          >
            <div className="w-full">
              <ImageGallery
                images={galleryImages.slice(0, 3)}
                columns={{ xs: 1, sm: 3 }}
                zoom={true}
                zoomConfig={{
                  maxZoom: 3,
                  minZoom: 1,
                  zoomStep: 0.5,
                  showControls: true,
                }}
              />
            </div>
          </ComponentDemo>

          {/* Lazy Loading */}
          <ComponentDemo
            title={t('components.imageGallery.lazyLoading', 'Lazy Loading')}
            description={t(
              'components.imageGallery.lazyLoadingDescription',
              'Tải hình ảnh khi cần thiết để tối ưu hiệu suất.'
            )}
            code={`<ImageGallery
  images={images}
  lazyLoad={true}
/>`}
          >
            <div className="w-full">
              <ImageGallery
                images={galleryImages}
                columns={{ xs: 1, sm: 2, md: 3 }}
                lazyLoad={true}
              />
            </div>
          </ComponentDemo>
        </div>
      </div>

      {/* API Reference */}
      <div className="mb-10">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-4">
          {t('components.imageGallery.apiReference', 'API Reference')}
        </h2>

        <Card>
          <h3 className="text-lg font-medium mb-4">ImageGallery Props</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Prop
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Default
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">images</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">GalleryImage[]</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">-</td>
                  <td className="px-6 py-4 text-sm">Danh sách hình ảnh</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">layout</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    'grid' | 'masonry' | 'carousel'
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">'grid'</td>
                  <td className="px-6 py-4 text-sm">Kiểu layout hiển thị</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">columns</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    number | &#123; xs, sm, md, lg, xl &#125;
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    &#123; xs: 1, sm: 2, md: 3, lg: 4, xl: 5 &#125;
                  </td>
                  <td className="px-6 py-4 text-sm">Số cột hiển thị</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">gap</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">number | string</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">16</td>
                  <td className="px-6 py-4 text-sm">Khoảng cách giữa các hình ảnh</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">lightbox</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">boolean</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">true</td>
                  <td className="px-6 py-4 text-sm">Bật/tắt lightbox</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">thumbnails</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">boolean</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">true</td>
                  <td className="px-6 py-4 text-sm">Bật/tắt thumbnails</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">zoom</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">boolean</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">true</td>
                  <td className="px-6 py-4 text-sm">Bật/tắt zoom</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">lazyLoad</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">boolean</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">true</td>
                  <td className="px-6 py-4 text-sm">Bật/tắt lazy loading</td>
                </tr>
              </tbody>
            </table>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ImageGalleryPage;
