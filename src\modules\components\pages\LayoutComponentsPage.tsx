import React from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import Container from '@/shared/components/common/Container';
import Grid from '@/shared/components/common/Grid';
import Resizer from '@/shared/components/common/Resizer';

const LayoutComponentsPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.categories.layout.title')}
        </h1>
        <p className="text-muted">{t('components.categories.layout.description')}</p>
      </div>

      <ComponentDemo
        title={t('components.layout.container.title')}
        description={t('components.layout.container.description')}
        code={`import Container from '@/shared/components/common/Container';

<Container>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">
    Container content
  </div>
</Container>`}
      >
        <div className="w-full">
          <Container>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Container content</div>
          </Container>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.layout.grid.title')}
        description={t('components.layout.grid.description')}
        code={`import Grid from '@/shared/components/common/Grid';

<Grid columns={3} columnGap="md" rowGap="md">
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 1</div>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 2</div>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 3</div>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 4</div>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 5</div>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 6</div>
</Grid>`}
      >
        <div className="w-full">
          <Grid columns={3} columnGap="md" rowGap="md">
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 1</div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 2</div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 3</div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 4</div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 5</div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 6</div>
          </Grid>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.layout.responsiveGrid.title')}
        description={t('components.layout.responsiveGrid.description')}
        code={`import Grid from '@/shared/components/common/Grid';

<Grid columns={{ xs: 1, sm: 2, md: 3, lg: 4 }} columnGap="md" rowGap="md">
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 1</div>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 2</div>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 3</div>
  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 4</div>
</Grid>`}
      >
        <div className="w-full">
          <Grid columns={{ xs: 1, sm: 2, md: 3, lg: 4 }} columnGap="md" rowGap="md">
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 1</div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 2</div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 3</div>
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">Item 4</div>
          </Grid>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.layout.resizer.title')}
        description={t('components.layout.resizer.description')}
        code={`import Resizer from '@/shared/components/common/Resizer';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<div className="h-64 flex">
  <div className="bg-gray-100 dark:bg-gray-700 p-4 flex-1">
    {t('components.layout.panels.left')}
  </div>
  <Resizer orientation="vertical" />
  <div className="bg-gray-200 dark:bg-gray-600 p-4 flex-1">
    {t('components.layout.panels.right')}
  </div>
</div>`}
      >
        <div className="h-64 w-full flex">
          <div className="bg-gray-100 dark:bg-gray-700 p-4 flex-1">
            {t('components.layout.panels.left')}
          </div>
          <Resizer orientation="vertical" />
          <div className="bg-gray-200 dark:bg-gray-600 p-4 flex-1">
            {t('components.layout.panels.right')}
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.layout.horizontalResizer.title')}
        description={t('components.layout.horizontalResizer.description')}
        code={`import Resizer from '@/shared/components/common/Resizer';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<div className="h-64 flex flex-col">
  <div className="bg-gray-100 dark:bg-gray-700 p-4 flex-1">
    {t('components.layout.panels.top')}
  </div>
  <Resizer orientation="horizontal" />
  <div className="bg-gray-200 dark:bg-gray-600 p-4 flex-1">
    {t('components.layout.panels.bottom')}
  </div>
</div>`}
      >
        <div className="h-64 w-full flex flex-col">
          <div className="bg-gray-100 dark:bg-gray-700 p-4 flex-1">
            {t('components.layout.panels.top')}
          </div>
          <Resizer orientation="horizontal" />
          <div className="bg-gray-200 dark:bg-gray-600 p-4 flex-1">
            {t('components.layout.panels.bottom')}
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default LayoutComponentsPage;
