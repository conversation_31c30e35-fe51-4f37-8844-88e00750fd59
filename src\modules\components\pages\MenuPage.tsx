import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import Menu from '@/shared/components/common/Menu/Menu';
import MenuItem from '@/shared/components/common/Menu/MenuItem';
import SubMenu from '@/shared/components/common/Menu/SubMenu';
import MenuDivider from '@/shared/components/common/Menu/MenuDivider';
import Button from '@/shared/components/common/Button';

const MenuPage: React.FC = () => {
  const { t } = useTranslation();
  const [selectedKey, setSelectedKey] = useState<string>('home');
  const [collapsed, setCollapsed] = useState<boolean>(false);

  // Xử lý khi chọn menu item
  const handleSelect = (key: string) => {
    setSelectedKey(key);
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.menu.title', 'Menu')}
        </h1>
        <p className="text-muted">
          {t(
            'components.menu.description',
            'Menu với nhiều tính năng: submenu, các mode khác nhau, collapsed state'
          )}
        </p>
      </div>

      {/* Horizontal Menu */}
      <ComponentDemo
        title={t('components.menu.horizontal.title', 'Horizontal Menu')}
        description={t('components.menu.horizontal.description', 'Menu hiển thị theo chiều ngang')}
        code={`import { Menu, MenuItem, SubMenu, MenuDivider } from '@/shared/components/common';

const [selectedKey, setSelectedKey] = useState<string>('home');

const handleSelect = (key: string) => {
  setSelectedKey(key);
};

<Menu
  mode="horizontal"
  selectedKey={selectedKey}
  onSelect={handleSelect}
  className="rounded-md shadow-sm border border-gray-200 dark:border-gray-700"
>
  <MenuItem itemKey="home" label="Trang chủ" icon="home" />
  <MenuItem itemKey="products" label="Sản phẩm" icon="components" />
  <SubMenu itemKey="services" label="Dịch vụ" icon="settings">
    <MenuItem itemKey="service1" label="Dịch vụ 1" />
    <MenuItem itemKey="service2" label="Dịch vụ 2" />
    <MenuItem itemKey="service3" label="Dịch vụ 3" />
  </SubMenu>
  <MenuDivider />
  <MenuItem itemKey="contact" label="Liên hệ" icon="mail" />
</Menu>`}
      >
        <div className="flex flex-col space-y-4">
          <Menu
            mode="horizontal"
            selectedKey={selectedKey}
            onSelect={handleSelect}
            className="rounded-md shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <MenuItem itemKey="home" label={t('common.home', 'Trang chủ')} icon="home" />
            <MenuItem
              itemKey="products"
              label={t('common.products', 'Sản phẩm')}
              icon="components"
            />
            <SubMenu itemKey="services" label={t('common.services', 'Dịch vụ')} icon="settings">
              <MenuItem itemKey="service1" label={t('common.service1', 'Dịch vụ 1')} />
              <MenuItem itemKey="service2" label={t('common.service2', 'Dịch vụ 2')} />
              <MenuItem itemKey="service3" label={t('common.service3', 'Dịch vụ 3')} />
            </SubMenu>
            <MenuDivider />
            <MenuItem itemKey="contact" label={t('common.contact', 'Liên hệ')} icon="mail" />
          </Menu>
        </div>
      </ComponentDemo>

      {/* Vertical Menu */}
      <ComponentDemo
        title={t('components.menu.vertical.title', 'Vertical Menu')}
        description={t('components.menu.vertical.description', 'Menu hiển thị theo chiều dọc')}
        code={`import { Menu, MenuItem, SubMenu, MenuDivider, Button } from '@/shared/components/common';

const [selectedKey, setSelectedKey] = useState<string>('home');
const [collapsed, setCollapsed] = useState<boolean>(false);

const handleSelect = (key: string) => {
  setSelectedKey(key);
};

<div className="flex flex-col space-y-4">
  <Button onClick={() => setCollapsed(!collapsed)}>
    {collapsed ? 'Mở rộng' : 'Thu gọn'}
  </Button>

  <Menu
    mode="vertical"
    selectedKey={selectedKey}
    onSelect={handleSelect}
    collapsed={collapsed}
    className="rounded-md shadow-sm border border-gray-200 dark:border-gray-700"
  >
    <MenuItem itemKey="home" label="Trang chủ" icon="home" />
    <MenuItem itemKey="products" label="Sản phẩm" icon="components" />
    <SubMenu itemKey="services" label="Dịch vụ" icon="settings">
      <MenuItem itemKey="service1" label="Dịch vụ 1" />
      <MenuItem itemKey="service2" label="Dịch vụ 2" />
      <MenuItem itemKey="service3" label="Dịch vụ 3" />
    </SubMenu>
    <MenuDivider />
    <MenuItem itemKey="contact" label="Liên hệ" icon="mail" />
  </Menu>
</div>`}
      >
        <div className="flex flex-col space-y-4">
          <Button onClick={() => setCollapsed(!collapsed)}>
            {collapsed
              ? t('components.menu.expand', 'Mở rộng')
              : t('components.menu.collapse', 'Thu gọn')}
          </Button>

          <Menu
            mode="vertical"
            selectedKey={selectedKey}
            onSelect={handleSelect}
            collapsed={collapsed}
            className="rounded-md shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <MenuItem itemKey="home" label={t('common.home', 'Trang chủ')} icon="home" />
            <MenuItem
              itemKey="products"
              label={t('common.products', 'Sản phẩm')}
              icon="components"
            />
            <SubMenu itemKey="services" label={t('common.services', 'Dịch vụ')} icon="settings">
              <MenuItem itemKey="service1" label={t('common.service1', 'Dịch vụ 1')} />
              <MenuItem itemKey="service2" label={t('common.service2', 'Dịch vụ 2')} />
              <MenuItem itemKey="service3" label={t('common.service3', 'Dịch vụ 3')} />
            </SubMenu>
            <MenuDivider />
            <MenuItem itemKey="contact" label={t('common.contact', 'Liên hệ')} icon="mail" />
          </Menu>
        </div>
      </ComponentDemo>

      {/* Inline Menu */}
      <ComponentDemo
        title={t('components.menu.inline.title', 'Inline Menu')}
        description={t('components.menu.inline.description', 'Menu với submenu hiển thị inline')}
        code={`import { Menu, MenuItem, SubMenu, MenuDivider } from '@/shared/components/common';

const [selectedKey, setSelectedKey] = useState<string>('home');

const handleSelect = (key: string) => {
  setSelectedKey(key);
};

<Menu
  mode="inline"
  selectedKey={selectedKey}
  onSelect={handleSelect}
  className="rounded-md shadow-sm border border-gray-200 dark:border-gray-700"
>
  <MenuItem itemKey="home" label="Trang chủ" icon="home" />
  <MenuItem itemKey="products" label="Sản phẩm" icon="components" />
  <SubMenu itemKey="services" label="Dịch vụ" icon="settings">
    <MenuItem itemKey="service1" label="Dịch vụ 1" />
    <MenuItem itemKey="service2" label="Dịch vụ 2" />
    <MenuItem itemKey="service3" label="Dịch vụ 3" />
  </SubMenu>
  <MenuDivider />
  <MenuItem itemKey="contact" label="Liên hệ" icon="mail" />
</Menu>`}
      >
        <div className="flex flex-col space-y-4">
          <Menu
            mode="inline"
            selectedKey={selectedKey}
            onSelect={handleSelect}
            className="rounded-md shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <MenuItem itemKey="home" label={t('common.home', 'Trang chủ')} icon="home" />
            <MenuItem
              itemKey="products"
              label={t('common.products', 'Sản phẩm')}
              icon="components"
            />
            <SubMenu itemKey="services" label={t('common.services', 'Dịch vụ')} icon="settings">
              <MenuItem itemKey="service1" label={t('common.service1', 'Dịch vụ 1')} />
              <MenuItem itemKey="service2" label={t('common.service2', 'Dịch vụ 2')} />
              <MenuItem itemKey="service3" label={t('common.service3', 'Dịch vụ 3')} />
            </SubMenu>
            <MenuDivider />
            <MenuItem itemKey="contact" label={t('common.contact', 'Liên hệ')} icon="mail" />
          </Menu>
        </div>
      </ComponentDemo>

      {/* Menu Variants */}
      <ComponentDemo
        title={t('components.menu.variants.title', 'Menu Variants')}
        description={t('components.menu.variants.description', 'Các biến thể khác nhau của Menu')}
        code={`import { Menu, MenuItem, SubMenu, MenuDivider } from '@/shared/components/common';

const [selectedKey, setSelectedKey] = useState<string>('home');

const handleSelect = (key: string) => {
  setSelectedKey(key);
};

// Primary variant
<Menu
  mode="horizontal"
  selectedKey={selectedKey}
  onSelect={handleSelect}
  variant="primary"
  className="mb-4 rounded-md"
>
  <MenuItem itemKey="home" label="Trang chủ" icon="home" />
  <MenuItem itemKey="products" label="Sản phẩm" icon="components" />
  <MenuItem itemKey="contact" label="Liên hệ" icon="mail" />
</Menu>

// Secondary variant
<Menu
  mode="horizontal"
  selectedKey={selectedKey}
  onSelect={handleSelect}
  variant="secondary"
  className="mb-4 rounded-md"
>
  <MenuItem itemKey="home" label="Trang chủ" icon="home" />
  <MenuItem itemKey="products" label="Sản phẩm" icon="components" />
  <MenuItem itemKey="contact" label="Liên hệ" icon="mail" />
</Menu>

// Bordered variant
<Menu
  mode="horizontal"
  selectedKey={selectedKey}
  onSelect={handleSelect}
  variant="bordered"
  className="mb-4"
>
  <MenuItem itemKey="home" label="Trang chủ" icon="home" />
  <MenuItem itemKey="products" label="Sản phẩm" icon="components" />
  <MenuItem itemKey="contact" label="Liên hệ" icon="mail" />
</Menu>`}
      >
        <div className="flex flex-col space-y-4">
          {/* Primary variant */}
          <Menu
            mode="horizontal"
            selectedKey={selectedKey}
            onSelect={handleSelect}
            variant="primary"
            className="rounded-md"
          >
            <MenuItem itemKey="home" label={t('common.home', 'Trang chủ')} icon="home" />
            <MenuItem
              itemKey="products"
              label={t('common.products', 'Sản phẩm')}
              icon="components"
            />
            <MenuItem itemKey="contact" label={t('common.contact', 'Liên hệ')} icon="mail" />
          </Menu>

          {/* Secondary variant */}
          <Menu
            mode="horizontal"
            selectedKey={selectedKey}
            onSelect={handleSelect}
            variant="secondary"
            className="rounded-md"
          >
            <MenuItem itemKey="home" label={t('common.home', 'Trang chủ')} icon="home" />
            <MenuItem
              itemKey="products"
              label={t('common.products', 'Sản phẩm')}
              icon="components"
            />
            <MenuItem itemKey="contact" label={t('common.contact', 'Liên hệ')} icon="mail" />
          </Menu>

          {/* Bordered variant */}
          <Menu
            mode="horizontal"
            selectedKey={selectedKey}
            onSelect={handleSelect}
            variant="bordered"
          >
            <MenuItem itemKey="home" label={t('common.home', 'Trang chủ')} icon="home" />
            <MenuItem
              itemKey="products"
              label={t('common.products', 'Sản phẩm')}
              icon="components"
            />
            <MenuItem itemKey="contact" label={t('common.contact', 'Liên hệ')} icon="mail" />
          </Menu>
        </div>
      </ComponentDemo>

      {/* Menu with Badge and Shortcut */}
      <ComponentDemo
        title={t('components.menu.advanced.title', 'Advanced Menu Items')}
        description={t('components.menu.advanced.description', 'Menu items với badge và shortcut')}
        code={`import { Menu, MenuItem, SubMenu, MenuDivider } from '@/shared/components/common';

const [selectedKey, setSelectedKey] = useState<string>('home');

const handleSelect = (key: string) => {
  setSelectedKey(key);
};

<Menu
  mode="vertical"
  selectedKey={selectedKey}
  onSelect={handleSelect}
  className="rounded-md shadow-sm border border-gray-200 dark:border-gray-700"
>
  <MenuItem itemKey="home" label="Trang chủ" icon="home" />
  <MenuItem
    itemKey="inbox"
    label="Hộp thư đến"
    icon="mail"
    badge="5"
    badgeColor="primary"
  />
  <MenuItem
    itemKey="notifications"
    label="Thông báo"
    icon="bell"
    badge="New"
    badgeColor="danger"
  />
  <MenuItem
    itemKey="settings"
    label="Cài đặt"
    icon="settings"
    shortcut="Ctrl+S"
  />
  <MenuDivider />
  <MenuItem
    itemKey="help"
    label="Trợ giúp"
    icon="info"
    shortcut="F1"
  />
</Menu>`}
      >
        <div className="flex flex-col space-y-4">
          <Menu
            mode="vertical"
            selectedKey={selectedKey}
            onSelect={handleSelect}
            className="rounded-md shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <MenuItem itemKey="home" label={t('common.home', 'Trang chủ')} icon="home" />
            <MenuItem
              itemKey="inbox"
              label={t('common.inbox', 'Hộp thư đến')}
              icon="mail"
              badge="5"
              badgeColor="primary"
            />
            <MenuItem
              itemKey="notifications"
              label={t('common.notifications', 'Thông báo')}
              icon="info"
              badge={t('common.ui.badge.new', 'New')}
              badgeColor="danger"
            />
            <MenuItem
              itemKey="settings"
              label={t('common.settings', 'Cài đặt')}
              icon="settings"
              shortcut="Ctrl+S"
            />
            <MenuDivider />
            <MenuItem
              itemKey="help"
              label={t('common.help', 'Trợ giúp')}
              icon="info"
              shortcut="F1"
            />
          </Menu>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default MenuPage;
