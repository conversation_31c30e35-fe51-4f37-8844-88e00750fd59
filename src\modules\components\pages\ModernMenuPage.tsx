import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, ModernMenu, IconCard, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import ComponentDemo from '../components/ComponentDemo';

const ModernMenuPage: React.FC = () => {
  const { t } = useTranslation();
  const [showBasicMenu, setShowBasicMenu] = useState(false);
  const [showIconMenu, setShowIconMenu] = useState(false);
  const [showPlacementMenu, setShowPlacementMenu] = useState(false);
  const [placement, setPlacement] = useState<'top' | 'right' | 'bottom' | 'left'>('bottom');

  // Tạo danh sách các action items cho ActionMenu
  const actionItems: ActionMenuItem[] = [
    {
      id: 'view',
      label: t('common.view', 'Xem'),
      icon: 'eye',
      onClick: () => console.log('View clicked'),
      tooltip: t('common.view', 'Xem'),
      showDirect: true,
    },
    {
      id: 'edit',
      label: t('common.edit', 'Chỉnh sửa'),
      icon: 'edit',
      onClick: () => console.log('Edit clicked'),
      tooltip: t('common.edit', 'Chỉnh sửa'),
      showDirect: true,
    },
    {
      id: 'delete',
      label: t('common.delete', 'Xóa'),
      icon: 'trash',
      onClick: () => console.log('Delete clicked'),
      tooltip: t('common.delete', 'Xóa'),
      showDirect: true,
    },
  ];

  // Tạo danh sách các action items cho ActionMenu với một số ẩn trong menu
  const actionItemsWithMenu: ActionMenuItem[] = [
    {
      id: 'view',
      label: t('common.view', 'Xem'),
      icon: 'eye',
      onClick: () => console.log('View clicked'),
      tooltip: t('common.view', 'Xem'),
      showDirect: true,
    },
    {
      id: 'edit',
      label: t('common.edit', 'Chỉnh sửa'),
      icon: 'edit',
      onClick: () => console.log('Edit clicked'),
      tooltip: t('common.edit', 'Chỉnh sửa'),
      showDirect: false,
    },
    {
      id: 'delete',
      label: t('common.delete', 'Xóa'),
      icon: 'trash',
      onClick: () => console.log('Delete clicked'),
      tooltip: t('common.delete', 'Xóa'),
      showDirect: false,
    },
    {
      id: 'divider1',
      divider: true,
    },
    {
      id: 'duplicate',
      label: t('common.duplicate', 'Nhân bản'),
      icon: 'copy',
      onClick: () => console.log('Duplicate clicked'),
    },
    {
      id: 'share',
      label: t('common.share', 'Chia sẻ'),
      icon: 'share',
      onClick: () => console.log('Share clicked'),
    },
  ];

  return (
    <div className="p-4 sm:p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-bold mb-4 text-foreground">
          {t('components.modernMenu.title', 'Modern Menu')}
        </h1>
        <p className="text-muted mb-6">
          {t(
            'components.modernMenu.description',
            'Modern menu component with various styles and placements.'
          )}
        </p>
      </div>

      {/* Basic Menu */}
      <ComponentDemo
        title={t('components.modernMenu.basic.title', 'Basic Menu')}
        description={t(
          'components.modernMenu.basic.description',
          'Basic menu with default settings.'
        )}
      >
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Button onClick={() => setShowBasicMenu(!showBasicMenu)}>
              {t('common.openMenu', 'Open Menu')}
            </Button>

            {showBasicMenu && (
              <ModernMenu
                isOpen={showBasicMenu}
                onClose={() => setShowBasicMenu(false)}
                items={[
                  {
                    id: 'item1',
                    label: t('common.edit', 'Edit'),
                    onClick: () => console.log('Edit clicked'),
                  },
                  {
                    id: 'item2',
                    label: t('common.delete', 'Delete'),
                    onClick: () => console.log('Delete clicked'),
                  },
                  {
                    id: 'divider1',
                    divider: true,
                  },
                  {
                    id: 'item3',
                    label: t('common.share', 'Share'),
                    onClick: () => console.log('Share clicked'),
                  },
                ]}
              />
            )}
          </div>
        </div>
      </ComponentDemo>

      {/* Menu with Icons */}
      <ComponentDemo
        title={t('components.modernMenu.withIcons.title', 'Menu with Icons')}
        description={t('components.modernMenu.withIcons.description', 'Menu items with icons.')}
      >
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Button onClick={() => setShowIconMenu(!showIconMenu)}>
              {t('common.openMenu', 'Open Menu')}
            </Button>

            {showIconMenu && (
              <ModernMenu
                isOpen={showIconMenu}
                onClose={() => setShowIconMenu(false)}
                items={[
                  {
                    id: 'item1',
                    label: t('common.edit', 'Edit'),
                    icon: 'edit',
                    onClick: () => console.log('Edit clicked'),
                  },
                  {
                    id: 'item2',
                    label: t('common.delete', 'Delete'),
                    icon: 'trash',
                    onClick: () => console.log('Delete clicked'),
                  },
                  {
                    id: 'divider1',
                    divider: true,
                  },
                  {
                    id: 'item3',
                    label: t('common.share', 'Share'),
                    icon: 'share',
                    onClick: () => console.log('Share clicked'),
                  },
                  {
                    id: 'item4',
                    label: t('common.download', 'Download'),
                    icon: 'download',
                    onClick: () => console.log('Download clicked'),
                  },
                ]}
              />
            )}
          </div>
        </div>
      </ComponentDemo>

      {/* Menu Placement */}
      <ComponentDemo
        title={t('components.modernMenu.placement.title', 'Menu Placement')}
        description={t(
          'components.modernMenu.placement.description',
          'Menu with different placements.'
        )}
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <Button
              variant={placement === 'top' ? 'primary' : 'outline'}
              onClick={() => setPlacement('top')}
            >
              {t('components.modernMenu.placement.top', 'Top')}
            </Button>
            <Button
              variant={placement === 'right' ? 'primary' : 'outline'}
              onClick={() => setPlacement('right')}
            >
              {t('components.modernMenu.placement.right', 'Right')}
            </Button>
            <Button
              variant={placement === 'bottom' ? 'primary' : 'outline'}
              onClick={() => setPlacement('bottom')}
            >
              {t('components.modernMenu.placement.bottom', 'Bottom')}
            </Button>
            <Button
              variant={placement === 'left' ? 'primary' : 'outline'}
              onClick={() => setPlacement('left')}
            >
              {t('components.modernMenu.placement.left', 'Left')}
            </Button>
          </div>

          <div className="flex items-center justify-center p-16 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
            <div className="relative">
              <IconCard icon="menu" onClick={() => setShowPlacementMenu(!showPlacementMenu)} />

              {showPlacementMenu && (
                <ModernMenu
                  isOpen={showPlacementMenu}
                  onClose={() => setShowPlacementMenu(false)}
                  placement={placement}
                  items={[
                    {
                      id: 'item1',
                      label: t('common.edit', 'Edit'),
                      icon: 'edit',
                      onClick: () => console.log('Edit clicked'),
                    },
                    {
                      id: 'item2',
                      label: t('common.delete', 'Delete'),
                      icon: 'trash',
                      onClick: () => console.log('Delete clicked'),
                    },
                  ]}
                />
              )}
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* ActionMenu Demo */}
      <ComponentDemo
        title={t('components.actionMenu.title', 'Action Menu')}
        description={t(
          'components.actionMenu.description',
          'Menu hành động với các tùy chọn hiển thị khác nhau.'
        )}
      >
        <div className="flex flex-col space-y-8">
          {/* Basic ActionMenu */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              {t('components.actionMenu.basic.title', 'Menu hành động cơ bản')}
            </h3>
            <div className="p-4 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common.moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
              />
            </div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {t(
                'components.actionMenu.basic.description',
                'Hiển thị tất cả các hành động trực tiếp với tooltip.'
              )}
            </p>
          </div>

          {/* ActionMenu with dropdown */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              {t('components.actionMenu.withDropdown.title', 'Menu hành động với dropdown')}
            </h3>
            <div className="p-4 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
              <ActionMenu
                items={actionItemsWithMenu}
                menuTooltip={t('common.moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
              />
            </div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {t(
                'components.actionMenu.withDropdown.description',
                'Hiển thị một số hành động trực tiếp và một số trong dropdown menu.'
              )}
            </p>
          </div>

          {/* ActionMenu with different sizes */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              {t('components.actionMenu.sizes.title', 'Kích thước khác nhau')}
            </h3>
            <div className="flex space-x-8 p-4 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
              <div>
                <p className="mb-2 text-sm">Small</p>
                <ActionMenu
                  items={actionItems}
                  menuTooltip={t('common.moreActions', 'Thêm hành động')}
                  iconSize="sm"
                  iconVariant="default"
                />
              </div>
              <div>
                <p className="mb-2 text-sm">Medium</p>
                <ActionMenu
                  items={actionItems}
                  menuTooltip={t('common.moreActions', 'Thêm hành động')}
                  iconSize="md"
                  iconVariant="default"
                />
              </div>
              <div>
                <p className="mb-2 text-sm">Large</p>
                <ActionMenu
                  items={actionItems}
                  menuTooltip={t('common.moreActions', 'Thêm hành động')}
                  iconSize="lg"
                  iconVariant="default"
                />
              </div>
            </div>
          </div>

          {/* ActionMenu with different variants */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              {t('components.actionMenu.variants.title', 'Các biến thể khác nhau')}
            </h3>
            <div className="flex space-x-8 p-4 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
              <div>
                <p className="mb-2 text-sm">Default</p>
                <ActionMenu
                  items={actionItems}
                  menuTooltip={t('common.moreActions', 'Thêm hành động')}
                  iconSize="sm"
                  iconVariant="default"
                />
              </div>
              <div>
                <p className="mb-2 text-sm">Primary</p>
                <ActionMenu
                  items={actionItems}
                  menuTooltip={t('common.moreActions', 'Thêm hành động')}
                  iconSize="sm"
                  iconVariant="primary"
                />
              </div>
              <div>
                <p className="mb-2 text-sm">Secondary</p>
                <ActionMenu
                  items={actionItems}
                  menuTooltip={t('common.moreActions', 'Thêm hành động')}
                  iconSize="sm"
                  iconVariant="secondary"
                />
              </div>
              <div>
                <p className="mb-2 text-sm">Ghost</p>
                <ActionMenu
                  items={actionItems}
                  menuTooltip={t('common.moreActions', 'Thêm hành động')}
                  iconSize="sm"
                  iconVariant="ghost"
                />
              </div>
            </div>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default ModernMenuPage;
