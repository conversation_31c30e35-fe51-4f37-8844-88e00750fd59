import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Card,
  Input,
  Select,
  ResponsiveGrid,
  Icon,
  IconName,
} from '@/shared/components/common';
import { ModuleCard } from '../card';

interface ModuleData {
  id: string;
  title: string;
  description: string;
  icon: IconName;
  linkTo: string;
  category: string;
  gradientColor: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  tags: string[];
}

const ModuleGalleryPage: React.FC = () => {
  const { t } = useTranslation(['components']);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSize, setSelectedSize] = useState<'sm' | 'md' | 'lg'>('md');
  const [loading, setLoading] = useState(false);

  // Mock data cho các module cards - di chuyển vào useMemo để tránh tạo lại mỗi lần render
  const moduleData = useMemo<ModuleData[]>(() => [
    {
      id: 'business-products',
      title: 'Sản phẩm',
      description: 'Quản lý danh mục sản phẩm, giá cả và thông tin chi tiết',
      icon: 'box',
      linkTo: '/business/product',
      category: 'business',
      gradientColor: 'primary',
      tags: ['business', 'product', 'management'],
    },
    {
      id: 'business-orders',
      title: 'Đơn hàng',
      description: 'Theo dõi và xử lý các đơn hàng từ khách hàng',
      icon: 'shopping-cart',
      linkTo: '/business/order',
      category: 'business',
      gradientColor: 'success',
      tags: ['business', 'order', 'sales'],
    },
    {
      id: 'marketing-campaigns',
      title: 'Chiến dịch',
      description: 'Tạo và quản lý các chiến dịch marketing đa kênh',
      icon: 'campaign',
      linkTo: '/marketing/campaign',
      category: 'marketing',
      gradientColor: 'warning',
      tags: ['marketing', 'campaign', 'promotion'],
    },
    {
      id: 'marketing-audience',
      title: 'Khách hàng',
      description: 'Quản lý danh sách khách hàng và phân khúc thị trường',
      icon: 'users',
      linkTo: '/marketing/audience',
      category: 'marketing',
      gradientColor: 'info',
      tags: ['marketing', 'audience', 'customers'],
    },
    {
      id: 'data-knowledge',
      title: 'File tri thức',
      description: 'Quản lý các tệp tin tri thức được sử dụng cho AI',
      icon: 'file-text',
      linkTo: '/data/knowledge-files',
      category: 'data',
      gradientColor: 'secondary',
      tags: ['data', 'knowledge', 'ai'],
    },
    {
      id: 'data-vector-store',
      title: 'Vector Store',
      description: 'Lưu trữ và embedding cho các ứng dụng AI và tìm kiếm',
      icon: 'database',
      linkTo: '/data/vector-store',
      category: 'data',
      gradientColor: 'primary',
      tags: ['data', 'vector', 'ai', 'search'],
    },
    {
      id: 'tools-management',
      title: 'Công cụ',
      description: 'Quản lý và tích hợp các công cụ hỗ trợ',
      icon: 'wrench',
      linkTo: '/tools',
      category: 'tools',
      gradientColor: 'error',
      tags: ['tools', 'integration', 'utility'],
    },
    {
      id: 'rpoint-packages',
      title: 'Gói R-Point',
      description: 'Quản lý các gói điểm thưởng và ưu đãi',
      icon: 'coin',
      linkTo: '/rpoint/packages',
      category: 'rpoint',
      gradientColor: 'warning',
      tags: ['rpoint', 'rewards', 'packages'],
    },
  ], []);

  // Categories for filtering
  const categories = [
    { value: 'all', label: t('components.moduleGallery.categories.all') },
    { value: 'business', label: t('components.moduleGallery.categories.business') },
    { value: 'marketing', label: t('components.moduleGallery.categories.marketing') },
    { value: 'data', label: t('components.moduleGallery.categories.data') },
    { value: 'tools', label: t('components.moduleGallery.categories.tools') },
    { value: 'rpoint', label: t('components.moduleGallery.categories.rpoint') },
  ];

  // Size options
  const sizeOptions = [
    { value: 'sm', label: t('components.moduleGallery.sizes.sm') },
    { value: 'md', label: t('components.moduleGallery.sizes.md') },
    { value: 'lg', label: t('components.moduleGallery.sizes.lg') },
  ];

  // Filter modules based on search and category
  const filteredModules = useMemo(() => {
    return moduleData.filter(module => {
      const matchesSearch =
        module.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || module.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [searchTerm, selectedCategory, moduleData]);

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1500);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <Typography variant="h4" className="font-bold text-foreground">
              {t('components.moduleGallery.title')}
            </Typography>
            <Typography variant="body1" className="text-muted mt-1">
              {t('components.moduleGallery.description')}
            </Typography>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              className="p-2 rounded-lg bg-primary/10 text-primary hover:bg-primary/20 transition-colors"
            >
              <Icon name="refresh-cw" size="sm" />
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <Input
              placeholder={t('components.moduleGallery.search.placeholder')}
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              leftIcon="search"
              className="w-full"
            />
          </div>
          <div>
            <Select
              value={selectedCategory}
              onChange={value => setSelectedCategory(value as string)}
              options={categories}
              placeholder={t('components.moduleGallery.filters.category')}
            />
          </div>
          <div>
            <Select
              value={selectedSize}
              onChange={value => setSelectedSize(value as 'sm' | 'md' | 'lg')}
              options={sizeOptions}
              placeholder={t('components.moduleGallery.filters.size')}
            />
          </div>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t border-border">
          <div className="flex items-center space-x-4">
            <Typography variant="body2" className="text-muted">
              {t('components.moduleGallery.stats.showing', {
                count: filteredModules.length,
                total: moduleData.length,
              })}
            </Typography>
          </div>
          <Typography variant="caption" className="text-muted">
            {t('components.moduleGallery.stats.sizeLabel', {
              size: sizeOptions.find(s => s.value === selectedSize)?.label,
            })}
          </Typography>
        </div>
      </Card>

      {/* Module Grid */}
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {filteredModules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            linkTo={module.linkTo}
            gradientColor={module.gradientColor}
            size={selectedSize}
            loading={loading}
          />
        ))}
      </ResponsiveGrid>

      {/* Empty state */}
      {filteredModules.length === 0 && !loading && (
        <Card className="p-12 text-center">
          <Icon name="search" size="xl" className="mx-auto text-muted mb-4" />
          <Typography variant="h6" className="text-muted mb-2">
            {t('components.moduleGallery.search.noResults')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('components.moduleGallery.search.noResultsDescription')}
          </Typography>
        </Card>
      )}
    </div>
  );
};

export default ModuleGalleryPage;
