import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import { Button, Card, Typography, Notification } from '@/shared/components/common';
import useNotification from '@/shared/hooks/common/useNotification';
import NotificationContainer from '@/shared/components/layout/chat-panel/NotificationContainer';

/**
 * Trang demo cho các loại thông báo (Notification)
 */
const NotificationPage: React.FC = () => {
  const { t } = useTranslation();
  const [showNotification, setShowNotification] = useState(false);
  const [notificationType, setNotificationType] = useState<
    'info' | 'success' | 'warning' | 'error'
  >('info');
  const { notifications, addNotification, removeNotification, clearNotifications } =
    useNotification();

  // Hiển thị thông báo trực tiếp
  const handleShowNotification = (type: 'info' | 'success' | 'warning' | 'error') => {
    setNotificationType(type);
    setShowNotification(true);
  };

  // Thêm thông báo sử dụng hook
  const handleAddNotification = (type: 'success' | 'error' | 'warning' | 'info') => {
    const message = `Đây là thông báo ${type} sẽ tự động biến mất sau 5 giây.`;
    const id = addNotification(type, message, 5000);
    console.log(`Đã thêm thông báo với ID: ${id}`);
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.notification.title', 'Thông báo (Notification)')}
        </h1>
        <p className="text-muted">
          {t(
            'components.notification.description',
            'Hiển thị thông báo cho người dùng về trạng thái của hành động hoặc thông tin quan trọng.'
          )}
        </p>
      </div>

      {/* Thông báo cơ bản */}
      <ComponentDemo
        title={t('components.notification.basic.title', 'Thông báo cơ bản')}
        description={t(
          'components.notification.basic.description',
          'Các loại thông báo cơ bản với các kiểu khác nhau.'
        )}
        code={`import { Notification } from '@/shared/components/common';

// Hiển thị thông báo
<Notification
  type="success"
  title="Thành công"
  message="Thao tác đã được thực hiện thành công."
  duration={5000}
  onClose={() => console.log('Đã đóng thông báo')}
/>

// Các loại thông báo
<Notification type="success" message="Thao tác thành công" />
<Notification type="error" message="Đã xảy ra lỗi" />
<Notification type="warning" message="Cảnh báo" />
<Notification type="info" message="Thông tin" />
`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button variant="primary" onClick={() => handleShowNotification('success')}>
              Thông báo thành công
            </Button>
            <Button variant="danger" onClick={() => handleShowNotification('error')}>
              Thông báo lỗi
            </Button>
            <Button variant="warning" onClick={() => handleShowNotification('warning')}>
              Thông báo cảnh báo
            </Button>
            <Button variant="secondary" onClick={() => handleShowNotification('info')}>
              Thông báo thông tin
            </Button>
          </div>

          {showNotification && (
            <Notification
              type={notificationType}
              title={`Thông báo ${
                notificationType === 'success'
                  ? 'thành công'
                  : notificationType === 'error'
                    ? 'lỗi'
                    : notificationType === 'warning'
                      ? 'cảnh báo'
                      : 'thông tin'
              }`}
              message={`Đây là một thông báo ${notificationType} với tiêu đề và nội dung.`}
              duration={5000}
              onClose={() => setShowNotification(false)}
            />
          )}
        </div>
      </ComponentDemo>

      {/* Sử dụng hook useNotification */}
      <ComponentDemo
        title={t('components.notification.hook.title', 'Sử dụng hook useNotification')}
        description={t(
          'components.notification.hook.description',
          'Quản lý thông báo bằng hook useNotification.'
        )}
        code={`import useNotification from '@/shared/hooks/common/useNotification';
import NotificationContainer from '@/shared/components/layout/chat-panel/NotificationContainer';

// Trong component
const { notifications, addNotification, removeNotification, clearNotifications } = useNotification();

// Thêm thông báo mới
const handleAddNotification = (type) => {
  const message = \`Đây là thông báo \${type}\`;
  const id = addNotification(type, message, 5000);
};

// Hiển thị danh sách thông báo
<NotificationContainer
  notifications={notifications}
  onRemove={removeNotification}
/>
`}
      >
        <div className="w-full max-w-md mx-auto space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button variant="primary" onClick={() => handleAddNotification('success')}>
              Thêm thông báo thành công
            </Button>
            <Button variant="danger" onClick={() => handleAddNotification('error')}>
              Thêm thông báo lỗi
            </Button>
            <Button variant="warning" onClick={() => handleAddNotification('warning')}>
              Thêm thông báo cảnh báo
            </Button>
            <Button variant="secondary" onClick={() => handleAddNotification('info')}>
              Thêm thông báo thông tin
            </Button>
            {notifications.length > 0 && (
              <Button variant="outline" onClick={clearNotifications}>
                Xóa tất cả
              </Button>
            )}
          </div>

          {notifications.length > 0 && (
            <div className="mt-4 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <Typography variant="subtitle2" className="mb-2">
                Danh sách thông báo ({notifications.length})
              </Typography>
              <NotificationContainer notifications={notifications} onRemove={removeNotification} />
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Hướng dẫn sử dụng */}
      <Card title="Hướng dẫn sử dụng thông báo" className="mb-6">
        <div className="space-y-4">
          <Typography variant="subtitle1">1. Sử dụng component Notification trực tiếp</Typography>
          <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
            {`import { Notification } from '@/shared/components/common';

// Trong component
const [showNotification, setShowNotification] = useState(false);

// Hiển thị thông báo
{showNotification && (
  <Notification
    type="success"
    title="Thành công"
    message="Thao tác đã được thực hiện thành công."
    duration={5000}
    onClose={() => setShowNotification(false)}
  />
)}`}
          </pre>

          <Typography variant="subtitle1">2. Sử dụng hook useNotification</Typography>
          <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
            {`import useNotification from '@/shared/hooks/common/useNotification';

// Trong component
const { notifications, addNotification, removeNotification } = useNotification();

// Thêm thông báo mới
const showNotification = (type, message) => {
  const id = addNotification(type, message, 5000);
  return id;
};

// Gọi hàm khi cần hiển thị thông báo
showNotification('success', 'Thao tác thành công!');`}
          </pre>

          <Typography variant="subtitle1">3. Sử dụng trong Chat Panel</Typography>
          <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
            {`// Trong ChatPanel.tsx
const { notifications, addNotification, removeNotification } = useNotification();

// Tạo hàm hiển thị thông báo
const showNotification = (type, message) => {
  const id = addNotification(type, message, 5000);
  return id;
};

// Truyền vào ChatContent để hiển thị
<ChatContent
  messages={messages}
  notifications={notifications}
  onRemoveNotification={removeNotification}
/>

// Truyền vào ChatInput để sử dụng
<ChatInput
  onSendMessage={handleSendMessage}
  showNotification={showNotification}
/>`}
          </pre>
        </div>
      </Card>
    </div>
  );
};

export default NotificationPage;
