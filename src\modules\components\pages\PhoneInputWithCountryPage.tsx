import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  PhoneInputWithCountry,
  Form,
  FormItem,
  Button,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { FieldValues } from 'react-hook-form';
import { z } from 'zod';

// Schema validation cho form demo
const demoFormSchema = z.object({
  phone: z.string().min(1, 'Số điện thoại là bắt buộc'),
  name: z.string().min(1, 'Tên là bắt buộc'),
});

type DemoFormValues = z.infer<typeof demoFormSchema>;

/**
 * Trang demo PhoneInputWithCountry component
 */
const PhoneInputWithCountryPage: React.FC = () => {
  const { t } = useTranslation(['common']);
  const { formRef, setFormErrors } = useFormErrors<DemoFormValues>();

  // State để hiển thị kết quả
  const [phoneValue, setPhoneValue] = useState<string>('');
  const [submittedData, setSubmittedData] = useState<any>(null);

  // Xử lý thay đổi phone
  const handlePhoneChange = (value: string) => {
    console.log('📞 Phone changed:', value);
    setPhoneValue(value);
  };

  // Xử lý submit form
  const handleSubmit = async (values: FieldValues) => {
    console.log('📝 Form submitted:', values);
    setSubmittedData(values);
  };

  // Test data
  const testCases = [
    { label: 'Số VN thường', value: '0987654321' },
    { label: 'Số VN quốc tế', value: '+84987654321' },
    { label: 'Số US', value: '+15551234567' },
    { label: 'Số Trung Quốc', value: '+8613800138000' },
    { label: 'Số Nhật Bản', value: '+819012345678' },
    { label: 'Rỗng', value: '' },
  ];

  return (
    <div className="w-full bg-background text-foreground p-6 space-y-6">
      <Typography variant="h2">PhoneInputWithCountry Demo</Typography>

      {/* Basic Usage */}
      <Card>
        <div className="space-y-4">
          <Typography variant="h4">Basic Usage</Typography>
          
          <div className="space-y-4">
            <div>
              <Typography variant="body1" className="mb-2">
                Standalone Component:
              </Typography>
              <PhoneInputWithCountry
                value={phoneValue}
                onChange={handlePhoneChange}
                placeholder="Nhập số điện thoại"
                fullWidth
                defaultCountry="VN"
              />
            </div>

            <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded">
              <Typography variant="body2">
                <strong>Current Value:</strong> {phoneValue || '(empty)'}
              </Typography>
            </div>
          </div>
        </div>
      </Card>

      {/* Test Cases */}
      <Card>
        <div className="space-y-4">
          <Typography variant="h4">Test Cases</Typography>
          
          <div className="grid grid-cols-2 gap-4">
            {testCases.map((testCase, index) => (
              <Button
                key={index}
                variant="secondary"
                onClick={() => setPhoneValue(testCase.value)}
                className="text-left"
              >
                <div>
                  <div className="font-medium">{testCase.label}</div>
                  <div className="text-sm text-muted-foreground">{testCase.value || '(empty)'}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {/* Form Integration */}
      <Card>
        <div className="space-y-4">
          <Typography variant="h4">Form Integration</Typography>
          
          <Form
            ref={formRef}
            schema={demoFormSchema}
            onSubmit={handleSubmit}
            defaultValues={{ phone: '', name: '' }}
            className="space-y-4"
          >
            <FormItem name="name" label="Tên" required>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Nhập tên"
              />
            </FormItem>

            <FormItem name="phone" label="Số điện thoại" required>
              <PhoneInputWithCountry
                placeholder="Nhập số điện thoại"
                fullWidth
                defaultCountry="VN"
              />
            </FormItem>

            <div className="flex gap-2">
              <Button type="submit" variant="primary">
                Submit Form
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  setFormErrors({
                    phone: 'Test error message for phone field',
                  });
                }}
              >
                Test Error
              </Button>
            </div>
          </Form>

          {submittedData && (
            <div className="bg-green-100 dark:bg-green-900 p-3 rounded">
              <Typography variant="body2">
                <strong>Submitted Data:</strong>
              </Typography>
              <pre className="text-sm mt-2">
                {JSON.stringify(submittedData, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </Card>

      {/* Size Variants */}
      <Card>
        <div className="space-y-4">
          <Typography variant="h4">Size Variants</Typography>
          
          <div className="space-y-4">
            <div>
              <Typography variant="body2" className="mb-2">Small:</Typography>
              <PhoneInputWithCountry
                size="sm"
                placeholder="Small size"
                defaultCountry="VN"
              />
            </div>

            <div>
              <Typography variant="body2" className="mb-2">Medium (default):</Typography>
              <PhoneInputWithCountry
                size="md"
                placeholder="Medium size"
                defaultCountry="VN"
              />
            </div>

            <div>
              <Typography variant="body2" className="mb-2">Large:</Typography>
              <PhoneInputWithCountry
                size="lg"
                placeholder="Large size"
                defaultCountry="VN"
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Disabled State */}
      <Card>
        <div className="space-y-4">
          <Typography variant="h4">Disabled State</Typography>
          
          <PhoneInputWithCountry
            value="+84987654321"
            disabled
            placeholder="Disabled input"
            defaultCountry="VN"
            fullWidth
          />
        </div>
      </Card>

      {/* Error State */}
      <Card>
        <div className="space-y-4">
          <Typography variant="h4">Error State</Typography>
          
          <PhoneInputWithCountry
            error="Số điện thoại không hợp lệ"
            placeholder="Input with error"
            defaultCountry="VN"
            fullWidth
          />
        </div>
      </Card>
    </div>
  );
};

export default PhoneInputWithCountryPage;
