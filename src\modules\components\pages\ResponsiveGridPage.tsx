import { useState } from 'react';
import {
  Card,
  ResponsiveGrid,
  Typography,
  Chip,
  IconCard,
  Button,
} from '@/shared/components/common';
import { useChatPanel } from '@/shared/contexts';
import { useTranslation } from 'react-i18next';

const ResponsiveGridPage = () => {
  const { t } = useTranslation();
  const { isChatPanelOpen, setIsChatPanelOpen } = useChatPanel();
  const [currentColumns, setCurrentColumns] = useState(0);

  // Tạo mảng các item demo
  const demoItems = Array.from({ length: 12 }, (_, index) => ({
    id: index + 1,
    title: `Card ${index + 1}`,
    content: `This is the content for card ${index + 1}`,
    color: getRandomColor(),
  }));

  // Hàm tạo màu ngẫu nhiên
  function getRandomColor() {
    const colors = [
      'bg-primary-50 dark:bg-primary-900',
      'bg-success-50 dark:bg-success-900',
      'bg-warning-50 dark:bg-warning-900',
      'bg-danger-50 dark:bg-danger-900',
      'bg-info-50 dark:bg-info-900',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  // Xử lý khi số cột thay đổi
  const handleColumnsChange = (columns: number) => {
    setCurrentColumns(columns);
  };

  return (
    <div className="space-y-8">
      <div className="mb-6">
        <Typography variant="h1" className="mb-2">
          {t('components.responsiveGrid.title')}
        </Typography>
        <Typography variant="body1" className="text-gray-600 dark:text-gray-300">
          {t('components.responsiveGrid.description')}
        </Typography>
      </div>

      <div className="flex items-center justify-between mb-4">
        <div>
          <Chip variant="primary" size="md" className="mr-2">
            {t('components.responsiveGrid.currentColumns')}: {currentColumns}
          </Chip>
          <Chip variant={isChatPanelOpen ? 'success' : 'danger'} size="md">
            {t('components.responsiveGrid.chatPanel')}:{' '}
            {isChatPanelOpen
              ? t('components.responsiveGrid.open')
              : t('components.responsiveGrid.closed')}
          </Chip>
        </div>
        <Button variant="primary" onClick={() => setIsChatPanelOpen(!isChatPanelOpen)}>
          {isChatPanelOpen
            ? t('components.responsiveGrid.closeChatPanel')
            : t('components.responsiveGrid.openChatPanel')}
        </Button>
      </div>

      <Card className="p-6 mb-6">
        <Typography variant="h2" className="mb-4">
          {t('components.responsiveGrid.basic.title')}
        </Typography>
        <Typography variant="body1" className="mb-4">
          {t('components.responsiveGrid.basic.description')}
        </Typography>

        <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <ResponsiveGrid onColumnsChange={handleColumnsChange}>
            {demoItems.slice(0, 6).map(item => (
              <Card key={item.id} className={`p-4 ${item.color}`}>
                <Typography variant="h3" className="mb-2">
                  {item.title}
                </Typography>
                <Typography variant="body2">{item.content}</Typography>
              </Card>
            ))}
          </ResponsiveGrid>
        </div>

        <Card className="p-4 bg-gray-50 dark:bg-gray-800 font-mono text-sm overflow-auto">
          {`<ResponsiveGrid>
  {items.map((item) => (
    <Card key={item.id}>
      <Typography variant="h3">{item.title}</Typography>
      <Typography variant="body2">{item.content}</Typography>
    </Card>
  ))}
</ResponsiveGrid>`}
        </Card>
      </Card>

      <Card className="p-6 mb-6">
        <Typography variant="h2" className="mb-4">
          {t('components.responsiveGrid.customColumns.title')}
        </Typography>
        <Typography variant="body1" className="mb-4">
          {t('components.responsiveGrid.customColumns.description')}
        </Typography>

        <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 1, md: 3, lg: 4, xl: 5 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 4 }}
            gap={{ xs: 2, md: 4, lg: 6 }}
          >
            {demoItems.slice(6, 12).map(item => (
              <Card key={item.id} className={`p-4 ${item.color}`}>
                <Typography variant="h3" className="mb-2">
                  {item.title}
                </Typography>
                <Typography variant="body2">{item.content}</Typography>
              </Card>
            ))}
          </ResponsiveGrid>
        </div>

        <Card className="p-4 bg-gray-50 dark:bg-gray-800 font-mono text-sm overflow-auto">
          {`<ResponsiveGrid
  maxColumns={{ xs: 1, sm: 1, md: 3, lg: 4, xl: 5 }}
  maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 4 }}
  gap={{ xs: 2, md: 4, lg: 6 }}
>
  {items.map((item) => (
    <Card key={item.id}>
      <Typography variant="h3">{item.title}</Typography>
      <Typography variant="body2">{item.content}</Typography>
    </Card>
  ))}
</ResponsiveGrid>`}
        </Card>
      </Card>

      <Card className="p-6 mb-6">
        <Typography variant="h2" className="mb-4">
          {t('components.responsiveGrid.realWorld.title')}
        </Typography>
        <Typography variant="body1" className="mb-4">
          {t('components.responsiveGrid.realWorld.description')}
        </Typography>

        <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
            gap={{ xs: 4, md: 5, lg: 6 }}
          >
            {demoItems.slice(0, 4).map(item => (
              <Card key={item.id} className="p-4">
                <div className="flex flex-row items-start gap-3 overflow-hidden">
                  {/* Avatar */}
                  <div className="relative w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
                    <div className="w-full h-full bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                      <Typography variant="h3">{item.id}</Typography>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex flex-col flex-grow min-w-0">
                    <div className="flex flex-col mb-2">
                      <Typography variant="h3" className="truncate">
                        {item.title}
                      </Typography>
                      <Typography variant="body2" className="text-gray-500">
                        AI Agent
                      </Typography>
                    </div>

                    <div className="mb-3">
                      <Chip variant="primary" size="sm">
                        gpt-4o
                      </Chip>
                    </div>

                    <div className="flex justify-end space-x-4">
                      <IconCard icon="edit" variant="default" size="md" />
                      <IconCard icon="eye" variant="default" size="md" />
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </ResponsiveGrid>
        </div>

        <Card className="p-4 bg-gray-50 dark:bg-gray-800 font-mono text-sm overflow-auto">
          {`<ResponsiveGrid
  maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
  maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
  gap={{ xs: 4, md: 5, lg: 6 }}
>
  {agents.map((agent) => (
    <div key={agent.id} className="h-full">
      <AgentCard agent={agent} />
    </div>
  ))}
</ResponsiveGrid>`}
        </Card>
      </Card>

      <Card className="p-6">
        <Typography variant="h2" className="mb-4">
          {t('components.responsiveGrid.props')}
        </Typography>

        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100 dark:bg-gray-800">
              <th className="p-2 text-left border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.propName')}
              </th>
              <th className="p-2 text-left border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.propType')}
              </th>
              <th className="p-2 text-left border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.propDefault')}
              </th>
              <th className="p-2 text-left border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.propDescription')}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="p-2 border border-gray-200 dark:border-gray-700">children</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">ReactNode</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">-</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.childrenDescription')}
              </td>
            </tr>
            <tr>
              <td className="p-2 border border-gray-200 dark:border-gray-700">gap</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">number | object</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">4</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.gapDescription')}
              </td>
            </tr>
            <tr>
              <td className="p-2 border border-gray-200 dark:border-gray-700">maxColumns</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">object</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                &#123; xs: 1, sm: 2, md: 2, lg: 2, xl: 3 &#125;
              </td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.maxColumnsDescription')}
              </td>
            </tr>
            <tr>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                maxColumnsWithChatPanel
              </td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">object</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                &#123; xs: 1, sm: 1, md: 1, lg: 1, xl: 2 &#125;
              </td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.maxColumnsWithChatPanelDescription')}
              </td>
            </tr>
            <tr>
              <td className="p-2 border border-gray-200 dark:border-gray-700">className</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">string</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">''</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.classNameDescription')}
              </td>
            </tr>
            <tr>
              <td className="p-2 border border-gray-200 dark:border-gray-700">onColumnsChange</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">function</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">-</td>
              <td className="p-2 border border-gray-200 dark:border-gray-700">
                {t('components.responsiveGrid.onColumnsChangeDescription')}
              </td>
            </tr>
          </tbody>
        </table>
      </Card>
    </div>
  );
};

export default ResponsiveGridPage;
