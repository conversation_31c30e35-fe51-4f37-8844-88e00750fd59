/**
 * <PERSON><PERSON> hướng dẫn sử dụng SSE (Server-Sent Events)
 */
import React, { useState, useCallback } from 'react';
import { ComponentDemo } from '../components';
import {
  Button,
  Card,
  Alert,
  Badge,
  Icon,
  SSEStatus,
  SSENotification,
} from '@/shared/components/common';
import {
  useSSE,
  useSSESubscription,
  useSSEConnection,
} from '@/shared/hooks/common';
import { SSEProvider } from '@/shared/contexts/sse';
import type { SSENotificationItem } from '@/shared/components/common/SSENotification';

/**
 * Demo cơ bản với useSSE hook
 */
const BasicSSEDemo: React.FC = () => {
  const sse = useSSE('/api/v1/sse/demo', {
    autoConnect: false,
    debug: true,
    autoReconnect: true,
    reconnectDelay: 3000,
  });

  const [subscriptions, setSubscriptions] = useState<string[]>([]);

  const handleSubscribe = useCallback((eventType: string) => {
    const id = sse.subscribe(eventType, (event) => {
      console.log(`Event ${eventType}:`, event);
    });
    setSubscriptions(prev => [...prev, id]);
  }, [sse]);

  const handleUnsubscribeAll = useCallback(() => {
    subscriptions.forEach(id => sse.unsubscribe(id));
    setSubscriptions([]);
  }, [sse, subscriptions]);

  return (
    <div className="space-y-4">
      <SSEStatus
        connectionInfo={sse.connectionInfo}
        variant="card"
        showDetails={true}
      />

      <div className="flex flex-wrap gap-2">
        <Button onClick={sse.connect} variant="primary" size="sm">
          <Icon name="wifi" size="sm" className="mr-1" />
          Kết nối
        </Button>
        <Button onClick={sse.disconnect} variant="secondary" size="sm">
          <Icon name="wifi-off" size="sm" className="mr-1" />
          Ngắt kết nối
        </Button>
        <Button onClick={sse.clearEvents} variant="outline" size="sm">
          <Icon name="trash-2" size="sm" className="mr-1" />
          Xóa Events
        </Button>
      </div>

      <div className="flex flex-wrap gap-2">
        <Button
          onClick={() => handleSubscribe('test')}
          variant="outline"
          size="sm"
        >
          Subscribe 'test'
        </Button>
        <Button
          onClick={() => handleSubscribe('notification')}
          variant="outline"
          size="sm"
        >
          Subscribe 'notification'
        </Button>
        <Button
          onClick={handleUnsubscribeAll}
          variant="danger"
          size="sm"
          disabled={subscriptions.length === 0}
        >
          Unsubscribe All ({subscriptions.length})
        </Button>
      </div>

      <div>
        <h4 className="font-medium mb-2 flex items-center">
          <Icon name="activity" size="sm" className="mr-1" />
          Events gần đây ({sse.events.length})
        </h4>
        <div className="max-h-40 overflow-y-auto space-y-1">
          {sse.events.slice(-10).map((event, index) => (
            <div key={index} className="text-sm p-2 bg-gray-50 dark:bg-gray-800 rounded">
              <Badge variant="info" className="mr-2">
                {event.type}
              </Badge>
              <span className="text-xs text-gray-500 mr-2">
                {new Date(event.timestamp).toLocaleTimeString()}
              </span>
              {JSON.stringify(event.data)}
            </div>
          ))}
          {sse.events.length === 0 && (
            <div className="text-sm text-gray-500 italic p-2">
              Chưa có events nào. Hãy kết nối và subscribe vào một event type.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Demo SSE Subscription
 */
const SubscriptionDemo: React.FC = () => {
  const subscription = useSSESubscription(
    '/api/v1/sse/demo',
    'notification',
    (event) => {
      console.log('Notification received:', event);
    },
    {
      autoSubscribe: false,
      maxEvents: 20,
      filter: {
        type: 'notification',
      },
    }
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <SSEStatus
          connectionInfo={subscription.connectionInfo}
          variant="inline"
        />
        <Badge variant={subscription.isSubscribed ? 'success' : 'info'}>
          {subscription.isSubscribed ? 'Đã Subscribe' : 'Chưa Subscribe'}
        </Badge>
      </div>

      <div className="flex gap-2">
        <Button onClick={subscription.connect} variant="primary" size="sm">
          Kết nối
        </Button>
        <Button
          onClick={subscription.subscribe}
          variant="outline"
          size="sm"
          disabled={subscription.isSubscribed}
        >
          Subscribe
        </Button>
        <Button
          onClick={subscription.unsubscribe}
          variant="outline"
          size="sm"
          disabled={!subscription.isSubscribed}
        >
          Unsubscribe
        </Button>
      </div>

      <div>
        <h4 className="font-medium mb-2">
          Notifications ({subscription.eventCount})
        </h4>
        {subscription.lastEvent && (
          <Alert
            type="info"
            message={
              <span>
                <strong>Mới nhất:</strong> {JSON.stringify(subscription.lastEvent.data)}
              </span>
            }
            className="mb-2"
          />
        )}
        <div className="max-h-32 overflow-y-auto space-y-1">
          {subscription.events.map((event, index) => (
            <div key={index} className="text-sm p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
              <span className="text-xs text-gray-500 mr-2">
                {new Date(event.timestamp).toLocaleTimeString()}
              </span>
              {JSON.stringify(event.data)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * Demo SSE Connection Management
 */
const ConnectionDemo: React.FC = () => {
  const connection = useSSEConnection('/api/v1/sse/demo', {
    trackMetrics: true,
    autoRetry: true,
    maxRetries: 3,
    onStateChange: (state) => {
      console.log('Connection state changed:', state);
    },
  });

  return (
    <div className="space-y-4">
      <SSEStatus
        connectionInfo={connection.connectionInfo}
        metrics={connection.metrics}
        variant="card"
        showDetails={true}
        showMetrics={true}
      />

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center">
          <Icon name="loader" size="xs" className="mr-1" />
          <span className="font-medium">Đang kết nối:</span>
          <span className="ml-1">{connection.isConnecting ? 'Có' : 'Không'}</span>
        </div>
        <div className="flex items-center">
          <Icon name="wifi" size="xs" className="mr-1" />
          <span className="font-medium">Đã kết nối:</span>
          <span className="ml-1">{connection.isConnected ? 'Có' : 'Không'}</span>
        </div>
        <div className="flex items-center">
          <Icon name="refresh-cw" size="xs" className="mr-1" />
          <span className="font-medium">Đang kết nối lại:</span>
          <span className="ml-1">{connection.isReconnecting ? 'Có' : 'Không'}</span>
        </div>
        <div className="flex items-center">
          <Icon name="alert-circle" size="xs" className="mr-1" />
          <span className="font-medium">Có lỗi:</span>
          <span className="ml-1">{connection.hasError ? 'Có' : 'Không'}</span>
        </div>
        <div className="flex items-center">
          <Icon name="clock" size="xs" className="mr-1" />
          <span className="font-medium">Uptime:</span>
          <span className="ml-1">{Math.floor(connection.getUptime() / 1000)}s</span>
        </div>
        <div className="flex items-center">
          <Icon name="heart" size="xs" className="mr-1" />
          <span className="font-medium">Health:</span>
          <span className="ml-1">{connection.checkHealth() ? 'Tốt' : 'Kém'}</span>
        </div>
      </div>

      <div className="flex gap-2">
        <Button onClick={connection.connect} variant="primary" size="sm">
          Kết nối
        </Button>
        <Button onClick={connection.disconnect} variant="secondary" size="sm">
          Ngắt kết nối
        </Button>
        <Button onClick={connection.retry} variant="outline" size="sm">
          Thử lại
        </Button>
        <Button onClick={connection.reset} variant="danger" size="sm">
          Reset
        </Button>
      </div>

      {connection.lastError && (
        <Alert
          type="error"
          message={
            <span>
              <strong>Lỗi:</strong> {connection.lastError.message}
            </span>
          }
        />
      )}
    </div>
  );
};

/**
 * Demo SSE Notifications
 */
const NotificationDemo: React.FC = () => {
  const [notifications, setNotifications] = useState<SSENotificationItem[]>([]);

  const sse = useSSE('/api/v1/sse/demo', {
    autoConnect: false,
  });

  React.useEffect(() => {
    const subscriptionId = sse.subscribe('notification', (event) => {
      // Type assertion for event.data
      const data = event.data as {
        type?: string;
        title?: string;
        message?: string;
        autoHide?: boolean;
        hideAfter?: number;
        actions?: Array<{ label: string; onClick: () => void; variant?: 'primary' | 'secondary' }>;
      };

      const notification: SSENotificationItem = {
        id: event.id || Date.now().toString(),
        type: (data.type as 'info' | 'success' | 'warning' | 'error') || 'info',
        title: data.title || 'Thông báo',
        message: data.message,
        event,
        timestamp: new Date(),
        autoHide: data.autoHide !== false,
        hideAfter: data.hideAfter || 5000,
        actions: data.actions,
      };

      setNotifications(prev => [...prev, notification]);
    });

    return () => sse.unsubscribe(subscriptionId);
  }, [sse]);

  const handleDismiss = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const handleClearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const addTestNotification = useCallback((type: 'info' | 'success' | 'warning' | 'error') => {
    const notification: SSENotificationItem = {
      id: Date.now().toString(),
      type,
      title: `Thông báo ${type}`,
      message: `Đây là một thông báo ${type} để test.`,
      event: {
        type: 'notification',
        data: { type, title: `Test ${type}`, message: `Test message` },
        timestamp: Date.now(),
      },
      timestamp: new Date(),
      autoHide: true,
      hideAfter: 5000,
    };

    setNotifications(prev => [...prev, notification]);
  }, []);

  return (
    <div className="space-y-4">
      <SSEStatus
        connectionInfo={sse.connectionInfo}
        variant="badge"
      />

      <div className="flex flex-wrap gap-2">
        <Button onClick={sse.connect} variant="primary" size="sm">
          Kết nối SSE
        </Button>
        <Button onClick={() => addTestNotification('info')} variant="outline" size="sm">
          Test Info
        </Button>
        <Button onClick={() => addTestNotification('success')} variant="outline" size="sm">
          Test Success
        </Button>
        <Button onClick={() => addTestNotification('warning')} variant="outline" size="sm">
          Test Warning
        </Button>
        <Button onClick={() => addTestNotification('error')} variant="outline" size="sm">
          Test Error
        </Button>
      </div>

      <div>
        <p className="text-sm text-gray-600">
          Thông báo đang hiển thị: {notifications.length}
        </p>
      </div>

      <SSENotification
        notifications={notifications}
        onDismiss={handleDismiss}
        onClearAll={handleClearAll}
        position="top-right"
        maxVisible={5}
        showTimestamp={true}
        showClearAll={true}
      />
    </div>
  );
};

/**
 * Trang chính SSE
 */
const SSEPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const basicSSECode = `import { useSSE } from '@/shared/hooks/common';

const MyComponent = () => {
  const sse = useSSE('/api/v1/sse/notifications', {
    autoConnect: true,
    autoReconnect: true,
    reconnectDelay: 3000,
  });

  React.useEffect(() => {
    const subscriptionId = sse.subscribe('user_notification', (event) => {
      console.log('Notification:', event.data);
    });

    return () => sse.unsubscribe(subscriptionId);
  }, [sse]);

  return (
    <div>
      <SSEStatus connectionInfo={sse.connectionInfo} />
      <button onClick={sse.connect}>Connect</button>
    </div>
  );
};`;

  const subscriptionCode = `import { useSSESubscription } from '@/shared/hooks/common';

const NotificationComponent = () => {
  const {
    events,
    isSubscribed,
    connectionInfo,
    subscribe,
    unsubscribe,
  } = useSSESubscription(
    '/api/v1/sse/notifications',
    'user_notification',
    (event) => {
      console.log('New notification:', event.data);
    },
    {
      autoSubscribe: true,
      maxEvents: 50,
      filter: {
        data: { userId: getCurrentUserId() }
      }
    }
  );

  return (
    <div>
      <p>Subscribed: {isSubscribed ? 'Yes' : 'No'}</p>
      <p>Events: {events.length}</p>
    </div>
  );
};`;

  return (
    <SSEProvider defaultOptions={{ debug: true }}>
      <div className="p-4 sm:p-6">
        <div className="mb-8">
          <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
            SSE (Server-Sent Events) Components
          </h1>
          <p className="text-muted">
            Bộ tiện ích hoàn chỉnh để làm việc với Server-Sent Events trong React
          </p>
        </div>

        <div className="mb-6">
          <Alert
            type="info"
            message={
              <div className="flex items-start">
                <Icon name="info" size="sm" className="mr-2 mt-0.5" />
                <div>
                  <strong>Lưu ý:</strong> Các demo này sử dụng endpoint giả lập.
                  Trong thực tế, bạn cần có server hỗ trợ SSE để test đầy đủ tính năng.
                </div>
              </div>
            }
          />
        </div>

        <div className="flex flex-wrap gap-1 mb-6 border-b">
          {[
            { id: 'overview', label: 'Tổng quan' },
            { id: 'basic', label: 'Cơ bản' },
            { id: 'subscription', label: 'Subscription' },
            { id: 'connection', label: 'Connection' },
            { id: 'notification', label: 'Notifications' },
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 text-sm font-medium transition-colors border-b-2 ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

          {activeTab === 'overview' && (
            <div className="space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Tính năng chính</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <Icon name="wifi" size="sm" className="mr-2 mt-1 text-green-500" />
                      <div>
                        <strong>Auto-Reconnection</strong>
                        <p className="text-sm text-gray-600">Tự động kết nối lại khi bị ngắt</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Icon name="filter" size="sm" className="mr-2 mt-1 text-blue-500" />
                      <div>
                        <strong>Event Filtering</strong>
                        <p className="text-sm text-gray-600">Lọc events theo pattern</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Icon name="layers" size="sm" className="mr-2 mt-1 text-purple-500" />
                      <div>
                        <strong>Multiple Connections</strong>
                        <p className="text-sm text-gray-600">Quản lý nhiều kết nối SSE</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <Icon name="bell" size="sm" className="mr-2 mt-1 text-orange-500" />
                      <div>
                        <strong>UI Components</strong>
                        <p className="text-sm text-gray-600">Components sẵn sàng sử dụng</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Icon name="activity" size="sm" className="mr-2 mt-1 text-red-500" />
                      <div>
                        <strong>TaskQueue Integration</strong>
                        <p className="text-sm text-gray-600">Tích hợp với hệ thống TaskQueue</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Icon name="shield" size="sm" className="mr-2 mt-1 text-indigo-500" />
                      <div>
                        <strong>TypeScript Support</strong>
                        <p className="text-sm text-gray-600">Type safety đầy đủ</p>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Hooks có sẵn</h3>
                <div className="space-y-3">
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded">
                    <code className="text-sm font-mono">useSSE</code>
                    <p className="text-sm text-gray-600 mt-1">Hook chính để sử dụng SSE</p>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded">
                    <code className="text-sm font-mono">useSSESubscription</code>
                    <p className="text-sm text-gray-600 mt-1">Subscribe vào specific events</p>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded">
                    <code className="text-sm font-mono">useSSEConnection</code>
                    <p className="text-sm text-gray-600 mt-1">Quản lý connection state</p>
                  </div>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded">
                    <code className="text-sm font-mono">useSSEWithTaskQueue</code>
                    <p className="text-sm text-gray-600 mt-1">Tích hợp với TaskQueue</p>
                  </div>
                </div>
              </Card>
            </div>
          )}

          {activeTab === 'basic' && (
            <ComponentDemo
              title="useSSE Hook"
              description="Hook cơ bản để kết nối và quản lý SSE"
              code={basicSSECode}
            >
              <BasicSSEDemo />
            </ComponentDemo>
          )}

          {activeTab === 'subscription' && (
            <ComponentDemo
              title="useSSESubscription Hook"
              description="Hook để subscribe vào specific event types"
              code={subscriptionCode}
            >
              <SubscriptionDemo />
            </ComponentDemo>
          )}

          {activeTab === 'connection' && (
            <ComponentDemo
              title="useSSEConnection Hook"
              description="Hook để quản lý connection state và metrics"
            >
              <ConnectionDemo />
            </ComponentDemo>
          )}

          {activeTab === 'notification' && (
            <ComponentDemo
              title="SSE Notifications"
              description="Component hiển thị notifications từ SSE events"
            >
              <NotificationDemo />
            </ComponentDemo>
          )}
      </div>
    </SSEProvider>
  );
};

export default SSEPage;
