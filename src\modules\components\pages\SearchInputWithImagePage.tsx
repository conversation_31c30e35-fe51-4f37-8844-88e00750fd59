import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Typography, Card, CodeBlock } from '@/shared/components/common';
import SearchInputWithImageExample from '../examples/SearchInputWithImageExample';
import ComponentDemo from '../components/ComponentDemo';

/**
 * Trang hiển thị component SearchInputWithImage
 */
const SearchInputWithImagePage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <Typography variant="h2" className="mb-2">
          {t('components.searchInputWithImage.title', 'SearchInputWithImage')}
        </Typography>
        <Typography className="text-gray-600 dark:text-gray-400">
          {t(
            'components.searchInputWithImage.description',
            'Component input tìm kiếm với khả năng hiển thị hình ảnh và tên trong kết quả'
          )}
        </Typography>
        <div className="mt-4">
          <Link to="/components" className="text-primary hover:text-primary/80">
            &larr; {t('common.backToComponents', 'Quay lại Components')}
          </Link>
        </div>
      </div>

      <ComponentDemo
        title={t('components.searchInputWithImage.basicUsage', 'Sử dụng cơ bản')}
        description={t(
          'components.searchInputWithImage.basicDescription',
          'SearchInputWithImage cho phép người dùng tìm kiếm và chọn từ danh sách các item với hình ảnh và tên.'
        )}
        code={`import { SearchInputWithImage } from '@/shared/components/common';
import { SearchItem } from '@/shared/types/search-input-with-image.types';

// Danh sách người dùng
const users: SearchItem[] = [
  {
    id: 1,
    name: 'Nguyễn Văn A',
    imageUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
    description: 'Frontend Developer',
  },
  {
    id: 2,
    name: 'Trần Thị B',
    imageUrl: 'https://randomuser.me/api/portraits/women/2.jpg',
    description: 'UI/UX Designer',
  },
  // ...
];

// State
const [selectedValue, setSelectedValue] = useState<string | number>('');
const [selectedItem, setSelectedItem] = useState<SearchItem | null>(null);

// Xử lý khi chọn item
const handleChange = (value: string | number, item: SearchItem) => {
  setSelectedValue(value);
  setSelectedItem(item);
};

// Render
<SearchInputWithImage
  label="Chọn người dùng"
  placeholder="Nhập tên người dùng..."
  items={users}
  value={selectedValue}
  onChange={handleChange}
  fullWidth
/>`}
      >
        <SearchInputWithImageExample />
      </ComponentDemo>

      <div className="mt-12">
        <Typography variant="h4" className="mb-4">
          {t('components.searchInputWithImage.api', 'API')}
        </Typography>

        <Card className="p-6 overflow-auto">
          <Typography variant="h5" className="mb-4">
            {t('components.searchInputWithImage.props', 'Props')}
          </Typography>

          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('components.api.name', 'Tên')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('components.api.type', 'Kiểu')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('components.api.default', 'Mặc định')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('components.api.description', 'Mô tả')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>value</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>string | number</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>undefined</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.valueDesc', 'Giá trị đã chọn')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>onChange</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>(value: string | number, item: SearchItem) =&gt; void</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>undefined</code>
                </td>
                <td className="px-6 py-4">
                  {t(
                    'components.searchInputWithImage.onChangeDesc',
                    'Callback khi giá trị thay đổi'
                  )}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>items</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>SearchItem[]</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>[]</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.itemsDesc', 'Danh sách các item')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>placeholder</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>string</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>''</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.placeholderDesc', 'Placeholder cho input')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>label</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>string</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>undefined</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.labelDesc', 'Label cho input')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>disabled</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>boolean</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>false</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.disabledDesc', 'Trạng thái disabled')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>error</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>string</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>undefined</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.errorDesc', 'Thông báo lỗi')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>helperText</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>string</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>undefined</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.helperTextDesc', 'Text hỗ trợ')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>size</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>'sm' | 'md' | 'lg'</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>'md'</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.sizeDesc', 'Kích thước')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>fullWidth</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>boolean</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>false</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.fullWidthDesc', 'Chiều rộng 100%')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>maxResults</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>number</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>5</code>
                </td>
                <td className="px-6 py-4">
                  {t(
                    'components.searchInputWithImage.maxResultsDesc',
                    'Số lượng kết quả tối đa hiển thị'
                  )}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>imageSize</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>'sm' | 'md' | 'lg'</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>'md'</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.imageSizeDesc', 'Kích thước hình ảnh')}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>showSearchIcon</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>boolean</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>true</code>
                </td>
                <td className="px-6 py-4">
                  {t(
                    'components.searchInputWithImage.showSearchIconDesc',
                    'Hiển thị icon tìm kiếm'
                  )}
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>showClearButton</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>boolean</code>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <code>true</code>
                </td>
                <td className="px-6 py-4">
                  {t('components.searchInputWithImage.showClearButtonDesc', 'Hiển thị nút xóa')}
                </td>
              </tr>
            </tbody>
          </table>
        </Card>

        <Card className="p-6 mt-8">
          <Typography variant="h5" className="mb-4">
            {t('components.searchInputWithImage.searchItemType', 'Kiểu dữ liệu SearchItem')}
          </Typography>

          <CodeBlock
            language="typescript"
            code={`interface SearchItem {
  /**
   * ID duy nhất của item
   */
  id: string | number;

  /**
   * Tên hiển thị của item
   */
  name: string;

  /**
   * URL hình ảnh của item
   */
  imageUrl?: string;

  /**
   * Mô tả ngắn của item (hiển thị dưới tên)
   */
  description?: string;

  /**
   * Icon hiển thị thay thế khi không có hình ảnh
   */
  icon?: ReactNode;

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * Dữ liệu tùy chỉnh
   */
  data?: Record<string, unknown>;
}`}
          />
        </Card>
      </div>
    </div>
  );
};

export default SearchInputWithImagePage;
