import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import { Form, FormItem, Button } from '@/shared/components/common';
import { SelectOption } from '@/shared/components/common/Select/Select';
import { SearchableSelect } from '@/shared/components/common/Select';

const SearchableSelectPage: React.FC = () => {
  const { t } = useTranslation();
  const [selectedBank, setSelectedBank] = useState<string | number>('');
  const [selectedCountry, setSelectedCountry] = useState<string | number>('');
  const [selectedFruit, setSelectedFruit] = useState<string | number>('');

  // Danh sách ngân hàng mẫu
  const bankOptions: SelectOption[] = [
    {
      value: 'vietcombank',
      label: 'Vietcombank',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/7/7c/Vietcombank_logo.png',
    },
    {
      value: 'vietinbank',
      label: 'VietinBank',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/8/89/VietinBank_logo.png',
    },
    {
      value: 'bidv',
      label: 'BIDV',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/3e/Logo_BIDV.svg',
    },
    {
      value: 'agribank',
      label: 'Agribank',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/d/d8/Logo_Agribank.svg',
    },
    {
      value: 'techcombank',
      label: 'Techcombank',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/7/7d/Techcombank_logo.svg',
    },
    {
      value: 'vpbank',
      label: 'VPBank',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/f/f3/VPBank_logo.svg',
    },
    {
      value: 'mbbank',
      label: 'MBBank',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/2/25/Logo_MB_new.png',
    },
    {
      value: 'acb',
      label: 'ACB',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/d/d9/Logo_ACB.svg',
    },
    {
      value: 'sacombank',
      label: 'Sacombank',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/6/6e/Sacombank_Logo.svg',
    },
    {
      value: 'vietcapitalbank',
      label: 'VietCapitalBank',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/0/0c/Logo_VietCapitalBank.png',
    },
  ];

  // Danh sách quốc gia mẫu
  const countryOptions: SelectOption[] = [
    { value: 'vn', label: 'Việt Nam', data: { image: 'https://flagcdn.com/w40/vn.png' } },
    { value: 'us', label: 'United States', data: { image: 'https://flagcdn.com/w40/us.png' } },
    { value: 'gb', label: 'United Kingdom', data: { image: 'https://flagcdn.com/w40/gb.png' } },
    { value: 'jp', label: 'Japan', data: { image: 'https://flagcdn.com/w40/jp.png' } },
    { value: 'kr', label: 'South Korea', data: { image: 'https://flagcdn.com/w40/kr.png' } },
    { value: 'cn', label: 'China', data: { image: 'https://flagcdn.com/w40/cn.png' } },
    { value: 'sg', label: 'Singapore', data: { image: 'https://flagcdn.com/w40/sg.png' } },
    { value: 'th', label: 'Thailand', data: { image: 'https://flagcdn.com/w40/th.png' } },
    { value: 'my', label: 'Malaysia', data: { image: 'https://flagcdn.com/w40/my.png' } },
    { value: 'id', label: 'Indonesia', data: { image: 'https://flagcdn.com/w40/id.png' } },
  ];

  // Danh sách trái cây mẫu
  const fruitOptions = [
    { value: 'apple', label: 'Apple' },
    { value: 'banana', label: 'Banana' },
    { value: 'orange', label: 'Orange' },
    { value: 'grape', label: 'Grape' },
    { value: 'watermelon', label: 'Watermelon' },
    { value: 'strawberry', label: 'Strawberry' },
    { value: 'mango', label: 'Mango' },
    { value: 'pineapple', label: 'Pineapple' },
    { value: 'kiwi', label: 'Kiwi' },
    { value: 'peach', label: 'Peach' },
  ];

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.searchableSelect.title', 'Searchable Select')}
        </h1>
        <p className="text-muted">
          {t(
            'components.searchableSelect.description',
            'A select component with search functionality and image support.'
          )}
        </p>
      </div>

      {/* Basic Searchable Select */}
      <ComponentDemo
        title={t('components.searchableSelect.basic.title', 'Basic Searchable Select')}
        description={t(
          'components.searchableSelect.basic.description',
          'A basic searchable select component with text options.'
        )}
        code={`import { SearchableSelect } from '@/shared/components/common';

const fruitOptions = [
  { value: 'apple', label: 'Apple' },
  { value: 'banana', label: 'Banana' },
  { value: 'orange', label: 'Orange' },
  // More options...
];

const [selectedFruit, setSelectedFruit] = useState<string | number>('');

<SearchableSelect
  value={selectedFruit}
  onChange={setSelectedFruit}
  options={fruitOptions}
  placeholder="Select a fruit"
  fullWidth
  inlineSearch={true}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <SearchableSelect
            value={selectedFruit}
            onChange={setSelectedFruit}
            options={fruitOptions}
            placeholder="Select a fruit"
            fullWidth
            inlineSearch={true}
          />
          <div className="mt-4 text-center">
            {selectedFruit ? (
              <p>Selected: {fruitOptions.find(o => o.value === selectedFruit)?.label}</p>
            ) : (
              <p>No fruit selected</p>
            )}
          </div>
        </div>
      </ComponentDemo>

      {/* Searchable Select with Images */}
      <ComponentDemo
        title={t('components.searchableSelect.withImages.title', 'Searchable Select with Images')}
        description={t(
          'components.searchableSelect.withImages.description',
          'A searchable select component with image support.'
        )}
        code={`import { SearchableSelect } from '@/shared/components/common';

const bankOptions = [
  {
    value: 'vietcombank',
    label: 'Vietcombank',
    imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/7/7c/Vietcombank_logo.png',
  },
  {
    value: 'vietinbank',
    label: 'VietinBank',
    imageUrl: 'https://upload.wikimedia.org/wikipedia/vi/8/89/VietinBank_logo.png',
  },
  // More options...
];

const [selectedBank, setSelectedBank] = useState<string | number>('');

<SearchableSelect
  value={selectedBank}
  onChange={setSelectedBank}
  options={bankOptions}
  placeholder="Select a bank"
  fullWidth
  inlineSearch={true}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <SearchableSelect
            value={selectedBank}
            onChange={setSelectedBank}
            options={bankOptions}
            placeholder="Select a bank"
            fullWidth
            inlineSearch={true}
          />
          <div className="mt-4 text-center">
            {selectedBank ? (
              <p>Selected: {bankOptions.find(o => o.value === selectedBank)?.label}</p>
            ) : (
              <p>No bank selected</p>
            )}
          </div>
        </div>
      </ComponentDemo>

      {/* Searchable Select with Data Images */}
      <ComponentDemo
        title={t(
          'components.searchableSelect.withDataImages.title',
          'Searchable Select with Data Images'
        )}
        description={t(
          'components.searchableSelect.withDataImages.description',
          'A searchable select component with images in data property.'
        )}
        code={`import { SearchableSelect } from '@/shared/components/common';

const countryOptions = [
  { value: 'vn', label: 'Việt Nam', data: { image: 'https://flagcdn.com/w40/vn.png' } },
  { value: 'us', label: 'United States', data: { image: 'https://flagcdn.com/w40/us.png' } },
  // More options...
];

const [selectedCountry, setSelectedCountry] = useState<string | number>('');

<SearchableSelect
  value={selectedCountry}
  onChange={setSelectedCountry}
  options={countryOptions}
  placeholder="Select a country"
  fullWidth
  inlineSearch={true}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <SearchableSelect
            value={selectedCountry}
            onChange={setSelectedCountry}
            options={countryOptions}
            placeholder="Select a country"
            fullWidth
            inlineSearch={true}
          />
          <div className="mt-4 text-center">
            {selectedCountry ? (
              <p>Selected: {countryOptions.find(o => o.value === selectedCountry)?.label}</p>
            ) : (
              <p>No country selected</p>
            )}
          </div>
        </div>
      </ComponentDemo>

      {/* Searchable Select in Form */}
      <ComponentDemo
        title={t('components.searchableSelect.inForm.title', 'Searchable Select in Form')}
        description={t(
          'components.searchableSelect.inForm.description',
          'Using searchable select component in a form.'
        )}
        code={`import { Form, FormItem, SearchableSelect, Button } from '@/shared/components/common';

<Form onSubmit={(e) => { e.preventDefault(); console.log('Form submitted'); }}>
  <FormItem label="Select Bank" required>
    <SearchableSelect
      value={selectedBank}
      onChange={setSelectedBank}
      options={bankOptions}
      placeholder="Select a bank"
      fullWidth
      inlineSearch={true}
    />
  </FormItem>

  <Button type="submit" className="mt-4">Submit</Button>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            onSubmit={e => {
              e.preventDefault();
              console.log('Form submitted', { selectedBank });
            }}
          >
            <FormItem label="Select Bank" required>
              <SearchableSelect
                value={selectedBank}
                onChange={setSelectedBank}
                options={bankOptions}
                placeholder="Select a bank"
                fullWidth
                inlineSearch={true}
              />
            </FormItem>

            <Button type="submit" className="mt-4">
              Submit
            </Button>
          </Form>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default SearchableSelectPage;
