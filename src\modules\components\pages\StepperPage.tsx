import React, { useState } from 'react';
import { ComponentDemo } from '../components';
import { Stepper, Typography } from '@/shared/components/common';
import type { StepItem, StepperVariant, StepperColorScheme } from '@/shared/components/common/Stepper';

const StepperPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formStep, setFormStep] = useState(0);

  // Basic steps data
  const basicSteps: StepItem[] = [
    {
      id: 'step1',
      title: 'Thông tin cơ bản',
      titleKey: 'stepper.basic_info',
      description: 'Nhập thông tin cá nhân',
      descriptionKey: 'stepper.basic_info_desc',
      icon: 'user',
    },
    {
      id: 'step2',
      title: '<PERSON><PERSON><PERSON> thực',
      titleKey: 'stepper.verification',
      description: '<PERSON>á<PERSON> thực tài khoản',
      descriptionKey: 'stepper.verification_desc',
      icon: 'lock',
    },
    {
      id: 'step3',
      title: '<PERSON><PERSON><PERSON> thành',
      titleKey: 'stepper.complete',
      description: '<PERSON><PERSON><PERSON> tất đăng ký',
      descriptionKey: 'stepper.complete_desc',
      icon: 'check',
    },
  ];

  // Steps with different statuses
  const statusSteps: StepItem[] = [
    {
      id: 'step1',
      title: 'Đã hoàn thành',
      description: 'Bước này đã được hoàn thành',
      status: 'completed',
      icon: 'check',
    },
    {
      id: 'step2',
      title: 'Đang xử lý',
      description: 'Bước này đang được xử lý',
      status: 'processing',
      icon: 'loading',
    },
    {
      id: 'step3',
      title: 'Lỗi',
      description: 'Bước này có lỗi xảy ra',
      status: 'error',
      icon: 'alert-circle',
    },
    {
      id: 'step4',
      title: 'Đang chờ',
      description: 'Bước này đang chờ xử lý',
      status: 'waiting',
      icon: 'calendar',
    },
    {
      id: 'step5',
      title: 'Đã bỏ qua',
      description: 'Bước này đã được bỏ qua',
      status: 'skipped',
      optional: true,
      icon: 'chevron-right',
    },
  ];

  // Form steps with content
  const formSteps: StepItem[] = [
    {
      id: 'personal',
      title: 'Thông tin cá nhân',
      description: 'Nhập họ tên, email, số điện thoại',
      icon: 'user',
      content: (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Họ và tên</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Nhập họ và tên"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Email</label>
            <input
              type="email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Nhập email"
            />
          </div>
        </div>
      ),
    },
    {
      id: 'address',
      title: 'Địa chỉ',
      description: 'Nhập địa chỉ liên hệ',
      icon: 'map-pin',
      content: (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Địa chỉ</label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              rows={3}
              placeholder="Nhập địa chỉ"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Thành phố</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                <option>Chọn thành phố</option>
                <option>Hà Nội</option>
                <option>TP. Hồ Chí Minh</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Mã bưu điện</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Mã bưu điện"
              />
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'review',
      title: 'Xem lại',
      description: 'Kiểm tra thông tin trước khi gửi',
      icon: 'eye',
      content: (
        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Thông tin đã nhập:</h4>
            <p className="text-sm text-gray-600">
              Vui lòng kiểm tra lại tất cả thông tin trước khi hoàn tất.
            </p>
          </div>
        </div>
      ),
    },
  ];

  const variants: StepperVariant[] = [
    'default',
    'minimal',
    'outlined',
    'filled',
    'dots',
    'arrows',
    'cards',
    'progress',
    'timeline',
    'numbered',
    'icons',
  ];

  const colorSchemes: StepperColorScheme[] = [
    'primary',
    'secondary',
    'success',
    'warning',
    'danger',
    'info',
  ];

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          Stepper Components
        </h1>
        <p className="text-muted">
          Các component Stepper với nhiều kiểu dáng, đa ngôn ngữ và responsive
        </p>
      </div>

      {/* Horizontal Icon Stepper - Like Image */}
      <ComponentDemo
        title="Horizontal Icon Stepper (Dark Theme)"
        description="Stepper ngang với icon tròn như trong ảnh - chỉ có 1 dòng text"
        code={`import { Stepper } from '@/shared/components/common';

const steps = [
  {
    id: 'personal',
    title: 'Thông tin cá nhân',
    icon: 'user',
  },
  {
    id: 'address',
    title: 'Địa chỉ',
    icon: 'map-pin',
  },
  {
    id: 'review',
    title: 'Xem lại',
    icon: 'eye',
  },
];

<div className="bg-slate-800 p-6 rounded-lg">
  <Stepper
    variant="filled"
    steps={steps}
    currentStep={1}
    showStepIcons
    colorScheme="danger"
    size="lg"
    className="stepper-dark-theme"
    showConnector={true}
  />
</div>`}
      >
        <div className="bg-slate-800 p-6 rounded-lg">
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <Stepper
              variant="filled"
              steps={[
                {
                  id: 'personal',
                  title: 'Thông tin cá nhân',
                  icon: 'user',
                },
                {
                  id: 'address',
                  title: 'Địa chỉ',
                  icon: 'map-pin',
                },
                {
                  id: 'review',
                  title: 'Xem lại',
                  icon: 'eye',
                },
              ]}
              currentStep={1}
              showStepIcons
              colorScheme="danger"
              size="lg"
              className="stepper-dark-theme w-full"
              showConnector={true}
              responsive={false}
            />
          </div>
        </div>
      </ComponentDemo>

      {/* Fixed Layout Stepper */}
      <ComponentDemo
        title="Fixed Layout Stepper"
        description="Stepper với layout cố định để tránh icon bị xẹp"
        code={`<div className="bg-slate-800 p-6 rounded-lg">
  <div className="grid grid-cols-3 gap-4 items-center">
    {steps.map((step, index) => (
      <div key={step.id} className="flex items-center justify-center">
        <Stepper
          variant="filled"
          steps={[step]}
          currentStep={index === 1 ? 0 : -1}
          showStepIcons
          colorScheme="danger"
          size="lg"
          className="stepper-dark-theme"
          showConnector={false}
        />
      </div>
    ))}
  </div>
</div>`}
      >
        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="grid grid-cols-3 gap-8 items-center">
            {[
              { id: 'personal', title: 'Thông tin cá nhân', icon: 'user', status: 'completed' },
              { id: 'address', title: 'Địa chỉ', icon: 'map-pin', status: 'processing' },
              { id: 'review', title: 'Xem lại', icon: 'eye', status: 'waiting' },
            ].map((step) => (
              <div key={step.id} className="flex flex-col items-center text-center">
                <div className={`
                  w-16 h-16 rounded-full flex items-center justify-center text-white text-xl mb-3
                  ${step.status === 'completed' ? 'bg-red-500' :
                    step.status === 'processing' ? 'bg-red-500' : 'bg-gray-500'}
                `}>
                  <Typography variant="body1" className="text-white font-medium">
                    {step.icon === 'user' ? '👤' : step.icon === 'map-pin' ? '📍' : '👁️'}
                  </Typography>
                </div>
                <Typography variant="body2" className="text-white font-medium">
                  {step.title}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      </ComponentDemo>

      {/* Alternative Color Schemes */}
      <ComponentDemo
        title="Alternative Color Schemes"
        description="Stepper với các màu sắc khác nhau trên nền tối"
        code={`// Primary color scheme
<div className="bg-slate-800 p-6 rounded-lg">
  <Stepper
    variant="filled"
    colorScheme="primary"
    steps={steps}
    currentStep={1}
    showStepIcons
    size="lg"
    className="stepper-dark-theme"
  />
</div>

// Success color scheme
<div className="bg-slate-800 p-6 rounded-lg">
  <Stepper
    variant="filled"
    colorScheme="success"
    steps={steps}
    currentStep={1}
    showStepIcons
    size="lg"
    className="stepper-dark-theme"
  />
</div>`}
      >
        <div className="space-y-6">
          <div>
            <Typography variant="body2" className="mb-2 font-medium">Primary Color</Typography>
            <div className="bg-slate-800 p-6 rounded-lg">
              <Stepper
                variant="filled"
                steps={[
                  {
                    id: 'personal',
                    title: 'Thông tin cá nhân',
                    icon: 'user',
                  },
                  {
                    id: 'address',
                    title: 'Địa chỉ',
                    icon: 'map-pin',
                  },
                  {
                    id: 'review',
                    title: 'Xem lại',
                    icon: 'eye',
                  },
                ]}
                currentStep={1}
                showStepIcons
                colorScheme="primary"
                size="lg"
                className="stepper-dark-theme"
              />
            </div>
          </div>

          <div>
            <Typography variant="body2" className="mb-2 font-medium">Success Color</Typography>
            <div className="bg-slate-800 p-6 rounded-lg">
              <Stepper
                variant="filled"
                steps={[
                  {
                    id: 'personal',
                    title: 'Thông tin cá nhân',
                    icon: 'user',
                  },
                  {
                    id: 'address',
                    title: 'Địa chỉ',
                    icon: 'map-pin',
                  },
                  {
                    id: 'review',
                    title: 'Xem lại',
                    icon: 'eye',
                  },
                ]}
                currentStep={1}
                showStepIcons
                colorScheme="success"
                size="lg"
                className="stepper-dark-theme"
              />
            </div>
          </div>

          <div>
            <Typography variant="body2" className="mb-2 font-medium">Warning Color</Typography>
            <div className="bg-slate-800 p-6 rounded-lg">
              <Stepper
                variant="filled"
                steps={[
                  {
                    id: 'personal',
                    title: 'Thông tin cá nhân',
                    icon: 'user',
                  },
                  {
                    id: 'address',
                    title: 'Địa chỉ',
                    icon: 'map-pin',
                  },
                  {
                    id: 'review',
                    title: 'Xem lại',
                    icon: 'eye',
                  },
                ]}
                currentStep={1}
                showStepIcons
                colorScheme="warning"
                size="lg"
                className="stepper-dark-theme"
              />
            </div>
          </div>
        </div>
      </ComponentDemo>

      {/* Basic Stepper */}
      <ComponentDemo
        title="Basic Stepper"
        description="Stepper cơ bản với navigation"
        code={`import { Stepper } from '@/shared/components/common';

const steps = [
  {
    id: 'step1',
    title: 'Thông tin cơ bản',
    description: 'Nhập thông tin cá nhân',
    icon: 'user',
  },
  {
    id: 'step2',
    title: 'Xác thực',
    description: 'Xác thực tài khoản',
    icon: 'lock',
  },
  {
    id: 'step3',
    title: 'Hoàn thành',
    description: 'Hoàn tất đăng ký',
    icon: 'check',
  },
];

<Stepper
  steps={steps}
  currentStep={currentStep}
  showNavigation
  onNext={() => setCurrentStep(prev => Math.min(prev + 1, steps.length - 1))}
  onPrevious={() => setCurrentStep(prev => Math.max(prev - 1, 0))}
/>`}
      >
        <Stepper
          steps={basicSteps}
          currentStep={currentStep}
          showNavigation
          showStepIcons
          onNext={() => setCurrentStep(prev => Math.min(prev + 1, basicSteps.length - 1))}
          onPrevious={() => setCurrentStep(prev => Math.max(prev - 1, 0))}
        />
      </ComponentDemo>

      {/* Different Variants */}
      <ComponentDemo
        title="Stepper Variants"
        description="Các kiểu dáng khác nhau của Stepper"
        code={`// Default
<Stepper variant="default" steps={steps} />

// Minimal
<Stepper variant="minimal" steps={steps} />

// Outlined
<Stepper variant="outlined" steps={steps} />

// Filled
<Stepper variant="filled" steps={steps} />

// Dots
<Stepper variant="dots" steps={steps} />

// Cards
<Stepper variant="cards" steps={steps} />`}
      >
        <div className="space-y-8">
          {variants.slice(0, 6).map(variant => (
            <div key={variant}>
              <Typography variant="h6" className="mb-3 capitalize">
                {variant} Variant
              </Typography>
              <Stepper
                variant={variant}
                steps={basicSteps.slice(0, 3)}
                currentStep={1}
                showStepIcons={variant === 'icons'}
                showStepNumbers={variant !== 'dots' && variant !== 'icons'}
              />
            </div>
          ))}
        </div>
      </ComponentDemo>

      {/* Different Sizes */}
      <ComponentDemo
        title="Stepper Sizes"
        description="Các kích thước khác nhau"
        code={`<Stepper size="xs" steps={steps} />
<Stepper size="sm" steps={steps} />
<Stepper size="md" steps={steps} />
<Stepper size="lg" steps={steps} />
<Stepper size="xl" steps={steps} />`}
      >
        <div className="space-y-6">
          {(['xs', 'sm', 'md', 'lg', 'xl'] as const).map(size => (
            <div key={size}>
              <Typography variant="body2" className="mb-2 uppercase font-medium">
                Size: {size}
              </Typography>
              <Stepper
                size={size}
                steps={basicSteps.slice(0, 3)}
                currentStep={1}
                showStepIcons
              />
            </div>
          ))}
        </div>
      </ComponentDemo>

      {/* Color Schemes */}
      <ComponentDemo
        title="Color Schemes"
        description="Các bảng màu khác nhau"
        code={`<Stepper colorScheme="primary" steps={steps} />
<Stepper colorScheme="success" steps={steps} />
<Stepper colorScheme="warning" steps={steps} />
<Stepper colorScheme="danger" steps={steps} />`}
      >
        <div className="space-y-6">
          {colorSchemes.map(colorScheme => (
            <div key={colorScheme}>
              <Typography variant="body2" className="mb-2 capitalize font-medium">
                {colorScheme} Color
              </Typography>
              <Stepper
                colorScheme={colorScheme}
                steps={basicSteps.slice(0, 3)}
                currentStep={1}
                showStepIcons
              />
            </div>
          ))}
        </div>
      </ComponentDemo>

      {/* Status Steps */}
      <ComponentDemo
        title="Step Statuses"
        description="Các trạng thái khác nhau của step"
        code={`const statusSteps = [
  { id: '1', title: 'Đã hoàn thành', status: 'completed' },
  { id: '2', title: 'Đang xử lý', status: 'processing' },
  { id: '3', title: 'Lỗi', status: 'error' },
  { id: '4', title: 'Đang chờ', status: 'waiting' },
  { id: '5', title: 'Đã bỏ qua', status: 'skipped', optional: true },
];

<Stepper steps={statusSteps} showStepIcons />`}
      >
        <Stepper
          steps={statusSteps}
          showStepIcons
          animated
        />
      </ComponentDemo>

      {/* Vertical Stepper */}
      <ComponentDemo
        title="Vertical Stepper"
        description="Stepper theo chiều dọc"
        code={`<Stepper
  orientation="vertical"
  steps={steps}
  currentStep={currentStep}
  showNavigation
/>`}
      >
        <div className="max-w-md">
          <Stepper
            orientation="vertical"
            steps={basicSteps}
            currentStep={1}
            showStepIcons
          />
        </div>
      </ComponentDemo>

      {/* Form Stepper with Content */}
      <ComponentDemo
        title="Form Stepper with Content"
        description="Stepper với nội dung form tích hợp"
        code={`const formSteps = [
  {
    id: 'personal',
    title: 'Thông tin cá nhân',
    content: <PersonalInfoForm />,
  },
  {
    id: 'address',
    title: 'Địa chỉ',
    content: <AddressForm />,
  },
  {
    id: 'review',
    title: 'Xem lại',
    content: <ReviewForm />,
  },
];

<Stepper
  steps={formSteps}
  currentStep={formStep}
  showStepContent
  showNavigation
  onNext={() => setFormStep(prev => prev + 1)}
  onPrevious={() => setFormStep(prev => prev - 1)}
/>`}
      >
        <Stepper
          steps={formSteps}
          currentStep={formStep}
          showStepContent
          showNavigation
          showStepIcons
          onNext={() => setFormStep(prev => Math.min(prev + 1, formSteps.length - 1))}
          onPrevious={() => setFormStep(prev => Math.max(prev - 1, 0))}
          onComplete={() => alert('Form completed!')}
        />
      </ComponentDemo>

      {/* Advanced Variants */}
      <ComponentDemo
        title="Advanced Variants"
        description="Các variant nâng cao"
        code={`// Progress variant
<Stepper variant="progress" steps={steps} />

// Timeline variant
<Stepper variant="timeline" steps={steps} />

// Numbered variant
<Stepper variant="numbered" steps={steps} />

// Icons variant
<Stepper variant="icons" steps={steps} showStepIcons />`}
      >
        <div className="space-y-8">
          {variants.slice(6).map(variant => (
            <div key={variant}>
              <Typography variant="h6" className="mb-3 capitalize">
                {variant} Variant
              </Typography>
              <Stepper
                variant={variant}
                steps={basicSteps.slice(0, 3)}
                currentStep={1}
                showStepIcons={variant === 'icons'}
                showStepNumbers={variant === 'numbered'}
                animated
              />
            </div>
          ))}
        </div>
      </ComponentDemo>

      {/* Interactive Stepper */}
      <ComponentDemo
        title="Interactive Stepper"
        description="Stepper có thể click để chuyển step"
        code={`<Stepper
  steps={steps}
  currentStep={currentStep}
  allowStepClick
  onStepClick={(stepId, stepIndex) => setCurrentStep(stepIndex)}
/>`}
      >
        <Stepper
          steps={basicSteps}
          currentStep={currentStep}
          allowStepClick
          showStepIcons
          onStepClick={(_, stepIndex) => setCurrentStep(stepIndex)}
        />
        <div className="mt-4 text-sm text-muted-foreground">
          Click vào các step để chuyển đổi
        </div>
      </ComponentDemo>

      {/* Real-world Examples */}
      <div className="mt-12">
        <Typography variant="h4" className="mb-6">
          Ví dụ thực tế
        </Typography>

        {/* E-commerce Checkout */}
        <ComponentDemo
          title="E-commerce Checkout Process"
          description="Quy trình thanh toán thương mại điện tử"
          code={`const checkoutSteps = [
  { id: 'cart', title: 'Giỏ hàng', icon: 'shopping-cart' },
  { id: 'shipping', title: 'Thông tin giao hàng', icon: 'map-pin' },
  { id: 'payment', title: 'Thanh toán', icon: 'credit-card' },
  { id: 'confirmation', title: 'Xác nhận', icon: 'check' },
];

<Stepper
  variant="cards"
  colorScheme="success"
  steps={checkoutSteps}
  currentStep={2}
  showStepIcons
/>`}
        >
          <Stepper
            variant="cards"
            colorScheme="success"
            steps={[
              { id: 'cart', title: 'Giỏ hàng', icon: 'shopping-cart' },
              { id: 'shipping', title: 'Thông tin giao hàng', icon: 'map-pin' },
              { id: 'payment', title: 'Thanh toán', icon: 'credit-card' },
              { id: 'confirmation', title: 'Xác nhận', icon: 'check' },
            ]}
            currentStep={2}
            showStepIcons
            animated
          />
        </ComponentDemo>

        {/* Project Management */}
        <ComponentDemo
          title="Project Management Workflow"
          description="Quy trình quản lý dự án"
          code={`<Stepper
  variant="timeline"
  orientation="vertical"
  colorScheme="info"
  steps={projectSteps}
  showStepIcons
/>`}
        >
          <div className="max-w-lg">
            <Stepper
              variant="timeline"
              orientation="vertical"
              colorScheme="info"
              steps={[
                {
                  id: 'planning',
                  title: 'Lập kế hoạch',
                  description: 'Phân tích yêu cầu và lập kế hoạch',
                  status: 'completed',
                  icon: 'document',
                },
                {
                  id: 'design',
                  title: 'Thiết kế',
                  description: 'Thiết kế giao diện và kiến trúc',
                  status: 'completed',
                  icon: 'layout',
                },
                {
                  id: 'development',
                  title: 'Phát triển',
                  description: 'Coding và implementation',
                  status: 'processing',
                  icon: 'code',
                },
                {
                  id: 'testing',
                  title: 'Kiểm thử',
                  description: 'Testing và QA',
                  status: 'waiting',
                  icon: 'check',
                },
                {
                  id: 'deployment',
                  title: 'Triển khai',
                  description: 'Deploy lên production',
                  status: 'waiting',
                  icon: 'upload',
                },
              ]}
              showStepIcons
              animated
            />
          </div>
        </ComponentDemo>

        {/* Account Setup */}
        <ComponentDemo
          title="Account Setup Wizard"
          description="Wizard thiết lập tài khoản"
          code={`<Stepper
  variant="progress"
  colorScheme="primary"
  steps={setupSteps}
  showNavigation
  animated
/>`}
        >
          <Stepper
            variant="progress"
            colorScheme="primary"
            steps={[
              {
                id: 'welcome',
                title: 'Chào mừng',
                description: 'Bắt đầu thiết lập tài khoản',
                icon: 'star',
              },
              {
                id: 'profile',
                title: 'Hồ sơ',
                description: 'Thiết lập thông tin cá nhân',
                icon: 'user',
              },
              {
                id: 'preferences',
                title: 'Tùy chọn',
                description: 'Cấu hình tùy chọn',
                icon: 'settings',
              },
              {
                id: 'complete',
                title: 'Hoàn thành',
                description: 'Tài khoản đã sẵn sàng',
                icon: 'check',
              },
            ]}
            currentStep={1}
            showStepIcons
            showNavigation
            animated
            onNext={() => {}}
            onPrevious={() => {}}
          />
        </ComponentDemo>
      </div>
    </div>
  );
};

export default StepperPage;
