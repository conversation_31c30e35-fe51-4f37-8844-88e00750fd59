import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '@/modules/components/components';
import { Table } from '@/shared/components/common/Table';
import { TableColumn } from '@/shared/components/common/Table/types';

interface User {
  id: number;
  name: string;
  age: number;
  address: string;
  email: string;
  description?: string;
}

const TablePage: React.FC = () => {
  const { t } = useTranslation();

  // State cho chọn hàng
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // State cho mở rộng hàng
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);

  // Dữ liệu mẫu
  const data: User[] = [
    {
      id: 1,
      name: '<PERSON>',
      age: 30,
      address: 'New York',
      email: '<EMAIL>',
      description: '<PERSON> is a software engineer with 5 years of experience.',
    },
    {
      id: 2,
      name: '<PERSON>',
      age: 25,
      address: 'London',
      email: '<EMAIL>',
      description: '<PERSON> is a designer specializing in UI/UX.',
    },
    {
      id: 3,
      name: '<PERSON>',
      age: 40,
      address: 'Paris',
      email: '<EMAIL>',
      description: 'Bob is a project manager with 10 years of experience.',
    },
    {
      id: 4,
      name: 'Alice <PERSON>',
      age: 35,
      address: 'Tokyo',
      email: '<EMAIL>',
      description: 'Alice is a data scientist specializing in machine learning.',
    },
    {
      id: 5,
      name: '<PERSON> <PERSON>',
      age: 28,
      address: 'Sydney',
      email: '<EMAIL>',
      description: 'Charlie is a marketing specialist with expertise in digital marketing.',
    },
  ];

  // Cấu trúc cột
  const columns: TableColumn<User>[] = [
    {
      key: 'name',
      title: 'Tên',
      dataIndex: 'name',
      sortable: true,
    },
    {
      key: 'age',
      title: 'Tuổi',
      dataIndex: 'age',
      sortable: true,
    },
    {
      key: 'address',
      title: 'Địa chỉ',
      dataIndex: 'address',
      sortable: true,
    },
    {
      key: 'email',
      title: 'Email',
      dataIndex: 'email',
    },
    {
      key: 'actions',
      title: 'Hành động',
      render: (_, record) => (
        <div className="flex space-x-2">
          <button
            className="text-blue-500 hover:text-blue-700"
            onClick={() => alert(`Xem ${record.name}`)}
          >
            Xem
          </button>
          <button
            className="text-green-500 hover:text-green-700"
            onClick={() => alert(`Sửa ${record.name}`)}
          >
            Sửa
          </button>
          <button
            className="text-red-500 hover:text-red-700"
            onClick={() => alert(`Xóa ${record.name}`)}
          >
            Xóa
          </button>
        </div>
      ),
    },
  ];

  // Xử lý sự kiện chọn hàng
  const handleSelectionChange = (keys: React.Key[], rows: User[]) => {
    setSelectedRowKeys(keys);
    console.log('Selected rows:', rows);
  };

  // Xử lý sự kiện mở rộng hàng
  const handleExpandedRowsChange = (keys: React.Key[]) => {
    setExpandedRowKeys(keys);
    console.log('Expanded rows:', keys);
  };

  return (
    <div className="space-y-8">
      <h1 className="text-2xl font-bold">{t('components.table.title', 'Bảng (Table)')}</h1>
      <p className="text-gray-600 dark:text-gray-400">
        {t(
          'components.table.description',
          'Component Bảng cung cấp giao diện để hiển thị dữ liệu dạng bảng với nhiều tính năng như sắp xếp, lọc, phân trang, chọn hàng và mở rộng hàng.'
        )}
      </p>

      <ComponentDemo
        title={t('components.table.basic.title', 'Bảng Cơ Bản')}
        description={t(
          'components.table.basic.description',
          'Bảng cơ bản hiển thị dữ liệu dạng bảng.'
        )}
        code={`import { Table } from '@/shared/components/common';

const columns = [
  {
    key: 'name',
    title: 'Tên',
    dataIndex: 'name',
  },
  {
    key: 'age',
    title: 'Tuổi',
    dataIndex: 'age',
  },
  {
    key: 'address',
    title: 'Địa chỉ',
    dataIndex: 'address',
  },
  {
    key: 'email',
    title: 'Email',
    dataIndex: 'email',
  },
];

const data = [
  { id: 1, name: 'John Doe', age: 30, address: 'New York', email: '<EMAIL>' },
  { id: 2, name: 'Jane Smith', age: 25, address: 'London', email: '<EMAIL>' },
  { id: 3, name: 'Bob Johnson', age: 40, address: 'Paris', email: '<EMAIL>' },
];

<Table
  data={data}
  columns={columns}
  rowKey="id"
/>`}
      >
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <Table data={data} columns={columns.filter(col => col.key !== 'actions')} rowKey="id" />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.table.sortFilter.title', 'Bảng Với Sắp Xếp và Lọc')}
        description={t(
          'components.table.sortFilter.description',
          'Bảng hỗ trợ sắp xếp và lọc dữ liệu.'
        )}
        code={`import { Table } from '@/shared/components/common';

const columns = [
  {
    key: 'name',
    title: 'Tên',
    dataIndex: 'name',
    sortable: true,
  },
  {
    key: 'age',
    title: 'Tuổi',
    dataIndex: 'age',
    sortable: true,
  },
  {
    key: 'address',
    title: 'Địa chỉ',
    dataIndex: 'address',
    sortable: true,
  },
];

<Table
  data={data}
  columns={columns}
  sortable
  rowKey="id"
/>`}
      >
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <Table
            data={data}
            columns={columns.filter(col => col.key !== 'actions')}
            sortable
            rowKey="id"
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.table.pagination.title', 'Bảng Với Phân Trang')}
        description={t(
          'components.table.pagination.description',
          'Bảng hỗ trợ phân trang dữ liệu.'
        )}
        code={`import { Table } from '@/shared/components/common';

<Table
  data={data}
  columns={columns}
  pagination
  rowKey="id"
/>`}
      >
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <Table
            data={Array.from({ length: 20 }, (_, index) => ({
              ...data[index % data.length],
              id: index + 1,
            }))}
            columns={columns.filter(col => col.key !== 'actions')}
            pagination
            rowKey="id"
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.table.selection.title', 'Bảng Với Chọn Hàng')}
        description={t('components.table.selection.description', 'Bảng hỗ trợ chọn nhiều hàng.')}
        code={`import { Table } from '@/shared/components/common';
import { useState } from 'react';

const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

const handleSelectionChange = (keys: React.Key[], rows: any[]) => {
  setSelectedRowKeys(keys);
  console.log('Selected rows:', rows);
};

<Table
  data={data}
  columns={columns}
  rowSelection={{
    selectedRowKeys,
    onChange: handleSelectionChange,
  }}
  rowKey="id"
/>`}
      >
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <Table
            data={data}
            columns={columns.filter(col => col.key !== 'actions')}
            rowSelection={{
              selectedRowKeys,
              onChange: handleSelectionChange,
            }}
            rowKey="id"
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.table.expandable.title', 'Bảng Với Mở Rộng Hàng')}
        description={t(
          'components.table.expandable.description',
          'Bảng hỗ trợ mở rộng hàng để hiển thị thêm thông tin.'
        )}
        code={`import { Table } from '@/shared/components/common';
import { useState } from 'react';

const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);

const handleExpandedRowsChange = (keys: React.Key[]) => {
  setExpandedRowKeys(keys);
  console.log('Expanded rows:', keys);
};

<Table
  data={data}
  columns={columns}
  expandable
  expandableConfig={{
    expandedRowKeys,
    onExpandedRowsChange: handleExpandedRowsChange,
    expandedRowRender: (record) => <p>{record.description}</p>,
  }}
  rowKey="id"
/>`}
      >
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <Table
            data={data}
            columns={columns.filter(col => col.key !== 'actions')}
            expandable
            expandableConfig={{
              expandedRowKeys,
              onExpandedRowsChange: handleExpandedRowsChange,
              expandedRowRender: record => <p>{record.description}</p>,
            }}
            rowKey="id"
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.table.complete.title', 'Bảng Đầy Đủ Tính Năng')}
        description={t(
          'components.table.complete.description',
          'Bảng với đầy đủ tính năng: sắp xếp, lọc, phân trang, chọn hàng, mở rộng hàng và hành động.'
        )}
        code={`import { Table } from '@/shared/components/common';
import { useState } from 'react';

const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);

<Table
  data={data}
  columns={columns}
  sortable
  pagination
  rowSelection={{
    selectedRowKeys,
    onChange: handleSelectionChange,
  }}
  expandable
  expandableConfig={{
    expandedRowKeys,
    onExpandedRowsChange: handleExpandedRowsChange,
    expandedRowRender: (record) => <p>{record.description}</p>,
  }}
  rowKey="id"
/>`}
      >
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <Table
            data={data}
            columns={columns}
            sortable
            pagination
            rowSelection={{
              selectedRowKeys,
              onChange: handleSelectionChange,
            }}
            expandable
            expandableConfig={{
              expandedRowKeys,
              onExpandedRowsChange: handleExpandedRowsChange,
              expandedRowRender: record => <p>{record.description}</p>,
            }}
            rowKey="id"
          />
        </div>
      </ComponentDemo>
    </div>
  );
};

export default TablePage;
