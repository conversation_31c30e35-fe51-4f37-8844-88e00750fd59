import React from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import ThemeToggle from '@/shared/components/common/ThemeToggle';
import LanguageFlag from '@/shared/components/common/LanguageFlag';
import { useTheme } from '@/shared/contexts/theme';

const ThemeComponentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { themeMode, toggleTheme } = useTheme();
  const theme = themeMode === 'custom' ? 'light' : themeMode;

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.categories.theme.title')}
        </h1>
        <p className="text-muted">{t('components.categories.theme.description')}</p>
      </div>

      <ComponentDemo
        title={t('components.theme.toggle.title')}
        description={t('components.theme.toggle.description')}
        code={`import ThemeToggle from '@/shared/components/common/ThemeToggle';
import { useTheme } from '@/shared/contexts/theme';

const { themeMode, toggleTheme } = useTheme();
const theme = themeMode === 'custom' ? 'light' : themeMode;

<ThemeToggle
  theme={theme}
  onToggle={toggleTheme}
/>

// Hoặc sử dụng ThemeToggle mà không cần truyền props
<ThemeToggle />`}
      >
        <div className="flex items-center justify-center">
          <ThemeToggle theme={theme} onToggle={toggleTheme} />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.toggleSizes.title')}
        description={t('components.theme.toggleSizes.description')}
        code={`import ThemeToggle from '@/shared/components/common/ThemeToggle';
import { useTheme } from '@/shared/contexts/theme';

const { themeMode, toggleTheme } = useTheme();
const theme = themeMode === 'custom' ? 'light' : themeMode;

<div className="flex items-center space-x-4">
  <ThemeToggle
    theme={theme}
    onToggle={toggleTheme}
    size="sm"
  />
  <ThemeToggle
    theme={theme}
    onToggle={toggleTheme}
    size="md"
  />
  <ThemeToggle
    theme={theme}
    onToggle={toggleTheme}
    size="lg"
  />
</div>`}
      >
        <div className="flex items-center space-x-4">
          <ThemeToggle theme={theme} onToggle={toggleTheme} size="sm" />
          <ThemeToggle theme={theme} onToggle={toggleTheme} size="md" />
          <ThemeToggle theme={theme} onToggle={toggleTheme} size="lg" />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.toggleCustomText.title')}
        description={t('components.theme.toggleCustomText.description')}
        code={`import ThemeToggle from '@/shared/components/common/ThemeToggle';
import { useTheme } from '@/shared/contexts/theme';

const { themeMode, toggleTheme } = useTheme();
const theme = themeMode === 'custom' ? 'light' : themeMode;

<ThemeToggle
  theme={theme}
  onToggle={toggleTheme}
  lightText="Sáng"
  darkText="Tối"
/>`}
      >
        <div className="flex items-center justify-center">
          <ThemeToggle theme={theme} onToggle={toggleTheme} lightText="Sáng" darkText="Tối" />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.languageFlags.title')}
        description={t('components.theme.languageFlags.description')}
        code={`import LanguageFlag from '@/shared/components/common/LanguageFlag';

<div className="flex space-x-4">
  <LanguageFlag code="vi" size="md" />
  <LanguageFlag code="en" size="md" />
  <LanguageFlag code="zh" size="md" />
</div>`}
      >
        <div className="flex space-x-4">
          <LanguageFlag code="vi" size="md" />
          <LanguageFlag code="en" size="md" />
          <LanguageFlag code="zh" size="md" />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.languageFlagSizes.title')}
        description={t('components.theme.languageFlagSizes.description')}
        code={`import LanguageFlag from '@/shared/components/common/LanguageFlag';

<div className="flex items-center space-x-4">
  <LanguageFlag code="vi" size="sm" />
  <LanguageFlag code="vi" size="md" />
  <LanguageFlag code="vi" size="lg" />
</div>`}
      >
        <div className="flex items-center space-x-4">
          <LanguageFlag code="vi" size="sm" />
          <LanguageFlag code="vi" size="md" />
          <LanguageFlag code="vi" size="lg" />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.languageFlagWithLabel.title')}
        description={t('components.theme.languageFlagWithLabel.description')}
        code={`import LanguageFlag from '@/shared/components/common/LanguageFlag';

<div className="flex flex-col space-y-2">
  <LanguageFlag code="vi" showLabel />
  <LanguageFlag code="en" showLabel />
  <LanguageFlag code="zh" showLabel />
</div>`}
      >
        <div className="flex flex-col space-y-2">
          <LanguageFlag code="vi" showLabel />
          <LanguageFlag code="en" showLabel />
          <LanguageFlag code="zh" showLabel />
        </div>
      </ComponentDemo>
    </div>
  );
};

export default ThemeComponentsPage;
