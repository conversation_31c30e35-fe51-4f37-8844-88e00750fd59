import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import {
  ThemeToggle,
  ThemeCustomizer,
  <PERSON><PERSON>,
  Card,
  Typography,
  Modal,
} from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

const ThemeSystemPage: React.FC = () => {
  const { t } = useTranslation();
  const { themeMode, toggleTheme } = useTheme();
  const theme = themeMode === 'custom' ? 'light' : themeMode;
  // Sử dụng các theme hooks

  const [showCustomizer, setShowCustomizer] = useState(false);

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.categories.theme.system.title')}
        </h1>
        <p className="text-muted">{t('components.categories.theme.system.description')}</p>
      </div>

      <ComponentDemo
        title={t('components.theme.toggle.title')}
        description={t('components.theme.toggle.description')}
        code={`import { ThemeToggle } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

// Sử dụng ThemeToggle với useTheme
<ThemeToggle />

// Hoặc truyền theme và toggleTheme từ useTheme
const { themeMode, toggleTheme } = useTheme();
const theme = themeMode === 'custom' ? 'light' : themeMode;

<ThemeToggle
  theme={theme}
  onToggle={toggleTheme}
/>`}
      >
        <div className="flex items-center justify-center">
          <ThemeToggle theme={theme} onToggle={toggleTheme} />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.customizer.title')}
        description={t('components.theme.customizer.description')}
        code={`import { ThemeCustomizer, Button } from '@/shared/components/common';
import { useState } from 'react';

const [showCustomizer, setShowCustomizer] = useState(false);

<div>
  <Button onClick={() => setShowCustomizer(true)}>
    Mở Theme Customizer
  </Button>

  {showCustomizer && (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="max-w-md w-full">
        <ThemeCustomizer
          onClose={() => setShowCustomizer(false)}
        />
      </div>
    </div>
  )}
</div>`}
      >
        <div className="flex flex-col items-center justify-center">
          <Button onClick={() => setShowCustomizer(true)}>
            {t('theme.customizer.open', 'Open Theme Customizer')}
          </Button>

          <Modal
            isOpen={showCustomizer}
            onClose={() => setShowCustomizer(false)}
            title={t('theme.customizer.title', 'Theme Customizer')}
          >
            <ThemeCustomizer onClose={() => setShowCustomizer(false)} />
          </Modal>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.system.variables.title')}
        description={t('components.theme.system.variables.description')}
        code={`// Sử dụng CSS variables trong component
<div className="bg-primary text-primary-foreground p-4 rounded-lg">
  Sử dụng màu primary
</div>

<div className="bg-secondary text-secondary-foreground p-4 rounded-lg">
  Sử dụng màu secondary
</div>

// Các CSS variables có sẵn
--color-background
--color-foreground
--color-muted
--color-border
--color-card
--color-card-muted
--color-primary
--color-primary-foreground
--color-primary-muted
--color-secondary
--color-secondary-foreground
--color-secondary-muted
...`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div className="bg-primary text-primary-foreground p-4 rounded-lg">
              <Typography variant="h6">
                {t('components.theme.system.variables.primaryColor')}
              </Typography>
              <Typography>{t('components.theme.system.variables.primaryDescription')}</Typography>
            </div>

            <div className="bg-secondary text-secondary-foreground p-4 rounded-lg">
              <Typography variant="h6">
                {t('components.theme.system.variables.secondaryColor')}
              </Typography>
              <Typography>{t('components.theme.system.variables.secondaryDescription')}</Typography>
            </div>

            <div className="bg-accent text-accent-foreground p-4 rounded-lg">
              <Typography variant="h6">
                {t('components.theme.system.variables.accentColor')}
              </Typography>
              <Typography>{t('components.theme.system.variables.accentDescription')}</Typography>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-success text-success-foreground p-4 rounded-lg">
              <Typography variant="h6">
                {t('components.theme.system.variables.successColor')}
              </Typography>
              <Typography>{t('components.theme.system.variables.successDescription')}</Typography>
            </div>

            <div className="bg-warning text-warning-foreground p-4 rounded-lg">
              <Typography variant="h6">
                {t('components.theme.system.variables.warningColor')}
              </Typography>
              <Typography>{t('components.theme.system.variables.warningDescription')}</Typography>
            </div>

            <div className="bg-error text-error-foreground p-4 rounded-lg">
              <Typography variant="h6">
                {t('components.theme.system.variables.errorColor')}
              </Typography>
              <Typography>{t('components.theme.system.variables.errorDescription')}</Typography>
            </div>
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.system.cards.title')}
        description={t('components.theme.system.cards.description')}
        code={`import { Card, Typography } from '@/shared/components/common';

<Card className="p-4">
  <Typography variant="h5">Card Title</Typography>
  <Typography>Card content using theme variables</Typography>
</Card>

<div className="bg-card-muted p-4 rounded-lg shadow-md">
  <Typography variant="h5">Muted Card</Typography>
  <Typography>Using card-muted background</Typography>
</div>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="p-4">
            <Typography variant="h5">{t('components.theme.system.cards.title')}</Typography>
            <Typography>{t('components.theme.system.cards.content')}</Typography>
            <div className="mt-4 flex space-x-2">
              <Button variant="primary" size="sm">
                {t('common.primary')}
              </Button>
              <Button variant="secondary" size="sm">
                {t('common.secondary')}
              </Button>
            </div>
          </Card>

          <div className="bg-card-muted p-4 rounded-lg shadow-md">
            <Typography variant="h5">{t('components.theme.system.cards.mutedTitle')}</Typography>
            <Typography>{t('components.theme.system.cards.mutedContent')}</Typography>
            <div className="mt-4 flex space-x-2">
              <Button variant="outline" size="sm">
                {t('common.outline')}
              </Button>
              <Button variant="ghost" size="sm">
                {t('common.ghost')}
              </Button>
            </div>
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.theme.system.typography.title')}
        description={t('components.theme.system.typography.description')}
        code={`import { Typography } from '@/shared/components/common';

<Typography variant="h1">Heading 1</Typography>
<Typography variant="h2">Heading 2</Typography>
<Typography variant="h3">Heading 3</Typography>
<Typography variant="h4">Heading 4</Typography>
<Typography variant="h5">Heading 5</Typography>
<Typography variant="h6">Heading 6</Typography>
<Typography variant="body1">Body 1</Typography>
<Typography variant="body2">Body 2</Typography>
<Typography variant="caption">Caption</Typography>`}
      >
        <div className="space-y-2">
          <Typography variant="h1">Heading 1</Typography>
          <Typography variant="h2">Heading 2</Typography>
          <Typography variant="h3">Heading 3</Typography>
          <Typography variant="h4">Heading 4</Typography>
          <Typography variant="h5">Heading 5</Typography>
          <Typography variant="h6">Heading 6</Typography>
          <Typography variant="body1">
            Body 1 - Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          </Typography>
          <Typography variant="body2">
            Body 2 - Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          </Typography>
          <Typography variant="caption">Caption - Lorem ipsum dolor sit amet</Typography>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default ThemeSystemPage;
