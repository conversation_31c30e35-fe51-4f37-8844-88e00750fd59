import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import { TopCard } from '@/shared/components/widgets';
import { TopCardItem } from '@/shared/components/widgets/TopCard/TopCard.types';
import { ResponsiveGrid } from '@/shared/components/common';

const TopCardPage: React.FC = () => {
  const { t } = useTranslation();
  const [paginationVariant, setPaginationVariant] = useState<
    'default' | 'simple' | 'minimal' | 'text' | 'compact'
  >('default');

  // Dữ liệu mẫu
  const statsItems: TopCardItem[] = [
    {
      title: t('dashboard.stats.users', 'Tổng người dùng'),
      value: '1,950',
      icon: 'users',
      change: '+12%',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.revenue', '<PERSON><PERSON>h thu'),
      value: '45,678,000đ',
      icon: 'chart',
      change: '+18%',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.orders', 'Đơn hàng'),
      value: '324',
      icon: 'shopping-cart',
      change: '+5%',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.conversion', 'Tỷ lệ chuyển đổi'),
      value: '3.2%',
      icon: 'trending-up',
      change: '-0.5%',
      isPositive: false,
    },
  ];

  // Dữ liệu mẫu lớn hơn cho phân trang
  const largeStatsItems: TopCardItem[] = [
    ...statsItems,
    {
      title: t('dashboard.stats.visitors', 'Lượt truy cập'),
      value: '12,450',
      icon: 'users',
      change: '+22%',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.avgTime', 'Thời gian trung bình'),
      value: '3:45',
      icon: 'calendar',
      change: '+0:15',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.bounceRate', 'Tỷ lệ thoát'),
      value: '42%',
      icon: 'trending-down',
      change: '-3%',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.newUsers', 'Người dùng mới'),
      value: '845',
      icon: 'user',
      change: '+15%',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.returningUsers', 'Người dùng quay lại'),
      value: '1,105',
      icon: 'users',
      change: '+8%',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.completedOrders', 'Đơn hàng hoàn thành'),
      value: '287',
      icon: 'check',
      change: '+12',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.pendingOrders', 'Đơn hàng đang xử lý'),
      value: '37',
      icon: 'calendar',
      change: '-5',
      isPositive: true,
    },
    {
      title: t('dashboard.stats.canceledOrders', 'Đơn hàng đã hủy'),
      value: '12',
      icon: 'x',
      change: '-2',
      isPositive: true,
    },
  ];

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.topCard.title', 'TopCard')}
        </h1>
        <p className="text-muted">
          {t(
            'components.topCard.description',
            'Component hiển thị danh sách các card với tiêu đề và giá trị.'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.topCard.basic.title', 'Sử dụng cơ bản')}
        description={t(
          'components.topCard.basic.description',
          'TopCard hiển thị danh sách các card với tiêu đề và giá trị.'
        )}
        code={`import { TopCard } from '@/shared/components/widgets';

const statsItems = [
  {
    title: 'Tổng người dùng',
    value: '1,950',
    icon: 'users',
    change: '+12%',
    isPositive: true,
  },
  {
    title: 'Doanh thu',
    value: '45,678,000đ',
    icon: 'chart',
    change: '+18%',
    isPositive: true,
  },
  {
    title: 'Đơn hàng',
    value: '324',
    icon: 'shopping-cart',
    change: '+5%',
    isPositive: true,
  },
  {
    title: 'Tỷ lệ chuyển đổi',
    value: '3.2%',
    icon: 'trending-up',
    change: '-0.5%',
    isPositive: false,
  },
];

<TopCard
  title="Thống kê tổng quan"
  items={statsItems}
/>`}
      >
        <TopCard title={t('dashboard.overview', 'Thống kê tổng quan')} items={statsItems} />
      </ComponentDemo>

      <ComponentDemo
        title={t('components.topCard.customization.title', 'Tùy chỉnh')}
        description={t(
          'components.topCard.customization.description',
          'TopCard có thể được tùy chỉnh với nhiều tùy chọn khác nhau.'
        )}
        code={`import { TopCard } from '@/shared/components/widgets';

const customItems = [
  {
    title: 'Doanh thu tháng',
    value: '120,450,000đ',
    icon: 'chart',
    iconColor: '#4CAF50',
  },
  {
    title: 'Chi phí',
    value: '45,780,000đ',
    icon: 'credit-card',
    iconColor: '#F44336',
  },
  {
    title: 'Lợi nhuận',
    value: '74,670,000đ',
    icon: 'trending-up',
    iconColor: '#2196F3',
  },
  {
    title: 'ROI',
    value: '162%',
    icon: 'chart',
    iconColor: '#FF9800',
    description: 'Tăng 12% so với quý trước',
  },
];

<TopCard
  title="Báo cáo tài chính"
  items={customItems}
/>`}
      >
        <TopCard
          title={t('dashboard.financial', 'Báo cáo tài chính')}
          items={[
            {
              title: t('dashboard.financial.revenue', 'Doanh thu tháng'),
              value: '120,450,000đ',
              icon: 'chart',
              iconColor: '#4CAF50',
            },
            {
              title: t('dashboard.financial.expenses', 'Chi phí'),
              value: '45,780,000đ',
              icon: 'credit-card',
              iconColor: '#F44336',
            },
            {
              title: t('dashboard.financial.profit', 'Lợi nhuận'),
              value: '74,670,000đ',
              icon: 'trending-up',
              iconColor: '#2196F3',
            },
            {
              title: t('dashboard.financial.roi', 'ROI'),
              value: '162%',
              icon: 'chart',
              iconColor: '#FF9800',
              description: t('dashboard.financial.roiDescription', 'Tăng 12% so với quý trước'),
            },
          ]}
        />
      </ComponentDemo>

      <ComponentDemo
        title={t('components.topCard.pagination.title', 'Phân trang')}
        description={t(
          'components.topCard.pagination.description',
          'TopCard có thể hiển thị phân trang khi có nhiều card.'
        )}
        code={`import { TopCard } from '@/shared/components/widgets';
import { useState } from 'react';

// Dữ liệu mẫu với nhiều items
const largeStatsItems = [
  // ... nhiều items
];

// Sử dụng state để lưu biến thể phân trang
const [paginationVariant, setPaginationVariant] = useState('default');

<div>
  <div className="mb-4">
    <label className="mr-2">Biến thể phân trang:</label>
    <Select
      value={paginationVariant}
      onChange={(e) => setPaginationVariant(e.target.value)}
      options={[
        { value: 'default', label: 'Mặc định' },
        { value: 'simple', label: 'Đơn giản' },
        { value: 'minimal', label: 'Tối giản' },
        { value: 'text', label: 'Văn bản' },
        { value: 'compact', label: 'Nhỏ gọn' },
      ]}
    />
  </div>

  <TopCard
    title="Thống kê chi tiết"
    items={largeStatsItems}
    pagination={true}
    itemsPerPage={4}
    paginationVariant={paginationVariant}
  />
</div>`}
      >
        <div>
          <div className="mb-4 flex items-center">
            <label className="mr-2">
              {t('components.topCard.pagination.variant', 'Biến thể phân trang')}:
            </label>
            <select
              value={paginationVariant}
              onChange={e =>
                setPaginationVariant(
                  e.target.value as 'default' | 'simple' | 'minimal' | 'text' | 'compact'
                )
              }
              className="w-40 p-2 border border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-700"
            >
              <option value="default">{t('components.pagination.default', 'Mặc định')}</option>
              <option value="simple">{t('components.pagination.simple', 'Đơn giản')}</option>
              <option value="minimal">{t('components.pagination.minimal', 'Tối giản')}</option>
              <option value="text">{t('components.pagination.text', 'Văn bản')}</option>
              <option value="compact">{t('components.pagination.compact', 'Nhỏ gọn')}</option>
            </select>
          </div>

          <TopCard
            title={t('dashboard.detailedStats', 'Thống kê chi tiết')}
            items={largeStatsItems}
            pagination={true}
            itemsPerPage={4}
            paginationVariant={paginationVariant}
          />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.topCard.responsive.title', 'Responsive với ResponsiveGrid')}
        description={t(
          'components.topCard.responsive.description',
          'Sử dụng ResponsiveGrid để hiển thị nhiều TopCard theo cách responsive.'
        )}
        code={`import { TopCard } from '@/shared/components/widgets';
import { ResponsiveGrid } from '@/shared/components/common';

// Dữ liệu mẫu
const statsItems = [
  { title: 'Tổng người dùng', value: '1,950', icon: 'users', change: '+12%', isPositive: true },
  { title: 'Doanh thu', value: '45,678,000đ', icon: 'chart', change: '+18%', isPositive: true },
  { title: 'Đơn hàng', value: '324', icon: 'shopping-cart', change: '+5%', isPositive: true },
  { title: 'Tỷ lệ chuyển đổi', value: '3.2%', icon: 'trending-up', change: '-0.5%', isPositive: false },
];

const financialItems = [
  { title: 'Doanh thu tháng', value: '120,450,000đ', icon: 'chart', iconColor: '#4CAF50' },
  { title: 'Chi phí', value: '45,780,000đ', icon: 'credit-card', iconColor: '#F44336' },
  { title: 'Lợi nhuận', value: '74,670,000đ', icon: 'trending-up', iconColor: '#2196F3' },
  { title: 'ROI', value: '162%', icon: 'chart', iconColor: '#FF9800' },
];

<ResponsiveGrid
  maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
  maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
  gap={{ xs: 4, md: 6, lg: 8 }}
>
  <TopCard
    title="Thống kê tổng quan"
    items={statsItems}
  />

  <TopCard
    title="Báo cáo tài chính"
    items={financialItems}
  />

  <TopCard
    title="Thống kê người dùng"
    items={[
      { title: 'Người dùng mới', value: '845', icon: 'user', change: '+15%', isPositive: true },
      { title: 'Người dùng quay lại', value: '1,105', icon: 'users', change: '+8%', isPositive: true },
      { title: 'Tỷ lệ giữ chân', value: '68%', icon: 'trending-up', change: '+2%', isPositive: true },
      { title: 'Thời gian trung bình', value: '3:45', icon: 'clock', change: '+0:15', isPositive: true },
    ]}
  />

  <TopCard
    title="Thống kê đơn hàng"
    items={[
      { title: 'Đơn hàng hoàn thành', value: '287', icon: 'check', change: '+12', isPositive: true },
      { title: 'Đơn hàng đang xử lý', value: '37', icon: 'clock', change: '-5', isPositive: true },
      { title: 'Đơn hàng đã hủy', value: '12', icon: 'x', change: '-2', isPositive: true },
      { title: 'Giá trị trung bình', value: '1,250,000đ', icon: 'chart', change: '+5%', isPositive: true },
    ]}
  />
</ResponsiveGrid>`}
      >
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
          gap={{ xs: 4, md: 6, lg: 8 }}
        >
          <TopCard title={t('dashboard.overview', 'Thống kê tổng quan')} items={statsItems} />

          <TopCard
            title={t('dashboard.financial', 'Báo cáo tài chính')}
            items={[
              {
                title: t('dashboard.financial.revenue', 'Doanh thu tháng'),
                value: '120,450,000đ',
                icon: 'chart',
                iconColor: '#4CAF50',
              },
              {
                title: t('dashboard.financial.expenses', 'Chi phí'),
                value: '45,780,000đ',
                icon: 'credit-card',
                iconColor: '#F44336',
              },
              {
                title: t('dashboard.financial.profit', 'Lợi nhuận'),
                value: '74,670,000đ',
                icon: 'trending-up',
                iconColor: '#2196F3',
              },
              {
                title: t('dashboard.financial.roi', 'ROI'),
                value: '162%',
                icon: 'chart',
                iconColor: '#FF9800',
              },
            ]}
          />

          <TopCard
            title={t('dashboard.userStats', 'Thống kê người dùng')}
            items={[
              {
                title: t('dashboard.stats.newUsers', 'Người dùng mới'),
                value: '845',
                icon: 'user',
                change: '+15%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.returningUsers', 'Người dùng quay lại'),
                value: '1,105',
                icon: 'users',
                change: '+8%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.retentionRate', 'Tỷ lệ giữ chân'),
                value: '68%',
                icon: 'trending-up',
                change: '+2%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.avgTime', 'Thời gian trung bình'),
                value: '3:45',
                icon: 'lock',
                change: '+0:15',
                isPositive: true,
              },
            ]}
          />

          <TopCard
            title={t('dashboard.orderStats', 'Thống kê đơn hàng')}
            items={[
              {
                title: t('dashboard.stats.completedOrders', 'Đơn hàng hoàn thành'),
                value: '287',
                icon: 'check',
                change: '+12',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.pendingOrders', 'Đơn hàng đang xử lý'),
                value: '37',
                icon: 'lock',
                change: '-5',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.canceledOrders', 'Đơn hàng đã hủy'),
                value: '12',
                icon: 'x',
                change: '-2',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.avgOrderValue', 'Giá trị trung bình'),
                value: '1,250,000đ',
                icon: 'chart',
                change: '+5%',
                isPositive: true,
              },
            ]}
          />
        </ResponsiveGrid>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.topCard.responsiveWithPagination.title', 'Responsive với phân trang')}
        description={t(
          'components.topCard.responsiveWithPagination.description',
          'Kết hợp ResponsiveGrid và phân trang để hiển thị nhiều TopCard theo cách responsive.'
        )}
        code={`import { TopCard } from '@/shared/components/widgets';
import { ResponsiveGrid } from '@/shared/components/common';

<ResponsiveGrid
  maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
  maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 1 }}
  gap={{ xs: 4, md: 6, lg: 8 }}
>
  <TopCard
    title="Thống kê chi tiết"
    items={largeStatsItems}
    pagination={true}
    itemsPerPage={4}
    paginationVariant="simple"
  />

  <TopCard
    title="Thống kê người dùng"
    items={userStatsItems}
    pagination={true}
    itemsPerPage={4}
    paginationVariant="minimal"
  />
</ResponsiveGrid>`}
      >
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 1 }}
          gap={{ xs: 4, md: 6, lg: 8 }}
        >
          <TopCard
            title={t('dashboard.detailedStats', 'Thống kê chi tiết')}
            items={largeStatsItems.slice(0, 8)}
            pagination={true}
            itemsPerPage={4}
            paginationVariant="simple"
          />

          <TopCard
            title={t('dashboard.userStats', 'Thống kê người dùng')}
            items={[
              {
                title: t('dashboard.stats.newUsers', 'Người dùng mới'),
                value: '845',
                icon: 'user',
                change: '+15%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.returningUsers', 'Người dùng quay lại'),
                value: '1,105',
                icon: 'users',
                change: '+8%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.retentionRate', 'Tỷ lệ giữ chân'),
                value: '68%',
                icon: 'trending-up',
                change: '+2%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.avgTime', 'Thời gian trung bình'),
                value: '3:45',
                icon: 'lock',
                change: '+0:15',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.bounceRate', 'Tỷ lệ thoát'),
                value: '42%',
                icon: 'trending-down',
                change: '-3%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.visitors', 'Lượt truy cập'),
                value: '12,450',
                icon: 'users',
                change: '+22%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.pageViews', 'Lượt xem trang'),
                value: '45,280',
                icon: 'eye',
                change: '+18%',
                isPositive: true,
              },
              {
                title: t('dashboard.stats.conversionRate', 'Tỷ lệ chuyển đổi'),
                value: '3.8%',
                icon: 'trending-up',
                change: '+0.5%',
                isPositive: true,
              },
            ]}
            pagination={true}
            itemsPerPage={4}
            paginationVariant="minimal"
          />
        </ResponsiveGrid>
      </ComponentDemo>
    </div>
  );
};

export default TopCardPage;
