import React, { useState } from 'react';
import { 
  Calendar,
  MultiSelectCalendar,
  EventCalendar,
  Card,
  Typography,
  Button,
  Badge
} from '@/shared/components/common';
import { addDays, format } from 'date-fns';
import type { CalendarEvent } from '@/shared/components/common';

/**
 * Trang demo đơn giản cho Calendar components
 */
const CalendarDemoSimple: React.FC = () => {

  // States
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [multiDates, setMultiDates] = useState<Date[]>([]);
  const [eventDate, setEventDate] = useState<Date | null>(new Date());

  // Sample events
  const events: CalendarEvent[] = [
    {
      id: '1',
      date: new Date(),
      title: 'Today Meeting',
      color: '#3B82F6',
      type: 'badge'
    },
    {
      id: '2',
      date: addDays(new Date(), 1),
      title: 'Tomorrow Task',
      color: '#10B981',
      type: 'dot'
    },
    {
      id: '3',
      date: addDays(new Date(), 2),
      title: 'Important Event',
      color: '#EF4444',
      type: 'highlight'
    }
  ];

  return (
    <div className="w-full bg-background text-foreground p-6">
      {/* Header */}
      <div className="mb-8 text-center">
        <Typography variant="h1" className="mb-2">
          Calendar Components Demo
        </Typography>
        <Typography variant="body1" className="text-muted-foreground mb-4">
          Simple showcase of calendar components with basic features
        </Typography>
        <div className="flex justify-center flex-wrap gap-2">
          <Badge variant="info">Responsive</Badge>
          <Badge variant="info">Touch Support</Badge>
          <Badge variant="info">Keyboard Navigation</Badge>
          <Badge variant="info">Events</Badge>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card className="p-4 text-center">
          <Typography variant="h2" className="text-primary mb-1">8+</Typography>
          <Typography variant="body2" className="text-muted-foreground">
            Calendar Components
          </Typography>
        </Card>
        <Card className="p-4 text-center">
          <Typography variant="h2" className="text-primary mb-1">20+</Typography>
          <Typography variant="body2" className="text-muted-foreground">
            Features & Props
          </Typography>
        </Card>
        <Card className="p-4 text-center">
          <Typography variant="h2" className="text-primary mb-1">100%</Typography>
          <Typography variant="body2" className="text-muted-foreground">
            TypeScript Support
          </Typography>
        </Card>
      </div>

      {/* Demos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
        {/* Basic Calendar */}
        <Card className="p-6">
          <Typography variant="h3" className="mb-4 text-center">
            Basic Calendar
          </Typography>
          <div className="flex justify-center mb-4">
            <Calendar
              selectedDate={selectedDate}
              onSelectDate={setSelectedDate}
              showToday
              showWeekNumbers
            />
          </div>
          {selectedDate && (
            <div className="text-center text-sm">
              <Typography variant="body2" className="text-muted-foreground">
                Selected: {format(selectedDate, 'dd/MM/yyyy')}
              </Typography>
            </div>
          )}
        </Card>

        {/* Multi-Select Calendar */}
        <Card className="p-6">
          <Typography variant="h3" className="mb-4 text-center">
            Multi-Select Calendar
          </Typography>
          <div className="flex justify-center mb-4">
            <MultiSelectCalendar
              selectedDates={multiDates}
              onSelectDates={setMultiDates}
              maxSelections={5}
              showSelectedCount
              showClearButton
            />
          </div>
          <div className="text-center text-sm">
            <Typography variant="body2" className="text-muted-foreground">
              Selected: {multiDates.length} dates
            </Typography>
          </div>
        </Card>

        {/* Event Calendar */}
        <Card className="p-6">
          <Typography variant="h3" className="mb-4 text-center">
            Event Calendar
          </Typography>
          <div className="flex justify-center mb-4">
            <EventCalendar
              selectedDate={eventDate}
              onSelectDate={setEventDate}
              events={events}
              showEventLegend
            />
          </div>
          <div className="text-center text-sm">
            <Typography variant="body2" className="text-muted-foreground">
              {events.length} events displayed
            </Typography>
          </div>
        </Card>
      </div>

      {/* Features */}
      <Card className="mt-8 p-6">
        <Typography variant="h2" className="mb-6 text-center">
          Key Features
        </Typography>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-primary text-xl">📅</span>
            </div>
            <Typography variant="h4" className="mb-2">Date Selection</Typography>
            <Typography variant="body2" className="text-muted-foreground">
              Single, multiple, and range date selection
            </Typography>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-primary text-xl">🎯</span>
            </div>
            <Typography variant="h4" className="mb-2">Events</Typography>
            <Typography variant="body2" className="text-muted-foreground">
              Display and manage calendar events
            </Typography>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-primary text-xl">📱</span>
            </div>
            <Typography variant="h4" className="mb-2">Responsive</Typography>
            <Typography variant="body2" className="text-muted-foreground">
              Works perfectly on all devices
            </Typography>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-primary text-xl">🎨</span>
            </div>
            <Typography variant="h4" className="mb-2">Customizable</Typography>
            <Typography variant="body2" className="text-muted-foreground">
              Themes, colors, and styling options
            </Typography>
          </div>
        </div>
      </Card>

      {/* Getting Started */}
      <Card className="mt-8 p-6">
        <Typography variant="h2" className="mb-4">
          Getting Started
        </Typography>
        
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4">
          <Typography variant="body2" className="font-mono text-sm">
            {`import { Calendar, MultiSelectCalendar, EventCalendar } from '@/shared/components/common';`}
          </Typography>
        </div>
        
        <Typography variant="body1" className="mb-4">
          All calendar components are fully typed with TypeScript and include comprehensive documentation.
        </Typography>
        
        <div className="flex flex-wrap gap-3">
          <Button
            variant="primary"
            onClick={() => window.open('/components/calendar-showcase', '_blank')}
          >
            View Full Showcase
          </Button>
          <Button
            variant="outline"
            onClick={() => window.open('/datepicker-demo', '_blank')}
          >
            DatePicker Demo
          </Button>
          <Button
            variant="outline"
            onClick={() => window.open('/datepicker-advanced-demo', '_blank')}
          >
            Advanced Examples
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default CalendarDemoSimple;
