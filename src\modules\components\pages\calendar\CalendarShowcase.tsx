import React, { useState } from 'react';
import { ComponentDemo } from '../../components';
import {
  Calendar,
  DatePicker,
  RangePicker,
  MultiSelectCalendar,
  EventCalendar,
  Card,
  Typography,
  Button,
  Badge
} from '@/shared/components/common';
import { addDays } from 'date-fns';
import type { CalendarEvent } from '@/shared/components/common';

/**
 * Trang demo toàn diện cho tất cả Calendar components
 */
const CalendarShowcase: React.FC = () => {

  // States cho các demo
  const [basicDate, setBasicDate] = useState<Date | null>(new Date());
  const [datePickerValue, setDatePickerValue] = useState<Date | null>(new Date());
  const [multiSelectDates, setMultiSelectDates] = useState<Date[]>([]);
  const [eventDate, setEventDate] = useState<Date | null>(new Date());
  const [rangeDate, setRangeDate] = useState<[Date | null, Date | null]>([null, null]);

  // Sample events cho EventCalendar
  const sampleEvents: CalendarEvent[] = [
    {
      id: '1',
      date: new Date(),
      title: 'Meeting with team',
      description: 'Weekly team sync',
      color: '#3B82F6',
      type: 'badge',
      priority: 'high'
    },
    {
      id: '2',
      date: addDays(new Date(), 1),
      title: 'Project deadline',
      color: '#EF4444',
      type: 'dot',
      priority: 'high'
    },
    {
      id: '3',
      date: addDays(new Date(), 3),
      title: 'Client presentation',
      description: 'Present Q4 results',
      color: '#10B981',
      type: 'highlight',
      priority: 'medium'
    }
  ];



  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-8">
        <Typography variant="h1" className="mb-2">
          Calendar Components Showcase
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          Comprehensive demo of all calendar components with advanced features
        </Typography>
        <div className="flex flex-wrap gap-2 mt-4">
          <Badge variant="info">Responsive Design</Badge>
          <Badge variant="info">Touch Gestures</Badge>
          <Badge variant="info">Keyboard Navigation</Badge>
          <Badge variant="info">Accessibility</Badge>
          <Badge variant="info">Event Management</Badge>
          <Badge variant="info">Multi-Selection</Badge>
          <Badge variant="info">Date Ranges</Badge>
        </div>
      </div>

      {/* Basic Calendar */}
      <ComponentDemo
        title="Basic Calendar"
        description="Simple calendar component with date selection"
        code={`import { Calendar } from '@/shared/components/common';
import { useState } from 'react';

const [date, setDate] = useState<Date | null>(new Date());

<Calendar
  selectedDate={date}
  onSelectDate={setDate}
  showToday
  showWeekNumbers
/>`}
      >
        <div className="flex justify-center">
          <Calendar
            selectedDate={basicDate}
            onSelectDate={setBasicDate}
            showToday
            showWeekNumbers
          />
        </div>
      </ComponentDemo>

      {/* DatePicker */}
      <ComponentDemo
        title="DatePicker Input"
        description="Date picker input component with calendar popup"
        code={`import { DatePicker } from '@/shared/components/common';
import { useState } from 'react';

const [date, setDate] = useState<Date | null>(new Date());

<DatePicker
  value={date}
  onChange={setDate}
  placeholder="Select a date"
  format="dd/MM/yyyy"
/>`}
      >
        <div className="flex justify-center">
          <DatePicker
            value={datePickerValue}
            onChange={setDatePickerValue}
            placeholder="Select a date"
            format="dd/MM/yyyy"
          />
        </div>
      </ComponentDemo>

      {/* Range Picker */}
      <ComponentDemo
        title="Range Picker"
        description="Date range picker for selecting start and end dates"
        code={`import { RangePicker } from '@/shared/components/common';
import { useState } from 'react';

const [range, setRange] = useState<[Date | null, Date | null]>([null, null]);

<RangePicker
  value={range}
  onChange={setRange}
  placeholder={['Start date', 'End date']}
  format="dd/MM/yyyy"
/>`}
      >
        <div className="flex justify-center">
          <RangePicker
            value={rangeDate}
            onChange={setRangeDate}
            placeholder={['Start date', 'End date']}
            format="dd/MM/yyyy"
          />
        </div>
      </ComponentDemo>

      {/* Multi-Select Calendar */}
      <ComponentDemo
        title="Multi-Select Calendar"
        description="Calendar that allows selecting multiple dates"
        code={`import { MultiSelectCalendar } from '@/shared/components/common';
import { useState } from 'react';

const [dates, setDates] = useState<Date[]>([]);

<MultiSelectCalendar
  selectedDates={dates}
  onSelectDates={setDates}
  maxSelections={5}
  showSelectedCount
  showClearButton
/>`}
      >
        <div className="flex justify-center">
          <MultiSelectCalendar
            selectedDates={multiSelectDates}
            onSelectDates={setMultiSelectDates}
            maxSelections={5}
            showSelectedCount
            showClearButton
            showTodayButton
          />
        </div>
      </ComponentDemo>

      {/* Event Calendar */}
      <ComponentDemo
        title="Event Calendar"
        description="Calendar with event display and management"
        code={`import { EventCalendar } from '@/shared/components/common';
import { useState } from 'react';

const events = [
  {
    id: '1',
    date: new Date(),
    title: 'Meeting',
    color: '#3B82F6',
    type: 'badge',
    priority: 'high'
  }
];

<EventCalendar
  selectedDate={date}
  onSelectDate={setDate}
  events={events}
  showEventLegend
/>`}
      >
        <div className="flex justify-center">
          <EventCalendar
            selectedDate={eventDate}
            onSelectDate={setEventDate}
            events={sampleEvents}
            showEventLegend
            onDateWithEventsClick={(date, events) => {
              console.log('Date with events clicked:', date, events);
            }}
          />
        </div>
      </ComponentDemo>

      {/* Features Overview */}
      <Card className="mt-8">
        <div className="p-6">
          <Typography variant="h2" className="mb-4">
            Calendar Features Overview
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <Typography variant="h3" className="mb-2 text-primary">
                Core Features
              </Typography>
              <ul className="space-y-1 text-sm">
                <li>• Single & Multi-date selection</li>
                <li>• Date range selection</li>
                <li>• Event management</li>
                <li>• Input components</li>
                <li>• Calendar widgets</li>
                <li>• Custom styling</li>
              </ul>
            </div>

            <div>
              <Typography variant="h3" className="mb-2 text-primary">
                User Experience
              </Typography>
              <ul className="space-y-1 text-sm">
                <li>• Responsive design</li>
                <li>• Touch gestures</li>
                <li>• Keyboard navigation</li>
                <li>• Smooth animations</li>
                <li>• Accessibility support</li>
                <li>• Mobile optimized</li>
              </ul>
            </div>

            <div>
              <Typography variant="h3" className="mb-2 text-primary">
                Customization
              </Typography>
              <ul className="space-y-1 text-sm">
                <li>• Custom styling</li>
                <li>• Event colors</li>
                <li>• Date formats</li>
                <li>• Disabled dates</li>
                <li>• Min/max dates</li>
                <li>• Placeholder text</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
            <Typography variant="h3" className="mb-2">
              Usage Examples
            </Typography>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('/datepicker-demo', '_blank')}
              >
                Basic DatePicker Demo
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('/datepicker-advanced-demo', '_blank')}
              >
                Advanced DatePicker Demo
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('/calendar-demo-simple', '_blank')}
              >
                Simple Calendar Demo
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CalendarShowcase;
