import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Container, Typography } from '@/shared/components/common';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AreaChart } from '@/shared/components/charts';

const ChartDemoPage: React.FC = () => {
  const { t } = useTranslation();

  // Dữ liệu mẫu cho biểu đồ cột
  const barData = [
    { month: t('chart.months.jan', 'Jan'), sales: 4000, profit: 2400, customers: 2400 },
    { month: t('chart.months.feb', 'Feb'), sales: 3000, profit: 1398, customers: 2210 },
    { month: t('chart.months.mar', 'Mar'), sales: 2000, profit: 9800, customers: 2290 },
    { month: t('chart.months.apr', 'Apr'), sales: 2780, profit: 3908, customers: 2000 },
    { month: t('chart.months.may', 'May'), sales: 1890, profit: 4800, customers: 2181 },
    { month: t('chart.months.jun', 'Jun'), sales: 2390, profit: 3800, customers: 2500 },
    { month: t('chart.months.jul', 'Jul'), sales: 3490, profit: 4300, customers: 2100 },
  ];

  // Dữ liệu mẫu cho biểu đồ tròn
  const pieData = [
    { name: t('chart.categories.electronics', 'Electronics'), value: 400 },
    { name: t('chart.categories.clothing', 'Clothing'), value: 300 },
    { name: t('chart.categories.food', 'Food'), value: 300 },
    { name: t('chart.categories.books', 'Books'), value: 200 },
    { name: t('chart.categories.other', 'Other'), value: 100 },
  ];

  // Dữ liệu mẫu cho biểu đồ vùng
  const areaData = [
    { month: t('chart.months.jan', 'Jan'), visits: 4000, pageViews: 2400, uniqueUsers: 1800 },
    { month: t('chart.months.feb', 'Feb'), visits: 3000, pageViews: 1398, uniqueUsers: 1500 },
    { month: t('chart.months.mar', 'Mar'), visits: 2000, pageViews: 9800, uniqueUsers: 1700 },
    { month: t('chart.months.apr', 'Apr'), visits: 2780, pageViews: 3908, uniqueUsers: 2000 },
    { month: t('chart.months.may', 'May'), visits: 1890, pageViews: 4800, uniqueUsers: 1500 },
    { month: t('chart.months.jun', 'Jun'), visits: 2390, pageViews: 3800, uniqueUsers: 1800 },
    { month: t('chart.months.jul', 'Jul'), visits: 3490, pageViews: 4300, uniqueUsers: 2100 },
  ];

  return (
    <Container>
      <Typography variant="h4" className="mb-6">
        {t('components.charts.demo.title', 'Chart Components Demo')}
      </Typography>

      <Typography variant="body1" className="mb-6">
        {t(
          'components.charts.demo.description',
          'Các component biểu đồ hỗ trợ responsive, đa ngôn ngữ, và theme.'
        )}
      </Typography>

      <div className="space-y-8">
        {/* BarChart Demo */}
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('components.charts.barChart.title', 'Bar Chart')}
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <BarChart
              data={barData}
              xAxisKey="month"
              bars={[
                {
                  dataKey: 'sales',
                  name: t('chart.metrics.sales', 'Sales'),
                  color: '#3B82F6',
                  showLabel: true,
                },
                { dataKey: 'profit', name: t('chart.metrics.profit', 'Profit'), color: '#10B981' },
                {
                  dataKey: 'customers',
                  name: t('chart.metrics.customers', 'Customers'),
                  color: '#F59E0B',
                },
              ]}
              xAxisLabel={t('chart.labels.month', 'Month')}
              yAxisLabel={t('chart.labels.amount', 'Amount')}
              height={400}
              showGrid
              showTooltip
              showLegend
              layout="horizontal"
            />
          </div>
        </Card>

        {/* Stacked BarChart Demo */}
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('components.charts.stackedBarChart.title', 'Stacked Bar Chart')}
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <BarChart
              data={barData}
              xAxisKey="month"
              bars={[
                { dataKey: 'sales', name: t('chart.metrics.sales', 'Sales'), color: '#3B82F6' },
                { dataKey: 'profit', name: t('chart.metrics.profit', 'Profit'), color: '#10B981' },
                {
                  dataKey: 'customers',
                  name: t('chart.metrics.customers', 'Customers'),
                  color: '#F59E0B',
                },
              ]}
              xAxisLabel={t('chart.labels.month', 'Month')}
              yAxisLabel={t('chart.labels.amount', 'Amount')}
              height={400}
              showGrid
              showTooltip
              showLegend
              stacked
              layout="horizontal"
            />
          </div>
        </Card>

        {/* PieChart Demo */}
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('components.charts.pieChart.title', 'Pie Chart')}
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <PieChart
              data={pieData}
              slices={[{ nameKey: 'name', valueKey: 'value', showLabel: true }]}
              height={400}
              showTooltip
              showLegend
            />
          </div>
        </Card>

        {/* Doughnut Chart Demo */}
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('components.charts.doughnutChart.title', 'Doughnut Chart')}
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <PieChart
              data={pieData}
              slices={[{ nameKey: 'name', valueKey: 'value' }]}
              innerRadius="60%"
              height={400}
              showTooltip
              showLegend
              centerContent={
                <div className="text-center">
                  <Typography variant="h4" className="font-bold">
                    {pieData.reduce((sum, item) => sum + (item.value as number), 0)}
                  </Typography>
                  <Typography variant="body2" color="muted">
                    {t('chart.labels.total', 'Total')}
                  </Typography>
                </div>
              }
            />
          </div>
        </Card>

        {/* AreaChart Demo */}
        <Card className="p-6" allowOverflow={true}>
          <Typography variant="h5" className="mb-4">
            {t('components.charts.areaChart.title', 'Area Chart')}
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <AreaChart
              data={areaData}
              xAxisKey="month"
              areas={[
                {
                  dataKey: 'visits',
                  name: t('chart.metrics.visits', 'Visits'),
                  strokeColor: '#3B82F6',
                  useGradient: true,
                  gradientStartColor: '#3B82F6',
                  gradientEndColor: 'rgba(59, 130, 246, 0.1)',
                },
                {
                  dataKey: 'pageViews',
                  name: t('chart.metrics.pageViews', 'Page Views'),
                  strokeColor: '#10B981',
                  useGradient: true,
                  gradientStartColor: '#10B981',
                  gradientEndColor: 'rgba(16, 185, 129, 0.1)',
                },
                {
                  dataKey: 'uniqueUsers',
                  name: t('chart.metrics.uniqueUsers', 'Unique Users'),
                  strokeColor: '#F59E0B',
                  useGradient: true,
                  gradientStartColor: '#F59E0B',
                  gradientEndColor: 'rgba(245, 158, 11, 0.1)',
                },
              ]}
              xAxisLabel={t('chart.labels.month', 'Month')}
              yAxisLabel={t('chart.labels.count', 'Count')}
              height={400}
              showGrid
              showTooltip
              showLegend
            />
          </div>
        </Card>

        {/* Stacked AreaChart Demo */}
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('components.charts.stackedAreaChart.title', 'Stacked Area Chart')}
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <AreaChart
              data={areaData}
              xAxisKey="month"
              areas={[
                {
                  dataKey: 'visits',
                  name: t('chart.metrics.visits', 'Visits'),
                  strokeColor: '#3B82F6',
                  fillColor: '#3B82F6',
                  fillOpacity: 0.7,
                },
                {
                  dataKey: 'pageViews',
                  name: t('chart.metrics.pageViews', 'Page Views'),
                  strokeColor: '#10B981',
                  fillColor: '#10B981',
                  fillOpacity: 0.7,
                },
                {
                  dataKey: 'uniqueUsers',
                  name: t('chart.metrics.uniqueUsers', 'Unique Users'),
                  strokeColor: '#F59E0B',
                  fillColor: '#F59E0B',
                  fillOpacity: 0.7,
                },
              ]}
              xAxisLabel={t('chart.labels.month', 'Month')}
              yAxisLabel={t('chart.labels.count', 'Count')}
              height={400}
              showGrid
              showTooltip
              showLegend
              stacked
            />
          </div>
        </Card>
      </div>
    </Container>
  );
};

export default ChartDemoPage;
