import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Container, Typography } from '@/shared/components/common';
import { LineChart, BasicLineChart } from '@/shared/components/charts';
import ComponentDemo from '@/modules/components/components/ComponentDemo';

/**
 * Trang demo cho LineChart component
 */
const LineChartPage: React.FC = () => {
  const { t } = useTranslation();

  // Dữ liệu mẫu cho biểu đồ
  const salesData = [
    { month: 'Jan', sales: 4000, profit: 2400, customers: 2400 },
    { month: 'Feb', sales: 3000, profit: 1398, customers: 2210 },
    { month: 'Mar', sales: 2000, profit: 9800, customers: 2290 },
    { month: 'Apr', sales: 2780, profit: 3908, customers: 2000 },
    { month: 'May', sales: 1890, profit: 4800, customers: 2181 },
    { month: 'Jun', sales: 2390, profit: 3800, customers: 2500 },
    { month: 'Jul', sales: 3490, profit: 4300, customers: 2100 },
  ];

  // Dữ liệu mẫu cho SimpleLineChart
  const simpleData = [
    { name: 'Page A', uv: 4000, pv: 2400, amt: 2400 },
    { name: 'Page B', uv: 3000, pv: 1398, amt: 2210 },
    { name: 'Page C', uv: 2000, pv: 9800, amt: 2290 },
    { name: 'Page D', uv: 2780, pv: 3908, amt: 2000 },
    { name: 'Page E', uv: 1890, pv: 4800, amt: 2181 },
    { name: 'Page F', uv: 2390, pv: 3800, amt: 2500 },
    { name: 'Page G', uv: 3490, pv: 4300, amt: 2100 },
  ];

  // Dữ liệu mẫu cho biểu đồ nhiều đường
  const temperatureData = [
    { day: 'Mon', high: 22, low: 15, avg: 18 },
    { day: 'Tue', high: 24, low: 16, avg: 20 },
    { day: 'Wed', high: 27, low: 18, avg: 22 },
    { day: 'Thu', high: 29, low: 20, avg: 25 },
    { day: 'Fri', high: 25, low: 18, avg: 21 },
    { day: 'Sat', high: 23, low: 16, avg: 19 },
    { day: 'Sun', high: 22, low: 15, avg: 18 },
  ];

  return (
    <Container>
      <Typography variant="h4" className="mb-6">
        {t('components.charts.lineChart.title', 'Line Chart')}
      </Typography>

      <Typography variant="body1" className="mb-6">
        {t(
          'components.charts.lineChart.description',
          'LineChart component hiển thị dữ liệu dạng đường, hỗ trợ nhiều đường dữ liệu, tooltip, và legend.'
        )}
      </Typography>

      <div className="space-y-8">
        {/* Simple Line Chart */}
        <ComponentDemo
          title="Simple Line Chart (Recharts)"
          description="Biểu đồ đường đơn giản sử dụng trực tiếp thư viện Recharts."
          code={`import { BasicLineChart } from '@/shared/components/charts';

const data = [
  { name: 'Page A', uv: 4000, pv: 2400, amt: 2400 },
  { name: 'Page B', uv: 3000, pv: 1398, amt: 2210 },
  { name: 'Page C', uv: 2000, pv: 9800, amt: 2290 },
  { name: 'Page D', uv: 2780, pv: 3908, amt: 2000 },
  { name: 'Page E', uv: 1890, pv: 4800, amt: 2181 },
  { name: 'Page F', uv: 2390, pv: 3800, amt: 2500 },
  { name: 'Page G', uv: 3490, pv: 4300, amt: 2100 },
];

<BasicLineChart data={data} />`}
        >
          <Card className="p-4" allowOverflow={true} style={{ minHeight: '350px' }}>
            <div style={{ height: '300px', width: '100%' }}>
              <BasicLineChart data={simpleData} />
            </div>
          </Card>
        </ComponentDemo>

        {/* Basic Line Chart */}
        <ComponentDemo
          title={t('components.charts.lineChart.basic.title', 'Basic Line Chart')}
          description={t(
            'components.charts.lineChart.basic.description',
            'Biểu đồ đường cơ bản với một đường dữ liệu.'
          )}
          code={`import { LineChart } from '@/shared/components/charts';

const data = [
  { month: 'Jan', sales: 4000 },
  { month: 'Feb', sales: 3000 },
  { month: 'Mar', sales: 2000 },
  { month: 'Apr', sales: 2780 },
  { month: 'May', sales: 1890 },
  { month: 'Jun', sales: 2390 },
  { month: 'Jul', sales: 3490 },
];

<LineChart
  data={data}
  xAxisKey="month"
  lines={[
    { dataKey: 'sales', name: 'Sales', color: '#FF3333' },
  ]}
  height={300}
  showGrid
  showTooltip
  showLegend
/>`}
        >
          <Card className="p-4" allowOverflow={true} style={{ minHeight: '350px' }}>
            <LineChart
              data={salesData}
              xAxisKey="month"
              lines={[{ dataKey: 'sales', name: 'Sales', color: '#FF3333' }]}
              height={300}
              showGrid
              showTooltip
              showLegend
            />
          </Card>
        </ComponentDemo>

        {/* Multi-line Chart */}
        <ComponentDemo
          title={t('components.charts.lineChart.multiLine.title', 'Multi-line Chart')}
          description={t(
            'components.charts.lineChart.multiLine.description',
            'Biểu đồ đường với nhiều đường dữ liệu.'
          )}
          code={`import { LineChart } from '@/shared/components/charts';

const data = [
  { month: 'Jan', sales: 4000, profit: 2400, customers: 2400 },
  { month: 'Feb', sales: 3000, profit: 1398, customers: 2210 },
  { month: 'Mar', sales: 2000, profit: 9800, customers: 2290 },
  { month: 'Apr', sales: 2780, profit: 3908, customers: 2000 },
  { month: 'May', sales: 1890, profit: 4800, customers: 2181 },
  { month: 'Jun', sales: 2390, profit: 3800, customers: 2500 },
  { month: 'Jul', sales: 3490, profit: 4300, customers: 2100 },
];

<LineChart
  data={data}
  xAxisKey="month"
  lines={[
    { dataKey: 'sales', name: 'Sales', color: '#FF3333' },
    { dataKey: 'profit', name: 'Profit', color: '#FFCC99' },
    { dataKey: 'customers', name: 'Customers', color: '#33FF33' },
  ]}
  height={300}
  showGrid
  showTooltip
  showLegend
/>`}
        >
          <Card className="p-4" allowOverflow={true} style={{ minHeight: '350px' }}>
            <LineChart
              data={salesData}
              xAxisKey="month"
              lines={[
                { dataKey: 'sales', name: 'Sales', color: '#FF3333' },
                { dataKey: 'profit', name: 'Profit', color: '#FFCC99' },
                { dataKey: 'customers', name: 'Customers', color: '#33FF33' },
              ]}
              height={300}
              showGrid
              showTooltip
              showLegend
            />
          </Card>
        </ComponentDemo>

        {/* Customized Line Chart */}
        <ComponentDemo
          title={t('components.charts.lineChart.customized.title', 'Customized Line Chart')}
          description={t(
            'components.charts.lineChart.customized.description',
            'Biểu đồ đường với các tùy chỉnh như loại đường, độ dày, và hiển thị điểm dữ liệu.'
          )}
          code={`import { LineChart } from '@/shared/components/charts';

const data = [
  { day: 'Mon', high: 22, low: 15, avg: 18 },
  { day: 'Tue', high: 24, low: 16, avg: 20 },
  { day: 'Wed', high: 27, low: 18, avg: 22 },
  { day: 'Thu', high: 29, low: 20, avg: 25 },
  { day: 'Fri', high: 25, low: 18, avg: 21 },
  { day: 'Sat', high: 23, low: 16, avg: 19 },
  { day: 'Sun', high: 22, low: 15, avg: 18 },
];

<LineChart
  data={data}
  xAxisKey="day"
  lines={[
    { dataKey: 'high', name: 'High Temp', color: '#FF3333', type: 'monotone', strokeWidth: 3, showDot: true },
    { dataKey: 'low', name: 'Low Temp', color: '#3333FF', type: 'monotone', strokeWidth: 3, showDot: true },
    { dataKey: 'avg', name: 'Avg Temp', color: '#33FF33', type: 'monotone', strokeWidth: 2, showDot: false },
  ]}
  height={300}
  showGrid
  showTooltip
  showLegend
  xAxisLabel="Day of Week"
  yAxisLabel="Temperature (°C)"
/>`}
        >
          <Card className="p-4" allowOverflow={true} style={{ minHeight: '350px' }}>
            <LineChart
              data={temperatureData}
              xAxisKey="day"
              lines={[
                {
                  dataKey: 'high',
                  name: 'High Temp',
                  color: '#FF3333',
                  type: 'monotone',
                  strokeWidth: 3,
                  showDot: true,
                },
                {
                  dataKey: 'low',
                  name: 'Low Temp',
                  color: '#3333FF',
                  type: 'monotone',
                  strokeWidth: 3,
                  showDot: true,
                },
                {
                  dataKey: 'avg',
                  name: 'Avg Temp',
                  color: '#33FF33',
                  type: 'monotone',
                  strokeWidth: 2,
                  showDot: false,
                },
              ]}
              height={300}
              showGrid
              showTooltip
              showLegend
            />
          </Card>
        </ComponentDemo>
      </div>
    </Container>
  );
};

export default LineChartPage;
