import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import { Form, FormItem, Input, Button, Card, Icon } from '@/shared/components/common';
import { z } from 'zod';

/**
 * Trang hiển thị cách tích hợp form với API
 */
const ApiIntegrationPage: React.FC = () => {
  const { t } = useTranslation();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [formData, setFormData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [apiResponse, setApiResponse] = useState<any>(null);

  // Basic API form schema
  const apiFormSchema = z.object({
    username: z.string().min(1, t('validation.required', 'Username is required')),
    email: z
      .string()
      .min(1, t('validation.required', 'Email is required'))
      .email(t('validation.email', 'Invalid email format')),
    message: z
      .string()
      .min(10, t('validation.minLength', 'Message must be at least 10 characters')),
  });

  // Mock API call
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mockApiCall = async (data: any): Promise<any> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Simulate API response
    return {
      success: true,
      data: {
        id: Math.floor(Math.random() * 1000),
        ...data,
        timestamp: new Date().toISOString(),
      },
    };
  };

  // Handle form submission
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmit = async (values: any) => {
    setIsLoading(true);
    setFormData(values);

    try {
      // Call mock API
      const response = await mockApiCall(values);
      setApiResponse(response);
    } catch (error) {
      console.error('API error:', error);
      setApiResponse({ success: false, error: 'An error occurred while processing your request.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('components.form.apiIntegration.title', 'API Form Integration')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t(
            'components.form.apiIntegration.description',
            'Learn how to integrate forms with API calls and handle loading states, success, and error responses.'
          )}
        </p>
      </div>

      {/* Basic API Integration */}
      <ComponentDemo
        title={t('components.form.apiIntegration.example1.title', 'Basic API Integration')}
        description={t(
          'components.form.apiIntegration.example1.description',
          'A simple form that submits data to an API and handles loading states.'
        )}
        code={`import { Form, FormItem, Input, Button } from '@/shared/components/common';
import { z } from 'zod';
import { useState } from 'react';

// Define schema
const schema = z.object({
  username: z.string().min(1, 'Username is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

// Define API call function
const submitToApi = async (data) => {
  // In a real app, this would be a fetch or axios call
  const response = await fetch('/api/submit', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error('API request failed');
  }

  return await response.json();
};

// Component with API integration
const ApiForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [apiResponse, setApiResponse] = useState(null);

  const handleSubmit = async (values) => {
    setIsLoading(true);

    try {
      const response = await submitToApi(values);
      setApiResponse(response);
    } catch (error) {
      console.error('API error:', error);
      setApiResponse({ success: false, error: 'An error occurred' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form schema={schema} onSubmit={handleSubmit}>
      <FormItem name="username" label="Username" required>
        <Input placeholder="Enter username" fullWidth />
      </FormItem>

      <FormItem name="email" label="Email" required>
        <Input type="email" placeholder="Enter email" fullWidth />
      </FormItem>

      <FormItem name="message" label="Message" required>
        <Input as="textarea" placeholder="Enter message" rows={4} fullWidth />
      </FormItem>

      <Button type="submit" isLoading={isLoading} disabled={isLoading}>
        {isLoading ? 'Submitting...' : 'Submit'}
      </Button>

      {apiResponse && (
        <div className="mt-4 p-3 bg-green-100 rounded">
          <h4>API Response:</h4>
          <pre>{JSON.stringify(apiResponse, null, 2)}</pre>
        </div>
      )}
    </Form>
  );
};`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form schema={apiFormSchema} onSubmit={handleSubmit} className="space-y-4">
            <FormItem
              name="username"
              label={t('components.form.apiIntegration.fields.username', 'Username')}
              required
            >
              <Input
                placeholder={t(
                  'components.form.apiIntegration.placeholders.username',
                  'Enter username'
                )}
                leftIcon={<Icon name="user" size="sm" />}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="email"
              label={t('components.form.apiIntegration.fields.email', 'Email')}
              required
            >
              <Input
                type="email"
                placeholder={t('components.form.apiIntegration.placeholders.email', 'Enter email')}
                leftIcon={<Icon name="mail" size="sm" />}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="message"
              label={t('components.form.apiIntegration.fields.message', 'Message')}
              required
            >
              <textarea
                className="w-full p-2 border rounded"
                placeholder={t(
                  'components.form.apiIntegration.placeholders.message',
                  'Enter your message here...'
                )}
                rows={4}
              />
            </FormItem>

            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading}
              disabled={isLoading}
              fullWidth
            >
              {isLoading
                ? t('components.form.apiIntegration.buttons.submitting', 'Submitting...')
                : t('components.form.apiIntegration.buttons.submit', 'Submit')}
            </Button>
          </Form>

          {formData && (
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
              <h4 className="font-medium mb-2">
                {t('components.form.apiIntegration.formData', 'Form Data')}:
              </h4>
              <pre className="text-sm overflow-auto">{JSON.stringify(formData, null, 2)}</pre>
            </div>
          )}

          {apiResponse && (
            <div className="mt-4 p-3 bg-green-100 dark:bg-green-800 dark:text-white rounded">
              <h4 className="font-medium mb-2">
                {t('components.form.apiIntegration.apiResponse', 'API Response')}:
              </h4>
              <pre className="text-sm overflow-auto">{JSON.stringify(apiResponse, null, 2)}</pre>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* API Integration Best Practices */}
      <div className="mt-12">
        <Card
          title={t(
            'components.form.apiIntegration.bestPractices.title',
            'API Integration Best Practices'
          )}
          className="mb-6"
        >
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">
                {t(
                  'components.form.apiIntegration.bestPractices.loading.title',
                  'Handle Loading States'
                )}
              </h3>
              <p className="mb-2">
                {t(
                  'components.form.apiIntegration.bestPractices.loading.description',
                  'Always show loading indicators when submitting forms to APIs.'
                )}
              </p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                {`// Show loading state
const [isLoading, setIsLoading] = useState(false);

// In submit handler
setIsLoading(true);
try {
  await apiCall(data);
} finally {
  setIsLoading(false);
}

// In button
<Button type="submit" isLoading={isLoading} disabled={isLoading}>
  {isLoading ? 'Submitting...' : 'Submit'}
</Button>`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">
                {t(
                  'components.form.apiIntegration.bestPractices.errors.title',
                  'Handle API Errors'
                )}
              </h3>
              <p className="mb-2">
                {t(
                  'components.form.apiIntegration.bestPractices.errors.description',
                  'Properly handle and display API errors to users.'
                )}
              </p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                {`// Handle API errors
try {
  const response = await apiCall(data);
  // Handle success
} catch (error) {
  // Handle error
  if (error.response?.data?.errors) {
    // Field-specific errors
    setFormErrors(error.response.data.errors);
  } else {
    // General error
    setGeneralError('An error occurred. Please try again.');
  }
}`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">
                {t(
                  'components.form.apiIntegration.bestPractices.validation.title',
                  'Client-Side and Server-Side Validation'
                )}
              </h3>
              <p className="mb-2">
                {t(
                  'components.form.apiIntegration.bestPractices.validation.description',
                  'Implement both client-side and server-side validation.'
                )}
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>
                  {t(
                    'components.form.apiIntegration.bestPractices.validation.item1',
                    'Use Zod for client-side validation'
                  )}
                </li>
                <li>
                  {t(
                    'components.form.apiIntegration.bestPractices.validation.item2',
                    'Validate data on the server as well'
                  )}
                </li>
                <li>
                  {t(
                    'components.form.apiIntegration.bestPractices.validation.item3',
                    'Display server validation errors in the form'
                  )}
                </li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ApiIntegrationPage;
