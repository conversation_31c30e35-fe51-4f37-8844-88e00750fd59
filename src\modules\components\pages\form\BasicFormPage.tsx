import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import { Form, FormItem, Input, Button, Toggle, Icon, Card } from '@/shared/components/common';
import { z } from 'zod';
import { <PERSON>V<PERSON>ues, SubmitHandler } from 'react-hook-form';

/**
 * Trang hiển thị các form components cơ bản
 */
const BasicFormPage: React.FC = () => {
  const { t } = useTranslation();
  // Basic form schema
  const basicFormSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
  });

  // Infer type from schema
  type BasicFormData = z.infer<typeof basicFormSchema>;

  // Form with validation schema
  const validationFormSchema = z
    .object({
      username: z
        .string()
        .min(3, 'Username must be at least 3 characters')
        .max(20, 'Username must be at most 20 characters'),
      email: z.string().min(1, 'Email is required').email('Invalid email format'),
      password: z
        .string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/[0-9]/, 'Password must contain at least one number'),
      confirmPassword: z.string().min(1, 'Please confirm your password'),
      agreeTerms: z
        .boolean()
        .refine(val => val === true, 'You must agree to the terms and conditions'),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    });

  // Infer type from schema
  type ValidationFormData = z.infer<typeof validationFormSchema>;

  // State to store form data
  const [formData, setFormData] = useState<BasicFormData | ValidationFormData | null>(null);

  // Handle submission for basic form
  const handleBasicFormSubmit: SubmitHandler<FieldValues> = data => {
    const values = data as BasicFormData;
    console.log('Basic form submitted:', values);
    setFormData(values);
  };

  // Handle submission for validation form
  const handleValidationFormSubmit: SubmitHandler<FieldValues> = data => {
    const values = data as ValidationFormData;
    console.log('Validation form submitted:', values);
    setFormData(values);
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.form.basic.title', 'Basic Form Components')}
        </h1>
        <p className="text-muted">
          {t(
            'components.form.basic.description',
            'Learn how to use the basic form components to create forms with validation.'
          )}
        </p>
      </div>

      {/* Basic Form */}
      <ComponentDemo
        title={t('components.form.basic.example1.title', 'Basic Form')}
        description={t(
          'components.form.basic.example1.description',
          'A simple form with basic validation using Zod schema.'
        )}
        code={`import { Form, FormItem, Input, Button } from '@/shared/components/common';
import { z } from 'zod';

// Define schema
const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
});

// Handle form submission
const handleSubmit = (values) => {
  console.log('Form submitted:', values);
};

// Render form
<Form
  schema={schema}
  onSubmit={handleSubmit}
  className="space-y-4"
>
  <FormItem name="name" label="Name" required>
    <Input placeholder="Enter your name" fullWidth />
  </FormItem>

  <FormItem name="email" label="Email" required>
    <Input
      type="email"
      placeholder="Enter your email"
      leftIcon={<Icon name="mail" size="sm" />}
      fullWidth
    />
  </FormItem>

  <Button type="submit">Submit</Button>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form schema={basicFormSchema} onSubmit={handleBasicFormSubmit} className="space-y-4">
            <FormItem name="name" label={t('components.form.basic.fields.name', 'Name')} required>
              <Input
                placeholder={t('components.form.basic.placeholders.name', 'Enter your name')}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="email"
              label={t('components.form.basic.fields.email', 'Email')}
              required
            >
              <Input
                type="email"
                placeholder={t('components.form.basic.placeholders.email', 'Enter your email')}
                leftIcon={<Icon name="mail" size="sm" />}
                fullWidth
              />
            </FormItem>

            <Button type="submit">{t('components.form.basic.buttons.submit', 'Submit')}</Button>
          </Form>

          {formData && (
            <div className="mt-4 p-3 bg-muted/20 rounded">
              <h4 className="font-medium mb-2 text-foreground">
                {t('components.form.basic.result', 'Result')}:
              </h4>
              <pre className="text-sm overflow-auto">{JSON.stringify(formData, null, 2)}</pre>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Form with Advanced Validation */}
      <ComponentDemo
        title={t('components.form.basic.example2.title', 'Form with Advanced Validation')}
        description={t(
          'components.form.basic.example2.description',
          'A form with advanced validation rules using Zod schema.'
        )}
        code={`import { Form, FormItem, Input, Button, Toggle } from '@/shared/components/common';
import { z } from 'zod';

// Define schema with advanced validation
const schema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be at most 20 characters'),
  email: z.string()
    .min(1, 'Email is required')
    .email('Invalid email format'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string()
    .min(1, 'Please confirm your password'),
  agreeTerms: z.boolean()
    .refine(val => val === true, 'You must agree to the terms and conditions'),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

// Render form
<Form schema={schema} onSubmit={handleSubmit} className="space-y-4">
  <FormItem name="username" label="Username" required>
    <Input placeholder="Enter username" fullWidth />
  </FormItem>

  <FormItem name="email" label="Email" required>
    <Input type="email" placeholder="Enter email" fullWidth />
  </FormItem>

  <FormItem name="password" label="Password" required>
    <Input type="password" placeholder="Enter password" fullWidth />
  </FormItem>

  <FormItem name="confirmPassword" label="Confirm Password" required>
    <Input type="password" placeholder="Confirm password" fullWidth />
  </FormItem>

  <FormItem name="agreeTerms" inline helpText="I agree to the terms and conditions">
    <Toggle />
  </FormItem>

  <Button type="submit">Register</Button>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={validationFormSchema}
            onSubmit={handleValidationFormSubmit}
            className="space-y-4"
          >
            <FormItem
              name="username"
              label={t('components.form.basic.fields.username', 'Username')}
              required
            >
              <Input
                placeholder={t('components.form.basic.placeholders.username', 'Enter username')}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="email"
              label={t('components.form.basic.fields.email', 'Email')}
              required
            >
              <Input
                type="email"
                placeholder={t('components.form.basic.placeholders.email', 'Enter email')}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="password"
              label={t('components.form.basic.fields.password', 'Password')}
              required
            >
              <Input
                type="password"
                placeholder={t('components.form.basic.placeholders.password', 'Enter password')}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="confirmPassword"
              label={t('components.form.basic.fields.confirmPassword', 'Confirm Password')}
              required
            >
              <Input
                type="password"
                placeholder={t(
                  'components.form.basic.placeholders.confirmPassword',
                  'Confirm password'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="agreeTerms"
              inline
              helpText={t(
                'components.form.basic.helpTexts.agreeTerms',
                'I agree to the terms and conditions'
              )}
            >
              <Toggle />
            </FormItem>

            <Button type="submit">{t('components.form.basic.buttons.register', 'Register')}</Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* Form Components Reference */}
      <div className="mt-12">
        <Card
          title={t('components.form.basic.reference.title', 'Form Components Reference')}
          className="mb-6"
        >
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2 text-foreground">
                {t('components.form.basic.reference.form.title', 'Form Component')}
              </h3>
              <p className="mb-2 text-muted">
                {t(
                  'components.form.basic.reference.form.description',
                  'The main component that manages form state and validation.'
                )}
              </p>
              <pre className="bg-muted/20 p-3 rounded text-sm overflow-auto">
                {`<Form
  schema={zodSchema}
  onSubmit={handleSubmit}
  defaultValues={{ name: 'Default value' }}
  className="space-y-4"
>
  {/* Form content */}
</Form>`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-foreground">
                {t('components.form.basic.reference.formItem.title', 'FormItem Component')}
              </h3>
              <p className="mb-2 text-muted">
                {t(
                  'components.form.basic.reference.formItem.description',
                  'Wraps input fields with labels, error messages, and help text.'
                )}
              </p>
              <pre className="bg-muted/20 p-3 rounded text-sm overflow-auto">
                {`<FormItem
  name="fieldName"
  label="Field Label"
  required
  helpText="Help text for this field"
>
  <Input placeholder="Enter value" />
</FormItem>`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-foreground">
                {t('components.form.basic.reference.input.title', 'Input Component')}
              </h3>
              <p className="mb-2 text-muted">
                {t(
                  'components.form.basic.reference.input.description',
                  'Basic input component with various options.'
                )}
              </p>
              <pre className="bg-muted/20 p-3 rounded text-sm overflow-auto">
                {`<Input
  type="text|email|password|number|etc"
  placeholder="Placeholder text"
  leftIcon={<Icon name="mail" size="sm" />}
  rightIcon={<Icon name="search" size="sm" />}
  fullWidth
  disabled
/>`}
              </pre>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default BasicFormPage;
