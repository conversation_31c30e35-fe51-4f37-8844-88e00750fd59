import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import {
  Checkbox,
  CheckboxGroup,
  Radio,
  RadioGroup,
  Card,
  Typography,
} from '@/shared/components/common';

/**
 * Trang demo cho Checkbox và Radio components
 */
const CheckboxRadioDemo: React.FC = () => {
  const { t } = useTranslation();

  // State cho các ví dụ
  const [singleCheckbox, setSingleCheckbox] = useState(false);
  const [indeterminateCheckbox, setIndeterminateCheckbox] = useState(false);
  const [checkboxGroupValues, setCheckboxGroupValues] = useState<string[]>(['option1']);
  const [radioValue, setRadioValue] = useState<string>('option1');
  const [horizontalRadioValue, setHorizontalRadioValue] = useState<string>('option1');
  const [sizeRadioValue, setSizeRadioValue] = useState<string>('option2');

  // Wrapper functions để xử lý type compatibility
  const handleCheckboxGroupChange = useCallback(
    (values: Array<string | number | readonly string[]>) => {
      setCheckboxGroupValues(values.map(v => String(v)) as string[]);
    },
    []
  );

  const handleRadioChange = useCallback((value: string | number | readonly string[]) => {
    setRadioValue(String(value));
  }, []);

  const handleHorizontalRadioChange = useCallback((value: string | number | readonly string[]) => {
    setHorizontalRadioValue(String(value));
  }, []);

  const handleSizeRadioChange = useCallback((value: string | number | readonly string[]) => {
    setSizeRadioValue(String(value));
  }, []);

  // Options cho CheckboxGroup
  const checkboxOptions = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3 (Disabled)', value: 'option3', disabled: true },
  ];

  // Options cho RadioGroup
  const radioOptions = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3 (Disabled)', value: 'option3', disabled: true },
  ];

  // Options cho RadioGroup với kích thước khác nhau
  const sizeRadioOptions = [
    { label: 'Small', value: 'option1' },
    { label: 'Medium', value: 'option2' },
    { label: 'Large', value: 'option3' },
  ];

  return (
    <div className="space-y-8 pb-16">
      <Typography variant="h1">Checkbox & Radio</Typography>
      <Typography>
        Checkbox và Radio components cho phép người dùng chọn một hoặc nhiều tùy chọn từ một danh
        sách.
      </Typography>

      {/* Basic Checkbox */}
      <ComponentDemo
        title={t('components.inputs.checkbox.basic.title', 'Basic Checkbox')}
        description={t(
          'components.inputs.checkbox.basic.description',
          'Checkbox cơ bản với label.'
        )}
        code={`import { Checkbox } from '@/shared/components/common';
import { useState } from 'react';

// State
const [checked, setChecked] = useState(false);

// Render
<Checkbox
  label="Basic Checkbox"
  checked={checked}
  onChange={setChecked}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Checkbox label="Basic Checkbox" checked={singleCheckbox} onChange={setSingleCheckbox} />
          <div className="mt-2 text-sm text-gray-500">
            Current value: {singleCheckbox ? 'Checked' : 'Unchecked'}
          </div>
        </div>
      </ComponentDemo>

      {/* Checkbox States */}
      <ComponentDemo
        title={t('components.inputs.checkbox.states.title', 'Checkbox States')}
        description={t(
          'components.inputs.checkbox.states.description',
          'Các trạng thái khác nhau của Checkbox.'
        )}
        code={`import { Checkbox } from '@/shared/components/common';

<div className="space-y-2">
  <Checkbox label="Default Checkbox" />
  <Checkbox label="Checked Checkbox" checked={true} />
  <Checkbox label="Disabled Checkbox" disabled />
  <Checkbox label="Disabled Checked Checkbox" checked disabled />
  <Checkbox label="Indeterminate Checkbox" indeterminate />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-2">
          <Checkbox label="Default Checkbox" />
          <Checkbox label="Checked Checkbox" checked />
          <Checkbox label="Disabled Checkbox" disabled />
          <Checkbox label="Disabled Checked Checkbox" checked disabled />
          <Checkbox
            label="Indeterminate Checkbox"
            indeterminate
            checked={indeterminateCheckbox}
            onChange={setIndeterminateCheckbox}
          />
        </div>
      </ComponentDemo>

      {/* Checkbox Sizes */}
      <ComponentDemo
        title={t('components.inputs.checkbox.sizes.title', 'Checkbox Sizes')}
        description={t(
          'components.inputs.checkbox.sizes.description',
          'Checkbox với các kích thước khác nhau.'
        )}
        code={`import { Checkbox } from '@/shared/components/common';

<div className="space-y-2">
  <Checkbox label="Small Checkbox" size="sm" />
  <Checkbox label="Medium Checkbox (Default)" size="md" />
  <Checkbox label="Large Checkbox" size="lg" />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-2">
          <Checkbox label="Small Checkbox" size="sm" />
          <Checkbox label="Medium Checkbox (Default)" size="md" />
          <Checkbox label="Large Checkbox" size="lg" />
        </div>
      </ComponentDemo>

      {/* Checkbox Group */}
      <ComponentDemo
        title={t('components.inputs.checkbox.group.title', 'Checkbox Group')}
        description={t(
          'components.inputs.checkbox.group.description',
          'CheckboxGroup cho phép chọn nhiều tùy chọn từ một danh sách.'
        )}
        code={`import { CheckboxGroup } from '@/shared/components/common';
import { useState } from 'react';

// State
const [values, setValues] = useState(['option1']);

// Options
const options = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
  { label: 'Option 3 (Disabled)', value: 'option3', disabled: true },
];

// Render
<CheckboxGroup
  options={options}
  value={values}
  onChange={setValues}
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <CheckboxGroup
            options={checkboxOptions}
            value={checkboxGroupValues}
            onChange={handleCheckboxGroupChange}
          />
          <div className="mt-2 text-sm text-gray-500">
            Selected values: {checkboxGroupValues.join(', ')}
          </div>
        </div>
      </ComponentDemo>

      {/* Horizontal Checkbox Group */}
      <ComponentDemo
        title={t('components.inputs.checkbox.horizontal.title', 'Horizontal Checkbox Group')}
        description={t(
          'components.inputs.checkbox.horizontal.description',
          'CheckboxGroup với hướng hiển thị ngang.'
        )}
        code={`import { CheckboxGroup } from '@/shared/components/common';

<CheckboxGroup
  options={options}
  value={values}
  onChange={setValues}
  direction="horizontal"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <CheckboxGroup
            options={checkboxOptions}
            value={checkboxGroupValues}
            onChange={handleCheckboxGroupChange}
            direction="horizontal"
          />
        </div>
      </ComponentDemo>

      {/* Basic Radio */}
      <ComponentDemo
        title={t('components.inputs.radio.basic.title', 'Basic Radio')}
        description={t('components.inputs.radio.basic.description', 'Radio cơ bản với label.')}
        code={`import { Radio } from '@/shared/components/common';

<div className="space-y-2">
  <Radio label="Unchecked Radio" name="basic-radio" value="option1" />
  <Radio label="Checked Radio" name="basic-radio" value="option2" checked />
  <Radio label="Disabled Radio" name="basic-radio" value="option3" disabled />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-2">
          <Radio label="Unchecked Radio" name="basic-radio" value="option1" />
          <Radio label="Checked Radio" name="basic-radio" value="option2" checked />
          <Radio label="Disabled Radio" name="basic-radio" value="option3" disabled />
        </div>
      </ComponentDemo>

      {/* Radio Group */}
      <ComponentDemo
        title={t('components.inputs.radio.group.title', 'Radio Group')}
        description={t(
          'components.inputs.radio.group.description',
          'RadioGroup cho phép chọn một tùy chọn từ một danh sách.'
        )}
        code={`import { RadioGroup } from '@/shared/components/common';
import { useState } from 'react';

// State
const [value, setValue] = useState('option1');

// Options
const options = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
  { label: 'Option 3 (Disabled)', value: 'option3', disabled: true },
];

// Render
<RadioGroup
  options={options}
  value={value}
  onChange={setValue}
  name="radio-group"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <RadioGroup
            options={radioOptions}
            value={radioValue}
            onChange={handleRadioChange}
            name="radio-group"
          />
          <div className="mt-2 text-sm text-gray-500">Selected value: {radioValue}</div>
        </div>
      </ComponentDemo>

      {/* Horizontal Radio Group */}
      <ComponentDemo
        title={t('components.inputs.radio.horizontal.title', 'Horizontal Radio Group')}
        description={t(
          'components.inputs.radio.horizontal.description',
          'RadioGroup với hướng hiển thị ngang.'
        )}
        code={`import { RadioGroup } from '@/shared/components/common';

<RadioGroup
  options={options}
  value={value}
  onChange={setValue}
  direction="horizontal"
  name="horizontal-radio"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <RadioGroup
            options={radioOptions}
            value={horizontalRadioValue}
            onChange={handleHorizontalRadioChange}
            direction="horizontal"
            name="horizontal-radio"
          />
        </div>
      </ComponentDemo>

      {/* Radio Sizes */}
      <ComponentDemo
        title={t('components.inputs.radio.sizes.title', 'Radio Sizes')}
        description={t(
          'components.inputs.radio.sizes.description',
          'RadioGroup với các kích thước khác nhau.'
        )}
        code={`import { RadioGroup } from '@/shared/components/common';

// Small
<RadioGroup
  options={options}
  value={value}
  onChange={setValue}
  size="sm"
  name="small-radio"
/>

// Medium (Default)
<RadioGroup
  options={options}
  value={value}
  onChange={setValue}
  size="md"
  name="medium-radio"
/>

// Large
<RadioGroup
  options={options}
  value={value}
  onChange={setValue}
  size="lg"
  name="large-radio"
/>`}
      >
        <div className="w-full max-w-md mx-auto space-y-6">
          <div>
            <Typography variant="subtitle2" className="mb-2">
              Small
            </Typography>
            <RadioGroup
              options={sizeRadioOptions}
              value={sizeRadioValue}
              onChange={handleSizeRadioChange}
              size="sm"
              name="small-radio"
            />
          </div>

          <div>
            <Typography variant="subtitle2" className="mb-2">
              Medium (Default)
            </Typography>
            <RadioGroup
              options={sizeRadioOptions}
              value={sizeRadioValue}
              onChange={handleSizeRadioChange}
              size="md"
              name="medium-radio"
            />
          </div>

          <div>
            <Typography variant="subtitle2" className="mb-2">
              Large
            </Typography>
            <RadioGroup
              options={sizeRadioOptions}
              value={sizeRadioValue}
              onChange={handleSizeRadioChange}
              size="lg"
              name="large-radio"
            />
          </div>
        </div>
      </ComponentDemo>

      {/* Checkbox Variants */}
      <ComponentDemo
        title={t('components.inputs.checkbox.variants.title', 'Checkbox Variants')}
        description={t(
          'components.inputs.checkbox.variants.description',
          'Các biến thể khác nhau của Checkbox.'
        )}
        code={`import { Checkbox } from '@/shared/components/common';

<div className="space-y-2">
  <Checkbox label="Default Variant" variant="default" checked />
  <Checkbox label="Rounded Variant" variant="rounded" checked />
  <Checkbox label="Filled Variant" variant="filled" checked />
  <Checkbox label="Outlined Variant" variant="outlined" checked />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-2">
          <Checkbox label="Default Variant" variant="default" checked />
          <Checkbox label="Rounded Variant" variant="rounded" checked />
          <Checkbox label="Filled Variant" variant="filled" checked />
          <Checkbox label="Outlined Variant" variant="outlined" checked />
        </div>
      </ComponentDemo>

      {/* Checkbox Colors */}
      <ComponentDemo
        title={t('components.inputs.checkbox.colors.title', 'Checkbox Colors')}
        description={t(
          'components.inputs.checkbox.colors.description',
          'Các màu sắc khác nhau của Checkbox.'
        )}
        code={`import { Checkbox } from '@/shared/components/common';

<div className="space-y-2">
  <Checkbox label="Primary Color" color="primary" checked />
  <Checkbox label="Success Color" color="success" checked />
  <Checkbox label="Warning Color" color="warning" checked />
  <Checkbox label="Danger Color" color="danger" checked />
  <Checkbox label="Info Color" color="info" checked />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-2">
          <Checkbox label="Primary Color" color="primary" checked />
          <Checkbox label="Success Color" color="success" checked />
          <Checkbox label="Warning Color" color="warning" checked />
          <Checkbox label="Danger Color" color="danger" checked />
          <Checkbox label="Info Color" color="info" checked />
        </div>
      </ComponentDemo>

      {/* Checkbox Variants & Colors */}
      <ComponentDemo
        title={t('components.inputs.checkbox.variantsColors.title', 'Checkbox Variants & Colors')}
        description={t(
          'components.inputs.checkbox.variantsColors.description',
          'Kết hợp các biến thể và màu sắc của Checkbox.'
        )}
        code={`import { Checkbox } from '@/shared/components/common';

<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <Checkbox label="Default Primary" variant="default" color="primary" checked />
  <Checkbox label="Rounded Success" variant="rounded" color="success" checked />
  <Checkbox label="Filled Warning" variant="filled" color="warning" checked />
  <Checkbox label="Outlined Danger" variant="outlined" color="danger" checked />
  <Checkbox label="Filled Info" variant="filled" color="info" checked />
</div>`}
      >
        <div className="w-full max-w-md mx-auto grid grid-cols-1 md:grid-cols-2 gap-4">
          <Checkbox label="Default Primary" variant="default" color="primary" checked />
          <Checkbox label="Rounded Success" variant="rounded" color="success" checked />
          <Checkbox label="Filled Warning" variant="filled" color="warning" checked />
          <Checkbox label="Outlined Danger" variant="outlined" color="danger" checked />
          <Checkbox label="Filled Info" variant="filled" color="info" checked />
        </div>
      </ComponentDemo>

      {/* Radio Variants */}
      <ComponentDemo
        title={t('components.inputs.radio.variants.title', 'Radio Variants')}
        description={t(
          'components.inputs.radio.variants.description',
          'Các biến thể khác nhau của Radio.'
        )}
        code={`import { Radio } from '@/shared/components/common';

<div className="space-y-2">
  <Radio label="Default Variant" variant="default" checked name="radio-variant" />
  <Radio label="Filled Variant" variant="filled" checked name="radio-variant" />
  <Radio label="Outlined Variant" variant="outlined" checked name="radio-variant" />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-2">
          <Radio label="Default Variant" variant="default" checked name="radio-variant" />
          <Radio label="Filled Variant" variant="filled" checked name="radio-variant" />
          <Radio label="Outlined Variant" variant="outlined" checked name="radio-variant" />
        </div>
      </ComponentDemo>

      {/* Radio Colors */}
      <ComponentDemo
        title={t('components.inputs.radio.colors.title', 'Radio Colors')}
        description={t(
          'components.inputs.radio.colors.description',
          'Các màu sắc khác nhau của Radio.'
        )}
        code={`import { Radio } from '@/shared/components/common';

<div className="space-y-2">
  <Radio label="Primary Color" color="primary" checked name="radio-color" />
  <Radio label="Success Color" color="success" checked name="radio-color" />
  <Radio label="Warning Color" color="warning" checked name="radio-color" />
  <Radio label="Danger Color" color="danger" checked name="radio-color" />
  <Radio label="Info Color" color="info" checked name="radio-color" />
</div>`}
      >
        <div className="w-full max-w-md mx-auto space-y-2">
          <Radio label="Primary Color" color="primary" checked name="radio-color" />
          <Radio label="Success Color" color="success" checked name="radio-color" />
          <Radio label="Warning Color" color="warning" checked name="radio-color" />
          <Radio label="Danger Color" color="danger" checked name="radio-color" />
          <Radio label="Info Color" color="info" checked name="radio-color" />
        </div>
      </ComponentDemo>

      {/* Radio Variants & Colors */}
      <ComponentDemo
        title={t('components.inputs.radio.variantsColors.title', 'Radio Variants & Colors')}
        description={t(
          'components.inputs.radio.variantsColors.description',
          'Kết hợp các biến thể và màu sắc của Radio.'
        )}
        code={`import { Radio } from '@/shared/components/common';

<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <Radio label="Default Primary" variant="default" color="primary" checked name="radio-variant-color" />
  <Radio label="Filled Success" variant="filled" color="success" checked name="radio-variant-color" />
  <Radio label="Outlined Warning" variant="outlined" color="warning" checked name="radio-variant-color" />
  <Radio label="Filled Danger" variant="filled" color="danger" checked name="radio-variant-color" />
  <Radio label="Outlined Info" variant="outlined" color="info" checked name="radio-variant-color" />
</div>`}
      >
        <div className="w-full max-w-md mx-auto grid grid-cols-1 md:grid-cols-2 gap-4">
          <Radio
            label="Default Primary"
            variant="default"
            color="primary"
            checked
            name="radio-variant-color"
          />
          <Radio
            label="Filled Success"
            variant="filled"
            color="success"
            checked
            name="radio-variant-color"
          />
          <Radio
            label="Outlined Warning"
            variant="outlined"
            color="warning"
            checked
            name="radio-variant-color"
          />
          <Radio
            label="Filled Danger"
            variant="filled"
            color="danger"
            checked
            name="radio-variant-color"
          />
          <Radio
            label="Outlined Info"
            variant="outlined"
            color="info"
            checked
            name="radio-variant-color"
          />
        </div>
      </ComponentDemo>

      {/* Usage Guide */}
      <Card title="Checkbox & Radio Usage Guide" className="mb-6">
        <div className="space-y-4">
          <Typography>
            Checkbox và Radio components là các thành phần cơ bản trong form, cho phép người dùng
            chọn một hoặc nhiều tùy chọn.
          </Typography>

          <Typography variant="subtitle1">Checkbox</Typography>
          <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-x-auto">
            {`// Single Checkbox
<Checkbox
  label="Remember me"
  checked={checked}
  onChange={setChecked}
  variant="default" // or "rounded", "filled", "outlined"
  color="primary" // or "success", "warning", "danger", "info"
  size="md" // or "sm", "lg"
/>

// Checkbox Group
<CheckboxGroup
  options={options}
  value={values}
  onChange={setValues}
  direction="vertical" // or "horizontal"
  variant="default" // or "rounded", "filled", "outlined"
  color="primary" // or "success", "warning", "danger", "info"
/>`}
          </pre>

          <Typography variant="subtitle1">Radio</Typography>
          <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-x-auto">
            {`// Single Radio
<Radio
  label="Option 1"
  checked={checked}
  onChange={setChecked}
  name="radio-name"
  variant="default" // or "filled", "outlined"
  color="primary" // or "success", "warning", "danger", "info"
  size="md" // or "sm", "lg"
/>

// Radio Group
<RadioGroup
  options={options}
  value={value}
  onChange={setValue}
  direction="vertical" // or "horizontal"
  name="radio-group-name"
  variant="default" // or "filled", "outlined"
  color="primary" // or "success", "warning", "danger", "info"
/>`}
          </pre>

          <Typography variant="subtitle1">Key Features:</Typography>
          <ul className="list-disc list-inside space-y-1 pl-4">
            <li>
              Sử dụng <code>Checkbox</code> cho các tùy chọn độc lập hoặc chọn nhiều
            </li>
            <li>
              Sử dụng <code>Radio</code> khi chỉ được chọn một tùy chọn
            </li>
            <li>
              Sử dụng <code>indeterminate</code> cho trạng thái không xác định của Checkbox
            </li>
            <li>
              Sử dụng <code>size</code> để thay đổi kích thước (sm, md, lg)
            </li>
            <li>
              Sử dụng <code>direction</code> để thay đổi hướng hiển thị (vertical, horizontal)
            </li>
            <li>
              Sử dụng <code>disabled</code> để vô hiệu hóa các tùy chọn
            </li>
            <li>
              Sử dụng <code>variant</code> để thay đổi kiểu dáng (default, rounded, filled,
              outlined)
            </li>
            <li>
              Sử dụng <code>color</code> để thay đổi màu sắc (primary, success, warning, danger,
              info)
            </li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default CheckboxRadioDemo;
