import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import {
  Form,
  FormItem,
  Input,
  Button,
  Toggle,
  Icon,
  ConditionalField,
  Card,
} from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import { FieldValues, SubmitHandler } from 'react-hook-form';

/**
 * Trang hiển thị các conditional fields
 */
const ConditionalFieldsPage: React.FC = () => {
  const { t } = useTranslation();
  // Basic conditional form schema
  const basicConditionalSchema = z.object({
    userType: z.enum(['personal', 'business']),
    name: z.string().min(1, 'Name is required'),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    companyName: z.string().min(1, 'Company name is required').optional(),
    taxId: z.string().min(1, 'Tax ID is required').optional(),
  });

  // Infer type from schema
  type BasicConditionalFormData = z.infer<typeof basicConditionalSchema>;

  // Advanced conditional form schema
  const advancedConditionalSchema = z.object({
    hasBillingAddress: z.boolean().optional(),
    billingAddress: z.string().min(1, 'Billing address is required').optional(),
    billingCity: z.string().min(1, 'City is required').optional(),
    hasSpecialRequirements: z.boolean().optional(),
    specialRequirements: z.string().min(1, 'Special requirements field is required').optional(),
    contactMethod: z.enum(['email', 'phone', 'both']).optional(),
    phoneNumber: z.string().min(1, 'Phone number is required').optional(),
  });

  // Infer type from schema
  type AdvancedConditionalFormData = z.infer<typeof advancedConditionalSchema>;

  // State to store form data
  const [formData, setFormData] = useState<
    BasicConditionalFormData | AdvancedConditionalFormData | null
  >(null);

  // Handle form submission
  const handleSubmit: SubmitHandler<FieldValues> = data => {
    console.log('Form submitted:', data);
    setFormData(data as BasicConditionalFormData | AdvancedConditionalFormData);
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.form.conditional.title', 'Conditional Form Fields')}
        </h1>
        <p className="text-muted">
          {t(
            'components.form.conditional.description',
            'Show or hide form fields based on conditions using the ConditionalField component.'
          )}
        </p>
      </div>

      {/* Basic Conditional Fields */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.form.conditional.basic.title', 'Basic Conditional Fields')}
            description={t(
              'components.form.conditional.basic.description',
              'Show fields based on a simple condition.'
            )}
            code={`import { Form, FormItem, Input, ConditionalField } from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

<Form schema={schema} onSubmit={handleSubmit}>
  <FormItem name="userType" label="Account Type" required>
    <select>
      <option value="personal">Personal</option>
      <option value="business">Business</option>
    </select>
  </FormItem>

  <FormItem name="name" label="Name" required>
    <Input placeholder="Enter your name" />
  </FormItem>

  <FormItem name="email" label="Email" required>
    <Input type="email" placeholder="Enter your email" />
  </FormItem>

  <ConditionalField
    condition={{
      field: 'userType',
      type: ConditionType.EQUALS,
      value: 'business'
    }}
  >
    <FormItem name="companyName" label="Company Name" required>
      <Input placeholder="Enter company name" />
    </FormItem>

    <FormItem name="taxId" label="Tax ID" required>
      <Input placeholder="Enter tax ID" />
    </FormItem>
  </ConditionalField>

  <Button type="submit">Submit</Button>
</Form>`}
          >
            <div className="w-full">
              <Form
                schema={basicConditionalSchema}
                onSubmit={handleSubmit}
                className="space-y-4"
                defaultValues={{
                  userType: 'personal',
                }}
              >
                <FormItem
                  name="userType"
                  label={t('components.form.conditional.fields.accountType', 'Account Type')}
                  required
                >
                  <select className="w-full p-2 border rounded bg-background text-foreground border-input">
                    <option value="personal">
                      {t('components.form.conditional.options.personal', 'Personal')}
                    </option>
                    <option value="business">
                      {t('components.form.conditional.options.business', 'Business')}
                    </option>
                  </select>
                </FormItem>

                <FormItem
                  name="name"
                  label={t('components.form.conditional.fields.name', 'Name')}
                  required
                >
                  <Input
                    placeholder={t(
                      'components.form.conditional.placeholders.name',
                      'Enter your name'
                    )}
                    fullWidth
                  />
                </FormItem>

                <FormItem
                  name="email"
                  label={t('components.form.conditional.fields.email', 'Email')}
                  required
                >
                  <Input
                    type="email"
                    placeholder={t(
                      'components.form.conditional.placeholders.email',
                      'Enter your email'
                    )}
                    leftIcon={<Icon name="mail" size="sm" />}
                    fullWidth
                  />
                </FormItem>

                <ConditionalField
                  condition={{
                    field: 'userType',
                    type: ConditionType.EQUALS,
                    value: 'business',
                  }}
                >
                  <FormItem
                    name="companyName"
                    label={t('components.form.conditional.fields.companyName', 'Company Name')}
                    required
                  >
                    <Input
                      placeholder={t(
                        'components.form.conditional.placeholders.companyName',
                        'Enter company name'
                      )}
                      leftIcon={<Icon name="building" size="sm" />}
                      fullWidth
                    />
                  </FormItem>

                  <FormItem
                    name="taxId"
                    label={t('components.form.conditional.fields.taxId', 'Tax ID')}
                    required
                  >
                    <Input
                      placeholder={t(
                        'components.form.conditional.placeholders.taxId',
                        'Enter tax ID'
                      )}
                      leftIcon={<Icon name="document" size="sm" />}
                      fullWidth
                    />
                  </FormItem>
                </ConditionalField>

                <Button type="submit">
                  {t('components.form.conditional.buttons.submit', 'Submit')}
                </Button>
              </Form>

              {formData && (
                <div className="mt-4 p-3 bg-muted/20 rounded">
                  <h4 className="font-medium mb-2 text-foreground">
                    {t('components.form.conditional.result', 'Result')}:
                  </h4>
                  <pre className="text-sm overflow-auto">{JSON.stringify(formData, null, 2)}</pre>
                </div>
              )}
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card
            title={t('components.form.conditional.component.title', 'ConditionalField Component')}
            className="h-full"
          >
            <div className="space-y-4">
              <p className="text-muted">
                {t(
                  'components.form.conditional.component.description',
                  "The ConditionalField component allows you to show or hide form fields based on conditions. It's useful for creating dynamic forms that adapt to user input."
                )}
              </p>

              <div>
                <h3 className="font-medium text-lg mb-2 text-foreground">
                  {t('components.form.conditional.component.basicUsage.title', 'Basic Usage')}
                </h3>
                <p className="mb-2 text-muted">
                  {t(
                    'components.form.conditional.component.basicUsage.description',
                    'The most common use case is to show fields based on a simple condition:'
                  )}
                </p>
                <ul className="list-disc list-inside space-y-1 text-muted">
                  <li>
                    {t(
                      'components.form.conditional.examples.business',
                      'Show company fields only when account type is "business"'
                    )}
                  </li>
                  <li>
                    {t(
                      'components.form.conditional.examples.shipping',
                      'Show shipping address only when "different shipping address" is checked'
                    )}
                  </li>
                  <li>
                    {t(
                      'components.form.conditional.examples.additional',
                      'Show additional fields based on a selection'
                    )}
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2 text-foreground">
                  {t(
                    'components.form.conditional.component.conditionTypes.title',
                    'Condition Types'
                  )}
                </h3>
                <p className="mb-2 text-muted">
                  {t(
                    'components.form.conditional.component.conditionTypes.description',
                    'The ConditionalField component supports various condition types:'
                  )}
                </p>
                <ul className="list-disc list-inside space-y-1 text-muted">
                  <li>
                    <code>EQUALS</code>:{' '}
                    {t(
                      'components.form.conditional.conditionTypes.equals',
                      'Field value equals a specific value'
                    )}
                  </li>
                  <li>
                    <code>NOT_EQUALS</code>:{' '}
                    {t(
                      'components.form.conditional.conditionTypes.notEquals',
                      'Field value does not equal a specific value'
                    )}
                  </li>
                  <li>
                    <code>IS_TRUE</code>:{' '}
                    {t(
                      'components.form.conditional.conditionTypes.isTrue',
                      'Field value is true (for boolean fields)'
                    )}
                  </li>
                  <li>
                    <code>IS_FALSE</code>:{' '}
                    {t(
                      'components.form.conditional.conditionTypes.isFalse',
                      'Field value is false (for boolean fields)'
                    )}
                  </li>
                  <li>
                    <code>IS_EMPTY</code>:{' '}
                    {t(
                      'components.form.conditional.conditionTypes.isEmpty',
                      'Field value is empty'
                    )}
                  </li>
                  <li>
                    <code>IS_NOT_EMPTY</code>:{' '}
                    {t(
                      'components.form.conditional.conditionTypes.isNotEmpty',
                      'Field value is not empty'
                    )}
                  </li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Advanced Conditional Fields */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.form.conditional.advanced.title', 'Advanced Conditional Fields')}
            description={t(
              'components.form.conditional.advanced.description',
              'Use complex conditions with AND/OR logic.'
            )}
            code={`import { Form, FormItem, Input, Toggle, ConditionalField } from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

<Form schema={schema} onSubmit={handleSubmit}>
  {/* Boolean condition */}
  <FormItem name="hasBillingAddress" label="Different Billing Address?" inline>
    <Toggle />
  </FormItem>

  <ConditionalField
    condition={{
      field: 'hasBillingAddress',
      type: ConditionType.IS_TRUE
    }}
  >
    <FormItem name="billingAddress" label="Billing Address" required>
      <Input placeholder="Enter billing address" />
    </FormItem>
    <FormItem name="billingCity" label="City" required>
      <Input placeholder="Enter city" />
    </FormItem>
  </ConditionalField>

  {/* Complex AND condition */}
  <FormItem name="hasSpecialRequirements" label="Special Requirements?" inline>
    <Toggle />
  </FormItem>

  <ConditionalField
    condition={{
      and: [
        { field: 'hasSpecialRequirements', type: ConditionType.IS_TRUE },
        { field: 'hasBillingAddress', type: ConditionType.IS_TRUE }
      ]
    }}
  >
    <FormItem name="specialRequirements" label="Special Requirements" required>
      <Input as="textarea" placeholder="Enter special requirements" />
    </FormItem>
  </ConditionalField>

  {/* Complex OR condition */}
  <FormItem name="contactMethod" label="Contact Method" required>
    <select>
      <option value="email">Email</option>
      <option value="phone">Phone</option>
      <option value="both">Both</option>
    </select>
  </FormItem>

  <ConditionalField
    condition={{
      or: [
        { field: 'contactMethod', type: ConditionType.EQUALS, value: 'phone' },
        { field: 'contactMethod', type: ConditionType.EQUALS, value: 'both' }
      ]
    }}
  >
    <FormItem name="phoneNumber" label="Phone Number" required>
      <Input placeholder="Enter phone number" />
    </FormItem>
  </ConditionalField>
</Form>`}
          >
            <div className="w-full">
              <Form
                schema={advancedConditionalSchema}
                onSubmit={handleSubmit}
                className="space-y-4"
                defaultValues={{
                  hasBillingAddress: false,
                  hasSpecialRequirements: false,
                  contactMethod: 'email',
                }}
              >
                {/* Boolean condition */}
                <FormItem
                  name="hasBillingAddress"
                  label={t(
                    'components.form.conditional.fields.differentBillingAddress',
                    'Different Billing Address?'
                  )}
                  inline
                >
                  <Toggle />
                </FormItem>

                <ConditionalField
                  condition={{
                    field: 'hasBillingAddress',
                    type: ConditionType.IS_TRUE,
                  }}
                >
                  <FormItem
                    name="billingAddress"
                    label={t(
                      'components.form.conditional.fields.billingAddress',
                      'Billing Address'
                    )}
                    required
                  >
                    <Input
                      placeholder={t(
                        'components.form.conditional.placeholders.billingAddress',
                        'Enter billing address'
                      )}
                      leftIcon={<Icon name="map-pin" size="sm" />}
                      fullWidth
                    />
                  </FormItem>
                  <FormItem
                    name="billingCity"
                    label={t('components.form.conditional.fields.city', 'City')}
                    required
                  >
                    <Input
                      placeholder={t('components.form.conditional.placeholders.city', 'Enter city')}
                      fullWidth
                    />
                  </FormItem>
                </ConditionalField>

                {/* Complex AND condition */}
                <FormItem
                  name="hasSpecialRequirements"
                  label={t(
                    'components.form.conditional.fields.hasSpecialRequirements',
                    'Special Requirements?'
                  )}
                  inline
                >
                  <Toggle />
                </FormItem>

                <ConditionalField
                  condition={{
                    and: [
                      { field: 'hasSpecialRequirements', type: ConditionType.IS_TRUE },
                      { field: 'hasBillingAddress', type: ConditionType.IS_TRUE },
                    ],
                  }}
                >
                  <FormItem
                    name="specialRequirements"
                    label={t(
                      'components.form.conditional.fields.specialRequirements',
                      'Special Requirements'
                    )}
                    required
                  >
                    <textarea
                      className="w-full p-2 border rounded bg-background text-foreground border-input"
                      placeholder={t(
                        'components.form.conditional.placeholders.specialRequirements',
                        'Enter special requirements'
                      )}
                      rows={3}
                    />
                  </FormItem>
                </ConditionalField>

                {/* Complex OR condition */}
                <FormItem
                  name="contactMethod"
                  label={t('components.form.conditional.fields.contactMethod', 'Contact Method')}
                  required
                >
                  <select className="w-full p-2 border rounded bg-background text-foreground border-input">
                    <option value="email">
                      {t('components.form.conditional.options.email', 'Email')}
                    </option>
                    <option value="phone">
                      {t('components.form.conditional.options.phone', 'Phone')}
                    </option>
                    <option value="both">
                      {t('components.form.conditional.options.both', 'Both')}
                    </option>
                  </select>
                </FormItem>

                <ConditionalField
                  condition={{
                    or: [
                      { field: 'contactMethod', type: ConditionType.EQUALS, value: 'phone' },
                      { field: 'contactMethod', type: ConditionType.EQUALS, value: 'both' },
                    ],
                  }}
                >
                  <FormItem
                    name="phoneNumber"
                    label={t('components.form.conditional.fields.phoneNumber', 'Phone Number')}
                    required
                  >
                    <Input
                      placeholder={t(
                        'components.form.conditional.placeholders.phoneNumber',
                        'Enter phone number'
                      )}
                      leftIcon={<Icon name="phone" size="sm" />}
                      fullWidth
                    />
                  </FormItem>
                </ConditionalField>

                <Button type="submit">
                  {t('components.form.conditional.buttons.submit', 'Submit')}
                </Button>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card
            title={t('components.form.conditional.complex.title', 'Complex Conditions')}
            className="h-full"
          >
            <div className="space-y-4">
              <p className="text-muted">
                {t(
                  'components.form.conditional.complex.description',
                  'The ConditionalField component supports complex conditions using AND and OR logic. This allows you to create sophisticated form behaviors.'
                )}
              </p>

              <div>
                <h3 className="font-medium text-lg mb-2 text-foreground">
                  {t('components.form.conditional.complex.and.title', 'AND Logic')}
                </h3>
                <p className="mb-2 text-muted">
                  {t(
                    'components.form.conditional.complex.and.description',
                    'Use AND logic when you want to show fields only when multiple conditions are met:'
                  )}
                </p>
                <pre className="bg-muted/20 p-3 rounded text-sm overflow-auto">
                  {`<ConditionalField
  condition={{
    and: [
      { field: 'hasSpecialRequirements', type: ConditionType.IS_TRUE },
      { field: 'hasBillingAddress', type: ConditionType.IS_TRUE }
    ]
  }}
>
  <FormItem name="specialRequirements" label="Special Requirements">
    <Input as="textarea" />
  </FormItem>
</ConditionalField>`}
                </pre>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2 text-foreground">
                  {t('components.form.conditional.complex.or.title', 'OR Logic')}
                </h3>
                <p className="mb-2 text-muted">
                  {t(
                    'components.form.conditional.complex.or.description',
                    'Use OR logic when you want to show fields when any of the conditions are met:'
                  )}
                </p>
                <pre className="bg-muted/20 p-3 rounded text-sm overflow-auto">
                  {`<ConditionalField
  condition={{
    or: [
      { field: 'contactMethod', type: ConditionType.EQUALS, value: 'phone' },
      { field: 'contactMethod', type: ConditionType.EQUALS, value: 'both' }
    ]
  }}
>
  <FormItem name="phoneNumber" label="Phone Number">
    <Input />
  </FormItem>
</ConditionalField>`}
                </pre>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ConditionalFieldsPage;
