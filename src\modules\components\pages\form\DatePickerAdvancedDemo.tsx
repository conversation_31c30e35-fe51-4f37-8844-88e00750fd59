import React, { useState } from 'react';
// import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import { DatePicker, RangePicker, Typography, Button } from '@/shared/components/common';
import { addDays, format, isWeekend, isSameDay } from 'date-fns';

/**
 * Trang demo nâng cao cho DatePicker component
 *
 * Trang này hiển thị các use cases phức tạp của DatePicker và RangePicker
 */
const DatePickerAdvancedDemo: React.FC = () => {
  // const { t } = useTranslation();

  // State cho các ví dụ
  const [businessDate, setBusinessDate] = useState<Date | null>(null);
  const [holidayDate, setHolidayDate] = useState<Date | null>(null);
  const [workingDaysRange, setWorkingDaysRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const [formValues, setFormValues] = useState({
    startDate: null as Date | null,
    endDate: null as Date | null,
    eventDate: null as Date | null,
  });

  // Hàm format an toàn
  const safeFormat = (date: Date | null, formatStr: string): string => {
    if (!date) return '';
    try {
      return format(date, formatStr);
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Danh sách ngày lễ (ví dụ)
  const holidays = [
    new Date(new Date().getFullYear(), 0, 1), // Năm mới
    new Date(new Date().getFullYear(), 4, 1), // Quốc tế Lao động
    new Date(new Date().getFullYear(), 8, 2), // Quốc khánh
    new Date(new Date().getFullYear(), 11, 25), // Giáng sinh
  ];

  // Disable ngày cuối tuần
  const disableWeekends = (date: Date): boolean => {
    try {
      return isWeekend(date);
    } catch (error) {
      console.error('Error in disableWeekends:', error);
      return false;
    }
  };

  // Disable ngày lễ và cuối tuần
  const disableHolidays = (date: Date): boolean => {
    try {
      // Kiểm tra ngày cuối tuần
      if (isWeekend(date)) return true;

      // Kiểm tra ngày lễ
      return holidays.some(holiday => isSameDay(holiday, date));
    } catch (error) {
      console.error('Error in disableHolidays:', error);
      return false;
    }
  };

  // Tính số ngày làm việc (không tính cuối tuần và ngày lễ)
  const calculateWorkingDays = (startDate: Date | null, endDate: Date | null): number => {
    if (!startDate || !endDate) return 0;

    let count = 0;
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      // Kiểm tra nếu không phải cuối tuần và không phải ngày lễ
      if (!isWeekend(currentDate) && !holidays.some(holiday => isSameDay(holiday, currentDate))) {
        count++;
      }

      currentDate = addDays(currentDate, 1);
    }

    return count;
  };

  // Không sử dụng customDayRender trong ví dụ này

  // Xử lý submit form
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert(
      `Event Date: ${safeFormat(formValues.eventDate, 'dd/MM/yyyy')}\nPeriod: ${safeFormat(formValues.startDate, 'dd/MM/yyyy')} - ${safeFormat(formValues.endDate, 'dd/MM/yyyy')}`
    );
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <Typography variant="h1" className="mb-2">
          DatePicker Advanced Examples
        </Typography>
        <Typography color="muted">
          Các ví dụ nâng cao về cách sử dụng DatePicker và RangePicker trong các tình huống thực tế.
        </Typography>
      </div>

      {/* Business Days Picker */}
      <ComponentDemo
        title="Business Days Picker"
        description="DatePicker chỉ cho phép chọn ngày làm việc (không bao gồm cuối tuần)."
        code={`import { DatePicker } from '@/shared/components/common';
import { useState, isWeekend } from 'react';

// State
const [date, setDate] = useState<Date | null>(null);

// Disable weekends
const disableWeekends = (date: Date): boolean => {
  return isWeekend(date); // true for Saturday and Sunday
};

// Render
<DatePicker
  label="Select a business day"
  value={date}
  onChange={setDate}
  disabledDates={disableWeekends}
  placeholder="DD/MM/YYYY"
  helperText="Weekends are disabled"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <DatePicker
            label="Select a business day"
            value={businessDate}
            onChange={setBusinessDate}
            disabledDates={disableWeekends}
            placeholder="DD/MM/YYYY"
            helperText="Weekends are disabled"
          />
          {businessDate && (
            <div className="mt-2 text-sm">
              Selected date:{' '}
              <span className="font-medium">{safeFormat(businessDate, 'EEEE, dd/MM/yyyy')}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Holiday Picker */}
      <ComponentDemo
        title="Holiday Picker"
        description="DatePicker với disabled dates cho ngày lễ và cuối tuần."
        code={`import { DatePicker } from '@/shared/components/common';
import { useState, isWeekend, isSameDay } from 'react';

// State
const [date, setDate] = useState<Date | null>(null);

// Holidays list
const holidays = [
  new Date(new Date().getFullYear(), 0, 1), // New Year
  new Date(new Date().getFullYear(), 4, 1), // Labor Day
  // More holidays...
];

// Disable holidays and weekends
const disableHolidays = (date: Date): boolean => {
  // Check weekends
  if (isWeekend(date)) return true;

  // Check holidays
  return holidays.some(holiday => isSameDay(holiday, date));
};

// Render
<DatePicker
  label="Select a working day"
  value={date}
  onChange={setDate}
  disabledDates={disableHolidays}
  placeholder="DD/MM/YYYY"
  helperText="Weekends and holidays are disabled"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <DatePicker
            label="Select a working day"
            value={holidayDate}
            onChange={setHolidayDate}
            disabledDates={disableHolidays}
            placeholder="DD/MM/YYYY"
            helperText="Weekends and holidays are disabled"
          />
          {holidayDate && (
            <div className="mt-2 text-sm">
              Selected date:{' '}
              <span className="font-medium">{safeFormat(holidayDate, 'EEEE, dd/MM/yyyy')}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Working Days Range Picker */}
      <ComponentDemo
        title="Working Days Range Picker"
        description="RangePicker với tính toán số ngày làm việc (không bao gồm cuối tuần và ngày lễ)."
        code={`import { RangePicker } from '@/shared/components/common';
import { useState, isWeekend, isSameDay, addDays } from 'react';

// State
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

// Calculate working days
const calculateWorkingDays = (startDate, endDate) => {
  if (!startDate || !endDate) return 0;

  let count = 0;
  let currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    // Check if not weekend and not holiday
    if (!isWeekend(currentDate) && !holidays.some(h => isSameDay(h, currentDate))) {
      count++;
    }

    currentDate = addDays(currentDate, 1);
  }

  return count;
};

// Render
<RangePicker
  label="Select date range"
  value={dateRange}
  onChange={setDateRange}
  placeholder={['Start date', 'End date']}
  showDaysCount
/>

// Display working days
{dateRange[0] && dateRange[1] && (
  <div className="mt-2 text-sm">
    Working days: <span className="font-medium">
      {calculateWorkingDays(dateRange[0], dateRange[1])}
    </span>
  </div>
)}`}
      >
        <div className="w-full max-w-md mx-auto">
          <RangePicker
            label="Select date range"
            value={workingDaysRange}
            onChange={setWorkingDaysRange}
            placeholder={['Start date', 'End date']}
            showDaysCount
          />
          {workingDaysRange[0] && workingDaysRange[1] && (
            <div className="mt-2 text-sm">
              Working days:{' '}
              <span className="font-medium">
                {calculateWorkingDays(workingDaysRange[0], workingDaysRange[1])}
              </span>{' '}
              days
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Form Integration */}
      <ComponentDemo
        title="Form Integration"
        description="Tích hợp DatePicker và RangePicker vào form."
        code={`import { DatePicker, RangePicker, Button } from '@/shared/components/common';
import { useState } from 'react';

// Form state
const [formValues, setFormValues] = useState({
  startDate: null,
  endDate: null,
  eventDate: null,
});

// Handle form submit
const handleFormSubmit = (e) => {
  e.preventDefault();
  alert(\`Event Date: \${formValues.eventDate}
Period: \${formValues.startDate} - \${formValues.endDate}\`);
};

// Render
<div className="space-y-4">
  <div className="mb-4">
    <label className="block text-sm font-medium mb-1">Event Date</label>
    <DatePicker
      value={formValues.eventDate}
      onChange={(date) => setFormValues({...formValues, eventDate: date})}
      placeholder="Select event date"
    />
  </div>

  <div className="mb-4">
    <label className="block text-sm font-medium mb-1">Event Period</label>
    <RangePicker
      value={[formValues.startDate, formValues.endDate]}
      onChange={([start, end]) => setFormValues({
        ...formValues,
        startDate: start,
        endDate: end
      })}
      placeholder={['Start date', 'End date']}
    />
  </div>

  <Button onClick={handleFormSubmit} type="button">Submit</Button>
</div>`}
      >
        <div className="w-full max-w-md mx-auto">
          <div className="space-y-4">
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Event Date</label>
              <DatePicker
                value={formValues.eventDate}
                onChange={(date: Date | null) => setFormValues({ ...formValues, eventDate: date })}
                placeholder="Select event date"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Event Period</label>
              <RangePicker
                value={[formValues.startDate, formValues.endDate]}
                onChange={([start, end]: [Date | null, Date | null]) =>
                  setFormValues({
                    ...formValues,
                    startDate: start,
                    endDate: end,
                  })
                }
                placeholder={['Start date', 'End date']}
              />
            </div>

            <Button onClick={handleFormSubmit} type="button">
              Submit
            </Button>
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default DatePickerAdvancedDemo;
