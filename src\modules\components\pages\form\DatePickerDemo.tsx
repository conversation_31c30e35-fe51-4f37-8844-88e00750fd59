import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import { DatePicker, RangePicker, Card, Typography, Icon } from '@/shared/components/common';
import { addDays, subDays, format } from 'date-fns';

/**
 * Trang demo cho DatePicker component
 */
const DatePickerDemo: React.FC = () => {
  const { t } = useTranslation();

  // State cho các ví dụ
  const [basicDate, setBasicDate] = useState<Date | null>(null);
  const [formattedDate, setFormattedDate] = useState<Date | null>(null);
  const [disabledDate, setDisabledDate] = useState<Date | null>(new Date());
  const [rangeDate, setRangeDate] = useState<[Date | null, Date | null]>([null, null]);
  const [customRangeDate, setCustomRangeDate] = useState<[Date | null, Date | null]>([null, null]);

  // Hàm format an toàn
  const safeFormat = (date: Date | null, formatStr: string): string => {
    if (!date) return '';
    try {
      return format(date, formatStr);
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Tạo hàm disabledDates để disable weekends
  const disableWeekends = (date: Date): boolean => {
    try {
      const day = date.getDay();
      return day === 0 || day === 6; // 0 = Sunday, 6 = Saturday
    } catch (error) {
      console.error('Error in disableWeekends:', error);
      return false;
    }
  };

  // Tạo hàm disabledDates để disable một số ngày cụ thể
  const disableSpecificDates = (date: Date): boolean => {
    try {
      // Disable ngày hiện tại và 3 ngày tiếp theo
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const todayPlus3 = addDays(today, 3);
      todayPlus3.setHours(23, 59, 59, 999);

      return date >= today && date <= todayPlus3;
    } catch (error) {
      console.error('Error in disableSpecificDates:', error);
      return false;
    }
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <Typography variant="h1" className="mb-2">
          {t('components.inputs.datepicker.title', 'DatePicker')}
        </Typography>
        <Typography color="muted">
          {t(
            'components.inputs.datepicker.description',
            'DatePicker component cho phép người dùng chọn ngày tháng một cách trực quan.'
          )}
        </Typography>
      </div>

      {/* Overview Card */}
      <Card title="DatePicker Component Overview" className="mb-6">
        <p className="mb-4">
          DatePicker component cung cấp giao diện trực quan để chọn ngày tháng với nhiều tính năng:
        </p>
        <ul className="list-disc list-inside space-y-1 pl-4 mb-4">
          <li>Chọn ngày đơn (Single date picker)</li>
          <li>Chọn khoảng thời gian (Range date picker)</li>
          <li>Hỗ trợ nhiều định dạng ngày tháng</li>
          <li>Disabled dates (không cho phép chọn một số ngày nhất định)</li>
          <li>Responsive và hỗ trợ theme sáng/tối</li>
          <li>Hỗ trợ đa ngôn ngữ</li>
        </ul>
      </Card>

      {/* Basic DatePicker */}
      <ComponentDemo
        title={t('components.inputs.datepicker.basic.title', 'Basic DatePicker')}
        description={t(
          'components.inputs.datepicker.basic.description',
          'DatePicker cơ bản cho phép chọn một ngày.'
        )}
        code={`import { DatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [date, setDate] = useState<Date | null>(null);

// Render
<DatePicker
  label="Select a date"
  value={date}
  onChange={setDate}
  placeholder="DD/MM/YYYY"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <DatePicker
            label="Select a date"
            value={basicDate}
            onChange={setBasicDate}
            placeholder="DD/MM/YYYY"
          />
          {basicDate && (
            <div className="mt-2 text-sm">
              Selected date:{' '}
              <span className="font-medium">{safeFormat(basicDate, 'dd/MM/yyyy')}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* DatePicker with different formats */}
      <ComponentDemo
        title={t('components.inputs.datepicker.formats.title', 'DatePicker with different formats')}
        description={t(
          'components.inputs.datepicker.formats.description',
          'DatePicker hỗ trợ nhiều định dạng ngày tháng khác nhau.'
        )}
        code={`import { DatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [date, setDate] = useState<Date | null>(null);

// Render
<DatePicker
  label="Date with custom format"
  value={date}
  onChange={setDate}
  format="yyyy-MM-dd"
  placeholder="YYYY-MM-DD"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <DatePicker
            label="Date with custom format"
            value={formattedDate}
            onChange={setFormattedDate}
            format="yyyy-MM-dd"
            placeholder="YYYY-MM-DD"
          />
          {formattedDate && (
            <div className="mt-2 text-sm">
              Selected date:{' '}
              <span className="font-medium">{safeFormat(formattedDate, 'yyyy-MM-dd')}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* DatePicker with disabled dates */}
      <ComponentDemo
        title={t('components.inputs.datepicker.disabled.title', 'DatePicker with disabled dates')}
        description={t(
          'components.inputs.datepicker.disabled.description',
          'DatePicker cho phép disable một số ngày nhất định.'
        )}
        code={`import { DatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [date, setDate] = useState<Date | null>(new Date());

// Disable weekends
const disableWeekends = (date: Date): boolean => {
  const day = date.getDay();
  return day === 0 || day === 6; // 0 = Sunday, 6 = Saturday
};

// Render
<DatePicker
  label="Date with disabled weekends"
  value={date}
  onChange={setDate}
  disabledDates={disableWeekends}
  placeholder="DD/MM/YYYY"
  helperText="Weekends are disabled"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <DatePicker
            label="Date with disabled weekends"
            value={disabledDate}
            onChange={setDisabledDate}
            disabledDates={disableWeekends}
            placeholder="DD/MM/YYYY"
            helperText="Weekends are disabled"
          />
        </div>
      </ComponentDemo>

      {/* RangePicker */}
      <ComponentDemo
        title={t('components.inputs.datepicker.range.title', 'Range DatePicker')}
        description={t(
          'components.inputs.datepicker.range.description',
          'RangePicker cho phép chọn khoảng thời gian.'
        )}
        code={`import { RangePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

// Render
<RangePicker
  label="Select date range"
  value={dateRange}
  onChange={setDateRange}
  placeholder={['Start date', 'End date']}
  showDaysCount
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <RangePicker
            label="Select date range"
            value={rangeDate}
            onChange={setRangeDate}
            placeholder={['Start date', 'End date']}
            showDaysCount
          />
        </div>
      </ComponentDemo>

      {/* Advanced RangePicker */}
      <ComponentDemo
        title={t('components.inputs.datepicker.advancedRange.title', 'Advanced Range DatePicker')}
        description={t(
          'components.inputs.datepicker.advancedRange.description',
          'RangePicker với nhiều tính năng nâng cao.'
        )}
        code={`import { RangePicker } from '@/shared/components/common';
import { useState } from 'react';
import { addDays, subDays } from 'date-fns';

// State
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

// Disable specific dates
const disableSpecificDates = (date: Date): boolean => {
  // Disable ngày hiện tại và 3 ngày tiếp theo
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const todayPlus3 = addDays(today, 3);
  todayPlus3.setHours(23, 59, 59, 999);

  return date >= today && date <= todayPlus3;
};

// Render
<RangePicker
  label="Advanced date range"
  value={dateRange}
  onChange={setDateRange}
  placeholder={['Start date', 'End date']}
  disabledDates={disableSpecificDates}
  minDate={subDays(new Date(), 30)}
  maxDate={addDays(new Date(), 30)}
  showTwoMonths
  showDaysCount
  separatorText="to"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <RangePicker
            label="Advanced date range"
            value={customRangeDate}
            onChange={setCustomRangeDate}
            placeholder={['Start date', 'End date']}
            disabledDates={disableSpecificDates}
            minDate={subDays(new Date(), 30)}
            maxDate={addDays(new Date(), 30)}
            showTwoMonths
            showDaysCount
            separatorText="to"
          />
        </div>
      </ComponentDemo>

      {/* DatePicker Sizes */}
      <ComponentDemo
        title={t('components.inputs.datepicker.sizes.title', 'DatePicker Sizes')}
        description={t(
          'components.inputs.datepicker.sizes.description',
          'DatePicker với các kích thước khác nhau.'
        )}
        code={`import { DatePicker, Grid } from '@/shared/components/common';

<Grid columns={1} gap="md">
  <DatePicker
    label="Small"
    size="sm"
    placeholder="Small DatePicker"
  />

  <DatePicker
    label="Medium (default)"
    size="md"
    placeholder="Medium DatePicker"
  />

  <DatePicker
    label="Large"
    size="lg"
    placeholder="Large DatePicker"
  />
</Grid>`}
      >
        <div className="w-full max-w-md mx-auto">
          <div className="space-y-4">
            <DatePicker label="Small" size="sm" placeholder="Small DatePicker" />

            <DatePicker label="Medium (default)" size="md" placeholder="Medium DatePicker" />

            <DatePicker label="Large" size="lg" placeholder="Large DatePicker" />
          </div>
        </div>
      </ComponentDemo>

      {/* Icon Only DatePicker */}
      <ComponentDemo
        title={t('components.inputs.datepicker.iconOnly.title', 'Icon Only DatePicker')}
        description={t(
          'components.inputs.datepicker.iconOnly.description',
          'DatePicker chỉ hiển thị icon, khi bấm vào sẽ hiện calendar.'
        )}
        code={`import { DatePicker } from '@/shared/components/common';
import { useState } from 'react';

// State
const [date, setDate] = useState<Date | null>(null);

// Render
<div className="flex items-center space-x-4">
  <DatePicker
    iconOnly
    value={date}
    onChange={setDate}
    placeholder="Chọn ngày"
  />

  <DatePicker
    iconOnly
    value={date}
    onChange={setDate}
    placeholder="Chọn ngày"
    format="dd/MM/yyyy"
  />

  <DatePicker
    iconOnly
    value={date}
    onChange={setDate}
    size="lg"
    placeholder="Chọn ngày"
  />
</div>`}
      >
        <div className="w-full max-w-md mx-auto">
          <div className="flex items-center space-x-4">
            <DatePicker
              iconOnly
              value={basicDate}
              onChange={setBasicDate}
              placeholder="Chọn ngày"
            />

            <DatePicker
              iconOnly
              value={formattedDate}
              onChange={setFormattedDate}
              placeholder="Chọn ngày"
              format="dd/MM/yyyy"
            />

            <DatePicker
              iconOnly
              value={disabledDate}
              onChange={setDisabledDate}
              size="lg"
              placeholder="Chọn ngày"
            />
          </div>
        </div>
      </ComponentDemo>

      {/* Custom Icon and Hidden Input DatePicker */}
      <ComponentDemo
        title={t(
          'components.inputs.datepicker.customIcon.title',
          'Custom Icon & Hidden Input DatePicker'
        )}
        description={t(
          'components.inputs.datepicker.customIcon.description',
          'DatePicker với icon tùy chỉnh và chế độ ẩn giá trị đã chọn.'
        )}
        code={`import { DatePicker, Icon } from '@/shared/components/common';
import { useState } from 'react';

// State
const [date, setDate] = useState<Date | null>(null);

// Render
<div className="flex items-center space-x-4">
  {/* Custom Icon */}
  <DatePicker
    iconOnly
    value={date}
    onChange={setDate}
    placeholder="Chọn ngày"
    calendarIcon={<Icon name="calendar" size="sm" className="text-primary" />}
  />

  {/* Hidden Input */}
  <DatePicker
    iconOnly
    hiddenInput
    value={date}
    onChange={setDate}
    placeholder="Chọn ngày"
  />

  {/* Custom Icon + Hidden Input */}
  <DatePicker
    iconOnly
    hiddenInput
    value={date}
    onChange={setDate}
    placeholder="Chọn ngày"
    calendarIcon={<Icon name="check" size="sm" className="text-success" />}
  />
</div>`}
      >
        <div className="w-full max-w-md mx-auto">
          <div className="flex items-center space-x-4">
            {/* Custom Icon */}
            <DatePicker
              iconOnly
              value={basicDate}
              onChange={setBasicDate}
              placeholder="Chọn ngày"
              calendarIcon={<Icon name="calendar" size="sm" className="text-primary" />}
            />

            {/* Hidden Input */}
            <DatePicker
              iconOnly
              hiddenInput
              value={formattedDate}
              onChange={setFormattedDate}
              placeholder="Chọn ngày"
            />

            {/* Custom Icon + Hidden Input */}
            <DatePicker
              iconOnly
              hiddenInput
              value={disabledDate}
              onChange={setDisabledDate}
              placeholder="Chọn ngày"
              calendarIcon={<Icon name="check" size="sm" className="text-success" />}
            />
          </div>
        </div>
      </ComponentDemo>

      {/* No Border DatePicker */}
      <ComponentDemo
        title={t('components.inputs.datepicker.noBorder.title', 'No Border DatePicker')}
        description={t(
          'components.inputs.datepicker.noBorder.description',
          'DatePicker không có border xung quanh icon.'
        )}
        code={`import { DatePicker, Icon } from '@/shared/components/common';
import { useState } from 'react';

// State
const [date, setDate] = useState<Date | null>(null);

// Render
<div className="flex items-center space-x-4">
  {/* No Border */}
  <DatePicker
    iconOnly
    noBorder
    value={date}
    onChange={setDate}
    placeholder="Chọn ngày"
  />

  {/* No Border + Hidden Input */}
  <DatePicker
    iconOnly
    noBorder
    hiddenInput
    value={date}
    onChange={setDate}
    placeholder="Chọn ngày"
  />

  {/* No Border + Custom Icon */}
  <DatePicker
    iconOnly
    noBorder
    value={date}
    onChange={setDate}
    placeholder="Chọn ngày"
    calendarIcon={<Icon name="calendar" size="sm" className="text-primary" />}
  />
</div>`}
      >
        <div className="w-full max-w-md mx-auto">
          <div className="flex items-center space-x-4">
            {/* No Border */}
            <DatePicker
              iconOnly
              noBorder
              value={basicDate}
              onChange={setBasicDate}
              placeholder="Chọn ngày"
            />

            {/* No Border + Hidden Input */}
            <DatePicker
              iconOnly
              noBorder
              hiddenInput
              value={formattedDate}
              onChange={setFormattedDate}
              placeholder="Chọn ngày"
            />

            {/* No Border + Custom Icon */}
            <DatePicker
              iconOnly
              noBorder
              value={disabledDate}
              onChange={setDisabledDate}
              placeholder="Chọn ngày"
              calendarIcon={<Icon name="calendar" size="sm" className="text-primary" />}
            />
          </div>
        </div>
      </ComponentDemo>

      {/* Usage Guide */}
      <Card title="DatePicker Usage Guide" className="mb-6">
        <div className="space-y-4">
          <p className="font-medium">Basic Usage:</p>
          <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm overflow-auto">
            {`import { DatePicker, RangePicker } from '@/shared/components/common';
import { useState } from 'react';

// Single date picker
const [date, setDate] = useState<Date | null>(null);

<DatePicker
  label="Select date"
  value={date}
  onChange={setDate}
  placeholder="DD/MM/YYYY"
/>

// Range date picker
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

<RangePicker
  label="Select date range"
  value={dateRange}
  onChange={setDateRange}
  placeholder={['Start date', 'End date']}
/>`}
          </pre>

          <p className="font-medium mt-4">Key Features:</p>
          <ul className="list-disc list-inside space-y-1 pl-4">
            <li>
              Sử dụng <code>format</code> để thay đổi định dạng ngày tháng
            </li>
            <li>
              Sử dụng <code>disabledDates</code> để disable một số ngày nhất định
            </li>
            <li>
              Sử dụng <code>minDate</code> và <code>maxDate</code> để giới hạn phạm vi chọn
            </li>
            <li>
              Sử dụng <code>iconOnly</code> để hiển thị DatePicker dạng icon
            </li>
            <li>
              Sử dụng <code>hiddenInput</code> để ẩn giá trị đã chọn khi ở chế độ iconOnly
            </li>
            <li>
              Sử dụng <code>noBorder</code> để ẩn border xung quanh icon
            </li>
            <li>
              Sử dụng <code>calendarIcon</code> để thay đổi icon hiển thị
            </li>
            <li>
              Sử dụng <code>showTwoMonths</code> trong RangePicker để hiển thị hai tháng cạnh nhau
            </li>
            <li>
              Sử dụng <code>showDaysCount</code> trong RangePicker để hiển thị số ngày đã chọn
            </li>
          </ul>

          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
            <p className="font-medium">Advanced Examples:</p>
            <p className="mt-1">
              Xem thêm các ví dụ nâng cao về cách sử dụng DatePicker trong các tình huống thực tế:
            </p>
            <a
              href="/datepicker-advanced-demo"
              className="mt-2 inline-block px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
            >
              Xem DatePicker Advanced Examples
            </a>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DatePickerDemo;
