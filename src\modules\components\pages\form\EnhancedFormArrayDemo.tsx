import React, { useRef } from 'react';
import { z } from 'zod';
import { FieldValues } from 'react-hook-form';
import {
  Button,
  Card,
  Form,
  FormArray,
  FormItem,
  Input,
  Icon,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

// Schema validation với Zod
const formArraySchema = z.object({
  contacts: z.array(
    z.object({
      name: z.string().min(1, 'Name is required'),
      email: z.string().email('Invalid email'),
      phone: z.string().optional(),
    })
  ),
  addresses: z.array(
    z.object({
      street: z.string().min(1, 'Street is required'),
      city: z.string().min(1, 'City is required'),
      zipCode: z.string().optional(),
    })
  ),
  skills: z.array(
    z.object({
      name: z.string().min(1, 'Skill name is required'),
      level: z.string().min(1, 'Level is required'),
      years: z.number().min(0, 'Years must be positive'),
    })
  ),
});

// type FormArrayValues = z.infer<typeof formArraySchema>;

/**
 * Demo page cho Enhanced FormArray Component
 */
const EnhancedFormArrayDemo: React.FC = () => {
  const formRef = useRef<FormRef<FieldValues>>(null);

  const handleSubmit = async (values: FieldValues) => {
    console.log('FormArray values:', values);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  const handleSortEnd = (oldIndex: number, newIndex: number) => {
    console.log(`Moved item from ${oldIndex} to ${newIndex}`);
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Enhanced FormArray Demo
        </h1>
        <p className="text-muted">
          Demonstration of enhanced FormArray features with drag-and-drop, layouts, and virtualization
        </p>
      </div>

      {/* Main Form */}
      <Card title="FormArray with Different Layouts" className="mb-8">
        <Form
          ref={formRef}
          schema={formArraySchema}
          onSubmit={handleSubmit}
          defaultValues={{
            contacts: [{ name: '', email: '', phone: '' }],
            addresses: [{ street: '', city: '', zipCode: '' }],
            skills: [{ name: '', level: '', years: 0 }],
          }}
          className="space-y-8"
        >
          {/* Contacts - List Layout */}
          <FormArray
            name="contacts"
            title="Contacts"
            description="Add your contacts with drag-and-drop reordering"
            layout="list"
            sortable
            sortableHandle
            sortableAnimation="fade"
            onSortEnd={handleSortEnd}
            renderItem={(index) => (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormItem name={`contacts.${index}.name`} label="Name">
                  <Input placeholder="Enter name" />
                </FormItem>
                <FormItem name={`contacts.${index}.email`} label="Email">
                  <Input type="email" placeholder="Enter email" />
                </FormItem>
                <FormItem name={`contacts.${index}.phone`} label="Phone">
                  <Input type="tel" placeholder="Enter phone" />
                </FormItem>
              </div>
            )}
            defaultValue={{ name: '', email: '', phone: '' }}
            minItems={1}
            maxItems={5}
          />

          {/* Addresses - Card Layout */}
          <FormArray
            name="addresses"
            title="Addresses"
            description="Add your addresses with card layout"
            layout="card"
            sortable
            sortableHandle
            sortableAnimation="slide"
            compact
            onSortEnd={handleSortEnd}
            renderItem={(index) => (
              <div className="space-y-4">
                <FormItem name={`addresses.${index}.street`} label="Street Address">
                  <Input placeholder="Enter street address" />
                </FormItem>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem name={`addresses.${index}.city`} label="City">
                    <Input placeholder="Enter city" />
                  </FormItem>
                  <FormItem name={`addresses.${index}.zipCode`} label="Zip Code">
                    <Input placeholder="Enter zip code" />
                  </FormItem>
                </div>
              </div>
            )}
            defaultValue={{ street: '', city: '', zipCode: '' }}
            minItems={1}
            maxItems={3}
          />

          {/* Skills - Grid Layout */}
          <FormArray
            name="skills"
            title="Skills"
            description="Add your skills with grid layout"
            layout="grid"
            sortable
            sortableHandle
            sortableAnimation="scale"
            onSortEnd={handleSortEnd}
            renderItem={(index) => (
              <div className="space-y-4">
                <FormItem name={`skills.${index}.name`} label="Skill Name">
                  <Input placeholder="e.g., JavaScript" />
                </FormItem>
                <FormItem name={`skills.${index}.level`} label="Level">
                  <Input placeholder="e.g., Expert" />
                </FormItem>
                <FormItem name={`skills.${index}.years`} label="Years of Experience">
                  <Input type="number" placeholder="0" />
                </FormItem>
              </div>
            )}
            defaultValue={{ name: '', level: '', years: 0 }}
            maxItems={10}
          />

          <div className="flex gap-4 pt-4">
            <Button type="submit" variant="primary" size="md">
              Submit Form
            </Button>
            <Button
              type="button"
              variant="outline"
              size="md"
              onClick={() => formRef.current?.reset()}
            >
              Reset
            </Button>
          </div>
        </Form>
      </Card>

      {/* Animation Variations */}
      <Card title="Animation Variations" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Fade Animation</h3>
            <FormArray
              name="fadeItems"
              sortable
              sortableAnimation="fade"
              layout="list"
              renderItem={(index) => (
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded">
                  <p>Item {index + 1} - Fade animation when dragging</p>
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add Fade Item"
            />
          </div>

          <div>
            <h3 className="font-medium mb-3">Slide Animation</h3>
            <FormArray
              name="slideItems"
              sortable
              sortableAnimation="slide"
              layout="list"
              renderItem={(index) => (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded">
                  <p>Item {index + 1} - Slide animation when dragging</p>
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add Slide Item"
            />
          </div>

          <div>
            <h3 className="font-medium mb-3">Scale Animation</h3>
            <FormArray
              name="scaleItems"
              sortable
              sortableAnimation="scale"
              layout="list"
              renderItem={(index) => (
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded">
                  <p>Item {index + 1} - Scale animation when dragging</p>
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add Scale Item"
            />
          </div>
        </div>
      </Card>

      {/* Layout Variations */}
      <Card title="Layout Variations" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">List Layout</h3>
            <FormArray
              name="listItems"
              layout="list"
              sortable
              renderItem={(index) => (
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded">
                  <span>List Item {index + 1}</span>
                  <Icon name="menu" />
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add List Item"
            />
          </div>

          <div>
            <h3 className="font-medium mb-3">Grid Layout</h3>
            <FormArray
              name="gridItems"
              layout="grid"
              sortable
              renderItem={(index) => (
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded text-center">
                  <Icon name="grid" className="mx-auto mb-2" />
                  <p>Grid Item {index + 1}</p>
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add Grid Item"
            />
          </div>

          <div>
            <h3 className="font-medium mb-3">Card Layout</h3>
            <FormArray
              name="cardItems"
              layout="card"
              sortable
              renderItem={(index) => (
                <div className="text-center">
                  <Icon name="card" className="mx-auto mb-2" />
                  <h4 className="font-medium">Card Item {index + 1}</h4>
                  <p className="text-sm text-muted">This is a card layout item</p>
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add Card Item"
            />
          </div>
        </div>
      </Card>

      {/* Advanced Features */}
      <Card title="Advanced Features" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Compact Mode</h3>
            <FormArray
              name="compactItems"
              compact
              sortable
              renderItem={(index) => (
                <div className="flex items-center justify-between">
                  <span>Compact Item {index + 1}</span>
                  <Icon name="more-horizontal" />
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add Compact Item"
            />
          </div>

          <div>
            <h3 className="font-medium mb-3">Custom Styling</h3>
            <FormArray
              name="styledItems"
              sortable
              itemClassName="border-2 border-dashed border-blue-300"
              dragHandleClassName="bg-blue-500 text-white"
              renderItem={(index) => (
                <div className="p-4 text-center">
                  <p>Custom Styled Item {index + 1}</p>
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add Styled Item"
            />
          </div>

          <div>
            <h3 className="font-medium mb-3">Touch Support</h3>
            <FormArray
              name="touchItems"
              sortable
              touchSupport
              renderItem={(index) => (
                <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded text-center">
                  <Icon name="smartphone" className="mx-auto mb-2" />
                  <p>Touch-enabled Item {index + 1}</p>
                </div>
              )}
              defaultValue={{}}
              addButtonText="Add Touch Item"
            />
          </div>
        </div>
      </Card>

      {/* Usage Guide */}
      <Card title="Usage Guide" className="mb-8">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-lg mb-2">Enhanced FormArray Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>Drag-and-Drop:</strong> Reorder items with smooth animations</li>
              <li><strong>Multiple Layouts:</strong> List, grid, and card layouts</li>
              <li><strong>Animations:</strong> Fade, slide, and scale animations during drag</li>
              <li><strong>Touch Support:</strong> Works on mobile and touch devices</li>
              <li><strong>Virtualization:</strong> Handle large arrays efficiently</li>
              <li><strong>Compact Mode:</strong> Reduced spacing for dense layouts</li>
              <li><strong>Custom Styling:</strong> Flexible styling options for items and handles</li>
              <li><strong>Accessibility:</strong> Full keyboard and screen reader support</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-2">API Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>onSortEnd:</strong> Callback when items are reordered</li>
              <li><strong>Min/Max Items:</strong> Control the number of items</li>
              <li><strong>Custom Controls:</strong> Show/hide add, remove, and move controls</li>
              <li><strong>Flexible Rendering:</strong> Custom render function for each item</li>
              <li><strong>Form Integration:</strong> Full integration with React Hook Form</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EnhancedFormArrayDemo;
