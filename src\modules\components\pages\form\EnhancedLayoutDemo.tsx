import React from 'react';
import {
  Card,
  FormHorizontal,
  FormInline,
  FormItem,
  Input,
  Button,
  // Icon,
} from '@/shared/components/common';

/**
 * Demo page cho Enhanced FormHorizontal và FormInline Components
 */
const EnhancedLayoutDemo: React.FC = () => {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Enhanced Form Layout Demo
        </h1>
        <p className="text-muted">
          Demonstration of enhanced FormHorizontal and FormInline features with responsive layouts
        </p>
      </div>

      {/* FormHorizontal Demos */}
      <Card title="FormHorizontal - Basic Layout" className="mb-8">
        <FormHorizontal labelWidth="md" gap="md" colon>
          <FormItem name="name" label="Full Name">
            <Input placeholder="Enter your full name" />
          </FormItem>
          <FormItem name="email" label="Email Address">
            <Input type="email" placeholder="Enter your email" />
          </FormItem>
          <FormItem name="phone" label="Phone Number">
            <Input type="tel" placeholder="Enter your phone" />
          </FormItem>
        </FormHorizontal>
      </Card>

      <Card title="FormHorizontal - Responsive Layout" className="mb-8">
        <FormHorizontal
          labelWidth="lg"
          gap="lg"
          responsive
          responsiveBreakpoint="md"
          labelAlign="right"
          colon
          size="lg"
        >
          <FormItem name="company" label="Company Name">
            <Input placeholder="Enter company name" />
          </FormItem>
          <FormItem name="position" label="Job Position">
            <Input placeholder="Enter your position" />
          </FormItem>
          <FormItem name="experience" label="Years of Experience">
            <Input type="number" placeholder="Enter years" />
          </FormItem>
        </FormHorizontal>
      </Card>

      <Card title="FormHorizontal - Alignment Variations" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Left Aligned Labels</h3>
            <FormHorizontal labelAlign="left" labelWidth="sm" gap="md">
              <FormItem name="left1" label="Field 1">
                <Input placeholder="Left aligned label" />
              </FormItem>
              <FormItem name="left2" label="Field 2">
                <Input placeholder="Left aligned label" />
              </FormItem>
            </FormHorizontal>
          </div>

          <div>
            <h3 className="font-medium mb-3">Center Aligned Labels</h3>
            <FormHorizontal labelAlign="center" labelWidth="sm" gap="md">
              <FormItem name="center1" label="Field 1">
                <Input placeholder="Center aligned label" />
              </FormItem>
              <FormItem name="center2" label="Field 2">
                <Input placeholder="Center aligned label" />
              </FormItem>
            </FormHorizontal>
          </div>

          <div>
            <h3 className="font-medium mb-3">Right Aligned Labels (Default)</h3>
            <FormHorizontal labelAlign="right" labelWidth="sm" gap="md">
              <FormItem name="right1" label="Field 1">
                <Input placeholder="Right aligned label" />
              </FormItem>
              <FormItem name="right2" label="Field 2">
                <Input placeholder="Right aligned label" />
              </FormItem>
            </FormHorizontal>
          </div>
        </div>
      </Card>

      <Card title="FormHorizontal - Compact Mode" className="mb-8">
        <FormHorizontal
          labelWidth="md"
          gap="sm"
          size="sm"
          compact
          colon
          labelWrap
        >
          <FormItem name="compact1" label="Compact Field 1">
            <Input placeholder="Compact layout" />
          </FormItem>
          <FormItem name="compact2" label="Compact Field 2 with Long Label">
            <Input placeholder="Compact layout" />
          </FormItem>
          <FormItem name="compact3" label="Compact Field 3">
            <Input placeholder="Compact layout" />
          </FormItem>
        </FormHorizontal>
      </Card>

      {/* FormInline Demos */}
      <Card title="FormInline - Basic Layout" className="mb-8">
        <FormInline gap="md" align="center" labelInline>
          <FormItem name="search" label="Search">
            <Input placeholder="Enter search term" />
          </FormItem>
          <FormItem name="category" label="Category">
            <Input placeholder="Select category" />
          </FormItem>
          <Button type="submit" variant="primary">
            Search
          </Button>
        </FormInline>
      </Card>

      <Card title="FormInline - Responsive Layout" className="mb-8">
        <FormInline
          gap="lg"
          align="end"
          responsive
          responsiveBreakpoint="md"
          equalWidth
        >
          <FormItem name="startDate" label="Start Date">
            <Input type="date" />
          </FormItem>
          <FormItem name="endDate" label="End Date">
            <Input type="date" />
          </FormItem>
          <FormItem name="status" label="Status">
            <Input placeholder="Select status" />
          </FormItem>
          <Button type="submit" variant="outline">
            Filter
          </Button>
        </FormInline>
      </Card>

      <Card title="FormInline - Direction Variations" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Row Direction (Default)</h3>
            <FormInline direction="row" gap="md" align="center">
              <FormItem name="row1" label="Field 1">
                <Input placeholder="Row direction" />
              </FormItem>
              <FormItem name="row2" label="Field 2">
                <Input placeholder="Row direction" />
              </FormItem>
              <Button type="button" variant="ghost">Action</Button>
            </FormInline>
          </div>

          <div>
            <h3 className="font-medium mb-3">Column Direction</h3>
            <FormInline direction="column" gap="md" align="start">
              <FormItem name="col1" label="Field 1">
                <Input placeholder="Column direction" />
              </FormItem>
              <FormItem name="col2" label="Field 2">
                <Input placeholder="Column direction" />
              </FormItem>
              <Button type="button" variant="ghost">Action</Button>
            </FormInline>
          </div>

          <div>
            <h3 className="font-medium mb-3">Row Reverse Direction</h3>
            <FormInline direction="row-reverse" gap="md" align="center">
              <FormItem name="reverse1" label="Field 1">
                <Input placeholder="Row reverse" />
              </FormItem>
              <FormItem name="reverse2" label="Field 2">
                <Input placeholder="Row reverse" />
              </FormItem>
              <Button type="button" variant="ghost">Action</Button>
            </FormInline>
          </div>
        </div>
      </Card>

      <Card title="FormInline - Flex Behaviors" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Equal Width Items</h3>
            <FormInline equalWidth gap="md" align="center">
              <FormItem name="equal1" label="Field 1">
                <Input placeholder="Equal width" />
              </FormItem>
              <FormItem name="equal2" label="Field 2">
                <Input placeholder="Equal width" />
              </FormItem>
              <FormItem name="equal3" label="Field 3">
                <Input placeholder="Equal width" />
              </FormItem>
            </FormInline>
          </div>

          <div>
            <h3 className="font-medium mb-3">Grow and Shrink</h3>
            <FormInline grow shrink gap="md" align="center">
              <FormItem name="grow1" label="Grow Field">
                <Input placeholder="This field will grow" />
              </FormItem>
              <FormItem name="grow2" label="Normal">
                <Input placeholder="Normal field" />
              </FormItem>
              <Button type="button" variant="ghost">Fixed Button</Button>
            </FormInline>
          </div>

          <div>
            <h3 className="font-medium mb-3">Width Constraints</h3>
            <FormInline
              gap="md"
              align="center"
              minItemWidth="150px"
              maxItemWidth="300px"
            >
              <FormItem name="constrained1" label="Min/Max Width">
                <Input placeholder="Constrained width" />
              </FormItem>
              <FormItem name="constrained2" label="Also Constrained">
                <Input placeholder="Also constrained" />
              </FormItem>
              <Button type="button" variant="ghost">Action</Button>
            </FormInline>
          </div>
        </div>
      </Card>

      <Card title="FormInline - Compact Mode" className="mb-8">
        <FormInline compact size="sm" gap="sm" align="center" labelInline>
          <FormItem name="compact1" label="Field 1">
            <Input placeholder="Compact" />
          </FormItem>
          <FormItem name="compact2" label="Field 2">
            <Input placeholder="Compact" />
          </FormItem>
          <FormItem name="compact3" label="Field 3">
            <Input placeholder="Compact" />
          </FormItem>
          <Button type="submit" variant="primary" size="sm">
            Submit
          </Button>
        </FormInline>
      </Card>

      <Card title="FormInline - Justify Variations" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Space Between</h3>
            <FormInline justify="between" gap="md" align="center">
              <FormItem name="between1" label="Start">
                <Input placeholder="Start field" />
              </FormItem>
              <FormItem name="between2" label="End">
                <Input placeholder="End field" />
              </FormItem>
            </FormInline>
          </div>

          <div>
            <h3 className="font-medium mb-3">Center Justified</h3>
            <FormInline justify="center" gap="md" align="center">
              <FormItem name="center1" label="Field 1">
                <Input placeholder="Centered" />
              </FormItem>
              <FormItem name="center2" label="Field 2">
                <Input placeholder="Centered" />
              </FormItem>
              <Button type="button" variant="ghost">Action</Button>
            </FormInline>
          </div>

          <div>
            <h3 className="font-medium mb-3">Space Around</h3>
            <FormInline justify="around" gap="md" align="center">
              <FormItem name="around1" label="Field 1">
                <Input placeholder="Space around" />
              </FormItem>
              <FormItem name="around2" label="Field 2">
                <Input placeholder="Space around" />
              </FormItem>
              <Button type="button" variant="ghost">Action</Button>
            </FormInline>
          </div>
        </div>
      </Card>

      {/* Usage Guide */}
      <Card title="Usage Guide" className="mb-8">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-lg mb-2">Enhanced FormHorizontal Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>Responsive Layout:</strong> Automatically switch to vertical layout on mobile</li>
              <li><strong>Label Alignment:</strong> Left, center, or right alignment for labels</li>
              <li><strong>Flexible Sizing:</strong> Small, medium, and large sizes with compact mode</li>
              <li><strong>Colon Support:</strong> Optional colon after labels</li>
              <li><strong>Label Wrapping:</strong> Allow labels to wrap to multiple lines</li>
              <li><strong>Vertical Alignment:</strong> Control vertical alignment of form rows</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-2">Enhanced FormInline Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>Flex Direction:</strong> Row, column, and reverse directions</li>
              <li><strong>Responsive Behavior:</strong> Switch to column layout on mobile</li>
              <li><strong>Flex Controls:</strong> Grow, shrink, and equal width options</li>
              <li><strong>Width Constraints:</strong> Set minimum and maximum widths for items</li>
              <li><strong>Justify Options:</strong> Space between, around, evenly, and center</li>
              <li><strong>Compact Mode:</strong> Reduced spacing for dense layouts</li>
              <li><strong>Label Inline:</strong> Show labels inline with inputs</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EnhancedLayoutDemo;
