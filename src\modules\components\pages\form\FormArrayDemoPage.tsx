import React from 'react';
import { ComponentDemo } from '../../components';
import {
  Card,
  Form,
  FormItem,
  FormArray,
  FormGrid,
  FormSection,
  Input,
  Button,
} from '@/shared/components/common';
import { z } from 'zod';

/**
 * Trang demo cho Form Array với hiển thị code
 */
const FormArrayDemoPage: React.FC = () => {
  return (
    <div className="space-y-8">
      <Card title="Form Array" className="mb-6">
        <p className="mb-4">
          Form Array là component để quản lý mảng các field trong form (dynamic fields).
        </p>
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
          <h3 className="text-sm font-medium mb-2">Tính năng chính:</h3>
          <ul className="list-disc list-inside text-sm space-y-1 text-gray-600 dark:text-gray-400">
            <li>Thêm/xóa field động</li>
            <li>Validation cho từng item trong mảng</li>
            <li>Hỗ trợ nested objects trong mảng</li>
            <li>Drag and drop để sắp xếp lại các item</li>
            <li>Số lượng item tối thiểu/tối đa</li>
          </ul>
        </div>
      </Card>

      {/* Demo 1: Danh sách liên hệ */}
      <ComponentDemo
        title="Danh sách liên hệ"
        description="Demo cơ bản về FormArray với danh sách liên hệ"
        code={`import { z } from 'zod';
import { Form, FormItem, FormArray, FormGrid, Input, Button } from '@/shared/components/common';

// Schema validation
const contactSchema = z.object({
  contacts: z.array(
    z.object({
      name: z.string().min(1, 'Tên là bắt buộc'),
      email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
      phone: z.string().optional().or(z.literal('')),
    })
  ),
});

// Component
<Form
  schema={contactSchema}
  onSubmit={handleSubmit}
  defaultValues={{
    contacts: [{ name: '', email: '', phone: '' }],
  }}
>
  <FormArray
    name="contacts"
    title="Danh sách liên hệ"
    description="Thêm các liên hệ của bạn"
    addButtonText="Thêm liên hệ"
    defaultValue={{ name: '', email: '', phone: '' }}
    minItems={1}
    renderItem={(index) => (
      <FormGrid columns={2} gap="md">
        <FormItem
          name={\`contacts.\${index}.name\`}
          label="Tên"
          required
        >
          <Input placeholder="Nhập tên" fullWidth />
        </FormItem>

        <FormItem
          name={\`contacts.\${index}.email\`}
          label="Email"
        >
          <Input
            type="email"
            placeholder="Nhập email"
            fullWidth
          />
        </FormItem>

        <FormItem
          name={\`contacts.\${index}.phone\`}
          label="Số điện thoại"
          className="col-span-2"
        >
          <Input
            placeholder="Nhập số điện thoại"
            fullWidth
          />
        </FormItem>
      </FormGrid>
    )}
  />

  <Button type="submit" variant="primary">
    Lưu danh sách liên hệ
  </Button>
</Form>`}
      >
        <div className="border p-4 rounded-lg">
          <Form
            schema={z.object({
              contacts: z.array(
                z.object({
                  name: z.string().min(1, 'Tên là bắt buộc'),
                  email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
                  phone: z.string().optional().or(z.literal('')),
                })
              ),
            })}
            onSubmit={values => console.log(values)}
            defaultValues={{
              contacts: [{ name: '', email: '', phone: '' }],
            }}
          >
            <FormArray
              name="contacts"
              title="Danh sách liên hệ"
              description="Thêm các liên hệ của bạn"
              addButtonText="Thêm liên hệ"
              defaultValue={{ name: '', email: '', phone: '' }}
              minItems={1}
              renderItem={index => (
                <FormGrid columns={2} gap="md">
                  <FormItem name={`contacts.${index}.name`} label="Tên" required>
                    <Input placeholder="Nhập tên" fullWidth />
                  </FormItem>

                  <FormItem name={`contacts.${index}.email`} label="Email">
                    <Input type="email" placeholder="Nhập email" fullWidth />
                  </FormItem>

                  <FormItem
                    name={`contacts.${index}.phone`}
                    label="Số điện thoại"
                    className="col-span-2"
                  >
                    <Input placeholder="Nhập số điện thoại" fullWidth />
                  </FormItem>
                </FormGrid>
              )}
            />

            <div className="pt-4">
              <Button type="submit" variant="primary">
                Lưu danh sách liên hệ
              </Button>
            </div>
          </Form>
        </div>
      </ComponentDemo>

      {/* Demo 2: Kết hợp với FormSection */}
      <ComponentDemo
        title="Kết hợp với FormSection"
        description="Kết hợp FormArray với FormSection để tổ chức form phức tạp"
        code={`import { Form, FormItem, FormArray, FormSection, Input, Button } from '@/shared/components/common';

<Form
  schema={educationSchema}
  defaultValues={{
    education: [
      {
        school: '',
        degree: '',
        startYear: '',
        endYear: '',
      },
    ],
  }}
>
  <FormArray
    name="education"
    title="Quá trình học tập"
    description="Thêm các bằng cấp và quá trình học tập của bạn"
    addButtonText="Thêm quá trình học tập"
    defaultValue={{
      school: '',
      degree: '',
      startYear: '',
      endYear: '',
    }}
    minItems={1}
    renderItem={(index) => (
      <FormSection
        title={\`Quá trình học tập \${index + 1}\`}
        collapsible
        defaultExpanded={index === 0}
      >
        <FormGrid columns={2} gap="md">
          <FormItem
            name={\`education.\${index}.school\`}
            label="Trường"
            required
          >
            <Input placeholder="Nhập tên trường" fullWidth />
          </FormItem>

          <FormItem
            name={\`education.\${index}.degree\`}
            label="Bằng cấp"
            required
          >
            <Input placeholder="Nhập bằng cấp" fullWidth />
          </FormItem>

          <FormItem
            name={\`education.\${index}.startYear\`}
            label="Năm bắt đầu"
            required
          >
            <Input placeholder="Ví dụ: 2018" fullWidth />
          </FormItem>

          <FormItem
            name={\`education.\${index}.endYear\`}
            label="Năm kết thúc"
          >
            <Input placeholder="Ví dụ: 2022" fullWidth />
          </FormItem>
        </FormGrid>
      </FormSection>
    )}
  />
</Form>`}
      >
        <div className="border p-4 rounded-lg">
          <Form
            schema={z.object({
              education: z.array(
                z.object({
                  school: z.string().min(1, 'Tên trường là bắt buộc'),
                  degree: z.string().min(1, 'Bằng cấp là bắt buộc'),
                  startYear: z.string().regex(/^\d{4}$/, 'Năm bắt đầu phải có 4 chữ số'),
                  endYear: z
                    .string()
                    .regex(/^\d{4}$/, 'Năm kết thúc phải có 4 chữ số')
                    .optional()
                    .or(z.literal('')),
                })
              ),
            })}
            onSubmit={values => console.log(values)}
            defaultValues={{
              education: [
                {
                  school: '',
                  degree: '',
                  startYear: '',
                  endYear: '',
                },
              ],
            }}
          >
            <FormArray
              name="education"
              title="Quá trình học tập"
              description="Thêm các bằng cấp và quá trình học tập của bạn"
              addButtonText="Thêm quá trình học tập"
              defaultValue={{
                school: '',
                degree: '',
                startYear: '',
                endYear: '',
              }}
              minItems={1}
              renderItem={index => (
                <FormSection
                  title={`Quá trình học tập ${index + 1}`}
                  collapsible
                  defaultExpanded={index === 0}
                >
                  <FormGrid columns={2} gap="md">
                    <FormItem name={`education.${index}.school`} label="Trường" required>
                      <Input placeholder="Nhập tên trường" fullWidth />
                    </FormItem>

                    <FormItem name={`education.${index}.degree`} label="Bằng cấp" required>
                      <Input placeholder="Nhập bằng cấp" fullWidth />
                    </FormItem>

                    <FormItem name={`education.${index}.startYear`} label="Năm bắt đầu" required>
                      <Input placeholder="Ví dụ: 2018" fullWidth />
                    </FormItem>

                    <FormItem name={`education.${index}.endYear`} label="Năm kết thúc">
                      <Input placeholder="Ví dụ: 2022" fullWidth />
                    </FormItem>
                  </FormGrid>
                </FormSection>
              )}
            />

            <div className="pt-4">
              <Button type="submit" variant="primary">
                Lưu quá trình học tập
              </Button>
            </div>
          </Form>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default FormArrayDemoPage;
