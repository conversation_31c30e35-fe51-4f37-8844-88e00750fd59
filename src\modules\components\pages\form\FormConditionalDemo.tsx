import React, { useRef } from 'react';
import { z } from 'zod';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Toggle,
  Icon,
  ConditionalField,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

// Định nghĩa schema validation với Zod
const userSchema = z.object({
  userType: z.enum(['personal', 'business']),
  name: z.string().min(1, 'Tên là bắt buộc'),
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),

  // Các field có điều kiện cho userType = business
  companyName: z.string().min(1, 'Tên công ty là bắt buộc').optional(),
  taxId: z.string().min(1, '<PERSON><PERSON> số thuế là bắt buộc').optional(),

  // Các field có điều kiện cho hasBillingAddress = true
  hasBillingAddress: z.boolean().optional(),
  billingAddress: z.string().min(1, 'Địa chỉ thanh toán là bắt buộc').optional(),
  billingCity: z.string().min(1, 'Thành phố là bắt buộc').optional(),

  // Các field có điều kiện phức tạp (AND)
  hasSpecialRequirements: z.boolean().optional(),
  specialRequirements: z.string().min(1, 'Yêu cầu đặc biệt là bắt buộc').optional(),

  // Các field có điều kiện phức tạp (OR)
  contactMethod: z.enum(['email', 'phone', 'both']).optional(),
  phoneNumber: z.string().min(1, 'Số điện thoại là bắt buộc').optional(),
});

// Định nghĩa kiểu dữ liệu từ schema
type UserFormValues = z.infer<typeof userSchema>;

/**
 * Trang demo cho ConditionalField component
 */
const FormConditionalDemo: React.FC = () => {
  const formRef = useRef<FormRef<UserFormValues>>(null);
  const [formData, setFormData] = React.useState<UserFormValues | null>(null);

  // Xử lý submit form
  const handleSubmit = (values: UserFormValues) => {
    console.log('Form submitted:', values);
    setFormData(values);
  };

  return (
    <div className="space-y-8">
      <Card title="Form Conditional Fields Demo" className="mb-6">
        <p className="mb-4">
          Demo component ConditionalField để hiển thị/ẩn field dựa trên điều kiện.
        </p>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Form với Conditional Fields" className="mb-6">
          <Form
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ref={formRef as any}
            schema={userSchema}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSubmit={handleSubmit as any}
            className="space-y-4"
            defaultValues={{
              userType: 'personal',
              hasBillingAddress: false,
              hasSpecialRequirements: false,
              contactMethod: 'email',
            }}
          >
            {/* Loại người dùng */}
            <FormItem name="userType" label="Loại tài khoản" required>
              <select className="w-full p-2 border rounded">
                <option value="personal">Cá nhân</option>
                <option value="business">Doanh nghiệp</option>
              </select>
            </FormItem>

            {/* Thông tin cơ bản */}
            <FormItem name="name" label="Tên" required>
              <Input placeholder="Nhập tên của bạn" fullWidth />
            </FormItem>

            <FormItem name="email" label="Email" required>
              <Input
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Icon name="chat" size="sm" />}
                fullWidth
              />
            </FormItem>

            {/* Các field có điều kiện cho userType = business */}
            <ConditionalField
              condition={{
                field: 'userType',
                type: ConditionType.EQUALS,
                value: 'business',
              }}
            >
              <FormItem name="companyName" label="Tên công ty" required>
                <Input placeholder="Nhập tên công ty" fullWidth />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'userType',
                type: ConditionType.EQUALS,
                value: 'business',
              }}
            >
              <FormItem name="taxId" label="Mã số thuế" required>
                <Input placeholder="Nhập mã số thuế" fullWidth />
              </FormItem>
            </ConditionalField>

            {/* Địa chỉ thanh toán */}
            <FormItem name="hasBillingAddress" label="Có địa chỉ thanh toán khác?" inline>
              <Toggle />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'hasBillingAddress',
                type: ConditionType.IS_TRUE,
              }}
            >
              <FormItem name="billingAddress" label="Địa chỉ thanh toán" required>
                <Input placeholder="Nhập địa chỉ thanh toán" fullWidth />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'hasBillingAddress',
                type: ConditionType.IS_TRUE,
              }}
            >
              <FormItem name="billingCity" label="Thành phố" required>
                <Input placeholder="Nhập thành phố" fullWidth />
              </FormItem>
            </ConditionalField>

            {/* Yêu cầu đặc biệt (điều kiện phức tạp - AND) */}
            <FormItem name="hasSpecialRequirements" label="Có yêu cầu đặc biệt?" inline>
              <Toggle />
            </FormItem>

            <ConditionalField
              condition={{
                and: [
                  {
                    field: 'hasSpecialRequirements',
                    type: ConditionType.IS_TRUE,
                  },
                  {
                    field: 'userType',
                    type: ConditionType.EQUALS,
                    value: 'business',
                  },
                ],
              }}
            >
              <FormItem name="specialRequirements" label="Yêu cầu đặc biệt" required>
                <textarea
                  className="w-full p-2 border rounded"
                  placeholder="Nhập yêu cầu đặc biệt của bạn"
                  rows={3}
                />
              </FormItem>
            </ConditionalField>

            {/* Phương thức liên hệ (điều kiện phức tạp - OR) */}
            <FormItem name="contactMethod" label="Phương thức liên hệ" required>
              <select className="w-full p-2 border rounded">
                <option value="email">Email</option>
                <option value="phone">Điện thoại</option>
                <option value="both">Cả hai</option>
              </select>
            </FormItem>

            <ConditionalField
              condition={{
                or: [
                  {
                    field: 'contactMethod',
                    type: ConditionType.EQUALS,
                    value: 'phone',
                  },
                  {
                    field: 'contactMethod',
                    type: ConditionType.EQUALS,
                    value: 'both',
                  },
                ],
              }}
            >
              <FormItem name="phoneNumber" label="Số điện thoại" required>
                <Input placeholder="Nhập số điện thoại" fullWidth />
              </FormItem>
            </ConditionalField>

            <div className="pt-4">
              <Button type="submit" variant="primary" fullWidth>
                Gửi
              </Button>
            </div>
          </Form>

          {formData && (
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
              <h4 className="font-medium mb-2">Kết quả:</h4>
              <pre className="text-sm overflow-auto">{JSON.stringify(formData, null, 2)}</pre>
            </div>
          )}
        </Card>

        <Card title="Hướng dẫn sử dụng" className="mb-6">
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-lg mb-2">ConditionalField</h3>
              <p className="mb-2">Component để hiển thị/ẩn field dựa trên điều kiện.</p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                {`<ConditionalField
  condition={{
    field: 'userType',
    type: ConditionType.EQUALS,
    value: 'business'
  }}
>
  <FormItem name="companyName" label="Tên công ty">
    <Input />
  </FormItem>
</ConditionalField>`}
              </pre>
            </div>

            <div>
              <h3 className="font-medium text-lg mb-2">Điều kiện phức tạp (AND)</h3>
              <p className="mb-2">Kết hợp nhiều điều kiện với AND.</p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                {`<ConditionalField
  condition={{
    and: [
      { field: 'hasSpecialRequirements', type: ConditionType.IS_TRUE },
      { field: 'userType', type: ConditionType.EQUALS, value: 'business' }
    ]
  }}
>
  <FormItem name="specialRequirements" label="Yêu cầu đặc biệt">
    <Input as="textarea" />
  </FormItem>
</ConditionalField>`}
              </pre>
            </div>

            <div>
              <h3 className="font-medium text-lg mb-2">Điều kiện phức tạp (OR)</h3>
              <p className="mb-2">Kết hợp nhiều điều kiện với OR.</p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                {`<ConditionalField
  condition={{
    or: [
      { field: 'contactMethod', type: ConditionType.EQUALS, value: 'phone' },
      { field: 'contactMethod', type: ConditionType.EQUALS, value: 'both' }
    ]
  }}
>
  <FormItem name="phoneNumber" label="Số điện thoại">
    <Input />
  </FormItem>
</ConditionalField>`}
              </pre>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default FormConditionalDemo;
