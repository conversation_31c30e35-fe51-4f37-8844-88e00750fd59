import React, { useRef, useState, useEffect } from 'react';
import { z } from 'zod';
import { Button, Card, Form, FormItem, Input, Toggle, FormGrid } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useFormContext } from 'react-hook-form';

// Định nghĩa schema validation với Zod
const userSchema = z.object({
  // Thông tin cơ bản
  firstName: z.string().min(1, 'Họ là bắt buộc'),
  lastName: z.string().min(1, 'Tên là bắt buộc'),
  fullName: z.string().optional(),
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),

  // Thông tin địa chỉ
  country: z.string().min(1, 'Quốc gia là bắt buộc'),
  province: z.string().min(1, 'Tỉnh/Thành phố là bắt buộc'),
  district: z.string().min(1, 'Quận/Huyện là bắt buộc'),

  // Thông tin khác
  age: z.string().optional(),
  isAdult: z.boolean().optional(),

  // Thông tin thanh toán
  paymentMethod: z.enum(['credit_card', 'bank_transfer', 'cash']),
  cardNumber: z.string().optional(),
  cardExpiry: z.string().optional(),
  cardCvv: z.string().optional(),
  bankName: z.string().optional(),
  accountNumber: z.string().optional(),
});

// Định nghĩa kiểu dữ liệu từ schema
type UserFormValues = z.infer<typeof userSchema>;

// Dữ liệu mẫu cho cascading select
const countries = [
  { value: 'vietnam', label: 'Việt Nam' },
  { value: 'usa', label: 'Hoa Kỳ' },
  { value: 'japan', label: 'Nhật Bản' },
];

const provinces = {
  vietnam: [
    { value: 'hanoi', label: 'Hà Nội' },
    { value: 'hochiminh', label: 'TP. Hồ Chí Minh' },
    { value: 'danang', label: 'Đà Nẵng' },
  ],
  usa: [
    { value: 'california', label: 'California' },
    { value: 'newyork', label: 'New York' },
    { value: 'texas', label: 'Texas' },
  ],
  japan: [
    { value: 'tokyo', label: 'Tokyo' },
    { value: 'osaka', label: 'Osaka' },
    { value: 'kyoto', label: 'Kyoto' },
  ],
};

const districts = {
  hanoi: [
    { value: 'caugiay', label: 'Cầu Giấy' },
    { value: 'dongda', label: 'Đống Đa' },
    { value: 'hoankiem', label: 'Hoàn Kiếm' },
  ],
  hochiminh: [
    { value: 'district1', label: 'Quận 1' },
    { value: 'district2', label: 'Quận 2' },
    { value: 'district3', label: 'Quận 3' },
  ],
  danang: [
    { value: 'haichau', label: 'Hải Châu' },
    { value: 'sontra', label: 'Sơn Trà' },
    { value: 'nguhanhson', label: 'Ngũ Hành Sơn' },
  ],
  california: [
    { value: 'losangeles', label: 'Los Angeles' },
    { value: 'sanfrancisco', label: 'San Francisco' },
    { value: 'sandiego', label: 'San Diego' },
  ],
  newyork: [
    { value: 'manhattan', label: 'Manhattan' },
    { value: 'brooklyn', label: 'Brooklyn' },
    { value: 'queens', label: 'Queens' },
  ],
  texas: [
    { value: 'houston', label: 'Houston' },
    { value: 'dallas', label: 'Dallas' },
    { value: 'austin', label: 'Austin' },
  ],
  tokyo: [
    { value: 'shinjuku', label: 'Shinjuku' },
    { value: 'shibuya', label: 'Shibuya' },
    { value: 'roppongi', label: 'Roppongi' },
  ],
  osaka: [
    { value: 'umeda', label: 'Umeda' },
    { value: 'namba', label: 'Namba' },
    { value: 'tennoji', label: 'Tennoji' },
  ],
  kyoto: [
    { value: 'gion', label: 'Gion' },
    { value: 'arashiyama', label: 'Arashiyama' },
    { value: 'fushimi', label: 'Fushimi' },
  ],
};

// Component để xử lý dependencies trong form
const FormDependencyHandler: React.FC = () => {
  const { watch, setValue } = useFormContext();

  // Theo dõi các field
  const firstName = watch('firstName');
  const lastName = watch('lastName');
  const age = watch('age');
  const paymentMethod = watch('paymentMethod');

  // Cập nhật fullName từ firstName và lastName
  useEffect(() => {
    const fullName = `${firstName || ''} ${lastName || ''}`.trim();
    setValue('fullName', fullName);
  }, [firstName, lastName, setValue]);

  // Cập nhật isAdult từ age
  useEffect(() => {
    const ageNum = parseInt(age as string, 10);
    const isAdult = !isNaN(ageNum) && ageNum >= 18;
    setValue('isAdult', isAdult);
  }, [age, setValue]);

  // Reset các field liên quan đến phương thức thanh toán
  useEffect(() => {
    if (paymentMethod === 'credit_card') {
      setValue('bankName', '');
      setValue('accountNumber', '');
    } else if (paymentMethod === 'bank_transfer') {
      setValue('cardNumber', '');
      setValue('cardExpiry', '');
      setValue('cardCvv', '');
    } else {
      setValue('cardNumber', '');
      setValue('cardExpiry', '');
      setValue('cardCvv', '');
      setValue('bankName', '');
      setValue('accountNumber', '');
    }
  }, [paymentMethod, setValue]);

  return null;
};

// Component để hiển thị province options
const ProvinceSelect: React.FC = () => {
  const { watch, setValue } = useFormContext();
  const country = watch('country');
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [options, setOptions] = useState<any[]>([]);

  useEffect(() => {
    // Cập nhật options dựa trên country
    const newOptions = provinces[country as keyof typeof provinces] || [];
    setOptions(newOptions);

    // Reset province và district khi country thay đổi
    setValue('province', '');
    setValue('district', '');
  }, [country, setValue]);

  return (
    <select className="w-full p-2 border rounded">
      <option value="">Chọn tỉnh/thành phố</option>
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

// Component để hiển thị district options
const DistrictSelect: React.FC = () => {
  const { watch, setValue } = useFormContext();
  const province = watch('province');
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [options, setOptions] = useState<any[]>([]);

  useEffect(() => {
    // Cập nhật options dựa trên province
    const newOptions = districts[province as keyof typeof districts] || [];
    setOptions(newOptions);

    // Reset district khi province thay đổi
    setValue('district', '');
  }, [province, setValue]);

  return (
    <select className="w-full p-2 border rounded">
      <option value="">Chọn quận/huyện</option>
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

/**
 * Trang demo cho Form Field Dependencies
 */
const FormDependencyDemo: React.FC = () => {
  const formRef = useRef<FormRef<UserFormValues>>(null);
  const [formData, setFormData] = useState<UserFormValues | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<string>('credit_card');

  // Xử lý submit form
  const handleSubmit = (values: UserFormValues) => {
    console.log('Form submitted:', values);
    setFormData(values);
  };

  return (
    <div className="space-y-8">
      <Card title="Form Field Dependencies Demo" className="mb-6">
        <p className="mb-4">Demo tính năng quản lý dependencies giữa các field trong form.</p>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Form với Field Dependencies" className="mb-6">
          <Form
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ref={formRef as any}
            schema={userSchema}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSubmit={handleSubmit as any}
            className="space-y-6"
            defaultValues={{
              country: '',
              province: '',
              district: '',
              paymentMethod: 'credit_card',
            }}
          >
            {/* Component xử lý dependencies */}
            <FormDependencyHandler />
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
              <h3 className="text-sm font-medium mb-2">1. Transform Dependencies:</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Cập nhật giá trị field dựa trên giá trị của field khác thông qua hàm transform.
              </p>
            </div>

            <FormGrid columns={2} gap="md">
              <FormItem name="firstName" label="Họ" required>
                <Input placeholder="Nhập họ" fullWidth />
              </FormItem>

              <FormItem name="lastName" label="Tên" required>
                <Input placeholder="Nhập tên" fullWidth />
              </FormItem>

              <FormItem name="fullName" label="Họ tên đầy đủ" className="col-span-2">
                <Input placeholder="Tự động điền từ Họ và Tên" fullWidth disabled />
              </FormItem>
            </FormGrid>

            <FormGrid columns={2} gap="md">
              <FormItem name="age" label="Tuổi">
                <Input type="number" placeholder="Nhập tuổi" fullWidth />
              </FormItem>

              <FormItem name="isAdult" label="Đã đủ 18 tuổi" inline>
                <Toggle disabled />
              </FormItem>
            </FormGrid>

            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
              <h3 className="text-sm font-medium mb-2">2. Cascading Select:</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Cập nhật options của select dựa trên giá trị của select khác.
              </p>
            </div>

            <FormGrid columns={3} gap="md">
              <FormItem name="country" label="Quốc gia" required>
                <select className="w-full p-2 border rounded">
                  <option value="">Chọn quốc gia</option>
                  {countries.map(country => (
                    <option key={country.value} value={country.value}>
                      {country.label}
                    </option>
                  ))}
                </select>
              </FormItem>

              <FormItem name="province" label="Tỉnh/Thành phố" required>
                <ProvinceSelect />
              </FormItem>

              <FormItem name="district" label="Quận/Huyện" required>
                <DistrictSelect />
              </FormItem>
            </FormGrid>

            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
              <h3 className="text-sm font-medium mb-2">3. Conditional Fields Reset:</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Reset giá trị các field khi field khác thay đổi.
              </p>
            </div>

            <FormItem name="paymentMethod" label="Phương thức thanh toán" required>
              <select
                className="w-full p-2 border rounded"
                onChange={e => setPaymentMethod(e.target.value)}
                defaultValue="credit_card"
              >
                <option value="credit_card">Thẻ tín dụng</option>
                <option value="bank_transfer">Chuyển khoản ngân hàng</option>
                <option value="cash">Tiền mặt</option>
              </select>
            </FormItem>

            {/* Các field cho thẻ tín dụng */}
            {paymentMethod === 'credit_card' && (
              <FormGrid columns={3} gap="md">
                <FormItem name="cardNumber" label="Số thẻ" required>
                  <Input placeholder="XXXX XXXX XXXX XXXX" fullWidth />
                </FormItem>

                <FormItem name="cardExpiry" label="Ngày hết hạn" required>
                  <Input placeholder="MM/YY" fullWidth />
                </FormItem>

                <FormItem name="cardCvv" label="CVV" required>
                  <Input placeholder="XXX" fullWidth />
                </FormItem>
              </FormGrid>
            )}

            {/* Các field cho chuyển khoản ngân hàng */}
            {paymentMethod === 'bank_transfer' && (
              <FormGrid columns={2} gap="md">
                <FormItem name="bankName" label="Tên ngân hàng" required>
                  <Input placeholder="Nhập tên ngân hàng" fullWidth />
                </FormItem>

                <FormItem name="accountNumber" label="Số tài khoản" required>
                  <Input placeholder="Nhập số tài khoản" fullWidth />
                </FormItem>
              </FormGrid>
            )}

            <div className="pt-4">
              <Button type="submit" variant="primary" fullWidth>
                Gửi
              </Button>
            </div>
          </Form>

          {formData && (
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
              <h4 className="font-medium mb-2">Kết quả:</h4>
              <pre className="text-sm overflow-auto">{JSON.stringify(formData, null, 2)}</pre>
            </div>
          )}
        </Card>

        <Card title="Hướng dẫn sử dụng" className="mb-6">
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-lg mb-2">Field Dependencies</h3>
              <p className="mb-2">Quản lý dependencies giữa các field trong form.</p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                {`// Trong component Form
const { watch, setValue } = useFormContext();

// Theo dõi các field
const firstName = watch('firstName');
const lastName = watch('lastName');

// Cập nhật fullName từ firstName và lastName
useEffect(() => {
  const fullName = \`\${firstName || ''} \${lastName || ''}\`.trim();
  setValue('fullName', fullName);
}, [firstName, lastName, setValue]);`}
              </pre>
            </div>

            <div>
              <h3 className="font-medium text-lg mb-2">Cascading Select</h3>

              <p className="mb-2">Cập nhật options của select dựa trên giá trị của select khác.</p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                {`// Trong component Form
const { watch, setValue } = useFormContext();
const country = watch('country');
const [options, setOptions] = useState([]);

useEffect(() => {
  // Cập nhật options dựa trên country
  const newOptions = getProvincesByCountry(country);
  setOptions(newOptions);

  // Reset province khi country thay đổi
  setValue('province', '');
}, [country, setValue]);`}
              </pre>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default FormDependencyDemo;
