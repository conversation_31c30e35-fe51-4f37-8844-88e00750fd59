import React, { useRef, useState } from 'react';
import { z } from 'zod';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Toggle,
  Icon,
  FormGrid,
  FormInline,
  FormHorizontal,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

// Định nghĩa schema validation với Zod
const userSchema = z.object({
  firstName: z.string().min(1, 'Họ là bắt buộc'),
  lastName: z.string().min(1, 'Tên là bắt buộc'),
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  country: z.string().optional(),
  agreeTerms: z.boolean().optional(),
});

// <PERSON><PERSON>nh nghĩa kiểu dữ liệu từ schema
type UserFormValues = z.infer<typeof userSchema>;

// Định nghĩa schema cho form tìm kiếm
const searchSchema = z.object({
  query: z.string().min(1, 'Từ khóa tìm kiếm là bắt buộc'),
  category: z.string().optional(),
});

// Định nghĩa kiểu dữ liệu từ schema
type SearchFormValues = z.infer<typeof searchSchema>;

/**
 * Trang demo cho Form Layout
 */
const FormLayoutDemo: React.FC = () => {
  const gridFormRef = useRef<FormRef<UserFormValues>>(null);
  const inlineFormRef = useRef<FormRef<SearchFormValues>>(null);
  const horizontalFormRef = useRef<FormRef<UserFormValues>>(null);

  const [gridFormData, setGridFormData] = useState<UserFormValues | null>(null);
  const [inlineFormData, setInlineFormData] = useState<SearchFormValues | null>(null);
  const [horizontalFormData, setHorizontalFormData] = useState<UserFormValues | null>(null);

  // Xử lý submit form grid
  const handleGridSubmit = (values: UserFormValues) => {
    console.log('Grid form submitted:', values);
    setGridFormData(values);
  };

  // Xử lý submit form inline
  const handleInlineSubmit = (values: SearchFormValues) => {
    console.log('Inline form submitted:', values);
    setInlineFormData(values);
  };

  // Xử lý submit form horizontal
  const handleHorizontalSubmit = (values: UserFormValues) => {
    console.log('Horizontal form submitted:', values);
    setHorizontalFormData(values);
  };

  return (
    <div className="space-y-8">
      <Card title="Form Layout Demo" className="mb-6">
        <p className="mb-4">
          Demo các component layout cho form: FormGrid, FormInline, và FormHorizontal.
        </p>
      </Card>

      {/* FormGrid Demo */}
      <Card title="FormGrid - Grid Layout" className="mb-6">
        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            FormGrid tạo layout dạng lưới cho form, hỗ trợ responsive trên các kích thước màn hình
            khác nhau.
          </p>
        </div>

        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
          <h3 className="text-sm font-medium mb-2">Responsive Grid Layout:</h3>
          <ul className="text-xs text-gray-600 dark:text-gray-400 list-disc pl-5 mb-2">
            <li>Mobile (mặc định): 1 cột</li>
            <li>Tablet (sm - 640px+): 1 cột</li>
            <li>Laptop (md - 768px+): 2 cột</li>
            <li>Desktop (lg - 1024px+): 2 cột</li>
          </ul>
        </div>

        <Form
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          ref={gridFormRef as any}
          schema={userSchema}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleGridSubmit as any}
          className="space-y-6"
        >
          <FormGrid
            columns={2} // 2 cột trên desktop (lg)
            columnsMd={2} // 2 cột trên laptop (md)
            columnsSm={1} // 1 cột trên tablet (sm)
            gap="md"
          >
            <FormItem name="firstName" label="Họ" required>
              <Input placeholder="Nhập họ" fullWidth />
            </FormItem>

            <FormItem name="lastName" label="Tên" required>
              <Input placeholder="Nhập tên" fullWidth />
            </FormItem>

            <FormItem
              name="email"
              label="Email"
              required
              className="sm:col-span-1 md:col-span-2 lg:col-span-2"
            >
              <Input
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Icon name="chat" size="sm" />}
                fullWidth
              />
            </FormItem>

            <FormItem name="phone" label="Số điện thoại">
              <Input placeholder="Nhập số điện thoại" fullWidth />
            </FormItem>

            <FormItem name="address" label="Địa chỉ">
              <Input placeholder="Nhập địa chỉ" fullWidth />
            </FormItem>
          </FormGrid>

          <div className="mt-8 mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
            <h3 className="text-sm font-medium mb-2">Responsive Grid Layout (3 cột):</h3>
            <ul className="text-xs text-gray-600 dark:text-gray-400 list-disc pl-5 mb-2">
              <li>Mobile (mặc định): 1 cột</li>
              <li>Tablet (sm - 640px+): 2 cột</li>
              <li>Laptop (md - 768px+): 2 cột</li>
              <li>Desktop (lg - 1024px+): 3 cột</li>
            </ul>
          </div>

          <FormGrid
            columns={3} // 3 cột trên desktop (lg)
            columnsMd={2} // 2 cột trên laptop (md)
            columnsSm={2} // 2 cột trên tablet (sm)
            gap="md"
          >
            <FormItem name="city" label="Thành phố">
              <Input placeholder="Nhập thành phố" fullWidth />
            </FormItem>

            <FormItem name="state" label="Tỉnh/Thành">
              <Input placeholder="Nhập tỉnh/thành" fullWidth />
            </FormItem>

            <FormItem name="zipCode" label="Mã bưu điện">
              <Input placeholder="Nhập mã bưu điện" fullWidth />
            </FormItem>
          </FormGrid>

          <FormItem name="country" label="Quốc gia">
            <Input placeholder="Nhập quốc gia" fullWidth />
          </FormItem>

          <FormItem name="agreeTerms" inline helpText="Tôi đồng ý với điều khoản sử dụng">
            <Toggle />
          </FormItem>

          <Button type="submit" variant="primary">
            Đăng ký
          </Button>
        </Form>

        {gridFormData && (
          <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
            <h4 className="font-medium mb-2">Kết quả:</h4>
            <pre className="text-sm overflow-auto">{JSON.stringify(gridFormData, null, 2)}</pre>
          </div>
        )}
      </Card>

      {/* FormInline Demo */}
      <Card title="FormInline - Inline Layout" className="mb-6">
        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            FormInline tạo layout dạng inline cho form, thích hợp cho các form tìm kiếm hoặc form
            đơn giản.
          </p>
        </div>

        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
          <h3 className="text-sm font-medium mb-2">Responsive Inline Layout:</h3>
          <ul className="text-xs text-gray-600 dark:text-gray-400 list-disc pl-5 mb-2">
            <li>Mobile: Các phần tử xếp dọc (wrap)</li>
            <li>Tablet (sm - 640px+): Các phần tử xếp dọc (wrap)</li>
            <li>Laptop (md - 768px+): Các phần tử xếp ngang</li>
          </ul>
        </div>

        <Form
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          ref={inlineFormRef as any}
          schema={searchSchema}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleInlineSubmit as any}
        >
          {/* Sử dụng className để thêm responsive */}
          <FormInline
            gap="md"
            align="end"
            className="flex-col md:flex-row items-stretch md:items-end"
          >
            <FormItem name="query" label="Từ khóa" required className="w-full md:w-auto">
              <Input placeholder="Nhập từ khóa tìm kiếm" fullWidth />
            </FormItem>

            <FormItem name="category" label="Danh mục" className="w-full md:w-auto">
              <select className="h-10 px-3 py-2 border rounded w-full">
                <option value="">Tất cả danh mục</option>
                <option value="products">Sản phẩm</option>
                <option value="services">Dịch vụ</option>
                <option value="blogs">Bài viết</option>
              </select>
            </FormItem>

            <Button type="submit" variant="primary" className="mt-4 md:mt-0">
              Tìm kiếm
            </Button>
          </FormInline>
        </Form>

        {inlineFormData && (
          <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
            <h4 className="font-medium mb-2">Kết quả:</h4>
            <pre className="text-sm overflow-auto">{JSON.stringify(inlineFormData, null, 2)}</pre>
          </div>
        )}
      </Card>

      {/* FormHorizontal Demo */}
      <Card title="FormHorizontal - Horizontal Layout" className="mb-6">
        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            FormHorizontal tạo layout với label bên trái và field bên phải, thích hợp cho các form
            chi tiết.
          </p>
        </div>

        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
          <h3 className="text-sm font-medium mb-2">Responsive Horizontal Layout:</h3>
          <ul className="text-xs text-gray-600 dark:text-gray-400 list-disc pl-5 mb-2">
            <li>Mobile: Label phía trên, field phía dưới</li>
            <li>Tablet (sm - 640px+): Label phía trên, field phía dưới</li>
            <li>Laptop (md - 768px+): Label bên trái, field bên phải</li>
          </ul>
        </div>

        <Form
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          ref={horizontalFormRef as any}
          schema={userSchema}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleHorizontalSubmit as any}
        >
          {/* Sử dụng className để thêm responsive */}
          <div className="md:block hidden">
            {/* Hiển thị trên màn hình md trở lên */}
            <FormHorizontal labelWidth="md" gap="md">
              <FormItem name="firstName" label="Họ" required>
                <Input placeholder="Nhập họ" fullWidth />
              </FormItem>

              <FormItem name="lastName" label="Tên" required>
                <Input placeholder="Nhập tên" fullWidth />
              </FormItem>

              <FormItem name="email" label="Email" required>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  leftIcon={<Icon name="chat" size="sm" />}
                  fullWidth
                />
              </FormItem>

              <FormItem name="phone" label="Số điện thoại">
                <Input placeholder="Nhập số điện thoại" fullWidth />
              </FormItem>

              <FormItem name="address" label="Địa chỉ">
                <Input placeholder="Nhập địa chỉ" fullWidth />
              </FormItem>

              <FormItem name="agreeTerms" label="Điều khoản">
                <Toggle />
              </FormItem>

              <div className="flex justify-end mt-4 ml-40">
                <Button type="submit" variant="primary">
                  Đăng ký
                </Button>
              </div>
            </FormHorizontal>
          </div>

          <div className="md:hidden block">
            {/* Hiển thị trên màn hình nhỏ hơn md */}
            <div className="space-y-4">
              <FormItem name="firstName" label="Họ" required>
                <Input placeholder="Nhập họ" fullWidth />
              </FormItem>

              <FormItem name="lastName" label="Tên" required>
                <Input placeholder="Nhập tên" fullWidth />
              </FormItem>

              <FormItem name="email" label="Email" required>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  leftIcon={<Icon name="chat" size="sm" />}
                  fullWidth
                />
              </FormItem>

              <FormItem name="phone" label="Số điện thoại">
                <Input placeholder="Nhập số điện thoại" fullWidth />
              </FormItem>

              <FormItem name="address" label="Địa chỉ">
                <Input placeholder="Nhập địa chỉ" fullWidth />
              </FormItem>

              <FormItem name="agreeTerms" label="Điều khoản" inline>
                <Toggle />
              </FormItem>

              <div className="flex justify-center mt-4">
                <Button type="submit" variant="primary">
                  Đăng ký
                </Button>
              </div>
            </div>
          </div>
        </Form>

        {horizontalFormData && (
          <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
            <h4 className="font-medium mb-2">Kết quả:</h4>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(horizontalFormData, null, 2)}
            </pre>
          </div>
        )}
      </Card>
    </div>
  );
};

export default FormLayoutDemo;
