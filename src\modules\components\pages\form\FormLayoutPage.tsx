import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import {
  Form,
  FormItem,
  Input,
  Button,
  FormGrid,
  FormInline,
  FormHorizontal,
  Icon,
  Card,
} from '@/shared/components/common';
import { z } from 'zod';
import { FieldValues, SubmitHandler } from 'react-hook-form';

/**
 * Trang hiển thị các layout form
 */
const FormLayoutPage: React.FC = () => {
  const { t } = useTranslation();
  // Grid form schema
  const gridFormSchema = z.object({
    firstName: z.string().min(1, t('errors.required', 'Trường này là bắt buộc')),
    lastName: z.string().min(1, t('errors.required', 'Trường này là bắt buộc')),
    email: z
      .string()
      .min(1, t('errors.required', 'Trường này là bắt buộc'))
      .email(t('errors.invalidEmail', '<PERSON><PERSON> kh<PERSON> hợ<PERSON> lệ')),
    phone: z.string().optional(),
    address: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
  });

  // Infer type from schema
  type GridFormData = z.infer<typeof gridFormSchema>;

  // Inline form schema
  const inlineFormSchema = z.object({
    search: z.string().min(1, t('errors.required', 'Trường này là bắt buộc')),
    category: z.string().optional(),
  });

  // Infer type from schema
  type InlineFormData = z.infer<typeof inlineFormSchema>;

  // Horizontal form schema
  const horizontalFormSchema = z.object({
    username: z.string().min(1, t('errors.required', 'Trường này là bắt buộc')),
    email: z
      .string()
      .min(1, t('errors.required', 'Trường này là bắt buộc'))
      .email(t('errors.invalidEmail', 'Email không hợp lệ')),
    password: z.string().min(1, t('errors.required', 'Trường này là bắt buộc')),
  });

  // Infer type from schema
  type HorizontalFormData = z.infer<typeof horizontalFormSchema>;

  // State to store form data
  const [formData, setFormData] = useState<
    GridFormData | InlineFormData | HorizontalFormData | null
  >(null);

  // Handle form submission
  const handleSubmit: SubmitHandler<FieldValues> = data => {
    console.log('Form submitted:', data);
    setFormData(data as GridFormData | InlineFormData | HorizontalFormData);
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.form.layout.title', 'Form Layouts')}
        </h1>
        <p className="text-muted">
          {t(
            'components.form.layout.description',
            'Các tùy chọn layout khác nhau để tổ chức các trường form.'
          )}
        </p>
      </div>

      {/* Grid Layout */}
      <ComponentDemo
        title={t('components.form.layout.grid.title', 'Grid Layout')}
        description={t(
          'components.form.layout.grid.description',
          'Tổ chức các trường form theo layout dạng lưới responsive.'
        )}
        code={`import { Form, FormItem, Input, Button, FormGrid } from '@/shared/components/common';
import { z } from 'zod';

// Định nghĩa schema
const schema = z.object({
  firstName: z.string().min(1, 'Tên là bắt buộc'),
  lastName: z.string().min(1, 'Họ là bắt buộc'),
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
  // ... các trường khác
});

// Hiển thị form
<Form schema={schema} onSubmit={handleSubmit} className="space-y-4">
  <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
    <FormItem name="firstName" label={t('components.form.layout.fields.firstName', 'Tên')} required>
      <Input placeholder={t('components.form.layout.placeholders.firstName', 'Nhập tên')} fullWidth />
    </FormItem>

    <FormItem name="lastName" label={t('components.form.layout.fields.lastName', 'Họ')} required>
      <Input placeholder={t('components.form.layout.placeholders.lastName', 'Nhập họ')} fullWidth />
    </FormItem>

    <FormItem name="email" label={t('components.form.layout.fields.email', 'Email')} required className="sm:col-span-1 md:col-span-2">
      <Input
        type="email"
        placeholder={t('components.form.layout.placeholders.email', 'Nhập email')}
        leftIcon={<Icon name="mail" size="sm" />}
        fullWidth
      />
    </FormItem>
  </FormGrid>

  <Button type="submit">{t('components.form.layout.buttons.submit', 'Gửi')}</Button>
</Form>`}
      >
        <div className="w-full max-w-2xl mx-auto">
          <Form schema={gridFormSchema} onSubmit={handleSubmit} className="space-y-4">
            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              <FormItem
                name="firstName"
                label={t('components.form.layout.fields.firstName', 'Tên')}
                required
              >
                <Input
                  placeholder={t('components.form.layout.placeholders.firstName', 'Nhập tên')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="lastName"
                label={t('components.form.layout.fields.lastName', 'Họ')}
                required
              >
                <Input
                  placeholder={t('components.form.layout.placeholders.lastName', 'Nhập họ')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="email"
                label={t('components.form.layout.fields.email', 'Email')}
                required
                className="sm:col-span-1 md:col-span-2"
              >
                <Input
                  type="email"
                  placeholder={t('components.form.layout.placeholders.email', 'Nhập email')}
                  leftIcon={<Icon name="mail" size="sm" />}
                  fullWidth
                />
              </FormItem>

              <FormItem name="phone" label={t('components.form.layout.fields.phone', 'Điện thoại')}>
                <Input
                  placeholder={t('components.form.layout.placeholders.phone', 'Nhập số điện thoại')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="address"
                label={t('components.form.layout.fields.address', 'Địa chỉ')}
              >
                <Input
                  placeholder={t('components.form.layout.placeholders.address', 'Nhập địa chỉ')}
                  fullWidth
                />
              </FormItem>
            </FormGrid>

            <FormGrid columns={3} columnsMd={3} columnsSm={1} gap="md">
              <FormItem name="city" label={t('components.form.layout.fields.city', 'Thành phố')}>
                <Input
                  placeholder={t('components.form.layout.placeholders.city', 'Nhập thành phố')}
                  fullWidth
                />
              </FormItem>

              <FormItem name="state" label={t('components.form.layout.fields.state', 'Tỉnh/Thành')}>
                <Input
                  placeholder={t('components.form.layout.placeholders.state', 'Nhập tỉnh/thành')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="zipCode"
                label={t('components.form.layout.fields.zipCode', 'Mã bưu điện')}
              >
                <Input
                  placeholder={t('components.form.layout.placeholders.zipCode', 'Nhập mã bưu điện')}
                  fullWidth
                />
              </FormItem>
            </FormGrid>

            <Button type="submit">{t('components.form.layout.buttons.submit', 'Gửi')}</Button>
          </Form>

          {formData && (
            <div className="mt-4 p-3 bg-muted/20 rounded">
              <h4 className="font-medium mb-2 text-foreground">
                {t('components.form.layout.result', 'Result')}:
              </h4>
              <pre className="text-sm overflow-auto">{JSON.stringify(formData, null, 2)}</pre>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Inline Layout */}
      <ComponentDemo
        title={t('components.form.layout.inline.title', 'Inline Layout')}
        description={t(
          'components.form.layout.inline.description',
          'Tạo form inline cho thanh tìm kiếm và bộ lọc.'
        )}
        code={`import { Form, FormItem, Input, Button, FormInline } from '@/shared/components/common';
import { z } from 'zod';

// Định nghĩa schema
const schema = z.object({
  search: z.string().min(1, 'Từ khóa tìm kiếm là bắt buộc'),
  category: z.string().optional(),
});

// Hiển thị form
<Form schema={schema} onSubmit={handleSubmit}>
  <FormInline gap="md" align="end" className="flex-col md:flex-row">
    <FormItem name="search" label={t('components.form.layout.fields.search', 'Tìm kiếm')} required>
      <Input
        placeholder={t('components.form.layout.placeholders.search', 'Tìm kiếm...')}
        leftIcon={<Icon name="search" size="sm" />}
        fullWidth
      />
    </FormItem>

    <FormItem name="category" label={t('components.form.layout.fields.category', 'Danh mục')}>
      <select className="h-10 px-3 py-2 border rounded w-full bg-background text-foreground border-input">
        <option value="">{t('components.form.layout.options.allCategories', 'Tất cả danh mục')}</option>
        <option value="products">{t('components.form.layout.options.products', 'Sản phẩm')}</option>
        <option value="services">{t('components.form.layout.options.services', 'Dịch vụ')}</option>
        <option value="blogs">{t('components.form.layout.options.blogs', 'Bài viết')}</option>
      </select>
    </FormItem>

    <Button type="submit">{t('components.form.layout.buttons.search', 'Tìm kiếm')}</Button>
  </FormInline>
</Form>`}
      >
        <div className="w-full max-w-2xl mx-auto">
          <Form schema={inlineFormSchema} onSubmit={handleSubmit}>
            <FormInline gap="md" align="end" className="flex-col md:flex-row">
              <FormItem
                name="search"
                label={t('components.form.layout.fields.search', 'Tìm kiếm')}
                required
              >
                <Input
                  placeholder={t('components.form.layout.placeholders.search', 'Tìm kiếm...')}
                  leftIcon={<Icon name="search" size="sm" />}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="category"
                label={t('components.form.layout.fields.category', 'Danh mục')}
              >
                <select className="h-10 px-3 py-2 border rounded w-full bg-background text-foreground border-input">
                  <option value="">
                    {t('components.form.layout.options.allCategories', 'Tất cả danh mục')}
                  </option>
                  <option value="products">
                    {t('components.form.layout.options.products', 'Sản phẩm')}
                  </option>
                  <option value="services">
                    {t('components.form.layout.options.services', 'Dịch vụ')}
                  </option>
                  <option value="blogs">
                    {t('components.form.layout.options.blogs', 'Bài viết')}
                  </option>
                </select>
              </FormItem>

              <Button type="submit">
                {t('components.form.layout.buttons.search', 'Tìm kiếm')}
              </Button>
            </FormInline>
          </Form>
        </div>
      </ComponentDemo>

      {/* Horizontal Layout */}
      <ComponentDemo
        title={t('components.form.layout.horizontal.title', 'Horizontal Layout')}
        description={t(
          'components.form.layout.horizontal.description',
          'Tạo form với label bên trái và trường bên phải.'
        )}
        code={`import { Form, FormItem, Input, Button, FormHorizontal } from '@/shared/components/common';
import { z } from 'zod';

// Định nghĩa schema
const schema = z.object({
  username: z.string().min(1, 'Tên đăng nhập là bắt buộc'),
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
  password: z.string().min(1, 'Mật khẩu là bắt buộc'),
});

// Hiển thị form
<Form schema={schema} onSubmit={handleSubmit} className="space-y-4">
  <FormHorizontal labelWidth="md" gap="md">
    <FormItem name="username" label={t('components.form.layout.fields.username', 'Tên đăng nhập')} required>
      <Input placeholder={t('components.form.layout.placeholders.username', 'Nhập tên đăng nhập')} fullWidth />
    </FormItem>

    <FormItem name="email" label={t('components.form.layout.fields.email', 'Email')} required>
      <Input
        type="email"
        placeholder={t('components.form.layout.placeholders.email', 'Nhập email')}
        leftIcon={<Icon name="mail" size="sm" />}
        fullWidth
      />
    </FormItem>

    <FormItem name="password" label={t('components.form.layout.fields.password', 'Mật khẩu')} required>
      <Input type="password" placeholder={t('components.form.layout.placeholders.password', 'Nhập mật khẩu')} fullWidth />
    </FormItem>
  </FormHorizontal>

  <div className="flex justify-end">
    <Button type="submit">{t('components.form.layout.buttons.login', 'Đăng nhập')}</Button>
  </div>
</Form>`}
      >
        <div className="w-full max-w-2xl mx-auto">
          <Form schema={horizontalFormSchema} onSubmit={handleSubmit} className="space-y-4">
            <FormHorizontal labelWidth="md" gap="md">
              <FormItem
                name="username"
                label={t('components.form.layout.fields.username', 'Tên đăng nhập')}
                required
              >
                <Input
                  placeholder={t(
                    'components.form.layout.placeholders.username',
                    'Nhập tên đăng nhập'
                  )}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="email"
                label={t('components.form.layout.fields.email', 'Email')}
                required
              >
                <Input
                  type="email"
                  placeholder={t('components.form.layout.placeholders.email', 'Nhập email')}
                  leftIcon={<Icon name="mail" size="sm" />}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="password"
                label={t('components.form.layout.fields.password', 'Mật khẩu')}
                required
              >
                <Input
                  type="password"
                  placeholder={t('components.form.layout.placeholders.password', 'Nhập mật khẩu')}
                  fullWidth
                />
              </FormItem>
            </FormHorizontal>

            <div className="flex justify-end">
              <Button type="submit">
                {t('components.form.layout.buttons.login', 'Đăng nhập')}
              </Button>
            </div>
          </Form>
        </div>
      </ComponentDemo>

      {/* Layout Components Reference */}
      <div className="mt-12">
        <Card
          title={t(
            'components.form.layout.reference.title',
            'Tài liệu tham khảo Form Layout Components'
          )}
          className="mb-6"
        >
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2 text-foreground">
                {t('components.form.layout.reference.formGrid.title', 'FormGrid Component')}
              </h3>
              <p className="mb-2 text-muted">
                {t(
                  'components.form.layout.reference.formGrid.description',
                  'Tạo layout dạng lưới responsive cho các trường form.'
                )}
              </p>
              <pre className="bg-muted/20 p-3 rounded text-sm overflow-auto">
                {`<FormGrid
  columns={2}       // Số cột trên màn hình lớn (lg+)
  columnsMd={2}     // Số cột trên màn hình trung bình (md)
  columnsSm={1}     // Số cột trên màn hình nhỏ (sm)
  gap="md"          // Khoảng cách giữa các cột
  rowGap="md"       // Khoảng cách giữa các hàng
>
  {/* Các component FormItem */}
</FormGrid>`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-foreground">
                {t('components.form.layout.reference.formInline.title', 'FormInline Component')}
              </h3>
              <p className="mb-2 text-muted">
                {t(
                  'components.form.layout.reference.formInline.description',
                  'Tạo layout inline cho các trường form.'
                )}
              </p>
              <pre className="bg-muted/20 p-3 rounded text-sm overflow-auto">
                {`<FormInline
  gap="md"          // Khoảng cách giữa các phần tử
  align="center"    // Căn chỉnh dọc (start, center, end, baseline, stretch)
  justify="start"   // Căn chỉnh ngang (start, center, end, between, around, evenly)
  wrap="wrap"       // Kiểu wrap (wrap, nowrap, wrap-reverse)
>
  {/* Các component FormItem */}
</FormInline>`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-foreground">
                {t(
                  'components.form.layout.reference.formHorizontal.title',
                  'FormHorizontal Component'
                )}
              </h3>
              <p className="mb-2 text-muted">
                {t(
                  'components.form.layout.reference.formHorizontal.description',
                  'Tạo layout ngang với label bên trái và trường bên phải.'
                )}
              </p>
              <pre className="bg-muted/20 p-3 rounded text-sm overflow-auto">
                {`<FormHorizontal
  labelWidth="md"    // Chiều rộng của label (xs, sm, md, lg, xl, auto, full)
  fieldWidth="full"  // Chiều rộng của trường (xs, sm, md, lg, xl, auto, full)
  gap="md"           // Khoảng cách giữa label và trường
  rowGap="md"        // Khoảng cách giữa các hàng
>
  {/* Các component FormItem */}
</FormHorizontal>`}
              </pre>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default FormLayoutPage;
