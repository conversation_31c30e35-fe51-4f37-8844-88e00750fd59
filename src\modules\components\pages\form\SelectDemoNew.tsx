import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import { Select, Icon, Card, Typography } from '@/shared/components/common';
import type { SelectOption, SelectGroup } from '@/shared/components/common/Select/Select';

/**
 * Trang demo cho component Select
 */
const SelectDemoNew: React.FC = () => {
  const { t } = useTranslation();
  const [singleValue, setSingleValue] = useState<string>('');
  const [multiValue, setMultiValue] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');
  const [loadingValue, setLoadingValue] = useState<string>('');
  const [bankAccountValue, setBankAccountValue] = useState<string>('vcb');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Options cho single select
  const singleOptions: SelectOption[] = [
    { value: 'apple', label: 'Apple' },
    { value: 'banana', label: 'Banana' },
    { value: 'orange', label: 'Orange' },
    { value: 'grape', label: 'Grape' },
    { value: 'watermelon', label: 'Watermelon' },
  ];

  // Options cho multi select
  const multiOptions: SelectOption[] = [
    { value: 'react', label: 'React', icon: <Icon name="code" size="sm" /> },
    { value: 'vue', label: 'Vue', icon: <Icon name="code" size="sm" /> },
    { value: 'angular', label: 'Angular', icon: <Icon name="code" size="sm" /> },
    { value: 'svelte', label: 'Svelte', icon: <Icon name="code" size="sm" /> },
    { value: 'nextjs', label: 'Next.js', icon: <Icon name="code" size="sm" /> },
  ];

  // Options cho searchable select
  const searchOptions: SelectOption[] = [
    { value: 'afghanistan', label: 'Afghanistan' },
    { value: 'albania', label: 'Albania' },
    { value: 'algeria', label: 'Algeria' },
    { value: 'andorra', label: 'Andorra' },
    { value: 'angola', label: 'Angola' },
    { value: 'argentina', label: 'Argentina' },
    { value: 'armenia', label: 'Armenia' },
    { value: 'australia', label: 'Australia' },
    { value: 'austria', label: 'Austria' },
    { value: 'azerbaijan', label: 'Azerbaijan' },
    // Thêm nhiều quốc gia khác...
    { value: 'vietnam', label: 'Vietnam' },
  ];

  // Options cho grouped select
  const groupedOptions: (SelectOption | SelectGroup)[] = [
    {
      label: 'Fruits',
      options: [
        { value: 'apple', label: 'Apple' },
        { value: 'banana', label: 'Banana' },
        { value: 'orange', label: 'Orange' },
      ],
    },
    {
      label: 'Vegetables',
      options: [
        { value: 'carrot', label: 'Carrot' },
        { value: 'broccoli', label: 'Broccoli' },
        { value: 'cucumber', label: 'Cucumber' },
      ],
    },
    {
      label: 'Berries',
      options: [
        { value: 'strawberry', label: 'Strawberry' },
        { value: 'blueberry', label: 'Blueberry' },
        { value: 'raspberry', label: 'Raspberry' },
      ],
    },
  ];

  // Simulate loading
  const handleLoadingClick = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };

  // Custom rendering cho options
  const renderCustomOption = (option: SelectOption) => {
    return (
      <div className="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-lighter cursor-pointer">
        <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-2">
          {option.label.charAt(0)}
        </div>
        <div>
          <div className="font-medium">{option.label}</div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Custom option</div>
        </div>
      </div>
    );
  };

  // Options cho custom rendering
  const customOptions: SelectOption[] = [
    { value: 'user1', label: 'John Doe' },
    { value: 'user2', label: 'Jane Smith' },
    { value: 'user3', label: 'Robert Johnson' },
    { value: 'user4', label: 'Emily Davis' },
  ];

  // Bank account options
  const bankAccountOptions: SelectOption[] = [
    {
      value: 'vcb',
      label: 'Vietcombank',
      data: {
        accountNumber: '**********',
        accountName: 'NGUYEN VAN A',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/vi/7/7c/Vietcombank_logo.png',
        color: '#004d1a',
      },
    },
    {
      value: 'tcb',
      label: 'Techcombank',
      data: {
        accountNumber: '**********',
        accountName: 'NGUYEN VAN A',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/vi/7/7c/Logo_Techcombank_new.png',
        color: '#f20000',
      },
    },
    {
      value: 'bidv',
      label: 'BIDV',
      data: {
        accountNumber: '**********',
        accountName: 'NGUYEN VAN A',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/3e/Logo_BIDV.svg',
        color: '#1a4f98',
      },
    },
    {
      value: 'mb',
      label: 'MB Bank',
      data: {
        accountNumber: '**********',
        accountName: 'NGUYEN VAN A',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/2/25/Logo_MB_new.png',
        color: '#1e40af',
      },
    },
  ];

  // Custom rendering cho bank accounts
  const renderBankAccountOption = (option: SelectOption) => {
    const bankData =
      (option.data as {
        logoUrl?: string;
        color?: string;
        accountNumber?: string;
        accountName?: string;
      }) || {};

    return (
      <div className="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-dark-lighter cursor-pointer">
        <div className="w-10 h-10 flex-shrink-0 rounded-md overflow-hidden bg-white flex items-center justify-center mr-3 border border-gray-200">
          {bankData.logoUrl ? (
            <img src={bankData.logoUrl} alt={option.label} className="w-8 h-8 object-contain" />
          ) : (
            <div
              className="w-full h-full flex items-center justify-center text-white font-bold"
              style={{ backgroundColor: bankData.color || '#4f46e5' }}
            >
              {option.label.charAt(0)}
            </div>
          )}
        </div>
        <div className="flex-grow">
          <div className="font-medium">{option.label}</div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {bankData.accountNumber || 'N/A'} • {bankData.accountName || 'N/A'}
          </div>
        </div>
        {option.value === 'vcb' && (
          <div className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
            Mặc định
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="mb-8">
        <Typography variant="h1" className="mb-2">
          {t('components.inputs.select.title')}
        </Typography>
        <Typography className="text-gray-600 dark:text-gray-400">
          {t('components.inputs.select.description')}
        </Typography>
      </div>

      {/* Overview Card */}
      <Card title="Select Component Overview" className="mb-6">
        <p className="mb-4">
          Select component là một dropdown nâng cao với nhiều tính năng hữu ích:
        </p>
        <ul className="list-disc list-inside space-y-1 pl-4 mb-4">
          <li>Single select và multi-select</li>
          <li>Tìm kiếm trong danh sách options</li>
          <li>Custom rendering của options</li>
          <li>Grouping options</li>
          <li>Loading state</li>
          <li>Responsive và hỗ trợ theme</li>
        </ul>
        <p className="mb-2">
          <strong>Các ví dụ dưới đây minh họa các tính năng của component Select:</strong>
        </p>
      </Card>

      {/* Single Select */}
      <ComponentDemo
        title={t('components.inputs.singleSelect.title')}
        description={t('components.inputs.singleSelect.description')}
        code={`import { Select } from '@/shared/components/common';

// Options
const options = [
  { value: 'apple', label: 'Apple' },
  { value: 'banana', label: 'Banana' },
  { value: 'orange', label: 'Orange' },
  { value: 'grape', label: 'Grape' },
  { value: 'watermelon', label: 'Watermelon' },
];

// State
const [value, setValue] = useState('');

// Render
<Select
  label="Select a fruit"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={options}
  placeholder="Choose a fruit"
/>`}
      >
        <div className="w-full max-w-3xl mx-auto">
          <Select
            label="Select a fruit"
            value={singleValue}
            onChange={(val: string | string[] | number | number[]) => setSingleValue(val as string)}
            options={singleOptions}
            placeholder="Choose a fruit"
          />
          {singleValue && (
            <div className="mt-2 text-sm">
              Selected value: <span className="font-medium">{singleValue}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Multi Select */}
      <ComponentDemo
        title={t('components.inputs.multiSelect.title')}
        description={t('components.inputs.multiSelect.description')}
        code={`import { Select, Icon } from '@/shared/components/common';

// Options with icons
const options = [
  { value: 'react', label: 'React', icon: <Icon name="code" size="sm" /> },
  { value: 'vue', label: 'Vue', icon: <Icon name="code" size="sm" /> },
  { value: 'angular', label: 'Angular', icon: <Icon name="code" size="sm" /> },
  { value: 'svelte', label: 'Svelte', icon: <Icon name="code" size="sm" /> },
  { value: 'nextjs', label: 'Next.js', icon: <Icon name="code" size="sm" /> },
];

// State
const [value, setValue] = useState<string[]>([]);

// Render
<Select
  label="Select frameworks"
  value={value}
  onChange={(val) => setValue(val as string[])}
  options={options}
  multiple
  placeholder="Choose frameworks"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select frameworks"
            value={multiValue}
            onChange={(val: string | string[] | number | number[]) =>
              setMultiValue(val as string[])
            }
            options={multiOptions}
            multiple
            placeholder="Choose frameworks"
          />
          {multiValue.length > 0 && (
            <div className="mt-2 text-sm">
              Selected values: <span className="font-medium">{multiValue.join(', ')}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Searchable Select */}
      <ComponentDemo
        title={t('components.inputs.searchableSelect.title')}
        description={t('components.inputs.searchableSelect.description')}
        code={`import { Select } from '@/shared/components/common';

// Many options
const countryOptions = [
  { value: 'afghanistan', label: 'Afghanistan' },
  { value: 'albania', label: 'Albania' },
  // ... many more countries
  { value: 'vietnam', label: 'Vietnam' },
];

// State
const [value, setValue] = useState('');

// Render
<Select
  label="Select a country"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={countryOptions}
  searchable
  placeholder="Search countries..."
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select a country"
            value={searchValue}
            onChange={(val: string | string[] | number | number[]) => setSearchValue(val as string)}
            options={searchOptions}
            searchable
            placeholder="Search countries..."
          />
          {searchValue && (
            <div className="mt-2 text-sm">
              Selected country: <span className="font-medium">{searchValue}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Grouped Select */}
      <ComponentDemo
        title={t('components.inputs.groupedSelect.title')}
        description={t('components.inputs.groupedSelect.description')}
        code={`import { Select } from '@/shared/components/common';

// Grouped options
const groupedOptions = [
  {
    label: 'Fruits',
    options: [
      { value: 'apple', label: 'Apple' },
      { value: 'banana', label: 'Banana' },
      { value: 'orange', label: 'Orange' },
    ],
  },
  {
    label: 'Vegetables',
    options: [
      { value: 'carrot', label: 'Carrot' },
      { value: 'broccoli', label: 'Broccoli' },
      { value: 'cucumber', label: 'Cucumber' },
    ],
  },
  // ... more groups
];

// State
const [value, setValue] = useState('');

// Render
<Select
  label="Select food"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={groupedOptions}
  placeholder="Choose food"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select food"
            value={singleValue}
            onChange={(val: string | string[] | number | number[]) => setSingleValue(val as string)}
            options={groupedOptions}
            placeholder="Choose food"
          />
        </div>
      </ComponentDemo>

      {/* Loading Select */}
      <ComponentDemo
        title={t('components.inputs.loadingSelect.title')}
        description={t('components.inputs.loadingSelect.description')}
        code={`import { Select, Button } from '@/shared/components/common';
import { useState } from 'react';

// State
const [value, setValue] = useState('');
const [isLoading, setIsLoading] = useState(false);

// Simulate loading
const handleLoadingClick = () => {
  setIsLoading(true);
  setTimeout(() => {
    setIsLoading(false);
  }, 2000);
};

// Render
<>
  <Select
    label="Loading state demo"
    value={value}
    onChange={(val) => setValue(val as string)}
    options={options}
    loading={isLoading}
    placeholder="Select an option"
  />
  <Button onClick={handleLoadingClick} className="mt-2">
    Simulate Loading
  </Button>
</>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Loading state demo"
            value={loadingValue}
            onChange={(val: string | string[] | number | number[]) =>
              setLoadingValue(val as string)
            }
            options={singleOptions}
            loading={isLoading}
            placeholder="Select an option"
          />
          <button
            onClick={handleLoadingClick}
            className="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Simulate Loading
          </button>
        </div>
      </ComponentDemo>

      {/* Custom Rendering */}
      <ComponentDemo
        title={t('components.inputs.customRenderingSelect.title')}
        description={t('components.inputs.customRenderingSelect.description')}
        code={`import { Select } from '@/shared/components/common';

// Custom render function
const renderCustomOption = (option) => (
  <div className="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-lighter cursor-pointer">
    <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-2">
      {option.label.charAt(0)}
    </div>
    <div>
      <div className="font-medium">{option.label}</div>
      <div className="text-xs text-gray-500 dark:text-gray-400">Custom option</div>
    </div>
  </div>
);

// Options
const customOptions = [
  { value: 'user1', label: 'John Doe' },
  { value: 'user2', label: 'Jane Smith' },
  { value: 'user3', label: 'Robert Johnson' },
  { value: 'user4', label: 'Emily Davis' },
];

// State
const [value, setValue] = useState('');

// Render
<Select
  label="Select user"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={customOptions}
  renderOption={renderCustomOption}
  placeholder="Choose a user"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select user"
            value={singleValue}
            onChange={(val: string | string[] | number | number[]) => setSingleValue(val as string)}
            options={customOptions}
            renderOption={renderCustomOption}
            placeholder="Choose a user"
          />
        </div>
      </ComponentDemo>

      {/* Bank Account Select */}
      <ComponentDemo
        title="Bank Account Select"
        description="Hiển thị tài khoản ngân hàng với logo, tên ngân hàng và thông tin tài khoản"
        code={`import { Select } from '@/shared/components/common';

// Bank account options with custom data
const bankAccountOptions = [
  {
    value: 'vcb',
    label: 'Vietcombank',
    data: {
      accountNumber: '**********',
      accountName: 'NGUYEN VAN A',
      logoUrl: 'https://upload.wikimedia.org/wikipedia/vi/7/7c/Vietcombank_logo.png',
      color: '#004d1a'
    }
  },
  {
    value: 'tcb',
    label: 'Techcombank',
    data: {
      accountNumber: '**********',
      accountName: 'NGUYEN VAN A',
      logoUrl: 'https://upload.wikimedia.org/wikipedia/vi/7/7c/Logo_Techcombank_new.png',
      color: '#f20000'
    }
  },
  // More bank accounts...
];

// Custom render function for bank accounts
const renderBankAccountOption = (option) => {
  const bankData = option.data;
  return (
    <div className="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-dark-lighter cursor-pointer">
      <div className="w-10 h-10 flex-shrink-0 rounded-md overflow-hidden bg-white flex items-center justify-center mr-3 border border-gray-200">
        {bankData.logoUrl ? (
          <img src={bankData.logoUrl} alt={option.label} className="w-8 h-8 object-contain" />
        ) : (
          <div
            className="w-full h-full flex items-center justify-center text-white font-bold"
            style={{ backgroundColor: bankData.color || '#4f46e5' }}
          >
            {option.label.charAt(0)}
          </div>
        )}
      </div>
      <div className="flex-grow">
        <div className="font-medium">{option.label}</div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {bankData.accountNumber} • {bankData.accountName}
        </div>
      </div>
      {option.value === 'vcb' && (
        <div className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
          Mặc định
        </div>
      )}
    </div>
  );
};

// State
const [value, setValue] = useState('vcb');

// Render
<Select
  label="Chọn tài khoản ngân hàng"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={bankAccountOptions}
  renderOption={renderBankAccountOption}
  placeholder="Chọn tài khoản"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Chọn tài khoản ngân hàng"
            value={bankAccountValue}
            onChange={(val: string | string[] | number | number[]) =>
              setBankAccountValue(val as string)
            }
            options={bankAccountOptions}
            renderOption={renderBankAccountOption}
            placeholder="Chọn tài khoản"
          />
          {bankAccountValue && (
            <div className="mt-2 text-sm">
              Selected bank:{' '}
              <span className="font-medium">
                {bankAccountOptions.find(opt => opt.value === bankAccountValue)?.label}
              </span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Hướng dẫn sử dụng */}
      <Card title="Hướng dẫn sử dụng Select Component" className="mb-6">
        <div className="space-y-4">
          <p className="font-medium">Các bước sử dụng component Select:</p>
          <ol className="list-decimal list-inside space-y-2 pl-4">
            <li>
              Import component:{' '}
              <code>import &#123; Select &#125; from '@/shared/components/common';</code>
            </li>
            <li>
              Chuẩn bị dữ liệu options:
              <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 text-sm overflow-auto">
                {`const options = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
];`}
              </pre>
            </li>
            <li>
              Quản lý state:
              <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 text-sm overflow-auto">
                {`// Single select
const [value, setValue] = useState('');

// Multi select
const [multiValue, setMultiValue] = useState<string[]>([]);`}
              </pre>
            </li>
            <li>
              Render component:
              <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 text-sm overflow-auto">
                {`<Select
  label="Label"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={options}
  placeholder="Placeholder"
  // Các props tùy chọn
  searchable={true}
  multiple={false}
  disabled={false}
  loading={false}
  error="Error message"
  helperText="Helper text"
  size="md" // 'sm' | 'md' | 'lg'
  fullWidth={true}
  renderOption={(option) => <CustomOption {...option} />}
/>`}
              </pre>
            </li>
          </ol>

          <div className="mt-6">
            <p className="font-medium">Props chính:</p>
            <table className="min-w-full mt-2 border-collapse">
              <thead>
                <tr className="bg-gray-100 dark:bg-gray-800">
                  <th className="p-2 text-left border border-gray-300 dark:border-gray-700">
                    Prop
                  </th>
                  <th className="p-2 text-left border border-gray-300 dark:border-gray-700">
                    Kiểu dữ liệu
                  </th>
                  <th className="p-2 text-left border border-gray-300 dark:border-gray-700">
                    Mặc định
                  </th>
                  <th className="p-2 text-left border border-gray-300 dark:border-gray-700">
                    Mô tả
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>value</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>string | string[] | number | number[]</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>undefined</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    Giá trị đã chọn
                  </td>
                </tr>
                <tr>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>onChange</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>function</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>undefined</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    Callback khi giá trị thay đổi
                  </td>
                </tr>
                <tr>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>options</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>SelectOption[] | SelectGroup[]</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>[]</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    Danh sách các options
                  </td>
                </tr>
                <tr>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>multiple</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>boolean</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>false</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    Cho phép chọn nhiều
                  </td>
                </tr>
                <tr>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>searchable</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>boolean</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    <code>false</code>
                  </td>
                  <td className="p-2 border border-gray-300 dark:border-gray-700">
                    Cho phép tìm kiếm
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className="mt-6">
            <p className="font-medium">Best Practices:</p>
            <ul className="list-disc list-inside space-y-1 pl-4">
              <li>
                Luôn sử dụng <code>label</code> và <code>placeholder</code> để cải thiện UX
              </li>
              <li>
                Sử dụng <code>searchable</code> khi có nhiều options
              </li>
              <li>
                Xử lý lỗi với prop <code>error</code>
              </li>
              <li>
                Cung cấp thông tin bổ sung với <code>helperText</code>
              </li>
              <li>
                Sử dụng <code>renderOption</code> để tùy chỉnh hiển thị của options
              </li>
            </ul>
          </div>

          <div className="mt-6">
            <p className="font-medium">Hiển thị tài khoản ngân hàng:</p>
            <p className="mb-2">
              Để hiển thị tài khoản ngân hàng với logo, tên ngân hàng và thông tin tài khoản, bạn có
              thể:
            </p>
            <ol className="list-decimal list-inside space-y-2 pl-4">
              <li>
                Chuẩn bị dữ liệu với thông tin chi tiết trong thuộc tính <code>data</code>:
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 text-sm overflow-auto">
                  {`const bankAccountOptions = [
  {
    value: 'vcb',
    label: 'Vietcombank',
    data: {
      accountNumber: '**********',
      accountName: 'NGUYEN VAN A',
      logoUrl: '/path/to/logo.png',
      color: '#004d1a'
    }
  },
  // Thêm các tài khoản khác...
];`}
                </pre>
              </li>
              <li>
                Tạo hàm render tùy chỉnh để hiển thị thông tin:
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 text-sm overflow-auto">
                  {`const renderBankAccountOption = (option) => {
  const bankData = option.data;
  return (
    <div className="flex items-center">
      {/* Logo ngân hàng */}
      <div className="w-10 h-10 rounded-md overflow-hidden mr-3">
        <img src={bankData.logoUrl} alt={option.label} />
      </div>

      {/* Thông tin tài khoản */}
      <div>
        <div className="font-medium">{option.label}</div>
        <div className="text-xs text-gray-500">
          {bankData.accountNumber} • {bankData.accountName}
        </div>
      </div>
    </div>
  );
};`}
                </pre>
              </li>
              <li>
                Sử dụng trong component Select:
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-1 text-sm overflow-auto">
                  {`<Select
  label="Chọn tài khoản ngân hàng"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={bankAccountOptions}
  renderOption={renderBankAccountOption}
  placeholder="Chọn tài khoản"
/>`}
                </pre>
              </li>
            </ol>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SelectDemoNew;
