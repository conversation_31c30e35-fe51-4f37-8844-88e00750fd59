import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon, Card, Button, Select } from '@/shared/components/common';
import { AsyncSelect, CreatableSelect, TypeaheadSelect } from '@/shared/components/common/Select';
import type { SelectOption } from '@/shared/components/common/Select/Select';

/**
 * Trang demo cho cách sử dụng các component Select trong các trường hợp thực tế
 */
const SelectUsageDemo: React.FC = () => {
  useTranslation(); // Keep the hook call to avoid React hooks rules violation

  // Form state
  const [formData, setFormData] = useState({
    country: '',
    languages: [] as string[],
    skills: [] as string[],
    jobTitle: '',
    industry: '',
  });

  // Loading state
  const [isLoading, setIsLoading] = useState(false);

  // Options
  const countryOptions: SelectOption[] = [
    { value: 'vietnam', label: 'Vietnam' },
    { value: 'usa', label: 'United States' },
    { value: 'japan', label: 'Japan' },
    { value: 'korea', label: 'South Korea' },
    { value: 'singapore', label: 'Singapore' },
    { value: 'australia', label: 'Australia' },
  ];

  const languageOptions: SelectOption[] = [
    { value: 'vietnamese', label: 'Vietnamese', icon: <Icon name="chat" size="sm" /> },
    { value: 'english', label: 'English', icon: <Icon name="chat" size="sm" /> },
    { value: 'japanese', label: 'Japanese', icon: <Icon name="chat" size="sm" /> },
    { value: 'korean', label: 'Korean', icon: <Icon name="chat" size="sm" /> },
    { value: 'chinese', label: 'Chinese', icon: <Icon name="chat" size="sm" /> },
    { value: 'french', label: 'French', icon: <Icon name="chat" size="sm" /> },
    { value: 'german', label: 'German', icon: <Icon name="chat" size="sm" /> },
  ];

  const skillOptions: SelectOption[] = [
    { value: 'javascript', label: 'JavaScript', icon: <Icon name="code" size="sm" /> },
    { value: 'typescript', label: 'TypeScript', icon: <Icon name="code" size="sm" /> },
    { value: 'react', label: 'React', icon: <Icon name="code" size="sm" /> },
    { value: 'vue', label: 'Vue', icon: <Icon name="code" size="sm" /> },
    { value: 'angular', label: 'Angular', icon: <Icon name="code" size="sm" /> },
    { value: 'node', label: 'Node.js', icon: <Icon name="server" size="sm" /> },
    { value: 'python', label: 'Python', icon: <Icon name="code" size="sm" /> },
    { value: 'java', label: 'Java', icon: <Icon name="code" size="sm" /> },
  ];

  const jobTitleOptions: SelectOption[] = [
    { value: 'frontend', label: 'Frontend Developer' },
    { value: 'backend', label: 'Backend Developer' },
    { value: 'fullstack', label: 'Full Stack Developer' },
    { value: 'mobile', label: 'Mobile Developer' },
    { value: 'devops', label: 'DevOps Engineer' },
    { value: 'qa', label: 'QA Engineer' },
    { value: 'pm', label: 'Project Manager' },
    { value: 'designer', label: 'UI/UX Designer' },
  ];

  // Simulate API call for industries
  const loadIndustries = async (inputValue: string): Promise<SelectOption[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock API response
    const industries = [
      { value: 'tech', label: 'Technology' },
      { value: 'finance', label: 'Finance' },
      { value: 'healthcare', label: 'Healthcare' },
      { value: 'education', label: 'Education' },
      { value: 'ecommerce', label: 'E-commerce' },
      { value: 'manufacturing', label: 'Manufacturing' },
      { value: 'retail', label: 'Retail' },
      { value: 'media', label: 'Media & Entertainment' },
      { value: 'telecom', label: 'Telecommunications' },
      { value: 'automotive', label: 'Automotive' },
    ];

    // Filter based on input
    return industries.filter(item => item.label.toLowerCase().includes(inputValue.toLowerCase()));
  };

  // Handle form submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      alert(JSON.stringify(formData, null, 2));
    }, 1500);
  };

  // Handle form reset
  const handleReset = () => {
    setFormData({
      country: '',
      languages: [],
      skills: [],
      jobTitle: '',
      industry: '',
    });
  };

  return (
    <div className="max-w-3xl mx-auto">
      <Typography variant="h1" className="mb-6">
        Ví dụ sử dụng Select Components
      </Typography>
      <Typography className="mb-8">
        Dưới đây là ví dụ về cách sử dụng các component Select trong một form thực tế.
      </Typography>

      <Card className="p-6">
        <Typography variant="h2" className="mb-6">
          Form Thông tin Ứng viên
        </Typography>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Country - Basic Select */}
          <div>
            <Select
              label="Quốc gia"
              value={formData.country}
              onChange={(val: string | string[] | number | number[]) =>
                setFormData({ ...formData, country: val as string })
              }
              options={countryOptions}
              placeholder="Chọn quốc gia của bạn"
              fullWidth
              error={formData.country ? undefined : 'Vui lòng chọn quốc gia'}
            />
            <Typography variant="caption" className="mt-1 text-gray-500">
              Sử dụng <code>Select</code> cơ bản cho danh sách cố định và ngắn
            </Typography>
          </div>

          {/* Languages - Multi Select */}
          <div>
            <Select
              label="Ngôn ngữ"
              value={formData.languages}
              onChange={(val: string | string[] | number | number[]) =>
                setFormData({ ...formData, languages: val as string[] })
              }
              options={languageOptions}
              placeholder="Chọn các ngôn ngữ bạn biết"
              multiple
              searchable
              fullWidth
            />
            <Typography variant="caption" className="mt-1 text-gray-500">
              Sử dụng <code>Select</code> với <code>multiple</code> và <code>searchable</code> cho
              việc chọn nhiều giá trị
            </Typography>
          </div>

          {/* Skills - TypeaheadSelect */}
          <div>
            <TypeaheadSelect
              label="Kỹ năng"
              value={formData.skills}
              onChange={(val: string | string[]) =>
                setFormData({ ...formData, skills: val as string[] })
              }
              options={skillOptions}
              placeholder="Gõ để tìm kiếm kỹ năng"
              multiple
              highlightMatch
              maxSuggestions={5}
              fullWidth
            />
            <Typography variant="caption" className="mt-1 text-gray-500">
              Sử dụng <code>TypeaheadSelect</code> cho trải nghiệm gõ và gợi ý tốt hơn
            </Typography>
          </div>

          {/* Job Title - CreatableSelect */}
          <div>
            <CreatableSelect
              label="Vị trí công việc"
              value={formData.jobTitle}
              onChange={(val: string | string[] | number | number[]) =>
                setFormData({ ...formData, jobTitle: val as string })
              }
              options={jobTitleOptions}
              placeholder="Chọn hoặc tạo vị trí công việc"
              formatCreateLabel={inputValue => `Tạo vị trí "${inputValue}"`}
              onCreateOption={inputValue => console.log(`Đã tạo vị trí: ${inputValue}`)}
              fullWidth
            />
            <Typography variant="caption" className="mt-1 text-gray-500">
              Sử dụng <code>CreatableSelect</code> khi muốn cho phép người dùng tạo giá trị mới
            </Typography>
          </div>

          {/* Industry - AsyncSelect */}
          <div>
            <AsyncSelect
              label="Ngành nghề"
              value={formData.industry}
              onChange={(val: string | string[] | number | number[]) =>
                setFormData({ ...formData, industry: val as string })
              }
              loadOptions={loadIndustries}
              placeholder="Tìm kiếm ngành nghề"
              debounceTime={300}
              noOptionsMessage="Không tìm thấy ngành nghề phù hợp"
              loadingMessage="Đang tìm kiếm ngành nghề..."
              fullWidth
            />
            <Typography variant="caption" className="mt-1 text-gray-500">
              Sử dụng <code>AsyncSelect</code> khi cần tải dữ liệu từ API
            </Typography>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button variant="outline" onClick={handleReset} disabled={isLoading}>
              Đặt lại
            </Button>
            <Button type="submit" isLoading={isLoading}>
              Gửi
            </Button>
          </div>
        </form>
      </Card>

      <div className="mt-8">
        <Typography variant="h2" className="mb-4">
          Hướng dẫn sử dụng
        </Typography>
        <Typography>
          Để biết thêm chi tiết về cách sử dụng các component Select, vui lòng tham khảo tài liệu
          hướng dẫn tại <code>docs/guides/advanced-select-components.md</code>.
        </Typography>
      </div>
    </div>
  );
};

export default SelectUsageDemo;
