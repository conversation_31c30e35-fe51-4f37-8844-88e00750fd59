import React from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import {
  Form,
  FormItem,
  Input,
  Select,
  Checkbox,
  Radio,
  RadioGroup,
  Button,
  Card,
  Typography,
} from '@/shared/components/common';
import { z } from 'zod';

const ThemeFormDemo: React.FC = () => {
  const { t } = useTranslation();

  // Options for Select
  const countryOptions = [
    { label: t('components.form.countries.us', 'United States'), value: 'us' },
    { label: t('components.form.countries.uk', 'United Kingdom'), value: 'uk' },
    { label: t('components.form.countries.ca', 'Canada'), value: 'ca' },
    { label: t('components.form.countries.au', 'Australia'), value: 'au' },
    { label: t('components.form.countries.de', 'Germany'), value: 'de' },
    { label: t('components.form.countries.fr', 'France'), value: 'fr' },
    { label: t('components.form.countries.jp', 'Japan'), value: 'jp' },
    { label: t('components.form.countries.cn', 'China'), value: 'cn' },
    { label: t('components.form.countries.in', 'India'), value: 'in' },
    { label: t('components.form.countries.br', 'Brazil'), value: 'br' },
  ];

  // Options for Radio
  const genderOptions = [
    { label: t('components.form.gender.male', 'Male'), value: 'male' },
    { label: t('components.form.gender.female', 'Female'), value: 'female' },
    { label: t('components.form.gender.other', 'Other'), value: 'other' },
  ];

  // Schema validation cho form
  const formSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    country: z.string().optional(),
    agreeTerms: z.boolean().optional(),
    gender: z.string().optional(),
  });

  // Xử lý submit form
  const handleFormSubmit = (data: Record<string, unknown>) => {
    console.log('Form submitted with data:', data);
    alert(JSON.stringify(data, null, 2));
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.form.themeDemo.title', 'Form Components with Theme System')}
        </h1>
        <p className="text-muted">
          {t(
            'components.form.themeDemo.description',
            'Demo of form components using the new theme system'
          )}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ComponentDemo
          title={t('components.form.basicForm.title', 'Basic Form')}
          description={t(
            'components.form.basicForm.description',
            'A basic form with various input types'
          )}
          code={`import { Form, FormItem, Input, Select, Checkbox, RadioGroup, Button } from '@/shared/components/common';
import { z } from 'zod';

// Schema validation cho form
const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  country: z.string().optional(),
  agreeTerms: z.boolean().optional(),
  gender: z.string().optional(),
});

// Xử lý submit form
const handleFormSubmit = (data) => {
  console.log('Form submitted with data:', data);
  alert(JSON.stringify(data, null, 2));
};

<Form schema={formSchema} onSubmit={handleFormSubmit}>
  <FormItem name="name" label="Name" required>
    <Input placeholder="Enter your name" />
  </FormItem>

  <FormItem name="email" label="Email" required>
    <Input type="email" placeholder="Enter your email" />
  </FormItem>

  <FormItem name="country" label="Country">
    <Select
      options={countryOptions}
      placeholder="Select your country"
    />
  </FormItem>

  <FormItem name="agreeTerms">
    <Checkbox label="I agree to the terms and conditions" />
  </FormItem>

  <FormItem name="gender" label="Gender">
    <RadioGroup options={genderOptions} />
  </FormItem>

  <Button type="submit">Submit</Button>
</Form>`}
        >
          <Card className="p-6">
            <Form schema={formSchema} onSubmit={handleFormSubmit}>
              <FormItem name="name" label={t('components.form.labels.name', 'Name')} required>
                <Input placeholder={t('components.form.placeholders.name', 'Enter your name')} />
              </FormItem>

              <FormItem name="email" label={t('components.form.labels.email', 'Email')} required>
                <Input
                  type="email"
                  placeholder={t('components.form.placeholders.email', 'Enter your email')}
                />
              </FormItem>

              <FormItem name="country" label={t('components.form.labels.country', 'Country')}>
                <Select
                  options={countryOptions}
                  placeholder={t('components.form.placeholders.country', 'Select your country')}
                />
              </FormItem>

              <FormItem name="agreeTerms">
                <Checkbox
                  label={t(
                    'components.form.labels.agreeTerms',
                    'I agree to the terms and conditions'
                  )}
                />
              </FormItem>

              <FormItem name="gender" label={t('components.form.labels.gender', 'Gender')}>
                <RadioGroup options={genderOptions} />
              </FormItem>

              <Button type="submit">{t('common.submit', 'Submit')}</Button>
            </Form>
          </Card>
        </ComponentDemo>

        <ComponentDemo
          title={t('components.form.variants.title', 'Form Component Variants')}
          description={t(
            'components.form.variants.description',
            'Different variants of form components'
          )}
          code={`import { Form, FormItem, Input, Select, Checkbox, Radio } from '@/shared/components/common';

<Form onSubmit={() => console.log('Form submitted')}>
  {/* Input with error */}
  <FormItem name="username" label="Username" required>
    <Input defaultValue="johndoe" error="Username is already taken" />
  </FormItem>

  {/* Input with helper text */}
  <FormItem
    name="password"
    label="Password"
    required
    helpText="Must be at least 8 characters"
  >
    <Input type="password" />
  </FormItem>

  {/* Checkbox variants */}
  <div className="space-y-2">
    <FormItem name="checkbox1">
      <Checkbox label="Default Checkbox" checked={true} />
    </FormItem>
    <FormItem name="checkbox2">
      <Checkbox label="Rounded Checkbox" checked={true} variant="rounded" />
    </FormItem>
    <FormItem name="checkbox3">
      <Checkbox label="Filled Checkbox" checked={true} variant="filled" />
    </FormItem>
    <FormItem name="checkbox4">
      <Checkbox label="Outlined Checkbox" checked={true} variant="outlined" />
    </FormItem>
  </div>

  {/* Radio variants */}
  <div className="space-y-2">
    <FormItem name="radio1">
      <Radio label="Default Radio" checked={true} name="radioGroup1" />
    </FormItem>
    <FormItem name="radio2">
      <Radio label="Filled Radio" checked={true} variant="filled" name="radioGroup2" />
    </FormItem>
    <FormItem name="radio3">
      <Radio label="Outlined Radio" checked={true} variant="outlined" name="radioGroup3" />
    </FormItem>
  </div>

  {/* Select with error */}
  <FormItem name="country" label="Country" required>
    <Select
      options={countryOptions}
      placeholder="Select your country"
      error="Please select a country"
    />
  </FormItem>
</Form>`}
        >
          <Card className="p-6">
            <Form onSubmit={() => console.log('Form submitted')}>
              {/* Input with error */}
              <div className="mb-6">
                <Typography variant="subtitle1">
                  {t('components.form.variants.inputError.title', 'Input with error')}
                </Typography>
                <FormItem
                  name="username"
                  label={t('components.form.labels.username', 'Username')}
                  required
                >
                  <Input
                    defaultValue="johndoe"
                    error={t('components.form.errors.usernameTaken', 'Username is already taken')}
                  />
                </FormItem>
              </div>

              {/* Input with helper text */}
              <div className="mb-6">
                <Typography variant="subtitle1">
                  {t('components.form.variants.inputHelper.title', 'Input with helper text')}
                </Typography>
                <FormItem
                  name="password"
                  label={t('components.form.labels.password', 'Password')}
                  required
                  helpText={t(
                    'components.form.helpers.passwordLength',
                    'Must be at least 8 characters'
                  )}
                >
                  <Input type="password" />
                </FormItem>
              </div>

              {/* Checkbox variants */}
              <div className="mb-6">
                <Typography variant="subtitle1">
                  {t('components.form.variants.checkbox.title', 'Checkbox variants')}
                </Typography>
                <div className="space-y-2">
                  <FormItem name="checkbox1">
                    <Checkbox
                      label={t('components.form.variants.checkbox.default', 'Default Checkbox')}
                      checked={true}
                    />
                  </FormItem>
                  <FormItem name="checkbox2">
                    <Checkbox
                      label={t('components.form.variants.checkbox.rounded', 'Rounded Checkbox')}
                      checked={true}
                      variant="rounded"
                    />
                  </FormItem>
                  <FormItem name="checkbox3">
                    <Checkbox
                      label={t('components.form.variants.checkbox.filled', 'Filled Checkbox')}
                      checked={true}
                      variant="filled"
                    />
                  </FormItem>
                  <FormItem name="checkbox4">
                    <Checkbox
                      label={t('components.form.variants.checkbox.outlined', 'Outlined Checkbox')}
                      checked={true}
                      variant="outlined"
                    />
                  </FormItem>
                </div>
              </div>

              {/* Radio variants */}
              <div className="mb-6">
                <Typography variant="subtitle1">
                  {t('components.form.variants.radio.title', 'Radio variants')}
                </Typography>
                <div className="space-y-2">
                  <FormItem name="radio1">
                    <Radio
                      label={t('components.form.variants.radio.default', 'Default Radio')}
                      checked={true}
                      name="radioGroup1"
                    />
                  </FormItem>
                  <FormItem name="radio2">
                    <Radio
                      label={t('components.form.variants.radio.filled', 'Filled Radio')}
                      checked={true}
                      variant="filled"
                      name="radioGroup2"
                    />
                  </FormItem>
                  <FormItem name="radio3">
                    <Radio
                      label={t('components.form.variants.radio.outlined', 'Outlined Radio')}
                      checked={true}
                      variant="outlined"
                      name="radioGroup3"
                    />
                  </FormItem>
                </div>
              </div>

              {/* Select with error */}
              <div>
                <Typography variant="subtitle1">
                  {t('components.form.variants.selectError.title', 'Select with error')}
                </Typography>
                <FormItem
                  name="country"
                  label={t('components.form.labels.country', 'Country')}
                  required
                >
                  <Select
                    options={countryOptions}
                    placeholder={t('components.form.placeholders.country', 'Select your country')}
                    error={t('components.form.errors.selectCountry', 'Please select a country')}
                  />
                </FormItem>
              </div>
            </Form>
          </Card>
        </ComponentDemo>
      </div>
    </div>
  );
};

export default ThemeFormDemo;
