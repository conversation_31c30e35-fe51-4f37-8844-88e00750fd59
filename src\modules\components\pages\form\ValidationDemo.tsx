import React, { useRef } from 'react';
import { FieldValues } from 'react-hook-form';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { ValidationRules } from '@/shared/validation/rules';
import { AsyncValidators } from '@/shared/validation/validators';
import { useValidation } from '@/shared/validation/hooks/useValidation';
import { useAsyncValidation } from '@/shared/validation/hooks/useAsyncValidation';

// Demo schema using enhanced validation - simplified for now
const validationDemoSchema = undefined; // Will be handled by individual field validation

/**
 * Demo page cho Enhanced Validation System
 */
const ValidationDemo: React.FC = () => {
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Mock async validation functions
  const checkEmailUnique = async (email: string): Promise<boolean> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    return !['<EMAIL>', '<EMAIL>'].includes(email);
  };

  const handleSubmit = async (values: FieldValues) => {
    console.log('Validation demo values:', values);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  // Example of using validation hooks
  const emailValidation = useAsyncValidation(
    AsyncValidators.uniqueEmail(checkEmailUnique),
    { debounceMs: 500 }
  );

  const passwordValidation = useValidation([
    ValidationRules.required(),
    ValidationRules.minLength(8),
    ValidationRules.strongPassword(),
  ]);

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Enhanced Validation System Demo
        </h1>
        <p className="text-muted">
          Demonstration of comprehensive validation features with sync/async validation, custom rules, and error handling
        </p>
      </div>

      {/* Main Registration Form */}
      <Card title="User Registration with Enhanced Validation" className="mb-8">
        <Form
          ref={formRef}
          schema={validationDemoSchema}
          onSubmit={handleSubmit}
          defaultValues={{
            email: '',
            password: '',
            confirmPassword: '',
            firstName: '',
            lastName: '',
            phone: '',
          }}
          className="space-y-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormItem name="firstName" label="First Name">
              <Input placeholder="Enter your first name" />
            </FormItem>

            <FormItem name="lastName" label="Last Name">
              <Input placeholder="Enter your last name" />
            </FormItem>
          </div>

          <FormItem name="email" label="Email Address">
            <div>
              <Input
                type="email"
                placeholder="Enter your email"
                onChange={(e) => {
                  emailValidation.validate(e.target.value, {
                    field: 'email',
                    formValues: formRef.current?.getValues() || {},
                  });
                }}
              />
              {emailValidation.isValidating && (
                <div className="text-sm text-blue-600 mt-1">
                  Checking email availability...
                </div>
              )}
              {emailValidation.error && (
                <div className="text-sm text-red-600 mt-1">
                  {emailValidation.error.message}
                </div>
              )}
            </div>
          </FormItem>

          <FormItem name="phone" label="Phone Number (Optional)">
            <Input type="tel" placeholder="Enter your phone number" />
          </FormItem>

          <FormItem name="password" label="Password">
            <div>
              <Input
                type="password"
                placeholder="Enter a strong password"
                onChange={(e) => {
                  passwordValidation.validateField('password', e.target.value, {
                    password: e.target.value,
                  });
                }}
              />
              {passwordValidation.isValidating && (
                <div className="text-sm text-blue-600 mt-1">
                  Validating password strength...
                </div>
              )}
              <div className="text-sm text-muted mt-1">
                Password must contain at least 8 characters, including uppercase, lowercase, number and special character
              </div>
            </div>
          </FormItem>

          <FormItem name="confirmPassword" label="Confirm Password">
            <Input type="password" placeholder="Confirm your password" />
          </FormItem>

          <FormItem name="birthDate" label="Birth Date (Optional)">
            <Input type="date" />
          </FormItem>

          <div className="flex gap-4 pt-4">
            <Button type="submit" variant="primary" size="md">
              Register
            </Button>
            <Button
              type="button"
              variant="outline"
              size="md"
              onClick={() => formRef.current?.reset()}
            >
              Reset
            </Button>
          </div>
        </Form>
      </Card>

      {/* Validation Rules Demo */}
      <Card title="Validation Rules Examples" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Basic Validation Rules</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Required Field</label>
                <Input placeholder="This field is required" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.required()</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Email Validation</label>
                <Input type="email" placeholder="Enter valid email" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.email()</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Min/Max Length</label>
                <Input placeholder="5-20 characters" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.minLength(5), maxLength(20)</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Numeric Range</label>
                <Input type="number" placeholder="1-100" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.min(1), max(100)</div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-3">Vietnamese-Specific Validation</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Vietnamese Phone</label>
                <Input placeholder="0123456789" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.phoneVN()</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Vietnamese Name</label>
                <Input placeholder="Nguyễn Văn A" />
                <div className="text-xs text-muted mt-1">Uses: CompositeRules.nameVN()</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Citizen ID</label>
                <Input placeholder="123456789 or 123456789012" />
                <div className="text-xs text-muted mt-1">Uses: CompositeRules.citizenIdVN()</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Currency VND</label>
                <Input placeholder="1000000" />
                <div className="text-xs text-muted mt-1">Uses: CompositeRules.currency()</div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-3">Advanced Validation</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Strong Password</label>
                <Input type="password" placeholder="Strong password required" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.strongPassword()</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Future Date</label>
                <Input type="date" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.futureDate()</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Age Validation</label>
                <Input type="date" placeholder="Must be 18+" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.minAge(18)</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">File Upload</label>
                <Input type="file" accept="image/*" />
                <div className="text-xs text-muted mt-1">Uses: ValidationRules.fileSize(), fileType()</div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Async Validation Demo */}
      <Card title="Async Validation Examples" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Server-Side Validation</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Unique Email Check</label>
                <Input
                  type="email"
                  placeholder="Try: <EMAIL>"
                  className="mb-2"
                />
                <div className="text-xs text-muted">
                  Checks server for email uniqueness (simulated)
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Domain Availability</label>
                <Input placeholder="example.com" className="mb-2" />
                <div className="text-xs text-muted">
                  Checks if domain is available (simulated)
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Validation Patterns Demo */}
      <Card title="Validation Patterns & Transformers" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Pattern Validation</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Credit Card</label>
                <Input placeholder="****************" />
                <div className="text-xs text-muted mt-1">Pattern: Credit card number</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">UUID</label>
                <Input placeholder="123e4567-e89b-12d3-a456-************" />
                <div className="text-xs text-muted mt-1">Pattern: UUID format</div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-3">Data Transformers</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Auto Trim & Lowercase</label>
                <Input placeholder="  <EMAIL>  " />
                <div className="text-xs text-muted mt-1">Transforms: trim() + toLowerCase()</div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Phone Formatting</label>
                <Input placeholder="1234567890" />
                <div className="text-xs text-muted mt-1">Transforms: formatPhone()</div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Usage Guide */}
      <Card title="Validation System Features" className="mb-8">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-lg mb-2">Core Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>Sync & Async Validation:</strong> Support for both synchronous and asynchronous validation</li>
              <li><strong>Debounced Validation:</strong> Configurable debouncing for better UX</li>
              <li><strong>Caching:</strong> Built-in caching for expensive validations</li>
              <li><strong>Error Severity:</strong> Support for errors, warnings, and info messages</li>
              <li><strong>Cross-Field Validation:</strong> Validate fields that depend on each other</li>
              <li><strong>Conditional Validation:</strong> Rules that apply based on conditions</li>
              <li><strong>Internationalization:</strong> Multi-language error messages</li>
              <li><strong>Data Transformers:</strong> Transform data before validation</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-2">Vietnamese Support</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>Phone Numbers:</strong> Vietnamese phone number validation</li>
              <li><strong>Names:</strong> Vietnamese name validation with diacritics</li>
              <li><strong>Citizen ID:</strong> CMND/CCCD validation</li>
              <li><strong>Addresses:</strong> Vietnamese address formatting</li>
              <li><strong>Currency:</strong> VND currency formatting</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-2">Advanced Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>Validation Hooks:</strong> React hooks for easy integration</li>
              <li><strong>Schema Factories:</strong> Pre-built schemas for common use cases</li>
              <li><strong>Validator Builder:</strong> Compose complex validation rules</li>
              <li><strong>Performance Monitoring:</strong> Built-in metrics and monitoring</li>
              <li><strong>Error Recovery:</strong> Graceful error handling and fallbacks</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ValidationDemo;
