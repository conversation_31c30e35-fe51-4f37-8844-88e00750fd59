import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Card, Icon } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';

interface FormComponentCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  to: string;
}

/**
 * Card hiển thị thông tin về một loại form component
 */
const FormComponentCard: React.FC<FormComponentCardProps> = ({ title, description, icon, to }) => {
  return (
    <Link to={to} className="block">
      <Card className="h-full transition-all duration-300 hover:shadow-md hover:-translate-y-1">
        <div className="flex items-start">
          <div className="mr-4 p-3 bg-primary/10 text-primary rounded-lg">{icon}</div>
          <div>
            <h3 className="text-lg font-medium mb-2 text-foreground">{title}</h3>
            <p className="text-muted">{description}</p>
          </div>
        </div>
      </Card>
    </Link>
  );
};

/**
 * Trang chính hiển thị tất cả các loại form components
 */
const FormPage: React.FC = () => {
  const { t } = useTranslation();

  // Danh sách các form components
  const formComponents = [
    {
      title: t('components.form.basic.title', 'Basic Form Components'),
      description: t(
        'components.form.basic.description',
        'Form, FormItem, Input, and other basic form components'
      ),
      icon: <Icon name="layout" size="lg" />,
      to: '/components/form/basic',
    },
    {
      title: t('components.form.layout.title', 'Form Layouts'),
      description: t(
        'components.form.layout.description',
        'Grid, inline, and horizontal form layouts'
      ),
      icon: <Icon name="grid" size="lg" />,
      to: '/components/form/layouts',
    },
    {
      title: t('components.form.conditional.title', 'Conditional Form Fields'),
      description: t(
        'components.form.conditional.description',
        'Show or hide fields based on conditions'
      ),
      icon: <Icon name="filter" size="lg" />,
      to: '/components/form/conditional',
    },
    {
      title: t('components.formSections.title', 'Form Sections'),
      description: t(
        'components.formSections.description',
        'Organize forms into collapsible sections'
      ),
      icon: <Icon name="layers" size="lg" />,
      to: '/components/form/sections',
    },
    {
      title: t('components.form.dependencies.title', 'Form Field Dependencies'),
      description: t(
        'components.form.dependencies.description',
        'Create dependent fields like cascading dropdowns'
      ),
      icon: <Icon name="link" size="lg" />,
      to: '/components/form/dependencies',
    },
    {
      title: t('components.form.apiForm.title', 'API Form Integration'),
      description: t('components.form.apiForm.description', 'Integrate forms with API calls'),
      icon: <Icon name="server" size="lg" />,
      to: '/components/form/api-integration',
    },
    {
      title: t('components.form.templates.title', 'Form Templates'),
      description: t(
        'components.form.templates.description',
        'Ready-to-use form templates for common use cases'
      ),
      icon: <Icon name={'file' as IconName} size="lg" />,
      to: '/components/form/templates',
    },
    {
      title: t('components.form.array.title', 'Form Arrays'),
      description: t(
        'components.form.array.description',
        'Dynamic form fields with add/remove functionality'
      ),
      icon: <Icon name="list" size="lg" />,
      to: '/form-array-demo',
    },
    {
      title: t('components.inputs.select.title', 'Select Component'),
      description: t(
        'components.inputs.select.description',
        'Advanced select component with multiple features'
      ),
      icon: <Icon name="chevron-down" size="lg" />,
      to: '/select-demo',
    },
    {
      title: t('components.inputs.select.newFormat.title', 'Select Component (New Format)'),
      description: t(
        'components.inputs.select.newFormat.description',
        'Select component with detailed documentation'
      ),
      icon: <Icon name="chevron-down" size="lg" />,
      to: '/select-demo-new',
    },
    {
      title: t('components.inputs.select.advanced.title', 'Advanced Select Components'),
      description: t(
        'components.inputs.select.advanced.description',
        'Async, Creatable, Combobox, and Typeahead Select components'
      ),
      icon: <Icon name="search" size="lg" />,
      to: '/advanced-select-demo',
    },
    {
      title: t(
        'components.inputs.select.advancedNew.title',
        'Advanced Select Components (New Format)'
      ),
      description: t(
        'components.inputs.select.advancedNew.description',
        'Advanced Select components with detailed documentation'
      ),
      icon: <Icon name="search" size="lg" />,
      to: '/advanced-select-demo-new',
    },
    {
      title: t('components.inputs.select.usage.title', 'Select Usage Examples'),
      description: t(
        'components.inputs.select.usage.description',
        'Practical examples of using Select components in forms'
      ),
      icon: <Icon name="file" size="lg" />,
      to: '/select-usage-demo',
    },
    {
      title: t('components.inputs.datepicker.title', 'DatePicker Component'),
      description: t(
        'components.inputs.datepicker.description',
        'Advanced date picker with single date and range selection'
      ),
      icon: <Icon name="calendar" size="lg" />,
      to: '/datepicker-demo',
    },
    {
      title: t('components.inputs.checkboxRadio.title', 'Checkbox & Radio Components'),
      description: t(
        'components.inputs.checkboxRadio.description',
        'Checkbox, CheckboxGroup, Radio, and RadioGroup components'
      ),
      icon: <Icon name={'check-square' as IconName} size="lg" />,
      to: '/checkbox-radio-demo',
    },
  ];

  // Danh sách các form examples
  const formExamples = [
    {
      title: t('components.form.examples.login.title', 'Login & Registration Forms'),
      description: t(
        'components.form.examples.login.description',
        'Complete examples of login and registration forms with validation.'
      ),
      to: '/form-demo',
    },
    {
      title: t('components.form.examples.multistep.title', 'Multi-step Form'),
      description: t(
        'components.form.examples.multistep.description',
        'Example of a multi-step form with progress tracking and validation.'
      ),
      to: '/form-conditional-demo',
    },
    {
      title: t('components.form.examples.datepicker.title', 'Advanced DatePicker Examples'),
      description: t(
        'components.form.examples.datepicker.description',
        'Real-world examples of DatePicker usage including business days, holidays, and form integration.'
      ),
      to: '/datepicker-advanced-demo',
    },
    {
      title: t('components.form.examples.checkboxRadio.title', 'Checkbox & Radio Examples'),
      description: t(
        'components.form.examples.checkboxRadio.description',
        'Examples of Checkbox and Radio components with different states, sizes, and layouts.'
      ),
      to: '/checkbox-radio-demo',
    },
  ];

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.form.title', 'Form Components')}
        </h1>
        <p className="text-muted">
          {t(
            'components.form.description',
            'A collection of form components for building forms with various layouts and behaviors.'
          )}
        </p>
      </div>

      {/* Form Components */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {formComponents.map((component, index) => (
          <FormComponentCard
            key={index}
            title={component.title}
            description={component.description}
            icon={component.icon}
            to={component.to}
          />
        ))}
      </div>

      {/* Form Examples */}
      <div className="mt-12">
        <h2 className="text-xl font-bold mb-4 text-foreground">
          {t('components.form.examples.title', 'Form Examples')}
        </h2>
        <p className="text-muted mb-6">
          {t(
            'components.form.examples.description',
            'Complete form examples for common use cases.'
          )}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {formExamples.map((example, index) => (
            <Link key={index} to={example.to} className="block">
              <Card className="h-full transition-all duration-300 hover:shadow-md hover:-translate-y-1">
                <h3 className="text-lg font-medium mb-2 text-foreground">{example.title}</h3>
                <p className="text-muted mb-4">{example.description}</p>
                <div className="flex justify-end">
                  <span className="text-primary flex items-center">
                    {t('common.viewExample', 'View Example')}{' '}
                    <Icon name="chevron-right" size="sm" className="ml-1" />
                  </span>
                </div>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FormPage;
