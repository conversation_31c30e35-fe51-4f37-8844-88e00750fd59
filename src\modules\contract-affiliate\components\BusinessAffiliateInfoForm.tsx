/**
 * Component form thông tin doanh nghiệp affiliate
 */
import React, { useRef, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FieldValues } from 'react-hook-form';
import { Button, Form, FormItem, Input, Typography } from '@/shared/components/common';
import { ContractAffiliateStepProps, BusinessAffiliateInfo } from '../types';
import { FormRef } from '@/shared/components/common/Form/Form';

const BusinessAffiliateInfoForm: React.FC<ContractAffiliateStepProps> = ({ 
  data, 
  onNext, 
  onPrevious, 
  isLoading 
}) => {
  const { t } = useTranslation('contract-affiliate');
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Validation schema
  const schema = useMemo(() => z.object({
    // Business Info
    companyName: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(2, t('contract-affiliate:validation.minLength', { length: 2 }))
      .max(100, t('contract-affiliate:validation.maxLength', { length: 100 })),
    taxCode: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .regex(/^[0-9]{10,13}$/, t('contract-affiliate:validation.taxCode')),
    companyEmail: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .email(t('contract-affiliate:validation.email')),
    companyAddress: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(10, t('contract-affiliate:validation.minLength', { length: 10 }))
      .max(200, t('contract-affiliate:validation.maxLength', { length: 200 })),
    companyPhone: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .regex(/^[0-9+\-\s()]{10,15}$/, t('contract-affiliate:validation.phone')),
    representative: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(2, t('contract-affiliate:validation.minLength', { length: 2 }))
      .max(50, t('contract-affiliate:validation.maxLength', { length: 50 })),
    position: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(2, t('contract-affiliate:validation.minLength', { length: 2 }))
      .max(50, t('contract-affiliate:validation.maxLength', { length: 50 })),
    
    // Bank Info
    bankName: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(2, t('contract-affiliate:validation.minLength', { length: 2 }))
      .max(100, t('contract-affiliate:validation.maxLength', { length: 100 })),
    accountNumber: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .regex(/^[0-9]{6,20}$/, t('contract-affiliate:validation.accountNumber')),
    accountHolder: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(2, t('contract-affiliate:validation.minLength', { length: 2 }))
      .max(50, t('contract-affiliate:validation.maxLength', { length: 50 })),
    branch: z
      .string()
      .optional()
      .refine((val) => !val || val.length >= 2, t('contract-affiliate:validation.minLength', { length: 2 })),
  }), [t]);

  const handleSubmit = (formData: FieldValues) => {
    const { bankName, accountNumber, accountHolder, branch, ...businessInfo } = formData;
    
    const businessAffiliateInfo: BusinessAffiliateInfo = {
      ...businessInfo,
      bankInfo: {
        bankName,
        accountNumber,
        accountHolder,
        branch: branch || undefined,
      }
    } as BusinessAffiliateInfo;

    onNext({ businessInfo: businessAffiliateInfo });
  };

  const defaultValues = useMemo(() => {
    if (data.businessInfo) {
      const { bankInfo, ...businessInfo } = data.businessInfo;
      return {
        ...businessInfo,
        ...bankInfo,
      };
    }
    return {};
  }, [data.businessInfo]);

  return (
    <div className="w-full">
      <Form
        ref={formRef}
        schema={schema}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        mode="onSubmit"
        validateOnChange={false}
        validateOnBlur={true}
      >
        <div className="space-y-8">
          {/* Business Information Section */}
          <div className="space-y-6">
            <Typography variant="h3" className="text-lg font-semibold">
              {t('contract-affiliate:businessInfo.title')}
            </Typography>
            
            {/* Tên công ty */}
            <FormItem name="companyName" label={t('contract-affiliate:businessInfo.companyName')} required>
              <Input
                placeholder={t('contract-affiliate:businessInfo.placeholders.companyName')}
                className="w-full"
              />
            </FormItem>

            {/* Mã số thuế */}
            <FormItem name="taxCode" label={t('contract-affiliate:businessInfo.taxCode')} required>
              <Input
                placeholder={t('contract-affiliate:businessInfo.placeholders.taxCode')}
                className="w-full"
              />
            </FormItem>

            {/* Email công ty */}
            <FormItem name="companyEmail" label={t('contract-affiliate:businessInfo.companyEmail')} required>
              <Input
                type="email"
                placeholder={t('contract-affiliate:businessInfo.placeholders.companyEmail')}
                className="w-full"
              />
            </FormItem>

            {/* Số điện thoại công ty */}
            <FormItem name="companyPhone" label={t('contract-affiliate:businessInfo.companyPhone')} required>
              <Input
                type="tel"
                placeholder={t('contract-affiliate:businessInfo.placeholders.companyPhone')}
                className="w-full"
              />
            </FormItem>

            {/* Địa chỉ công ty */}
            <FormItem name="companyAddress" label={t('contract-affiliate:businessInfo.companyAddress')} required>
              <Input
                placeholder={t('contract-affiliate:businessInfo.placeholders.companyAddress')}
                className="w-full"
              />
            </FormItem>

            {/* Người đại diện */}
            <FormItem name="representative" label={t('contract-affiliate:businessInfo.representative')} required>
              <Input
                placeholder={t('contract-affiliate:businessInfo.placeholders.representative')}
                className="w-full"
              />
            </FormItem>

            {/* Chức vụ */}
            <FormItem name="position" label={t('contract-affiliate:businessInfo.position')} required>
              <Input
                placeholder={t('contract-affiliate:businessInfo.placeholders.position')}
                className="w-full"
              />
            </FormItem>
          </div>

          {/* Bank Information Section */}
          <div className="space-y-6">
            <Typography variant="h3" className="text-lg font-semibold">
              {t('contract-affiliate:bankInfo.title')}
            </Typography>
            
            {/* Tên ngân hàng */}
            <FormItem name="bankName" label={t('contract-affiliate:bankInfo.bankName')} required>
              <Input
                placeholder={t('contract-affiliate:bankInfo.placeholders.bankName')}
                className="w-full"
              />
            </FormItem>

            {/* Số tài khoản */}
            <FormItem name="accountNumber" label={t('contract-affiliate:bankInfo.accountNumber')} required>
              <Input
                placeholder={t('contract-affiliate:bankInfo.placeholders.accountNumber')}
                className="w-full"
              />
            </FormItem>

            {/* Tên chủ tài khoản */}
            <FormItem name="accountHolder" label={t('contract-affiliate:bankInfo.accountHolder')} required>
              <Input
                placeholder={t('contract-affiliate:bankInfo.placeholders.accountHolder')}
                className="w-full"
              />
            </FormItem>

            {/* Chi nhánh (tùy chọn) */}
            <FormItem name="branch" label={t('contract-affiliate:bankInfo.branch')}>
              <Input
                placeholder={t('contract-affiliate:bankInfo.placeholders.branch')}
                className="w-full"
              />
            </FormItem>
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={onPrevious}
            disabled={isLoading}
          >
            {t('contract-affiliate:actions.previous')}
          </Button>

          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
          >
            {t('contract-affiliate:actions.next')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default BusinessAffiliateInfoForm;
