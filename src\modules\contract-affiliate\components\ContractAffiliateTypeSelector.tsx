/**
 * Component chọ<PERSON> lo<PERSON><PERSON> hợp đồng affiliate (Doanh nghiệp/C<PERSON> nhân)
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon } from '@/shared/components/common';
import { ContractAffiliateType, ContractAffiliateStepProps } from '../types';

const ContractAffiliateTypeSelector: React.FC<ContractAffiliateStepProps> = ({ data, onNext }) => {
  const { t } = useTranslation('contract-affiliate');

  const handleSelectType = (type: ContractAffiliateType) => {
    // Tự động chuyển sang bước tiếp theo khi chọn loại
    onNext({ type });
  };

  return (
    <div className="w-full">
      <div className="text-center mb-8">
        <Typography variant="h2" className="text-2xl font-bold mb-4">
          {t('contract-affiliate:types.selectType')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('contract-affiliate:types.selectTypeDescription')}
        </Typography>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-w-4xl mx-auto">
        {/* Doanh nghiệp */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
            data.type === ContractAffiliateType.BUSINESS
              ? 'bg-primary/5 shadow-lg border-primary'
              : 'hover:shadow-md hover:border-primary/50'
          }`}
          onClick={() => handleSelectType(ContractAffiliateType.BUSINESS)}
        >
          <div className="text-center p-6">
            <div className="mb-4">
              <Icon
                name="building"
                size="xl"
                className={`mx-auto ${
                  data.type === ContractAffiliateType.BUSINESS ? 'text-primary' : 'text-muted'
                }`}
              />
            </div>
            <Typography variant="h4" className="mb-2">
              {t('contract-affiliate:types.business')}
            </Typography>
            <Typography variant="body2" className="text-muted">
              Dành cho các doanh nghiệp, công ty muốn trở thành đối tác affiliate
            </Typography>
          </div>
        </Card>

        {/* Cá nhân */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
            data.type === ContractAffiliateType.PERSONAL
              ? 'bg-primary/5 shadow-lg border-primary'
              : 'hover:shadow-md hover:border-primary/50'
          }`}
          onClick={() => handleSelectType(ContractAffiliateType.PERSONAL)}
        >
          <div className="text-center p-6">
            <div className="mb-4">
              <Icon
                name="user"
                size="xl"
                className={`mx-auto ${
                  data.type === ContractAffiliateType.PERSONAL ? 'text-primary' : 'text-muted'
                }`}
              />
            </div>
            <Typography variant="h4" className="mb-2">
              {t('contract-affiliate:types.personal')}
            </Typography>
            <Typography variant="body2" className="text-muted">
              Dành cho cá nhân muốn trở thành đối tác affiliate
            </Typography>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ContractAffiliateTypeSelector;
