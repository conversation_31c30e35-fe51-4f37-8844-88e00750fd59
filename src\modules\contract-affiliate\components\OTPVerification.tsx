/**
 * Wrapper component cho OTPVerification từ contract module
 */
import React from 'react';
import { OTPVerification as OriginalOTPVerification } from '@/modules/contract/components';
import { ContractAffiliateStepProps, ContractAffiliateType } from '../types';
import { ContractType, ContractData } from '@/modules/contract/types';

const OTPVerification: React.FC<ContractAffiliateStepProps> = (props) => {
  // Convert ContractAffiliateData to ContractData
  const convertedData: ContractData = {
    type: props.data.type === ContractAffiliateType.BUSINESS ? ContractType.BUSINESS : ContractType.PERSONAL,
    termsAccepted: props.data.termsAccepted,
    businessInfo: props.data.businessInfo ? {
      companyName: props.data.businessInfo.companyName,
      taxCode: props.data.businessInfo.taxCode,
      companyEmail: props.data.businessInfo.companyEmail,
      companyAddress: props.data.businessInfo.companyAddress,
      companyPhone: props.data.businessInfo.companyPhone,
      representative: props.data.businessInfo.representative,
      position: props.data.businessInfo.position,
    } : undefined,
    personalInfo: props.data.personalInfo ? {
      fullName: props.data.personalInfo.fullName,
      dateOfBirth: props.data.personalInfo.dateOfBirth,
      idNumber: props.data.personalInfo.idNumber,
      idIssuedDate: props.data.personalInfo.idIssuedDate,
      idIssuedPlace: props.data.personalInfo.idIssuedPlace,
      phone: props.data.personalInfo.phone,
      address: props.data.personalInfo.address,
      taxCode: props.data.personalInfo.taxCode,
    } : undefined,
    contractUrl: props.data.contractUrl,
    contractBase64: props.data.contractBase64,
    handSignature: props.data.handSignature,
    otpCode: props.data.otpCode,
    isCompleted: props.data.isCompleted,
  };

  const handleNext = (updatedData: Partial<ContractData>) => {
    // Convert back to ContractAffiliateData
    props.onNext({
      contractUrl: updatedData.contractUrl,
      contractBase64: updatedData.contractBase64,
      handSignature: updatedData.handSignature,
      otpCode: updatedData.otpCode,
      isCompleted: updatedData.isCompleted,
    });
  };

  return (
    <OriginalOTPVerification
      data={convertedData}
      onNext={handleNext}
      onPrevious={props.onPrevious}
      isLoading={props.isLoading}
    />
  );
};

export default OTPVerification;
