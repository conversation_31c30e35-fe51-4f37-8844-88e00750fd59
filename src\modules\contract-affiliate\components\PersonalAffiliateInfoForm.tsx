/**
 * Component form thông tin cá nhân affiliate
 */
import React, { useRef, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FieldValues } from 'react-hook-form';
import { Button, Form, FormItem, Input, DatePickerFormField, Typography } from '@/shared/components/common';
import { ValidationSchemas } from '@/shared/validation/schemas';
import { ContractAffiliateStepProps, PersonalAffiliateInfo } from '../types';
import { FormRef } from '@/shared/components/common/Form/Form';

const PersonalAffiliateInfoForm: React.FC<ContractAffiliateStepProps> = ({ 
  data, 
  onNext, 
  onPrevious, 
  isLoading 
}) => {
  const { t } = useTranslation('contract-affiliate');
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Validation schema
  const schema = useMemo(() => z.object({
    // Personal Info
    fullName: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(2, t('contract-affiliate:validation.minLength', { length: 2 }))
      .max(50, t('contract-affiliate:validation.maxLength', { length: 50 }))
      .regex(/^[a-zA-ZÀ-ỹ\s]+$/, t('contract-affiliate:validation.nameFormat')),
    dateOfBirth: ValidationSchemas.flexibleDate({ t })
      .refine((date) => {
        const birthDate = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        return age >= 18 && age <= 100;
      }, t('contract-affiliate:validation.ageRange')),
    idNumber: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .regex(/^[0-9]{9,12}$/, t('contract-affiliate:validation.idNumber')),
    idIssuedDate: ValidationSchemas.flexibleDate({ t })
      .refine((date) => {
        const issuedDate = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        return issuedDate <= today;
      }, t('contract-affiliate:validation.pastDate')),
    idIssuedPlace: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(5, t('contract-affiliate:validation.minLength', { length: 5 }))
      .max(100, t('contract-affiliate:validation.maxLength', { length: 100 })),
    phone: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .regex(/^[0-9+\-\s()]{10,15}$/, t('contract-affiliate:validation.phone')),
    address: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(10, t('contract-affiliate:validation.minLength', { length: 10 }))
      .max(200, t('contract-affiliate:validation.maxLength', { length: 200 })),
    taxCode: z
      .string()
      .optional()
      .refine((val) => !val || /^[0-9]{10,13}$/.test(val), t('contract-affiliate:validation.taxCode')),
    
    // Bank Info
    bankName: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(2, t('contract-affiliate:validation.minLength', { length: 2 }))
      .max(100, t('contract-affiliate:validation.maxLength', { length: 100 })),
    accountNumber: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .regex(/^[0-9]{6,20}$/, t('contract-affiliate:validation.accountNumber')),
    accountHolder: z
      .string()
      .min(1, t('contract-affiliate:validation.required'))
      .min(2, t('contract-affiliate:validation.minLength', { length: 2 }))
      .max(50, t('contract-affiliate:validation.maxLength', { length: 50 }))
      .regex(/^[a-zA-ZÀ-ỹ\s]+$/, t('contract-affiliate:validation.nameFormat')),
    branch: z
      .string()
      .optional()
      .refine((val) => !val || val.length >= 2, t('contract-affiliate:validation.minLength', { length: 2 })),
  }), [t]);

  const handleSubmit = (formData: FieldValues) => {
    const { bankName, accountNumber, accountHolder, branch, ...personalInfo } = formData;
    
    const personalAffiliateInfo: PersonalAffiliateInfo = {
      fullName: personalInfo['fullName'],
      dateOfBirth: personalInfo['dateOfBirth'],
      idNumber: personalInfo['idNumber'],
      idIssuedDate: personalInfo['idIssuedDate'],
      idIssuedPlace: personalInfo['idIssuedPlace'],
      phone: personalInfo['phone'],
      address: personalInfo['address'],
      taxCode: personalInfo['taxCode'],
      bankInfo: {
        bankName,
        accountNumber,
        accountHolder,
        branch: branch || undefined,
      }
    };

    onNext({ personalInfo: personalAffiliateInfo });
  };

  const defaultValues = useMemo(() => {
    if (data.personalInfo) {
      const { bankInfo, ...personalInfo } = data.personalInfo;
      return {
        ...personalInfo,
        ...bankInfo,
      };
    }
    return {};
  }, [data.personalInfo]);

  return (
    <div className="w-full">
      <Form
        ref={formRef}
        schema={schema}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        mode="onSubmit"
        validateOnChange={false}
        validateOnBlur={true}
      >
        <div className="space-y-8">
          {/* Personal Information Section */}
          <div className="space-y-6">
            <Typography variant="h3" className="text-lg font-semibold">
              {t('contract-affiliate:personalInfo.title')}
            </Typography>
            
            {/* Họ và tên */}
            <FormItem name="fullName" label={t('contract-affiliate:personalInfo.fullName')} required>
              <Input
                placeholder={t('contract-affiliate:personalInfo.placeholders.fullName')}
                className="w-full"
              />
            </FormItem>

            {/* Ngày sinh */}
            <FormItem name="dateOfBirth" label={t('contract-affiliate:personalInfo.dateOfBirth')} required>
              <DatePickerFormField
                placeholder={t('contract-affiliate:personalInfo.placeholders.dateOfBirth')}
                className="w-full"
                format="dd/MM/yyyy"
                maxDate={new Date()}
              />
            </FormItem>

            {/* Số CCCD */}
            <FormItem name="idNumber" label={t('contract-affiliate:personalInfo.idNumber')} required>
              <Input
                placeholder={t('contract-affiliate:personalInfo.placeholders.idNumber')}
                className="w-full"
              />
            </FormItem>

            {/* Ngày cấp */}
            <FormItem name="idIssuedDate" label={t('contract-affiliate:personalInfo.idIssuedDate')} required>
              <DatePickerFormField
                placeholder={t('contract-affiliate:personalInfo.placeholders.idIssuedDate')}
                className="w-full"
                format="dd/MM/yyyy"
                maxDate={new Date()}
              />
            </FormItem>

            {/* Nơi cấp */}
            <FormItem name="idIssuedPlace" label={t('contract-affiliate:personalInfo.idIssuedPlace')} required>
              <Input
                placeholder={t('contract-affiliate:personalInfo.placeholders.idIssuedPlace')}
                className="w-full"
              />
            </FormItem>

            {/* Số điện thoại */}
            <FormItem name="phone" label={t('contract-affiliate:personalInfo.phone')} required>
              <Input
                type="tel"
                placeholder={t('contract-affiliate:personalInfo.placeholders.phone')}
                className="w-full"
              />
            </FormItem>

            {/* Địa chỉ */}
            <FormItem name="address" label={t('contract-affiliate:personalInfo.address')} required>
              <Input
                placeholder={t('contract-affiliate:personalInfo.placeholders.address')}
                className="w-full"
              />
            </FormItem>

            {/* Mã số thuế (tùy chọn) */}
            <FormItem name="taxCode" label={t('contract-affiliate:personalInfo.taxCode')}>
              <Input
                placeholder={t('contract-affiliate:personalInfo.placeholders.taxCode')}
                className="w-full"
              />
            </FormItem>
          </div>

          {/* Bank Information Section */}
          <div className="space-y-6">
            <Typography variant="h3" className="text-lg font-semibold">
              {t('contract-affiliate:bankInfo.title')}
            </Typography>
            
            {/* Tên ngân hàng */}
            <FormItem name="bankName" label={t('contract-affiliate:bankInfo.bankName')} required>
              <Input
                placeholder={t('contract-affiliate:bankInfo.placeholders.bankName')}
                className="w-full"
              />
            </FormItem>

            {/* Số tài khoản */}
            <FormItem name="accountNumber" label={t('contract-affiliate:bankInfo.accountNumber')} required>
              <Input
                placeholder={t('contract-affiliate:bankInfo.placeholders.accountNumber')}
                className="w-full"
              />
            </FormItem>

            {/* Tên chủ tài khoản */}
            <FormItem name="accountHolder" label={t('contract-affiliate:bankInfo.accountHolder')} required>
              <Input
                placeholder={t('contract-affiliate:bankInfo.placeholders.accountHolder')}
                className="w-full"
              />
            </FormItem>

            {/* Chi nhánh (tùy chọn) */}
            <FormItem name="branch" label={t('contract-affiliate:bankInfo.branch')}>
              <Input
                placeholder={t('contract-affiliate:bankInfo.placeholders.branch')}
                className="w-full"
              />
            </FormItem>
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={onPrevious}
            disabled={isLoading}
          >
            {t('contract-affiliate:actions.previous')}
          </Button>

          <Button 
            type="submit"
            variant="primary"
            isLoading={isLoading || false}
          >
            {t('contract-affiliate:actions.next')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default PersonalAffiliateInfoForm;
