/**
 * Component chấp nhận đ<PERSON><PERSON><PERSON> k<PERSON><PERSON>n affiliate
 */
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Checkbox } from '@/shared/components/common';
import { ContractAffiliateStepProps } from '../types';

const TermsAcceptance: React.FC<ContractAffiliateStepProps> = ({ data, onNext, onPrevious }) => {
  const { t } = useTranslation('contract-affiliate');
  const [accepted, setAccepted] = useState(data.termsAccepted || false);
  const [showError, setShowError] = useState(false);

  const handleNext = () => {
    if (!accepted) {
      setShowError(true);
      return;
    }
    onNext({ termsAccepted: true });
  };

  const handleAcceptChange = (value: boolean) => {
    setAccepted(value);
    setShowError(false);
  };

  return (
    <div className="w-full">
      <div className="mb-6">
        <div className="max-h-96 overflow-y-auto p-4 mb-6 bg-background">
          <div className="space-y-6 text-sm leading-relaxed">
            {/* Header */}
            <div className="text-center">
              <Typography variant="h4" className="font-bold mb-2">
                {t('contract-affiliate:terms.content.header')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('contract-affiliate:terms.content.lastUpdated')}
              </Typography>
            </div>

            {/* Mở đầu */}
            <div className="space-y-3">
              <p>{t('contract-affiliate:terms.content.introduction.welcome')}</p>
              <p>{t('contract-affiliate:terms.content.introduction.purpose')}</p>
              <p>{t('contract-affiliate:terms.content.introduction.changes')}</p>
            </div>

            {/* ĐIỀU 1: GIẢI THÍCH TỪ NGỮ */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">
                {t('contract-affiliate:terms.content.article1.title')}
              </Typography>
              <div className="space-y-2">
                <p><strong>{t('contract-affiliate:terms.content.article1.definitions.affiliate')}</strong></p>
                <p><strong>{t('contract-affiliate:terms.content.article1.definitions.commission')}</strong></p>
                <p><strong>{t('contract-affiliate:terms.content.article1.definitions.referral')}</strong></p>
                <p><strong>{t('contract-affiliate:terms.content.article1.definitions.conversion')}</strong></p>
              </div>
            </div>

            {/* ĐIỀU 2: QUYỀN VÀ NGHĨA VỤ CỦA AFFILIATE */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">
                {t('contract-affiliate:terms.content.article2.title')}
              </Typography>
              <div className="space-y-3">
                <div>
                  <p className="font-medium mb-2">
                    {t('contract-affiliate:terms.content.article2.rightsTitle')}
                  </p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li>{t('contract-affiliate:terms.content.article2.rights.commission')}</li>
                    <li>{t('contract-affiliate:terms.content.article2.rights.materials')}</li>
                    <li>{t('contract-affiliate:terms.content.article2.rights.support')}</li>
                  </ul>
                </div>
                <div>
                  <p className="font-medium mb-2">
                    {t('contract-affiliate:terms.content.article2.obligationsTitle')}
                  </p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li>{t('contract-affiliate:terms.content.article2.obligations.promotion')}</li>
                    <li>{t('contract-affiliate:terms.content.article2.obligations.compliance')}</li>
                    <li>{t('contract-affiliate:terms.content.article2.obligations.reporting')}</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* ĐIỀU 3: CHÍNH SÁCH HOA HỒNG */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">
                {t('contract-affiliate:terms.content.article3.title')}
              </Typography>
              <div className="space-y-2">
                <p>{t('contract-affiliate:terms.content.article3.structure')}</p>
                <p>{t('contract-affiliate:terms.content.article3.payment')}</p>
                <p>{t('contract-affiliate:terms.content.article3.minimum')}</p>
              </div>
            </div>

            {/* Thông tin liên hệ */}
            <div>
              <Typography variant="h5" className="font-semibold mb-3">
                {t('contract-affiliate:terms.content.contactInfo.title')}
              </Typography>
              <div className="space-y-1">
                <p><strong>{t('contract-affiliate:terms.content.contactInfo.companyName')}</strong></p>
                <p>{t('contract-affiliate:terms.content.contactInfo.address')}</p>
                <p>{t('contract-affiliate:terms.content.contactInfo.hotline')}</p>
                <p>{t('contract-affiliate:terms.content.contactInfo.email')}</p>
                <p>{t('contract-affiliate:terms.content.contactInfo.website')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Checkbox chấp nhận */}
        <div className="space-y-2">
          <Checkbox
            id="terms-accept"
            label={t('contract-affiliate:terms.accept')}
            checked={accepted}
            onChange={handleAcceptChange}
            variant="filled"
            color="primary"
            size="md"
          />
          {showError && <p className="text-error text-sm">{t('contract-affiliate:terms.mustAccept')}</p>}
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrevious}>
          {t('contract-affiliate:actions.previous')}
        </Button>

        <Button variant="primary" onClick={handleNext} disabled={!accepted}>
          {t('contract-affiliate:actions.next')}
        </Button>
      </div>
    </div>
  );
};

export default TermsAcceptance;
