/**
 * <PERSON><PERSON> hợp đồng affiliate
 */
import React, { useState } from 'react';
import { Stepper } from '@/shared/components/common';
import { ContractAffiliateType, ContractAffiliateStep, ContractAffiliateData } from '../types';
import {
  ContractAffiliateTypeSelector,
  TermsAcceptance,
  BusinessAffiliateInfoForm,
  PersonalAffiliateInfoForm,
  DocumentUploadForm,
  ContractDisplay,
  ContractSigning,
  HandSignature,
  OTPVerification,
  ContractSuccess,
} from '../components';

const ContractAffiliatePage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<ContractAffiliateStep>(ContractAffiliateStep.TYPE_SELECTION);
  const [isLoading, setIsLoading] = useState(false);
  const [contractData, setContractData] = useState<ContractAffiliateData>({
    termsAccepted: false,
    isCompleted: false,
  });

  // <PERSON><PERSON><PERSON> định các bước theo loại hợp đồng affiliate
  const getStepsForType = (type: ContractAffiliateType): ContractAffiliateStep[] => {
    const commonSteps = [
      ContractAffiliateStep.TYPE_SELECTION,
      ContractAffiliateStep.TERMS_ACCEPTANCE,
      ContractAffiliateStep.INFO_FORM,
      ContractAffiliateStep.DOCUMENT_UPLOAD,
      ContractAffiliateStep.CONTRACT_DISPLAY,
    ];

    if (type === ContractAffiliateType.BUSINESS) {
      return [
        ...commonSteps,
        ContractAffiliateStep.CONTRACT_SIGNING,
        ContractAffiliateStep.COMPLETED,
      ];
    } else {
      return [
        ...commonSteps,
        ContractAffiliateStep.HAND_SIGNATURE,
        ContractAffiliateStep.OTP_VERIFICATION,
        ContractAffiliateStep.COMPLETED,
      ];
    }
  };

  const steps = contractData.type ? getStepsForType(contractData.type) : [ContractAffiliateStep.TYPE_SELECTION];
  const currentStepIndex = steps.indexOf(currentStep);

  // Tạo stepItems cho Stepper component - các bước thực tế
  const getStepTitle = (step: ContractAffiliateStep): string => {
    switch (step) {
      case ContractAffiliateStep.TYPE_SELECTION:
        return 'Chọn loại hợp đồng';
      case ContractAffiliateStep.TERMS_ACCEPTANCE:
        return 'Chấp nhận điều khoản';
      case ContractAffiliateStep.INFO_FORM:
        return 'Thông tin & Ngân hàng';
      case ContractAffiliateStep.DOCUMENT_UPLOAD:
        return 'Tải lên tài liệu';
      case ContractAffiliateStep.CONTRACT_DISPLAY:
        return 'Xem hợp đồng';
      case ContractAffiliateStep.CONTRACT_SIGNING:
        return 'Ký hợp đồng';
      case ContractAffiliateStep.HAND_SIGNATURE:
        return 'Ký tay';
      case ContractAffiliateStep.OTP_VERIFICATION:
        return 'Xác thực OTP';
      case ContractAffiliateStep.COMPLETED:
        return 'Hoàn thành';
      default:
        return 'Bước';
    }
  };

  const stepItems = steps
    .filter(step => step !== ContractAffiliateStep.COMPLETED) // Bỏ bước hoàn thành khỏi stepper
    .map((step, index) => ({
      id: (index + 1).toString(),
      title: getStepTitle(step),
      status: index < currentStepIndex ? 'completed' as const :
              index === currentStepIndex ? 'processing' as const :
              'waiting' as const,
    }));

  const handleNext = async (updatedData: Partial<ContractAffiliateData>) => {
    setIsLoading(true);

    try {
      // Cập nhật dữ liệu
      const newData = { ...contractData, ...updatedData };
      setContractData(newData);

      // Chuyển sang bước tiếp theo
      const nextStepIndex = currentStepIndex + 1;
      if (nextStepIndex < steps.length) {
        setCurrentStep(steps[nextStepIndex]);
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Error in handleNext:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrevious = () => {
    const prevStepIndex = currentStepIndex - 1;
    if (prevStepIndex >= 0) {
      setCurrentStep(steps[prevStepIndex]);
    }
  };

  const renderStepContent = () => {
    const stepProps = {
      data: contractData,
      onNext: handleNext,
      onPrevious: handlePrevious,
      isLoading,
    };

    switch (currentStep) {
      case ContractAffiliateStep.TYPE_SELECTION:
        return <ContractAffiliateTypeSelector {...stepProps} />;

      case ContractAffiliateStep.TERMS_ACCEPTANCE:
        return <TermsAcceptance {...stepProps} />;

      case ContractAffiliateStep.INFO_FORM:
        return contractData.type === ContractAffiliateType.BUSINESS ? (
          <BusinessAffiliateInfoForm {...stepProps} />
        ) : contractData.type === ContractAffiliateType.PERSONAL ? (
          <PersonalAffiliateInfoForm {...stepProps} />
        ) : (
          <ContractAffiliateTypeSelector {...stepProps} />
        );

      case ContractAffiliateStep.DOCUMENT_UPLOAD:
        return <DocumentUploadForm {...stepProps} />;

      case ContractAffiliateStep.CONTRACT_DISPLAY:
        return <ContractDisplay {...stepProps} />;

      case ContractAffiliateStep.CONTRACT_SIGNING:
        return <ContractSigning {...stepProps} />;

      case ContractAffiliateStep.HAND_SIGNATURE:
        return <HandSignature {...stepProps} />;

      case ContractAffiliateStep.OTP_VERIFICATION:
        return <OTPVerification {...stepProps} />;

      case ContractAffiliateStep.COMPLETED:
        return <ContractSuccess {...stepProps} />;

      default:
        return <ContractAffiliateTypeSelector {...stepProps} />;
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      {/* Progress Stepper */}
      {currentStep !== ContractAffiliateStep.COMPLETED && (
        <div className="mb-8">
          <Stepper steps={stepItems} showStepIcons />
        </div>
      )}

      {/* Main Content */}
      <div className="w-full">
        <div className="animate-fade-in">
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
};

export default ContractAffiliatePage;
