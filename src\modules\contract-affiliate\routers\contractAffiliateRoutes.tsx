import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';

// Lazy load pages
const ContractAffiliatePage = lazy(() => import('../pages/ContractAffiliatePage'));

/**
 * Contract Affiliate module routes
 */
const contractAffiliateRoutes: RouteObject[] = [
  {
    path: '/contract-affiliate',
    element: (
      <MainLayout title="Hợp đồng Affiliate">
        <Suspense fallback={<Loading />}>
          <ContractAffiliatePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default contractAffiliateRoutes;
