/**
 * Contract Affiliate module types
 */

export enum ContractAffiliateType {
  BUSINESS = 'business',
  PERSONAL = 'personal',
}

export enum ContractAffiliateStep {
  TYPE_SELECTION = 'type_selection',
  TERMS_ACCEPTANCE = 'terms_acceptance',
  INFO_FORM = 'info_form',
  BANK_INFO_FORM = 'bank_info_form',
  DOCUMENT_UPLOAD = 'document_upload',
  CONTRACT_DISPLAY = 'contract_display',
  CONTRACT_SIGNING = 'contract_signing',
  HAND_SIGNATURE = 'hand_signature',
  OTP_VERIFICATION = 'otp_verification',
  COMPLETED = 'completed',
}

export interface BusinessAffiliateInfo {
  companyName: string;
  taxCode: string;
  companyEmail: string;
  companyAddress: string;
  companyPhone: string;
  representative: string;
  position: string;
  bankInfo: BankInfo;
}

export interface PersonalAffiliateInfo {
  fullName: string;
  dateOfBirth: string;
  idNumber: string;
  idIssuedDate: string;
  idIssuedPlace: string;
  phone: string;
  address: string;
  taxCode?: string;
  bankInfo: BankInfo;
}

export interface BankInfo {
  bankName: string;
  accountNumber: string;
  accountHolder: string;
  branch?: string;
}

export interface DocumentUpload {
  businessLicense?: File;
  idCardFront?: File;
  idCardBack?: File;
}

export interface ContractAffiliateData {
  type?: ContractAffiliateType;
  termsAccepted: boolean;
  businessInfo?: BusinessAffiliateInfo;
  personalInfo?: PersonalAffiliateInfo;
  documentUpload?: DocumentUpload;
  contractUrl?: string;
  contractBase64?: string;
  handSignature?: string;
  otpCode?: string;
  isCompleted: boolean;
}

export interface ContractAffiliateStepProps {
  data: ContractAffiliateData;
  onNext: (updatedData: Partial<ContractAffiliateData>) => void;
  onPrevious: () => void;
  isLoading?: boolean;
}
