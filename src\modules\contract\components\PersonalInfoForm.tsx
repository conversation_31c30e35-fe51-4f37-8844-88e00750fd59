/**
 * Component form thông tin cá nhân
 */
import React, { useRef, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FieldValues } from 'react-hook-form';
import { Button, Form, FormItem, Input, DatePickerFormField } from '@/shared/components/common';
import { ValidationSchemas } from '@/shared/validation/schemas';
import { ContractStepProps, PersonalInfo } from '../types';
import { FormRef } from '@/shared/components/common/Form/Form';

const PersonalInfoForm: React.FC<ContractStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract');
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Validation schema
  const schema = useMemo(() => z.object({
    fullName: z
      .string()
      .min(1, t('contract:validation.required'))
      .min(2, t('contract:validation.minLength', { length: 2 }))
      .max(50, t('contract:validation.maxLength', { length: 50 }))
      .regex(/^[a-zA-ZÀ-ỹ\s]+$/, t('contract:validation.nameFormat')),
    dateOfBirth: ValidationSchemas.flexibleDate({ t })
      .refine((date) => {
        const birthDate = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        return age >= 18 && age <= 100;
      }, t('contract:validation.ageRange')),
    idNumber: z
      .string()
      .min(1, t('contract:validation.required'))
      .regex(/^[0-9]{9,12}$/, t('contract:validation.idNumber')),
    idIssuedDate: ValidationSchemas.flexibleDate({ t })
      .refine((date) => {
        const issuedDate = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        return issuedDate <= today;
      }, t('contract:validation.pastDate')),
    idIssuedPlace: z
      .string()
      .min(1, t('contract:validation.required'))
      .min(5, t('contract:validation.minLength', { length: 5 }))
      .max(100, t('contract:validation.maxLength', { length: 100 })),
    phone: z
      .string()
      .min(1, t('contract:validation.required'))
      .regex(/^[0-9+\-\s()]{10,15}$/, t('contract:validation.phone')),
    address: z
      .string()
      .min(1, t('contract:validation.required'))
      .min(10, t('contract:validation.minLength', { length: 10 }))
      .max(200, t('contract:validation.maxLength', { length: 200 })),
    taxCode: z
      .string()
      .optional()
      .refine((val) => !val || /^[0-9]{10,13}$/.test(val), t('contract:validation.taxCode')),
  }), [t]);

  const handleSubmit = (formData: FieldValues) => {
    onNext({ personalInfo: formData as PersonalInfo });
  };

  return (
    <div className="w-full">
      <Form
        ref={formRef}
        schema={schema}
        onSubmit={handleSubmit}
        defaultValues={data.personalInfo}
        mode="onSubmit"
        validateOnChange={false}
        validateOnBlur={true}
      >
        <div className="space-y-6">
          {/* Họ và tên */}
          <FormItem name="fullName" label={t('contract:personalInfo.fullName')} required>
            <Input
              placeholder={t('contract:personalInfo.placeholders.fullName')}
              className="w-full"
            />
          </FormItem>

          {/* Ngày sinh */}
          <FormItem name="dateOfBirth" label={t('contract:personalInfo.dateOfBirth')} required>
            <DatePickerFormField
              placeholder={t('contract:personalInfo.placeholders.dateOfBirth')}
              className="w-full"
              format="dd/MM/yyyy"
              maxDate={new Date()} // Không cho phép chọn ngày trong tương lai
            />
          </FormItem>

          {/* Số CCCD */}
          <FormItem name="idNumber" label={t('contract:personalInfo.idNumber')} required>
            <Input
              placeholder={t('contract:personalInfo.placeholders.idNumber')}
              className="w-full"
            />
          </FormItem>

          {/* Ngày cấp */}
          <FormItem name="idIssuedDate" label={t('contract:personalInfo.idIssuedDate')} required>
            <DatePickerFormField
              placeholder={t('contract:personalInfo.placeholders.idIssuedDate')}
              className="w-full"
              format="dd/MM/yyyy"
              maxDate={new Date()} // Không cho phép chọn ngày trong tương lai
            />
          </FormItem>

          {/* Nơi cấp */}
          <FormItem name="idIssuedPlace" label={t('contract:personalInfo.idIssuedPlace')} required>
            <Input
              placeholder={t('contract:personalInfo.placeholders.idIssuedPlace')}
              className="w-full"
            />
          </FormItem>

          {/* Số điện thoại */}
          <FormItem name="phone" label={t('contract:personalInfo.phone')} required>
            <Input
              type="tel"
              placeholder={t('contract:personalInfo.placeholders.phone')}
              className="w-full"
            />
          </FormItem>

          {/* Địa chỉ */}
          <FormItem name="address" label={t('contract:personalInfo.address')} required>
            <Input
              placeholder={t('contract:personalInfo.placeholders.address')}
              className="w-full"
            />
          </FormItem>

          {/* Mã số thuế (tùy chọn) */}
          <FormItem name="taxCode" label={t('contract:personalInfo.taxCode')}>
            <Input
              placeholder={t('contract:personalInfo.placeholders.taxCode')}
              className="w-full"
            />
          </FormItem>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={onPrevious}
            disabled={isLoading}
          >
            {t('contract:actions.previous')}
          </Button>

          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
          >
            {t('contract:actions.next')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default PersonalInfoForm;
