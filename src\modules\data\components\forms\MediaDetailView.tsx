import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  
} from '@/shared/components/common';

// Import types từ media service
import { MediaDto } from '@/modules/data/media/types/media.types';

interface MediaDetailViewProps {
  media: MediaDto;
  onClose: () => void;
  onDelete: (media: MediaDto) => void;
}

/**
 * Component hiển thị chi tiết Media
 */
const MediaDetailView: React.FC<MediaDetailViewProps> = ({
  media,
  onClose,
  
}) => {
  const { t } = useTranslation(['data', 'common']);
  
 

  // Get file icon based on storage key extension
  const getFileIcon = (storageKey?: string): string => {
    if (!storageKey) return 'file';

    const extension = storageKey.toLowerCase().split('.').pop() || '';

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension)) {
      return 'image';
    }

    // Video files
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension)) {
      return 'video';
    }

    // Audio files
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(extension)) {
      return 'music';
    }

    // Document files
    if (['pdf'].includes(extension)) {
      return 'file-text';
    }

    if (['doc', 'docx', 'txt', 'rtf'].includes(extension)) {
      return 'file-text';
    }

    if (['xls', 'xlsx', 'csv'].includes(extension)) {
      return 'file-text';
    }

    if (['ppt', 'pptx'].includes(extension)) {
      return 'file-text';
    }

    return 'file';
  };

  return (
    <Card className="p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Typography variant="h6" className="text-gray-900 dark:text-gray-100">
            {t('data:media.detail.title', 'Chi tiết Media')}
          </Typography>
      
        </div>

        {/* Media Preview */}
        <div className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex-shrink-0">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <Icon name={getFileIcon(media.storageKey)} size="lg" className="text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <Typography variant="subtitle1" className="font-medium text-gray-900 dark:text-gray-100 truncate">
              {media.name}
            </Typography>
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {media.description || t('data:media.detail.noDescription', 'Không có mô tả')}
            </Typography>
          </div>
        </div>

        {/* Media Information */}
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-300 mb-1">
              {t('data:media.detail.name', 'Tên file')}
            </Typography>
            <Typography variant="body2" className="text-gray-900 dark:text-gray-100">
              {media.name}
            </Typography>
          </div>

          <div>
            <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-300 mb-1">
              {t('data:media.detail.description', 'Mô tả')}
            </Typography>
            <Typography variant="body2" className="text-gray-900 dark:text-gray-100">
              {media.description || t('data:media.detail.noDescription', 'Không có mô tả')}
            </Typography>
          </div>

     

          <div>
            <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-300 mb-1">
              {t('data:media.detail.storageKey', 'Storage Key')}
            </Typography>
            <Typography variant="body2" className="text-gray-900 dark:text-gray-100 break-all">
              {media.storageKey}
            </Typography>
          </div>

          <div>
            <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-300 mb-1">
              {t('data:media.detail.status', 'Trạng thái')}
            </Typography>
            <Typography variant="body2" className="text-gray-900 dark:text-gray-100">
              {media.status}
            </Typography>
          </div>

          {media.viewUrl && (
            <div>
              <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-300 mb-1">
                {t('data:media.detail.viewUrl', 'URL xem')}
              </Typography>
              <Typography variant="body2" className="text-blue-600 dark:text-blue-400 break-all">
                <a href={media.viewUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                  {media.viewUrl}
                </a>
              </Typography>
            </div>
          )}

          {media.tags && media.tags.length > 0 && (
            <div>
              <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-300 mb-1">
                {t('data:media.detail.tags', 'Tags')}
              </Typography>
              <div className="flex flex-wrap gap-2">
                {media.tags.map((tag, index) => (
                  <span
                    key={index} 
                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

        </div>

        {/* Actions */}
        <div className="flex justify-end w-full  pt-4 border-t border-gray-200 dark:border-gray-700  "  >

          <Button variant="outline" onClick={onClose}>
            {t('common:close', 'Đóng')}
          </Button>
      
        </div>
      </div>

      
    </Card>
  );
};

export default MediaDetailView;
