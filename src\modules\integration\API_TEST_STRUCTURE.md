# ✅ Đã sửa cấu trúc API call cho test-with-config

## 🔍 **Vấn đề trước đây:**

Frontend gửi sai cấu trúc dữ liệu:
```json
{
  "recipientEmail": "<EMAIL>",
  "subject": "Test Email từ RedAI - Ki<PERSON>m tra cấu hình"
}
```

## ✅ **Cấu trúc đúng theo API specification:**

```json
{
  "emailServerConfig": {
    "serverName": "Test Configuration",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "app-password",
    "useSsl": false,
    "useStartTls": true,
    "additionalSettings": {}
  },
  "testInfo": {
    "recipientEmail": "<EMAIL>",
    "subject": "Test Email từ RedAI - Kiểm tra cấu hình"
  }
}
```

## 🛠️ **Những gì đã sửa:**

### 1. **Thêm validation email**
```typescript
// Validation email helper
const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate email format
if (!isValidEmail(testData.recipientEmail)) {
  alert(t('integration:email.invalidEmailFormat', 'Định dạng email không hợp lệ'));
  return;
}
```

### 2. **Sửa cấu trúc dữ liệu gửi API**
```typescript
const testEmailServerWithConfigData: TestEmailServerWithConfigDto = {
  emailServerConfig: {
    serverName: formData.serverName || 'Test Configuration',
    host: formData.host,
    port: formData.port || 587,
    username: formData.username,
    password: formData.password,
    useSsl: formData.useSsl !== undefined ? formData.useSsl : false,
    useStartTls: formData.useStartTls !== undefined ? formData.useStartTls : true,
    additionalSettings,
  },
  testInfo: {
    recipientEmail: testData.recipientEmail,
    subject: testData.subject,
  },
};
```

### 3. **Thêm validation trong UI**
```typescript
<Input
  type="email"
  placeholder="<EMAIL>"
  value={testData.recipientEmail}
  onChange={(e) => setTestData({ ...testData, recipientEmail: e.target.value })}
  leftIcon={<Icon name="mail" size="sm" />}
  fullWidth
  error={testData.recipientEmail && !isValidEmail(testData.recipientEmail) ? 'Định dạng email không hợp lệ' : undefined}
/>
```

### 4. **Disable button khi email không hợp lệ**
```typescript
<Button
  variant="primary"
  onClick={handleTestEmail}
  disabled={
    !testData.recipientEmail || 
    !isValidEmail(testData.recipientEmail) || 
    testEmailMutation.isPending || 
    testEmailWithConfigMutation.isPending
  }
>
```

### 5. **Thêm debug logs**
```typescript
console.log('📤 Sending test request:', testEmailServerWithConfigData);
```

## 🎯 **API Endpoint:**
- **URL**: `POST /user/integration/email-server/test-with-config`
- **Content-Type**: `application/json`

## 📋 **TypeScript Types:**

```typescript
export interface EmailServerConfigDto {
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls?: boolean;
  additionalSettings?: Record<string, any>;
}

export interface TestEmailServerWithConfigDto {
  emailServerConfig: EmailServerConfigDto;
  testInfo: TestEmailServerDto;
}

export interface TestEmailServerDto {
  recipientEmail?: string;
  subject?: string;
}
```

## 🔍 **Cách test:**

1. Mở EmailIntegrationPage
2. Điền thông tin form:
   - Server Name: "Gmail SMTP"
   - Host: "smtp.gmail.com"
   - Port: 587
   - Username: "<EMAIL>"
   - Password: "your-app-password"
   - Use SSL: false
   - Use STARTTLS: true

3. Bấm "Test kết nối"
4. Nhập email hợp lệ: "<EMAIL>"
5. Bấm "Gửi test"

## ✅ **Kết quả mong đợi:**

- ✅ Email validation hoạt động
- ✅ Button disabled khi email không hợp lệ
- ✅ API call với cấu trúc đúng
- ✅ Form không bị reset
- ✅ Debug logs hiển thị cấu trúc dữ liệu

## 🚀 **Cải thiện:**

1. **Email validation real-time** trong input
2. **Cấu trúc API call đúng** theo specification
3. **UX tốt hơn** với error states
4. **Debug logs** để theo dõi request
5. **Type safety** với TypeScript

**API call giờ đây đã đúng cấu trúc và có validation email! 🎉**
