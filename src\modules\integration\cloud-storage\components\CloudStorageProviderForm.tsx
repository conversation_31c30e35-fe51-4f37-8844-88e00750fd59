import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Input,
  Select,
  Switch,
  Textarea,
} from '@/shared/components/common';

import { NotificationUtil } from '@/shared/utils/notification';
import { cloudStorageProviderConfigurationSchema } from '../schemas';
import { CLOUD_STORAGE_PROVIDER_TYPES } from '../constants';
import { useCreateCloudStorageProvider, useUpdateCloudStorageProvider, useTestCloudStorageProviderWithConfig } from '../hooks';
import type { CloudStorageProviderConfiguration, CloudStorageProviderFormData } from '../types';

interface CloudStorageProviderFormProps {
  provider?: CloudStorageProviderConfiguration;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form tạo/chỉnh sửa Cloud Storage Provider
 */
const CloudStorageProviderForm: React.FC<CloudStorageProviderFormProps> = ({
  provider,
  onSuccess,
  onCancel
}) => {
  const { t } = useTranslation(['integration', 'common']);

  const [, setFormErrors] = useState<Partial<Record<keyof CloudStorageProviderFormData, string>>>({});

  const [formData, setFormData] = useState<CloudStorageProviderFormData>({
    providerType: provider?.providerType || 'google-drive',
    providerName: provider?.providerName || '',
    clientId: provider?.clientId || '',
    clientSecret: provider?.clientSecret || '',
    refreshToken: provider?.refreshToken || '',
    rootFolderId: provider?.rootFolderId || '',
    isActive: provider?.isActive ?? true,
    autoSync: provider?.autoSync ?? false,
    syncFolders: provider?.syncFolders ? JSON.stringify(provider.syncFolders) : '',
  });

  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const createMutation = useCreateCloudStorageProvider();
  const updateMutation = useUpdateCloudStorageProvider();
  const testMutation = useTestCloudStorageProviderWithConfig();

  const isEditing = !!provider;
  const isLoading = createMutation.isPending || updateMutation.isPending;

  // Provider options
  const providerOptions = Object.values(CLOUD_STORAGE_PROVIDER_TYPES).map(provider => ({
    value: provider.id,
    label: provider.displayName,
  }));

  const selectedProvider = CLOUD_STORAGE_PROVIDER_TYPES[formData.providerType];

  const handleInputChange = (field: keyof CloudStorageProviderFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleTestConnection = async () => {
    try {
      setIsTestingConnection(true);

      // Validate form data first
      const validatedData = cloudStorageProviderConfigurationSchema.parse(formData);

      // Parse sync folders
      if (validatedData.syncFolders) {
        try {
          JSON.parse(validatedData.syncFolders);
        } catch {
          setFormErrors({ syncFolders: t('integration:cloudStorage.validation.syncFolders.invalidJson') });
          return;
        }
      }

      const testData = {
        storageConfig: {
          providerType: validatedData.providerType,
          providerName: validatedData.providerName,
          clientId: validatedData.clientId,
          clientSecret: validatedData.clientSecret,
          refreshToken: validatedData.refreshToken,
          rootFolderId: validatedData.rootFolderId,
        },
        testInfo: {
          testFolderName: 'RedAI Test Folder',
          testFileName: 'test-connection.txt',
        },
      };

      const result = await testMutation.mutateAsync(testData);

      if (result.result?.result.success) {
        NotificationUtil.success({
          title: t('integration:cloudStorage.notifications.testConnection.success.title'),
          message: t('integration:cloudStorage.notifications.testConnection.success.message')
        });
      } else {
        NotificationUtil.error({
          title: t('integration:cloudStorage.notifications.testConnection.error.title'),
          message: t('integration:cloudStorage.notifications.testConnection.error.message')
        });
      }
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof CloudStorageProviderFormData, string>> = {};
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        zodError.issues.forEach((issue) => {
          const field = issue.path[0] as keyof CloudStorageProviderFormData;
          fieldErrors[field] = issue.message;
        });
        setFormErrors(fieldErrors);
      } else {
        // Network or other errors
        NotificationUtil.error({
          title: t('integration:cloudStorage.notifications.testConnection.error.title'),
          message: t('integration:cloudStorage.notifications.testConnection.error.message')
        });
      }
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate form data
      const validatedData = cloudStorageProviderConfigurationSchema.parse(formData);

      // Parse sync folders
      let syncFolders: string[] = [];
      if (validatedData.syncFolders) {
        try {
          syncFolders = JSON.parse(validatedData.syncFolders);
        } catch {
          setFormErrors({ syncFolders: t('integration:cloudStorage.validation.syncFolders.invalidJson') });
          return;
        }
      }

      const submitData = {
        providerType: validatedData.providerType,
        providerName: validatedData.providerName,
        clientId: validatedData.clientId,
        clientSecret: validatedData.clientSecret,
        refreshToken: validatedData.refreshToken,
        rootFolderId: validatedData.rootFolderId,
        isActive: validatedData.isActive,
        autoSync: validatedData.autoSync,
        syncFolders,
      };

      if (isEditing && provider) {
        await updateMutation.mutateAsync({ id: provider.id, data: submitData });
        NotificationUtil.success({
          title: t('integration:cloudStorage.notifications.update.success.title'),
          message: t('integration:cloudStorage.notifications.update.success.message')
        });
      } else {
        await createMutation.mutateAsync(submitData);
        NotificationUtil.success({
          title: t('integration:cloudStorage.notifications.create.success.title'),
          message: t('integration:cloudStorage.notifications.create.success.message')
        });
      }

      onSuccess?.();
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof CloudStorageProviderFormData, string>> = {};
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        zodError.issues.forEach((issue) => {
          const field = issue.path[0] as keyof CloudStorageProviderFormData;
          fieldErrors[field] = issue.message;
        });
        setFormErrors(fieldErrors);
      } else {
        // Network or other errors
        NotificationUtil.error({
          title: isEditing
            ? t('integration:cloudStorage.notifications.update.error.title')
            : t('integration:cloudStorage.notifications.create.error.title'),
          message: isEditing
            ? t('integration:cloudStorage.notifications.update.error.message')
            : t('integration:cloudStorage.notifications.create.error.message')
        });
      }
    }
  };

  return (
    <Card className="w-full">
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <Icon name="cloud" size="lg" className="text-primary" />
          <Typography variant="h3">
            {isEditing
              ? t('integration:cloudStorage.form.editTitle')
              : t('integration:cloudStorage.form.createTitle')
            }
          </Typography>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Provider Type */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              {t('integration:cloudStorage.form.providerType.label')} <span className="text-red-500">*</span>
            </label>
            <Select
              value={formData.providerType}
              onChange={(value) => handleInputChange('providerType', value as string)}
              options={providerOptions}
              placeholder={t('integration:cloudStorage.form.providerType.placeholder')}
              fullWidth
              disabled={isEditing} // Cannot change provider type when editing
            />
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.providerType.helpText')}
            </Typography>
          </div>

          {/* Provider Name */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              {t('integration:cloudStorage.form.providerName.label')} <span className="text-red-500">*</span>
            </label>
            <Input
              type="text"
              value={formData.providerName}
              onChange={(e) => handleInputChange('providerName', e.target.value)}
              placeholder={t('integration:cloudStorage.form.providerName.placeholder')}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.providerName.helpText')}
            </Typography>
          </div>

          {/* Client ID */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              {t('integration:cloudStorage.form.clientId.label')} <span className="text-red-500">*</span>
            </label>
            <Input
              type="text"
              value={formData.clientId}
              onChange={(e) => handleInputChange('clientId', e.target.value)}
              placeholder={t('integration:cloudStorage.form.clientId.placeholder')}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.clientId.helpText')}
            </Typography>
          </div>

          {/* Client Secret */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              {t('integration:cloudStorage.form.clientSecret.label')} <span className="text-red-500">*</span>
            </label>
            <Input
              type="password"
              value={formData.clientSecret}
              onChange={(e) => handleInputChange('clientSecret', e.target.value)}
              placeholder={t('integration:cloudStorage.form.clientSecret.placeholder')}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.clientSecret.helpText')}
            </Typography>
          </div>

          {/* Refresh Token */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              {t('integration:cloudStorage.form.refreshToken.label')} <span className="text-red-500">*</span>
            </label>
            <Textarea
              value={formData.refreshToken}
              onChange={(e) => handleInputChange('refreshToken', e.target.value)}
              placeholder={t('integration:cloudStorage.form.refreshToken.placeholder')}
              rows={3}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.refreshToken.helpText')}
            </Typography>
          </div>

          {/* Root Folder ID */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              {t('integration:cloudStorage.form.rootFolderId.label')}
            </label>
            <Input
              type="text"
              value={formData.rootFolderId}
              onChange={(e) => handleInputChange('rootFolderId', e.target.value)}
              placeholder={t('integration:cloudStorage.form.rootFolderId.placeholder')}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.rootFolderId.helpText')}
            </Typography>
          </div>

          {/* Sync Folders */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              {t('integration:cloudStorage.form.syncFolders.label')}
            </label>
            <Textarea
              value={formData.syncFolders}
              onChange={(e) => handleInputChange('syncFolders', e.target.value)}
              placeholder={t('integration:cloudStorage.form.syncFolders.placeholder')}
              rows={3}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:cloudStorage.form.syncFolders.helpText')}
            </Typography>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <Typography variant="h6">
              {t('common:settings')}
            </Typography>

            {/* Is Active */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                {t('integration:cloudStorage.form.isActive.label')}
              </label>
              <Switch
                checked={formData.isActive}
                onChange={(checked) => handleInputChange('isActive', checked)}
              />
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:cloudStorage.form.isActive.helpText')}
              </Typography>
            </div>

            {/* Auto Sync */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                {t('integration:cloudStorage.form.autoSync.label')}
              </label>
              <Switch
                checked={formData.autoSync}
                onChange={(checked) => handleInputChange('autoSync', checked)}
              />
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:cloudStorage.form.autoSync.helpText')}
              </Typography>
            </div>
          </div>

          {/* Provider Info */}
          {selectedProvider && (
            <Card className="p-4 bg-muted/50">
              <div className="flex items-start gap-3">
                <Icon name="info" size="sm" className="text-primary mt-1" />
                <div className="space-y-2">
                  <Typography variant="subtitle2">
                    {selectedProvider.displayName}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {selectedProvider.description}
                  </Typography>
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Typography variant="caption" className="text-muted-foreground">
                      <strong>{t('integration:cloudStorage.details.storageQuota')}:</strong> {selectedProvider.maxFileSize}
                    </Typography>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedProvider.supportedFeatures.map(feature => (
                      <span
                        key={feature}
                        className="px-2 py-1 bg-primary/10 text-primary text-xs rounded"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Actions */}
          <div className="flex gap-3 pt-6 border-t">
            {/* Test Connection */}
            <Button
              type="button"
              variant="outline"
              onClick={handleTestConnection}
              disabled={isTestingConnection || isLoading}
              className="flex-1"
            >
              {isTestingConnection ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2" />
                  {t('common:testing')}
                </>
              ) : (
                <>
                  <Icon name="zap" size="sm" className="mr-2" />
                  {t('integration:cloudStorage.form.testConnection')}
                </>
              )}
            </Button>

            {/* Cancel */}
            {onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1"
              >
                {t('common:cancel')}
              </Button>
            )}

            {/* Submit */}
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2" />
                  {isEditing ? t('common:updating') : t('common:creating')}
                </>
              ) : (
                <>
                  <Icon name="save" size="sm" className="mr-2" />
                  {isEditing ? t('common:update') : t('common:create')}
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default CloudStorageProviderForm;
