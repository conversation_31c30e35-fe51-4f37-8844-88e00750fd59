import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ConfirmDeleteModal } from '@/shared/components/common';
import { SortOrder, TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import ActionMenu from '@/shared/components/common/ActionMenu';

import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import {
  useShippingProviderConfigurations,
  useCreateShippingProviderConfiguration,
  useUpdateShippingProviderConfiguration,
  useDeleteShippingProviderConfiguration,
} from '../shipping/hooks';
import {
  ShippingProviderConfiguration,
  ShippingProviderQueryParams,
  CreateShippingProviderDto,
  UpdateShippingProviderDto,
  ShippingProviderType,
} from '../shipping/types';

import ShippingProviderForm from '../shipping/components/ShippingProviderForm';
import ProviderTypeSelector from '../shipping/components/ProviderTypeSelector';
import GHTKProviderForm from '../shipping/components/GHTKProviderForm';
import GHNProviderForm from '../shipping/components/GHNProviderForm';

/**
 * Trang quản lý tích hợp vận chuyển
 */
const ShippingIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);
  const [shippingProviders, setShippingProviders] = useState<ShippingProviderConfiguration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const [providerToDelete, setProviderToDelete] = useState<ShippingProviderConfiguration | null>(null);
  const [providerToEdit, setProviderToEdit] = useState<ShippingProviderConfiguration | null>(null);
  const [providerToView, setProviderToView] = useState<ShippingProviderConfiguration | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State cho việc chọn provider type
  const [, setSelectedProviderType] = useState<ShippingProviderType | null>(null);

  // Sử dụng hook animation cho form tạo mới
  const { isVisible: isCreateFormVisible, hideForm: hideCreateForm } = useSlideForm();

  // Sử dụng hook animation cho provider selector
  const { isVisible: isProviderSelectorVisible, showForm: showProviderSelectorForm, hideForm: hideProviderSelectorForm } = useSlideForm();

  // Sử dụng hook animation cho GHTK form
  const { isVisible: isGHTKFormVisible, showForm: showGHTKForm, hideForm: hideGHTKForm } = useSlideForm();

  // Sử dụng hook animation cho GHN form
  const { isVisible: isGHNFormVisible, showForm: showGHNForm, hideForm: hideGHNForm } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const { isVisible: isEditFormVisible, showForm: showEditForm, hideForm: hideEditForm } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const { isVisible: isViewFormVisible, showForm: showViewForm, hideForm: hideViewForm } = useSlideForm();

  // Helper function to close all forms
  const closeAllForms = useCallback(() => {
    hideCreateForm();
    hideEditForm();
    hideViewForm();
    hideProviderSelectorForm();
    hideGHTKForm();
    hideGHNForm();
    setProviderToEdit(null);
    setProviderToView(null);
    setSelectedProviderType(null);
  }, [hideCreateForm, hideEditForm, hideViewForm, hideProviderSelectorForm, hideGHTKForm, hideGHNForm]);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((provider: ShippingProviderConfiguration) => {
    console.log('🔍 Delete button clicked for provider:', provider);
    closeAllForms(); // Đóng tất cả form trước khi hiển thị confirm dialog
    setProviderToDelete(provider);
    setShowDeleteConfirm(true);
  }, [closeAllForms]);

  // Xử lý hiển thị form chỉnh sửa
  const handleShowEditForm = useCallback((provider: ShippingProviderConfiguration) => {
    console.log('🔍 Edit button clicked for provider:', provider);
    closeAllForms(); // Đóng tất cả form trước khi mở form edit
    setProviderToEdit(provider);
    showEditForm();
  }, [showEditForm, closeAllForms]);

  // Xử lý hiển thị form xem chi tiết
  const handleShowViewForm = useCallback((provider: ShippingProviderConfiguration) => {
    console.log('🔍 View button clicked for provider:', provider);
    closeAllForms(); // Đóng tất cả form trước khi mở form view
    setProviderToView(provider);
    showViewForm();
  }, [showViewForm, closeAllForms]);

  // Xử lý hiển thị provider selector
  const handleShowProviderSelector = useCallback(() => {
    console.log('🔍 Show provider selector');
    closeAllForms(); // Đóng tất cả form trước khi mở selector
    showProviderSelectorForm();
  }, [showProviderSelectorForm, closeAllForms]);

  // Xử lý chọn provider type
  const handleSelectProviderType = useCallback((type: ShippingProviderType) => {
    console.log('🔍 Selected provider type:', type);
    setSelectedProviderType(type);
    closeAllForms(); // Đóng tất cả form trước khi mở form mới

    // Hiển thị form tương ứng
    if (type === 'GHTK') {
      showGHTKForm();
    } else if (type === 'GHN') {
      showGHNForm();
    }
  }, [closeAllForms, showGHTKForm, showGHNForm]);

  // Xử lý quay lại provider selector
  const handleBackToSelector = useCallback(() => {
    console.log('🔍 Back to provider selector');
    closeAllForms(); // Đóng tất cả form trước khi mở selector
    showProviderSelectorForm();
  }, [closeAllForms, showProviderSelectorForm]);

  // Xử lý đóng provider selector
  const handleCloseProviderSelector = useCallback(() => {
    closeAllForms();
  }, [closeAllForms]);

  // Xử lý đóng GHTK form
  const handleCloseGHTKForm = useCallback(() => {
    closeAllForms();
  }, [closeAllForms]);

  // Xử lý đóng GHN form
  const handleCloseGHNForm = useCallback(() => {
    closeAllForms();
  }, [closeAllForms]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<ShippingProviderConfiguration>[]>(() => {
    const allColumns: TableColumn<ShippingProviderConfiguration>[] = [
      {
        key: 'name',
        title: t('integration:shipping.list.columns.providerName'),
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'type',
        title: t('integration:shipping.list.columns.providerType'),
        dataIndex: 'type',
        width: '20%',
        sortable: true,
        render: (value: unknown) => {
          const type = value as string;
          const typeLabels: Record<string, string> = {
            'GHN': 'Giao Hàng Nhanh',
            'GHTK': 'Giao Hàng Tiết Kiệm',
            'viettel-post': 'Viettel Post',
            'vnpost': 'VN Post',
          };
          return (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {typeLabels[type] || type}
            </span>
          );
        },
      },
      {
        key: 'shopId',
        title: t('integration:shipping.list.columns.shopId'),
        dataIndex: 'shopId',
        width: '20%',
        sortable: true,
        render: (_: unknown, record: unknown) => {
          // Extract shopId from config based on type
          const typedRecord = record as ShippingProviderConfiguration;
          // Only GHN has shopId, GHTK doesn't
          const shopId = typedRecord?.ghnConfig?.shopId || '-';
          return <span>{shopId === '***ENCRYPTED***' ? 'Đã cấu hình' : shopId}</span>;
        },
      },

      {
        key: 'isActive',
        title: t('integration:shipping.list.columns.status'),
        dataIndex: 'isActive',
        width: '15%',
        render: (_, record: unknown) => {
          // Check if config has token to determine status
          const typedRecord = record as ShippingProviderConfiguration;
          const config = typedRecord?.ghnConfig || typedRecord?.ghtkConfig;
          const hasToken = config?.hasToken || false;
          return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              hasToken ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {hasToken ? 'Hoạt động' : 'Chưa cấu hình'}
            </span>
          );
        },
      },
      {
        key: 'actions',
        title: t('integration:shipping.list.columns.actions'),
        render: (_: unknown, record: ShippingProviderConfiguration) => {
          const actionItems = [
            {
              id: 'edit',
              label: t('integration:shipping.actions.edit'),
              icon: 'edit' as const,
              onClick: () => {
                console.log('🔍 Menu Edit clicked for:', record);
                handleShowEditForm(record);
              },
            },
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye' as const,
              onClick: () => {
                console.log('🔍 Menu View clicked for:', record);
                handleShowViewForm(record);
              },
            },
            {
              id: 'delete',
              label: t('integration:shipping.actions.delete'),
              icon: 'trash' as const,
              onClick: () => {
                console.log('🔍 Menu Delete clicked for:', record);
                handleShowDeleteConfirm(record);
              },
            },
          ];


          return (
            <ActionMenu
              items={actionItems}
              showAllInMenu={true}
              placement="left"
              preferRight={true}
              preferTop={true}
              menuTooltip="Thao tác"
            />
          );
        },
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowEditForm, handleShowViewForm]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<ShippingProviderConfiguration, ShippingProviderQueryParams>({
    columns,
    filterOptions: [
      {
        id: 'all',
        label: t('common:all'),
        icon: 'list',
        value: 'all',
      },
      {
        id: 'ghn',
        label: 'Giao Hàng Nhanh',
        icon: 'truck',
        value: 'ghn',
      },
      {
        id: 'ghtk',
        label: 'Giao Hàng Tiết Kiệm',
        icon: 'truck',
        value: 'ghtk',
      },
      {
        id: 'viettel-post',
        label: 'Viettel Post',
        icon: 'truck',
        value: 'viettel-post',
      },
      {
        id: 'vnpost',
        label: 'VN Post',
        icon: 'truck',
        value: 'vnpost',
      },
      {
        id: 'active',
        label: t('common:active'),
        icon: 'check',
        value: true,
      },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'x',
        value: false,
      },
    ],
    showDateFilter: false,
    createQueryParams: params => {
      const queryParams: ShippingProviderQueryParams = {
        page: params.page,
        limit: params.pageSize,
      };

      // Chỉ thêm search nếu có giá trị
      if (params.searchTerm) {
        queryParams.search = params.searchTerm;
      }

      // Chỉ thêm providerType nếu có giá trị hợp lệ
      if (params.filterValue !== 'all' && typeof params.filterValue === 'string') {
        queryParams.providerType = params.filterValue as ShippingProviderType;
      }

      // Chỉ thêm isActive nếu có giá trị boolean
      if (typeof params.filterValue === 'boolean') {
        queryParams.isActive = params.filterValue;
      }

      return queryParams;
    },
  });

  const dataTable = useDataTable(dataTableConfig);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Tạo query params cho API
  const queryParams = useMemo<ShippingProviderQueryParams>(() => {
    const params: ShippingProviderQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
    };

    // Chỉ thêm search nếu có giá trị
    if (dataTable.tableData.searchTerm) {
      params.search = dataTable.tableData.searchTerm;
    }

    // Thêm filter params dựa trên selectedValue
    const filterValue = dataTable.filter.selectedValue;
    if (filterValue !== 'all') {
      if (typeof filterValue === 'string') {
        params.providerType = filterValue as ShippingProviderType;
      } else if (typeof filterValue === 'boolean') {
        params.isActive = filterValue;
      }
    }

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
    dataTable.filter.selectedValue,
  ]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearSort, handleClearAll } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      'ghn': 'Giao Hàng Nhanh',
      'ghtk': 'Giao Hàng Tiết Kiệm',
      'viettel-post': 'Viettel Post',
      'vnpost': 'VN Post',
      'true': t('common:active'),
      'false': t('common:inactive'),
    },
    t,
  });

  // Hooks để gọi API
  const {
    data: shippingProvidersData,
    isLoading: isLoadingProviders,
    error: providersError,
  } = useShippingProviderConfigurations(queryParams);

  const createProviderMutation = useCreateShippingProviderConfiguration();
  const updateProviderMutation = useUpdateShippingProviderConfiguration();
  const deleteProviderMutation = useDeleteShippingProviderConfiguration();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    console.log('🔍 ShippingProviders API Response:', shippingProvidersData);
    console.log('🔍 API Error:', providersError);
    console.log('🔍 Is Loading:', isLoadingProviders);

    if (shippingProvidersData) {
      // API trả về trực tiếp object có items và meta, không có wrapper
      if ('items' in shippingProvidersData && Array.isArray(shippingProvidersData.items)) {
        setShippingProviders(shippingProvidersData.items);
        setTotalItems(shippingProvidersData.result?.meta?.totalItems || shippingProvidersData.items.length);
      } else if (Array.isArray(shippingProvidersData)) {
        console.log('🔍 Response is array:', shippingProvidersData);
        setShippingProviders(shippingProvidersData);
        setTotalItems(shippingProvidersData.length);
      } else {
        console.log('🔍 Unknown response structure:', shippingProvidersData);
        setShippingProviders([]);
        setTotalItems(0);
      }
    } else if (providersError) {
      console.log('🔍 API error:', providersError);
      setShippingProviders([]);
      setTotalItems(0);
    }

    setIsLoading(isLoadingProviders);
  }, [shippingProvidersData, providersError, isLoadingProviders]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback(
    (term: string) => {
      dataTable.tableData.handleSearch(term);
    },
    [dataTable.tableData]
  );

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback(
    (column: string | null, order: SortOrder) => {
      dataTable.tableData.handleSortChange(column, order);
    },
    [dataTable.tableData]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProviderToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!providerToDelete) return;

    try {
      await deleteProviderMutation.mutateAsync(providerToDelete.id);
      setShowDeleteConfirm(false);
      setProviderToDelete(null);
    } catch (error) {
      console.error('Error deleting shipping provider:', error);
    }
  }, [providerToDelete, deleteProviderMutation]);

  // Xử lý submit form tạo mới
  const handleSubmitCreateProvider = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsSubmitting(true);
        const createData: CreateShippingProviderDto = values as unknown as CreateShippingProviderDto;

        await createProviderMutation.mutateAsync(createData);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating shipping provider:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createProviderMutation, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditProvider = useCallback(
    async (values: Record<string, unknown>) => {
      if (!providerToEdit) return;

      try {
        setIsSubmitting(true);
        console.log('🔍 Edit form values:', values);
        console.log('🔍 Provider to edit:', providerToEdit);

        const updateData: UpdateShippingProviderDto = values as unknown as UpdateShippingProviderDto;
        console.log('🔍 Update data:', updateData);

        const result = await updateProviderMutation.mutateAsync({
          id: providerToEdit.id,
          data: updateData,
        });

        console.log('🔍 Update result:', result);
        console.log('✅ Provider updated successfully');

        hideEditForm();
        setProviderToEdit(null);
      } catch (error) {
        console.error('❌ Error updating shipping provider:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [providerToEdit, updateProviderMutation, hideEditForm]
  );

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    closeAllForms();
  }, [closeAllForms]);

  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    closeAllForms();
  }, [closeAllForms]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<ShippingProviderConfiguration>[]>(() => {
    if (visibleColumns.length === 0) {
      setVisibleColumns([
        { id: 'all', label: t('common:all'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={handleShowProviderSelector}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          {/* ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho provider selector */}
        <SlideInForm isVisible={isProviderSelectorVisible}>
          <ProviderTypeSelector
            onSelectProvider={handleSelectProviderType}
            onCancel={handleCloseProviderSelector}
          />
        </SlideInForm>

        {/* SlideInForm cho GHTK form */}
        <SlideInForm isVisible={isGHTKFormVisible}>
          <GHTKProviderForm
            onSubmit={handleSubmitCreateProvider}
            onCancel={handleCloseGHTKForm}
            onBack={handleBackToSelector}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho GHN form */}
        <SlideInForm isVisible={isGHNFormVisible}>
          <GHNProviderForm
            onSubmit={handleSubmitCreateProvider}
            onCancel={handleCloseGHNForm}
            onBack={handleBackToSelector}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho form tạo mới (legacy - có thể xóa sau) */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <ShippingProviderForm
            onSubmit={handleSubmitCreateProvider}
            onCancel={hideCreateForm}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {providerToEdit && providerToEdit.type === 'GHTK' && (
            <GHTKProviderForm
              initialData={providerToEdit}
              onSubmit={handleSubmitEditProvider}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
              readOnly={false}
            />
          )}
          {providerToEdit && providerToEdit.type === 'GHN' && (
            <GHNProviderForm
              initialData={providerToEdit}
              onSubmit={handleSubmitEditProvider}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
              readOnly={false}
            />
          )}
          {providerToEdit && !['GHTK', 'GHN'].includes(providerToEdit.type) && (
            <ShippingProviderForm
              initialData={providerToEdit}
              onSubmit={handleSubmitEditProvider}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
              readOnly={false}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {providerToView && providerToView.type === 'GHTK' && (
            <GHTKProviderForm
              initialData={providerToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              readOnly={true}
            />
          )}
          {providerToView && providerToView.type === 'GHN' && (
            <GHNProviderForm
              initialData={providerToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              readOnly={true}
            />
          )}
          {providerToView && !['GHTK', 'GHN'].includes(providerToView.type) && (
            <ShippingProviderForm
              initialData={providerToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              readOnly={true}
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<ShippingProviderConfiguration>
            columns={filteredColumns}
            data={shippingProviders}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('integration:shipping.confirmations.deleteTitle')}
        message={t('integration:shipping.confirmations.delete')}
        itemName={providerToDelete?.name || ''}
      />
    </div>
  );
};

export default ShippingIntegrationPage;
