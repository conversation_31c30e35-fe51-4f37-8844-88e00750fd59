import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Icon,
  Input,
} from '@/shared/components/common';
import { ShippingProviderConfiguration } from '../types';
import { ghnConfigSchema } from '../schemas';

interface GHNProviderFormProps {
  initialData?: ShippingProviderConfiguration | null;
  onSubmit?: (values: Record<string, unknown>) => void | Promise<void>;
  onCancel?: () => void;
  onBack?: () => void;
  isSubmitting?: boolean;
  readOnly?: boolean;
}

interface GHNFormData {
  name: string;
  token: string;
  shopId: string;
  timeout: number;
  isTestMode: boolean;
}

// Define the API data structure for GHN configuration
interface GHNApiData {
  name: string;
  type?: 'GHN';
  ghnConfig: {
    token: string;
    shopId: string;
    timeout: number;
  };
}

/**
 * Form component cho việc cấu hình <PERSON>HN
 */
const GHNProviderForm: React.FC<GHNProviderFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  onBack,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isLoading = isSubmitting;
  const [formData, setFormData] = useState<GHNFormData>({
    name: '',
    token: '',
    shopId: '',
    timeout: 30000,
    isTestMode: true,
  });

  const isEditing = !!initialData;

  // Load provider data when editing
  useEffect(() => {
    if (initialData && initialData.type === 'GHN') {
      console.log('🔍 GHN Form initialData:', initialData);
      
      const mappedData: GHNFormData = {
        name: initialData.name || '',
        token: readOnly && initialData.ghnConfig?.hasToken
          ? '***ENCRYPTED***'
          : '',
        shopId: readOnly && initialData.ghnConfig?.hasShopId
          ? '***ENCRYPTED***'
          : '',
        timeout: initialData.ghnConfig?.timeout || 30000,
        isTestMode: initialData.ghnConfig?.isTestMode || true,
      };

      console.log('🔍 GHN Mapped form data:', mappedData);
      setFormData(mappedData);
    }
  }, [initialData, readOnly]);

  // Handle form field changes
  const handleFieldChange = (
    field: keyof GHNFormData,
    value: string | number | boolean
  ) => {
    setFormData((prev: GHNFormData) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (readOnly) return;

    try {
      console.log('🔍 GHN Form data before submission:', formData);

      // Validate using Zod schema
      const validationData = {
        token: formData.token.trim(),
        shopId: formData.shopId.trim(),
        timeout: formData.timeout,
        isTestMode: formData.isTestMode,
      };

      // Only validate token and shopId if not editing or if they are provided
      if (!isEditing || (formData.token.trim() && formData.shopId.trim())) {
        const validatedConfig = ghnConfigSchema.parse(validationData);
        console.log('🔍 GHN Validated config:', validatedConfig);
      }

      // Validate name
      if (!formData.name.trim()) {
        setErrors({ name: 'Tên hiển thị là bắt buộc' });
        return;
      }

      // Prepare data for API according to the structure you provided
      const apiData: GHNApiData = {
        name: formData.name.trim(),
        ghnConfig: {
          token: formData.token.trim(),
          shopId: formData.shopId.trim(),
          timeout: formData.timeout,
        },
      };

      // Only include type for create (not edit)
      if (!isEditing) {
        apiData.type = 'GHN';
      }

      console.log('🔍 GHN API data to submit:', apiData);

      // Call parent onSubmit handler
      await onSubmit?.(apiData);
      console.log('✅ GHN Form submitted successfully');

      // Auto close form on success
      if (onCancel) {
        onCancel();
      }
    } catch (error: unknown) {
      console.error('❌ GHN Form submission error:', error);
      if (error && typeof error === 'object' && 'errors' in error) {
        // Handle Zod validation errors
        const fieldErrors: Record<string, string> = {};
        const zodError = error as { errors: Array<{ path: (string | number)[]; message: string }> };
        zodError.errors.forEach(err => {
          if (err.path && err.path.length > 0) {
            fieldErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(fieldErrors);
        console.log('🔍 GHN Validation errors:', fieldErrors);
      }
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="w-full p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          {onBack && !readOnly && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="mr-2"
            >
              <Icon name="arrow-left" size="sm" />
            </Button>
          )}
          <Icon name="truck" size="lg" className="text-blue-600" />
          <Typography variant="h3">
            {readOnly
              ? 'Xem chi tiết cấu hình GHN'
              : isEditing
              ? 'Chỉnh sửa cấu hình GHN'
              : 'Cấu hình GHN - Giao Hàng Nhanh'}
          </Typography>
        </div>

        {/* Form */}
        <div className="w-full space-y-6">
          {/* Provider Name */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Tên hiển thị <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.name}
              onChange={e => handleFieldChange('name', e.target.value)}
              placeholder="Ví dụ: Cấu hình GHN chính"
              disabled={readOnly}
              fullWidth
              error={errors.name}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          {/* Token */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Token <span className="text-red-500">*</span>
            </label>
            <Input
              type={readOnly ? 'text' : 'password'}
              value={formData.token}
              onChange={e => handleFieldChange('token', e.target.value)}
              placeholder={readOnly ? "Dữ liệu đã được mã hóa" : isEditing ? "Nhập Token mới (để trống nếu không thay đổi)" : "Nhập Token từ GHN"}
              disabled={readOnly}
              fullWidth
              error={errors.token}
            />
            {errors.token && (
              <p className="text-sm text-red-500">{errors.token}</p>
            )}
          </div>

          {/* Shop ID */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Shop ID <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.shopId}
              onChange={e => handleFieldChange('shopId', e.target.value)}
              placeholder={readOnly ? "Dữ liệu đã được mã hóa" : isEditing ? "Nhập Shop ID mới (để trống nếu không thay đổi)" : "Nhập Shop ID từ GHN"}
              disabled={readOnly}
              fullWidth
              error={errors.shopId}
            />
            {errors.shopId && (
              <p className="text-sm text-red-500">{errors.shopId}</p>
            )}
          </div>

          {/* Timeout */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-foreground">
              Timeout (ms)
            </label>
            <Input
              type="number"
              value={formData.timeout.toString()}
              onChange={e => handleFieldChange('timeout', parseInt(e.target.value) || 30000)}
              placeholder="30000"
              disabled={readOnly}
              fullWidth
            />
          </div>



          {/* Actions */}
          <div className="w-full pt-6 border-t border-border">
            <div className="flex flex-col sm:flex-row gap-3 justify-end">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
                  {readOnly ? t('common:close') : t('common:cancel')}
                </Button>
              )}

              {!readOnly && (
                <Button
                  type="button"
                  variant="primary"
                  isLoading={isLoading}
                  onClick={handleSubmit}
                >
                  {isEditing ? t('common:save') : 'Tạo cấu hình GHN'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GHNProviderForm;
