import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Icon,
  Card,
} from '@/shared/components/common';
import type { IconName } from '@/shared/components/common/Icon/Icon';
import { ShippingProviderType } from '../types';

interface ProviderTypeSelectorProps {
  onSelectProvider: (type: ShippingProviderType) => void;
  onCancel: () => void;
}

/**
 * Component để chọn loại nhà vận chuyển (GHTK hoặc GHN)
 */
const ProviderTypeSelector: React.FC<ProviderTypeSelectorProps> = ({
  onSelectProvider,
  onCancel,
}) => {
  const { t } = useTranslation(['integration', 'common']);

  const providerOptions = [
    {
      type: 'GHTK' as ShippingProviderType,
      name: 'GHTK',
      fullName: 'Giao Hàng Tiết Kiệm',
      description: 'Dịch vụ giao hàng nhanh chóng với chi phí tiết kiệm',
      icon: 'truck',
      color: 'bg-green-50 border-green-200 hover:bg-green-100',
      iconColor: 'text-green-600',
    },
    {
      type: 'GHN' as ShippingProviderType,
      name: 'GHN',
      fullName: 'Giao Hàng Nhanh',
      description: 'Mạng lưới giao hàng rộng khắp toàn quốc',
      icon: 'truck',
      color: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
      iconColor: 'text-blue-600',
    },
  ];

  return (
    <div className="w-full bg-background text-foreground">
      <div className="w-full p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <Icon name="plus" size="lg" className="text-primary" />
          <Typography variant="h3">
            {t('integration:shipping.selectProviderType', 'Chọn nhà vận chuyển')}
          </Typography>
        </div>

        {/* Description */}
        <div className="mb-8">
          <Typography variant="body1" className="text-muted-foreground">
            Chọn nhà vận chuyển bạn muốn tích hợp vào hệ thống
          </Typography>
        </div>

        {/* Provider Cards */}
        <div className="grid grid-cols-2 gap-6 mb-8">
          {providerOptions.map((provider) => (
            <Card
              key={provider.type}
              className={`cursor-pointer transition-all duration-200 ${provider.color} hover:shadow-md border-2`}
              onClick={() => onSelectProvider(provider.type)}
            >
              <div className="p-8 text-center space-y-6">
                <div className="flex justify-center">
                  <div className={`p-6 rounded-full bg-white shadow-lg`}>
                    <Icon
                      name={provider.icon as IconName}
                      size="xl"
                      className={provider.iconColor}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Typography variant="h3" className="font-bold">
                    {provider.name}
                  </Typography>
                  <Typography variant="h5" className="text-muted-foreground font-medium">
                    {provider.fullName}
                  </Typography>
                  <Typography variant="body1" className="text-sm leading-relaxed">
                    {provider.description}
                  </Typography>
                </div>

                <Button
                  variant="outline"
                  size="md"
                  className="mt-6 px-8 py-2 font-medium"
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectProvider(provider.type);
                  }}
                >
                  Chọn {provider.name}
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Actions */}
        <div className="w-full pt-6 border-t border-border">
          <div className="flex justify-end">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t('common:cancel')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProviderTypeSelector;
