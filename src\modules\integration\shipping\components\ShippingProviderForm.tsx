import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  Select,
  Switch,
  Textarea,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import {
  ShippingProviderConfiguration,
  ShippingProviderType,
  LegacyCreateShippingProviderDto,
} from '../types';

// Legacy form data interface for backward compatibility
interface LegacyShippingProviderFormData extends Omit<LegacyCreateShippingProviderDto, 'settings'> {
  settings?: string; // JSON string for form handling
}
import { shippingProviderConfigurationSchema } from '../schemas';

interface ShippingProviderFormProps {
  initialData?: ShippingProviderConfiguration | null;
  onSubmit?: (values: Record<string, unknown>) => void | Promise<void>;
  onCancel?: () => void;
  isSubmitting?: boolean;
  readOnly?: boolean;
}

/**
 * Form component cho việc thêm/sửa nhà vận chuyển
 */
const ShippingProviderForm: React.FC<ShippingProviderFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const { formRef, setFormErrors } = useFormErrors<LegacyShippingProviderFormData>();

  const isLoading = isSubmitting;
  const [formData, setFormData] = useState<LegacyShippingProviderFormData>({
    providerType: 'GHN',
    providerName: '',
    apiKey: '',
    apiSecret: '',
    shopId: '',
    clientId: '',
    isActive: true,
    isDefault: false,
    settings: '',
  });

  const isEditing = !!initialData;

  // Provider type options
  const providerOptions = [
    { value: 'GHN', label: 'GHN - Giao Hàng Nhanh' },
    { value: 'GHTK', label: 'GHTK - Giao Hàng Tiết Kiệm' },
    { value: 'viettel-post', label: 'Viettel Post' },
    { value: 'vnpost', label: 'VNPost' },
  ];

  // Load provider data when editing
  useEffect(() => {
    if (initialData) {
      console.log('🔍 Form initialData:', initialData);
      console.log('🔍 initialData.type:', initialData.type);

      // Map from ShippingProviderConfiguration to ShippingProviderFormData
      const mappedData = {
        providerType: initialData.type || 'GHN',
        providerName: initialData.name || '',
        // Chỉ hiển thị encrypted values khi readOnly, khi edit thì để trống để user nhập lại
        apiKey: readOnly && (initialData.ghnConfig?.hasToken || initialData.ghtkConfig?.hasToken)
          ? '***ENCRYPTED***'
          : '',
        apiSecret: '',
        // Chỉ hiển thị encrypted shopId khi readOnly, khi edit thì để trống để user nhập lại
        shopId: readOnly && initialData.ghnConfig?.hasShopId
          ? '***ENCRYPTED***'
          : '',
        clientId: '',
        isActive: true, // Default value since API doesn't return this
        isDefault: false, // Default value since API doesn't return this
        settings: '',
      };

      console.log('🔍 Mapped form data:', mappedData);
      setFormData(mappedData);
    }
  }, [initialData, readOnly]);

  // Handle form field changes
  const handleFieldChange = (
    field: keyof LegacyShippingProviderFormData,
    value: string | boolean | ShippingProviderType
  ) => {
    setFormData((prev: LegacyShippingProviderFormData) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (readOnly) return;

    try {
      console.log('🔍 Form data before validation:', formData);

      // Validate form data
      const validatedData = shippingProviderConfigurationSchema.parse(formData);
      console.log('🔍 Validated data:', validatedData);

      // Prepare data for API
      const apiData = {
        providerType: validatedData.providerType,
        providerName: validatedData.providerName,
        apiKey: validatedData.apiKey,
        apiSecret: validatedData.apiSecret,
        shopId: validatedData.shopId,
        clientId: validatedData.clientId,
        isActive: validatedData.isActive,
        isDefault: validatedData.isDefault,
        settings: validatedData.settings ? JSON.parse(validatedData.settings) : undefined,
      };

      console.log('🔍 API data to submit:', apiData);

      // Call parent onSubmit handler
      await onSubmit?.(apiData);
      console.log('✅ Form submitted successfully');
    } catch (error: unknown) {
      console.error('❌ Form submission error:', error);
      if (error && typeof error === 'object' && 'errors' in error) {
        // Handle Zod validation errors
        const fieldErrors: Record<string, string> = {};
        const zodError = error as { errors: Array<{ path: (string | number)[]; message: string }> };
        zodError.errors.forEach(err => {
          if (err.path && err.path.length > 0) {
            fieldErrors[err.path[0] as string] = err.message;
          }
        });
        setFormErrors(fieldErrors);
        console.log('🔍 Validation errors:', fieldErrors);
      }
    }
  };

  // Get provider-specific fields
  const getProviderSpecificFields = () => {
    switch (formData.providerType) {
      case 'GHN':
        return (
          <FormItem label="Shop ID" name="shopId" required>
            <Input
              value={formData.shopId || ''}
              onChange={e => handleFieldChange('shopId', e.target.value)}
              placeholder={readOnly ? "Dữ liệu đã được mã hóa" : isEditing ? "Nhập Shop ID mới (để trống nếu không thay đổi)" : "Nhập Shop ID từ GHN"}
              disabled={readOnly}
              fullWidth
            />
          </FormItem>
        );
      case 'GHTK':
        return null;
      case 'viettel-post':
        return (
          <div className="w-full space-y-6">
            <FormItem label="Client ID" name="clientId">
              <Input
                value={formData.clientId || ''}
                onChange={e => handleFieldChange('clientId', e.target.value)}
                placeholder="Nhập Client ID từ Viettel Post"
                disabled={readOnly}
                fullWidth
              />
            </FormItem>
            <FormItem label="API Secret" name="apiSecret">
              <Input
                type={readOnly ? 'text' : 'password'}
                value={formData.apiSecret || ''}
                onChange={e => handleFieldChange('apiSecret', e.target.value)}
                placeholder="Nhập API Secret"
                disabled={readOnly}
                fullWidth
              />
            </FormItem>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="w-full p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <Icon name="truck" size="lg" className="text-primary" />
          <Typography variant="h3">
            {readOnly
              ? t('integration:shipping.viewProvider', 'Xem chi tiết nhà vận chuyển')
              : isEditing
              ? t('integration:shipping.editProvider', 'Chỉnh sửa nhà vận chuyển')
              : t('integration:shipping.addProvider', 'Thêm nhà vận chuyển')}
          </Typography>
        </div>

        {/* Form */}
        <Form ref={formRef as never} className="w-full space-y-6" onSubmit={handleSubmit}>
          {/* Provider Type */}
          <FormItem label="Loại nhà vận chuyển" name="providerType" required>
            <Select
              value={formData.providerType}
              onChange={value => handleFieldChange('providerType', value as ShippingProviderType)}
              options={providerOptions}
              placeholder="Chọn nhà vận chuyển"
              disabled={isEditing || readOnly} // Không cho phép thay đổi type khi edit hoặc readOnly
            />
          </FormItem>

          {/* Provider Name */}
          <FormItem label="Tên hiển thị" name="providerName" required>
            <Input
              value={formData.providerName || ''}
              onChange={e => handleFieldChange('providerName', e.target.value)}
              placeholder="Nhập tên hiển thị cho nhà vận chuyển"
              disabled={readOnly}
              fullWidth
            />
          </FormItem>

          {/* API Key */}
          <FormItem label="API Key" name="apiKey" required>
            <Input
              type={readOnly ? 'text' : 'password'}
              value={formData.apiKey || ''}
              onChange={e => handleFieldChange('apiKey', e.target.value)}
              placeholder={readOnly ? "Dữ liệu đã được mã hóa" : isEditing ? "Nhập API Key mới (để trống nếu không thay đổi)" : "Nhập API Key"}
              disabled={readOnly}
              fullWidth
            />
          </FormItem>

          {/* Provider-specific fields */}
          {getProviderSpecificFields()}

          {/* Settings */}
          <FormItem label="Cấu hình nâng cao (JSON)" name="settings">
            <Textarea
              value={formData.settings || ''}
              onChange={e => handleFieldChange('settings', e.target.value)}
              placeholder='{"webhookUrl": "https://example.com/webhook", "enableWebhook": true}'
              rows={4}
              disabled={readOnly}
              fullWidth
            />
          </FormItem>

          {/* Status toggles */}
          <div className="w-full space-y-4">
            <FormItem label="Trạng thái hoạt động" name="isActive">
              <Switch
                checked={formData.isActive}
                onChange={checked => handleFieldChange('isActive', checked)}
                disabled={readOnly}
              />
            </FormItem>

            <FormItem label="Đặt làm mặc định" name="isDefault">
              <Switch
                checked={formData.isDefault}
                onChange={checked => handleFieldChange('isDefault', checked)}
                disabled={readOnly}
              />
            </FormItem>
          </div>

          {/* Actions */}
          <div className="w-full pt-6 border-t border-border">
            <div className="flex flex-col sm:flex-row gap-3 justify-end">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
                  {readOnly ? t('common:close') : t('common:cancel')}
                </Button>
              )}

              {!readOnly && (
                <Button
                  type="button"
                  variant="primary"
                  isLoading={isLoading}
                  onClick={handleSubmit}
                >
                  {isEditing ? t('common:save') : t('integration:shipping.create', 'Tạo')}
                </Button>
              )}
            </div>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default ShippingProviderForm;
