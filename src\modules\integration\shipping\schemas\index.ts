import { z } from 'zod';

/**
 * Shipping Integration Schemas
 */

export const shippingAddressSchema = z.object({
  name: z.string().min(1, 'integration:shipping.validation.name.required'),
  phone: z.string().min(1, 'integration:shipping.validation.phone.required'),
  address: z.string().min(1, 'integration:shipping.validation.address.required'),
  wardCode: z.string().optional(),
  wardName: z.string().optional(),
  districtId: z.number().optional(),
  districtName: z.string().optional(),
  provinceId: z.number().optional(),
  provinceName: z.string().optional(),
  country: z.string().optional(),
});

export const shippingProviderSettingsSchema = z.object({
  token: z.string().optional(),
  shopId: z.number().optional(),
  partnerId: z.string().optional(),
  defaultFromAddress: shippingAddressSchema.optional(),
  defaultPackageType: z.string().optional(),
  defaultServiceType: z.string().optional(),
  webhookUrl: z.string().url().optional(),
  enableWebhook: z.boolean().optional(),
});

export const shippingProviderConfigurationSchema = z.object({
  providerType: z.enum(['GHN', 'GHTK', 'viettel-post', 'vnpost'], {
    errorMap: () => ({ message: 'integration:shipping.validation.providerType.invalid' }),
  }),

  providerName: z
    .string()
    .min(1, 'integration:shipping.validation.providerName.required')
    .max(100, 'integration:shipping.validation.providerName.maxLength'),

  apiKey: z
    .string()
    .min(1, 'integration:shipping.validation.apiKey.required')
    .max(255, 'integration:shipping.validation.apiKey.maxLength'),

  apiSecret: z
    .string()
    .max(255, 'integration:shipping.validation.apiSecret.maxLength')
    .optional(),

  shopId: z
    .string()
    .max(100, 'integration:shipping.validation.shopId.maxLength')
    .optional(),

  clientId: z
    .string()
    .max(255, 'integration:shipping.validation.clientId.maxLength')
    .optional(),

  isActive: z.boolean().default(true),

  isDefault: z.boolean().default(false),

  settings: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      try {
        JSON.parse(val);
        return true;
      } catch {
        return false;
      }
    }, 'integration:shipping.validation.settings.invalidJson'),

  userId: z.number().optional(),
});

export const updateShippingProviderConfigurationSchema = shippingProviderConfigurationSchema.partial();

export const testShippingProviderSchema = z.object({
  fromAddress: shippingAddressSchema.optional(),
  toAddress: shippingAddressSchema.optional(),
  weight: z.number().min(0.1).max(50).optional(),
  serviceType: z.string().optional(),
});

export const shippingProviderQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  providerType: z.enum(['GHN', 'GHTK', 'viettel-post', 'vnpost']).optional(),
  isActive: z.boolean().optional(),
  userId: z.number().optional(),
});

export const shippingOrderSchema = z.object({
  fromAddress: shippingAddressSchema,
  toAddress: shippingAddressSchema,
  weight: z.number().min(0.1, 'integration:shipping.validation.weight.min'),
  dimensions: z.object({
    length: z.number().min(1),
    width: z.number().min(1),
    height: z.number().min(1),
  }).optional(),
  serviceType: z.string().min(1, 'integration:shipping.validation.serviceType.required'),
  codAmount: z.number().min(0).optional(),
  note: z.string().optional(),
  requiredNote: z.string().optional(),
});

export const shippingRateRequestSchema = z.object({
  fromAddress: shippingAddressSchema,
  toAddress: shippingAddressSchema,
  weight: z.number().min(0.1),
  dimensions: z.object({
    length: z.number().min(1),
    width: z.number().min(1),
    height: z.number().min(1),
  }).optional(),
  serviceType: z.string().optional(),
});

/**
 * New API Format Schemas
 */
export const ghnConfigSchema = z.object({
  token: z.string().min(1, 'Token là bắt buộc'),
  shopId: z.string().min(1, 'Shop ID là bắt buộc'),
  timeout: z.number().min(1000).default(30000),
  isTestMode: z.boolean().default(true),
});

export const ghtkConfigSchema = z.object({
  token: z.string().min(1, 'Token là bắt buộc'),
  timeout: z.number().min(1000).default(30000),
  isTestMode: z.boolean().default(true),
});

export const createShippingProviderSchema = z.object({
  name: z.string().min(1, 'Tên hiển thị là bắt buộc').max(100, 'Tên hiển thị không được quá 100 ký tự'),
  type: z.enum(['GHN', 'GHTK', 'viettel-post', 'vnpost'], {
    errorMap: () => ({ message: 'Loại nhà vận chuyển không hợp lệ' }),
  }),
  ghnConfig: ghnConfigSchema.optional(),
  ghtkConfig: ghtkConfigSchema.optional(),
}).refine((data) => {
  // Validate that the correct config is provided based on type
  if (data.type === 'GHN' && !data.ghnConfig) {
    return false;
  }
  if (data.type === 'GHTK' && !data.ghtkConfig) {
    return false;
  }
  return true;
}, {
  message: 'Cấu hình không phù hợp với loại nhà vận chuyển',
});

export const updateShippingProviderSchema = z.object({
  name: z.string().min(1, 'Tên hiển thị là bắt buộc').max(100, 'Tên hiển thị không được quá 100 ký tự').optional(),
  ghnConfig: ghnConfigSchema.partial().optional(),
  ghtkConfig: ghtkConfigSchema.partial().optional(),
});

export type ShippingProviderConfigurationFormData = z.infer<typeof shippingProviderConfigurationSchema>;
export type UpdateShippingProviderConfigurationFormData = z.infer<typeof updateShippingProviderConfigurationSchema>;
export type TestShippingProviderFormData = z.infer<typeof testShippingProviderSchema>;
export type ShippingProviderQueryFormData = z.infer<typeof shippingProviderQuerySchema>;
export type ShippingOrderFormData = z.infer<typeof shippingOrderSchema>;
export type ShippingRateRequestFormData = z.infer<typeof shippingRateRequestSchema>;

// New API Format Types
export type CreateShippingProviderFormData = z.infer<typeof createShippingProviderSchema>;
export type UpdateShippingProviderFormData = z.infer<typeof updateShippingProviderSchema>;
export type GHNConfigFormData = z.infer<typeof ghnConfigSchema>;
export type GHTKConfigFormData = z.infer<typeof ghtkConfigSchema>;
