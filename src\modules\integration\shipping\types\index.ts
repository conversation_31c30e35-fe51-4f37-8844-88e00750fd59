/**
 * Shipping Integration Types
 */

import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Shipping Provider Types
 */
export type ShippingProviderType = 'GHN' | 'GHTK' | 'viettel-post' | 'vnpost';

/**
 * GHN Configuration
 */
export interface GHNConfig {
  baseUrl: string;
  timeout: number;
  isTestMode: boolean;
  token: string;
  shopId: string;
  hasToken: boolean;
  hasShopId: boolean;
}

/**
 * GHTK Configuration
 */
export interface GHTKConfig {
  baseUrl: string;
  timeout: number;
  isTestMode: boolean;
  token: string;
  hasToken: boolean;
}

/**
 * Shipping Provider Configuration (API Response)
 */
export interface ShippingProviderConfiguration {
  id: string;
  name: string;
  type: ShippingProviderType;
  ghnConfig?: GHNConfig;
  ghtkConfig?: GHTKConfig;
  createdAt: number;
  isDefault?: boolean;
  isActive?: boolean;
}

/**
 * Legacy Shipping Provider Configuration (for backward compatibility)
 */
export interface LegacyShippingProviderConfiguration {
  id: number;
  userId: number;
  providerType: ShippingProviderType;
  providerName: string;
  apiKey: string;
  apiSecret?: string;
  shopId?: string;
  clientId?: string;
  isActive: boolean;
  isDefault: boolean;
  settings?: ShippingProviderSettings;
  createdAt: string;
  updatedAt: string;
}

/**
 * Provider-specific settings
 */
export interface ShippingProviderSettings {
  // GHN settings
  token?: string;
  shopId?: number;
  
  // GHTK settings
  partnerId?: string;
  
  // Common settings
  defaultFromAddress?: ShippingAddress;
  defaultPackageType?: string;
  defaultServiceType?: string;
  webhookUrl?: string;
  enableWebhook?: boolean;
}

/**
 * Shipping Address
 */
export interface ShippingAddress {
  name: string;
  phone: string;
  address: string;
  wardCode?: string;
  wardName?: string;
  districtId?: number;
  districtName?: string;
  provinceId?: number;
  provinceName?: string;
  country?: string;
}

/**
 * Create Shipping Provider DTO (New API format)
 */
export interface CreateShippingProviderDto {
  name: string;
  type: ShippingProviderType;
  ghnConfig?: {
    token: string;
    shopId: string;
    timeout: number;
    isTestMode: boolean;
  };
  ghtkConfig?: {
    token: string;
    timeout: number;
    isTestMode: boolean;
  };
}

/**
 * Legacy Create Shipping Provider DTO (for backward compatibility)
 */
export interface LegacyCreateShippingProviderDto {
  providerType: ShippingProviderType;
  providerName: string;
  apiKey: string;
  apiSecret?: string;
  shopId?: string;
  clientId?: string;
  isActive: boolean;
  isDefault: boolean;
  settings?: ShippingProviderSettings;
}

/**
 * Update Shipping Provider DTO (New API format)
 */
export interface UpdateShippingProviderDto {
  name?: string;
  ghnConfig?: {
    token?: string;
    shopId?: string;
    timeout?: number;
    isTestMode?: boolean;
  };
  ghtkConfig?: {
    token?: string;
    timeout?: number;
    isTestMode?: boolean;
  };
}

/**
 * Legacy Update Shipping Provider DTO (for backward compatibility)
 */
export interface LegacyUpdateShippingProviderDto {
  providerName?: string;
  apiKey?: string;
  apiSecret?: string;
  shopId?: string;
  clientId?: string;
  isActive?: boolean;
  isDefault?: boolean;
  settings?: ShippingProviderSettings;
}

/**
 * Test Shipping Provider DTO
 */
export interface TestShippingProviderDto {
  fromAddress?: ShippingAddress;
  toAddress?: ShippingAddress;
  weight?: number;
  serviceType?: string;
}

/**
 * Shipping Provider Config for testing
 */
export interface ShippingProviderConfigDto {
  providerType: ShippingProviderType;
  providerName: string;
  apiKey: string;
  apiSecret?: string;
  shopId?: string;
  clientId?: string;
  settings?: ShippingProviderSettings;
}

/**
 * Test Shipping Provider with config DTO
 */
export interface TestShippingProviderWithConfigDto {
  providerConfig: ShippingProviderConfigDto;
  testInfo: TestShippingProviderDto;
}

/**
 * Query parameters for Shipping Providers
 */
export interface ShippingProviderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  providerType?: ShippingProviderType;
  isActive?: boolean;
}

/**
 * Shipping Provider test result
 */
export interface ShippingProviderTestResult {
  success: boolean;
  message: string;
  details?: {
    error?: string;
    statusCode?: number;
    responseTime?: number;
    serviceInfo?: {
      serviceName: string;
      estimatedDelivery: string;
      shippingFee: number;
    };
  };
}

/**
 * Form data for Shipping Provider
 */
export interface ShippingProviderFormData extends Omit<CreateShippingProviderDto, 'settings'> {
  settings?: string; // JSON string for form handling
}

/**
 * Shipping Service
 */
export interface ShippingService {
  serviceId: string;
  serviceName: string;
  serviceType: string;
  description?: string;
  estimatedDelivery?: string;
  fee?: number;
  isAvailable: boolean;
}

/**
 * Shipping Rate
 */
export interface ShippingRate {
  serviceId: string;
  serviceName: string;
  fee: number;
  estimatedDelivery: string;
  currency: string;
}

/**
 * Shipping Order
 */
export interface ShippingOrder {
  id: string;
  trackingNumber: string;
  status: ShippingOrderStatus;
  fromAddress: ShippingAddress;
  toAddress: ShippingAddress;
  weight: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  serviceType: string;
  fee: number;
  estimatedDelivery: string;
  actualDelivery?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Shipping Order Status
 */
export type ShippingOrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'picked_up'
  | 'in_transit'
  | 'delivered'
  | 'cancelled'
  | 'returned';

/**
 * Province/District/Ward data
 */
export interface Province {
  id: number;
  name: string;
  code: string;
}

export interface District {
  id: number;
  name: string;
  provinceId: number;
  code?: string;
}

export interface Ward {
  id: number;
  name: string;
  districtId: number;
  code?: string;
}
