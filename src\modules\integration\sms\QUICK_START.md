# 🚀 SMS Integration - Quick Start Guide

## 📍 **T<PERSON><PERSON> cập SMS Integration**

### **Cách 1: Từ Navigation**
```
1. Vào trang /integrations
2. Click vào card "Quản lý SMS" 
3. Sẽ chuyển đến /integrations/sms
```

### **Cách 2: Direct URL**
```
T<PERSON>y cập trực tiếp: /integrations/sms
```

## ⚡ **Sử dụng nhanh**

### **1. Thêm Provider mới**
```
1. Click button "Thêm nhà cung cấp"
2. Chọn loại provider (Twilio, AWS SNS, Viettel, etc.)
3. Đ<PERSON><PERSON>n thông tin credentials
4. Click "Tạo cấu hình"
```

### **2. Test Provider**
```
1. Click button "Test" trên provider card
2. Nhập số điện thoại test
3. Nhập nội dung tin nhắn
4. Click "Gửi test"
```

### **3. <PERSON><PERSON><PERSON><PERSON> lý Provider**
```
- Edit: Click icon edit để chỉnh sửa
- Delete: Click icon trash để xóa
- Toggle: Click button để bật/tắt provider
- Set Default: Tick checkbox "Đặt làm mặc định"
```

## 🏭 **Provider Setup**

### **Twilio**
```typescript
Credentials cần thiết:
- Account SID: ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
- Auth Token: your-auth-token
- From Number: +********** (optional)
```

### **AWS SNS**
```typescript
Credentials cần thiết:
- Access Key ID: AKIAIOSFODNN7EXAMPLE
- Secret Access Key: your-secret-key
- Region: us-east-1
```

### **Viettel SMS**
```typescript
Credentials cần thiết:
- API Key: your-viettel-api-key
- Username: your-username
- From Name: REDAI (optional)
```

### **VNPT SMS**
```typescript
Credentials cần thiết:
- Username: your-username
- Password: your-password
- API Key: your-vnpt-api-key
```

### **FPT SMS**
```typescript
Credentials cần thiết:
- API Key: your-fpt-api-key
- Username: your-username
- Password: your-password
```

### **Custom API**
```typescript
Credentials cần thiết:
- Endpoint: https://api.example.com/sms
- API Key: your-api-key
- Headers: {"Authorization": "Bearer token"} (optional)
```

## 🔍 **Search & Filter**

### **Search**
```
- Tìm kiếm theo tên provider
- Real-time search khi gõ
```

### **Filter**
```
- Filter theo Status: Active, Inactive, Error, Testing, Pending
- Filter theo Type: Twilio, AWS SNS, Viettel, VNPT, FPT, Custom
```

## 📱 **Responsive Layout**

```
Mobile (xs):    1 column
Tablet (sm):    1-2 columns  
Desktop (md):   2 columns
Large (lg):     3 columns
Extra Large:    3 columns
```

## 🎨 **Status Indicators**

```
🟢 Active:   Provider đang hoạt động
⚪ Inactive: Provider tạm dừng
🔴 Error:    Provider có lỗi
🟡 Testing:  Provider đang test
⚫ Pending:  Provider chờ xử lý
⭐ Default:  Provider mặc định
```

## 🧪 **Demo & Testing**

### **Demo Component**
```typescript
import SmsIntegrationDemo from '@/modules/integration/sms/demo/SmsIntegrationDemo';

// Sử dụng trong development để test UI
<SmsIntegrationDemo />
```

### **Test Module**
```typescript
import '@/modules/integration/sms/test-sms-integration';
// Sẽ log kết quả test vào console
```

## 🔧 **Development**

### **Import Components**
```typescript
import {
  SmsProviderCard,
  SmsProviderForm,
  SmsProviderList,
  SmsIntegrationPage
} from '@/modules/integration/sms';
```

### **Import Hooks**
```typescript
import {
  useSmsProviders,
  useSmsIntegration
} from '@/modules/integration/sms';

// Usage
const { data: providers } = useSmsProviders();
const { createProvider } = useSmsIntegration();
```

### **Import Types**
```typescript
import type {
  SmsProviderConfig,
  SmsProviderFormData,
  SmsProviderType
} from '@/modules/integration/sms';
```

## 🌐 **Internationalization**

```typescript
// Sử dụng translation keys
const { t } = useTranslation(['integration']);

t('integration:sms.title')           // "Tích hợp SMS"
t('integration:sms.addProvider')     // "Thêm nhà cung cấp"
t('integration:sms.status.active')   // "Hoạt động"
```

## 🚨 **Troubleshooting**

### **Provider không kết nối được**
```
1. Kiểm tra credentials có đúng không
2. Kiểm tra network connectivity
3. Xem logs trong browser console
4. Test với provider khác
```

### **Form validation lỗi**
```
1. Kiểm tra required fields
2. Kiểm tra format credentials
3. Xem error messages trong form
```

### **SMS test không gửi được**
```
1. Kiểm tra số điện thoại format
2. Kiểm tra provider status = active
3. Kiểm tra rate limits
4. Kiểm tra balance (với paid providers)
```

## 📞 **Support**

Nếu gặp vấn đề, hãy:
1. Kiểm tra browser console logs
2. Kiểm tra network tab trong DevTools  
3. Xem file README.md để biết thêm chi tiết
4. Liên hệ development team

---

**SMS Integration Module sẵn sàng sử dụng! 🎉**
