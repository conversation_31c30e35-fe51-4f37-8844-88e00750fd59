# SMS Integration Module

Hệ thống tích hợp và quản lý các nhà cung cấp dịch vụ SMS cho ứng dụng RedAI.

## 🚀 Tính năng

- ✅ Hỗ trợ nhiều nhà cung cấp SMS (Twilio, AWS SNS, Viettel, VNPT, FPT, Custom API)
- ✅ Giao diện quản lý provider thân thiện
- ✅ Form cấu hình động theo từng loại provider
- ✅ Test kết nối và gửi SMS thử nghiệm
- ✅ Quản lý trạng thái provider (active/inactive/error)
- ✅ Validation dữ liệu với Zod schema
- ✅ TypeScript support đầy đủ
- ✅ Responsive design
- ✅ Internationalization (i18n)

## 📁 Cấu trúc thư mục

```
src/modules/integration/sms/
├── components/              # React components
│   ├── SmsProviderCard.tsx     # Card hiển thị provider
│   ├── SmsProviderForm.tsx     # Form cấu hình provider
│   └── SmsProviderList.tsx     # Danh sách providers
├── pages/                   # Pages
│   └── SmsIntegrationPage.tsx  # Trang chính
├── hooks/                   # Custom hooks
│   ├── useSmsIntegration.ts    # Mutations hook
│   └── useSmsProviders.ts      # Query hook
├── services/                # API services
│   └── sms-integration.service.ts
├── types/                   # TypeScript types
│   └── sms-integration.types.ts
├── schemas/                 # Zod validation schemas
│   └── sms-integration.schema.ts
├── constants/               # Constants và configs
│   └── sms-providers.constants.ts
├── demo/                    # Demo components
│   └── SmsIntegrationDemo.tsx
├── test-sms-integration.ts  # Test file
├── README.md               # Documentation
└── index.ts                # Module exports
```

## 🔧 Cài đặt và sử dụng

### 1. Import module

```typescript
import {
  SmsIntegrationPage,
  useSmsProviders,
  useSmsIntegration,
  SMS_PROVIDER_TYPES,
} from '@/modules/integration/sms';
```

### 2. Sử dụng trong routing

```typescript
// Đã được tích hợp vào integrationRoutes.tsx
{
  path: '/integrations/sms',
  element: <SmsIntegrationPage />
}
```

### 3. Sử dụng hooks

```typescript
// Lấy danh sách providers
const { data: providers, isLoading } = useSmsProviders({
  page: 1,
  limit: 10,
  status: 'active'
});

// Thực hiện mutations
const {
  createProvider,
  updateProvider,
  deleteProvider,
  testProvider,
  sendTestSms
} = useSmsIntegration();
```

## 🏭 Nhà cung cấp hỗ trợ

### 1. Twilio
- **Credentials**: Account SID, Auth Token
- **Features**: International SMS, delivery reports
- **Documentation**: https://www.twilio.com/docs/sms

### 2. AWS SNS
- **Credentials**: Access Key ID, Secret Access Key, Region
- **Features**: Cloud-based SMS, global coverage
- **Documentation**: https://docs.aws.amazon.com/sns/

### 3. Viettel SMS
- **Credentials**: API Key, Username
- **Features**: Vietnam local SMS, brand name support
- **Documentation**: https://sms.viettel.vn/api-docs

### 4. VNPT SMS
- **Credentials**: Username, Password, API Key
- **Features**: Vietnam local SMS, competitive pricing
- **Documentation**: https://sms.vnpt.vn/api-docs

### 5. FPT SMS
- **Credentials**: API Key, Username, Password
- **Features**: Vietnam local SMS, reliable delivery
- **Documentation**: https://sms.fpt.vn/api-docs

### 6. Custom API
- **Credentials**: Endpoint, API Key, Headers (optional)
- **Features**: Flexible integration với bất kỳ SMS API nào

## 📝 API Endpoints

```typescript
// Provider management
GET    /api/v1/integrations/sms/providers
POST   /api/v1/integrations/sms/providers
GET    /api/v1/integrations/sms/providers/:id
PUT    /api/v1/integrations/sms/providers/:id
DELETE /api/v1/integrations/sms/providers/:id

// Testing
POST   /api/v1/integrations/sms/providers/:id/test
POST   /api/v1/integrations/sms/test-send

// Status management
PATCH  /api/v1/integrations/sms/providers/:id/status
```

## 🎨 Components

### SmsProviderCard
Card component hiển thị thông tin provider với các action buttons.

```typescript
<SmsProviderCard
  provider={provider}
  onEdit={handleEdit}
  onDelete={handleDelete}
  onTest={handleTest}
  onToggleStatus={handleToggleStatus}
/>
```

### SmsProviderForm
Form component để tạo/chỉnh sửa provider với validation.

```typescript
<SmsProviderForm
  initialData={provider}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  mode="edit"
/>
```

### SmsProviderList
Component hiển thị danh sách providers với search và filter.

```typescript
<SmsProviderList
  providers={providers}
  onAddProvider={handleAdd}
  onEditProvider={handleEdit}
  onDeleteProvider={handleDelete}
/>
```

## 🧪 Testing

### Chạy test module
```bash
# Import test file để kiểm tra
import './test-sms-integration';
```

### Demo page
Truy cập `/demo/sms-integration` để xem demo các components.

## 🌐 Internationalization

Module hỗ trợ đa ngôn ngữ với namespace `integration:sms`:

```json
{
  "integration": {
    "sms": {
      "title": "Tích hợp SMS",
      "description": "Quản lý tích hợp các nhà cung cấp dịch vụ SMS",
      "addProvider": "Thêm nhà cung cấp",
      // ... more translations
    }
  }
}
```

## 🔒 Security

- Credentials được mã hóa khi lưu trữ
- API keys không hiển thị trong UI (masked)
- Validation nghiêm ngặt cho tất cả input
- Rate limiting cho test SMS

## 📊 Monitoring

- Test kết nối định kỳ
- Tracking delivery rates
- Error logging và alerting
- Performance metrics

## 🚀 Deployment

Module đã được tích hợp vào hệ thống routing chính và sẵn sàng sử dụng tại:
- **URL**: `/integrations/sms`
- **Navigation**: Từ trang `/integrations` → SMS Integration card

## 🤝 Contributing

1. Tuân thủ coding standards của dự án
2. Sử dụng TypeScript strict mode
3. Viết tests cho các tính năng mới
4. Cập nhật documentation khi cần thiết

## 📞 Support

Liên hệ team development để được hỗ trợ khi gặp vấn đề với SMS Integration module.
