import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  ResponsiveGrid,
  Badge,
} from '@/shared/components/common';
import { SmsProviderConfig } from '../types';
import { SMS_PROVIDER_TEMPLATES } from '../constants';
import SmsProviderCard from '../components/SmsProviderCard';
import SmsProviderForm from '../components/SmsProviderForm';

/**
 * Demo component để test SMS Integration
 */
const SmsIntegrationDemo: React.FC = () => {
  const [showForm, setShowForm] = useState(false);

  // Mock data cho demo
  const mockProviders: SmsProviderConfig[] = [
    {
      id: '1',
      name: 'twilio-production',
      type: 'twilio',
      displayName: 'Twilio Production',
      description: 'Twilio SMS service for production environment',
      status: 'active',
      credentials: {
        accountSid: 'ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
        authToken: '••••••••••••••••••••••••••••••••',
      },
      settings: {
        fromNumber: '+**********',
        rateLimits: {
          perSecond: 1,
          perMinute: 60,
          perHour: 3600,
          perDay: 86400,
          perMonth: 2592000,
        },
        retryConfig: {
          maxRetries: 3,
          retryDelay: 5,
          backoffMultiplier: 2,
        },
        enableDeliveryReports: true,
        enableOptOut: true,
        timezone: 'Asia/Ho_Chi_Minh',
      },
      isDefault: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      lastTestedAt: '2024-01-01T12:00:00Z',
      testResult: {
        success: true,
        message: 'Connection successful',
        responseTime: 250,
        timestamp: '2024-01-01T12:00:00Z',
      },
    },
    {
      id: '2',
      name: 'viettel-sms',
      type: 'viettel',
      displayName: 'Viettel SMS',
      description: 'Viettel SMS service for Vietnamese market',
      status: 'inactive',
      credentials: {
        apiKey: 'viettel-api-key',
        username: 'viettel-user',
      },
      settings: {
        fromName: 'REDAI',
        rateLimits: {
          perSecond: 2,
          perMinute: 100,
          perHour: 6000,
          perDay: 144000,
          perMonth: 4320000,
        },
        retryConfig: {
          maxRetries: 3,
          retryDelay: 3,
          backoffMultiplier: 1.5,
        },
        enableDeliveryReports: true,
        enableOptOut: true,
        timezone: 'Asia/Ho_Chi_Minh',
      },
      isDefault: false,
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z',
    },
    {
      id: '3',
      name: 'aws-sns-backup',
      type: 'aws-sns',
      displayName: 'AWS SNS Backup',
      description: 'AWS SNS as backup SMS service',
      status: 'error',
      credentials: {
        accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
        secretAccessKey: '••••••••••••••••••••••••••••••••',
        region: 'us-east-1',
      },
      settings: {
        rateLimits: {
          perSecond: 1,
          perMinute: 20,
          perHour: 1200,
          perDay: 28800,
          perMonth: 864000,
        },
        retryConfig: {
          maxRetries: 3,
          retryDelay: 5,
          backoffMultiplier: 2,
        },
        enableDeliveryReports: true,
        enableOptOut: true,
        timezone: 'Asia/Ho_Chi_Minh',
      },
      isDefault: false,
      createdAt: '2024-01-03T00:00:00Z',
      updatedAt: '2024-01-03T00:00:00Z',
      lastTestedAt: '2024-01-03T10:00:00Z',
      testResult: {
        success: false,
        message: 'Invalid credentials',
        responseTime: 1000,
        timestamp: '2024-01-03T10:00:00Z',
        details: {
          statusCode: 401,
          error: 'Unauthorized',
        },
      },
    },
  ];

  const handleProviderAction = (action: string, provider: SmsProviderConfig) => {
    console.log(`${action} provider:`, provider);
    alert(`Demo: ${action} provider "${provider.displayName}"`);
  };

  const handleFormSubmit = (data: unknown) => {
    console.log('Form submitted:', data);
    alert('Demo: Form submitted successfully!');
    setShowForm(false);
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      {/* Header */}
      <div className="mb-8">
        <Typography variant="h3" className="font-bold mb-2">
          🧪 SMS Integration Demo
        </Typography>
        <Typography variant="body1" className="text-muted-foreground mb-4">
          Demo trang để test các component SMS Integration
        </Typography>
        
        <div className="flex items-center space-x-4">
          <Badge variant="success">
            {mockProviders.filter(p => p.status === 'active').length} Active
          </Badge>
          <Badge variant="primary">
            {mockProviders.filter(p => p.isDefault).length} Default
          </Badge>
          <Badge variant="danger">
            {mockProviders.filter(p => p.status === 'error').length} Error
          </Badge>
        </div>
      </div>

      {/* Actions */}
      <div className="mb-6">
        <Button 
          onClick={() => setShowForm(!showForm)}
          variant="primary"
        >
          {showForm ? 'Ẩn Form' : 'Hiện Form Demo'}
        </Button>
      </div>

      {/* Form Demo */}
      {showForm && (
        <Card className="p-6 mb-8">
          <Typography variant="h5" className="mb-4">
            📝 SMS Provider Form Demo
          </Typography>
          <SmsProviderForm
            onSubmit={handleFormSubmit}
            onCancel={() => setShowForm(false)}
            mode="create"
          />
        </Card>
      )}

      {/* Provider Templates Info */}
      <Card className="p-6 mb-8">
        <Typography variant="h5" className="mb-4">
          🏭 Supported SMS Providers
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6 }} gap={4}>
          {Object.values(SMS_PROVIDER_TEMPLATES).map((template) => (
            <div key={template.type} className="text-center">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Typography variant="body2" className="font-bold">
                  {template.displayName.charAt(0)}
                </Typography>
              </div>
              <Typography variant="body2" className="font-medium">
                {template.displayName}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {template.type}
              </Typography>
            </div>
          ))}
        </ResponsiveGrid>
      </Card>

      {/* Provider Cards Demo */}
      <div className="mb-8">
        <Typography variant="h5" className="mb-4">
          📱 SMS Provider Cards Demo
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 2, lg: 3 }} gap={6}>
          {mockProviders.map((provider) => (
            <SmsProviderCard
              key={provider.id}
              provider={provider}
              onEdit={(p) => handleProviderAction('Edit', p)}
              onDelete={(p) => handleProviderAction('Delete', p)}
              onTest={(p) => handleProviderAction('Test', p)}
              onToggleStatus={(p) => handleProviderAction('Toggle Status', p)}
            />
          ))}
        </ResponsiveGrid>
      </div>

      {/* Info */}
      <Card className="p-6 bg-muted/30">
        <Typography variant="h6" className="mb-2">
          ℹ️ Demo Information
        </Typography>
        <Typography variant="body2" className="text-muted-foreground">
          Đây là trang demo để test các component SMS Integration. Tất cả dữ liệu đều là mock data.
          Các action sẽ hiển thị alert thay vì thực hiện API call thật.
        </Typography>
      </Card>
    </div>
  );
};

export default SmsIntegrationDemo;
