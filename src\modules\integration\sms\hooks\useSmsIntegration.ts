import { useMutation, useQueryClient } from '@tanstack/react-query';
import { SmsProviderFormData, SmsTestRequest } from '../types';
import { smsIntegrationService } from '../services';
import { SMS_INTEGRATION_QUERY_KEYS } from '../constants';

/**
 * Hook for SMS Integration mutations
 */
export const useSmsIntegration = () => {
  const queryClient = useQueryClient();

  // Create provider mutation
  const createProvider = useMutation({
    mutationFn: (data: SmsProviderFormData) =>
      smsIntegrationService.createProviderWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate and refetch providers list
      queryClient.invalidateQueries({
        queryKey: SMS_INTEGRATION_QUERY_KEYS.PROVIDERS(),
      });
    },
    onError: (error: Error) => {
      console.error('Create provider error:', error);
    },
  });

  // Update provider mutation
  const updateProvider = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<SmsProviderFormData> }) =>
      smsIntegrationService.updateProviderWithBusinessLogic(id, data),
    onSuccess: (data, variables) => {
      // Invalidate providers list
      queryClient.invalidateQueries({
        queryKey: SMS_INTEGRATION_QUERY_KEYS.PROVIDERS(),
      });
      
      // Update specific provider cache
      queryClient.setQueryData(
        SMS_INTEGRATION_QUERY_KEYS.PROVIDER(variables.id),
        data
      );
    },
    onError: (error: Error) => {
      console.error('Update provider error:', error);
    },
  });

  // Delete provider mutation
  const deleteProvider = useMutation({
    mutationFn: (id: string) =>
      smsIntegrationService.deleteProviderWithBusinessLogic(id),
    onSuccess: () => {
      // Invalidate providers list
      queryClient.invalidateQueries({
        queryKey: SMS_INTEGRATION_QUERY_KEYS.PROVIDERS(),
      });
    },
    onError: (error: Error) => {
      console.error('Delete provider error:', error);
    },
  });

  // Test provider mutation
  const testProvider = useMutation({
    mutationFn: (id: string) =>
      smsIntegrationService.testProviderWithBusinessLogic(id),
    onSuccess: () => {
      // Invalidate providers list to update test results
      queryClient.invalidateQueries({
        queryKey: SMS_INTEGRATION_QUERY_KEYS.PROVIDERS(),
      });
    },
    onError: (error: Error) => {
      console.error('Test provider error:', error);
    },
  });

  // Toggle provider status mutation
  const toggleProviderStatus = useMutation({
    mutationFn: ({ id }: { id: string; status: string }) =>
      smsIntegrationService.updateProviderWithBusinessLogic(id, {
        settings: { /* existing settings would be preserved */ }
      }),
    onSuccess: () => {
      // Invalidate providers list
      queryClient.invalidateQueries({
        queryKey: SMS_INTEGRATION_QUERY_KEYS.PROVIDERS(),
      });
    },
    onError: (error: Error) => {
      console.error('Toggle provider status error:', error);
    },
  });

  // Send test SMS mutation
  const sendTestSms = useMutation({
    mutationFn: (data: SmsTestRequest) =>
      smsIntegrationService.sendTestSmsWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate providers list to update test results
      queryClient.invalidateQueries({
        queryKey: SMS_INTEGRATION_QUERY_KEYS.PROVIDERS(),
      });
    },
    onError: (error: Error) => {
      console.error('Send test SMS error:', error);
    },
  });

  // Check if any mutation is loading
  const isLoading = 
    createProvider.isPending ||
    updateProvider.isPending ||
    deleteProvider.isPending ||
    testProvider.isPending ||
    toggleProviderStatus.isPending ||
    sendTestSms.isPending;

  return {
    // Mutations
    createProvider,
    updateProvider,
    deleteProvider,
    testProvider,
    toggleProviderStatus,
    sendTestSms,
    
    // Loading state
    isLoading,
    
    // Individual loading states
    isCreating: createProvider.isPending,
    isUpdating: updateProvider.isPending,
    isDeleting: deleteProvider.isPending,
    isTesting: testProvider.isPending,
    isTogglingStatus: toggleProviderStatus.isPending,
    isSendingTest: sendTestSms.isPending,
    
    // Error states
    createError: createProvider.error,
    updateError: updateProvider.error,
    deleteError: deleteProvider.error,
    testError: testProvider.error,
    toggleStatusError: toggleProviderStatus.error,
    sendTestError: sendTestSms.error,
  };
};
