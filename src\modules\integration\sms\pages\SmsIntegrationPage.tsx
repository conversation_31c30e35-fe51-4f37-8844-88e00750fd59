import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Icon,
  Modal,
  Card,
  Input,
} from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import { SmsProviderConfig, SmsProviderFormData, SmsProviderQueryParams } from '../types';
import { smsTestRequestSchema } from '../schemas';
import SmsProviderList from '../components/SmsProviderList';
import SmsProviderForm from '../components/SmsProviderForm';
import { useSmsProviders, useSmsIntegration } from '../hooks';

/**
 * Trang chính quản lý tích hợp SMS
 */
const SmsIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);

  // State management
  const [queryParams, setQueryParams] = useState<SmsProviderQueryParams>({
    page: 1,
    limit: 12,
  });
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showTestModal, setShowTestModal] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<SmsProviderConfig | null>(null);

  // Hooks
  const {
    data: providersData,
    isLoading: providersLoading,
    refetch: refetchProviders,
  } = useSmsProviders(queryParams);

  const {
    createProvider,
    updateProvider,
    deleteProvider,
    toggleProviderStatus,
    sendTestSms,
    isLoading: actionLoading,
  } = useSmsIntegration();

  // Handlers
  const handleQueryChange = (newParams: SmsProviderQueryParams) => {
    setQueryParams(newParams);
  };

  const handleAddProvider = () => {
    setSelectedProvider(null);
    setShowAddForm(true);
  };

  const handleEditProvider = (provider: SmsProviderConfig) => {
    setSelectedProvider(provider);
    setShowEditForm(true);
  };

  const handleDeleteProvider = async (provider: SmsProviderConfig) => {
    try {
      await deleteProvider.mutateAsync(provider.id);
      NotificationUtil.success({
        message: t('integration:sms.deleteSuccess', 'Xóa nhà cung cấp thành công')
      });
      refetchProviders();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : t('integration:sms.deleteError', 'Có lỗi xảy ra khi xóa');
      NotificationUtil.error({ message: errorMessage });
    }
  };

  const handleTestProvider = (provider: SmsProviderConfig) => {
    setSelectedProvider(provider);
    setShowTestModal(true);
  };

  const handleToggleProviderStatus = async (provider: SmsProviderConfig) => {
    try {
      await toggleProviderStatus.mutateAsync({
        id: provider.id,
        status: provider.status === 'active' ? 'inactive' : 'active',
      });
      NotificationUtil.success({
        message: t('integration:sms.statusUpdateSuccess', 'Cập nhật trạng thái thành công')
      });
      refetchProviders();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : t('integration:sms.statusUpdateError', 'Có lỗi xảy ra');
      NotificationUtil.error({ message: errorMessage });
    }
  };

  const handleFormSubmit = async (data: SmsProviderFormData) => {
    try {
      if (selectedProvider) {
        // Update existing provider
        await updateProvider.mutateAsync({
          id: selectedProvider.id,
          data,
        });
        NotificationUtil.success({
          message: t('integration:sms.updateSuccess', 'Cập nhật nhà cung cấp thành công')
        });
        setShowEditForm(false);
      } else {
        // Create new provider
        await createProvider.mutateAsync(data);
        NotificationUtil.success({
          message: t('integration:sms.createSuccess', 'Tạo nhà cung cấp thành công')
        });
        setShowAddForm(false);
      }
      setSelectedProvider(null);
      refetchProviders();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : t('integration:sms.saveError', 'Có lỗi xảy ra khi lưu');
      NotificationUtil.error({ message: errorMessage });
    }
  };

  const handleFormCancel = () => {
    setShowAddForm(false);
    setShowEditForm(false);
    setSelectedProvider(null);
  };

  const handleTestSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    if (!selectedProvider) return;

    const formData = new FormData(event.currentTarget);
    const data = {
      providerId: selectedProvider.id,
      toNumber: formData.get('toNumber') as string,
      message: formData.get('message') as string,
    };

    try {
      // Validate data
      const validatedData = smsTestRequestSchema.parse(data);
      
      // Send test SMS
      await sendTestSms.mutateAsync(validatedData);
      NotificationUtil.success({
        message: t('integration:sms.testSentSuccess', 'Gửi tin nhắn test thành công')
      });
      setShowTestModal(false);
      refetchProviders();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : t('integration:sms.testSentError', 'Có lỗi xảy ra khi gửi test');
      NotificationUtil.error({ message: errorMessage });
    }
  };

  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({ ...prev, page }));
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Page Header */}
      <div className="mb-8">
        <Typography variant="h3" className="font-bold mb-2">
          {t('integration:sms.title', 'Tích hợp SMS')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('integration:sms.description', 'Quản lý và cấu hình các nhà cung cấp dịch vụ SMS')}
        </Typography>
      </div>

      {/* Provider List */}
      <SmsProviderList
        providers={providersData?.items || []}
        loading={providersLoading}
        queryParams={queryParams}
        onQueryChange={handleQueryChange}
        onAddProvider={handleAddProvider}
        onEditProvider={handleEditProvider}
        onDeleteProvider={handleDeleteProvider}
        onTestProvider={handleTestProvider}
        onToggleProviderStatus={handleToggleProviderStatus}
        totalCount={providersData?.total || 0}
        currentPage={queryParams.page || 1}
        itemsPerPage={queryParams.limit || 12}
        onPageChange={handlePageChange}
      />

      {/* Add Provider Modal */}
      <Modal
        isOpen={showAddForm}
        onClose={handleFormCancel}
        title={t('integration:sms.addProvider', 'Thêm nhà cung cấp SMS')}
        size="lg"
      >
        <SmsProviderForm
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          loading={actionLoading}
          mode="create"
        />
      </Modal>

      {/* Edit Provider Modal */}
      <Modal
        isOpen={showEditForm}
        onClose={handleFormCancel}
        title={t('integration:sms.editProvider', 'Chỉnh sửa nhà cung cấp SMS')}
        size="lg"
      >
        {selectedProvider && (
          <SmsProviderForm
            initialData={selectedProvider}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
            loading={actionLoading}
            mode="edit"
          />
        )}
      </Modal>

      {/* Test SMS Modal */}
      <Modal
        isOpen={showTestModal}
        onClose={() => setShowTestModal(false)}
        title={t('integration:sms.testProvider', 'Test nhà cung cấp SMS')}
      >
        {selectedProvider && (
          <div className="space-y-6">
            {/* Provider Info */}
            <Card className="p-4 bg-muted/30">
              <div className="flex items-center space-x-3">
                <Icon name="message-circle" size="lg" className="text-primary" />
                <div>
                  <Typography variant="h6" className="font-semibold">
                    {selectedProvider.displayName}
                  </Typography>
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('integration:sms.testDescription', 'Gửi tin nhắn test để kiểm tra kết nối')}
                  </Typography>
                </div>
              </div>
            </Card>

            {/* Test Form */}
            <form onSubmit={handleTestSubmit} className="space-y-4">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t('integration:sms.testPhoneNumber', 'Số điện thoại nhận')} *
                  </label>
                  <Input
                    name="toNumber"
                    type="tel"
                    placeholder="+84901234567"
                    fullWidth
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('integration:sms.testPhoneNumberHelp', 'Nhập số điện thoại để nhận tin nhắn test')}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {t('integration:sms.testMessage', 'Nội dung tin nhắn')} *
                  </label>
                  <Input
                    name="message"
                    type="text"
                    placeholder={t('integration:sms.testMessagePlaceholder', 'Đây là tin nhắn test từ hệ thống')}
                    maxLength={160}
                    fullWidth
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('integration:sms.testMessageHelp', 'Nội dung tin nhắn test (tối đa 160 ký tự)')}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowTestModal(false)}
                >
                  {t('common:cancel', 'Hủy')}
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={sendTestSms.isPending}
                  disabled={sendTestSms.isPending}
                >
                  <Icon name="send" size="sm" className="mr-2" />
                  {t('integration:sms.sendTest', 'Gửi test')}
                </Button>
              </div>
            </form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SmsIntegrationPage;
