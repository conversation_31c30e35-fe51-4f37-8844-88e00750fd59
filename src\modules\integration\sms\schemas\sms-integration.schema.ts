import { z } from 'zod';
import { SMS_PROVIDER_TYPES } from '../constants';

/**
 * SMS Provider Credentials Schema - Dynamic based on provider type
 */
const smsProviderCredentialsSchema = z.object({
  // Twilio
  accountSid: z.string().optional(),
  authToken: z.string().optional(),
  
  // AWS SNS
  accessKeyId: z.string().optional(),
  secretAccessKey: z.string().optional(),
  region: z.string().optional(),
  
  // Vietnamese providers
  apiKey: z.string().optional(),
  apiSecret: z.string().optional(),
  username: z.string().optional(),
  password: z.string().optional(),
  
  // Custom API
  endpoint: z.string().url().optional(),
  headers: z.record(z.string()).optional(),
}).passthrough(); // Allow additional fields

/**
 * SMS Rate Limits Schema
 */
const smsRateLimitsSchema = z.object({
  perSecond: z.number().min(0).max(1000),
  perMinute: z.number().min(0).max(60000),
  perHour: z.number().min(0).max(3600000),
  perDay: z.number().min(0).max(********),
  perMonth: z.number().min(0).max(**********),
});

/**
 * SMS Retry Configuration Schema
 */
const smsRetryConfigSchema = z.object({
  maxRetries: z.number().min(0).max(10),
  retryDelay: z.number().min(1).max(300), // 1-300 seconds
  backoffMultiplier: z.number().min(1).max(5),
});

/**
 * SMS Provider Settings Schema
 */
const smsProviderSettingsSchema = z.object({
  fromNumber: z.string().optional(),
  fromName: z.string().max(11).optional(), // SMS sender name limit
  rateLimits: smsRateLimitsSchema,
  retryConfig: smsRetryConfigSchema,
  webhookUrl: z.string().url().optional(),
  enableDeliveryReports: z.boolean(),
  enableOptOut: z.boolean(),
  timezone: z.string(),
});

/**
 * SMS Provider Form Schema
 */
export const smsProviderFormSchema = z.object({
  name: z.string()
    .min(1, 'Tên provider không được để trống')
    .max(100, 'Tên provider không được quá 100 ký tự')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Tên provider chỉ được chứa chữ cái, số, dấu gạch ngang và gạch dưới'),
  
  type: z.enum([
    SMS_PROVIDER_TYPES.TWILIO,
    SMS_PROVIDER_TYPES.AWS_SNS,
    SMS_PROVIDER_TYPES.VIETTEL,
    SMS_PROVIDER_TYPES.VNPT,
    SMS_PROVIDER_TYPES.FPT,
    SMS_PROVIDER_TYPES.CUSTOM,
  ] as const, {
    errorMap: () => ({ message: 'Vui lòng chọn loại provider' })
  }),
  
  displayName: z.string()
    .min(1, 'Tên hiển thị không được để trống')
    .max(100, 'Tên hiển thị không được quá 100 ký tự'),
  
  description: z.string()
    .max(500, 'Mô tả không được quá 500 ký tự')
    .optional(),
  
  credentials: smsProviderCredentialsSchema,
  
  settings: smsProviderSettingsSchema.partial(),
  
  isDefault: z.boolean(),
}).superRefine((data, ctx) => {
  // Validate required credentials based on provider type
  const { type, credentials } = data;
  
  switch (type) {
    case SMS_PROVIDER_TYPES.TWILIO:
      if (!credentials.accountSid) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Account SID là bắt buộc cho Twilio',
          path: ['credentials', 'accountSid'],
        });
      }
      if (!credentials.authToken) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Auth Token là bắt buộc cho Twilio',
          path: ['credentials', 'authToken'],
        });
      }
      break;
      
    case SMS_PROVIDER_TYPES.AWS_SNS:
      if (!credentials.accessKeyId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Access Key ID là bắt buộc cho AWS SNS',
          path: ['credentials', 'accessKeyId'],
        });
      }
      if (!credentials.secretAccessKey) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Secret Access Key là bắt buộc cho AWS SNS',
          path: ['credentials', 'secretAccessKey'],
        });
      }
      if (!credentials.region) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Region là bắt buộc cho AWS SNS',
          path: ['credentials', 'region'],
        });
      }
      break;
      
    case SMS_PROVIDER_TYPES.VIETTEL:
    case SMS_PROVIDER_TYPES.FPT:
      if (!credentials.apiKey) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'API Key là bắt buộc',
          path: ['credentials', 'apiKey'],
        });
      }
      if (!credentials.username) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Username là bắt buộc',
          path: ['credentials', 'username'],
        });
      }
      break;
      
    case SMS_PROVIDER_TYPES.VNPT:
      if (!credentials.username) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Username là bắt buộc cho VNPT',
          path: ['credentials', 'username'],
        });
      }
      if (!credentials.password) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Password là bắt buộc cho VNPT',
          path: ['credentials', 'password'],
        });
      }
      if (!credentials.apiKey) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'API Key là bắt buộc cho VNPT',
          path: ['credentials', 'apiKey'],
        });
      }
      break;
      
    case SMS_PROVIDER_TYPES.CUSTOM:
      if (!credentials.endpoint) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Endpoint là bắt buộc cho Custom API',
          path: ['credentials', 'endpoint'],
        });
      }
      if (!credentials.apiKey) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'API Key là bắt buộc cho Custom API',
          path: ['credentials', 'apiKey'],
        });
      }
      break;
  }
});

/**
 * SMS Test Request Schema
 */
export const smsTestRequestSchema = z.object({
  providerId: z.string().min(1, 'Provider ID là bắt buộc'),
  toNumber: z.string()
    .min(1, 'Số điện thoại là bắt buộc')
    .regex(/^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/, 'Số điện thoại không hợp lệ'),
  message: z.string()
    .min(1, 'Nội dung tin nhắn là bắt buộc')
    .max(160, 'Nội dung tin nhắn không được quá 160 ký tự'),
});

/**
 * SMS Provider Query Schema
 */
export const smsProviderQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  status: z.enum(['active', 'inactive', 'error', 'testing', 'pending']).optional(),
  type: z.enum([
    SMS_PROVIDER_TYPES.TWILIO,
    SMS_PROVIDER_TYPES.AWS_SNS,
    SMS_PROVIDER_TYPES.VIETTEL,
    SMS_PROVIDER_TYPES.VNPT,
    SMS_PROVIDER_TYPES.FPT,
    SMS_PROVIDER_TYPES.CUSTOM,
  ] as const).optional(),
  search: z.string().max(100).optional(),
  sortBy: z.enum(['name', 'type', 'status', 'createdAt', 'updatedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

/**
 * Type exports for form validation
 */
export type SmsProviderFormSchemaData = z.infer<typeof smsProviderFormSchema>;
export type SmsTestRequestData = z.infer<typeof smsTestRequestSchema>;
export type SmsProviderQueryData = z.infer<typeof smsProviderQuerySchema>;
