import { apiClient } from '@/shared/api';
import {
  SmsProviderConfig,
  SmsProviderFormData,
  SmsProviderQueryParams,
  SmsProviderResponse,
  SmsTestRequest,
  SmsTestResult,
} from '../types';
import { SMS_INTEGRATION_ENDPOINTS } from '../constants';

/**
 * SMS Integration API Service
 */
export class SmsIntegrationService {
  /**
   * Get list of SMS providers
   */
  static async getProviders(params?: SmsProviderQueryParams): Promise<SmsProviderResponse> {
    const response = await apiClient.get<SmsProviderResponse>(SMS_INTEGRATION_ENDPOINTS.PROVIDERS, {
      params,
    });
    return response.result;
  }

  /**
   * Get SMS provider by ID
   */
  static async getProvider(id: string): Promise<SmsProviderConfig> {
    const response = await apiClient.get<SmsProviderConfig>(SMS_INTEGRATION_ENDPOINTS.PROVIDER_DETAIL(id));
    return response.result;
  }

  /**
   * Create new SMS provider
   */
  static async createProvider(data: SmsProviderFormData): Promise<SmsProviderConfig> {
    const response = await apiClient.post<SmsProviderConfig>(SMS_INTEGRATION_ENDPOINTS.PROVIDERS, data);
    return response.result;
  }

  /**
   * Update SMS provider
   */
  static async updateProvider(id: string, data: Partial<SmsProviderFormData>): Promise<SmsProviderConfig> {
    const response = await apiClient.put<SmsProviderConfig>(SMS_INTEGRATION_ENDPOINTS.PROVIDER_DETAIL(id), data);
    return response.result;
  }

  /**
   * Delete SMS provider
   */
  static async deleteProvider(id: string): Promise<void> {
    await apiClient.delete(SMS_INTEGRATION_ENDPOINTS.PROVIDER_DETAIL(id));
  }

  /**
   * Test SMS provider connection
   */
  static async testProvider(id: string): Promise<SmsTestResult> {
    const response = await apiClient.post<SmsTestResult>(SMS_INTEGRATION_ENDPOINTS.TEST_PROVIDER(id));
    return response.result;
  }

  /**
   * Update SMS provider status
   */
  static async updateProviderStatus(id: string, status: string): Promise<SmsProviderConfig> {
    const response = await apiClient.patch<SmsProviderConfig>(SMS_INTEGRATION_ENDPOINTS.PROVIDER_STATUS(id), {
      status,
    });
    return response.result;
  }

  /**
   * Send test SMS
   */
  static async sendTestSms(data: SmsTestRequest): Promise<SmsTestResult> {
    const response = await apiClient.post<SmsTestResult>(SMS_INTEGRATION_ENDPOINTS.SEND_TEST_SMS, data);
    return response.result;
  }
}

/**
 * Business Logic Service for SMS Integration
 */
export class SmsIntegrationBusinessService {
  /**
   * Get providers with business logic
   */
  static async getProvidersWithBusinessLogic(params?: SmsProviderQueryParams): Promise<SmsProviderResponse> {
    // Apply default parameters
    const defaultParams: SmsProviderQueryParams = {
      page: 1,
      limit: 12,
      sortBy: 'createdAt',
      sortOrder: 'desc',
      ...params,
    };

    // Validate parameters
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    if (defaultParams.page && defaultParams.page < 1) {
      throw new Error('Page must be greater than 0');
    }

    return SmsIntegrationService.getProviders(defaultParams);
  }

  /**
   * Create provider with business logic
   */
  static async createProviderWithBusinessLogic(data: SmsProviderFormData): Promise<SmsProviderConfig> {
    // Validate business rules
    await this.validateProviderBusinessRules(data);

    // If setting as default, ensure only one default exists
    if (data.isDefault) {
      await this.ensureSingleDefaultProvider();
    }

    return SmsIntegrationService.createProvider(data);
  }

  /**
   * Update provider with business logic
   */
  static async updateProviderWithBusinessLogic(
    id: string,
    data: Partial<SmsProviderFormData>
  ): Promise<SmsProviderConfig> {
    // Get current provider
    const currentProvider = await SmsIntegrationService.getProvider(id);

    // Validate business rules
    if (data.name || data.type || data.credentials) {
      await this.validateProviderBusinessRules({
        ...currentProvider,
        ...data,
      } as SmsProviderFormData);
    }

    // If setting as default, ensure only one default exists
    if (data.isDefault && !currentProvider.isDefault) {
      await this.ensureSingleDefaultProvider();
    }

    return SmsIntegrationService.updateProvider(id, data);
  }

  /**
   * Delete provider with business logic
   */
  static async deleteProviderWithBusinessLogic(id: string): Promise<void> {
    // Get current provider
    const provider = await SmsIntegrationService.getProvider(id);

    // Prevent deletion of default provider if it's the only one
    if (provider.isDefault) {
      const allProviders = await SmsIntegrationService.getProviders({ limit: 100 });
      if (allProviders.items.length === 1) {
        throw new Error('Cannot delete the only SMS provider');
      }
    }

    return SmsIntegrationService.deleteProvider(id);
  }

  /**
   * Test provider with enhanced error handling
   */
  static async testProviderWithBusinessLogic(id: string): Promise<SmsTestResult> {
    try {
      const result = await SmsIntegrationService.testProvider(id);
      
      // Update provider's last tested time and result
      await SmsIntegrationService.updateProvider(id, {
        settings: {
          // This would be merged with existing settings
        },
      });

      return result;
    } catch (error: unknown) {
      // Return failed test result
      const errorMessage = error instanceof Error ? error.message : 'Test failed';
      return {
        success: false,
        message: errorMessage,
        responseTime: 0,
        timestamp: new Date().toISOString(),
        details: {
          error: errorMessage,
        },
      };
    }
  }

  /**
   * Send test SMS with validation
   */
  static async sendTestSmsWithBusinessLogic(data: SmsTestRequest): Promise<SmsTestResult> {
    // Validate phone number format
    const phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
    if (!phoneRegex.test(data.toNumber)) {
      throw new Error('Invalid Vietnamese phone number format');
    }

    // Validate message length
    if (data.message.length > 160) {
      throw new Error('Message cannot exceed 160 characters');
    }

    // Check if provider exists and is active
    const provider = await SmsIntegrationService.getProvider(data.providerId);
    if (provider.status !== 'active') {
      throw new Error('Provider is not active');
    }

    return SmsIntegrationService.sendTestSms(data);
  }

  /**
   * Validate provider business rules
   */
  private static async validateProviderBusinessRules(data: SmsProviderFormData): Promise<void> {
    // Check for duplicate names
    const existingProviders = await SmsIntegrationService.getProviders({
      search: data.name,
      limit: 100,
    });

    const duplicateName = existingProviders.items.find(
      provider => provider.name.toLowerCase() === data.name.toLowerCase()
    );

    if (duplicateName) {
      throw new Error(`Provider with name "${data.name}" already exists`);
    }

    // Validate credentials based on provider type
    this.validateCredentialsByType(data);
  }

  /**
   * Validate credentials based on provider type
   */
  private static validateCredentialsByType(data: SmsProviderFormData): void {
    const { type, credentials } = data;

    switch (type) {
      case 'twilio':
        if (!credentials.accountSid || !credentials.authToken) {
          throw new Error('Twilio requires Account SID and Auth Token');
        }
        break;

      case 'aws-sns':
        if (!credentials.accessKeyId || !credentials.secretAccessKey || !credentials.region) {
          throw new Error('AWS SNS requires Access Key ID, Secret Access Key, and Region');
        }
        break;

      case 'viettel':
      case 'fpt':
        if (!credentials.apiKey || !credentials.username) {
          throw new Error(`${type.toUpperCase()} requires API Key and Username`);
        }
        break;

      case 'vnpt':
        if (!credentials.username || !credentials.password || !credentials.apiKey) {
          throw new Error('VNPT requires Username, Password, and API Key');
        }
        break;

      case 'custom':
        if (!credentials.endpoint || !credentials.apiKey) {
          throw new Error('Custom API requires Endpoint and API Key');
        }
        break;

      default:
        throw new Error(`Unsupported provider type: ${type}`);
    }
  }

  /**
   * Ensure only one default provider exists
   */
  private static async ensureSingleDefaultProvider(): Promise<void> {
    const allProviders = await SmsIntegrationService.getProviders({ limit: 100 });
    const defaultProviders = allProviders.items.filter(p => p.isDefault);

    // Update all current default providers to non-default
    for (const provider of defaultProviders) {
      await SmsIntegrationService.updateProvider(provider.id, {
        isDefault: false,
      });
    }
  }
}

// Export both services
export { SmsIntegrationService as smsIntegrationApi };
export { SmsIntegrationBusinessService as smsIntegrationService };
