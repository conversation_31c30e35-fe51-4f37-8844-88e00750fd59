/**
 * SMS Integration Types
 */

/**
 * Supported SMS Provider Types
 */
export type SmsProviderType = 
  | 'twilio'
  | 'aws-sns'
  | 'viettel'
  | 'vnpt'
  | 'fpt'
  | 'custom';

/**
 * SMS Provider Status
 */
export type SmsProviderStatus = 'active' | 'inactive' | 'error' | 'testing' | 'pending';

/**
 * SMS Provider Configuration
 */
export interface SmsProviderConfig {
  id: string;
  name: string;
  type: SmsProviderType;
  displayName: string;
  description?: string;
  status: SmsProviderStatus;
  credentials: SmsProviderCredentials;
  settings: SmsProviderSettings;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  lastTestedAt?: string;
  testResult?: SmsTestResult;
}

/**
 * SMS Provider Credentials - Dynamic based on provider type
 */
export interface SmsProviderCredentials {
  // Twilio
  accountSid?: string;
  authToken?: string;
  
  // AWS SNS
  accessKeyId?: string;
  secretAccessKey?: string;
  region?: string;
  
  // Vietnamese providers (Viettel, VNPT, FPT)
  apiKey?: string;
  apiSecret?: string;
  username?: string;
  password?: string;
  
  // Custom API
  endpoint?: string;
  headers?: Record<string, string>;
  
  // Common
  [key: string]: string | Record<string, string> | undefined;
}

/**
 * SMS Provider Settings
 */
export interface SmsProviderSettings {
  fromNumber?: string;
  fromName?: string;
  rateLimits: SmsRateLimits;
  retryConfig: SmsRetryConfig;
  webhookUrl?: string;
  enableDeliveryReports: boolean;
  enableOptOut: boolean;
  timezone: string;
}

/**
 * SMS Rate Limits
 */
export interface SmsRateLimits {
  perSecond: number;
  perMinute: number;
  perHour: number;
  perDay: number;
  perMonth: number;
}

/**
 * SMS Retry Configuration
 */
export interface SmsRetryConfig {
  maxRetries: number;
  retryDelay: number; // seconds
  backoffMultiplier: number;
}

/**
 * SMS Test Result
 */
export interface SmsTestResult {
  success: boolean;
  message: string;
  responseTime: number; // milliseconds
  timestamp: string;
  details?: {
    statusCode?: number;
    response?: unknown;
    error?: string;
  };
}

/**
 * SMS Provider Form Data
 */
export interface SmsProviderFormData {
  name: string;
  type: SmsProviderType;
  displayName: string;
  description?: string;
  credentials: SmsProviderCredentials;
  settings: Partial<SmsProviderSettings>;
  isDefault: boolean;
}

/**
 * SMS Provider Query Parameters
 */
export interface SmsProviderQueryParams {
  page?: number;
  limit?: number;
  status?: SmsProviderStatus;
  type?: SmsProviderType;
  search?: string;
  sortBy?: 'name' | 'type' | 'status' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * SMS Provider API Response
 */
export interface SmsProviderResponse {
  items: SmsProviderConfig[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * SMS Test Request
 */
export interface SmsTestRequest {
  providerId: string;
  toNumber: string;
  message: string;
}

/**
 * SMS Provider Template for different types
 */
export interface SmsProviderTemplate {
  type: SmsProviderType;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  documentationUrl: string;
  requiredCredentials: string[];
  optionalCredentials: string[];
  defaultSettings: Partial<SmsProviderSettings>;
  configurationSteps: string[];
}
