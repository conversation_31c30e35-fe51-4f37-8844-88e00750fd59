/**
 * Provider Model Types for Integration Module
 */

import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Interface cho thông tin provider model
 */
export interface ProviderModel {
  /**
   * ID của provider model
   */
  id: string;

  /**
   * Tên định danh cho provider model
   */
  name: string;

  /**
   * Loại nhà cung cấp
   */
  type: TypeProviderEnum;

  /**
   * API key của nhà cung cấp (chỉ hiển thị khi edit)
   */
  apiKey?: string;

  /**
   * Thời gian tạo (unix timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (unix timestamp)
   */
  updatedAt?: number;

  /**
   * Thông tin người tạo
   */
  createdBy?: {
    id: number;
    name: string;
    avatar: string | null;
  };

  /**
   * Thông tin người cập nhật
   */
  updatedBy?: {
    id: number;
    name: string;
    avatar: string | null;
  };
}

/**
 * Interface cho item trong danh sách provider model
 */
export interface ProviderModelListItem {
  /**
   * ID của provider model
   */
  id: string;

  /**
   * Tên định danh cho provider model
   */
  name: string;

  /**
   * Loại nhà cung cấp
   */
  type: TypeProviderEnum;

  /**
   * Thời gian tạo (unix timestamp)
   */
  createdAt: number;
}

/**
 * Interface cho request tạo provider model mới
 */
export interface CreateProviderModelDto {
  /**
   * Tên định danh cho provider model
   */
  name: string;

  /**
   * Loại nhà cung cấp
   */
  provider: TypeProviderEnum;

  /**
   * API key của nhà cung cấp
   */
  apiKey: string;
}

/**
 * Interface cho request cập nhật provider model
 */
export interface UpdateProviderModelDto {
  /**
   * Tên định danh cho provider model
   */
  name?: string;

  /**
   * API key của nhà cung cấp
   */
  apiKey?: string;
}

/**
 * Interface cho query parameters
 */
export interface ProviderModelQueryDto extends QueryDto {
  /**
   * Lọc theo loại provider
   */
  type?: TypeProviderEnum;
}

/**
 * Interface cho form data
 */
export interface ProviderModelFormData extends Omit<CreateProviderModelDto, 'provider'> {
  type: TypeProviderEnum;
}

/**
 * Utility function để lấy tên hiển thị của provider
 */
export const getProviderDisplayName = (provider: TypeProviderEnum): string => {
  const providerNames: Record<TypeProviderEnum, string> = {
    [TypeProviderEnum.REDAI]: 'RedAI',
    [TypeProviderEnum.OPENAI]: 'OpenAI',
    [TypeProviderEnum.ANTHROPIC]: 'Anthropic',
    [TypeProviderEnum.GOOGLE]: 'Google',
    [TypeProviderEnum.META]: 'Meta',
    [TypeProviderEnum.DEEPSEEK]: 'DeepSeek',
    [TypeProviderEnum.XAI]: 'XAI',
  };

  return providerNames[provider] || provider;
};

/**
 * Utility function để lấy icon của provider
 */
export const getProviderIcon = (provider: TypeProviderEnum): string => {
  const providerIcons: Record<TypeProviderEnum, string> = {
    [TypeProviderEnum.REDAI]: 'robot',
    [TypeProviderEnum.OPENAI]: 'openai',
    [TypeProviderEnum.ANTHROPIC]: 'anthropic',
    [TypeProviderEnum.GOOGLE]: 'google',
    [TypeProviderEnum.META]: 'meta',
    [TypeProviderEnum.DEEPSEEK]: 'deepseek',
    [TypeProviderEnum.XAI]: 'grok',
  };

  return providerIcons[provider] || 'robot';
};

/**
 * Utility function để lấy danh sách options cho select
 */
export const getProviderOptions = () => {
  return Object.values(TypeProviderEnum).map(type => ({
    value: type,
    label: getProviderDisplayName(type),
    icon: getProviderIcon(type),
  }));
};

/**
 * Utility function để validate provider type
 */
export const isValidProviderType = (type: string): type is TypeProviderEnum => {
  return Object.values(TypeProviderEnum).includes(type as TypeProviderEnum);
};
