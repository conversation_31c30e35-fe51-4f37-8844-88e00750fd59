# Bulk Delete API Update - Segment Management

## Thay đổi

Cập nhật API xóa nhiều segment để sử dụng endpoint mới thay vì gọi nhiều API đơn lẻ.

### Trước:
```typescript
// Gọi API xóa từng segment một
await Promise.all(selectedRowKeys.map(id => deleteSegment(Number(id))));
```

### Sau:
```typescript
// Gọi API xóa nhiều segment cùng lúc
const idsToDelete = selectedRowKeys.map(id => Number(id));
await bulkDeleteSegments(idsToDelete);
```

## API Endpoint Mới

**Endpoint:** `DELETE /v1/marketing/segments`

**Request Body:**
```json
{
  "ids": [1, 2, 3]
}
```

**Response:**
```json
{
  "success": true
}
```

## Files đã cập nhật

### 1. Service Layer
**File:** `src/modules/marketing/services/segment.service.ts`

```typescript
/**
 * Delete multiple segments
 */
deleteMultipleSegments: async (ids: number[]): Promise<{ success: boolean }> => {
  await apiClient.delete<{ success: boolean }>(`/v1${BASE_URL}`, {
    data: { ids }
  });
  return { success: true };
},
```

### 2. Hooks Layer
**File:** `src/modules/marketing/hooks/useSegmentQuery.ts`

```typescript
/**
 * Hook to delete multiple segments
 */
export const useBulkDeleteSegments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) => SegmentService.deleteMultipleSegments(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
  });
};
```

### 3. Page Component
**File:** `src/modules/marketing/pages/SegmentPage.tsx`

```typescript
// Import hook mới
import { useBulkDeleteSegments } from '../hooks';

// Sử dụng hook
const { mutateAsync: bulkDeleteSegments } = useBulkDeleteSegments();

// Cập nhật logic xử lý
const handleConfirmBulkDelete = useCallback(async () => {
  if (selectedRowKeys.length === 0) return;

  try {
    const deletedCount = selectedRowKeys.length;
    const idsToDelete = selectedRowKeys.map(id => Number(id));

    // Gọi API mới
    await bulkDeleteSegments(idsToDelete);

    setSelectedRowKeys([]);
    setShowBulkDeleteConfirm(false);

    NotificationUtil.success({
      message: t('marketing:segment.bulkDeleteSuccess', 
        `Đã xóa ${deletedCount} phân đoạn thành công`),
      duration: 3000,
    });
  } catch (error) {
    console.error('Error deleting segments:', error);
    NotificationUtil.error({
      message: t('marketing:segment.bulkDeleteError', 
        'Có lỗi xảy ra khi xóa phân đoạn'),
      duration: 3000,
    });
  }
}, [selectedRowKeys, bulkDeleteSegments, t]);
```

## Lợi ích

### 1. Performance
- **Trước**: N requests cho N segments
- **Sau**: 1 request cho N segments
- **Cải thiện**: Giảm network overhead, faster execution

### 2. Reliability
- **Trước**: Nếu 1 request fail, các request khác vẫn thành công → inconsistent state
- **Sau**: Atomic operation, all-or-nothing
- **Cải thiện**: Better data consistency

### 3. User Experience
- **Trước**: Có thể có delay khi xóa nhiều items
- **Sau**: Faster response time
- **Cải thiện**: Better perceived performance

### 4. Server Load
- **Trước**: Multiple database transactions
- **Sau**: Single database transaction
- **Cải thiện**: Reduced server load

## Testing

### Test Cases

1. **Xóa 1 segment**
   - Select 1 segment
   - Click bulk delete
   - Verify API call với `ids: [1]`

2. **Xóa nhiều segments**
   - Select multiple segments (e.g., 3 segments)
   - Click bulk delete
   - Verify API call với `ids: [1, 2, 3]`

3. **Error handling**
   - Mock API error
   - Verify error notification
   - Verify UI state không bị broken

4. **Success handling**
   - Verify success notification
   - Verify table refresh
   - Verify selected items cleared

### Manual Testing

```bash
# Test API endpoint trực tiếp
curl -X DELETE "http://localhost:3000/v1/marketing/segments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"ids": [1, 2, 3]}'
```

### Frontend Testing

```javascript
// Test trong browser console
const testIds = [1, 2, 3];
bulkDeleteSegments(testIds)
  .then(result => console.log('Success:', result))
  .catch(error => console.error('Error:', error));
```

## Backward Compatibility

- **Single delete**: Vẫn sử dụng `useDeleteSegment()` hook
- **Bulk delete**: Sử dụng `useBulkDeleteSegments()` hook mới
- **UI**: Không thay đổi, user experience giữ nguyên

## Migration Notes

- Không cần migration data
- API cũ vẫn hoạt động cho single delete
- Chỉ bulk delete sử dụng endpoint mới

## Error Handling

### Possible Errors:
1. **Network Error**: Connection timeout, server down
2. **Authorization Error**: Invalid token, insufficient permissions  
3. **Validation Error**: Invalid IDs, empty array
4. **Business Logic Error**: Segments in use, cannot delete

### Error Response Format:
```json
{
  "code": 400,
  "message": "Validation failed",
  "detail": {
    "invalidIds": [999],
    "reason": "Segments not found"
  }
}
```

## Future Enhancements

1. **Progress Indicator**: Show progress for large bulk operations
2. **Partial Success**: Handle cases where some deletes succeed, others fail
3. **Undo Functionality**: Allow users to undo bulk delete
4. **Batch Size Limit**: Implement maximum batch size for performance
