# Debug Custom Field Integration

## Vấn đề hiện tại
- API trả về data đúng format
- Nhưng dropdown chỉ hiển thị static fields, không hiển thị custom fields

## API Response Structure
```json
{
  "code": 200,
  "message": "Success", 
  "result": {
    "data": [...], // ← API trả về "data" không phải "items"
    "meta": {
      "total": 32,
      "page": 1,
      "limit": 20,
      "totalPages": 2
    }
  }
}
```

## Debug Steps

### 1. Test API Response Parsing
Mở browser console và chạy:
```javascript
// Test CustomFieldBusinessService
const testParams = { search: '', page: 1, limit: 20 };

// Check if loadCustomFieldOptions works
loadCustomFieldOptions(testParams).then(result => {
  console.log('loadCustomFieldOptions result:', result);
});
```

### 2. Test Combined Fields Function
```javascript
// Test loadAllFieldOptions
loadAllFieldOptions({ page: 1, limit: 20 }).then(result => {
  console.log('loadAllFieldOptions result:', result);
  console.log('Total items:', result.items.length);
  console.log('Static fields:', result.items.filter(item => item.description === 'Trường hệ thống'));
  console.log('Custom fields:', result.items.filter(item => item.description !== 'Trường hệ thống'));
});
```

### 3. Check AsyncSelectWithPagination Props
Verify trong React DevTools:
- `loadOptions` function có được pass đúng không
- `autoLoadInitial={true}` có hoạt động không
- Component có call `loadOptions` khi mount không

### 4. Network Tab Debug
Kiểm tra trong Network tab:
- Request đến `/v1/user/marketing/audience-custom-fields` có được gửi không
- Response có đúng format không
- Status code có phải 200 không

## Expected vs Actual

### Expected Behavior:
1. Component mount → `autoLoadInitial=true` → call `loadAllFieldOptions`
2. `loadAllFieldOptions` → call `loadCustomFieldOptions` → call API
3. API response → transform data → combine với static fields
4. Dropdown hiển thị: Static fields + Custom fields

### Actual Behavior:
- Dropdown chỉ hiển thị static fields
- Custom fields không xuất hiện

## Possible Issues

### 1. API Response Parsing
```javascript
// Current code expects:
result.items || []

// But API returns:
result.data || []
```
**Status: ✅ FIXED** - Updated parsing logic

### 2. AsyncSelectWithPagination Configuration
```javascript
// Current config:
searchOnEnter={true}  // ← Có thể ngăn auto load
autoLoadInitial={undefined}  // ← Cần set true

// Fixed config:
searchOnEnter={false}
autoLoadInitial={true}
```
**Status: ✅ FIXED** - Updated props

### 3. Function Reference Issue
```javascript
// Check if loadAllFieldOptions is properly bound
console.log('loadAllFieldOptions type:', typeof loadAllFieldOptions);
console.log('loadAllFieldOptions:', loadAllFieldOptions);
```

### 4. Error Handling
```javascript
// Check if errors are being caught silently
loadAllFieldOptions({ page: 1, limit: 20 })
  .then(result => console.log('Success:', result))
  .catch(error => console.error('Error:', error));
```

## Debug Console Commands

### Test API Direct Call:
```javascript
// Test CustomFieldService directly
CustomFieldService.getCustomFields({ page: 1, limit: 20 })
  .then(response => {
    console.log('Direct API response:', response);
    console.log('Result structure:', response.result);
    console.log('Data array:', response.result.data);
  })
  .catch(error => console.error('API Error:', error));
```

### Test Business Service:
```javascript
// Test CustomFieldBusinessService
CustomFieldBusinessService.getCustomFieldsForSelect({ page: 1, limit: 20 })
  .then(result => {
    console.log('Business service result:', result);
    console.log('Transformed items:', result.items);
  })
  .catch(error => console.error('Business service error:', error));
```

### Test Hook:
```javascript
// Test useCustomFieldLoadOptions
const loadOptions = useCustomFieldLoadOptions();
loadOptions({ page: 1, limit: 20 })
  .then(result => {
    console.log('Hook result:', result);
  })
  .catch(error => console.error('Hook error:', error));
```

## Verification Checklist

- [ ] API call được gửi thành công
- [ ] Response có đúng structure `result.data`
- [ ] Transform logic parse đúng fields
- [ ] Static fields được tạo đúng
- [ ] Combine logic hoạt động
- [ ] AsyncSelectWithPagination nhận đúng data
- [ ] Dropdown hiển thị cả static và custom fields

## Quick Fix Test

Thử hardcode data để test UI:
```javascript
const testLoadOptions = async () => {
  return {
    items: [
      { value: 'email', label: 'Email', description: 'Trường hệ thống' },
      { value: 'birth_date', label: 'Ngày sinh', description: 'Trường tùy chỉnh' },
      { value: 'gender', label: 'Giới tính', description: 'Trường tùy chỉnh' }
    ],
    totalItems: 3,
    totalPages: 1,
    currentPage: 1
  };
};

// Replace loadOptions temporarily
<AsyncSelectWithPagination
  loadOptions={testLoadOptions}
  // ... other props
/>
```

Nếu hardcode hoạt động → vấn đề ở data loading
Nếu hardcode không hoạt động → vấn đề ở AsyncSelectWithPagination config
