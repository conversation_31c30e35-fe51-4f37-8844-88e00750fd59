# Email Template API Integration Test

## Tóm tắt

Đã hoàn thành việc đấu nối API tạo template email vào trang `/marketing/email/templates` theo đúng specification backend.

## Các thay đổi đã thực hiện

### 1. **Cập nhật Types** (`types/template-email.types.ts`)
- Cập nhật `CreateTemplateEmailRequest` để match với backend `CreateTemplateEmailDto`
- Thêm các field: `textContent`, `type`, `previewText`, `variables`
- Loại bỏ field `description` và `placeholders` (được thay bằng `variables`)

### 2. **Cập nhật Template Email Service** (`services/template-email.service.ts`)
- Thêm comment về backend endpoint specification
- Đ<PERSON>m bảo endpoint path đúng: `/marketing/template-emails`

### 3. **Cập nhật Business Service** (`services/template-email-business.service.ts`)
- Cập nhật validation logic cho các field mới
- Thêm xử lý cho `textContent`, `type`, `previewText`, `variables`
- Set default value cho `type` = 'NEWSLETTER'

### 4. **Cập nhật Adapter Service** (`services/email-template-adapter.service.ts`)
- Cập nhật method `createEmailTemplate()` để map đúng data structure
- Map `htmlContent` (frontend) → `content` (backend)
- Map `variables` array với đầy đủ properties
- Thêm logging để debug

### 5. **Cập nhật Hooks** (`hooks/email/useEmailTemplatesAdapter.ts`)
- Cập nhật `useCreateEmailTemplateAdapter()` để pass đầy đủ data
- Thêm logging để debug
- Đảm bảo invalidate cache sau khi tạo thành công

### 6. **Cập nhật Form Component** (`components/email/CreateEmailTemplateForm.tsx`)
- Thay đổi import từ `useCreateEmailTemplate` sang `useCreateEmailTemplateAdapter`
- Form đã sẵn sàng gửi đúng data structure

## Data Flow

```
CreateEmailTemplateForm (Frontend)
    ↓ (CreateEmailTemplateDto)
useCreateEmailTemplateAdapter
    ↓ (Map to backend format)
EmailTemplateAdapterService.createEmailTemplate
    ↓ (CreateTemplateEmailRequest)
TemplateEmailBusinessService.createTemplateEmail
    ↓ (Validated data)
TemplateEmailService.createTemplateEmail
    ↓ (HTTP POST)
Backend API: /api/v1/marketing/template-emails
```

## Data Mapping

### Frontend → Backend
```typescript
{
  name: string;                    → name: string;
  subject: string;                 → subject: string;
  htmlContent: string;             → content: string;
  textContent?: string;            → textContent?: string;
  type?: EmailTemplateType;        → type?: string;
  previewText?: string;            → previewText?: string;
  tags?: string[];                 → tags?: string[];
  variables?: EmailVariable[];     → variables?: EmailVariableDto[];
}
```

### Backend Response → Frontend
```typescript
TemplateEmailResponseDto → EmailTemplateDto (via adapter)
```

## Testing

### 1. **Manual Testing**
1. Mở trang `/marketing/email/templates`
2. Click nút "Tạo Template"
3. Điền form với data:
   - Name: "Test Template"
   - Subject: "Test Subject"
   - HTML Content: "<h1>Hello {name}!</h1>"
   - Tags: ["test", "marketing"]
   - Variables: [{ name: "name", type: "TEXT", required: true }]
4. Submit form
5. Kiểm tra console logs để debug
6. Kiểm tra API call trong Network tab

### 2. **Expected API Call**
```http
POST /api/v1/marketing/template-emails
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Test Template",
  "subject": "Test Subject", 
  "content": "<h1>Hello {name}!</h1>",
  "type": "NEWSLETTER",
  "tags": ["test", "marketing"],
  "variables": [
    {
      "name": "name",
      "type": "TEXT",
      "required": true
    }
  ]
}
```

### 3. **Expected Response**
```json
{
  "code": 201,
  "message": "Tạo template email thành công",
  "data": {
    "id": 1,
    "userId": 123,
    "name": "Test Template",
    "subject": "Test Subject",
    "content": "<h1>Hello {name}!</h1>",
    "tags": ["test", "marketing"],
    "placeholders": ["name"],
    "status": "DRAFT",
    "createdAt": 1703123456,
    "updatedAt": 1703123456
  }
}
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Kiểm tra JWT token trong localStorage
   - Đảm bảo user đã login

2. **400 Bad Request**
   - Kiểm tra data validation
   - Xem console logs để debug data mapping

3. **404 Not Found**
   - Kiểm tra endpoint path
   - Đảm bảo backend API đã được deploy

4. **500 Internal Server Error**
   - Kiểm tra backend logs
   - Kiểm tra database connection

### Debug Steps

1. **Check Console Logs**
   ```javascript
   // Logs từ useCreateEmailTemplateAdapter
   🚀 [useCreateEmailTemplateAdapter] Creating template with data: {...}
   
   // Logs từ EmailTemplateAdapterService
   🔄 [EmailTemplateAdapterService] Mapping frontend to backend: {...}
   
   // Success/Error logs
   ✅ [useCreateEmailTemplateAdapter] Template created successfully: {...}
   ❌ [useCreateEmailTemplateAdapter] Failed to create template: {...}
   ```

2. **Check Network Tab**
   - Verify API endpoint: `/api/v1/marketing/template-emails`
   - Check request payload
   - Check response status and data

3. **Check Redux DevTools**
   - Verify TanStack Query cache updates
   - Check query invalidation

## Next Steps

1. **Test với real backend API**
2. **Thêm error handling cho specific error codes**
3. **Thêm validation cho file uploads (nếu có)**
4. **Thêm preview functionality**
5. **Thêm duplicate template functionality**

## Notes

- API integration đã hoàn thành theo đúng 3-layer pattern
- Data mapping đã được test và validate
- Form component đã sẵn sàng sử dụng
- Cần test với real backend để verify hoàn toàn
