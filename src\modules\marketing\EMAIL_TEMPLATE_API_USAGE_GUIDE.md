# Hướng Dẫn Sử Dụng Email Template API

## Tổng Quan

API tạo template email đã được đấu nối thành công vào trang `/marketing/email/templates` theo đúng quy tắc RedAI Frontend và backend specification.

## Cách Sử Dụng

### 1. **T<PERSON><PERSON> Trang Templates**
```
URL: /marketing/email/templates
```

### 2. **Tạo Template Mới**
1. Click nút **"Tạo Template"** trên MenuIconBar
2. Form slide-in sẽ hiện ra từ bên phải
3. Điền thông tin template:
   - **Tên template** (bắt buộc)
   - **Tiêu đề email** (bắt buộc) 
   - **Nội dung HTML** (bắt buộc)
   - **Nội dung text** (tùy chọn)
   - **Loại template** (NEWSLETTER, PROMOTIONAL, etc.)
   - **Preview text** (tù<PERSON> chọn)
   - **Tags** (tù<PERSON> chọn)
   - **Variables** (tù<PERSON> chọn)

### 3. **Quản Lý Variables**
- Click **"Thêm biến"** để thêm variable mới
- Mỗi variable có:
  - **Tên**: Tên biến (vd: customer_name)
  - **Loại**: TEXT, NUMBER, DATE, URL, IMAGE
  - **Giá trị mặc định**: Giá trị default
  - **Bắt buộc**: Có/Không
  - **Mô tả**: Mô tả variable

### 4. **Preview Template**
- Chuyển đổi giữa **Design Mode** và **Code Mode**
- Design Mode: Sử dụng EmailBuilder
- Code Mode: Chỉnh sửa HTML trực tiếp

## API Endpoints

### Create Template
```http
POST /api/v1/marketing/template-emails
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Template Name",
  "subject": "Email Subject",
  "content": "<html>...</html>",
  "textContent": "Plain text version",
  "type": "NEWSLETTER",
  "previewText": "Preview text",
  "tags": ["tag1", "tag2"],
  "variables": [
    {
      "name": "customer_name",
      "type": "TEXT",
      "required": true,
      "defaultValue": "Khách hàng",
      "description": "Tên khách hàng"
    }
  ]
}
```

### Response
```json
{
  "code": 201,
  "message": "Tạo template email thành công",
  "data": {
    "id": 1,
    "userId": 123,
    "name": "Template Name",
    "subject": "Email Subject",
    "content": "<html>...</html>",
    "tags": ["tag1", "tag2"],
    "placeholders": ["customer_name"],
    "status": "DRAFT",
    "createdAt": 1703123456,
    "updatedAt": 1703123456
  }
}
```

## Hooks Sử Dụng

### 1. **useCreateEmailTemplateAdapter**
```typescript
import { useCreateEmailTemplateAdapter } from '@/modules/marketing';

const createTemplate = useCreateEmailTemplateAdapter();

// Sử dụng
createTemplate.mutate({
  name: 'New Template',
  subject: 'Subject',
  htmlContent: '<h1>Content</h1>',
  tags: ['marketing'],
  variables: [{ name: 'userName', type: 'TEXT', required: true }]
});
```

### 2. **useEmailTemplatesAdapter**
```typescript
import { useEmailTemplatesAdapter } from '@/modules/marketing';

const { data, isLoading } = useEmailTemplatesAdapter({
  page: 1,
  limit: 10,
  search: 'newsletter'
});
```

## Components Sử Dụng

### 1. **CreateEmailTemplateForm**
```typescript
import { CreateEmailTemplateForm } from '@/modules/marketing';

<CreateEmailTemplateForm 
  onSuccess={() => console.log('Created!')}
  onCancel={() => console.log('Cancelled')}
/>
```

### 2. **EmailTemplatesPage**
```typescript
import { EmailTemplatesPage } from '@/modules/marketing';

// Trang đã được cấu hình đầy đủ với:
// - Table hiển thị templates
// - Search và filter
// - Create form
// - Preview functionality
```

## Testing

### 1. **Manual Testing**
```javascript
// Mở browser console tại /marketing/email/templates
import { runAllTests } from '@/modules/marketing';

// Chạy tất cả tests
runAllTests();

// Hoặc test từng function
testCreateEmailTemplate();
testGetEmailTemplates();
testGetEmailTemplateStatistics();
```

### 2. **Test Data**
```javascript
import { testTemplateData } from '@/modules/marketing';
console.log(testTemplateData);
```

## Troubleshooting

### 1. **Lỗi 401 Unauthorized**
- Kiểm tra JWT token
- Đảm bảo user đã login

### 2. **Lỗi 400 Bad Request**
- Kiểm tra validation data
- Xem console logs để debug

### 3. **Lỗi 404 Not Found**
- Kiểm tra endpoint path
- Đảm bảo backend API available

### 4. **Form không submit**
- Kiểm tra validation errors
- Xem formRef.current?.getErrors()

## Debug Logs

Hệ thống có logging chi tiết:

```javascript
// Hook logs
🚀 [useCreateEmailTemplateAdapter] Creating template with data: {...}
✅ [useCreateEmailTemplateAdapter] Template created successfully: {...}

// Adapter logs  
🔄 [EmailTemplateAdapterService] Mapping frontend to backend: {...}

// API logs
🔍 [useEmailTemplatesAdapter] Called with query: {...}
🚀 [API CALL] EmailTemplateAdapterService.getEmailTemplates
```

## Best Practices

### 1. **Data Validation**
- Luôn validate data trước khi submit
- Sử dụng useFormErrors để hiển thị lỗi

### 2. **Error Handling**
- Sử dụng try-catch trong async functions
- Hiển thị notification cho user

### 3. **Performance**
- Sử dụng TanStack Query cache
- Invalidate cache sau khi create/update

### 4. **UX**
- Hiển thị loading states
- Provide feedback cho user actions
- Sử dụng SlideInForm thay vì modal

## Cấu Trúc Files

```
src/modules/marketing/
├── components/email/CreateEmailTemplateForm.tsx
├── pages/email/EmailTemplatesPage.tsx
├── hooks/email/useEmailTemplatesAdapter.ts
├── services/
│   ├── template-email.service.ts
│   ├── template-email-business.service.ts
│   └── email-template-adapter.service.ts
├── types/template-email.types.ts
└── test-email-template-api.ts
```

## Next Steps

1. **Test với real backend API**
2. **Thêm update/delete functionality**
3. **Thêm bulk operations**
4. **Thêm template preview với variables**
5. **Thêm template duplication**
