# Marketing Overview API Integration

## Tổng quan

Tài liệu này mô tả việc tích hợp 2 API mới cho Marketing Overview vào EmailOverviewPage:

1. **GET /marketing/overview** - <PERSON><PERSON><PERSON> thông tin overview marketing
2. **GET /marketing/overview/recent-templates** - L<PERSON><PERSON> danh sách 5 templates gần đây nhất

## Cấu trúc API

### 1. Marketing Overview API

**Endpoint**: `GET /marketing/overview`

**Response**:
```typescript
interface MarketingOverviewResponseDto {
  totalTemplates: number;      // Tổng số templates
  openRate: number;           // Tỷ lệ mở email (%)
  clickRate: number;          // Tỷ lệ click email (%)
  totalEmailsSent: number;    // Tổng số email đã gửi
}
```

### 2. Recent Templates API

**Endpoint**: `GET /marketing/overview/recent-templates`

**Response**:
```typescript
interface RecentTemplatesResponseDto {
  templates: RecentTemplateDto[];
}

interface RecentTemplateDto {
  id: string;
  name: string;
  subject: string;
  status: 'ACTIVE' | 'DRAFT';
  createdAt: string;
  updatedAt: string;
}
```

## Cấu trúc Implementation

### 1. Types (statistics.types.ts)
- Định nghĩa `MarketingOverviewResponseDto`
- Định nghĩa `RecentTemplateDto` và `RecentTemplatesResponseDto`
- Định nghĩa response types với `ApiResponseDto`

### 2. Service Layer (3-Layer Pattern)

#### Layer 1: Raw API Calls
**File**: `marketing-overview.service.ts`
- `MarketingOverviewService.getOverview()`
- `MarketingOverviewService.getRecentTemplates()`

#### Layer 2: Business Logic
**File**: `marketing-overview-business.service.ts`
- `MarketingOverviewBusinessService.getMarketingOverview()`
- `MarketingOverviewBusinessService.getRecentTemplates()`
- Validation và business rules

#### Layer 3: React Query Hooks
**File**: `useMarketingOverview.ts`
- `useMarketingOverview()` - Hook cho overview stats
- `useRecentTemplates()` - Hook cho recent templates
- Query keys và caching configuration

### 3. Integration trong EmailOverviewPage

#### Thay đổi chính:
1. **Import hooks mới**:
   ```typescript
   import { useMarketingOverview, useRecentTemplates } from '../../hooks/useMarketingOverview';
   ```

2. **Sử dụng API data**:
   ```typescript
   const { data: overviewData, isLoading: isOverviewLoading } = useMarketingOverview();
   const { data: recentTemplatesData, isLoading: isRecentTemplatesLoading } = useRecentTemplates();
   ```

3. **Combine data với fallback**:
   ```typescript
   const stats = {
     totalTemplates: overviewData?.totalTemplates || templatesData?.meta.totalItems || 0,
     emailsSentThisMonth: overviewData?.totalEmailsSent || 0,
     averageOpenRate: overviewData?.openRate || 0,
     averageClickRate: overviewData?.clickRate || 0,
   };
   ```

4. **Hiển thị recent templates từ API**:
   ```typescript
   {recentTemplatesData?.templates.map((template: RecentTemplateDto) => (
     // Template card component
   ))}
   ```

## Query Keys Structure

```typescript
export const MARKETING_OVERVIEW_QUERY_KEYS = {
  all: ['marketing', 'overview'] as const,
  overview: () => [...MARKETING_OVERVIEW_QUERY_KEYS.all, 'stats'] as const,
  recentTemplates: () => [...MARKETING_OVERVIEW_QUERY_KEYS.all, 'recent-templates'] as const,
};
```

## Caching Strategy

- **Overview stats**: 5 phút stale time
- **Recent templates**: 2 phút stale time
- **Retry**: 2 lần khi có lỗi

## Export Structure

Tất cả types, services và hooks đã được export từ:
- `src/modules/marketing/index.ts`
- `src/modules/marketing/services/index.ts`
- `src/modules/marketing/hooks/index.ts`

## Lợi ích

1. **Dữ liệu thực tế**: Thay thế mock data bằng API calls thực
2. **Performance**: Caching và optimized loading states
3. **Maintainability**: 3-layer pattern dễ maintain và test
4. **Type Safety**: Full TypeScript support
5. **Error Handling**: Proper error handling và fallbacks

## Testing

Để test integration:

1. **API calls**: Test service layers
2. **Business logic**: Test business service validation
3. **React hooks**: Test hooks với React Query Testing Library
4. **UI Integration**: Test component với mock API responses

## Fallback Strategy

Khi API không available:
- Overview stats fallback về 0 hoặc existing template count
- Recent templates fallback về empty state với create button
- Loading states hiển thị skeleton components
