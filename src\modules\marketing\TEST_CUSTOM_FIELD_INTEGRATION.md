# Test Custom Field Integration

## Test Cases

### 1. Test API Call với sortBy

**Test 1: sortBy = 'id' (should work)**
```bash
curl -X GET "http://localhost:3000/v1/user/marketing/audience-custom-fields?page=1&limit=20&sortBy=id&sortDirection=ASC" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Test 2: sortBy = 'displayName' (might fail)**
```bash
curl -X GET "http://localhost:3000/v1/user/marketing/audience-custom-fields?page=1&limit=20&sortBy=displayName&sortDirection=ASC" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Test 3: No sortBy (fallback)**
```bash
curl -X GET "http://localhost:3000/v1/user/marketing/audience-custom-fields?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Test Frontend Integration

**Test trong browser console:**
```javascript
// Test loadAllFieldOptions function
const testParams = { search: '', page: 1, limit: 20 };
loadAllFieldOptions(testParams).then(result => {
  console.log('Combined fields result:', result);
  console.log('Static fields count:', result.items.filter(item => item.description === 'Trường hệ thống').length);
  console.log('Custom fields count:', result.items.filter(item => item.description !== 'Trường hệ thống').length);
});
```

### 3. Test Error Handling

**Test với invalid sortBy:**
```javascript
// Trong CustomFieldBusinessService
const invalidParams = {
  page: 1,
  limit: 20,
  sortBy: 'invalid_field',
  sortDirection: 'ASC'
};

CustomFieldService.getCustomFields(invalidParams)
  .then(result => console.log('Success:', result))
  .catch(error => console.log('Expected error:', error));
```

### 4. Test UI Components

**Test dropdown behavior:**
1. Mở Segment Form
2. Click vào dropdown "Chọn Trường"
3. Verify:
   - Static fields hiển thị ở đầu
   - Custom fields load từ API
   - Search hoạt động
   - Pagination hoạt động
   - Dropdown không bị che bởi Card

**Test search functionality:**
1. Nhập "email" vào search box
2. Verify: Static field "Email" hiển thị
3. Nhập tên custom field
4. Verify: Custom field matching hiển thị

### 5. Test Error Recovery

**Scenario: API completely fails**
```javascript
// Mock API failure
const originalGetCustomFields = CustomFieldService.getCustomFields;
CustomFieldService.getCustomFields = () => Promise.reject(new Error('API Error'));

// Test fallback behavior
loadAllFieldOptions({ page: 1, limit: 20 })
  .then(result => {
    console.log('Fallback result:', result);
    // Should only contain static fields
    console.log('Should be static fields only:', result.items.length === AVAILABLE_FIELDS.length);
  });

// Restore original function
CustomFieldService.getCustomFields = originalGetCustomFields;
```

## Expected Results

### Success Cases:
1. **API with sortBy='id'**: Should return custom fields successfully
2. **API without sortBy**: Should return custom fields successfully  
3. **Combined fields**: Should show static fields + custom fields
4. **Search**: Should filter both static and custom fields
5. **Pagination**: Should work for custom fields

### Error Cases:
1. **Invalid sortBy**: Should fallback to simple params
2. **API failure**: Should fallback to static fields only
3. **Network error**: Should show error message but not crash

## Debug Commands

**Enable debug logging:**
```javascript
// In browser console
localStorage.setItem('debug', 'marketing:*');
```

**Check API response:**
```javascript
// In Network tab, look for:
// GET /v1/user/marketing/audience-custom-fields
// Check response structure and field names
```

**Verify field mapping:**
```javascript
// In console after API call
console.log('Raw API response:', response);
console.log('Transformed items:', transformedItems);
```

## Performance Tests

**Test large dataset:**
```bash
# Test with large limit
curl -X GET "http://localhost:3000/v1/user/marketing/audience-custom-fields?page=1&limit=100" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Test search performance:**
```javascript
// Measure search response time
const startTime = performance.now();
loadAllFieldOptions({ search: 'test', page: 1, limit: 20 })
  .then(() => {
    const endTime = performance.now();
    console.log(`Search took ${endTime - startTime} milliseconds`);
  });
```

## Validation Checklist

- [ ] API calls work without sortBy parameter
- [ ] API calls work with sortBy='id'
- [ ] Error handling works for invalid sortBy
- [ ] Static fields always display
- [ ] Custom fields load and display correctly
- [ ] Search works for both field types
- [ ] Pagination works for custom fields
- [ ] Dropdown has proper z-index
- [ ] Field selection updates form state
- [ ] Translation keys work correctly
- [ ] Performance is acceptable (< 2s for initial load)
- [ ] Error messages are user-friendly
- [ ] Fallback behavior works when API fails
