import React, { useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Upload, FileSpreadsheet, Download, AlertCircle, CheckCircle } from 'lucide-react';
import * as XLSX from 'xlsx';
import {
  Card,
  Typography,
  Button,
  Alert,
  Table,
} from '@/shared/components/common';
import type { EmailTemplateDto } from '../../types/email.types';

interface ExcelVariableImporterProps {
  template: EmailTemplateDto;
  onImport: (variables: Record<string, string>) => void;
  onClose: () => void;
}

interface ExcelData {
  headers: string[];
  rows: string[][];
}

interface VariableMapping {
  templateVariable: string;
  excelColumn: string;
  sampleValue?: string;
}

/**
 * Component import biến từ Excel cho email template
 */
export function ExcelVariableImporter({
  template,
  onImport,
  onClose,
}: ExcelVariableImporterProps) {
  const { t } = useTranslation(['marketing', 'common']);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // State
  const [excelData, setExcelData] = useState<ExcelData | null>(null);
  const [variableMappings, setVariableMappings] = useState<VariableMapping[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  // Initialize variable mappings when template changes
  React.useEffect(() => {
    const mappings: VariableMapping[] = template.variables.map(variable => ({
      templateVariable: variable.name,
      excelColumn: '',
    }));
    setVariableMappings(mappings);
  }, [template.variables]);

  // Handle file upload
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsProcessing(true);
    setErrors([]);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // Convert to JSON with header
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: '',
        }) as string[][];

        if (jsonData.length === 0) {
          setErrors([t('marketing:email.template.variables.import.emptyFile', 'File Excel trống')]);
          return;
        }

        const headers = jsonData[0] || [];
        const rows = jsonData.slice(1);

        setExcelData({ headers, rows });

        // Auto-map columns based on name similarity
        const newMappings = variableMappings.map(mapping => {
          const matchingHeader = headers.find(header => 
            header.toLowerCase().includes(mapping.templateVariable.toLowerCase()) ||
            mapping.templateVariable.toLowerCase().includes(header.toLowerCase())
          );
          
          return {
            ...mapping,
            excelColumn: matchingHeader || '',
            sampleValue: matchingHeader && rows.length > 0 
              ? rows[0][headers.indexOf(matchingHeader)] 
              : undefined,
          };
        });
        
        setVariableMappings(newMappings);
      } catch (error) {
        console.error('Error parsing Excel file:', error);
        setErrors([t('marketing:email.template.variables.import.parseError', 'Lỗi đọc file Excel')]);
      } finally {
        setIsProcessing(false);
      }
    };

    reader.readAsArrayBuffer(file);
  }, [variableMappings, t]);

  // Handle mapping change
  const handleMappingChange = useCallback((templateVariable: string, excelColumn: string) => {
    setVariableMappings(prev => prev.map(mapping => 
      mapping.templateVariable === templateVariable 
        ? { 
            ...mapping, 
            excelColumn,
            sampleValue: excelColumn && excelData 
              ? excelData.rows[0]?.[excelData.headers.indexOf(excelColumn)]
              : undefined
          }
        : mapping
    ));
  }, [excelData]);

  // Process import
  const handleImport = useCallback(() => {
    if (!excelData) return;

    const newErrors: string[] = [];
    const variables: Record<string, string> = {};

    // Validate mappings
    const requiredVariables = template.variables.filter(v => v.required);
    const mappedRequiredVariables = variableMappings.filter(m => 
      requiredVariables.some(v => v.name === m.templateVariable) && m.excelColumn
    );

    if (mappedRequiredVariables.length < requiredVariables.length) {
      newErrors.push(t('marketing:email.template.variables.import.missingRequired', 'Chưa map đủ biến bắt buộc'));
    }

    if (newErrors.length > 0) {
      setErrors(newErrors);
      return;
    }

    // Process first row of data (for now, we'll use the first row as sample)
    // In a real implementation, you might want to process multiple rows
    if (excelData.rows.length > 0) {
      const firstRow = excelData.rows[0];
      
      variableMappings.forEach(mapping => {
        if (mapping.excelColumn) {
          const columnIndex = excelData.headers.indexOf(mapping.excelColumn);
          if (columnIndex >= 0 && columnIndex < firstRow.length) {
            variables[mapping.templateVariable] = firstRow[columnIndex] || '';
          }
        }
      });
    }

    onImport(variables);
  }, [excelData, variableMappings, template.variables, onImport, t]);

  // Download template
  const handleDownloadTemplate = useCallback(() => {
    const templateData = [
      // Header row with variable names
      template.variables.map(v => v.name),
      // Sample row with descriptions or default values
      template.variables.map(v => v.description || v.defaultValue || `Sample ${v.name}`),
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Variables');
    
    XLSX.writeFile(workbook, `${template.name}_variables_template.xlsx`);
  }, [template]);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5 text-primary" />
            <Typography variant="h6">
              {t('marketing:email.template.variables.import.title', 'Import biến từ Excel')}
            </Typography>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-6">
          {/* Instructions */}
          <Alert
            type="info"
            message={t('marketing:email.template.variables.import.instructions', 'Hướng dẫn')}
            description={
              <div className="space-y-2">
                <p>{t('marketing:email.template.variables.import.step1', '1. Tải template Excel hoặc upload file Excel có sẵn')}</p>
                <p>{t('marketing:email.template.variables.import.step2', '2. Map các cột trong Excel với biến trong template')}</p>
                <p>{t('marketing:email.template.variables.import.step3', '3. Xem trước và import dữ liệu')}</p>
              </div>
            }
          />

          {/* Actions */}
          <div className="flex gap-2 flex-wrap">
            <Button
              variant="outline"
              onClick={handleDownloadTemplate}
            >
              <Download className="h-4 w-4 mr-1" />
              {t('marketing:email.template.variables.import.downloadTemplate', 'Tải template Excel')}
            </Button>
            
            <Button
              variant="primary"
              onClick={() => fileInputRef.current?.click()}
              disabled={isProcessing}
            >
              <Upload className="h-4 w-4 mr-1" />
              {t('marketing:email.template.variables.import.uploadFile', 'Upload file Excel')}
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>

          {/* Errors */}
          {errors.length > 0 && (
            <Alert
              type="error"
              message={t('marketing:email.template.variables.import.errors', 'Có lỗi xảy ra')}
              description={
                <ul className="space-y-1">
                  {errors.map((error, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <AlertCircle className="h-3 w-3" />
                      {error}
                    </li>
                  ))}
                </ul>
              }
            />
          )}

          {/* Excel Data Preview */}
          {excelData && (
            <div className="space-y-4">
              <Typography variant="h6">
                {t('marketing:email.template.variables.import.preview', 'Xem trước dữ liệu Excel')}
              </Typography>
              
              <div className="border rounded-lg overflow-hidden">
                <div className="max-h-40 overflow-y-auto">
                  <Table
                    columns={excelData.headers.map(header => ({
                      key: header,
                      title: header,
                      dataIndex: header,
                    }))}
                    data={excelData.rows.slice(0, 5).map((row, index) => {
                      const rowData: Record<string, string> = { id: index.toString() };
                      excelData.headers.forEach((header, colIndex) => {
                        rowData[header] = row[colIndex] || '';
                      });
                      return rowData;
                    })}
                    pagination={false}
                  />
                </div>
                {excelData.rows.length > 5 && (
                  <div className="p-2 bg-muted/50 text-center">
                    <Typography variant="caption" color="muted">
                      {t('marketing:email.template.variables.import.moreRows', 'Và {{count}} dòng khác...', { count: excelData.rows.length - 5 })}
                    </Typography>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Variable Mapping */}
          {excelData && (
            <div className="space-y-4">
              <Typography variant="h6">
                {t('marketing:email.template.variables.import.mapping', 'Map biến với cột Excel')}
              </Typography>
              
              <div className="space-y-3">
                {variableMappings.map((mapping) => {
                  const variable = template.variables.find(v => v.name === mapping.templateVariable);
                  return (
                    <div key={mapping.templateVariable} className="flex items-center gap-4 p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Typography variant="body2" weight="medium">
                            {mapping.templateVariable}
                          </Typography>
                          {variable?.required && <span className="text-red-500">*</span>}
                        </div>
                        {variable?.description && (
                          <Typography variant="caption" color="muted">
                            {variable.description}
                          </Typography>
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <select
                          value={mapping.excelColumn}
                          onChange={(e) => handleMappingChange(mapping.templateVariable, e.target.value)}
                          className="w-full p-2 border rounded-md bg-background"
                        >
                          <option value="">{t('marketing:email.template.variables.import.selectColumn', 'Chọn cột...')}</option>
                          {excelData.headers.map(header => (
                            <option key={header} value={header}>{header}</option>
                          ))}
                        </select>
                      </div>
                      
                      <div className="flex-1">
                        {mapping.sampleValue && (
                          <div className="p-2 bg-muted/30 rounded text-sm">
                            <Typography variant="caption" color="muted">Sample:</Typography>
                            <Typography variant="body2">{mapping.sampleValue}</Typography>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              {t('common:cancel', 'Hủy')}
            </Button>
            {excelData && (
              <Button onClick={handleImport} disabled={isProcessing}>
                <CheckCircle className="h-4 w-4 mr-1" />
                {t('marketing:email.template.variables.import.import', 'Import')}
              </Button>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
}

export default ExcelVariableImporter;
