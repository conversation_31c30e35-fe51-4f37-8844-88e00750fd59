import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  ResponsiveGrid,
  EmptyState,
  SearchBar,
  Select,
  Modal,
} from '@/shared/components/common';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';
import FacebookCampaignCard from './FacebookCampaignCard';
import CreateCampaignForm from './CreateCampaignForm';

interface FacebookCampaignManagerProps {
  /**
   * Show search and filters
   */
  showFilters?: boolean;
  
  /**
   * Show create campaign button
   */
  showCreateButton?: boolean;
  
  /**
   * Grid layout columns
   */
  gridColumns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  
  /**
   * Account ID filter
   */
  accountId?: string;
  
  /**
   * Callback khi view campaign
   */
  onViewCampaign?: (campaignId: string) => void;
  
  /**
   * Callback khi edit campaign
   */
  onEditCampaign?: (campaignId: string) => void;
  
  /**
   * Callback khi create campaign
   */
  onCreateCampaign?: () => void;
}

/**
 * Facebook Campaign Manager Component
 * Component tổng hợp để quản lý chiến dịch Facebook Ads
 */
const FacebookCampaignManager: React.FC<FacebookCampaignManagerProps> = ({
  showFilters = true,
  showCreateButton = true,
  gridColumns = { xs: 1, sm: 1, md: 2, lg: 2, xl: 3 },
  accountId,
  onViewCampaign,
  onEditCampaign,
  onCreateCampaign,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [objectiveFilter, setObjectiveFilter] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  
  const { isAuthenticated } = useFacebookAuth();

  // Mock data for demo - in real app, this would come from API
  const mockCampaigns = useMemo(() => [
    {
      id: '1',
      campaignId: 'camp_123456789',
      name: 'Summer Sale 2024',
      objective: 'CONVERSIONS',
      status: 'active' as const,
      budget: 1000000,
      budgetType: 'daily' as const,
      spend: 750000,
      impressions: 45230,
      clicks: 1250,
      ctr: 2.76,
      cpc: 600,
      startDate: '2024-06-01T00:00:00Z',
      endDate: '2024-06-30T23:59:59Z',
      accountId: 'act_123456789',
      accountName: 'RedAI Marketing Account',
    },
    {
      id: '2',
      campaignId: 'camp_987654321',
      name: 'Brand Awareness Q2',
      objective: 'BRAND_AWARENESS',
      status: 'paused' as const,
      budget: 500000,
      budgetType: 'daily' as const,
      spend: 2100000,
      impressions: 125430,
      clicks: 2340,
      ctr: 1.87,
      cpc: 897,
      startDate: '2024-04-01T00:00:00Z',
      accountId: 'act_123456789',
      accountName: 'RedAI Marketing Account',
    },
    {
      id: '3',
      campaignId: 'camp_456789123',
      name: 'Product Launch Campaign',
      objective: 'TRAFFIC',
      status: 'active' as const,
      budget: ********,
      budgetType: 'lifetime' as const,
      spend: 8500000,
      impressions: 89650,
      clicks: 3420,
      ctr: 3.82,
      cpc: 2485,
      startDate: '2024-05-15T00:00:00Z',
      endDate: '2024-07-15T23:59:59Z',
      accountId: 'act_987654321',
      accountName: 'E-commerce Ads Account',
    },
  ], []);

  // Filter campaigns
  const filteredCampaigns = useMemo(() => {
    return mockCampaigns.filter(campaign => {
      const matchesSearch = campaign.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           campaign.campaignId.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || campaign.status === statusFilter;
      const matchesObjective = objectiveFilter === 'all' || campaign.objective === objectiveFilter;
      const matchesAccount = !accountId || campaign.accountId === accountId;
      
      return matchesSearch && matchesStatus && matchesObjective && matchesAccount;
    });
  }, [mockCampaigns, searchQuery, statusFilter, objectiveFilter, accountId]);

  const statusOptions = [
    { value: 'all', label: t('common:filter.all', 'Tất cả') },
    { value: 'active', label: t('common:status.active', 'Hoạt động') },
    { value: 'paused', label: t('common:status.paused', 'Tạm dừng') },
    { value: 'archived', label: t('common:status.archived', 'Lưu trữ') },
  ];

  const objectiveOptions = [
    { value: 'all', label: t('common:filter.all', 'Tất cả') },
    { value: 'CONVERSIONS', label: t('marketing:facebookAds.objectives.conversions', 'Chuyển đổi') },
    { value: 'TRAFFIC', label: t('marketing:facebookAds.objectives.traffic', 'Lưu lượng') },
    { value: 'BRAND_AWARENESS', label: t('marketing:facebookAds.objectives.brandAwareness', 'Nhận diện thương hiệu') },
    { value: 'REACH', label: t('marketing:facebookAds.objectives.reach', 'Tiếp cận') },
    { value: 'ENGAGEMENT', label: t('marketing:facebookAds.objectives.engagement', 'Tương tác') },
  ];

  const handleViewCampaign = (campaignId: string) => {
    onViewCampaign?.(campaignId);
  };

  const handleEditCampaign = (campaignId: string) => {
    onEditCampaign?.(campaignId);
  };

  const handleToggleCampaign = async (campaignId: string, currentStatus: string) => {
    console.log('Toggle campaign:', campaignId, currentStatus);
    // TODO: Implement campaign toggle logic
  };

  const handleDuplicateCampaign = async (campaignId: string) => {
    console.log('Duplicate campaign:', campaignId);
    // TODO: Implement campaign duplication logic
  };

  const handleCreateCampaign = () => {
    if (onCreateCampaign) {
      onCreateCampaign();
    } else {
      setShowCreateModal(true);
    }
  };

  const handleSubmitCreateCampaign = async (data: unknown) => {
    console.log('Create campaign:', data);
    // TODO: Implement campaign creation logic
    setShowCreateModal(false);
  };

  const handleRefreshCampaigns = () => {
    console.log('Refresh campaigns');
    // TODO: Implement refresh logic
  };

  // Show not authenticated state
  if (!isAuthenticated) {
    return (
      <Card className="p-8 text-center">
        <EmptyState
          icon="facebook"
          title={t('marketing:facebookAds.campaigns.notConnected.title', 'Chưa kết nối Facebook')}
          description={t('marketing:facebookAds.campaigns.notConnected.description', 'Kết nối tài khoản Facebook để quản lý chiến dịch quảng cáo')}
        />
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Typography variant="h6">
            {t('marketing:facebookAds.campaigns.manager.title', 'Chiến dịch Facebook Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('marketing:facebookAds.campaigns.manager.description', 'Quản lý và tối ưu hóa các chiến dịch quảng cáo Facebook')}
          </Typography>
        </div>
        
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleRefreshCampaigns}
          >
            <Icon name="refresh-cw" className="mr-2" />
            {t('common:button.refresh', 'Làm mới')}
          </Button>
          
          {showCreateButton && (
            <Button
              variant="primary"
              onClick={handleCreateCampaign}
            >
              <Icon name="plus" className="mr-2" />
              {t('marketing:facebookAds.campaigns.create', 'Tạo chiến dịch')}
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && filteredCampaigns.length > 0 && (
        <Card className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder={t('marketing:facebookAds.campaigns.search', 'Tìm kiếm chiến dịch...')}
              />
            </div>
            <div className="flex gap-2">
              <Select
                value={statusFilter}
                onChange={(value) => setStatusFilter(value as string)}
                options={statusOptions}
                placeholder={t('marketing:facebookAds.campaigns.filterStatus', 'Lọc theo trạng thái')}
              />
              <Select
                value={objectiveFilter}
                onChange={(value) => setObjectiveFilter(value as string)}
                options={objectiveOptions}
                placeholder={t('marketing:facebookAds.campaigns.filterObjective', 'Lọc theo mục tiêu')}
              />
            </div>
          </div>
        </Card>
      )}

      {/* Campaigns Grid */}
      {filteredCampaigns.length === 0 ? (
        <Card className="p-8 text-center">
          <EmptyState
            icon="campaign"
            title={
              searchQuery || statusFilter !== 'all' || objectiveFilter !== 'all'
                ? t('marketing:facebookAds.campaigns.noResults.title', 'Không tìm thấy chiến dịch')
                : t('marketing:facebookAds.campaigns.empty.title', 'Chưa có chiến dịch nào')
            }
            description={
              searchQuery || statusFilter !== 'all' || objectiveFilter !== 'all'
                ? t('marketing:facebookAds.campaigns.noResults.description', 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm')
                : t('marketing:facebookAds.campaigns.empty.description', 'Tạo chiến dịch đầu tiên để bắt đầu quảng cáo trên Facebook')
            }
            actions={
              searchQuery || statusFilter !== 'all' || objectiveFilter !== 'all' ? (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('all');
                    setObjectiveFilter('all');
                  }}
                >
                  <Icon name="x" className="mr-2" />
                  {t('common:button.clearFilters', 'Xóa bộ lọc')}
                </Button>
              ) : showCreateButton ? (
                <Button
                  variant="primary"
                  onClick={handleCreateCampaign}
                >
                  <Icon name="plus" className="mr-2" />
                  {t('marketing:facebookAds.campaigns.createFirst', 'Tạo chiến dịch đầu tiên')}
                </Button>
              ) : undefined
            }
          />
        </Card>
      ) : (
        <ResponsiveGrid maxColumns={gridColumns}>
          {filteredCampaigns.map((campaign) => (
            <FacebookCampaignCard
              key={campaign.id}
              campaign={campaign}
              showMetrics
              onView={handleViewCampaign}
              onEdit={handleEditCampaign}
              onToggle={handleToggleCampaign}
              onDuplicate={handleDuplicateCampaign}
            />
          ))}
        </ResponsiveGrid>
      )}

      {/* Summary */}
      {filteredCampaigns.length > 0 && (
        <Card className="p-4">
          <div className="flex justify-between items-center text-sm">
            <Typography variant="body2" className="text-muted-foreground">
              {t('marketing:facebookAds.campaigns.summary.total', 'Tổng cộng')}: {filteredCampaigns.length} {t('marketing:facebookAds.campaigns.summary.campaigns', 'chiến dịch')}
            </Typography>
            
            <div className="flex space-x-4">
              <Typography variant="body2" className="text-green-600">
                {t('common:status.active', 'Hoạt động')}: {filteredCampaigns.filter(c => c.status === 'active').length}
              </Typography>
              <Typography variant="body2" className="text-yellow-600">
                {t('common:status.paused', 'Tạm dừng')}: {filteredCampaigns.filter(c => c.status === 'paused').length}
              </Typography>
              <Typography variant="body2" className="text-gray-600">
                {t('common:status.archived', 'Lưu trữ')}: {filteredCampaigns.filter(c => (c.status as string) === 'archived').length}
              </Typography>
            </div>
          </div>
        </Card>
      )}

      {/* Create Campaign Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={t('marketing:facebookAds.campaigns.create', 'Tạo chiến dịch mới')}
        size="lg"
      >
        <CreateCampaignForm
          onSubmit={handleSubmitCreateCampaign}
          onCancel={() => setShowCreateModal(false)}
        />
      </Modal>
    </div>
  );
};

export default FacebookCampaignManager;
