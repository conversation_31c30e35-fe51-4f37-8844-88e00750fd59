/**
 * Tik<PERSON>ok Ads Constants
 * Định nghĩa các constants cho TikTok Ads module
 */

/**
 * TikTok Ads Query Keys cho TanStack Query
 */

// Base keys
const TIKTOK_ADS_BASE_KEYS = ['tiktokAds'] as const;

// Accounts keys
const TIKTOK_ADS_ACCOUNTS_BASE = [...TIKTOK_ADS_BASE_KEYS, 'accounts'] as const;

// Campaigns keys
const TIKTOK_ADS_CAMPAIGNS_BASE = [...TIKTOK_ADS_BASE_KEYS, 'campaigns'] as const;

// Ad Groups keys
const TIKTOK_ADS_AD_GROUPS_BASE = [...TIKTOK_ADS_BASE_KEYS, 'adGroups'] as const;

// Creatives keys
const TIKTOK_ADS_CREATIVES_BASE = [...TIKTOK_ADS_BASE_KEYS, 'creatives'] as const;

// Audiences keys
const TIKTOK_ADS_AUDIENCES_BASE = [...TIKTOK_ADS_BASE_KEYS, 'audiences'] as const;

// Reports keys
const TIKTOK_ADS_REPORTS_BASE = [...TIKTOK_ADS_BASE_KEYS, 'reports'] as const;

export const TIKTOK_ADS_QUERY_KEYS = {
  // Base keys
  ALL: TIKTOK_ADS_BASE_KEYS,

  // Accounts
  ACCOUNTS: {
    ALL: TIKTOK_ADS_ACCOUNTS_BASE,
    LIST: (params: Record<string, unknown> = {}) => [...TIKTOK_ADS_ACCOUNTS_BASE, 'list', params] as const,
    DETAIL: (id: number) => [...TIKTOK_ADS_ACCOUNTS_BASE, 'detail', id] as const,
  },

  // Campaigns
  CAMPAIGNS: {
    ALL: TIKTOK_ADS_CAMPAIGNS_BASE,
    LIST: (params: Record<string, unknown> = {}) => [...TIKTOK_ADS_CAMPAIGNS_BASE, 'list', params] as const,
    DETAIL: (id: number) => [...TIKTOK_ADS_CAMPAIGNS_BASE, 'detail', id] as const,
  },

  // Ad Groups
  AD_GROUPS: {
    ALL: TIKTOK_ADS_AD_GROUPS_BASE,
    LIST: (params: Record<string, unknown>) => [...TIKTOK_ADS_AD_GROUPS_BASE, 'list', params] as const,
    DETAIL: (id: number) => [...TIKTOK_ADS_AD_GROUPS_BASE, 'detail', id] as const,
  },

  // Creatives
  CREATIVES: {
    ALL: TIKTOK_ADS_CREATIVES_BASE,
    LIST: (params: Record<string, unknown>) => [...TIKTOK_ADS_CREATIVES_BASE, 'list', params] as const,
    DETAIL: (id: number) => [...TIKTOK_ADS_CREATIVES_BASE, 'detail', id] as const,
  },

  // Audiences
  AUDIENCES: {
    ALL: TIKTOK_ADS_AUDIENCES_BASE,
    LIST: (params: Record<string, unknown>) => [...TIKTOK_ADS_AUDIENCES_BASE, 'list', params] as const,
    DETAIL: (id: number) => [...TIKTOK_ADS_AUDIENCES_BASE, 'detail', id] as const,
  },

  // Reports & Analytics
  REPORTS: {
    ALL: TIKTOK_ADS_REPORTS_BASE,
    OVERVIEW: (params: Record<string, unknown>) => [...TIKTOK_ADS_REPORTS_BASE, 'overview', params] as const,
    CAMPAIGN_PERFORMANCE: (params: Record<string, unknown>) => [...TIKTOK_ADS_REPORTS_BASE, 'campaignPerformance', params] as const,
    CREATIVE_PERFORMANCE: (params: Record<string, unknown>) => [...TIKTOK_ADS_REPORTS_BASE, 'creativePerformance', params] as const,
  },
} as const;

/**
 * TikTok Ads API Endpoints
 */
export const TIKTOK_ADS_ENDPOINTS = {
  // Accounts
  ACCOUNTS: '/api/v1/marketing/tiktok-ads/accounts',
  ACCOUNT_DETAIL: (id: number) => `/api/v1/marketing/tiktok-ads/accounts/${id}`,
  
  // Campaigns
  CAMPAIGNS: '/api/v1/marketing/tiktok-ads/campaigns',
  CAMPAIGN_DETAIL: (id: number) => `/api/v1/marketing/tiktok-ads/campaigns/${id}`,
  
  // Ad Groups
  AD_GROUPS: '/api/v1/marketing/tiktok-ads/ad-groups',
  AD_GROUP_DETAIL: (id: number) => `/api/v1/marketing/tiktok-ads/ad-groups/${id}`,
  
  // Creatives
  CREATIVES: '/api/v1/marketing/tiktok-ads/creatives',
  CREATIVE_DETAIL: (id: number) => `/api/v1/marketing/tiktok-ads/creatives/${id}`,
  
  // Audiences
  AUDIENCES: '/api/v1/marketing/tiktok-ads/audiences',
  AUDIENCE_DETAIL: (id: number) => `/api/v1/marketing/tiktok-ads/audiences/${id}`,
  
  // Reports
  REPORTS_OVERVIEW: '/api/v1/marketing/tiktok-ads/reports/overview',
  REPORTS_CAMPAIGNS: '/api/v1/marketing/tiktok-ads/reports/campaigns',
  REPORTS_CREATIVES: '/api/v1/marketing/tiktok-ads/reports/creatives',
} as const;

/**
 * TikTok Ads Default Values
 */
export const TIKTOK_ADS_DEFAULTS = {
  // Pagination
  PAGE_SIZE: 10,
  DEFAULT_PAGE: 1,
  
  // Budget
  MIN_DAILY_BUDGET: 20, // USD
  MIN_TOTAL_BUDGET: 50, // USD
  
  // Bid
  MIN_BID_AMOUNT: 0.01, // USD
  
  // Targeting
  MIN_AGE: 13,
  MAX_AGE: 65,
  
  // Creative
  MAX_TITLE_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 300,
  
  // Date ranges
  DEFAULT_DATE_RANGE_DAYS: 30,
} as const;

/**
 * TikTok Ads Status Colors cho UI
 */
export const TIKTOK_ADS_STATUS_COLORS = {
  // Account Status
  ACCOUNT: {
    ACTIVE: 'success',
    INACTIVE: 'warning',
    SUSPENDED: 'error',
    PENDING: 'info',
  },
  
  // Campaign Status
  CAMPAIGN: {
    ENABLED: 'success',
    PAUSED: 'warning',
    DELETED: 'error',
    PENDING: 'info',
  },
  
  // Creative Status
  CREATIVE: {
    ENABLED: 'success',
    PAUSED: 'warning',
    DELETED: 'error',
    UNDER_REVIEW: 'info',
    REJECTED: 'error',
  },
  
  // Audience Status
  AUDIENCE: {
    ACTIVE: 'success',
    INACTIVE: 'warning',
    PENDING: 'info',
  },
} as const;

/**
 * TikTok Ads Objective Labels cho UI
 */
export const TIKTOK_ADS_OBJECTIVE_LABELS = {
  REACH: 'Reach',
  TRAFFIC: 'Traffic',
  VIDEO_VIEWS: 'Video Views',
  LEAD_GENERATION: 'Lead Generation',
  CONVERSIONS: 'Conversions',
  APP_PROMOTION: 'App Promotion',
  CATALOG_SALES: 'Catalog Sales',
} as const;

/**
 * TikTok Ads Creative Type Labels cho UI
 */
export const TIKTOK_ADS_CREATIVE_TYPE_LABELS = {
  VIDEO: 'Video',
  IMAGE: 'Image',
  SPARK_AD: 'Spark Ad',
  COLLECTION: 'Collection',
} as const;

/**
 * TikTok Ads Validation Rules
 */
export const TIKTOK_ADS_VALIDATION = {
  ACCOUNT_NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 100,
  },
  CAMPAIGN_NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 100,
  },
  CREATIVE_TITLE: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 100,
  },
  CREATIVE_DESCRIPTION: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 300,
  },
  AUDIENCE_NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 100,
  },
} as const;
