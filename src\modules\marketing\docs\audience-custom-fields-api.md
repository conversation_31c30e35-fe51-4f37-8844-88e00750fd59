# Audience Custom Fields API Integration

## Tổng quan

Tài liệu này mô tả cách tích hợp API lưu trường tùy chỉnh cho audience trong module marketing.

## API Endpoint

```
PUT /user/marketing/audiences/:audienceId/custom-fields
```

## Request Structure

### BulkUpdateCustomFieldsDto

```typescript
interface BulkUpdateCustomFieldsDto {
  fields: CreateCustomFieldDto[];
}
```

### CreateCustomFieldDto

```typescript
interface CreateCustomFieldDto {
  fieldId: number;     // ID tham chiếu đến định nghĩa trường tùy chỉnh
  fieldValue: unknown; // Giá trị của trường tùy chỉnh
}
```

## Response Structure

```typescript
ApiResponseDto<MarketingCustomFieldResponse[]>
```

## Implementation

### 1. Service Layer

```typescript
// src/modules/marketing/services/audience.service.ts
export const AudienceService = {
  bulkUpdateCustomFields: async (
    audienceId: number,
    data: BulkUpdateCustomFieldsDto
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse[]>> => {
    return apiClient.put<MarketingCustomFieldResponse[]>(
      `/user/marketing/audiences/${audienceId}/custom-fields`,
      data
    );
  },
};
```

### 2. Hook Layer

```typescript
// src/modules/marketing/hooks/useAudienceQuery.ts
export const useBulkUpdateAudienceCustomFields = (audienceId: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkUpdateCustomFieldsDto) => 
      AudienceService.bulkUpdateCustomFields(audienceId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.detail(audienceId) });
      queryClient.invalidateQueries({ queryKey: AUDIENCE_QUERY_KEYS.all });
    },
  });
};
```

### 3. Component Usage

```typescript
// src/modules/marketing/components/forms/AudienceDetailView.tsx
const AudienceDetailView: React.FC<AudienceDetailViewProps> = ({ audienceId, onClose, onEdit }) => {
  const bulkUpdateCustomFields = useBulkUpdateAudienceCustomFields(audienceId);
  const [audienceCustomFields, setAudienceCustomFields] = useState<SelectedCustomField[]>([]);

  const handleSaveEditCustomFields = useCallback(async () => {
    try {
      const bulkUpdateData: BulkUpdateCustomFieldsDto = {
        fields: audienceCustomFields.map((field): CreateCustomFieldDto => ({
          fieldId: field.id,
          fieldValue: field.value?.['value'] ?? '',
        })),
      };

      await bulkUpdateCustomFields.mutateAsync(bulkUpdateData);
      setIsEditingCustomFields(false);
    } catch (error) {
      console.error('Error saving custom fields:', error);
    }
  }, [audienceCustomFields, bulkUpdateCustomFields]);
};
```

## Data Transformation

### From Component State to API

```typescript
// Component state: SelectedCustomField[]
const audienceCustomFields = [
  {
    id: 1,
    displayName: 'Thành phố',
    dataType: 'text',
    value: { value: 'Hà Nội' }
  },
  {
    id: 2,
    displayName: 'Tuổi',
    dataType: 'number',
    value: { value: 25 }
  }
];

// Transform to API format: BulkUpdateCustomFieldsDto
const bulkUpdateData = {
  fields: [
    { fieldId: 1, fieldValue: 'Hà Nội' },
    { fieldId: 2, fieldValue: 25 }
  ]
};
```

## Error Handling

```typescript
const handleSaveEditCustomFields = useCallback(async () => {
  setIsSavingCustomFields(true);
  try {
    await bulkUpdateCustomFields.mutateAsync(bulkUpdateData);
    // Success handling
  } catch (error) {
    console.error('Error saving custom fields:', error);
    alert(t('marketing:audience.customFields.saveError', 'Có lỗi xảy ra khi lưu trường tùy chỉnh'));
  } finally {
    setIsSavingCustomFields(false);
  }
}, []);
```

## Testing

```typescript
// Mock service for testing
jest.mock('../../../services/audience.service');
const mockAudienceService = AudienceService as jest.Mocked<typeof AudienceService>;

mockAudienceService.bulkUpdateCustomFields.mockResolvedValue({
  result: [],
  success: true,
  message: 'Custom fields updated successfully',
});
```

## Key Features

1. **Bulk Update**: Thay thế hoàn toàn tất cả custom fields của audience
2. **Type Safety**: Sử dụng TypeScript interfaces đầy đủ
3. **Error Handling**: Xử lý lỗi và hiển thị thông báo phù hợp
4. **Cache Invalidation**: Tự động refresh data sau khi update
5. **Loading States**: Hiển thị trạng thái loading trong quá trình save

## Migration Notes

- API này thay thế hoàn toàn các giá trị custom fields hiện có
- Cần transform dữ liệu từ `SelectedCustomField[]` sang `BulkUpdateCustomFieldsDto`
- Hook `useBulkUpdateAudienceCustomFields` được export tự động qua `hooks/index.ts`
