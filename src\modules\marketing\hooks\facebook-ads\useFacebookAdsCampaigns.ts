import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  FacebookAdsCampaignDto,
  FacebookAdsCampaignQueryDto,
  CreateFacebookAdsCampaignDto,
  UpdateFacebookAdsCampaignDto,
} from '../../types/facebook-ads.types';
import {
  getFacebookAdsCampaigns,
  getFacebookAdsCampaign,
  createFacebookAdsCampaign,
  updateFacebookAdsCampaign,
  deleteFacebookAdsCampaign,
  toggleFacebookAdsCampaign,
} from '../../services/facebook-ads.service';

/**
 * Facebook Ads Campaigns Hooks
 * React Query hooks cho quản lý chiến dịch Facebook Ads
 */

// Query Keys
export const FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS = {
  ALL: ['facebook-ads-campaigns'] as const,
  LIST: (params: FacebookAdsCampaignQueryDto) => [...FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (campaignId: string) => [...FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.ALL, 'detail', campaignId] as const,
} as const;

/**
 * Hook để lấy danh sách chiến dịch Facebook Ads
 */
export const useFacebookAdsCampaigns = (
  params?: FacebookAdsCampaignQueryDto,
  options?: Omit<UseQueryOptions<ApiResponseDto<PaginatedResult<FacebookAdsCampaignDto>>>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.LIST(params || {}),
    queryFn: () => getFacebookAdsCampaigns(params),
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for real-time data)
    ...options,
  });
};

/**
 * Hook để lấy chi tiết chiến dịch Facebook Ads
 */
export const useFacebookAdsCampaign = (
  campaignId: string,
  options?: UseQueryOptions<ApiResponseDto<FacebookAdsCampaignDto>>
) => {
  return useQuery({
    queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.DETAIL(campaignId),
    queryFn: () => getFacebookAdsCampaign(campaignId),
    enabled: !!campaignId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

/**
 * Hook để tạo chiến dịch Facebook Ads mới
 */
export const useCreateFacebookAdsCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFacebookAdsCampaignDto) => createFacebookAdsCampaign(data),
    onSuccess: () => {
      // Invalidate campaigns list
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để cập nhật chiến dịch Facebook Ads
 */
export const useUpdateFacebookAdsCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ campaignId, data }: { campaignId: string; data: UpdateFacebookAdsCampaignDto }) =>
      updateFacebookAdsCampaign(campaignId, data),
    onSuccess: (_, variables) => {
      // Invalidate campaigns list
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.ALL,
      });
      // Invalidate specific campaign detail
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.DETAIL(variables.campaignId),
      });
    },
  });
};

/**
 * Hook để xóa chiến dịch Facebook Ads
 */
export const useDeleteFacebookAdsCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (campaignId: string) => deleteFacebookAdsCampaign(campaignId),
    onSuccess: (_, campaignId) => {
      // Invalidate campaigns list
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.ALL,
      });
      // Remove specific campaign detail from cache
      queryClient.removeQueries({
        queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.DETAIL(campaignId),
      });
    },
  });
};

/**
 * Hook để tạm dừng/kích hoạt chiến dịch Facebook Ads
 */
export const useToggleFacebookAdsCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ campaignId, status }: { campaignId: string; status: 'ACTIVE' | 'PAUSED' }) =>
      toggleFacebookAdsCampaign(campaignId, status),
    onSuccess: (_, variables) => {
      // Invalidate campaigns list
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.ALL,
      });
      // Invalidate specific campaign detail
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_CAMPAIGNS_QUERY_KEYS.DETAIL(variables.campaignId),
      });
    },
  });
};

/**
 * Hook tổng hợp cho quản lý chiến dịch Facebook Ads
 */
export const useFacebookAdsCampaignManagement = (params?: FacebookAdsCampaignQueryDto) => {
  const campaignsQuery = useFacebookAdsCampaigns(params);
  const createMutation = useCreateFacebookAdsCampaign();
  const updateMutation = useUpdateFacebookAdsCampaign();
  const deleteMutation = useDeleteFacebookAdsCampaign();
  const toggleMutation = useToggleFacebookAdsCampaign();

  return {
    // Data
    campaigns: campaignsQuery.data?.result?.items || [],
    totalItems: campaignsQuery.data?.result?.meta?.totalItems || 0,
    
    // Loading states
    isLoading: campaignsQuery.isLoading,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isToggling: toggleMutation.isPending,
    
    // Error states
    error: campaignsQuery.error,
    createError: createMutation.error,
    updateError: updateMutation.error,
    deleteError: deleteMutation.error,
    toggleError: toggleMutation.error,
    
    // Actions
    createCampaign: createMutation.mutate,
    updateCampaign: updateMutation.mutate,
    deleteCampaign: deleteMutation.mutate,
    toggleCampaign: toggleMutation.mutate,
    refetch: campaignsQuery.refetch,
    
    // Success handlers (deprecated in TanStack Query v5)
    // Use useEffect to handle success in components instead
  };
};

/**
 * Hook để lấy thống kê chiến dịch Facebook Ads
 */
export const useFacebookAdsCampaignStats = (campaignId?: string) => {
  const campaignQuery = useFacebookAdsCampaign(campaignId || '');

  const stats = campaignQuery.data?.result ? {
    status: campaignQuery.data.result.status,
    objective: campaignQuery.data.result.objective,
    budget: campaignQuery.data.result.dailyBudget || campaignQuery.data.result.lifetimeBudget || 0,
    budgetType: campaignQuery.data.result.dailyBudget ? 'daily' : 'lifetime',
    startDate: campaignQuery.data.result.startTime,
    endDate: campaignQuery.data.result.endTime,
    // TODO: Add more stats from insights
  } : null;

  return {
    stats,
    isLoading: campaignQuery.isLoading,
    error: campaignQuery.error,
  };
};

/**
 * Hook để lấy danh sách chiến dịch theo tài khoản
 */
export const useFacebookAdsCampaignsByAccount = (
  accountId: string,
  options?: Omit<UseQueryOptions<ApiResponseDto<PaginatedResult<FacebookAdsCampaignDto>>>, 'queryKey' | 'queryFn'>
) => {
  const params: FacebookAdsCampaignQueryDto = {
    accountId,
  };

  return useFacebookAdsCampaigns(params, {
    enabled: !!accountId,
    ...options,
  });
};

/**
 * Hook để lấy chiến dịch gần đây
 */
export const useRecentFacebookAdsCampaigns = (limit: number = 5) => {
  const params: FacebookAdsCampaignQueryDto = {
    limit,
    sortBy: 'createdAt',
    sortDirection: 'DESC',
  };

  return useFacebookAdsCampaigns(params, {
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};
