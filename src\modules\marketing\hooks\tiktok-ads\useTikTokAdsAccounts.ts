/**
 * TikTok Ads Accounts Hooks
 * Hooks layer cho TikTok Ads Accounts với TanStack Query
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';
import { TIKTOK_ADS_QUERY_KEYS } from '../../constants/tiktok-ads.constants';
import { TikTokAdsAccountService } from '../../services/tiktok-ads.service';
import {
  TikTokAdsAccountStatus,
} from '../../types/tiktok-ads.types';
import type {
  TikTokAdsAccountDto,
  TikTokAdsAccountQueryDto,
  CreateTikTokAdsAccountDto,
  UpdateTikTokAdsAccountDto,
  TikTokAdsAccountResponse,
} from '../../types/tiktok-ads.types';

/**
 * Hook factory cho TikTok Ads Accounts
 */
export const useTikTokAdsAccounts = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const queryClient = useQueryClient();

  /**
   * Lấy danh sách tài khoản TikTok Ads
   */
  const useAccounts = (params?: TikTokAdsAccountQueryDto) => {
    return useQuery<TikTokAdsAccountResponse, Error>({
      queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.LIST(params as Record<string, unknown> || {}),
      queryFn: async () => {
        const response = await TikTokAdsAccountService.getAccounts(params);
        return response.result;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  /**
   * Lấy chi tiết tài khoản TikTok Ads
   */
  const useAccount = (id: number) => {
    return useQuery<TikTokAdsAccountDto, Error>({
      queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.DETAIL(id),
      queryFn: async () => {
        const response = await TikTokAdsAccountService.getAccount(id);
        return response.result;
      },
      enabled: !!id && id > 0,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  /**
   * Tạo tài khoản TikTok Ads mới
   */
  const useCreateAccount = () => {
    return useMutation<TikTokAdsAccountDto, Error, CreateTikTokAdsAccountDto>({
      mutationFn: async (data) => {
        const response = await TikTokAdsAccountService.createAccount(data);
        return response.result;
      },
      onSuccess: (data) => {
        // Invalidate và refetch accounts list
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.ALL,
        });

        NotificationUtil.success({
          title: t('marketing:tiktokAds.account.createSuccess', 'Tài khoản TikTok Ads đã được tạo thành công'),
          message: `${data.name} - ${data.accountId}`,
        });
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('marketing:tiktokAds.account.createError', 'Lỗi khi tạo tài khoản TikTok Ads'),
          message: error.message,
        });
      },
    });
  };

  /**
   * Cập nhật tài khoản TikTok Ads
   */
  const useUpdateAccount = () => {
    return useMutation<TikTokAdsAccountDto, Error, { id: number; data: UpdateTikTokAdsAccountDto }>({
      mutationFn: async ({ id, data }) => {
        const response = await TikTokAdsAccountService.updateAccount(id, data);
        return response.result;
      },
      onSuccess: (data) => {
        // Invalidate và refetch accounts list và detail
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.ALL,
        });
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.DETAIL(data.id),
        });

        NotificationUtil.success({
          title: t('marketing:tiktokAds.account.updateSuccess', 'Tài khoản TikTok Ads đã được cập nhật thành công'),
          message: data.name,
        });
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('marketing:tiktokAds.account.updateError', 'Lỗi khi cập nhật tài khoản TikTok Ads'),
          message: error.message,
        });
      },
    });
  };

  /**
   * Xóa tài khoản TikTok Ads
   */
  const useDeleteAccount = () => {
    return useMutation<void, Error, number>({
      mutationFn: async (id) => {
        const response = await TikTokAdsAccountService.deleteAccount(id);
        return response.result;
      },
      onSuccess: (_, accountId) => {
        // Invalidate và refetch accounts list
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.ALL,
        });
        // Remove account detail từ cache
        queryClient.removeQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.DETAIL(accountId),
        });

        NotificationUtil.success({
          message: t('marketing:tiktokAds.account.deleteSuccess', 'Tài khoản TikTok Ads đã được xóa thành công'),
        });
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('marketing:tiktokAds.account.deleteError', 'Lỗi khi xóa tài khoản TikTok Ads'),
          message: error.message,
        });
      },
    });
  };

  /**
   * Cập nhật trạng thái tài khoản TikTok Ads
   */
  const useUpdateAccountStatus = () => {
    return useMutation<TikTokAdsAccountDto, Error, { id: number; status: TikTokAdsAccountStatus }>({
      mutationFn: async ({ id, status }) => {
        const response = await TikTokAdsAccountService.updateAccount(id, { status });
        return response.result;
      },
      onSuccess: (data) => {
        // Invalidate và refetch accounts list và detail
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.ALL,
        });
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.ACCOUNTS.DETAIL(data.id),
        });

        const statusMessage = data.status === TikTokAdsAccountStatus.ACTIVE
          ? t('marketing:tiktokAds.account.activated', 'Tài khoản TikTok Ads đã được kích hoạt')
          : t('marketing:tiktokAds.account.deactivated', 'Tài khoản TikTok Ads đã được vô hiệu hóa');

        NotificationUtil.success({
          title: statusMessage,
          message: data.name,
        });
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('marketing:tiktokAds.account.statusUpdateError', 'Lỗi khi cập nhật trạng thái tài khoản TikTok Ads'),
          message: error.message,
        });
      },
    });
  };

  return {
    useAccounts,
    useAccount,
    useCreateAccount,
    useUpdateAccount,
    useDeleteAccount,
    useUpdateAccountStatus,
  };
};


