/**
 * TikTok Ads Campaigns Hooks
 * Hooks layer cho TikTok Ads Campaigns với TanStack Query
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';
import { TIKTOK_ADS_QUERY_KEYS } from '../../constants/tiktok-ads.constants';
import { TikTokAdsCampaignService } from '../../services/tiktok-ads.service';
import {
  TikTokAdsCampaignStatus,
} from '../../types/tiktok-ads.types';
import type {
  TikTokAdsCampaignDto,
  TikTokAdsCampaignQueryDto,
  CreateTikTokAdsCampaignDto,
  UpdateTikTokAdsCampaignDto,
  TikTokAdsCampaignResponse,
} from '../../types/tiktok-ads.types';

/**
 * Hook factory cho TikTok Ads Campaigns
 */
export const useTikTokAdsCampaigns = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const queryClient = useQueryClient();

  /**
   * Lấy danh sách chiến dịch TikTok Ads
   */
  const useCampaigns = (params?: TikTokAdsCampaignQueryDto) => {
    return useQuery<TikTokAdsCampaignResponse, Error>({
      queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.LIST(params as Record<string, unknown> || {}),
      queryFn: async () => {
        const response = await TikTokAdsCampaignService.getCampaigns(params);
        return response.result;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  /**
   * Lấy chi tiết chiến dịch TikTok Ads
   */
  const useCampaign = (id: number) => {
    return useQuery<TikTokAdsCampaignDto, Error>({
      queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.DETAIL(id),
      queryFn: async () => {
        const response = await TikTokAdsCampaignService.getCampaign(id);
        return response.result;
      },
      enabled: !!id && id > 0,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  /**
   * Tạo chiến dịch TikTok Ads mới
   */
  const useCreateCampaign = () => {
    return useMutation<TikTokAdsCampaignDto, Error, CreateTikTokAdsCampaignDto>({
      mutationFn: async (data) => {
        const response = await TikTokAdsCampaignService.createCampaign(data);
        return response.result;
      },
      onSuccess: (data) => {
        // Invalidate và refetch campaigns list
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.ALL,
        });

        NotificationUtil.success({
          title: t('marketing:tiktokAds.campaign.createSuccess', 'Chiến dịch TikTok Ads đã được tạo thành công'),
          message: `${data.name} - ${data.campaignId}`,
        });
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('marketing:tiktokAds.campaign.createError', 'Lỗi khi tạo chiến dịch TikTok Ads'),
          message: error.message,
        });
      },
    });
  };

  /**
   * Cập nhật chiến dịch TikTok Ads
   */
  const useUpdateCampaign = () => {
    return useMutation<TikTokAdsCampaignDto, Error, { id: number; data: UpdateTikTokAdsCampaignDto }>({
      mutationFn: async ({ id, data }) => {
        const response = await TikTokAdsCampaignService.updateCampaign(id, data);
        return response.result;
      },
      onSuccess: (data) => {
        // Invalidate và refetch campaigns list và detail
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.ALL,
        });
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.DETAIL(data.id),
        });

        NotificationUtil.success({
          title: t('marketing:tiktokAds.campaign.updateSuccess', 'Chiến dịch TikTok Ads đã được cập nhật thành công'),
          message: data.name,
        });
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('marketing:tiktokAds.campaign.updateError', 'Lỗi khi cập nhật chiến dịch TikTok Ads'),
          message: error.message,
        });
      },
    });
  };

  /**
   * Xóa chiến dịch TikTok Ads
   */
  const useDeleteCampaign = () => {
    return useMutation<void, Error, number>({
      mutationFn: async (id) => {
        const response = await TikTokAdsCampaignService.deleteCampaign(id);
        return response.result;
      },
      onSuccess: (_, campaignId) => {
        // Invalidate và refetch campaigns list
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.ALL,
        });
        // Remove campaign detail từ cache
        queryClient.removeQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.DETAIL(campaignId),
        });

        NotificationUtil.success({
          message: t('marketing:tiktokAds.campaign.deleteSuccess', 'Chiến dịch TikTok Ads đã được xóa thành công'),
        });
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('marketing:tiktokAds.campaign.deleteError', 'Lỗi khi xóa chiến dịch TikTok Ads'),
          message: error.message,
        });
      },
    });
  };

  /**
   * Cập nhật trạng thái chiến dịch TikTok Ads
   */
  const useUpdateCampaignStatus = () => {
    return useMutation<TikTokAdsCampaignDto, Error, { id: number; status: TikTokAdsCampaignStatus }>({
      mutationFn: async ({ id, status }) => {
        const response = await TikTokAdsCampaignService.updateCampaign(id, { status });
        return response.result;
      },
      onSuccess: (data) => {
        // Invalidate và refetch campaigns list và detail
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.ALL,
        });
        queryClient.invalidateQueries({
          queryKey: TIKTOK_ADS_QUERY_KEYS.CAMPAIGNS.DETAIL(data.id),
        });

        const statusMessage = data.status === TikTokAdsCampaignStatus.ENABLED
          ? t('marketing:tiktokAds.campaign.enabled', 'Chiến dịch TikTok Ads đã được kích hoạt')
          : t('marketing:tiktokAds.campaign.paused', 'Chiến dịch TikTok Ads đã được tạm dừng');

        NotificationUtil.success({
          title: statusMessage,
          message: data.name,
        });
      },
      onError: (error) => {
        NotificationUtil.error({
          title: t('marketing:tiktokAds.campaign.statusUpdateError', 'Lỗi khi cập nhật trạng thái chiến dịch TikTok Ads'),
          message: error.message,
        });
      },
    });
  };

  return {
    useCampaigns,
    useCampaign,
    useCreateCampaign,
    useUpdateCampaign,
    useDeleteCampaign,
    useUpdateCampaignStatus,
  };
};


