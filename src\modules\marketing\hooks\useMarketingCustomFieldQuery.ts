/**
 * Hooks cho Marketing Custom Fields - hoàn toàn độc lập
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import { NotificationUtil } from '@/shared/utils/notification';
import {
  MarketingCustomFieldService,
  MarketingCustomFieldBusinessService,
  MarketingCustomFieldQueryParams,
} from '../services/marketing-custom-field.service';
import {
  CreateMarketingCustomFieldRequest,
  UpdateMarketingCustomFieldRequest,
} from '../types/custom-field.types';

/**
 * Query keys cho marketing custom fields
 */
export const MARKETING_CUSTOM_FIELD_QUERY_KEYS = {
  all: ['marketing-custom-fields'] as const,
  lists: () => [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'list'] as const,
  list: (params: MarketingCustomFieldQueryParams) => 
    [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(), params] as const,
  details: () => [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.details(), id] as const,
  configExamples: () => [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'config-examples'] as const,
};

/**
 * Hook lấy danh sách marketing custom fields
*/
export const useMarketingCustomFields = (params: MarketingCustomFieldQueryParams = {}) => {
  return useQuery({
    queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.list(params),
    queryFn: () => MarketingCustomFieldBusinessService.getCustomFieldsWithBusinessLogic(params),
    select: (data) => {
      // API trả về { code, message, result: { items, meta } }
      return data.result || data;
    },
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết marketing custom field
 */
export const useMarketingCustomField = (id: number) => {
  return useQuery({
    queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.detail(id),
    queryFn: () => MarketingCustomFieldService.getCustomFieldById(id),
    select: (data) => data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook tạo marketing custom field mới
 */
export const useCreateMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketing', 'common']);

  return useMutation({
    mutationFn: (data: CreateMarketingCustomFieldRequest) =>
      MarketingCustomFieldBusinessService.createCustomFieldWithValidation(data),
    onSuccess: (data) => {
      NotificationUtil.success({
        message: data.message || t('marketing:customField.createSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string; errors?: string[] }>) => {
      console.error('Error creating marketing custom field:', error);
      
      let errorMessage = t('marketing:customField.errors.createError');
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      // Hiển thị chi tiết lỗi validation nếu có
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        errorMessage += '\n' + error.response.data.errors.join('\n');
      }

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });
    },
  });
};

/**
 * Hook cập nhật marketing custom field
 */
export const useUpdateMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketing', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateMarketingCustomFieldRequest }) =>
      MarketingCustomFieldBusinessService.updateCustomFieldWithValidation(id, data),
    onSuccess: (data, variables) => {
      NotificationUtil.success({
        message: data.message || t('marketing:customField.updateSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách và chi tiết
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
      
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.detail(variables.id),
        exact: true,
      });
    },
    onError: (error: AxiosError<{ message: string; errors?: string[] }>) => {
      console.error('Error updating marketing custom field:', error);
      
      let errorMessage = t('marketing:customField.errors.updateError');
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      // Hiển thị chi tiết lỗi validation nếu có
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        errorMessage += '\n' + error.response.data.errors.join('\n');
      }

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });
    },
  });
};

/**
 * Hook xóa marketing custom field
 */
export const useDeleteMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketing', 'common']);

  return useMutation({
    mutationFn: (id: number) => MarketingCustomFieldService.deleteCustomField(id),
    onSuccess: (data) => {
      NotificationUtil.success({
        message: data.message || t('marketing:customField.deleteSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('Error deleting marketing custom field:', error);
      NotificationUtil.error({
        message: error.response?.data?.message || t('marketing:customField.errors.deleteError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook xóa nhiều marketing custom fields
 */
export const useDeleteMultipleMarketingCustomFields = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketing', 'common']);

  return useMutation({
    mutationFn: (ids: number[]) => MarketingCustomFieldService.deleteMultipleCustomFields(ids),
    onSuccess: (data) => {
      NotificationUtil.success({
        message: data.message || t('marketing:customField.deleteMultipleSuccess', { count: data.result?.deletedCount || 0 }),
        duration: 3000,
      });

      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('Error deleting multiple marketing custom fields:', error);
      NotificationUtil.error({
        message: error.response?.data?.message || t('marketing:customField.errors.deleteMultipleError'),
        duration: 3000,
      });
    },
  });
};

