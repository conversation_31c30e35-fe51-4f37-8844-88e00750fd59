/**
 * React Query hooks for marketing overview - Layer 3: React Query hooks
 */

import { useQuery } from '@tanstack/react-query';
import { MarketingOverviewBusinessService } from '../services/marketing-overview-business.service';

/**
 * Query keys cho Marketing Overview
 */
export const MARKETING_OVERVIEW_QUERY_KEYS = {
  all: ['marketing', 'overview'] as const,
  overview: () => [...MARKETING_OVERVIEW_QUERY_KEYS.all, 'stats'] as const,
  recentTemplates: () => [...MARKETING_OVERVIEW_QUERY_KEYS.all, 'recent-templates'] as const,
};

/**
 * Hook để lấy thông tin overview marketing
 */
export function useMarketingOverview() {
  return useQuery({
    queryKey: MARKETING_OVERVIEW_QUERY_KEYS.overview(),
    queryFn: () => MarketingOverviewBusinessService.getMarketingOverview(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook để lấy danh sách templates gần đây
 */
export function useRecentTemplates() {
  return useQuery({
    queryKey: MARKETING_OVERVIEW_QUERY_KEYS.recentTemplates(),
    queryFn: () => MarketingOverviewBusinessService.getRecentTemplates(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
}
