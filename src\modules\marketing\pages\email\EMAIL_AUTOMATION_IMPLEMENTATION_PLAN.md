# Email Automation Workflow Implementation Plan
## Triển khai React Flow cho /marketing/email/automation

### 📋 **Tổng quan**
Triển khai hệ thống Email Automation sử dụng React Flow để tạo workflow automation với drag-and-drop interface, cho phép người dùng tạo các luồng email tự động phức tạp.

### 🎯 **Mục tiêu**
- Tạo visual workflow builder cho email automation
- Drag & drop interface với các node types khác nhau
- Real-time workflow execution và monitoring
- Integration với email templates và audience management

### 🏗️ **Kiến trúc tổng quan**

```
/marketing/email/automation/
├── components/
│   ├── WorkflowBuilder/           # Main workflow builder component
│   ├── nodes/                     # Custom node components
│   ├── edges/                     # Custom edge components
│   ├── panels/                    # Side panels (toolbox, properties)
│   └── modals/                    # Configuration modals
├── hooks/
│   ├── useWorkflowBuilder.ts      # Main workflow logic
│   ├── useNodeTypes.ts            # Node type definitions
│   └── useWorkflowExecution.ts    # Workflow execution logic
├── types/
│   ├── workflow.types.ts          # Workflow type definitions
│   └── node.types.ts              # Node type definitions
├── services/
│   ├── workflow.service.ts        # API calls
│   └── execution.service.ts       # Workflow execution
└── utils/
    ├── nodeValidation.ts          # Node validation logic
    └── workflowUtils.ts           # Utility functions
```

### 📦 **Dependencies cần cài đặt**

```bash
npm install @xyflow/react
npm install @xyflow/node-resizer
npm install dagre  # For auto-layout
npm install react-colorful  # For color picker
```

### 🔧 **Node Types cần triển khai**

#### 1. **Trigger Nodes (Nodes kích hoạt)**
- **Email Opened**: Khi email được mở
- **Link Clicked**: Khi link trong email được click
- **Time Delay**: Delay theo thời gian
- **Date/Time**: Kích hoạt vào ngày/giờ cụ thể
- **Audience Join**: Khi user join vào audience
- **Custom Event**: Khi có event tùy chỉnh

#### 2. **Action Nodes (Nodes hành động)**
- **Send Email**: Gửi email template
- **Add Tag**: Thêm tag cho contact
- **Remove Tag**: Xóa tag khỏi contact
- **Move to Audience**: Chuyển contact sang audience khác
- **Update Contact**: Cập nhật thông tin contact
- **Wait**: Chờ một khoảng thời gian
- **HTTP Request**: Gọi API external

#### 3. **Condition Nodes (Nodes điều kiện)**
- **If/Else**: Điều kiện đơn giản
- **Switch**: Nhiều điều kiện
- **Contact Filter**: Lọc contact theo criteria
- **A/B Test**: Chia traffic cho A/B testing

#### 4. **Utility Nodes (Nodes tiện ích)**
- **Note**: Ghi chú trong workflow
- **Group**: Nhóm các nodes lại
- **Merge**: Gộp nhiều luồng
- **Split**: Chia luồng

### 🎨 **UI/UX Design**

#### **Layout chính:**
```
┌─────────────────────────────────────────────────────────────┐
│ Header: Workflow Name | Save | Test | Activate             │
├─────────────┬───────────────────────────────┬───────────────┤
│ Toolbox     │ Main Canvas                   │ Properties    │
│ - Triggers  │ (React Flow Canvas)           │ Panel         │
│ - Actions   │                               │ - Node Config │
│ - Conditions│                               │ - Settings    │
│ - Utilities │                               │ - Preview     │
├─────────────┼───────────────────────────────┼───────────────┤
│ Minimap     │ Controls (Zoom, Fit, etc.)    │ Execution Log │
└─────────────┴───────────────────────────────┴───────────────┘
```

### 📝 **Implementation Phases**

#### **Phase 1: Core Setup (Week 1)**
- [ ] Setup React Flow basic structure
- [ ] Create basic node types (Send Email, Wait, If/Else)
- [ ] Implement drag & drop from toolbox
- [ ] Basic workflow save/load functionality

#### **Phase 2: Advanced Nodes (Week 2)**
- [ ] Implement all trigger nodes
- [ ] Implement all action nodes
- [ ] Add node configuration panels
- [ ] Node validation logic

#### **Phase 3: Workflow Logic (Week 3)**
- [ ] Workflow execution engine
- [ ] Real-time status updates
- [ ] Error handling và retry logic
- [ ] Workflow testing functionality

#### **Phase 4: Integration (Week 4)**
- [ ] Integration với email templates
- [ ] Integration với audience management
- [ ] Analytics và reporting
- [ ] Performance optimization

### 🔄 **Workflow Execution Flow**

```mermaid
graph TD
    A[Workflow Triggered] --> B[Load Workflow Definition]
    B --> C[Initialize Execution Context]
    C --> D[Find Start Node]
    D --> E[Execute Node]
    E --> F{Node Type?}
    F -->|Trigger| G[Wait for Event]
    F -->|Action| H[Perform Action]
    F -->|Condition| I[Evaluate Condition]
    G --> J[Continue to Next Node]
    H --> J
    I --> J
    J --> K{More Nodes?}
    K -->|Yes| E
    K -->|No| L[Complete Workflow]
```

### 📊 **Data Models**

#### **Workflow Model:**
```typescript
interface Workflow {
  id: string;
  name: string;
  description?: string;
  status: 'draft' | 'active' | 'paused' | 'archived';
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  settings: WorkflowSettings;
  createdAt: string;
  updatedAt: string;
}
```

#### **Node Model:**
```typescript
interface WorkflowNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
  config: NodeConfig;
}
```

### 🧪 **Testing Strategy**
- Unit tests cho node logic
- Integration tests cho workflow execution
- E2E tests cho user workflows
- Performance tests cho large workflows

### 📈 **Success Metrics**
- Workflow creation time < 5 minutes
- Workflow execution latency < 1 second
- 99.9% uptime cho automation engine
- User adoption rate > 80%

### 🚀 **Deployment Plan**
1. Development environment setup
2. Staging deployment với test data
3. Beta testing với selected users
4. Production rollout với monitoring
5. Post-launch optimization

### 📚 **Documentation Plan**
- User guide cho workflow creation
- Developer documentation cho custom nodes
- API documentation cho integrations
- Video tutorials cho common use cases

---

## 🛠️ **Technical Implementation Details**

### **Core Components Structure**

#### **1. WorkflowBuilder Component**
```typescript
// src/modules/marketing/pages/email/automation/components/WorkflowBuilder/WorkflowBuilder.tsx
interface WorkflowBuilderProps {
  workflowId?: string;
  onSave: (workflow: Workflow) => void;
  onTest: (workflow: Workflow) => void;
}

const WorkflowBuilder: React.FC<WorkflowBuilderProps> = ({
  workflowId,
  onSave,
  onTest
}) => {
  // React Flow setup
  // Node management
  // Edge management
  // Toolbox integration
  // Properties panel
};
```

#### **2. Custom Node Components**
```typescript
// src/modules/marketing/pages/email/automation/components/nodes/
├── TriggerNodes/
│   ├── EmailOpenedNode.tsx
│   ├── LinkClickedNode.tsx
│   ├── TimeDelayNode.tsx
│   └── AudienceJoinNode.tsx
├── ActionNodes/
│   ├── SendEmailNode.tsx
│   ├── AddTagNode.tsx
│   ├── UpdateContactNode.tsx
│   └── WaitNode.tsx
├── ConditionNodes/
│   ├── IfElseNode.tsx
│   ├── SwitchNode.tsx
│   └── ContactFilterNode.tsx
└── UtilityNodes/
    ├── NoteNode.tsx
    └── GroupNode.tsx
```

#### **3. Node Configuration System**
```typescript
// Base node configuration interface
interface BaseNodeConfig {
  id: string;
  type: NodeType;
  name: string;
  description?: string;
  enabled: boolean;
}

// Send Email Node specific config
interface SendEmailNodeConfig extends BaseNodeConfig {
  templateId: string;
  fromEmail: string;
  fromName: string;
  subject: string;
  personalizations: Record<string, any>;
  trackOpens: boolean;
  trackClicks: boolean;
}
```

### **API Endpoints cần triển khai**

#### **Workflow Management**
```typescript
// GET /api/v1/marketing/email/workflows
// POST /api/v1/marketing/email/workflows
// PUT /api/v1/marketing/email/workflows/:id
// DELETE /api/v1/marketing/email/workflows/:id
// POST /api/v1/marketing/email/workflows/:id/activate
// POST /api/v1/marketing/email/workflows/:id/pause
// POST /api/v1/marketing/email/workflows/:id/test
```

#### **Workflow Execution**
```typescript
// GET /api/v1/marketing/email/workflows/:id/executions
// GET /api/v1/marketing/email/workflows/:id/analytics
// POST /api/v1/marketing/email/workflows/:id/trigger
```

### **State Management với Zustand**
```typescript
// src/modules/marketing/pages/email/automation/store/workflowStore.ts
interface WorkflowStore {
  // Current workflow state
  currentWorkflow: Workflow | null;
  nodes: Node[];
  edges: Edge[];

  // UI state
  selectedNode: string | null;
  isPropertiesPanelOpen: boolean;

  // Actions
  setWorkflow: (workflow: Workflow) => void;
  addNode: (node: Node) => void;
  updateNode: (id: string, updates: Partial<Node>) => void;
  deleteNode: (id: string) => void;
  addEdge: (edge: Edge) => void;
  deleteEdge: (id: string) => void;
}
```

### **Real-time Updates với WebSocket**
```typescript
// src/modules/marketing/pages/email/automation/hooks/useWorkflowExecution.ts
const useWorkflowExecution = (workflowId: string) => {
  const [executionStatus, setExecutionStatus] = useState<ExecutionStatus>();

  useEffect(() => {
    const ws = new WebSocket(`ws://localhost:3001/workflows/${workflowId}/execution`);

    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      setExecutionStatus(update);
    };

    return () => ws.close();
  }, [workflowId]);

  return { executionStatus };
};
```

### **Node Validation System**
```typescript
// src/modules/marketing/pages/email/automation/utils/nodeValidation.ts
interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

const validateWorkflow = (workflow: Workflow): ValidationResult => {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // Check for orphaned nodes
  // Validate node configurations
  // Check for circular dependencies
  // Validate email templates exist

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};
```
