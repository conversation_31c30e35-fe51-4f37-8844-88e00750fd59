# Email Automation Implementation Plan
## Kế hoạch triển khai React Flow cho /marketing/email/automation

### 📋 **Tổng quan**
Triển khai hệ thống Email Automation sử dụng React Flow để tạo workflow automation với drag-and-drop interface, cho phép người dùng tạo các luồng email tự động phức tạp.

### 🎯 **Mục tiêu**
- Tạo visual workflow builder cho email automation
- Drag & drop interface với các node types khác nhau
- Real-time workflow execution và monitoring
- Integration với email templates và audience management

### 🚀 **Phase 1: Setup & Core Structure (30 phút)**

#### 1.1 Cài đặt Dependencies
```bash
npm install @xyflow/react
npm install @xyflow/node-resizer
npm install dagre
npm install react-colorful
```

#### 1.2 Tạo cấu trúc thư mục
```
src/modules/marketing/pages/email/automation/
├── components/
│   ├── WorkflowBuilder/
│   │   ├── WorkflowBuilder.tsx
│   │   ├── WorkflowBuilder.types.ts
│   │   └── index.ts
│   ├── nodes/
│   │   ├── TriggerNodes/
│   │   │   ├── EmailOpenedNode.tsx
│   │   │   ├── TimeDelayNode.tsx
│   │   │   └── index.ts
│   │   ├── ActionNodes/
│   │   │   ├── SendEmailNode.tsx
│   │   │   ├── WaitNode.tsx
│   │   │   └── index.ts
│   │   ├── ConditionNodes/
│   │   │   ├── IfElseNode.tsx
│   │   │   └── index.ts
│   │   └── BaseNode/
│   │       ├── BaseNode.tsx
│   │       └── BaseNode.types.ts
│   ├── panels/
│   │   ├── Toolbox/
│   │   │   ├── Toolbox.tsx
│   │   │   └── index.ts
│   │   ├── PropertiesPanel/
│   │   │   ├── PropertiesPanel.tsx
│   │   │   └── index.ts
│   │   └── ExecutionPanel/
│   │       ├── ExecutionPanel.tsx
│   │       └── index.ts
│   └── modals/
│       ├── NodeConfigModal/
│       │   ├── NodeConfigModal.tsx
│       │   └── index.ts
│       └── WorkflowSettingsModal/
│           ├── WorkflowSettingsModal.tsx
│           └── index.ts
├── hooks/
│   ├── useWorkflowBuilder.ts
│   ├── useNodeTypes.ts
│   ├── useWorkflowExecution.ts
│   └── index.ts
├── types/
│   ├── workflow.types.ts
│   ├── node.types.ts
│   └── index.ts
├── services/
│   ├── workflow.service.ts
│   ├── execution.service.ts
│   └── index.ts
├── utils/
│   ├── nodeValidation.ts
│   ├── workflowUtils.ts
│   └── index.ts
├── store/
│   ├── workflowStore.ts
│   └── index.ts
├── EmailAutomationPage.tsx
└── index.ts
```

#### 1.3 Setup Routing
- Thêm route `/marketing/email/automation` vào router
- Tạo EmailAutomationPage component cơ bản

### 🔧 **Phase 2: Core Components (45 phút)**

#### 2.1 Base Types & Interfaces
- Định nghĩa các interface cơ bản cho workflow, nodes, edges
- Setup TypeScript types cho React Flow

#### 2.2 WorkflowBuilder Component
- Tạo component chính với React Flow
- Setup basic canvas với zoom, pan controls
- Implement minimap

#### 2.3 Basic Node Types
- **SendEmailNode**: Node gửi email
- **WaitNode**: Node delay/wait
- **IfElseNode**: Node điều kiện

#### 2.4 Toolbox Component
- Drag & drop từ toolbox vào canvas
- Categorize nodes (Triggers, Actions, Conditions)

### ⚡ **Phase 3: Advanced Features (60 phút)**

#### 3.1 Node Configuration
- Properties panel cho từng node type
- Form validation cho node settings
- Real-time preview

#### 3.2 Workflow Logic
- Node connection validation
- Workflow execution flow
- Error handling

#### 3.3 Integration với Email Templates
- Template selector trong SendEmailNode
- Variable mapping
- Preview functionality

### 🎨 **Phase 4: UI/UX Polish (30 phút)**

#### 4.1 Styling & Theme
- Consistent với design system
- Dark/light mode support
- Responsive design

#### 4.2 User Experience
- Keyboard shortcuts
- Context menus
- Undo/redo functionality

### 📊 **Phase 5: Testing & Documentation (15 phút)**

#### 5.1 Testing
- Unit tests cho core functions
- Integration tests cho workflow execution

#### 5.2 Documentation
- Component documentation
- Usage examples
- API documentation

---

## 🛠️ **Technical Implementation Details**

### **Node Types cần triển khai**

#### **Trigger Nodes**
1. **Email Opened** - Khi email được mở
2. **Link Clicked** - Khi link trong email được click
3. **Time Delay** - Delay theo thời gian
4. **Date/Time** - Kích hoạt vào ngày/giờ cụ thể
5. **Audience Join** - Khi user join vào audience

#### **Action Nodes**
1. **Send Email** - Gửi email template
2. **Add Tag** - Thêm tag cho contact
3. **Remove Tag** - Xóa tag khỏi contact
4. **Update Contact** - Cập nhật thông tin contact
5. **Wait** - Chờ một khoảng thời gian

#### **Condition Nodes**
1. **If/Else** - Điều kiện rẽ nhánh
2. **Switch** - Nhiều điều kiện
3. **Contact Filter** - Lọc contact theo tiêu chí

### **Workflow Execution Flow**

```mermaid
graph TD
    A[Workflow Triggered] --> B[Load Workflow Definition]
    B --> C[Initialize Execution Context]
    C --> D[Find Start Node]
    D --> E[Execute Node]
    E --> F{Node Type?}
    F -->|Trigger| G[Wait for Event]
    F -->|Action| H[Perform Action]
    F -->|Condition| I[Evaluate Condition]
    G --> J[Continue to Next Node]
    H --> J
    I --> J
    J --> K{More Nodes?}
    K -->|Yes| E
    K -->|No| L[Complete Workflow]
```

### **State Management**

Sử dụng Zustand cho state management:
- Current workflow state
- Nodes và edges
- UI state (selected node, panels)
- Execution status

### **API Integration**

- GET `/v1/marketing/email/automations` - Lấy danh sách workflows
- POST `/v1/marketing/email/automations` - Tạo workflow mới
- PUT `/v1/marketing/email/automations/:id` - Cập nhật workflow
- DELETE `/v1/marketing/email/automations/:id` - Xóa workflow
- POST `/v1/marketing/email/automations/:id/execute` - Thực thi workflow

---

## ✅ **Checklist triển khai**

### Phase 1: Setup
- [ ] Cài đặt React Flow dependencies
- [ ] Tạo cấu trúc thư mục
- [ ] Setup routing cho /marketing/email/automation
- [ ] Tạo EmailAutomationPage component cơ bản

### Phase 2: Core Components
- [ ] Định nghĩa types và interfaces
- [ ] Tạo WorkflowBuilder component
- [ ] Implement basic node types (Send Email, Wait, If/Else)
- [ ] Tạo Toolbox component với drag & drop

### Phase 3: Advanced Features
- [ ] Properties panel cho node configuration
- [ ] Workflow validation logic
- [ ] Integration với email templates
- [ ] Workflow execution engine

### Phase 4: UI/UX
- [ ] Styling và theming
- [ ] Responsive design
- [ ] User experience improvements

### Phase 5: Testing
- [ ] Unit tests
- [ ] Integration tests
- [ ] Documentation

---

## 🎯 **Kết quả mong đợi**

Sau khi hoàn thành, hệ thống sẽ có:

1. **Visual Workflow Builder** với drag & drop interface
2. **Đa dạng Node Types** cho các use case khác nhau
3. **Real-time Execution** với monitoring và logging
4. **Integration** hoàn chỉnh với email templates và audience
5. **User-friendly Interface** dễ sử dụng cho non-technical users

Thời gian triển khai dự kiến: **3-4 giờ** cho version đầy đủ tính năng.
