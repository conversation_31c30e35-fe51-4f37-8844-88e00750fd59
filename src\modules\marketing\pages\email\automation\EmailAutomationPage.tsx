/**
 * EmailAutomationPage - Trang chính cho Email Automation với React Flow
 */

import React from 'react';
import { WorkflowBuilder } from './components/WorkflowBuilder';
import type { Workflow } from './types';

/**
 * EmailAutomationPage component
 */
export const EmailAutomationPage: React.FC = () => {
  // const { t } = useTranslation(['marketing', 'common']);

  const handleSaveWorkflow = (workflow: Workflow) => {
    console.log('Saving workflow:', workflow);
    // TODO: Implement API call to save workflow
  };

  const handleTestWorkflow = (workflow: Workflow) => {
    console.log('Testing workflow:', workflow);
    // TODO: Implement workflow testing
  };

  const handlePublishWorkflow = (workflow: Workflow) => {
    console.log('Publishing workflow:', workflow);
    // TODO: Implement workflow publishing
  };

  return (
    <div className="w-full h-screen bg-background text-foreground">
      <WorkflowBuilder
        onSave={handleSaveWorkflow}
        onTest={handleTestWorkflow}
        onPublish={handlePublishWorkflow}
      />
    </div>
  );
};

export default EmailAutomationPage;
