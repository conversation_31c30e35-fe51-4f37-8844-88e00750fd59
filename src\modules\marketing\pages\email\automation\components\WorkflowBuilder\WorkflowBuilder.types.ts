/**
 * Types cho WorkflowBuilder component
 */

import type { Workflow, WorkflowNode, WorkflowEdge } from '../../types';

/**
 * WorkflowBuilder props
 */
export interface WorkflowBuilderProps {
  workflowId?: string;
  initialWorkflow?: Workflow;
  onSave: (workflow: Workflow) => void;
  onTest?: (workflow: Workflow) => void;
  onPublish?: (workflow: Workflow) => void;
  readOnly?: boolean;
}

/**
 * Workflow builder state
 */
export interface WorkflowBuilderState {
  workflow: Workflow | null;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  selectedNodeId: string | null;
  selectedEdgeId: string | null;
  isPropertiesPanelOpen: boolean;
  isToolboxOpen: boolean;
  isExecutionPanelOpen: boolean;
  isDirty: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * Workflow builder actions
 */
export interface WorkflowBuilderActions {
  setWorkflow: (workflow: Workflow) => void;
  addNode: (node: WorkflowNode) => void;
  updateNode: (id: string, updates: Partial<WorkflowNode>) => void;
  deleteNode: (id: string) => void;
  addEdge: (edge: WorkflowEdge) => void;
  updateEdge: (id: string, updates: Partial<WorkflowEdge>) => void;
  deleteEdge: (id: string) => void;
  selectNode: (id: string | null) => void;
  selectEdge: (id: string | null) => void;
  togglePropertiesPanel: () => void;
  toggleToolbox: () => void;
  toggleExecutionPanel: () => void;
  setDirty: (dirty: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

/**
 * Panel sizes
 */
export interface PanelSizes {
  toolbox: number;
  properties: number;
  execution: number;
}

/**
 * Viewport settings
 */
export interface ViewportSettings {
  zoom: number;
  x: number;
  y: number;
}

/**
 * Grid settings
 */
export interface GridSettings {
  enabled: boolean;
  size: number;
  color: string;
}

/**
 * Canvas settings
 */
export interface CanvasSettings {
  viewport: ViewportSettings;
  grid: GridSettings;
  snapToGrid: boolean;
  showMinimap: boolean;
  showControls: boolean;
}
