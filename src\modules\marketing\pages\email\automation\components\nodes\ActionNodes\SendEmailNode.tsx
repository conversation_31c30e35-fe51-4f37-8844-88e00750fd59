/**
 * SendEmailNode - Node để gửi email trong workflow
 */

import React from 'react';
import { Mail } from 'lucide-react';
import { BaseNode } from '../BaseNode/BaseNode';
import type { BaseNodeProps } from '../BaseNode/BaseNode.types';
import type { SendEmailNodeConfig } from '../../../types';

/**
 * SendEmailNode component
 */
export const SendEmailNode: React.FC<BaseNodeProps> = (props) => {
  const config = props.data.config as unknown as SendEmailNodeConfig;

  const getDescription = () => {
    if (config.templateId) {
      return `Template: ${config.templateId}`;
    }
    return 'No template selected';
  };

  const getDelayInfo = () => {
    if (config.delay && config.delay > 0) {
      return `Delay: ${config.delay} minutes`;
    }
    return null;
  };

  return (
    <BaseNode
      {...props}
      variant="action"
      icon={<Mail className="w-4 h-4 text-green-600 dark:text-green-400" />}
      title="Send Email"
      description={getDescription()}
    >
      {/* Additional info */}
      <div className="space-y-1">
        {getDelayInfo() && (
          <div className="text-xs text-muted-foreground">
            {getDelayInfo()}
          </div>
        )}
        
        {config.sendTime === 'scheduled' && config.scheduledTime && (
          <div className="text-xs text-muted-foreground">
            Scheduled: {config.scheduledTime}
          </div>
        )}

        {config.variables && Object.keys(config.variables).length > 0 && (
          <div className="text-xs text-muted-foreground">
            Variables: {Object.keys(config.variables).length}
          </div>
        )}
      </div>
    </BaseNode>
  );
};

export default SendEmailNode;
