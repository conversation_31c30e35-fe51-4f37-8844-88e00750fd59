/**
 * WaitNode - Node để chờ/delay trong workflow
 */

import React from 'react';
import { Clock } from 'lucide-react';
import { BaseNode } from '../BaseNode/BaseNode';
import type { BaseNodeProps } from '../BaseNode/BaseNode.types';
import type { WaitNodeConfig } from '../../../types';

/**
 * WaitNode component
 */
export const WaitNode: React.FC<BaseNodeProps> = (props) => {
  const config = props.data.config as unknown as WaitNodeConfig;

  const getDescription = () => {
    const { duration, unit } = config;
    if (!duration) return 'No duration set';
    
    const unitLabel = {
      minutes: duration === 1 ? 'minute' : 'minutes',
      hours: duration === 1 ? 'hour' : 'hours',
      days: duration === 1 ? 'day' : 'days',
    };

    return `Wait ${duration} ${unitLabel[unit]}`;
  };

  const getDurationInMinutes = () => {
    const { duration, unit } = config;
    if (!duration) return 0;

    switch (unit) {
      case 'minutes':
        return duration;
      case 'hours':
        return duration * 60;
      case 'days':
        return duration * 60 * 24;
      default:
        return duration;
    }
  };

  return (
    <BaseNode
      {...props}
      variant="action"
      icon={<Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />}
      title="Wait"
      description={getDescription()}
    >
      {/* Additional info */}
      <div className="space-y-1">
        {getDurationInMinutes() > 0 && (
          <div className="text-xs text-muted-foreground">
            Total: {getDurationInMinutes()} minutes
          </div>
        )}
        
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
          <span className="text-xs text-muted-foreground">
            Waiting period
          </span>
        </div>
      </div>
    </BaseNode>
  );
};

export default WaitNode;
