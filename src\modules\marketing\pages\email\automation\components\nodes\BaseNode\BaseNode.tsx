/**
 * BaseNode - Component c<PERSON> bản cho tất cả workflow nodes
 */

import React from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { cn } from '@/shared/utils/cn';
import type { BaseNodeProps, NodeStatus } from './BaseNode.types';

/**
 * Get node style based on variant and status
 */
const getNodeStyle = (variant: 'trigger' | 'action' | 'condition', status?: NodeStatus) => {
  const baseClasses = 'px-4 py-3 rounded-lg border-2 bg-card text-card-foreground shadow-md transition-all duration-200 min-w-[200px]';

  // Variant styles using CSS variables for theme compatibility
  const variantClasses = {
    trigger: 'border-blue-500 bg-blue-50/80 dark:bg-blue-950/50 text-blue-900 dark:text-blue-100',
    action: 'border-green-500 bg-green-50/80 dark:bg-green-950/50 text-green-900 dark:text-green-100',
    condition: 'border-orange-500 bg-orange-50/80 dark:bg-orange-950/50 text-orange-900 dark:text-orange-100',
  };

  // Status styles
  const statusClasses = {
    IDLE: '',
    RUNNING: 'border-yellow-500 bg-yellow-50/80 dark:bg-yellow-950/50 animate-pulse',
    SUCCESS: 'border-green-600 bg-green-100/80 dark:bg-green-900/50',
    ERROR: 'border-red-500 bg-red-50/80 dark:bg-red-950/50',
    WAITING: 'border-blue-400 bg-blue-50/80 dark:bg-blue-950/50',
  };

  return cn(
    baseClasses,
    variantClasses[variant],
    status && statusClasses[status]
  );
};

/**
 * BaseNode component
 */
export const BaseNode: React.FC<BaseNodeProps & {
  variant: 'trigger' | 'action' | 'condition';
  icon?: React.ReactNode;
  title: string;
  description?: string;
  status?: NodeStatus;
  children?: React.ReactNode;
  showSourceHandle?: boolean;
  showTargetHandle?: boolean;
}> = ({
  data,
  selected,
  variant,
  icon,
  title,
  description,
  status,
  children,
  showSourceHandle = true,
  showTargetHandle = true,
}) => {
  return (
    <div className={getNodeStyle(variant, status)}>
      {/* Target Handle */}
      {showTargetHandle && (
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-primary border-2 border-background hover:bg-primary/80 transition-colors"
        />
      )}

      {/* Node Content */}
      <div className="flex items-start space-x-3 min-w-[200px]">
        {/* Icon */}
        {icon && (
          <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-background/80 border border-border">
            {icon}
          </div>
        )}

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm truncate text-foreground">{title}</h3>
            {status && (
              <div className={cn(
                'w-2 h-2 rounded-full flex-shrink-0 ml-2',
                {
                  'bg-muted-foreground': status === 'IDLE',
                  'bg-yellow-500 animate-pulse': status === 'RUNNING',
                  'bg-green-500': status === 'SUCCESS',
                  'bg-red-500': status === 'ERROR',
                  'bg-blue-500': status === 'WAITING',
                }
              )} />
            )}
          </div>
          
          {description && (
            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
              {description}
            </p>
          )}

          {/* Custom content */}
          {children && (
            <div className="mt-2">
              {children}
            </div>
          )}

          {/* Node enabled/disabled indicator */}
          {!data.enabled && (
            <div className="mt-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-muted text-muted-foreground">
                Disabled
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Source Handle */}
      {showSourceHandle && (
        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 bg-primary border-2 border-background hover:bg-primary/80 transition-colors"
        />
      )}

      {/* Selection indicator */}
      {selected && (
        <div className="absolute inset-0 border-2 border-primary rounded-lg pointer-events-none" />
      )}
    </div>
  );
};

export default BaseNode;
