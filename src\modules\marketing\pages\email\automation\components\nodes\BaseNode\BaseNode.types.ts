/**
 * Types cho BaseNode component
 */

import type { NodeProps } from '@xyflow/react';
import type { BaseNodeData } from '../../../types';

/**
 * Base node props
 */
export interface BaseNodeProps extends NodeProps {
  data: BaseNodeData;
}

/**
 * Node status
 */
export enum NodeStatus {
  IDLE = 'IDLE',
  RUNNING = 'RUNNING',
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  WAITING = 'WAITING',
}

/**
 * Node execution state
 */
export interface NodeExecutionState {
  status: NodeStatus;
  message?: string;
  progress?: number;
  startTime?: Date;
  endTime?: Date;
}

/**
 * Base node style props
 */
export interface BaseNodeStyleProps {
  status?: NodeStatus;
  selected?: boolean;
  disabled?: boolean;
  variant?: 'trigger' | 'action' | 'condition';
}
