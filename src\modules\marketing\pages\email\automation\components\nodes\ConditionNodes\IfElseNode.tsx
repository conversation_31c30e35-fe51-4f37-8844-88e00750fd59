/**
 * IfElseNode - Node đi<PERSON>u kiện if/else trong workflow
 */

import React from 'react';
import { GitBranch } from 'lucide-react';
import { Handle, Position } from '@xyflow/react';
import { BaseNode } from '../BaseNode/BaseNode';
import type { BaseNodeProps } from '../BaseNode/BaseNode.types';
import type { IfElseNodeConfig } from '../../../types';

/**
 * IfElseNode component
 */
export const IfElseNode: React.FC<BaseNodeProps> = (props) => {
  const config = props.data.config as unknown as IfElseNodeConfig;

  const getDescription = () => {
    const { condition } = config;
    if (!condition) return 'No condition set';
    
    const operatorLabels = {
      equals: '=',
      not_equals: '≠',
      contains: 'contains',
      not_contains: 'not contains',
      greater_than: '>',
      less_than: '<',
    };

    return `${condition.field} ${operatorLabels[condition.operator]} ${condition.value}`;
  };

  return (
    <div className="relative">
      <BaseNode
        {...props}
        variant="condition"
        icon={<GitBranch className="w-4 h-4 text-orange-600" />}
        title="If/Else"
        description={getDescription()}
        showSourceHandle={false}
      >
        {/* Condition details */}
        <div className="space-y-1">
          {config.condition && (
            <div className="text-xs text-muted-foreground">
              Field: {config.condition.field}
            </div>
          )}
        </div>
      </BaseNode>

      {/* Custom handles for Yes/No branches */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="yes"
        style={{ left: '25%' }}
        className="w-3 h-3 bg-green-500 border-2 border-background"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="no"
        style={{ left: '75%' }}
        className="w-3 h-3 bg-red-500 border-2 border-background"
      />

      {/* Labels for branches */}
      <div className="absolute -bottom-6 left-0 right-0 flex justify-between text-xs text-muted-foreground">
        <span className="ml-4">Yes</span>
        <span className="mr-4">No</span>
      </div>
    </div>
  );
};

export default IfElseNode;
