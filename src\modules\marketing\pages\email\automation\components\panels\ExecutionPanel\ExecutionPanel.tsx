/**
 * ExecutionPanel - Panel hiển thị thông tin thực thi workflow
 */

import React from 'react';
import { X, Play, Pause, Square, Clock, CheckCircle, XCircle } from 'lucide-react';
import { Card, Typography, Button } from '@/shared/components/common';

/**
 * ExecutionPanel props
 */
interface ExecutionPanelProps {
  workflowId?: string;
  onClose: () => void;
}

/**
 * Mock execution data
 */
const mockExecutionLogs = [
  {
    id: '1',
    nodeId: 'send-email-1',
    action: 'Send Email',
    status: 'SUCCESS',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    duration: 1200,
    message: 'Email sent successfully to 150 recipients',
  },
  {
    id: '2',
    nodeId: 'wait-1',
    action: 'Wait',
    status: 'RUNNING',
    timestamp: new Date(Date.now() - 3 * 60 * 1000),
    duration: 0,
    message: 'Waiting for 2 hours',
  },
  {
    id: '3',
    nodeId: 'if-else-1',
    action: 'If/Else',
    status: 'PENDING',
    timestamp: new Date(),
    duration: 0,
    message: 'Waiting for previous step',
  },
];

/**
 * ExecutionPanel component
 */
export const ExecutionPanel: React.FC<ExecutionPanelProps> = ({
  // workflowId,
  onClose,
}) => {
  const [isRunning, setIsRunning] = React.useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'RUNNING':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'ERROR':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'PENDING':
        return <Clock className="w-4 h-4 text-gray-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  return (
    <div className="h-full flex flex-col bg-card">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <Typography variant="h6">Execution</Typography>
          <button
            onClick={onClose}
            className="p-1 hover:bg-muted rounded"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        <Typography variant="caption" className="text-muted-foreground mt-1">
          Monitor workflow execution
        </Typography>
      </div>

      {/* Controls */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant={isRunning ? "secondary" : "primary"}
            onClick={() => setIsRunning(!isRunning)}
            className="flex items-center space-x-2"
          >
            {isRunning ? (
              <>
                <Pause className="w-4 h-4" />
                <span>Pause</span>
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                <span>Start</span>
              </>
            )}
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={() => setIsRunning(false)}
            className="flex items-center space-x-2"
          >
            <Square className="w-4 h-4" />
            <span>Stop</span>
          </Button>
        </div>
      </div>

      {/* Execution Status */}
      <div className="p-4 border-b border-border">
        <Card className="p-3">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="subtitle2">Current Status</Typography>
            <div className="flex items-center space-x-2">
              {isRunning ? (
                <>
                  <Clock className="w-4 h-4 text-blue-500 animate-spin" />
                  <span className="text-sm text-blue-600">Running</span>
                </>
              ) : (
                <>
                  <Pause className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Paused</span>
                </>
              )}
            </div>
          </div>
          
          <div className="space-y-1 text-sm text-muted-foreground">
            <div>Started: {formatTimestamp(new Date(Date.now() - 10 * 60 * 1000))}</div>
            <div>Duration: {formatDuration(10 * 60 * 1000)}</div>
            <div>Processed: 150 contacts</div>
          </div>
        </Card>
      </div>

      {/* Execution Logs */}
      <div className="flex-1 overflow-y-auto p-4">
        <Typography variant="subtitle2" className="mb-3">
          Execution Log
        </Typography>

        <div className="space-y-3">
          {mockExecutionLogs.map((log) => (
            <Card key={log.id} className="p-3">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getStatusIcon(log.status)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <Typography variant="body2" className="font-medium">
                      {log.action}
                    </Typography>
                    <Typography variant="caption" className="text-muted-foreground">
                      {formatTimestamp(log.timestamp)}
                    </Typography>
                  </div>
                  
                  <Typography variant="caption" className="text-muted-foreground block mb-1">
                    {log.message}
                  </Typography>
                  
                  {log.duration > 0 && (
                    <Typography variant="caption" className="text-muted-foreground">
                      Duration: {formatDuration(log.duration)}
                    </Typography>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {mockExecutionLogs.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="w-8 h-8 mx-auto mb-2" />
            <Typography variant="body2">
              No execution logs yet
            </Typography>
            <Typography variant="caption">
              Start the workflow to see execution details
            </Typography>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <Typography variant="caption" className="text-muted-foreground">
          Real-time execution monitoring
        </Typography>
      </div>
    </div>
  );
};

export default ExecutionPanel;
