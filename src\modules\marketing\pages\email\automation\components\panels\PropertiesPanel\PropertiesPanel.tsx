/**
 * PropertiesPanel - Panel để cấu hình properties của node/edge được chọn
 */

import React from 'react';
import { X, Settings } from 'lucide-react';
import { Card, Typography, Input, FormItem } from '@/shared/components/common';
import type { WorkflowNode, WorkflowEdge } from '../../../types';

/**
 * PropertiesPanel props
 */
interface PropertiesPanelProps {
  selectedNodeId: string | null;
  selectedEdgeId: string | null;
  onUpdateNode: (id: string, updates: Partial<WorkflowNode>) => void;
  onUpdateEdge: (id: string, updates: Partial<WorkflowEdge>) => void;
  onClose: () => void;
}

/**
 * PropertiesPanel component
 */
export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  selectedNodeId,
  selectedEdgeId,
  onUpdateNode,
  onUpdateEdge,
  onClose,
}) => {
  const [nodeConfig, setNodeConfig] = React.useState<Record<string, unknown>>({});

  // Mock node data - in real app, this would come from state
  const selectedNode = React.useMemo(() => selectedNodeId ? {
    id: selectedNodeId,
    type: 'SEND_EMAIL',
    position: { x: 0, y: 0 },
    data: {
      id: selectedNodeId,
      type: 'SEND_EMAIL' as const,
      label: 'Send Email',
      description: 'Send an email template',
      enabled: true,
      config: {
        templateId: '',
        sendTime: 'immediate',
        variables: {},
      },
    },
  } as WorkflowNode : null, [selectedNodeId]);

  const selectedEdge = selectedEdgeId ? {
    id: selectedEdgeId,
    source: 'node1',
    target: 'node2',
    data: {
      condition: '',
      label: '',
    },
  } as WorkflowEdge : null;

  React.useEffect(() => {
    if (selectedNode) {
      setNodeConfig(selectedNode.data.config);
    }
  }, [selectedNode]);

  const handleNodeConfigChange = (field: string, value: unknown) => {
    const newConfig = { ...nodeConfig, [field]: value };
    setNodeConfig(newConfig);
    
    if (selectedNodeId) {
      onUpdateNode(selectedNodeId, {
        data: {
          ...selectedNode!.data,
          config: newConfig,
        },
      });
    }
  };

  const renderNodeProperties = () => {
    if (!selectedNode) return null;

    switch (selectedNode.data.type) {
      case 'SEND_EMAIL':
        return (
          <div className="space-y-4">
            <FormItem label="Template ID" name="templateId">
              <Input
                value={nodeConfig.templateId as string || ''}
                onChange={(e) => handleNodeConfigChange('templateId', e.target.value)}
                placeholder="Enter template ID"
              />
            </FormItem>

            <FormItem label="Send Time" name="sendTime">
              <select
                value={nodeConfig.sendTime as string || 'immediate'}
                onChange={(e) => handleNodeConfigChange('sendTime', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md bg-background"
              >
                <option value="immediate">Immediate</option>
                <option value="scheduled">Scheduled</option>
              </select>
            </FormItem>

            {nodeConfig.sendTime === 'scheduled' && (
              <FormItem label="Scheduled Time" name="scheduledTime">
                <Input
                  type="datetime-local"
                  value={nodeConfig.scheduledTime as string || ''}
                  onChange={(e) => handleNodeConfigChange('scheduledTime', e.target.value)}
                />
              </FormItem>
            )}

            <FormItem label="Delay (minutes)" name="delay">
              <Input
                type="number"
                value={nodeConfig.delay as number || 0}
                onChange={(e) => handleNodeConfigChange('delay', parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
              />
            </FormItem>
          </div>
        );

      case 'WAIT':
        return (
          <div className="space-y-4">
            <FormItem label="Duration" name="duration">
              <Input
                type="number"
                value={nodeConfig.duration as number || 1}
                onChange={(e) => handleNodeConfigChange('duration', parseInt(e.target.value) || 1)}
                placeholder="1"
                min="1"
              />
            </FormItem>

            <FormItem label="Unit" name="unit">
              <select
                value={nodeConfig.unit as string || 'hours'}
                onChange={(e) => handleNodeConfigChange('unit', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md bg-background"
              >
                <option value="minutes">Minutes</option>
                <option value="hours">Hours</option>
                <option value="days">Days</option>
              </select>
            </FormItem>
          </div>
        );

      case 'IF_ELSE':
        return (
          <div className="space-y-4">
            <FormItem label="Field" name="field">
              <Input
                value={(nodeConfig.condition as Record<string, unknown>)?.field as string || ''}
                onChange={(e) => handleNodeConfigChange('condition', {
                  ...(nodeConfig.condition as Record<string, unknown>),
                  field: e.target.value,
                })}
                placeholder="Enter field name"
              />
            </FormItem>

            <FormItem label="Operator" name="operator">
              <select
                value={(nodeConfig.condition as Record<string, unknown>)?.operator as string || 'equals'}
                onChange={(e) => handleNodeConfigChange('condition', {
                  ...(nodeConfig.condition as Record<string, unknown>),
                  operator: e.target.value,
                })}
                className="w-full px-3 py-2 border border-border rounded-md bg-background"
              >
                <option value="equals">Equals</option>
                <option value="not_equals">Not Equals</option>
                <option value="contains">Contains</option>
                <option value="not_contains">Not Contains</option>
                <option value="greater_than">Greater Than</option>
                <option value="less_than">Less Than</option>
              </select>
            </FormItem>

            <FormItem label="Value" name="value">
              <Input
                value={(nodeConfig.condition as Record<string, unknown>)?.value as string || ''}
                onChange={(e) => handleNodeConfigChange('condition', {
                  ...(nodeConfig.condition as Record<string, unknown>),
                  value: e.target.value,
                })}
                placeholder="Enter value"
              />
            </FormItem>
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-muted-foreground">
            <Settings className="w-8 h-8 mx-auto mb-2" />
            <Typography variant="body2">
              No configuration available for this node type
            </Typography>
          </div>
        );
    }
  };

  const renderEdgeProperties = () => {
    if (!selectedEdge) return null;

    return (
      <div className="space-y-4">
        <FormItem label="Label" name="label">
          <Input
            value={selectedEdge.data?.label || ''}
            onChange={(e) => onUpdateEdge(selectedEdgeId!, {
              data: { ...selectedEdge.data, label: e.target.value },
            })}
            placeholder="Enter edge label"
          />
        </FormItem>

        <FormItem label="Condition" name="condition">
          <Input
            value={selectedEdge.data?.condition || ''}
            onChange={(e) => onUpdateEdge(selectedEdgeId!, {
              data: { ...selectedEdge.data, condition: e.target.value },
            })}
            placeholder="Enter condition"
          />
        </FormItem>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col bg-card">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <Typography variant="h6">Properties</Typography>
          <button
            onClick={onClose}
            className="p-1 hover:bg-muted rounded"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {selectedNode && (
          <div className="space-y-4">
            <Card className="p-4">
              <Typography variant="subtitle2" className="mb-2">
                Node: {selectedNode.data.label}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {selectedNode.data.description}
              </Typography>
            </Card>

            <Card className="p-4">
              <Typography variant="subtitle2" className="mb-4">
                Configuration
              </Typography>
              {renderNodeProperties()}
            </Card>

            <Card className="p-4">
              <Typography variant="subtitle2" className="mb-4">
                General Settings
              </Typography>
              <FormItem label="Enabled" name="enabled">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={selectedNode.data.enabled}
                    onChange={(e) => onUpdateNode(selectedNodeId!, {
                      data: { ...selectedNode.data, enabled: e.target.checked },
                    })}
                    className="rounded border-border"
                  />
                  <span className="text-sm">Enable this node</span>
                </label>
              </FormItem>
            </Card>
          </div>
        )}

        {selectedEdge && (
          <div className="space-y-4">
            <Card className="p-4">
              <Typography variant="subtitle2" className="mb-2">
                Edge Configuration
              </Typography>
              {renderEdgeProperties()}
            </Card>
          </div>
        )}

        {!selectedNode && !selectedEdge && (
          <div className="text-center py-8 text-muted-foreground">
            <Settings className="w-8 h-8 mx-auto mb-2" />
            <Typography variant="body2">
              Select a node or edge to configure its properties
            </Typography>
          </div>
        )}
      </div>
    </div>
  );
};

export default PropertiesPanel;
