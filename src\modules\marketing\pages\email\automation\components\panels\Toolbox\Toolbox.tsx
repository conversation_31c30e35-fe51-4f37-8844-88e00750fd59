/**
 * Toolbox - Panel chứa các node templates để drag & drop
 */

import React from 'react';
import { Mail, Clock, GitBranch, Play, Tag, UserPlus, X } from 'lucide-react';
import { Card, Typography } from '@/shared/components/common';
import type { WorkflowNode, ActionType, ConditionType, TriggerType } from '../../../types';

/**
 * Node template definition
 */
interface NodeTemplate {
  id: string;
  type: TriggerType | ActionType | ConditionType;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'trigger' | 'action' | 'condition';
  defaultConfig: Record<string, unknown>;
}

/**
 * Toolbox props
 */
interface ToolboxProps {
  onAddNode: (node: WorkflowNode) => void;
  onClose: () => void;
}

/**
 * Node templates
 */
const nodeTemplates: NodeTemplate[] = [
  // Trigger nodes
  {
    id: 'email-opened',
    type: 'EMAIL_OPENED' as TriggerType,
    name: 'Email Opened',
    description: 'Trigger when email is opened',
    icon: <Mail className="w-4 h-4" />,
    category: 'trigger',
    defaultConfig: {
      timeWindow: 24,
    },
  },
  {
    id: 'audience-join',
    type: 'AUDIENCE_JOIN' as TriggerType,
    name: 'Audience Join',
    description: 'Trigger when contact joins audience',
    icon: <UserPlus className="w-4 h-4" />,
    category: 'trigger',
    defaultConfig: {
      audienceId: '',
    },
  },

  // Action nodes
  {
    id: 'send-email',
    type: 'SEND_EMAIL' as ActionType,
    name: 'Send Email',
    description: 'Send an email template',
    icon: <Mail className="w-4 h-4" />,
    category: 'action',
    defaultConfig: {
      templateId: '',
      sendTime: 'immediate',
      variables: {},
    },
  },
  {
    id: 'wait',
    type: 'WAIT' as ActionType,
    name: 'Wait',
    description: 'Wait for a specified duration',
    icon: <Clock className="w-4 h-4" />,
    category: 'action',
    defaultConfig: {
      duration: 1,
      unit: 'hours',
    },
  },
  {
    id: 'add-tag',
    type: 'ADD_TAG' as ActionType,
    name: 'Add Tag',
    description: 'Add tags to contact',
    icon: <Tag className="w-4 h-4" />,
    category: 'action',
    defaultConfig: {
      tags: [],
    },
  },

  // Condition nodes
  {
    id: 'if-else',
    type: 'IF_ELSE' as ConditionType,
    name: 'If/Else',
    description: 'Branch based on condition',
    icon: <GitBranch className="w-4 h-4" />,
    category: 'condition',
    defaultConfig: {
      condition: {
        field: '',
        operator: 'equals',
        value: '',
      },
    },
  },
];

/**
 * Group templates by category
 */
const groupedTemplates = nodeTemplates.reduce((acc, template) => {
  if (!acc[template.category]) {
    acc[template.category] = [];
  }
  acc[template.category].push(template);
  return acc;
}, {} as Record<string, NodeTemplate[]>);

/**
 * Toolbox component
 */
export const Toolbox: React.FC<ToolboxProps> = ({ onAddNode, onClose }) => {
  const handleDragStart = (event: React.DragEvent, template: NodeTemplate) => {
    event.dataTransfer.setData('application/reactflow', JSON.stringify(template));
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleAddNode = (template: NodeTemplate) => {
    const newNode: WorkflowNode = {
      id: `${template.id}-${Date.now()}`,
      type: template.type,
      position: { x: 100, y: 100 },
      data: {
        id: `${template.id}-${Date.now()}`,
        type: template.type,
        label: template.name,
        description: template.description,
        enabled: true,
        config: template.defaultConfig,
      },
    };
    onAddNode(newNode);
  };

  const categoryLabels = {
    trigger: 'Triggers',
    action: 'Actions',
    condition: 'Conditions',
  };

  const categoryIcons = {
    trigger: <Play className="w-4 h-4" />,
    action: <Mail className="w-4 h-4" />,
    condition: <GitBranch className="w-4 h-4" />,
  };

  return (
    <div className="h-full flex flex-col bg-card">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <Typography variant="h6">Toolbox</Typography>
          <button
            onClick={onClose}
            className="p-1 hover:bg-muted rounded"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        <Typography variant="caption" className="text-muted-foreground mt-1">
          Drag nodes to canvas or click to add
        </Typography>
      </div>

      {/* Node Categories */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {Object.entries(groupedTemplates).map(([category, templates]) => (
          <div key={category}>
            <div className="flex items-center space-x-2 mb-3">
              {categoryIcons[category as keyof typeof categoryIcons]}
              <Typography variant="subtitle2" className="font-medium">
                {categoryLabels[category as keyof typeof categoryLabels]}
              </Typography>
            </div>

            <div className="space-y-2">
              {templates.map((template) => (
                <Card
                  key={template.id}
                  className="p-3 cursor-pointer hover:shadow-md transition-shadow border-dashed"
                  draggable
                  onDragStart={(e) => handleDragStart(e, template)}
                  onClick={() => handleAddNode(template)}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded bg-muted">
                      {template.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <Typography variant="body2" className="font-medium">
                        {template.name}
                      </Typography>
                      <Typography variant="caption" className="text-muted-foreground">
                        {template.description}
                      </Typography>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <Typography variant="caption" className="text-muted-foreground">
          Tip: Drag nodes from here to the canvas to build your workflow
        </Typography>
      </div>
    </div>
  );
};

export default Toolbox;
