/**
 * Hook quản lý state và logic cho WorkflowBuilder
 */

import { useState, useCallback, useEffect, useMemo } from 'react';
import type {
  Workflow,
  WorkflowNode,
  WorkflowEdge,
  WorkflowSettings,
} from '../types';
import { WorkflowStatus } from '../types';
import type {
  WorkflowBuilderState,
  WorkflowBuilderActions,
  WorkflowBuilderProps,
} from '../components/WorkflowBuilder/WorkflowBuilder.types';

/**
 * Default workflow settings
 */
const defaultWorkflowSettings: WorkflowSettings = {
  timeZone: 'Asia/Ho_Chi_Minh',
  retryOnError: true,
  maxRetries: 3,
  notifyOnError: true,
};

/**
 * Hook useWorkflowBuilder
 */
export function useWorkflowBuilder({
  workflowId,
  initialWorkflow,
  // onSave,
  // onTest,
  // onPublish,
}: Pick<WorkflowBuilderProps, 'workflowId' | 'initialWorkflow' | 'onSave' | 'onTest' | 'onPublish'>) {
  // State
  const [state, setState] = useState<WorkflowBuilderState>({
    workflow: initialWorkflow || null,
    nodes: initialWorkflow?.nodes || [],
    edges: initialWorkflow?.edges || [],
    selectedNodeId: null,
    selectedEdgeId: null,
    isPropertiesPanelOpen: false,
    isToolboxOpen: true,
    isExecutionPanelOpen: false,
    isDirty: false,
    isLoading: false,
    error: null,
  });

  // Actions
  const setWorkflow = useCallback((workflow: Workflow) => {
    setState((prev) => ({
      ...prev,
      workflow,
      nodes: workflow.nodes,
      edges: workflow.edges,
      isDirty: false,
    }));
  }, []);

  const addNode = useCallback((node: WorkflowNode) => {
    setState((prev) => ({
      ...prev,
      nodes: [...prev.nodes, node],
      isDirty: true,
    }));
  }, []);

  const updateNode = useCallback((id: string, updates: Partial<WorkflowNode>) => {
    setState((prev) => ({
      ...prev,
      nodes: prev.nodes.map((node) =>
        node.id === id ? { ...node, ...updates } : node
      ),
      isDirty: true,
    }));
  }, []);

  const deleteNode = useCallback((id: string) => {
    setState((prev) => ({
      ...prev,
      nodes: prev.nodes.filter((node) => node.id !== id),
      edges: prev.edges.filter((edge) => edge.source !== id && edge.target !== id),
      selectedNodeId: prev.selectedNodeId === id ? null : prev.selectedNodeId,
      isDirty: true,
    }));
  }, []);

  const addEdge = useCallback((edge: WorkflowEdge) => {
    setState((prev) => ({
      ...prev,
      edges: [...prev.edges, edge],
      isDirty: true,
    }));
  }, []);

  const updateEdge = useCallback((id: string, updates: Partial<WorkflowEdge>) => {
    setState((prev) => ({
      ...prev,
      edges: prev.edges.map((edge) =>
        edge.id === id ? { ...edge, ...updates } : edge
      ),
      isDirty: true,
    }));
  }, []);

  const deleteEdge = useCallback((id: string) => {
    setState((prev) => ({
      ...prev,
      edges: prev.edges.filter((edge) => edge.id !== id),
      selectedEdgeId: prev.selectedEdgeId === id ? null : prev.selectedEdgeId,
      isDirty: true,
    }));
  }, []);

  const selectNode = useCallback((id: string | null) => {
    setState((prev) => ({
      ...prev,
      selectedNodeId: id,
      selectedEdgeId: null,
      isPropertiesPanelOpen: id !== null,
    }));
  }, []);

  const selectEdge = useCallback((id: string | null) => {
    setState((prev) => ({
      ...prev,
      selectedEdgeId: id,
      selectedNodeId: null,
      isPropertiesPanelOpen: id !== null,
    }));
  }, []);

  const togglePropertiesPanel = useCallback(() => {
    setState((prev) => ({
      ...prev,
      isPropertiesPanelOpen: !prev.isPropertiesPanelOpen,
    }));
  }, []);

  const toggleToolbox = useCallback(() => {
    setState((prev) => ({
      ...prev,
      isToolboxOpen: !prev.isToolboxOpen,
    }));
  }, []);

  const toggleExecutionPanel = useCallback(() => {
    setState((prev) => ({
      ...prev,
      isExecutionPanelOpen: !prev.isExecutionPanelOpen,
    }));
  }, []);

  const setDirty = useCallback((dirty: boolean) => {
    setState((prev) => ({
      ...prev,
      isDirty: dirty,
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState((prev) => ({
      ...prev,
      isLoading: loading,
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState((prev) => ({
      ...prev,
      error,
    }));
  }, []);

  const actions: WorkflowBuilderActions = useMemo(() => ({
    setWorkflow,
    addNode,
    updateNode,
    deleteNode,
    addEdge,
    updateEdge,
    deleteEdge,
    selectNode,
    selectEdge,
    togglePropertiesPanel,
    toggleToolbox,
    toggleExecutionPanel,
    setDirty,
    setLoading,
    setError,
  }), [
    setWorkflow,
    addNode,
    updateNode,
    deleteNode,
    addEdge,
    updateEdge,
    deleteEdge,
    selectNode,
    selectEdge,
    togglePropertiesPanel,
    toggleToolbox,
    toggleExecutionPanel,
    setDirty,
    setLoading,
    setError,
  ]);

  // Create new workflow if none exists
  useEffect(() => {
    if (!state.workflow && !workflowId) {
      const newWorkflow: Workflow = {
        id: `workflow-${Date.now()}`,
        name: 'New Workflow',
        description: '',
        status: WorkflowStatus.DRAFT,
        nodes: [],
        edges: [],
        settings: defaultWorkflowSettings,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'current-user', // TODO: Get from auth context
      };
      actions.setWorkflow(newWorkflow);
    }
  }, [state.workflow, workflowId, actions]);

  // Update workflow when nodes or edges change
  useEffect(() => {
    if (state.workflow && state.isDirty) {
      const updatedWorkflow: Workflow = {
        ...state.workflow,
        nodes: state.nodes,
        edges: state.edges,
        updatedAt: new Date(),
      };
      setState((prev) => ({
        ...prev,
        workflow: updatedWorkflow,
      }));
    }
  }, [state.nodes, state.edges, state.workflow, state.isDirty]);

  return {
    state,
    actions,
  };
}
