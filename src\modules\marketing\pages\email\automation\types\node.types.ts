/**
 * Types cho các node trong workflow
 */

import type { TriggerType, ActionType, ConditionType } from './workflow.types';

/**
 * Base node configuration
 */
export interface BaseNodeConfig {
  id: string;
  type: TriggerType | ActionType | ConditionType;
  name: string;
  description?: string;
  enabled: boolean;
}

/**
 * Send Email Node Configuration
 */
export interface SendEmailNodeConfig extends BaseNodeConfig {
  type: ActionType.SEND_EMAIL;
  templateId: string;
  variables?: Record<string, string>;
  delay?: number; // minutes
  sendTime?: 'immediate' | 'scheduled';
  scheduledTime?: string;
}

/**
 * Wait Node Configuration
 */
export interface WaitNodeConfig extends BaseNodeConfig {
  type: ActionType.WAIT;
  duration: number; // minutes
  unit: 'minutes' | 'hours' | 'days';
}

/**
 * If/Else Node Configuration
 */
export interface IfElseNodeConfig extends BaseNodeConfig {
  type: ConditionType.IF_ELSE;
  condition: {
    field: string;
    operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
    value: string;
  };
}

/**
 * Email Opened Trigger Configuration
 */
export interface EmailOpenedTriggerConfig extends BaseNodeConfig {
  type: TriggerType.EMAIL_OPENED;
  templateId?: string;
  timeWindow?: number; // hours
}

/**
 * Time Delay Trigger Configuration
 */
export interface TimeDelayTriggerConfig extends BaseNodeConfig {
  type: TriggerType.TIME_DELAY;
  delay: number;
  unit: 'minutes' | 'hours' | 'days';
}

/**
 * Add Tag Node Configuration
 */
export interface AddTagNodeConfig extends BaseNodeConfig {
  type: ActionType.ADD_TAG;
  tags: string[];
}

/**
 * Remove Tag Node Configuration
 */
export interface RemoveTagNodeConfig extends BaseNodeConfig {
  type: ActionType.REMOVE_TAG;
  tags: string[];
}

/**
 * Update Contact Node Configuration
 */
export interface UpdateContactNodeConfig extends BaseNodeConfig {
  type: ActionType.UPDATE_CONTACT;
  fields: Record<string, string>;
}

/**
 * Audience Join Trigger Configuration
 */
export interface AudienceJoinTriggerConfig extends BaseNodeConfig {
  type: TriggerType.AUDIENCE_JOIN;
  audienceId: string;
}

/**
 * Link Clicked Trigger Configuration
 */
export interface LinkClickedTriggerConfig extends BaseNodeConfig {
  type: TriggerType.LINK_CLICKED;
  templateId?: string;
  linkUrl?: string;
  timeWindow?: number; // hours
}

/**
 * Switch Node Configuration
 */
export interface SwitchNodeConfig extends BaseNodeConfig {
  type: ConditionType.SWITCH;
  field: string;
  cases: Array<{
    value: string;
    label: string;
  }>;
  defaultCase?: string;
}

/**
 * Contact Filter Node Configuration
 */
export interface ContactFilterNodeConfig extends BaseNodeConfig {
  type: ConditionType.CONTACT_FILTER;
  filters: Array<{
    field: string;
    operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
    value: string;
  }>;
  logic: 'AND' | 'OR';
}

/**
 * Date/Time Trigger Configuration
 */
export interface DateTimeTriggerConfig extends BaseNodeConfig {
  type: TriggerType.DATE_TIME;
  scheduleType: 'once' | 'recurring';
  dateTime?: string; // ISO string for once
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string; // HH:mm format
    daysOfWeek?: number[]; // 0-6, Sunday = 0
    dayOfMonth?: number; // 1-31
  };
}

/**
 * Custom Event Trigger Configuration
 */
export interface CustomEventTriggerConfig extends BaseNodeConfig {
  type: TriggerType.CUSTOM_EVENT;
  eventName: string;
  eventProperties?: Record<string, string>;
}

/**
 * Union type cho tất cả node configurations
 */
export type NodeConfig =
  | SendEmailNodeConfig
  | WaitNodeConfig
  | IfElseNodeConfig
  | EmailOpenedTriggerConfig
  | TimeDelayTriggerConfig
  | AddTagNodeConfig
  | RemoveTagNodeConfig
  | UpdateContactNodeConfig
  | AudienceJoinTriggerConfig
  | LinkClickedTriggerConfig
  | SwitchNodeConfig
  | ContactFilterNodeConfig
  | DateTimeTriggerConfig
  | CustomEventTriggerConfig;

/**
 * Node category cho toolbox
 */
export interface NodeCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  nodes: NodeTemplate[];
}

/**
 * Node template cho toolbox
 */
export interface NodeTemplate {
  id: string;
  type: TriggerType | ActionType | ConditionType;
  name: string;
  description: string;
  icon: string;
  category: string;
  defaultConfig: Partial<NodeConfig>;
}

/**
 * Node validation result
 */
export interface NodeValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Node execution result
 */
export interface NodeExecutionResult {
  success: boolean;
  output?: Record<string, unknown>;
  error?: string;
  nextNodeIds: string[];
}
