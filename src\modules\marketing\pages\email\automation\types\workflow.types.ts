/**
 * Types cho Email Automation Workflow
 */

import type { Node, Edge } from '@xyflow/react';

/**
 * Trạng thái workflow
 */
export enum WorkflowStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR',
}

/**
 * Loại trigger cho workflow
 */
export enum TriggerType {
  EMAIL_OPENED = 'EMAIL_OPENED',
  LINK_CLICKED = 'LINK_CLICKED',
  TIME_DELAY = 'TIME_DELAY',
  DATE_TIME = 'DATE_TIME',
  AUDIENCE_JOIN = 'AUDIENCE_JOIN',
  CUSTOM_EVENT = 'CUSTOM_EVENT',
}

/**
 * Loại action trong workflow
 */
export enum ActionType {
  SEND_EMAIL = 'SEND_EMAIL',
  ADD_TAG = 'ADD_TAG',
  REMOVE_TAG = 'REMOVE_TAG',
  UPDATE_CONTACT = 'UPDATE_CONTACT',
  WAIT = 'WAIT',
}

/**
 * Loại condition trong workflow
 */
export enum ConditionType {
  IF_ELSE = 'IF_ELSE',
  SWITCH = 'SWITCH',
  CONTACT_FILTER = 'CONTACT_FILTER',
}

/**
 * Tất cả node types
 */
export type NodeType = TriggerType | ActionType | ConditionType;

/**
 * Base interface cho workflow node data
 */
export interface BaseNodeData extends Record<string, unknown> {
  id: string;
  type: NodeType;
  label: string;
  description?: string;
  enabled: boolean;
  config: Record<string, unknown>;
}

/**
 * Workflow node extends React Flow Node
 */
export interface WorkflowNode extends Node {
  data: BaseNodeData;
}

/**
 * Workflow edge extends React Flow Edge
 */
export interface WorkflowEdge extends Edge {
  data?: {
    condition?: string;
    label?: string;
  };
}

/**
 * Workflow definition
 */
export interface Workflow {
  id: string;
  name: string;
  description?: string;
  status: WorkflowStatus;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  settings: WorkflowSettings;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

/**
 * Workflow settings
 */
export interface WorkflowSettings {
  maxExecutions?: number;
  timeZone: string;
  retryOnError: boolean;
  maxRetries: number;
  notifyOnError: boolean;
  notifyEmail?: string;
}

/**
 * Workflow execution context
 */
export interface WorkflowExecutionContext {
  workflowId: string;
  executionId: string;
  contactId: string;
  variables: Record<string, unknown>;
  currentNodeId: string;
  status: ExecutionStatus;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}

/**
 * Trạng thái thực thi workflow
 */
export enum ExecutionStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  WAITING = 'WAITING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

/**
 * Workflow execution log
 */
export interface WorkflowExecutionLog {
  id: string;
  workflowId: string;
  executionId: string;
  nodeId: string;
  action: string;
  status: ExecutionStatus;
  input?: Record<string, unknown>;
  output?: Record<string, unknown>;
  error?: string;
  duration: number;
  timestamp: Date;
}

/**
 * Workflow statistics
 */
export interface WorkflowStats {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastExecutionAt?: Date;
}

/**
 * Query parameters cho workflows
 */
export interface WorkflowQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: WorkflowStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Create workflow DTO
 */
export interface CreateWorkflowDto {
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  settings: WorkflowSettings;
}

/**
 * Update workflow DTO
 */
export interface UpdateWorkflowDto {
  name?: string;
  description?: string;
  status?: WorkflowStatus;
  nodes?: WorkflowNode[];
  edges?: WorkflowEdge[];
  settings?: WorkflowSettings;
}
