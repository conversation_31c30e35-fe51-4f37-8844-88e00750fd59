import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button, Icon } from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import CreateCampaignForm from '../../components/facebook-ads/CreateCampaignForm';

interface CreateCampaignFormData {
  name: string;
  objective: string;
  accountId: string;
  budgetType: 'daily' | 'lifetime';
  budget: number;
  startDate: string;
  endDate?: string;
  status: 'active' | 'paused';
  specialAdCategories: string[];
  description?: string;
}

/**
 * Create Campaign Page
 * Trang tạo chiến dịch Facebook Ads mới
 */
const CreateCampaignPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();

  const handleSubmitCampaign = async (data: CreateCampaignFormData) => {
    try {
      console.log('Creating campaign:', data);
      
      // TODO: Implement campaign creation API call
      // const response = await createFacebookCampaignDirect(data.accountId, {
      //   name: data.name,
      //   objective: data.objective,
      //   status: data.status.toUpperCase(),
      //   daily_budget: data.budgetType === 'daily' ? data.budget : undefined,
      //   lifetime_budget: data.budgetType === 'lifetime' ? data.budget : undefined,
      //   start_time: data.startDate,
      //   stop_time: data.endDate,
      //   special_ad_categories: data.specialAdCategories,
      // });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate back to campaigns list
      navigate('/marketing/facebook-ads/campaigns');
      
      // TODO: Show success notification
      console.log('Campaign created successfully');
      
    } catch (error) {
      console.error('Failed to create campaign:', error);
      // TODO: Show error notification
    }
  };

  const handleCancel = () => {
    navigate('/marketing/facebook-ads/campaigns');
  };

  return (
    <div className="w-full bg-background text-foreground">
      <MarketingViewHeader
        title={t('marketing:facebookAds.campaigns.create', 'Tạo chiến dịch mới')}
        description={t('marketing:facebookAds.campaigns.createDescription', 'Tạo chiến dịch quảng cáo Facebook để tiếp cận khách hàng mục tiêu')}
        icon="plus"
        actions={
          <Button variant="outline" onClick={handleCancel}>
            <Icon name="arrow-left" className="mr-2" />
            {t('common:button.back', 'Quay lại')}
          </Button>
        }
      />

      <CreateCampaignForm
        onSubmit={handleSubmitCampaign}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default CreateCampaignPage;
