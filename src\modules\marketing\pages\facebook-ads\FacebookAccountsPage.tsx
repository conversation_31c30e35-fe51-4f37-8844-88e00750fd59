import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Icon,
  Modal,
  Form,
  FormItem,
  Input,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import FacebookAuthButton from '../../components/facebook-ads/FacebookAuthButton';
import FacebookAccountManager from '../../components/facebook-ads/FacebookAccountManager';

// interface FacebookAccount {
//   id: string;
//   accountId: string;
//   name: string;
//   businessName: string;
//   status: 'active' | 'inactive' | 'error';
//   currency: string;
//   timezone: string;
//   balance: number;
//   spendCap: number;
//   createdAt: string;
// }

interface ConnectAccountForm {
  accessToken: string;
  accountId: string;
}

/**
 * Facebook Accounts Management Page
 * Trang quản lý tài khoản Facebook Ads
 */
const FacebookAccountsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const [isConnectModalOpen, setIsConnectModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { formRef, setFormErrors } = useFormErrors();
  // Mock data cho demo
  // const mockAccounts: FacebookAccount[] = [
  //   {
  //     id: '1',
  //     accountId: 'act_123456789',
  //     name: 'RedAI Marketing Account',
  //     businessName: 'RedAI Company',
  //     status: 'active',
  //     currency: 'VND',
  //     timezone: 'Asia/Ho_Chi_Minh',
  //     balance: 5000000,
  //     spendCap: ********,
  //     createdAt: '2024-01-15T10:30:00Z',
  //   },
  //   {
  //     id: '2',
  //     accountId: 'act_987654321',
  //     name: 'E-commerce Ads Account',
  //     businessName: 'RedAI E-commerce',
  //     status: 'inactive',
  //     currency: 'VND',
  //     timezone: 'Asia/Ho_Chi_Minh',
  //     balance: 2500000,
  //     spendCap: 5000000,
  //     createdAt: '2024-02-20T14:15:00Z',
  //   },
  // ];

  const handleConnectAccount = async (data: ConnectAccountForm) => {
    setIsLoading(true);
    try {
      // TODO: Implement Facebook account connection logic
      console.log('Connecting account:', data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsConnectModalOpen(false);
      // TODO: Refresh accounts list
    } catch (error) {
      console.error('Error connecting account:', error);
      setFormErrors({
        accessToken: t('marketing:facebookAds.accounts.errors.invalidToken', 'Token không hợp lệ'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditAccount = (accountId: string) => {
    console.log('Edit account:', accountId);
    // TODO: Implement edit account logic
  };

  const handleSyncAccount = (accountId: string) => {
    console.log('Sync account:', accountId);
    // TODO: Implement sync account logic
  };

  const handleDeleteAccount = (accountId: string) => {
    console.log('Delete account:', accountId);
    // TODO: Implement delete account logic with confirmation
  };

  // Use real ad accounts from Facebook Auth if available
  // const displayAccounts = isAuthenticated && adAccounts.length > 0
  //   ? adAccounts.map(account => ({
  //       id: account.id,
  //       accountId: account.accountId,
  //       name: account.name,
  //       businessName: account.businessName || 'N/A',
  //       status: account.accountStatus === 1 ? 'active' as const : 'inactive' as const,
  //       currency: account.currency,
  //       timezone: account.timezone || 'N/A',
  //       balance: 0, // TODO: Get from API
  //       spendCap: 0, // TODO: Get from API
  //       createdAt: new Date().toISOString(),
  //     }))
  //   : mockAccounts;

  return (
    <div className="w-full bg-background text-foreground">
      <MarketingViewHeader
        title={t('marketing:facebookAds.accounts.title', 'Quản lý tài khoản Facebook')}
        description={t('marketing:facebookAds.accounts.description', 'Kết nối và quản lý các tài khoản quảng cáo Facebook')}
        icon="facebook"
        actions={
          <div className="flex space-x-2">
            <FacebookAuthButton
              variant="outline"
              showUserInfo={false}
            />
            <Button
              variant="primary"
              onClick={() => setIsConnectModalOpen(true)}
            >
              <Icon name="plus" className="mr-2" />
              {t('marketing:facebookAds.accounts.addManual', 'Thêm thủ công')}
            </Button>
          </div>
        }
      />

      <FacebookAccountManager
        showFilters={true}
        showAddButton={true}
        onSyncAccount={handleSyncAccount}
        onEditAccount={handleEditAccount}
        onDeleteAccount={handleDeleteAccount}
        onAddAccount={() => setIsConnectModalOpen(true)}
      />

      {/* Connect Account Modal */}
      <Modal
        isOpen={isConnectModalOpen}
        onClose={() => setIsConnectModalOpen(false)}
        title={t('marketing:facebookAds.accounts.modal.title', 'Kết nối tài khoản Facebook')}
        size="md"
      >
        <Form ref={formRef} onSubmit={(data) => handleConnectAccount(data as ConnectAccountForm)}>
          <div className="space-y-4">
            <FormItem
              label={t('marketing:facebookAds.accounts.modal.accessToken', 'Access Token')}
              name="accessToken"
              required
            >
              <Input
                type="password"
                placeholder={t('marketing:facebookAds.accounts.modal.accessTokenPlaceholder', 'Nhập Facebook Access Token')}
              />
            </FormItem>
            
            <FormItem
              label={t('marketing:facebookAds.accounts.modal.accountId', 'Account ID')}
              name="accountId"
              required
            >
              <Input
                placeholder={t('marketing:facebookAds.accounts.modal.accountIdPlaceholder', 'Nhập Facebook Account ID (act_...)')}
              />
            </FormItem>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsConnectModalOpen(false)}
                disabled={isLoading}
              >
                {t('common:button.cancel', 'Hủy')}
              </Button>
              <Button
                type="submit"
                variant="primary"
                isLoading={isLoading}
              >
                {t('marketing:facebookAds.accounts.modal.connect', 'Kết nối')}
              </Button>
            </div>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default FacebookAccountsPage;
