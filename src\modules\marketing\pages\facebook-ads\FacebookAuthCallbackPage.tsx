import React from 'react';
import FacebookAuthCallback from '../../components/facebook-ads/FacebookAuthCallback';

/**
 * Facebook OAuth Callback Page
 * Trang xử lý callback từ Facebook OAuth
 */
const FacebookAuthCallbackPage: React.FC = () => {
  return (
    <FacebookAuthCallback
      successRedirect="/marketing/facebook-ads"
      errorRedirect="/marketing/facebook-ads"
      onSuccess={() => {
        console.log('Facebook authentication successful');
      }}
      onError={(error) => {
        console.error('Facebook authentication failed:', error);
      }}
    />
  );
};

export default FacebookAuthCallbackPage;
