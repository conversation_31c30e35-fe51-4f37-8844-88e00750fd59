import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Icon,
} from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import FacebookCampaignManager from '../../components/facebook-ads/FacebookCampaignManager';

// interface FacebookCampaign {
//   id: string;
//   campaignId: string;
//   name: string;
//   objective: string;
//   status: 'active' | 'paused' | 'archived';
//   budget: number;
//   budgetType: 'daily' | 'lifetime';
//   spend: number;
//   impressions: number;
//   clicks: number;
//   ctr: number;
//   cpc: number;
//   startDate: string;
//   endDate?: string;
//   accountId: string;
//   accountName: string;
// }

/**
 * Facebook Campaigns Management Page
 * Trang quản lý chiến dịch Facebook Ads
 */
const FacebookCampaignsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  const handleCreateCampaign = () => {
    console.log('Create campaign');
    // TODO: Navigate to create campaign page
    // navigate('/marketing/facebook-ads/campaigns/create');
  };

  const handleViewCampaign = (campaignId: string) => {
    console.log('View campaign:', campaignId);
    // TODO: Navigate to campaign detail page
    // navigate(`/marketing/facebook-ads/campaigns/${campaignId}`);
  };

  const handleEditCampaign = (campaignId: string) => {
    console.log('Edit campaign:', campaignId);
    // TODO: Navigate to edit campaign page
    // navigate(`/marketing/facebook-ads/campaigns/${campaignId}/edit`);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <MarketingViewHeader
        title={t('marketing:facebookAds.campaigns.title', 'Quản lý chiến dịch')}
        description={t('marketing:facebookAds.campaigns.description', 'Tạo và quản lý các chiến dịch quảng cáo Facebook')}
        icon="campaign"
        actions={
          <Button
            variant="primary"
            onClick={handleCreateCampaign}
          >
            <Icon name="plus" className="mr-2" />
            {t('marketing:facebookAds.campaigns.create', 'Tạo chiến dịch')}
          </Button>
        }
      />

      <FacebookCampaignManager
        showFilters={true}
        showCreateButton={true}
        onViewCampaign={handleViewCampaign}
        onEditCampaign={handleEditCampaign}
        onCreateCampaign={handleCreateCampaign}
      />
    </div>
  );
};

export default FacebookCampaignsPage;
