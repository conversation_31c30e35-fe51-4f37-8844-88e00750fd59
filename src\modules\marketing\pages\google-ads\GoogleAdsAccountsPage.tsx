import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Card,
  Table,
  Typography,
  Button,
  Chip,
  Skeleton,
  Modal,
} from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useSlideInForm } from '@/shared/hooks/form';
import { formatTimestamp } from '@/shared/utils/date';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  Plus,
  Settings,
  RefreshCw,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import useGoogleAdsAccounts from '../../hooks/google-ads/useGoogleAdsAccounts';
import type { GoogleAdsAccountDto } from '../../types/google-ads.types';

/**
 * Trang quản lý tài khoản Google Ads
 */
const GoogleAdsAccountsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const [searchParams] = useSearchParams();
  const { isOpen, openForm, closeForm } = useSlideInForm();

  // Hooks để lấy dữ liệu
  const googleAdsAccountsHooks = useGoogleAdsAccounts();
  const { data, isLoading, refetch } = googleAdsAccountsHooks.useAccounts({
    page: parseInt(searchParams.get('page') || '1'),
    limit: parseInt(searchParams.get('limit') || '10'),
    search: searchParams.get('search') || undefined,
  });

  // Handlers
  const handleAddAccount = useCallback(() => {
    openForm();
  }, [openForm]);

  const handleSyncAccount = useCallback((accountId: number) => {
    // TODO: Implement sync account
    console.log('Sync account:', accountId);
  }, []);

  const handleViewInGoogleAds = useCallback((customerId: string) => {
    window.open(`https://ads.google.com/aw/accounts?ocid=${customerId}`, '_blank');
  }, []);

  const handleEditAccount = useCallback((accountId: number) => {
    // TODO: Implement edit account
    console.log('Edit account:', accountId);
  }, []);

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Cấu hình columns cho table
  const columns = useMemo((): TableColumn<GoogleAdsAccountDto>[] => [
    {
      key: 'name',
      title: t('marketing:googleAds.accounts.table.name', 'Tên tài khoản'),
      dataIndex: 'name',
      sortable: true,
      render: (value: unknown, record: GoogleAdsAccountDto) => (
        <div>
          <Typography variant="subtitle2" className="font-medium">
            {value as string}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            ID: {record.customerId}
          </Typography>
        </div>
      ),
    },
    {
      key: 'email',
      title: t('marketing:googleAds.accounts.table.email', 'Email'),
      dataIndex: 'email',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted-foreground">
          {value as string}
        </Typography>
      ),
    },
    {
      key: 'currency',
      title: t('marketing:googleAds.accounts.table.currency', 'Tiền tệ'),
      dataIndex: 'currency',
      render: (value: unknown) => (
        <Chip variant="info" size="sm">
          {value as string}
        </Chip>
      ),
    },
    {
      key: 'status',
      title: t('marketing:googleAds.accounts.table.status', 'Trạng thái'),
      dataIndex: 'status',
      sortable: true,
      render: (value: unknown) => {
        const status = value as string;
        switch (status) {
          case 'ACTIVE':
            return (
              <Chip variant="success" size="sm">
                <CheckCircle className="h-3 w-3 mr-1" />
                {t('marketing:googleAds.accounts.status.active', 'Hoạt động')}
              </Chip>
            );
          case 'SUSPENDED':
            return (
              <Chip variant="danger" size="sm">
                <XCircle className="h-3 w-3 mr-1" />
                {t('marketing:googleAds.accounts.status.suspended', 'Tạm dừng')}
              </Chip>
            );
          case 'PENDING':
            return (
              <Chip variant="warning" size="sm">
                <AlertCircle className="h-3 w-3 mr-1" />
                {t('marketing:googleAds.accounts.status.pending', 'Chờ duyệt')}
              </Chip>
            );
          default:
            return (
              <Chip variant="default" size="sm">
                {status}
              </Chip>
            );
        }
      },
    },
    {
      key: 'lastSyncAt',
      title: t('marketing:googleAds.accounts.table.lastSync', 'Đồng bộ cuối'),
      dataIndex: 'lastSyncAt',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="caption" className="text-muted-foreground">
          {formatTimestamp(value, 'vi-VN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
          })}
        </Typography>
      ),
    },
    {
      key: 'actions',
      title: t('common:actions', 'Hành động'),
      dataIndex: 'id',
      render: (_value: unknown, record: GoogleAdsAccountDto) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSyncAccount(record.id)}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewInGoogleAds(record.customerId)}
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditAccount(record.id)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ], [t, handleSyncAccount, handleViewInGoogleAds, handleEditAccount]);

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    return {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };
  }, []);

  // Cấu hình data table
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams,
  }));



  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h1">
            {t('marketing:googleAds.accounts.title', 'Tài khoản Google Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mt-1">
            {t('marketing:googleAds.accounts.description', 'Quản lý và kết nối tài khoản Google Ads')}
          </Typography>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {t('common:refresh', 'Làm mới')}
          </Button>
          <Button onClick={handleAddAccount}>
            <Plus className="h-4 w-4 mr-2" />
            {t('marketing:googleAds.accounts.addAccount', 'Thêm tài khoản')}
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.accounts.stats.total', 'Tổng tài khoản')}
              </Typography>
              <Typography variant="h2" className="text-blue-600">
                {isLoading ? <Skeleton className="h-8 w-12" /> : data?.meta?.totalItems || 0}
              </Typography>
            </div>
            <CheckCircle className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.accounts.stats.active', 'Đang hoạt động')}
              </Typography>
              <Typography variant="h2" className="text-green-600">
                {isLoading ? (
                  <Skeleton className="h-8 w-12" />
                ) : (
                  data?.items?.filter(acc => acc.status === 'ACTIVE').length || 0
                )}
              </Typography>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.accounts.stats.needsAttention', 'Cần chú ý')}
              </Typography>
              <Typography variant="h2" className="text-orange-600">
                {isLoading ? (
                  <Skeleton className="h-8 w-12" />
                ) : (
                  data?.items?.filter(acc => acc.status !== 'ACTIVE').length || 0
                )}
              </Typography>
            </div>
            <AlertCircle className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Accounts Table */}
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.items || []}
          loading={isLoading}
        />
      </Card>

      {/* Add Account Form */}
      <Modal
        isOpen={isOpen}
        onClose={closeForm}
        title={t('marketing:googleAds.accounts.addAccount', 'Thêm tài khoản Google Ads')}
        size="lg"
      >
        <div className="p-6">
          <Typography variant="body1" className="text-center text-muted-foreground">
            {t('marketing:googleAds.accounts.addForm.comingSoon', 'Form thêm tài khoản đang được phát triển')}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

export default GoogleAdsAccountsPage;
