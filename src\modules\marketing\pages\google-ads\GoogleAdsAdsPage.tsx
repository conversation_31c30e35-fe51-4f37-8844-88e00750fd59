import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Table,
  Typography,
  Button,
  Chip,
  Skeleton,
  Modal,
  ResponsiveGrid,
} from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useSlideInForm } from '@/shared/hooks/form';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  Plus,
  Eye,
  Edit,
  Copy,
  RefreshCw,
  Settings,
  Play,
  Pause,
  BarChart3,
  FileText,
  Image,
  Video,
} from 'lucide-react';

// Mock interface cho Ads (sẽ thay thế bằng real types)
interface GoogleAdsAdDto {
  id: string;
  headline1: string;
  headline2: string;
  headline3: string;
  description1: string;
  description2: string;
  finalUrl: string;
  displayUrl: string;
  type: 'TEXT_AD' | 'RESPONSIVE_SEARCH_AD' | 'IMAGE_AD' | 'VIDEO_AD';
  status: 'ENABLED' | 'PAUSED' | 'REMOVED';
  campaignName: string;
  adGroupName: string;
  impressions: number;
  clicks: number;
  ctr: number;
  cost: number;
  conversions: number;
  conversionRate: number;
  qualityScore: number;
  adStrength: 'POOR' | 'AVERAGE' | 'GOOD' | 'EXCELLENT';
  createdAt: string;
  updatedAt: string;
}

/**
 * Trang quản lý quảng cáo Google Ads
 */
const GoogleAdsAdsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  const { isOpen, openForm, closeForm } = useSlideInForm();

  // Mock data - sẽ thay thế bằng real API call
  const mockData = {
    items: [
      {
        id: '1',
        headline1: 'Marketing Automation Tool',
        headline2: 'Boost Your Sales Today',
        headline3: 'Free Trial Available',
        description1: 'Automate your marketing campaigns and increase ROI with our powerful platform.',
        description2: 'Join thousands of satisfied customers. Start your free trial now!',
        finalUrl: 'https://example.com/marketing-automation',
        displayUrl: 'example.com/marketing-automation',
        type: 'RESPONSIVE_SEARCH_AD' as const,
        status: 'ENABLED' as const,
        campaignName: 'Marketing Tools Campaign',
        adGroupName: 'Automation Tools',
        impressions: 12450,
        clicks: 678,
        ctr: 5.44,
        cost: 15600000,
        conversions: 34,
        conversionRate: 5.01,
        qualityScore: 8,
        adStrength: 'GOOD' as const,
        createdAt: '1729680000000',
        updatedAt: '1729766400000',
      },
      // Thêm mock data khác...
    ],
    meta: {
      totalItems: 89,
      totalPages: 9,
      currentPage: 1,
      itemsPerPage: 10,
    },
  };

  const isLoading = false; // Mock loading state

  // Handlers
  const handleCreateAd = useCallback(() => {
    openForm();
  }, [openForm]);

  const handlePreviewAd = useCallback((adId: string) => {
    // TODO: Open ad preview modal
    console.log('Preview ad:', adId);
  }, []);

  const handleDuplicateAd = useCallback((adId: string) => {
    // TODO: Implement duplicate ad
    console.log('Duplicate ad:', adId);
  }, []);

  const handleEditAd = useCallback((adId: string) => {
    // TODO: Implement edit ad
    console.log('Edit ad:', adId);
  }, []);

  const handleRefresh = useCallback(() => {
    // TODO: Implement refresh
    console.log('Refresh ads');
  }, []);

  // Cấu hình columns cho table
  const columns = useMemo((): TableColumn<GoogleAdsAdDto>[] => [
    {
      key: 'ad',
      title: t('marketing:googleAds.ads.table.ad', 'Quảng cáo'),
      dataIndex: 'headline1',
      sortable: true,
      render: (_value: unknown, record: GoogleAdsAdDto) => (
        <div className="max-w-xs">
          <div className="flex items-center space-x-2 mb-1">
            <Typography variant="subtitle2" className="font-medium truncate">
              {record.headline1}
            </Typography>
            {record.type === 'VIDEO_AD' && <Video className="h-4 w-4 text-red-500" />}
            {record.type === 'IMAGE_AD' && <Image className="h-4 w-4 text-blue-500" />}
            {record.type === 'TEXT_AD' && <FileText className="h-4 w-4 text-gray-500" />}
          </div>
          <Typography variant="caption" className="text-muted-foreground line-clamp-2">
            {record.description1}
          </Typography>
          <Typography variant="caption" className="text-green-600 mt-1">
            {record.displayUrl}
          </Typography>
        </div>
      ),
    },
    {
      key: 'type',
      title: t('marketing:googleAds.ads.table.type', 'Loại'),
      dataIndex: 'type',
      render: (value: unknown) => {
        const type = value as string;
        const typeVariants: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'> = {
          TEXT_AD: 'default',
          RESPONSIVE_SEARCH_AD: 'primary',
          IMAGE_AD: 'info',
          VIDEO_AD: 'warning',
        };
        return (
          <Chip variant={typeVariants[type] || 'default'} size="sm">
            {type.replace('_', ' ')}
          </Chip>
        );
      },
    },
    {
      key: 'status',
      title: t('marketing:googleAds.ads.table.status', 'Trạng thái'),
      dataIndex: 'status',
      sortable: true,
      render: (value: unknown) => {
        const status = value as string;
        switch (status) {
          case 'ENABLED':
            return (
              <Chip variant="success" size="sm">
                <Play className="h-3 w-3 mr-1" />
                {t('marketing:googleAds.ads.status.enabled', 'Đang chạy')}
              </Chip>
            );
          case 'PAUSED':
            return (
              <Chip variant="warning" size="sm">
                <Pause className="h-3 w-3 mr-1" />
                {t('marketing:googleAds.ads.status.paused', 'Tạm dừng')}
              </Chip>
            );
          case 'REMOVED':
            return (
              <Chip variant="danger" size="sm">
                {t('marketing:googleAds.ads.status.removed', 'Đã xóa')}
              </Chip>
            );
          default:
            return (
              <Chip variant="default" size="sm">
                {status}
              </Chip>
            );
        }
      },
    },
    {
      key: 'adStrength',
      title: t('marketing:googleAds.ads.table.adStrength', 'Độ mạnh'),
      dataIndex: 'adStrength',
      sortable: true,
      render: (value: unknown) => {
        const strength = value as string;
        const strengthVariants: Record<string, 'danger' | 'warning' | 'info' | 'success'> = {
          POOR: 'danger',
          AVERAGE: 'warning',
          GOOD: 'info',
          EXCELLENT: 'success',
        };
        return (
          <Chip variant={strengthVariants[strength] || 'default'} size="sm">
            {t(`marketing:googleAds.ads.strength.${strength.toLowerCase()}`, strength)}
          </Chip>
        );
      },
    },
    {
      key: 'impressions',
      title: t('marketing:googleAds.ads.table.impressions', 'Hiển thị'),
      dataIndex: 'impressions',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {(value as number)?.toLocaleString('vi-VN')}
        </Typography>
      ),
    },
    {
      key: 'clicks',
      title: t('marketing:googleAds.ads.table.clicks', 'Click'),
      dataIndex: 'clicks',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {(value as number)?.toLocaleString('vi-VN')}
        </Typography>
      ),
    },
    {
      key: 'ctr',
      title: t('marketing:googleAds.ads.table.ctr', 'CTR'),
      dataIndex: 'ctr',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {((value as number) || 0).toFixed(2)}%
        </Typography>
      ),
    },
    {
      key: 'cost',
      title: t('marketing:googleAds.ads.table.cost', 'Chi phí'),
      dataIndex: 'cost',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2" className="font-medium text-red-600">
          {(value as number)?.toLocaleString('vi-VN')} ₫
        </Typography>
      ),
    },
    {
      key: 'conversions',
      title: t('marketing:googleAds.ads.table.conversions', 'Chuyển đổi'),
      dataIndex: 'conversions',
      sortable: true,
      render: (value: unknown, record: GoogleAdsAdDto) => (
        <div>
          <Typography variant="body2" className="font-medium">
            {(value as number)?.toLocaleString('vi-VN')}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.conversionRate.toFixed(2)}%
          </Typography>
        </div>
      ),
    },
    {
      key: 'actions',
      title: t('common:actions', 'Hành động'),
      dataIndex: 'id',
      render: (_value: unknown, record: GoogleAdsAdDto) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePreviewAd(record.id)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDuplicateAd(record.id)}
          >
            <Copy className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditAd(record.id)}
          >
            <Edit className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ], [t, handlePreviewAd, handleDuplicateAd, handleEditAd]);

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    return {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };
  }, []);

  // Cấu hình data table
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams,
  }));

  // Tính toán stats
  const stats = useMemo(() => {
    if (!mockData?.items) return { totalCost: 0, totalImpressions: 0, totalClicks: 0, avgCTR: 0 };

    const totalCost = mockData.items.reduce((sum, ad) => sum + (ad.cost || 0), 0);
    const totalImpressions = mockData.items.reduce((sum, ad) => sum + (ad.impressions || 0), 0);
    const totalClicks = mockData.items.reduce((sum, ad) => sum + (ad.clicks || 0), 0);
    const avgCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

    return { totalCost, totalImpressions, totalClicks, avgCTR };
  }, [mockData?.items]);



  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h1">
            {t('marketing:googleAds.ads.title', 'Quảng cáo Google Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mt-1">
            {t('marketing:googleAds.ads.description', 'Tạo và quản lý nội dung quảng cáo')}
          </Typography>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {t('common:refresh', 'Làm mới')}
          </Button>
          <Button onClick={handleCreateAd}>
            <Plus className="h-4 w-4 mr-2" />
            {t('marketing:googleAds.ads.createAd', 'Tạo quảng cáo')}
          </Button>
        </div>
      </div>

      {/* Performance Stats */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4 }}>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.ads.stats.totalCost', 'Tổng chi phí')}
              </Typography>
              <Typography variant="h2" className="text-red-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : `${stats.totalCost.toLocaleString('vi-VN')} ₫`}
              </Typography>
            </div>
            <BarChart3 className="h-8 w-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.ads.stats.impressions', 'Lượt hiển thị')}
              </Typography>
              <Typography variant="h2" className="text-blue-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : stats.totalImpressions.toLocaleString('vi-VN')}
              </Typography>
            </div>
            <Eye className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.ads.stats.clicks', 'Lượt click')}
              </Typography>
              <Typography variant="h2" className="text-green-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : stats.totalClicks.toLocaleString('vi-VN')}
              </Typography>
            </div>
            <FileText className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.ads.stats.avgCTR', 'CTR trung bình')}
              </Typography>
              <Typography variant="h2" className="text-purple-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : `${stats.avgCTR.toFixed(2)}%`}
              </Typography>
            </div>
            <Settings className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Ads Table */}
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={mockData?.items || []}
          loading={isLoading}
        />
      </Card>

      {/* Create Ad Form */}
      <Modal
        isOpen={isOpen}
        onClose={closeForm}
        title={t('marketing:googleAds.ads.createAd', 'Tạo quảng cáo Google Ads')}
        size="lg"
      >
        <div className="p-6">
          <Typography variant="body1" className="text-center text-muted-foreground">
            {t('marketing:googleAds.ads.createForm.comingSoon', 'Form tạo quảng cáo đang được phát triển')}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

export default GoogleAdsAdsPage;
