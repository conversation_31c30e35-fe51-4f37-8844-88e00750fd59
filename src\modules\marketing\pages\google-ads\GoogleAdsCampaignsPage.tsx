import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Card,
  Table,
  Typography,
  Button,
  Chip,
  Skeleton,
  Modal,
} from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useSlideInForm } from '@/shared/hooks/form';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  Plus,
  Play,
  Pause,
  BarChart3,
  Settings,
  RefreshCw,
  TrendingUp,
  DollarSign,
  Eye,
  MousePointer,
} from 'lucide-react';
import useGoogleAdsCampaigns from '../../hooks/google-ads/useGoogleAdsCampaigns';
import type { GoogleAdsCampaignDto } from '../../types/google-ads.types';

/**
 * Trang quản lý chiến dịch Google Ads
 */
const GoogleAdsCampaignsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const [searchParams] = useSearchParams();
  const { isOpen, openForm, closeForm } = useSlideInForm();

  // Hooks để lấy dữ liệu
  const googleAdsCampaignsHooks = useGoogleAdsCampaigns();
  const { data, isLoading, refetch } = googleAdsCampaignsHooks.useCampaigns({
    page: parseInt(searchParams.get('page') || '1'),
    limit: parseInt(searchParams.get('limit') || '10'),
    search: searchParams.get('search') || undefined,
  });

  // Handlers
  const handleCreateCampaign = useCallback(() => {
    openForm();
  }, [openForm]);

  const handleToggleCampaign = useCallback((campaignId: number, currentStatus: string) => {
    // TODO: Implement toggle campaign
    console.log('Toggle campaign:', campaignId, currentStatus);
  }, []);

  const handleViewAnalytics = useCallback((campaignId: number) => {
    // TODO: Navigate to analytics page
    console.log('View analytics:', campaignId);
  }, []);

  const handleEditCampaign = useCallback((campaignId: number) => {
    // TODO: Implement edit campaign
    console.log('Edit campaign:', campaignId);
  }, []);

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Cấu hình columns cho table
  const columns = useMemo((): TableColumn<GoogleAdsCampaignDto>[] => [
    {
      key: 'name',
      title: t('marketing:googleAds.campaigns.table.name', 'Tên chiến dịch'),
      dataIndex: 'name',
      sortable: true,
      render: (value: unknown, record: GoogleAdsCampaignDto) => (
        <div>
          <Typography variant="subtitle2" className="font-medium">
            {value as string}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            ID: {record.id}
          </Typography>
        </div>
      ),
    },
    {
      key: 'type',
      title: t('marketing:googleAds.campaigns.table.type', 'Loại'),
      dataIndex: 'type',
      render: (value: unknown) => {
        const type = value as string;
        const typeVariants: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'> = {
          SEARCH: 'primary',
          DISPLAY: 'info',
          SHOPPING: 'success',
          VIDEO: 'warning',
          DISCOVERY: 'default',
        };
        return (
          <Chip variant={typeVariants[type] || 'default'} size="sm">
            {type}
          </Chip>
        );
      },
    },
    {
      key: 'status',
      title: t('marketing:googleAds.campaigns.table.status', 'Trạng thái'),
      dataIndex: 'status',
      sortable: true,
      render: (value: unknown) => {
        const status = value as string;
        switch (status) {
          case 'ENABLED':
            return (
              <Chip variant="success" size="sm">
                <Play className="h-3 w-3 mr-1" />
                {t('marketing:googleAds.campaigns.status.enabled', 'Đang chạy')}
              </Chip>
            );
          case 'PAUSED':
            return (
              <Chip variant="warning" size="sm">
                <Pause className="h-3 w-3 mr-1" />
                {t('marketing:googleAds.campaigns.status.paused', 'Tạm dừng')}
              </Chip>
            );
          case 'REMOVED':
            return (
              <Chip variant="danger" size="sm">
                {t('marketing:googleAds.campaigns.status.removed', 'Đã xóa')}
              </Chip>
            );
          default:
            return (
              <Chip variant="default" size="sm">
                {status}
              </Chip>
            );
        }
      },
    },
    {
      key: 'budget',
      title: t('marketing:googleAds.campaigns.table.budget', 'Ngân sách'),
      dataIndex: 'budget',
      sortable: true,
      render: (value: unknown, record: GoogleAdsCampaignDto) => (
        <div>
          <Typography variant="body2" className="font-medium">
            {(value as number)?.toLocaleString('vi-VN')} ₫
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            /{record.budgetType === 'DAILY' ? 'ngày' : 'tháng'}
          </Typography>
        </div>
      ),
    },
    {
      key: 'impressions',
      title: t('marketing:googleAds.campaigns.table.impressions', 'Hiển thị'),
      dataIndex: 'impressions',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {(value as number)?.toLocaleString('vi-VN') || '0'}
        </Typography>
      ),
    },
    {
      key: 'clicks',
      title: t('marketing:googleAds.campaigns.table.clicks', 'Click'),
      dataIndex: 'clicks',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {(value as number)?.toLocaleString('vi-VN') || '0'}
        </Typography>
      ),
    },
    {
      key: 'ctr',
      title: t('marketing:googleAds.campaigns.table.ctr', 'CTR'),
      dataIndex: 'ctr',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {((value as number) || 0).toFixed(2)}%
        </Typography>
      ),
    },
    {
      key: 'cost',
      title: t('marketing:googleAds.campaigns.table.cost', 'Chi phí'),
      dataIndex: 'cost',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2" className="font-medium text-red-600">
          {(value as number)?.toLocaleString('vi-VN')} ₫
        </Typography>
      ),
    },
    {
      key: 'actions',
      title: t('common:actions', 'Hành động'),
      dataIndex: 'id',
      render: (_value: unknown, record: GoogleAdsCampaignDto) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleToggleCampaign(record.id, record.status)}
          >
            {record.status === 'ENABLED' ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewAnalytics(record.id)}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditCampaign(record.id)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ], [t, handleToggleCampaign, handleViewAnalytics, handleEditCampaign]);

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    return {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };
  }, []);

  // Cấu hình data table
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams,
  }));

  // Mock stats
  const stats = useMemo(() => {
    if (!data?.items) return { totalCost: 0, totalImpressions: 0, totalClicks: 0, avgCTR: 0 };

    const totalCost = data.items.reduce((sum, campaign) => sum + (campaign.cost || 0), 0);
    const totalImpressions = data.items.reduce((sum, campaign) => sum + (campaign.impressions || 0), 0);
    const totalClicks = data.items.reduce((sum, campaign) => sum + (campaign.clicks || 0), 0);
    const avgCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

    return { totalCost, totalImpressions, totalClicks, avgCTR };
  }, [data?.items]);



  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h1">
            {t('marketing:googleAds.campaigns.title', 'Chiến dịch Google Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mt-1">
            {t('marketing:googleAds.campaigns.description', 'Quản lý và theo dõi hiệu suất chiến dịch')}
          </Typography>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {t('common:refresh', 'Làm mới')}
          </Button>
          <Button onClick={handleCreateCampaign}>
            <Plus className="h-4 w-4 mr-2" />
            {t('marketing:googleAds.campaigns.createCampaign', 'Tạo chiến dịch')}
          </Button>
        </div>
      </div>

      {/* Performance Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.campaigns.stats.totalCost', 'Tổng chi phí')}
              </Typography>
              <Typography variant="h2" className="text-red-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : `${stats.totalCost.toLocaleString('vi-VN')} ₫`}
              </Typography>
            </div>
            <DollarSign className="h-8 w-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.campaigns.stats.impressions', 'Lượt hiển thị')}
              </Typography>
              <Typography variant="h2" className="text-blue-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : stats.totalImpressions.toLocaleString('vi-VN')}
              </Typography>
            </div>
            <Eye className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.campaigns.stats.clicks', 'Lượt click')}
              </Typography>
              <Typography variant="h2" className="text-green-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : stats.totalClicks.toLocaleString('vi-VN')}
              </Typography>
            </div>
            <MousePointer className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.campaigns.stats.avgCTR', 'CTR trung bình')}
              </Typography>
              <Typography variant="h2" className="text-purple-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : `${stats.avgCTR.toFixed(2)}%`}
              </Typography>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
      </div>

      {/* Campaigns Table */}
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.items || []}
          loading={isLoading}
        />
      </Card>

      {/* Create Campaign Form */}
      <Modal
        isOpen={isOpen}
        onClose={closeForm}
        title={t('marketing:googleAds.campaigns.createCampaign', 'Tạo chiến dịch Google Ads')}
        size="lg"
      >
        <div className="p-6">
          <Typography variant="body1" className="text-center text-muted-foreground">
            {t('marketing:googleAds.campaigns.createForm.comingSoon', 'Form tạo chiến dịch đang được phát triển')}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

export default GoogleAdsCampaignsPage;
