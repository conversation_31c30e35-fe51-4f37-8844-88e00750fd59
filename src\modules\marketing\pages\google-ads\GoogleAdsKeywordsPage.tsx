import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Table,
  Typography,
  Button,
  Chip,
  Skeleton,
  Modal,
  ResponsiveGrid,
} from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useSlideInForm } from '@/shared/hooks/form';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  Plus,
  Search,
  TrendingUp,
  RefreshCw,
  Settings,
  Target,
  DollarSign,
  BarChart3,
} from 'lucide-react';

// Mock interface cho Keywords (sẽ thay thế bằng real types)
interface GoogleAdsKeywordDto {
  id: string;
  keyword: string;
  matchType: 'EXACT' | 'PHRASE' | 'BROAD';
  status: 'ENABLED' | 'PAUSED' | 'REMOVED';
  campaignName: string;
  adGroupName: string;
  maxCpc: number;
  qualityScore: number;
  impressions: number;
  clicks: number;
  ctr: number;
  avgPosition: number;
  cost: number;
  conversions: number;
  conversionRate: number;
  costPerConversion: number;
  searchVolume: number;
  competition: 'LOW' | 'MEDIUM' | 'HIGH';
  createdAt: string;
  updatedAt: string;
}

/**
 * Trang quản lý từ khóa Google Ads
 */
const GoogleAdsKeywordsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  const { isOpen, openForm, closeForm } = useSlideInForm();

  // Mock data - sẽ thay thế bằng real API call
  const mockData = {
    items: [
      {
        id: '1',
        keyword: 'marketing automation',
        matchType: 'EXACT' as const,
        status: 'ENABLED' as const,
        campaignName: 'Marketing Tools Campaign',
        adGroupName: 'Automation Tools',
        maxCpc: 25000,
        qualityScore: 8,
        impressions: 15420,
        clicks: 892,
        ctr: 5.78,
        avgPosition: 2.1,
        cost: 18500000,
        conversions: 45,
        conversionRate: 5.04,
        costPerConversion: 411111,
        searchVolume: 8100,
        competition: 'HIGH' as const,
        createdAt: '1729680000000',
        updatedAt: '1729766400000',
      },
      // Thêm mock data khác...
    ],
    meta: {
      totalItems: 156,
      totalPages: 16,
      currentPage: 1,
      itemsPerPage: 10,
    },
  };

  const isLoading = false; // Mock loading state

  // Handlers
  const handleAddKeyword = useCallback(() => {
    openForm();
  }, [openForm]);

  const handleViewKeywordDetails = useCallback((keywordId: string) => {
    // TODO: Navigate to keyword details
    console.log('View keyword details:', keywordId);
  }, []);

  const handleEditKeyword = useCallback((keywordId: string) => {
    // TODO: Implement edit keyword
    console.log('Edit keyword:', keywordId);
  }, []);

  const handleRefresh = useCallback(() => {
    // TODO: Implement refresh
    console.log('Refresh keywords');
  }, []);

  const handleKeywordResearch = useCallback(() => {
    // TODO: Open keyword research tool
    console.log('Open keyword research');
  }, []);

  // Cấu hình columns cho table
  const columns = useMemo((): TableColumn<GoogleAdsKeywordDto>[] => [
    {
      key: 'keyword',
      title: t('marketing:googleAds.keywords.table.keyword', 'Từ khóa'),
      dataIndex: 'keyword',
      sortable: true,
      render: (value: unknown, record: GoogleAdsKeywordDto) => (
        <div>
          <Typography variant="subtitle2" className="font-medium">
            {value as string}
          </Typography>
          <div className="flex items-center space-x-2 mt-1">
            <Chip
              variant={record.matchType === 'EXACT' ? 'success' : record.matchType === 'PHRASE' ? 'warning' : 'info'}
              size="sm"
            >
              {record.matchType}
            </Chip>
            <Typography variant="caption" className="text-muted-foreground">
              {record.adGroupName}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      title: t('marketing:googleAds.keywords.table.status', 'Trạng thái'),
      dataIndex: 'status',
      sortable: true,
      render: (value: unknown) => {
        const status = value as string;
        switch (status) {
          case 'ENABLED':
            return (
              <Chip variant="success" size="sm">
                {t('marketing:googleAds.keywords.status.enabled', 'Hoạt động')}
              </Chip>
            );
          case 'PAUSED':
            return (
              <Chip variant="warning" size="sm">
                {t('marketing:googleAds.keywords.status.paused', 'Tạm dừng')}
              </Chip>
            );
          case 'REMOVED':
            return (
              <Chip variant="danger" size="sm">
                {t('marketing:googleAds.keywords.status.removed', 'Đã xóa')}
              </Chip>
            );
          default:
            return (
              <Chip variant="default" size="sm">
                {status}
              </Chip>
            );
        }
      },
    },
    {
      key: 'qualityScore',
      title: t('marketing:googleAds.keywords.table.qualityScore', 'Điểm chất lượng'),
      dataIndex: 'qualityScore',
      sortable: true,
      render: (value: unknown) => {
        const score = value as number;
        const variant = score >= 7 ? 'success' : score >= 5 ? 'warning' : 'danger';
        return (
          <Chip variant={variant} size="sm">
            {score}/10
          </Chip>
        );
      },
    },
    {
      key: 'maxCpc',
      title: t('marketing:googleAds.keywords.table.maxCpc', 'CPC tối đa'),
      dataIndex: 'maxCpc',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2" className="font-medium">
          {(value as number)?.toLocaleString('vi-VN')} ₫
        </Typography>
      ),
    },
    {
      key: 'impressions',
      title: t('marketing:googleAds.keywords.table.impressions', 'Hiển thị'),
      dataIndex: 'impressions',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {(value as number)?.toLocaleString('vi-VN')}
        </Typography>
      ),
    },
    {
      key: 'clicks',
      title: t('marketing:googleAds.keywords.table.clicks', 'Click'),
      dataIndex: 'clicks',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {(value as number)?.toLocaleString('vi-VN')}
        </Typography>
      ),
    },
    {
      key: 'ctr',
      title: t('marketing:googleAds.keywords.table.ctr', 'CTR'),
      dataIndex: 'ctr',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">
          {((value as number) || 0).toFixed(2)}%
        </Typography>
      ),
    },
    {
      key: 'cost',
      title: t('marketing:googleAds.keywords.table.cost', 'Chi phí'),
      dataIndex: 'cost',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2" className="font-medium text-red-600">
          {(value as number)?.toLocaleString('vi-VN')} ₫
        </Typography>
      ),
    },
    {
      key: 'conversions',
      title: t('marketing:googleAds.keywords.table.conversions', 'Chuyển đổi'),
      dataIndex: 'conversions',
      sortable: true,
      render: (value: unknown, record: GoogleAdsKeywordDto) => (
        <div>
          <Typography variant="body2" className="font-medium">
            {(value as number)?.toLocaleString('vi-VN')}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.conversionRate.toFixed(2)}%
          </Typography>
        </div>
      ),
    },
    {
      key: 'actions',
      title: t('common:actions', 'Hành động'),
      dataIndex: 'id',
      render: (_value: unknown, record: GoogleAdsKeywordDto) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewKeywordDetails(record.id)}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditKeyword(record.id)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ], [t, handleViewKeywordDetails, handleEditKeyword]);

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    return {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };
  }, []);

  // Cấu hình data table
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams,
  }));

  // Tính toán stats
  const stats = useMemo(() => {
    if (!mockData?.items) return { totalCost: 0, totalImpressions: 0, totalClicks: 0, avgQualityScore: 0 };

    const totalCost = mockData.items.reduce((sum, keyword) => sum + (keyword.cost || 0), 0);
    const totalImpressions = mockData.items.reduce((sum, keyword) => sum + (keyword.impressions || 0), 0);
    const totalClicks = mockData.items.reduce((sum, keyword) => sum + (keyword.clicks || 0), 0);
    const avgQualityScore = mockData.items.reduce((sum, keyword) => sum + (keyword.qualityScore || 0), 0) / mockData.items.length;

    return { totalCost, totalImpressions, totalClicks, avgQualityScore };
  }, [mockData?.items]);



  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h1">
            {t('marketing:googleAds.keywords.title', 'Từ khóa Google Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mt-1">
            {t('marketing:googleAds.keywords.description', 'Quản lý và tối ưu hóa từ khóa quảng cáo')}
          </Typography>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleKeywordResearch}>
            <Search className="h-4 w-4 mr-2" />
            {t('marketing:googleAds.keywords.research', 'Nghiên cứu từ khóa')}
          </Button>
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {t('common:refresh', 'Làm mới')}
          </Button>
          <Button onClick={handleAddKeyword}>
            <Plus className="h-4 w-4 mr-2" />
            {t('marketing:googleAds.keywords.addKeyword', 'Thêm từ khóa')}
          </Button>
        </div>
      </div>

      {/* Performance Stats */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4 }}>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.keywords.stats.totalCost', 'Tổng chi phí')}
              </Typography>
              <Typography variant="h2" className="text-red-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : `${stats.totalCost.toLocaleString('vi-VN')} ₫`}
              </Typography>
            </div>
            <DollarSign className="h-8 w-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.keywords.stats.impressions', 'Lượt hiển thị')}
              </Typography>
              <Typography variant="h2" className="text-blue-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : stats.totalImpressions.toLocaleString('vi-VN')}
              </Typography>
            </div>
            <TrendingUp className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.keywords.stats.clicks', 'Lượt click')}
              </Typography>
              <Typography variant="h2" className="text-green-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : stats.totalClicks.toLocaleString('vi-VN')}
              </Typography>
            </div>
            <Target className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.keywords.stats.avgQualityScore', 'Điểm chất lượng TB')}
              </Typography>
              <Typography variant="h2" className="text-purple-600">
                {isLoading ? <Skeleton className="h-8 w-20" /> : `${stats.avgQualityScore.toFixed(1)}/10`}
              </Typography>
            </div>
            <BarChart3 className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Keywords Table */}
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={mockData?.items || []}
          loading={isLoading}
        />
      </Card>

      {/* Add Keyword Form */}
      <Modal
        isOpen={isOpen}
        onClose={closeForm}
        title={t('marketing:googleAds.keywords.addKeyword', 'Thêm từ khóa Google Ads')}
        size="lg"
      >
        <div className="p-6">
          <Typography variant="body1" className="text-center text-muted-foreground">
            {t('marketing:googleAds.keywords.addForm.comingSoon', 'Form thêm từ khóa đang được phát triển')}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

export default GoogleAdsKeywordsPage;
