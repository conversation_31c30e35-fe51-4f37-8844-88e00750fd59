import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Chip,
  Skeleton,
} from '@/shared/components/common';
import {
  TrendingUp,
  DollarSign,
  Eye,
  MousePointer,
  Users,
  BarChart3,
  Plus,
  Settings,
} from 'lucide-react';
import useGoogleAdsAccounts from '../../hooks/google-ads/useGoogleAdsAccounts';
import useGoogleAdsCampaigns from '../../hooks/google-ads/useGoogleAdsCampaigns';

/**
 * Trang tổng quan Google Ads
 */
const GoogleAdsOverviewPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();

  // Hooks để lấy dữ liệu
  const googleAdsAccountsHooks = useGoogleAdsAccounts();
  const googleAdsCampaignsHooks = useGoogleAdsCampaigns();

  const { data: accountsData, isLoading: isLoadingAccounts } = googleAdsAccountsHooks.useAccounts({});
  const { data: campaignsData } = googleAdsCampaignsHooks.useCampaigns({});

  // Mock data cho demo (sẽ thay thế bằng real data)
  const mockStats = {
    totalSpend: 125000,
    totalImpressions: 2450000,
    totalClicks: 18500,
    averageCTR: 0.75,
    totalConversions: 1250,
    averageCPC: 6.76,
    activeAccounts: accountsData?.items?.filter(acc => acc.status === 'ACTIVE').length || 0,
    activeCampaigns: campaignsData?.items?.filter(camp => camp.status === 'ENABLED').length || 0,
  };

  const handleCreateCampaign = () => {
    navigate('/marketing/google-ads/campaigns?action=create');
  };

  const handleViewAccounts = () => {
    navigate('/marketing/google-ads/accounts');
  };

  const handleViewCampaigns = () => {
    navigate('/marketing/google-ads/campaigns');
  };

  const handleViewReports = () => {
    navigate('/marketing/google-ads/reports');
  };

  const handleSettings = () => {
    navigate('/marketing/google-ads/settings');
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:googleAds.overview.stats.totalSpend', 'Tổng chi phí')}
            </Typography>
            <DollarSign className="h-4 w-4 text-green-600" />
          </div>
          <Typography variant="h2" className="text-green-600">
            {isLoadingAccounts ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              `${mockStats.totalSpend.toLocaleString('vi-VN')} ₫`
            )}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:googleAds.overview.stats.thisMonth', 'Tháng này')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:googleAds.overview.stats.impressions', 'Lượt hiển thị')}
            </Typography>
            <Eye className="h-4 w-4 text-blue-600" />
          </div>
          <Typography variant="h2" className="text-blue-600">
            {isLoadingAccounts ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              mockStats.totalImpressions.toLocaleString('vi-VN')
            )}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            {t('marketing:googleAds.overview.stats.thisMonth', 'Tháng này')}
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:googleAds.overview.stats.clicks', 'Lượt click')}
            </Typography>
            <MousePointer className="h-4 w-4 text-orange-600" />
          </div>
          <Typography variant="h2" className="text-orange-600">
            {isLoadingAccounts ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              mockStats.totalClicks.toLocaleString('vi-VN')
            )}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            CTR: {mockStats.averageCTR}%
          </Typography>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="caption" className="font-medium text-muted-foreground">
              {t('marketing:googleAds.overview.stats.conversions', 'Chuyển đổi')}
            </Typography>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </div>
          <Typography variant="h2" className="text-purple-600">
            {isLoadingAccounts ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              mockStats.totalConversions.toLocaleString('vi-VN')
            )}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground mt-1">
            CPC: {mockStats.averageCPC.toLocaleString('vi-VN')} ₫
          </Typography>
        </Card>
      </div>

      {/* Account & Campaign Status */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h3">
              {t('marketing:googleAds.overview.accounts.title', 'Tài khoản Google Ads')}
            </Typography>
            <Button variant="outline" size="sm" onClick={handleViewAccounts}>
              {t('common:viewAll', 'Xem tất cả')}
            </Button>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Typography variant="body2">
                {t('marketing:googleAds.overview.accounts.active', 'Tài khoản hoạt động')}
              </Typography>
              <Chip variant="success" size="sm">
                {mockStats.activeAccounts}
              </Chip>
            </div>

            <div className="flex items-center justify-between">
              <Typography variant="body2">
                {t('marketing:googleAds.overview.accounts.total', 'Tổng tài khoản')}
              </Typography>
              <Chip variant="info" size="sm">
                {accountsData?.meta?.totalItems || 0}
              </Chip>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h3">
              {t('marketing:googleAds.overview.campaigns.title', 'Chiến dịch')}
            </Typography>
            <Button variant="outline" size="sm" onClick={handleViewCampaigns}>
              {t('common:viewAll', 'Xem tất cả')}
            </Button>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Typography variant="body2">
                {t('marketing:googleAds.overview.campaigns.active', 'Chiến dịch đang chạy')}
              </Typography>
              <Chip variant="success" size="sm">
                {mockStats.activeCampaigns}
              </Chip>
            </div>

            <div className="flex items-center justify-between">
              <Typography variant="body2">
                {t('marketing:googleAds.overview.campaigns.total', 'Tổng chiến dịch')}
              </Typography>
              <Chip variant="info" size="sm">
                {campaignsData?.meta?.totalItems || 0}
              </Chip>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card
          className="p-4 cursor-pointer hover:shadow-lg transition-shadow"
          onClick={handleCreateCampaign}
          hoverable
        >
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded bg-primary/10 flex items-center justify-center text-primary">
              <Plus className="h-5 w-5" />
            </div>
            <div>
              <Typography variant="subtitle2">
                {t('marketing:googleAds.overview.actions.createCampaign', 'Tạo chiến dịch')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.overview.actions.createCampaignDesc', 'Tạo chiến dịch mới')}
              </Typography>
            </div>
          </div>
        </Card>

        <Card
          className="p-4 cursor-pointer hover:shadow-lg transition-shadow"
          onClick={handleViewAccounts}
          hoverable
        >
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded bg-blue-100 flex items-center justify-center text-blue-600">
              <Users className="h-5 w-5" />
            </div>
            <div>
              <Typography variant="subtitle2">
                {t('marketing:googleAds.overview.actions.manageAccounts', 'Quản lý tài khoản')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.overview.actions.manageAccountsDesc', 'Kết nối & quản lý')}
              </Typography>
            </div>
          </div>
        </Card>

        <Card
          className="p-4 cursor-pointer hover:shadow-lg transition-shadow"
          onClick={handleViewReports}
          hoverable
        >
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded bg-green-100 flex items-center justify-center text-green-600">
              <BarChart3 className="h-5 w-5" />
            </div>
            <div>
              <Typography variant="subtitle2">
                {t('marketing:googleAds.overview.actions.viewReports', 'Xem báo cáo')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.overview.actions.viewReportsDesc', 'Phân tích hiệu suất')}
              </Typography>
            </div>
          </div>
        </Card>

        <Card
          className="p-4 cursor-pointer hover:shadow-lg transition-shadow"
          onClick={handleSettings}
          hoverable
        >
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded bg-gray-100 flex items-center justify-center text-gray-600">
              <Settings className="h-5 w-5" />
            </div>
            <div>
              <Typography variant="subtitle2">
                {t('marketing:googleAds.overview.actions.settings', 'Cài đặt')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.overview.actions.settingsDesc', 'Cấu hình tích hợp')}
              </Typography>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default GoogleAdsOverviewPage;
