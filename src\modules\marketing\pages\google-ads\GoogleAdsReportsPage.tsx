import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Chip,
  Skeleton,
  ResponsiveGrid,
} from '@/shared/components/common';

import {
  Download,
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Eye,
  MousePointer,
  Target,
  BarChart3,
  PieChart,
  LineChart,
  RefreshCw,
} from 'lucide-react';

// Mock interface cho Reports data
interface ReportData {
  date: string;
  impressions: number;
  clicks: number;
  cost: number;
  conversions: number;
  ctr: number;
  cpc: number;
  conversionRate: number;
}

interface CampaignPerformance {
  campaignName: string;
  impressions: number;
  clicks: number;
  cost: number;
  conversions: number;
  ctr: number;
}

/**
 * Trang báo cáo Google Ads
 */
const GoogleAdsReportsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const [dateRange, setDateRange] = useState('last_30_days');
  const [isLoading, setIsLoading] = useState(false);

  const mockCampaignData: CampaignPerformance[] = [
    { campaignName: 'Marketing Tools Campaign', impressions: 45600, clicks: 2340, cost: 52800000, conversions: 125, ctr: 5.13 },
    { campaignName: 'Brand Awareness Campaign', impressions: 38200, clicks: 1890, cost: 41200000, conversions: 89, ctr: 4.95 },
    { campaignName: 'Product Launch Campaign', impressions: 29800, clicks: 1567, cost: 35600000, conversions: 78, ctr: 5.26 },
  ];

  // Tính toán tổng stats
  const totalStats = useMemo(() => {
    // Mock data cho charts
    const mockChartData: ReportData[] = [
      { date: '2025-01-01', impressions: 12500, clicks: 678, cost: 15600000, conversions: 34, ctr: 5.42, cpc: 23009, conversionRate: 5.01 },
      { date: '2025-01-02', impressions: 13200, clicks: 712, cost: 16800000, conversions: 38, ctr: 5.39, cpc: 23595, conversionRate: 5.34 },
      { date: '2025-01-03', impressions: 11800, clicks: 634, cost: 14200000, conversions: 29, ctr: 5.37, cpc: 22398, conversionRate: 4.57 },
      // Thêm data cho 30 ngày...
    ];

    const totalImpressions = mockChartData.reduce((sum, day) => sum + day.impressions, 0);
    const totalClicks = mockChartData.reduce((sum, day) => sum + day.clicks, 0);
    const totalCost = mockChartData.reduce((sum, day) => sum + day.cost, 0);
    const totalConversions = mockChartData.reduce((sum, day) => sum + day.conversions, 0);
    const avgCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
    const avgCPC = totalClicks > 0 ? totalCost / totalClicks : 0;
    const conversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;

    return {
      totalImpressions,
      totalClicks,
      totalCost,
      totalConversions,
      avgCTR,
      avgCPC,
      conversionRate,
    };
  }, []);

  // Handlers
  const handleDateRangeChange = useCallback((range: string) => {
    setDateRange(range);
    // TODO: Fetch new data based on date range
  }, []);

  const handleExportReport = useCallback(() => {
    // TODO: Implement export functionality
    console.log('Export report');
  }, []);

  const handleRefresh = useCallback(() => {
    setIsLoading(true);
    // TODO: Implement refresh
    setTimeout(() => setIsLoading(false), 1000);
  }, []);

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h1">
            {t('marketing:googleAds.reports.title', 'Báo cáo Google Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mt-1">
            {t('marketing:googleAds.reports.description', 'Phân tích hiệu suất và xu hướng chiến dịch')}
          </Typography>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <select
              value={dateRange}
              onChange={(e) => handleDateRangeChange(e.target.value)}
              className="bg-background border border-border rounded px-3 py-1 text-sm"
            >
              <option value="last_7_days">{t('marketing:googleAds.reports.dateRange.last7Days', '7 ngày qua')}</option>
              <option value="last_30_days">{t('marketing:googleAds.reports.dateRange.last30Days', '30 ngày qua')}</option>
              <option value="last_90_days">{t('marketing:googleAds.reports.dateRange.last90Days', '90 ngày qua')}</option>
              <option value="custom">{t('marketing:googleAds.reports.dateRange.custom', 'Tùy chỉnh')}</option>
            </select>
          </div>
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {t('common:refresh', 'Làm mới')}
          </Button>
          <Button variant="outline" onClick={handleExportReport}>
            <Download className="h-4 w-4 mr-2" />
            {t('marketing:googleAds.reports.export', 'Xuất báo cáo')}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4 }}>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.reports.metrics.totalCost', 'Tổng chi phí')}
              </Typography>
              <Typography variant="h2" className="text-red-600">
                {isLoading ? <Skeleton className="h-8 w-24" /> : `${totalStats.totalCost.toLocaleString('vi-VN')} ₫`}
              </Typography>
              <div className="flex items-center mt-1">
                <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                <Typography variant="caption" className="text-green-500">
                  +12.5%
                </Typography>
              </div>
            </div>
            <DollarSign className="h-8 w-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.reports.metrics.impressions', 'Lượt hiển thị')}
              </Typography>
              <Typography variant="h2" className="text-blue-600">
                {isLoading ? <Skeleton className="h-8 w-24" /> : totalStats.totalImpressions.toLocaleString('vi-VN')}
              </Typography>
              <div className="flex items-center mt-1">
                <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                <Typography variant="caption" className="text-green-500">
                  +8.3%
                </Typography>
              </div>
            </div>
            <Eye className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.reports.metrics.clicks', 'Lượt click')}
              </Typography>
              <Typography variant="h2" className="text-green-600">
                {isLoading ? <Skeleton className="h-8 w-24" /> : totalStats.totalClicks.toLocaleString('vi-VN')}
              </Typography>
              <div className="flex items-center mt-1">
                <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                <Typography variant="caption" className="text-red-500">
                  -2.1%
                </Typography>
              </div>
            </div>
            <MousePointer className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:googleAds.reports.metrics.conversions', 'Chuyển đổi')}
              </Typography>
              <Typography variant="h2" className="text-purple-600">
                {isLoading ? <Skeleton className="h-8 w-24" /> : totalStats.totalConversions.toLocaleString('vi-VN')}
              </Typography>
              <div className="flex items-center mt-1">
                <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                <Typography variant="caption" className="text-green-500">
                  +15.7%
                </Typography>
              </div>
            </div>
            <Target className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Charts Section */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2 }}>
        {/* Performance Trend Chart */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h3">
              {t('marketing:googleAds.reports.charts.performanceTrend', 'Xu hướng hiệu suất')}
            </Typography>
            <LineChart className="h-5 w-5 text-muted-foreground" />
          </div>
          <div className="h-64 flex items-center justify-center bg-muted/20 rounded">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:googleAds.reports.charts.comingSoon', 'Biểu đồ đang được phát triển')}
              </Typography>
            </div>
          </div>
        </Card>

        {/* Campaign Performance Chart */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h3">
              {t('marketing:googleAds.reports.charts.campaignPerformance', 'Hiệu suất chiến dịch')}
            </Typography>
            <PieChart className="h-5 w-5 text-muted-foreground" />
          </div>
          <div className="h-64 flex items-center justify-center bg-muted/20 rounded">
            <div className="text-center">
              <PieChart className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:googleAds.reports.charts.comingSoon', 'Biểu đồ đang được phát triển')}
              </Typography>
            </div>
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Campaign Performance Table */}
      <Card className="p-6">
        <Typography variant="h3" className="mb-4">
          {t('marketing:googleAds.reports.campaignPerformance.title', 'Hiệu suất theo chiến dịch')}
        </Typography>
        <div className="space-y-4">
          {mockCampaignData.map((campaign, index) => (
            <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div className="flex-1">
                <Typography variant="subtitle2" className="font-medium">
                  {campaign.campaignName}
                </Typography>
                <div className="flex items-center space-x-4 mt-2">
                  <div className="text-center">
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('marketing:googleAds.reports.table.impressions', 'Hiển thị')}
                    </Typography>
                    <Typography variant="body2" className="font-medium">
                      {campaign.impressions.toLocaleString('vi-VN')}
                    </Typography>
                  </div>
                  <div className="text-center">
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('marketing:googleAds.reports.table.clicks', 'Click')}
                    </Typography>
                    <Typography variant="body2" className="font-medium">
                      {campaign.clicks.toLocaleString('vi-VN')}
                    </Typography>
                  </div>
                  <div className="text-center">
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('marketing:googleAds.reports.table.cost', 'Chi phí')}
                    </Typography>
                    <Typography variant="body2" className="font-medium text-red-600">
                      {campaign.cost.toLocaleString('vi-VN')} ₫
                    </Typography>
                  </div>
                  <div className="text-center">
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('marketing:googleAds.reports.table.conversions', 'Chuyển đổi')}
                    </Typography>
                    <Typography variant="body2" className="font-medium">
                      {campaign.conversions.toLocaleString('vi-VN')}
                    </Typography>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <Chip
                  variant={campaign.ctr >= 5 ? 'success' : campaign.ctr >= 3 ? 'warning' : 'danger'}
                  size="sm"
                >
                  CTR: {campaign.ctr.toFixed(2)}%
                </Chip>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Additional Metrics */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3 }}>
        <Card className="p-4">
          <Typography variant="subtitle2" className="mb-2">
            {t('marketing:googleAds.reports.additionalMetrics.avgCTR', 'CTR trung bình')}
          </Typography>
          <Typography variant="h2" className="text-blue-600">
            {totalStats.avgCTR.toFixed(2)}%
          </Typography>
        </Card>

        <Card className="p-4">
          <Typography variant="subtitle2" className="mb-2">
            {t('marketing:googleAds.reports.additionalMetrics.avgCPC', 'CPC trung bình')}
          </Typography>
          <Typography variant="h2" className="text-orange-600">
            {totalStats.avgCPC.toLocaleString('vi-VN')} ₫
          </Typography>
        </Card>

        <Card className="p-4">
          <Typography variant="subtitle2" className="mb-2">
            {t('marketing:googleAds.reports.additionalMetrics.conversionRate', 'Tỷ lệ chuyển đổi')}
          </Typography>
          <Typography variant="h2" className="text-green-600">
            {totalStats.conversionRate.toFixed(2)}%
          </Typography>
        </Card>
      </ResponsiveGrid>
    </div>
  );
};

export default GoogleAdsReportsPage;
