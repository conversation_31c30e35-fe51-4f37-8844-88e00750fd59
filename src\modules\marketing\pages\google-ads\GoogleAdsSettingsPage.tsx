import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Form,
  FormItem,
  Input,
  Chip,
  ResponsiveGrid,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import {
  Settings,
  Key,
  Globe,
  Bell,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Save,
  TestTube,
} from 'lucide-react';

// Interface cho Settings form
interface GoogleAdsSettingsForm {
  clientId: string;
  clientSecret: string;
  developerToken: string;
  customerId: string;
  refreshToken: string;
  webhookUrl: string;
  defaultCurrency: string;
  defaultTimezone: string;
  autoSync: boolean;
  syncInterval: number;
  notificationEmail: string;
  enableNotifications: boolean;
}

/**
 * Trang cài đặt Google Ads
 */
const GoogleAdsSettingsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const { formRef, setFormErrors } = useFormErrors();

  // Mock connection status
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'testing'>('disconnected');
  const [isLoading, setIsLoading] = useState(false);

  // Default form data
  const defaultFormData: GoogleAdsSettingsForm = {
    clientId: '',
    clientSecret: '',
    developerToken: '',
    customerId: '',
    refreshToken: '',
    webhookUrl: '',
    defaultCurrency: 'VND',
    defaultTimezone: 'Asia/Ho_Chi_Minh',
    autoSync: true,
    syncInterval: 60,
    notificationEmail: '',
    enableNotifications: true,
  };

  const handleTestConnection = useCallback(async () => {
    setConnectionStatus('testing');
    setIsLoading(true);

    // Mock API call
    setTimeout(() => {
      setConnectionStatus('connected');
      setIsLoading(false);
    }, 2000);
  }, []);

  const handleSaveSettings = useCallback(async (data: Record<string, unknown>) => {
    setIsLoading(true);

    // Validation
    const errors: Record<string, string> = {};
    if (!data.clientId) errors.clientId = t('marketing:googleAds.settings.validation.clientIdRequired', 'Client ID là bắt buộc');
    if (!data.clientSecret) errors.clientSecret = t('marketing:googleAds.settings.validation.clientSecretRequired', 'Client Secret là bắt buộc');
    if (!data.developerToken) errors.developerToken = t('marketing:googleAds.settings.validation.developerTokenRequired', 'Developer Token là bắt buộc');

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setIsLoading(false);
      return;
    }

    // Mock save
    setTimeout(() => {
      setIsLoading(false);
      // TODO: Show success message
    }, 1000);
  }, [setFormErrors, t]);

  const handleDisconnect = useCallback(() => {
    setConnectionStatus('disconnected');
    // Reset form through form ref
    if (formRef.current) {
      formRef.current.setValues({
        clientId: '',
        clientSecret: '',
        developerToken: '',
        customerId: '',
        refreshToken: '',
      } as Record<string, unknown>);
    }
  }, [formRef]);

  const renderConnectionStatus = () => {
    switch (connectionStatus) {
      case 'connected':
        return (
          <Chip variant="success" size="sm">
            <CheckCircle className="h-3 w-3 mr-1" />
            {t('marketing:googleAds.settings.status.connected', 'Đã kết nối')}
          </Chip>
        );
      case 'testing':
        return (
          <Chip variant="warning" size="sm">
            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
            {t('marketing:googleAds.settings.status.testing', 'Đang kiểm tra')}
          </Chip>
        );
      default:
        return (
          <Chip variant="danger" size="sm">
            <XCircle className="h-3 w-3 mr-1" />
            {t('marketing:googleAds.settings.status.disconnected', 'Chưa kết nối')}
          </Chip>
        );
    }
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h1">
            {t('marketing:googleAds.settings.title', 'Cài đặt Google Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mt-1">
            {t('marketing:googleAds.settings.description', 'Cấu hình tích hợp và kết nối với Google Ads API')}
          </Typography>
        </div>
        <div className="flex items-center space-x-2">
          {renderConnectionStatus()}
        </div>
      </div>

      <Form
        ref={formRef}
        onSubmit={handleSaveSettings}
        defaultValues={defaultFormData}
      >
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2 }}>
          {/* API Configuration */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Key className="h-5 w-5 text-primary" />
              <Typography variant="h3">
                {t('marketing:googleAds.settings.apiConfig.title', 'Cấu hình API')}
              </Typography>
            </div>

            <div className="space-y-4">
              <FormItem
                label={t('marketing:googleAds.settings.apiConfig.clientId', 'Client ID')}
                name="clientId"
                required
              >
                <Input
                  type="text"
                  name="clientId"
                  placeholder={t('marketing:googleAds.settings.apiConfig.clientIdPlaceholder', 'Nhập Client ID từ Google Cloud Console')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                label={t('marketing:googleAds.settings.apiConfig.clientSecret', 'Client Secret')}
                name="clientSecret"
                required
              >
                <Input
                  type="password"
                  name="clientSecret"
                  placeholder={t('marketing:googleAds.settings.apiConfig.clientSecretPlaceholder', 'Nhập Client Secret')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                label={t('marketing:googleAds.settings.apiConfig.developerToken', 'Developer Token')}
                name="developerToken"
                required
              >
                <Input
                  type="password"
                  name="developerToken"
                  placeholder={t('marketing:googleAds.settings.apiConfig.developerTokenPlaceholder', 'Nhập Developer Token')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                label={t('marketing:googleAds.settings.apiConfig.customerId', 'Customer ID')}
                name="customerId"
              >
                <Input
                  type="text"
                  name="customerId"
                  placeholder={t('marketing:googleAds.settings.apiConfig.customerIdPlaceholder', 'Nhập Customer ID (tùy chọn)')}
                  fullWidth
                />
              </FormItem>

              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleTestConnection}
                  disabled={isLoading}
                >
                  <TestTube className="h-4 w-4 mr-2" />
                  {t('marketing:googleAds.settings.testConnection', 'Kiểm tra kết nối')}
                </Button>
                {connectionStatus === 'connected' && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleDisconnect}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    {t('marketing:googleAds.settings.disconnect', 'Ngắt kết nối')}
                  </Button>
                )}
              </div>
            </div>
          </Card>

          {/* General Settings */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Settings className="h-5 w-5 text-primary" />
              <Typography variant="h3">
                {t('marketing:googleAds.settings.general.title', 'Cài đặt chung')}
              </Typography>
            </div>

            <div className="space-y-4">
              <FormItem
                label={t('marketing:googleAds.settings.general.defaultCurrency', 'Tiền tệ mặc định')}
                name="defaultCurrency"
              >
                <select
                  name="defaultCurrency"
                  className="w-full bg-background border border-border rounded px-3 py-2"
                >
                  <option value="VND">VND - Việt Nam Đồng</option>
                  <option value="USD">USD - US Dollar</option>
                  <option value="EUR">EUR - Euro</option>
                </select>
              </FormItem>

              <FormItem
                label={t('marketing:googleAds.settings.general.defaultTimezone', 'Múi giờ mặc định')}
                name="defaultTimezone"
              >
                <select
                  name="defaultTimezone"
                  className="w-full bg-background border border-border rounded px-3 py-2"
                >
                  <option value="Asia/Ho_Chi_Minh">Asia/Ho_Chi_Minh</option>
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">America/New_York</option>
                </select>
              </FormItem>

              <FormItem
                label={t('marketing:googleAds.settings.general.syncInterval', 'Khoảng thời gian đồng bộ (phút)')}
                name="syncInterval"
              >
                <Input
                  type="number"
                  name="syncInterval"
                  min={15}
                  max={1440}
                  fullWidth
                />
              </FormItem>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="autoSync"
                  name="autoSync"
                  className="rounded border-border"
                />
                <label htmlFor="autoSync" className="text-sm">
                  {t('marketing:googleAds.settings.general.autoSync', 'Tự động đồng bộ dữ liệu')}
                </label>
              </div>
            </div>
          </Card>

          {/* Webhook Settings */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Globe className="h-5 w-5 text-primary" />
              <Typography variant="h3">
                {t('marketing:googleAds.settings.webhook.title', 'Webhook')}
              </Typography>
            </div>

            <div className="space-y-4">
              <FormItem
                label={t('marketing:googleAds.settings.webhook.url', 'Webhook URL')}
                name="webhookUrl"
              >
                <Input
                  type="url"
                  name="webhookUrl"
                  placeholder={t('marketing:googleAds.settings.webhook.urlPlaceholder', 'https://your-domain.com/webhook')}
                  fullWidth
                />
              </FormItem>

              <div className="p-3 bg-muted/20 rounded border border-border">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                  <div>
                    <Typography variant="caption" className="font-medium">
                      {t('marketing:googleAds.settings.webhook.note', 'Lưu ý')}
                    </Typography>
                    <Typography variant="caption" className="text-muted-foreground block">
                      {t('marketing:googleAds.settings.webhook.noteText', 'Webhook sẽ nhận thông báo về thay đổi trong tài khoản Google Ads')}
                    </Typography>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Notification Settings */}
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Bell className="h-5 w-5 text-primary" />
              <Typography variant="h3">
                {t('marketing:googleAds.settings.notifications.title', 'Thông báo')}
              </Typography>
            </div>

            <div className="space-y-4">
              <FormItem
                label={t('marketing:googleAds.settings.notifications.email', 'Email thông báo')}
                name="notificationEmail"
              >
                <Input
                  type="email"
                  name="notificationEmail"
                  placeholder={t('marketing:googleAds.settings.notifications.emailPlaceholder', '<EMAIL>')}
                  fullWidth
                />
              </FormItem>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="enableNotifications"
                  name="enableNotifications"
                  className="rounded border-border"
                />
                <label htmlFor="enableNotifications" className="text-sm">
                  {t('marketing:googleAds.settings.notifications.enable', 'Bật thông báo email')}
                </label>
              </div>
            </div>
          </Card>
        </ResponsiveGrid>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            <Save className="h-4 w-4 mr-2" />
            {isLoading
              ? t('common:saving', 'Đang lưu...')
              : t('common:save', 'Lưu cài đặt')
            }
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default GoogleAdsSettingsPage;
