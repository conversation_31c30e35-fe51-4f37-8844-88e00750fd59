import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import {
  Card,
  Table,
  Typography,
  Button,
  Chip,
  ResponsiveGrid,
} from '@/shared/components/common';
import type { ChipVariant } from '@/shared/components/common/Chip/Chip';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useSlideInForm } from '@/shared/hooks/form';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  Plus,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Play,
  Pause,
  ArrowLeft,
  DollarSign,
  Calendar,
  Globe,
} from 'lucide-react';

import { useTikTokAdsAccounts } from '../../hooks/tiktok-ads';
import {
  TikTokAdsAccountDto,
  TikTokAdsAccountStatus,
  TikTokAdsAccountQueryDto,
} from '../../types/tiktok-ads.types';

/**
 * Trang quản lý tài khoản TikTok Ads
 */
const TikTokAdsAccountsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();
  const { openForm } = useSlideInForm();

  // Hooks
  const {
    useAccounts,
    useDeleteAccount,
    useUpdateAccountStatus,
  } = useTikTokAdsAccounts();

  // Mutations
  const deleteAccountMutation = useDeleteAccount();
  const updateStatusMutation = useUpdateAccountStatus();

  // Event handlers
  const handleAddAccount = useCallback(() => {
    openForm();
  }, [openForm]);

  const handleViewAccount = useCallback((id: number) => {
    navigate(`/marketing/tiktok-ads/accounts/${id}`);
  }, [navigate]);

  const handleEditAccount = useCallback((id: number) => {
    navigate(`/marketing/tiktok-ads/accounts/${id}/edit`);
  }, [navigate]);

  const handleDeleteAccount = useCallback((id: number) => {
    // TODO: Implement proper confirm dialog
    if (window.confirm(t('marketing:tiktokAds.account.deleteConfirm'))) {
      deleteAccountMutation.mutate(id);
    }
  }, [t, deleteAccountMutation]);

  const handleToggleStatus = useCallback((id: number, status: TikTokAdsAccountStatus) => {
    updateStatusMutation.mutate({ id, status });
  }, [updateStatusMutation]);

  const handleRefresh = useCallback(() => {
    // TODO: Implement refresh functionality
    window.location.reload();
  }, []);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<TikTokAdsAccountDto>[]>(() => [
    {
      title: t('marketing:tiktokAds.account.id'),
      dataIndex: 'accountId',
      key: 'accountId',
      sortable: true,
      width: 150,
      render: (value: unknown) => (
        <Typography variant="body2" className="font-mono">
          {String(value)}
        </Typography>
      ),
    },
    {
      title: t('marketing:tiktokAds.account.name'),
      dataIndex: 'name',
      key: 'name',
      sortable: true,
      render: (value: unknown, record: TikTokAdsAccountDto) => (
        <div>
          <Typography variant="body2" className="font-medium">
            {String(value)}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.currency} • {record.timezone}
          </Typography>
        </div>
      ),
    },
    {
      title: t('marketing:tiktokAds.account.status'),
      dataIndex: 'status',
      key: 'status',
      sortable: true,
      width: 120,
      render: (value: unknown) => {
        const status = value as TikTokAdsAccountStatus;
        const getVariant = (status: TikTokAdsAccountStatus): ChipVariant => {
          switch (status) {
            case TikTokAdsAccountStatus.ACTIVE:
              return 'success';
            case TikTokAdsAccountStatus.INACTIVE:
              return 'warning';
            case TikTokAdsAccountStatus.SUSPENDED:
              return 'danger';
            case TikTokAdsAccountStatus.PENDING:
              return 'info';
            default:
              return 'default';
          }
        };

        return (
          <Chip variant={getVariant(status)} size="sm">
            {t(`marketing:tiktokAds.accountStatus.${status.toLowerCase()}`)}
          </Chip>
        );
      },
    },
    {
      title: t('marketing:tiktokAds.account.balance'),
      dataIndex: 'balance',
      key: 'balance',
      sortable: true,
      width: 120,
      align: 'right',
      render: (value: unknown, record: TikTokAdsAccountDto) => (
        <div className="text-right">
          <Typography variant="body2" className="font-medium">
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: record.currency,
            }).format(Number(value))}
          </Typography>
        </div>
      ),
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      sortable: true,
      width: 150,
      render: (value: unknown) => (
        <Typography variant="body2">
          {new Date(String(value)).toLocaleDateString('vi-VN')}
        </Typography>
      ),
    },
    {
      title: t('common:actions'),
      key: 'actions',
      width: 200,
      render: (_, record: TikTokAdsAccountDto) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewAccount(record.id)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditAccount(record.id)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          {record.status === TikTokAdsAccountStatus.ACTIVE ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(record.id, TikTokAdsAccountStatus.INACTIVE)}
              isLoading={updateStatusMutation.isPending}
            >
              <Pause className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(record.id, TikTokAdsAccountStatus.ACTIVE)}
              isLoading={updateStatusMutation.isPending}
            >
              <Play className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteAccount(record.id)}
            isLoading={deleteAccountMutation.isPending}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ], [t, deleteAccountMutation.isPending, updateStatusMutation.isPending, handleViewAccount, handleEditAccount, handleToggleStatus, handleDeleteAccount]);

  // Filter options
  const filterOptions = useMemo(() => [
    { id: 'all', label: t('common:all'), value: 'all' },
    { id: 'active', label: t('marketing:tiktokAds.accountStatus.active'), value: TikTokAdsAccountStatus.ACTIVE },
    { id: 'inactive', label: t('marketing:tiktokAds.accountStatus.inactive'), value: TikTokAdsAccountStatus.INACTIVE },
    { id: 'suspended', label: t('marketing:tiktokAds.accountStatus.suspended'), value: TikTokAdsAccountStatus.SUSPENDED },
    { id: 'pending', label: t('marketing:tiktokAds.accountStatus.pending'), value: TikTokAdsAccountStatus.PENDING },
  ], [t]);

  // Create query params function
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): TikTokAdsAccountQueryDto => {
    const queryParams: TikTokAdsAccountQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as TikTokAdsAccountStatus;
    }

    return queryParams;
  }, []);

  // Data table configuration
  const dataTable = useDataTable(
    useDataTableConfig<TikTokAdsAccountDto, TikTokAdsAccountQueryDto>({
      columns,
      filterOptions,
      createQueryParams,
      showDateFilter: false,
    })
  );

  // Fetch data
  const { data: accountsData, isLoading } = useAccounts(dataTable.queryParams);

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/marketing/tiktok-ads')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common:back')}
          </Button>
          <Typography variant="h1">
            {t('marketing:tiktokAds.accounts.title')}
          </Typography>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            isLoading={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {t('common:refresh')}
          </Button>
          <Button
            variant="primary"
            size="sm"
            onClick={handleAddAccount}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('marketing:tiktokAds.account.add')}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }}>
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Globe className="h-5 w-5 text-primary" />
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:tiktokAds.stats.totalAccounts')}
              </Typography>
              <Typography variant="h3">
                {accountsData?.meta?.totalItems || 0}
              </Typography>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-success/10 rounded-lg">
              <Play className="h-5 w-5 text-success" />
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:tiktokAds.stats.activeAccounts')}
              </Typography>
              <Typography variant="h3">
                {accountsData?.items?.filter((acc: TikTokAdsAccountDto) => acc.status === TikTokAdsAccountStatus.ACTIVE).length || 0}
              </Typography>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-info/10 rounded-lg">
              <DollarSign className="h-5 w-5 text-info" />
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:tiktokAds.stats.totalBalance')}
              </Typography>
              <Typography variant="h3">
                ${accountsData?.items?.reduce((sum: number, acc: TikTokAdsAccountDto) => sum + acc.balance, 0)?.toLocaleString() || '0'}
              </Typography>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-warning/10 rounded-lg">
              <Calendar className="h-5 w-5 text-warning" />
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:tiktokAds.stats.thisMonth')}
              </Typography>
              <Typography variant="h3">
                {accountsData?.items?.filter((acc: TikTokAdsAccountDto) => {
                  const created = new Date(acc.createdAt);
                  const now = new Date();
                  return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
                }).length || 0}
              </Typography>
            </div>
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Table */}
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={accountsData?.items || []}
          loading={isLoading}
          pagination={{
            current: dataTable.tableData.currentPage,
            pageSize: dataTable.tableData.pageSize,
            total: accountsData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
          }}
          sortable
          onSortChange={dataTable.tableData.handleSortChange}
        />
      </Card>
    </div>
  );
};

export default TikTokAdsAccountsPage;
