import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button } from '@/shared/components/common';
import { Target, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

/**
 * Trang quản lý đối tượng TikTok Ads
 */
const TikTokAdsAudiencesPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Back Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => navigate('/marketing/tiktok-ads')}
        className="mb-4"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        {t('common:back', 'Quay lại')}
      </Button>

      {/* Coming Soon */}
      <Card className="p-8 text-center">
        <div className="h-16 w-16 rounded-full bg-orange-100 flex items-center justify-center mx-auto mb-4">
          <Target className="h-8 w-8 text-orange-600" />
        </div>
        <Typography variant="h2" className="mb-2">
          {t('marketing:tiktokAds.audiences.title', 'Quản lý đối tượng TikTok Ads')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground mb-6">
          {t('marketing:tiktokAds.audiences.comingSoon', 'Tính năng quản lý đối tượng TikTok Ads đang được phát triển')}
        </Typography>
        
        <div className="max-w-md mx-auto space-y-4">
          <div className="text-left">
            <Typography variant="subtitle2" className="mb-2">Tính năng sẽ có:</Typography>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Tạo custom audience</li>
              <li>• Lookalike audience</li>
              <li>• Demographic targeting</li>
              <li>• Interest & behavior targeting</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TikTokAdsAudiencesPage;
