import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import {
  Card,
  Table,
  Typography,
  Button,
  Chip,
  ResponsiveGrid,
} from '@/shared/components/common';
import type { ChipVariant } from '@/shared/components/common/Chip/Chip';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useSlideInForm } from '@/shared/hooks/form';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  Plus,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Play,
  Pause,
  ArrowLeft,
  DollarSign,
  Target,
  BarChart3,
} from 'lucide-react';

import { useTikTokAdsCampaigns } from '../../hooks/tiktok-ads';
import {
  TikTokAdsCampaignDto,
  TikTokAdsCampaignStatus,
  TikTokAdsCampaignQueryDto,
} from '../../types/tiktok-ads.types';

/**
 * Trang quản lý chiến dịch TikTok Ads
 */
const TikTokAdsCampaignsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();
  const { openForm } = useSlideInForm();

  // Hooks
  const {
    useCampaigns,
    useDeleteCampaign,
    useUpdateCampaignStatus,
  } = useTikTokAdsCampaigns();

  // Mutations
  const deleteCampaignMutation = useDeleteCampaign();
  const updateStatusMutation = useUpdateCampaignStatus();

  // Event handlers
  const handleAddCampaign = useCallback(() => {
    openForm();
  }, [openForm]);

  const handleViewCampaign = useCallback((id: number) => {
    navigate(`/marketing/tiktok-ads/campaigns/${id}`);
  }, [navigate]);

  const handleEditCampaign = useCallback((id: number) => {
    navigate(`/marketing/tiktok-ads/campaigns/${id}/edit`);
  }, [navigate]);

  const handleDeleteCampaign = useCallback((id: number) => {
    // TODO: Implement proper confirm dialog
    if (window.confirm(t('marketing:tiktokAds.campaign.deleteConfirm'))) {
      deleteCampaignMutation.mutate(id);
    }
  }, [t, deleteCampaignMutation]);

  const handleToggleStatus = useCallback((id: number, status: TikTokAdsCampaignStatus) => {
    updateStatusMutation.mutate({ id, status });
  }, [updateStatusMutation]);

  const handleRefresh = useCallback(() => {
    // TODO: Implement refresh functionality
    window.location.reload();
  }, []);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<TikTokAdsCampaignDto>[]>(() => [
    {
      title: t('marketing:tiktokAds.campaign.id'),
      dataIndex: 'campaignId',
      key: 'campaignId',
      sortable: true,
      width: 150,
      render: (value: unknown) => (
        <Typography variant="body2" className="font-mono">
          {String(value)}
        </Typography>
      ),
    },
    {
      title: t('marketing:tiktokAds.campaign.name'),
      dataIndex: 'name',
      key: 'name',
      sortable: true,
      render: (value: unknown, record: TikTokAdsCampaignDto) => (
        <div>
          <Typography variant="body2" className="font-medium">
            {String(value)}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.objective}
          </Typography>
        </div>
      ),
    },
    {
      title: t('marketing:tiktokAds.campaign.status'),
      dataIndex: 'status',
      key: 'status',
      sortable: true,
      width: 120,
      render: (value: unknown) => {
        const status = value as TikTokAdsCampaignStatus;
        const getVariant = (status: TikTokAdsCampaignStatus): ChipVariant => {
          switch (status) {
            case TikTokAdsCampaignStatus.ENABLED:
              return 'success';
            case TikTokAdsCampaignStatus.PAUSED:
              return 'warning';
            case TikTokAdsCampaignStatus.DELETED:
              return 'danger';
            case TikTokAdsCampaignStatus.PENDING:
              return 'info';
            default:
              return 'default';
          }
        };

        return (
          <Chip variant={getVariant(status)} size="sm">
            {t(`marketing:tiktokAds.campaignStatus.${status.toLowerCase()}`)}
          </Chip>
        );
      },
    },
    {
      title: t('marketing:tiktokAds.campaign.budget'),
      dataIndex: 'budget',
      key: 'budget',
      sortable: true,
      width: 120,
      align: 'right',
      render: (value: unknown, record: TikTokAdsCampaignDto) => (
        <div className="text-right">
          <Typography variant="body2" className="font-medium">
            ${Number(value).toLocaleString()}
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.budgetMode === 'DAILY' ? t('marketing:tiktokAds.daily') : t('marketing:tiktokAds.total')}
          </Typography>
        </div>
      ),
    },
    {
      title: t('marketing:tiktokAds.campaign.performance'),
      key: 'performance',
      width: 150,
      render: (_, record: TikTokAdsCampaignDto) => (
        <div className="text-right">
          <Typography variant="body2" className="font-medium">
            {record.impressions?.toLocaleString() || 0} imp
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.clicks?.toLocaleString() || 0} clicks • {record.ctr?.toFixed(2) || 0}% CTR
          </Typography>
        </div>
      ),
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      sortable: true,
      width: 150,
      render: (value: unknown) => (
        <Typography variant="body2">
          {new Date(String(value)).toLocaleDateString('vi-VN')}
        </Typography>
      ),
    },
    {
      title: t('common:actions'),
      key: 'actions',
      width: 200,
      render: (_, record: TikTokAdsCampaignDto) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewCampaign(record.id)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditCampaign(record.id)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          {record.status === TikTokAdsCampaignStatus.ENABLED ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(record.id, TikTokAdsCampaignStatus.PAUSED)}
              isLoading={updateStatusMutation.isPending}
            >
              <Pause className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(record.id, TikTokAdsCampaignStatus.ENABLED)}
              isLoading={updateStatusMutation.isPending}
            >
              <Play className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteCampaign(record.id)}
            isLoading={deleteCampaignMutation.isPending}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ], [t, deleteCampaignMutation.isPending, updateStatusMutation.isPending, handleViewCampaign, handleEditCampaign, handleToggleStatus, handleDeleteCampaign]);

  // Filter options
  const filterOptions = useMemo(() => [
    { id: 'all', label: t('common:all'), value: 'all' },
    { id: 'enabled', label: t('marketing:tiktokAds.campaignStatus.enabled'), value: TikTokAdsCampaignStatus.ENABLED },
    { id: 'paused', label: t('marketing:tiktokAds.campaignStatus.paused'), value: TikTokAdsCampaignStatus.PAUSED },
    { id: 'deleted', label: t('marketing:tiktokAds.campaignStatus.deleted'), value: TikTokAdsCampaignStatus.DELETED },
    { id: 'pending', label: t('marketing:tiktokAds.campaignStatus.pending'), value: TikTokAdsCampaignStatus.PENDING },
  ], [t]);

  // Create query params function
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): TikTokAdsCampaignQueryDto => {
    const queryParams: TikTokAdsCampaignQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as TikTokAdsCampaignStatus;
    }

    return queryParams;
  }, []);

  // Data table configuration
  const dataTable = useDataTable(
    useDataTableConfig<TikTokAdsCampaignDto, TikTokAdsCampaignQueryDto>({
      columns,
      filterOptions,
      createQueryParams,
      showDateFilter: true,
    })
  );

  // Fetch data
  const { data: campaignsData, isLoading } = useCampaigns(dataTable.queryParams);

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/marketing/tiktok-ads')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common:back')}
          </Button>
          <Typography variant="h1">
            {t('marketing:tiktokAds.campaigns.title')}
          </Typography>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            isLoading={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {t('common:refresh')}
          </Button>
          <Button
            variant="primary"
            size="sm"
            onClick={handleAddCampaign}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('marketing:tiktokAds.campaign.add')}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }}>
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <BarChart3 className="h-5 w-5 text-primary" />
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:tiktokAds.stats.totalCampaigns')}
              </Typography>
              <Typography variant="h3">
                {campaignsData?.meta?.totalItems || 0}
              </Typography>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-success/10 rounded-lg">
              <Play className="h-5 w-5 text-success" />
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:tiktokAds.stats.activeCampaigns')}
              </Typography>
              <Typography variant="h3">
                {campaignsData?.items?.filter((camp: TikTokAdsCampaignDto) => camp.status === TikTokAdsCampaignStatus.ENABLED).length || 0}
              </Typography>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-info/10 rounded-lg">
              <DollarSign className="h-5 w-5 text-info" />
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:tiktokAds.stats.totalSpend')}
              </Typography>
              <Typography variant="h3">
                ${campaignsData?.items?.reduce((sum: number, camp: TikTokAdsCampaignDto) => sum + (camp.spend || 0), 0)?.toLocaleString() || '0'}
              </Typography>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-warning/10 rounded-lg">
              <Target className="h-5 w-5 text-warning" />
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:tiktokAds.stats.avgCTR')}
              </Typography>
              <Typography variant="h3">
                {campaignsData?.items?.length ?
                  (campaignsData.items.reduce((sum: number, camp: TikTokAdsCampaignDto) => sum + (camp.ctr || 0), 0) / campaignsData.items.length).toFixed(2) + '%'
                  : '0%'}
              </Typography>
            </div>
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Table */}
      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={campaignsData?.items || []}
          loading={isLoading}
          pagination={{
            current: dataTable.tableData.currentPage,
            pageSize: dataTable.tableData.pageSize,
            total: campaignsData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
          }}
          sortable
          onSortChange={dataTable.tableData.handleSortChange}
        />
      </Card>
    </div>
  );
};

export default TikTokAdsCampaignsPage;
