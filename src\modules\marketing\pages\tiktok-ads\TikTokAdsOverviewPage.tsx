import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
} from '@/shared/components/common';
import {
  TrendingUp,
  DollarSign,
  Eye,
  MousePointer,
  Users,
  BarChart3,
  Settings,
  Video,
  Target,
} from 'lucide-react';

/**
 * Trang tổng quan TikTok Ads
 */
const TikTokAdsOverviewPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();

  // Mock data cho demo
  const mockStats = {
    totalSpend: 85000,
    totalImpressions: 1850000,
    totalClicks: 12500,
    averageCTR: 0.68,
    totalConversions: 890,
    averageCPC: 6.80,
    activeAccounts: 2,
    activeCampaigns: 8,
  };



  const handleViewAccounts = () => {
    navigate('/marketing/tiktok-ads/accounts');
  };

  const handleViewCampaigns = () => {
    navigate('/marketing/tiktok-ads/campaigns');
  };

  const handleViewCreatives = () => {
    navigate('/marketing/tiktok-ads/creatives');
  };

  const handleViewReports = () => {
    navigate('/marketing/tiktok-ads/reports');
  };

  const handleSettings = () => {
    navigate('/marketing/tiktok-ads/settings');
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Coming Soon Banner */}
      <Card className="p-6 bg-gradient-to-r from-pink-50 to-purple-50 border-pink-200">
        <div className="flex items-center justify-between">
          <div>
            <Typography variant="h2" className="text-pink-600 mb-2">
              🚀 TikTok Ads - Coming Soon
            </Typography>
            <Typography variant="body1" className="text-gray-700">
              {t('marketing:tiktokAds.overview.comingSoon', 'Module TikTok Ads đang được phát triển. Các tính năng sẽ sớm có mặt!')}
            </Typography>
          </div>
          <div className="h-16 w-16 rounded-full bg-pink-100 flex items-center justify-center">
            <Video className="h-8 w-8 text-pink-600" />
          </div>
        </div>
      </Card>

      {/* Planned Features */}
      <Card className="p-6">
        <Typography variant="h3" className="mb-4">
          {t('marketing:tiktokAds.overview.plannedFeatures', 'Tính năng dự kiến')}
        </Typography>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div className="flex items-start space-x-3 p-4 rounded-lg bg-gray-50">
            <Users className="h-6 w-6 text-blue-600 mt-1" />
            <div>
              <Typography variant="subtitle2" className="mb-1">
                {t('marketing:tiktokAds.features.accountManagement', 'Quản lý tài khoản')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                Kết nối và quản lý tài khoản TikTok Ads
              </Typography>
            </div>
          </div>

          <div className="flex items-start space-x-3 p-4 rounded-lg bg-gray-50">
            <BarChart3 className="h-6 w-6 text-green-600 mt-1" />
            <div>
              <Typography variant="subtitle2" className="mb-1">
                {t('marketing:tiktokAds.features.campaignManagement', 'Quản lý chiến dịch')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                Tạo và quản lý chiến dịch quảng cáo
              </Typography>
            </div>
          </div>

          <div className="flex items-start space-x-3 p-4 rounded-lg bg-gray-50">
            <Video className="h-6 w-6 text-purple-600 mt-1" />
            <div>
              <Typography variant="subtitle2" className="mb-1">
                {t('marketing:tiktokAds.features.creativeManagement', 'Quản lý creative')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                Upload và quản lý video, hình ảnh
              </Typography>
            </div>
          </div>

          <div className="flex items-start space-x-3 p-4 rounded-lg bg-gray-50">
            <Target className="h-6 w-6 text-orange-600 mt-1" />
            <div>
              <Typography variant="subtitle2" className="mb-1">
                {t('marketing:tiktokAds.features.audienceTargeting', 'Targeting đối tượng')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                Tạo và quản lý đối tượng mục tiêu
              </Typography>
            </div>
          </div>

          <div className="flex items-start space-x-3 p-4 rounded-lg bg-gray-50">
            <TrendingUp className="h-6 w-6 text-red-600 mt-1" />
            <div>
              <Typography variant="subtitle2" className="mb-1">
                {t('marketing:tiktokAds.features.analytics', 'Báo cáo & Analytics')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                Phân tích hiệu suất chiến dịch
              </Typography>
            </div>
          </div>

          <div className="flex items-start space-x-3 p-4 rounded-lg bg-gray-50">
            <Settings className="h-6 w-6 text-gray-600 mt-1" />
            <div>
              <Typography variant="subtitle2" className="mb-1">
                {t('marketing:tiktokAds.features.integration', 'Tích hợp API')}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                Kết nối với TikTok Ads API
              </Typography>
            </div>
          </div>
        </div>
      </Card>

      {/* Mock Stats Preview */}
      <Card className="p-6">
        <Typography variant="h3" className="mb-4">
          {t('marketing:tiktokAds.overview.mockPreview', 'Preview giao diện (Demo)')}
        </Typography>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 opacity-60">
          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Typography variant="caption" className="font-medium text-muted-foreground">
                Tổng chi phí
              </Typography>
              <DollarSign className="h-4 w-4 text-green-600" />
            </div>
            <Typography variant="h2" className="text-green-600">
              {mockStats.totalSpend.toLocaleString('vi-VN')} ₫
            </Typography>
            <Typography variant="caption" className="text-muted-foreground mt-1">
              Tháng này
            </Typography>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Typography variant="caption" className="font-medium text-muted-foreground">
                Lượt hiển thị
              </Typography>
              <Eye className="h-4 w-4 text-blue-600" />
            </div>
            <Typography variant="h2" className="text-blue-600">
              {mockStats.totalImpressions.toLocaleString('vi-VN')}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground mt-1">
              Tháng này
            </Typography>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Typography variant="caption" className="font-medium text-muted-foreground">
                Lượt click
              </Typography>
              <MousePointer className="h-4 w-4 text-orange-600" />
            </div>
            <Typography variant="h2" className="text-orange-600">
              {mockStats.totalClicks.toLocaleString('vi-VN')}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground mt-1">
              CTR: {mockStats.averageCTR}%
            </Typography>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Typography variant="caption" className="font-medium text-muted-foreground">
                Chuyển đổi
              </Typography>
              <TrendingUp className="h-4 w-4 text-purple-600" />
            </div>
            <Typography variant="h2" className="text-purple-600">
              {mockStats.totalConversions.toLocaleString('vi-VN')}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground mt-1">
              CPC: {mockStats.averageCPC.toLocaleString('vi-VN')} ₫
            </Typography>
          </Card>
        </div>
      </Card>

      {/* Navigation Links */}
      <Card className="p-6">
        <Typography variant="h3" className="mb-4">
          {t('marketing:tiktokAds.overview.navigation', 'Điều hướng (Sẵn sàng)')}
        </Typography>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            onClick={handleViewAccounts}
          >
            <Users className="h-5 w-5 mr-3" />
            <div className="text-left">
              <div className="font-medium">Tài khoản</div>
              <div className="text-sm text-muted-foreground">Quản lý tài khoản TikTok Ads</div>
            </div>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            onClick={handleViewCampaigns}
          >
            <BarChart3 className="h-5 w-5 mr-3" />
            <div className="text-left">
              <div className="font-medium">Chiến dịch</div>
              <div className="text-sm text-muted-foreground">Quản lý chiến dịch quảng cáo</div>
            </div>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            onClick={handleViewCreatives}
          >
            <Video className="h-5 w-5 mr-3" />
            <div className="text-left">
              <div className="font-medium">Creative</div>
              <div className="text-sm text-muted-foreground">Quản lý nội dung quảng cáo</div>
            </div>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            onClick={handleViewReports}
          >
            <TrendingUp className="h-5 w-5 mr-3" />
            <div className="text-left">
              <div className="font-medium">Báo cáo</div>
              <div className="text-sm text-muted-foreground">Phân tích hiệu suất</div>
            </div>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            onClick={handleSettings}
          >
            <Settings className="h-5 w-5 mr-3" />
            <div className="text-left">
              <div className="font-medium">Cài đặt</div>
              <div className="text-sm text-muted-foreground">Cấu hình tích hợp</div>
            </div>
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default TikTokAdsOverviewPage;
