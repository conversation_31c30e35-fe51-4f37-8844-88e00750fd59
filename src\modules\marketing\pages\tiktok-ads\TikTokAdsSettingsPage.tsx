import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button } from '@/shared/components/common';
import { Settings, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

/**
 * Trang cài đặt TikTok Ads
 */
const TikTokAdsSettingsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Back Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => navigate('/marketing/tiktok-ads')}
        className="mb-4"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        {t('common:back', 'Quay lại')}
      </Button>

      {/* Coming Soon */}
      <Card className="p-8 text-center">
        <div className="h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4">
          <Settings className="h-8 w-8 text-gray-600" />
        </div>
        <Typography variant="h2" className="mb-2">
          {t('marketing:tiktokAds.settings.title', 'Cài đặt TikTok Ads')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground mb-6">
          {t('marketing:tiktokAds.settings.comingSoon', 'Tính năng cài đặt TikTok Ads đang được phát triển')}
        </Typography>
        
        <div className="max-w-md mx-auto space-y-4">
          <div className="text-left">
            <Typography variant="subtitle2" className="mb-2">Tính năng sẽ có:</Typography>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Cấu hình API keys</li>
              <li>• Webhook settings</li>
              <li>• Default campaign settings</li>
              <li>• Notification preferences</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TikTokAdsSettingsPage;
