/**
 * Email Template Adapter Service
 * Bridges the gap between backend template-email API and frontend email template expectations
 */

import { TemplateEmailBusinessService } from './template-email-business.service';
import { TemplateEmail, TemplateEmailQueryParams, TemplateEmailOverviewResponseDto, UpdateTemplateEmailRequest } from '../types/template-email.types';
import { EmailTemplateDto, EmailTemplateQueryDto, EmailTemplateStatus, EmailTemplateType } from '../types/email.types';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Adapter service to convert between backend template-email and frontend email-template formats
 */
export class EmailTemplateAdapterService {
  /**
   * Convert backend TemplateEmail to frontend EmailTemplateDto
   */
  static convertToEmailTemplateDto(backendTemplate: TemplateEmail): EmailTemplateDto {
    // Handle placeholders - backend can send either array or object
    let placeholderNames: string[] = [];
    if (Array.isArray(backendTemplate.placeholders)) {
      placeholderNames = backendTemplate.placeholders;
    } else if (backendTemplate.placeholders && typeof backendTemplate.placeholders === 'object') {
      placeholderNames = Object.keys(backendTemplate.placeholders);
    }

    // Map status from backend to frontend
    let status = EmailTemplateStatus.ACTIVE;
    if (backendTemplate.status) {
      switch (backendTemplate.status.toUpperCase()) {
        case 'DRAFT':
          status = EmailTemplateStatus.DRAFT;
          break;
        case 'ARCHIVED':
          status = EmailTemplateStatus.ARCHIVED;
          break;
        default:
          status = EmailTemplateStatus.ACTIVE;
      }
    }

    return {
      id: backendTemplate.id.toString(),
      name: backendTemplate.name,
      subject: backendTemplate.subject,
      htmlContent: backendTemplate.content,
      textContent: undefined,
      type: EmailTemplateType.NEWSLETTER, // Default type since backend doesn't have this
      status,
      previewText: undefined,
      thumbnailUrl: undefined,
      tags: backendTemplate.tags || [],
      variables: placeholderNames.map(placeholder => ({
        name: placeholder,
        type: 'TEXT' as const,
        defaultValue: '',
        required: false,
        description: `Variable: ${placeholder}`,
      })),
      createdAt: new Date(Number(backendTemplate.createdAt)),
      updatedAt: new Date(Number(backendTemplate.updatedAt)),
    };
  }

  /**
   * Convert frontend EmailTemplateQueryDto to backend TemplateEmailQueryParams
   */
  static convertToBackendQueryParams(frontendQuery: EmailTemplateQueryDto): TemplateEmailQueryParams {
    return {
      name: frontendQuery.search, // Backend uses 'name' for search
      page: frontendQuery.page,
      limit: frontendQuery.limit,
      sortBy: frontendQuery.sortBy,
      sortDirection: frontendQuery.sortDirection,
      // Note: Backend doesn't support type/status filtering, so we ignore these
    };
  }

  /**
   * Get email templates using backend API and convert to frontend format
   */
  static async getEmailTemplates(query?: EmailTemplateQueryDto): Promise<PaginatedResult<EmailTemplateDto>> {
    const backendQuery = query ? this.convertToBackendQueryParams(query) : {};
    console.log('🔍 [EmailTemplateAdapterService] Backend query:', backendQuery);

    const backendResult = await TemplateEmailBusinessService.getTemplateEmails(backendQuery);
    console.log('📦 [EmailTemplateAdapterService] Backend result:', backendResult);

    const convertedItems = backendResult.items.map(item => {
      console.log('🔄 [EmailTemplateAdapterService] Converting item:', item);
      const converted = this.convertToEmailTemplateDto(item);
      console.log('✅ [EmailTemplateAdapterService] Converted item:', converted);
      return converted;
    });

    const result = {
      items: convertedItems,
      meta: backendResult.meta,
    };

    console.log('🎯 [EmailTemplateAdapterService] Final result:', result);
    return result;
  }

  /**
   * Get email template by ID using backend API and convert to frontend format
   */
  static async getEmailTemplate(id: string): Promise<EmailTemplateDto> {
    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    const backendTemplate = await TemplateEmailBusinessService.getTemplateEmailById(numericId);
    return this.convertToEmailTemplateDto(backendTemplate);
  }

  /**
   * Create email template using backend API
   * Maps frontend data structure to backend CreateTemplateEmailDto
   */
  static async createEmailTemplate(data: {
    name: string;
    subject: string;
    htmlContent: string;
    textContent?: string;
    type?: string;
    previewText?: string;
    tags?: string[];
    variables?: Array<{ name: string; type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE'; required?: boolean; defaultValue?: string; description?: string }>;
  }): Promise<EmailTemplateDto> {
    // Map frontend data to backend CreateTemplateEmailRequest
    const backendData = {
      name: data.name,
      subject: data.subject,
      content: data.htmlContent, // htmlContent -> content
      textContent: data.textContent,
      type: data.type,
      previewText: data.previewText,
      tags: data.tags || [],
      variables: data.variables?.map(v => ({
        name: v.name,
        type: v.type,
        required: v.required || false,
        defaultValue: v.defaultValue,
        description: v.description,
      })) || [],
    };

    console.log('🔄 [EmailTemplateAdapterService] Mapping frontend to backend:', { frontend: data, backend: backendData });

    const backendTemplate = await TemplateEmailBusinessService.createTemplateEmail(backendData);
    return this.convertToEmailTemplateDto(backendTemplate);
  }

  /**
   * Update email template using backend API
   */
  static async updateEmailTemplate(
    id: string,
    data: {
      name?: string;
      subject?: string;
      htmlContent?: string;
      textContent?: string;
      type?: string;
      status?: string;
      previewText?: string;
      tags?: string[];
      variables?: Array<{ name: string; type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE'; required?: boolean; defaultValue?: string; description?: string }>;
    }
  ): Promise<EmailTemplateDto> {
    console.log('🔄 [EmailTemplateAdapterService] updateEmailTemplate called with:', { id, data });

    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    // Backend now accepts same structure as create API - no mapping needed
    const backendData: UpdateTemplateEmailRequest = {
      name: data.name,
      subject: data.subject,
      htmlContent: data.htmlContent,
      textContent: data.textContent,
      type: data.type,
      previewText: data.previewText,
      tags: data.tags,
      variables: data.variables,
    };

    console.log('🔄 [EmailTemplateAdapterService] Calling business service with:', { numericId, backendData });

    const backendTemplate = await TemplateEmailBusinessService.updateTemplateEmail(numericId, backendData);

    console.log('✅ [EmailTemplateAdapterService] Business service returned:', backendTemplate);

    const result = this.convertToEmailTemplateDto(backendTemplate);

    console.log('✅ [EmailTemplateAdapterService] Final result:', result);

    return result;
  }

  /**
   * Delete email template using backend API
   */
  static async deleteEmailTemplate(id: string): Promise<boolean> {
    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    return TemplateEmailBusinessService.deleteTemplateEmail(numericId);
  }

  /**
   * Bulk delete email templates using backend API
   */
  static async bulkDeleteEmailTemplates(ids: string[]): Promise<boolean> {
    const numericIds = ids.map(id => {
      const numericId = parseInt(id, 10);
      if (isNaN(numericId)) {
        throw new Error(`Invalid template ID: ${id}`);
      }
      return numericId;
    });

    return TemplateEmailBusinessService.bulkDeleteTemplateEmails(numericIds);
  }

  /**
   * Get email template statistics
   */
  static async getEmailTemplateStatistics(): Promise<{
    totalTemplates: number;
    activeTemplates: number;
    draftTemplates: number;
    recentTemplates: EmailTemplateDto[];
  }> {
    const backendStats = await TemplateEmailBusinessService.getTemplateStatistics();

    return {
      totalTemplates: backendStats.totalTemplates,
      activeTemplates: backendStats.activeTemplates,
      draftTemplates: backendStats.draftTemplates,
      recentTemplates: backendStats.recentTemplates.map(template =>
        this.convertToEmailTemplateDto(template)
      ),
    };
  }

  /**
   * Preview template with variables
   */
  static previewTemplate(content: string, variables: Record<string, string>): string {
    return TemplateEmailBusinessService.previewTemplate(content, variables);
  }

  /**
   * Validate template placeholders
   */
  static validateTemplatePlaceholders(content: string, placeholders: string[]): {
    isValid: boolean;
    missingPlaceholders: string[];
    unusedPlaceholders: string[];
  } {
    return TemplateEmailBusinessService.validateTemplatePlaceholders(content, placeholders);
  }

  /**
   * Get template email overview statistics
   */
  static async getOverview(): Promise<TemplateEmailOverviewResponseDto> {
    return TemplateEmailBusinessService.getOverview();
  }
}
