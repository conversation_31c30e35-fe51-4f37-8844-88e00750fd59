import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Facebook OAuth Authentication Service
 * Xử lý authentication flow với Facebook Marketing API
 */

export interface FacebookAuthUrlResponse {
  authUrl: string;
  state: string;
}

export interface FacebookAuthCallbackRequest {
  code: string;
  state: string;
  redirectUri: string;
}

export interface FacebookAuthTokenResponse {
  accessToken: string;
  tokenType: string;
  expiresIn: number;
  scope: string[];
  userId: string;
}

export interface FacebookUserInfo {
  id: string;
  name: string;
  email?: string;
  picture?: {
    data: {
      url: string;
    };
  };
}

export interface FacebookAdAccount {
  id: string;
  accountId: string;
  name: string;
  accountStatus: number;
  currency: string;
  timezone: string;
  businessName?: string;
}

/**
 * Tạo URL xác thực Facebook OAuth
 */
export const createFacebookAuthUrl = async (
  redirectUri: string,
  scopes: string[] = [
    'ads_management',
    'ads_read',
    'business_management',
    'pages_read_engagement',
    'pages_manage_ads'
  ]
): Promise<ApiResponseDto<FacebookAuthUrlResponse>> => {
  return apiClient.post('/marketing/facebook-ads/auth/url', {
    redirectUri,
    scopes,
  });
};

/**
 * Xử lý callback từ Facebook OAuth
 */
export const handleFacebookAuthCallback = async (
  data: FacebookAuthCallbackRequest
): Promise<ApiResponseDto<FacebookAuthTokenResponse>> => {
  return apiClient.post('/marketing/facebook-ads/auth/callback', data);
};

/**
 * Lấy thông tin user từ Facebook
 */
export const getFacebookUserInfo = async (
  accessToken: string
): Promise<ApiResponseDto<FacebookUserInfo>> => {
  return apiClient.get('/marketing/facebook-ads/auth/user', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
};

/**
 * Lấy danh sách ad accounts của user
 */
export const getFacebookUserAdAccounts = async (
  accessToken: string
): Promise<ApiResponseDto<FacebookAdAccount[]>> => {
  return apiClient.get('/marketing/facebook-ads/auth/ad-accounts', {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
};

/**
 * Refresh Facebook access token
 */
export const refreshFacebookToken = async (
  refreshToken: string
): Promise<ApiResponseDto<FacebookAuthTokenResponse>> => {
  return apiClient.post('/marketing/facebook-ads/auth/refresh', {
    refreshToken,
  });
};

/**
 * Revoke Facebook access token
 */
export const revokeFacebookToken = async (
  accessToken: string
): Promise<ApiResponseDto<void>> => {
  return apiClient.post('/marketing/facebook-ads/auth/revoke', {
    accessToken,
  });
};

/**
 * Validate Facebook access token
 */
export const validateFacebookToken = async (
  accessToken: string
): Promise<ApiResponseDto<{
  isValid: boolean;
  expiresAt: number;
  scopes: string[];
  userId: string;
}>> => {
  return apiClient.post('/marketing/facebook-ads/auth/validate', {
    accessToken,
  });
};

/**
 * Facebook Auth Utils
 */
export class FacebookAuthUtils {
  private static readonly STORAGE_KEY = 'facebook_auth_state';
  private static readonly TOKEN_KEY = 'facebook_access_token';
  private static readonly USER_KEY = 'facebook_user_info';

  /**
   * Generate random state for OAuth security
   */
  static generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Store OAuth state in localStorage
   */
  static storeState(state: string): void {
    localStorage.setItem(this.STORAGE_KEY, state);
  }

  /**
   * Retrieve and validate OAuth state
   */
  static validateState(state: string): boolean {
    const storedState = localStorage.getItem(this.STORAGE_KEY);
    localStorage.removeItem(this.STORAGE_KEY);
    return storedState === state;
  }

  /**
   * Store access token securely
   */
  static storeToken(token: FacebookAuthTokenResponse): void {
    const tokenData = {
      ...token,
      storedAt: Date.now(),
    };
    localStorage.setItem(this.TOKEN_KEY, JSON.stringify(tokenData));
  }

  /**
   * Get stored access token
   */
  static getToken(): FacebookAuthTokenResponse | null {
    try {
      const tokenData = localStorage.getItem(this.TOKEN_KEY);
      if (!tokenData) return null;

      const parsed = JSON.parse(tokenData);
      const now = Date.now();
      const expiresAt = parsed.storedAt + (parsed.expiresIn * 1000);

      // Check if token is expired
      if (now >= expiresAt) {
        this.clearToken();
        return null;
      }

      return parsed;
    } catch {
      return null;
    }
  }

  /**
   * Clear stored token
   */
  static clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  /**
   * Store user info
   */
  static storeUserInfo(userInfo: FacebookUserInfo): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(userInfo));
  }

  /**
   * Get stored user info
   */
  static getUserInfo(): FacebookUserInfo | null {
    try {
      const userInfo = localStorage.getItem(this.USER_KEY);
      return userInfo ? JSON.parse(userInfo) : null;
    } catch {
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    return this.getToken() !== null;
  }

  /**
   * Get current access token string
   */
  static getAccessToken(): string | null {
    const token = this.getToken();
    return token?.accessToken || null;
  }

  /**
   * Build Facebook OAuth URL
   */
  static buildAuthUrl(
    clientId: string,
    redirectUri: string,
    state: string,
    scopes: string[] = ['ads_management', 'ads_read']
  ): string {
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      state,
      scope: scopes.join(','),
      response_type: 'code',
    });

    return `https://www.facebook.com/v18.0/dialog/oauth?${params.toString()}`;
  }

  /**
   * Parse callback URL parameters
   */
  static parseCallbackUrl(url: string): { code?: string; state?: string; error?: string } {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;

    return {
      code: params.get('code') || undefined,
      state: params.get('state') || undefined,
      error: params.get('error') || undefined,
    };
  }
}
