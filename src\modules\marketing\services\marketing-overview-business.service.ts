/**
 * Business service for marketing overview - Layer 2: Business logic
 */

import { MarketingOverviewService } from './marketing-overview.service';
import {
  MarketingOverviewResponseDto,
  RecentTemplatesResponseDto,
} from '../types/statistics.types';

/**
 * Marketing overview business service
 */
export const MarketingOverviewBusinessService = {
  /**
   * L<PERSON>y thông tin overview marketing với business logic
   */
  getMarketingOverview: async (): Promise<MarketingOverviewResponseDto> => {
    try {
      const response = await MarketingOverviewService.getOverview();
      
      // Business logic: Validate data
      const data = response.result;
      
      // Ensure all values are non-negative
      const validatedData: MarketingOverviewResponseDto = {
        totalTemplates: Math.max(0, data.totalTemplates || 0),
        openRate: Math.max(0, Math.min(100, data.openRate || 0)), // Clamp between 0-100
        clickRate: Math.max(0, Math.min(100, data.clickRate || 0)), // Clamp between 0-100
        totalEmailsSent: Math.max(0, data.totalEmailsSent || 0),
      };

      return validatedData;
    } catch (error) {
      console.error('Error fetching marketing overview:', error);
      throw error;
    }
  },

  /**
   * Lấy danh sách templates gần đây với business logic
   */
  getRecentTemplates: async (): Promise<RecentTemplatesResponseDto> => {
    try {
      const response = await MarketingOverviewService.getRecentTemplates();
      
      // Business logic: Sort by updatedAt descending and limit to 5
      const data = response.result;
      const sortedTemplates = [...data.templates]
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, 5);

      return {
        templates: sortedTemplates,
      };
    } catch (error) {
      console.error('Error fetching recent templates:', error);
      throw error;
    }
  },
};
