/**
 * Service for marketing overview API
 */

import { apiClient } from '@/shared/api';
import {
  MarketingOverviewResponseDto,
  MarketingOverviewResponse,
  RecentTemplatesResponseDto,
  RecentTemplatesResponse,
} from '../types/statistics.types';

/**
 * Base URL for marketing overview API
 */
const BASE_URL = '/marketing/overview';

/**
 * Marketing overview service - Layer 1: Raw API calls
 */
export const MarketingOverviewService = {
  /**
   * Lấy thông tin overview marketing
   */
  getOverview: async (): Promise<MarketingOverviewResponse> => {
    return apiClient.get<MarketingOverviewResponseDto>(BASE_URL);
  },

  /**
   * <PERSON><PERSON><PERSON> danh sách templates gần đây
   */
  getRecentTemplates: async (): Promise<RecentTemplatesResponse> => {
    return apiClient.get<RecentTemplatesResponseDto>(`${BASE_URL}/recent-templates`);
  },
};
