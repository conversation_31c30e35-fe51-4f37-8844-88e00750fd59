/**
 * TikTok Ads Service
 * Business logic layer cho TikTok Ads API
 */

import { apiClient } from '@/shared/api';
import { TIKTOK_ADS_ENDPOINTS, TIKTOK_ADS_DEFAULTS } from '../constants/tiktok-ads.constants';
import type {
  TikTokAdsAccountDto,
  TikTokAdsCampaignDto,
  TikTokAdsCreativeDto,
  TikTokAdsAudienceDto,
  TikTokAdsAccountQueryDto,
  TikTokAdsCampaignQueryDto,
  TikTokAdsCreativeQueryDto,
  TikTokAdsAudienceQueryDto,
  CreateTikTokAdsAccountDto,
  UpdateTikTokAdsAccountDto,
  CreateTikTokAdsCampaignDto,
  UpdateTikTokAdsCampaignDto,
  CreateTikTokAdsCreativeDto,
  UpdateTikTokAdsCreativeDto,
  CreateTikTokAdsAudienceDto,
  UpdateTikTokAdsAudienceDto,
  TikTokAdsAccountResponse,
  TikTokAdsCampaignResponse,
  TikTokAdsCreativeResponse,
  TikTokAdsAudienceResponse,
} from '../types/tiktok-ads.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * TikTok Ads Account Service
 */
export const TikTokAdsAccountService = {
  /**
   * Lấy danh sách tài khoản TikTok Ads với business logic
   */
  async getAccounts(params?: TikTokAdsAccountQueryDto): Promise<ApiResponseDto<TikTokAdsAccountResponse>> {
    const defaultParams: TikTokAdsAccountQueryDto = {
      page: TIKTOK_ADS_DEFAULTS.DEFAULT_PAGE,
      limit: TIKTOK_ADS_DEFAULTS.PAGE_SIZE,
      ...params,
    };

    // Validate params
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    return apiClient.get<TikTokAdsAccountResponse>(TIKTOK_ADS_ENDPOINTS.ACCOUNTS, { params: defaultParams });
  },

  /**
   * Lấy chi tiết tài khoản TikTok Ads
   */
  async getAccount(id: number): Promise<ApiResponseDto<TikTokAdsAccountDto>> {
    if (!id || id <= 0) {
      throw new Error('Invalid account ID');
    }

    return apiClient.get<TikTokAdsAccountDto>(TIKTOK_ADS_ENDPOINTS.ACCOUNT_DETAIL(id));
  },

  /**
   * Tạo tài khoản TikTok Ads mới
   */
  async createAccount(data: CreateTikTokAdsAccountDto): Promise<ApiResponseDto<TikTokAdsAccountDto>> {
    // Validate required fields
    if (!data.name?.trim()) {
      throw new Error('Account name is required');
    }
    if (!data.currency?.trim()) {
      throw new Error('Currency is required');
    }
    if (!data.timezone?.trim()) {
      throw new Error('Timezone is required');
    }

    return apiClient.post<TikTokAdsAccountDto>(TIKTOK_ADS_ENDPOINTS.ACCOUNTS, data);
  },

  /**
   * Cập nhật tài khoản TikTok Ads
   */
  async updateAccount(id: number, data: UpdateTikTokAdsAccountDto): Promise<ApiResponseDto<TikTokAdsAccountDto>> {
    if (!id || id <= 0) {
      throw new Error('Invalid account ID');
    }

    return apiClient.put<TikTokAdsAccountDto>(TIKTOK_ADS_ENDPOINTS.ACCOUNT_DETAIL(id), data);
  },

  /**
   * Xóa tài khoản TikTok Ads
   */
  async deleteAccount(id: number): Promise<ApiResponseDto<void>> {
    if (!id || id <= 0) {
      throw new Error('Invalid account ID');
    }

    return apiClient.delete<void>(TIKTOK_ADS_ENDPOINTS.ACCOUNT_DETAIL(id));
  },
};

/**
 * TikTok Ads Campaign Service
 */
export const TikTokAdsCampaignService = {
  /**
   * Lấy danh sách chiến dịch TikTok Ads với business logic
   */
  async getCampaigns(params?: TikTokAdsCampaignQueryDto): Promise<ApiResponseDto<TikTokAdsCampaignResponse>> {
    const defaultParams: TikTokAdsCampaignQueryDto = {
      page: TIKTOK_ADS_DEFAULTS.DEFAULT_PAGE,
      limit: TIKTOK_ADS_DEFAULTS.PAGE_SIZE,
      ...params,
    };

    // Validate params
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    return apiClient.get<TikTokAdsCampaignResponse>(TIKTOK_ADS_ENDPOINTS.CAMPAIGNS, { params: defaultParams });
  },

  /**
   * Lấy chi tiết chiến dịch TikTok Ads
   */
  async getCampaign(id: number): Promise<ApiResponseDto<TikTokAdsCampaignDto>> {
    if (!id || id <= 0) {
      throw new Error('Invalid campaign ID');
    }

    return apiClient.get<TikTokAdsCampaignDto>(TIKTOK_ADS_ENDPOINTS.CAMPAIGN_DETAIL(id));
  },

  /**
   * Tạo chiến dịch TikTok Ads mới
   */
  async createCampaign(data: CreateTikTokAdsCampaignDto): Promise<ApiResponseDto<TikTokAdsCampaignDto>> {
    // Validate required fields
    if (!data.name?.trim()) {
      throw new Error('Campaign name is required');
    }
    if (!data.accountId?.trim()) {
      throw new Error('Account ID is required');
    }
    if (!data.objective) {
      throw new Error('Campaign objective is required');
    }
    if (!data.budget || data.budget < TIKTOK_ADS_DEFAULTS.MIN_DAILY_BUDGET) {
      throw new Error(`Budget must be at least $${TIKTOK_ADS_DEFAULTS.MIN_DAILY_BUDGET}`);
    }

    return apiClient.post<TikTokAdsCampaignDto>(TIKTOK_ADS_ENDPOINTS.CAMPAIGNS, data);
  },

  /**
   * Cập nhật chiến dịch TikTok Ads
   */
  async updateCampaign(id: number, data: UpdateTikTokAdsCampaignDto): Promise<ApiResponseDto<TikTokAdsCampaignDto>> {
    if (!id || id <= 0) {
      throw new Error('Invalid campaign ID');
    }

    // Validate budget if provided
    if (data.budget && data.budget < TIKTOK_ADS_DEFAULTS.MIN_DAILY_BUDGET) {
      throw new Error(`Budget must be at least $${TIKTOK_ADS_DEFAULTS.MIN_DAILY_BUDGET}`);
    }

    return apiClient.put(TIKTOK_ADS_ENDPOINTS.CAMPAIGN_DETAIL(id), data);
  },

  /**
   * Xóa chiến dịch TikTok Ads
   */
  async deleteCampaign(id: number): Promise<ApiResponseDto<void>> {
    if (!id || id <= 0) {
      throw new Error('Invalid campaign ID');
    }

    return apiClient.delete<void>(TIKTOK_ADS_ENDPOINTS.CAMPAIGN_DETAIL(id));
  },
};

/**
 * TikTok Ads Creative Service
 */
export const TikTokAdsCreativeService = {
  /**
   * Lấy danh sách creative TikTok Ads với business logic
   */
  async getCreatives(params?: TikTokAdsCreativeQueryDto): Promise<ApiResponseDto<TikTokAdsCreativeResponse>> {
    const defaultParams: TikTokAdsCreativeQueryDto = {
      page: TIKTOK_ADS_DEFAULTS.DEFAULT_PAGE,
      limit: TIKTOK_ADS_DEFAULTS.PAGE_SIZE,
      ...params,
    };

    // Validate params
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    return apiClient.get<TikTokAdsCreativeResponse>(TIKTOK_ADS_ENDPOINTS.CREATIVES, { params: defaultParams });
  },

  /**
   * Lấy chi tiết creative TikTok Ads
   */
  async getCreative(id: number): Promise<ApiResponseDto<TikTokAdsCreativeDto>> {
    if (!id || id <= 0) {
      throw new Error('Invalid creative ID');
    }

    return apiClient.get<TikTokAdsCreativeDto>(TIKTOK_ADS_ENDPOINTS.CREATIVE_DETAIL(id));
  },

  /**
   * Tạo creative TikTok Ads mới
   */
  async createCreative(data: CreateTikTokAdsCreativeDto): Promise<ApiResponseDto<TikTokAdsCreativeDto>> {
    // Validate required fields
    if (!data.name?.trim()) {
      throw new Error('Creative name is required');
    }
    if (!data.adGroupId?.trim()) {
      throw new Error('Ad Group ID is required');
    }
    if (!data.title?.trim()) {
      throw new Error('Creative title is required');
    }
    if (!data.description?.trim()) {
      throw new Error('Creative description is required');
    }
    if (!data.landingPageUrl?.trim()) {
      throw new Error('Landing page URL is required');
    }

    // Validate title and description length
    if (data.title.length > TIKTOK_ADS_DEFAULTS.MAX_TITLE_LENGTH) {
      throw new Error(`Title cannot exceed ${TIKTOK_ADS_DEFAULTS.MAX_TITLE_LENGTH} characters`);
    }
    if (data.description.length > TIKTOK_ADS_DEFAULTS.MAX_DESCRIPTION_LENGTH) {
      throw new Error(`Description cannot exceed ${TIKTOK_ADS_DEFAULTS.MAX_DESCRIPTION_LENGTH} characters`);
    }

    return apiClient.post<TikTokAdsCreativeDto>(TIKTOK_ADS_ENDPOINTS.CREATIVES, data);
  },

  /**
   * Cập nhật creative TikTok Ads
   */
  async updateCreative(id: number, data: UpdateTikTokAdsCreativeDto): Promise<ApiResponseDto<TikTokAdsCreativeDto>> {
    if (!id || id <= 0) {
      throw new Error('Invalid creative ID');
    }

    // Validate title and description length if provided
    if (data.title && data.title.length > TIKTOK_ADS_DEFAULTS.MAX_TITLE_LENGTH) {
      throw new Error(`Title cannot exceed ${TIKTOK_ADS_DEFAULTS.MAX_TITLE_LENGTH} characters`);
    }
    if (data.description && data.description.length > TIKTOK_ADS_DEFAULTS.MAX_DESCRIPTION_LENGTH) {
      throw new Error(`Description cannot exceed ${TIKTOK_ADS_DEFAULTS.MAX_DESCRIPTION_LENGTH} characters`);
    }

    return apiClient.put<TikTokAdsCreativeDto>(TIKTOK_ADS_ENDPOINTS.CREATIVE_DETAIL(id), data);
  },

  /**
   * Xóa creative TikTok Ads
   */
  async deleteCreative(id: number): Promise<ApiResponseDto<void>> {
    if (!id || id <= 0) {
      throw new Error('Invalid creative ID');
    }

    return apiClient.delete<void>(TIKTOK_ADS_ENDPOINTS.CREATIVE_DETAIL(id));
  },
};

/**
 * TikTok Ads Audience Service
 */
export const TikTokAdsAudienceService = {
  /**
   * Lấy danh sách audience TikTok Ads với business logic
   */
  async getAudiences(params?: TikTokAdsAudienceQueryDto): Promise<ApiResponseDto<TikTokAdsAudienceResponse>> {
    const defaultParams: TikTokAdsAudienceQueryDto = {
      page: TIKTOK_ADS_DEFAULTS.DEFAULT_PAGE,
      limit: TIKTOK_ADS_DEFAULTS.PAGE_SIZE,
      ...params,
    };

    // Validate params
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    return apiClient.get<TikTokAdsAudienceResponse>(TIKTOK_ADS_ENDPOINTS.AUDIENCES, { params: defaultParams });
  },

  /**
   * Lấy chi tiết audience TikTok Ads
   */
  async getAudience(id: number): Promise<ApiResponseDto<TikTokAdsAudienceDto>> {
    if (!id || id <= 0) {
      throw new Error('Invalid audience ID');
    }

    return apiClient.get<TikTokAdsAudienceDto>(TIKTOK_ADS_ENDPOINTS.AUDIENCE_DETAIL(id));
  },

  /**
   * Tạo audience TikTok Ads mới
   */
  async createAudience(data: CreateTikTokAdsAudienceDto): Promise<ApiResponseDto<TikTokAdsAudienceDto>> {
    // Validate required fields
    if (!data.name?.trim()) {
      throw new Error('Audience name is required');
    }
    if (!data.accountId?.trim()) {
      throw new Error('Account ID is required');
    }
    if (!data.type) {
      throw new Error('Audience type is required');
    }

    return apiClient.post<TikTokAdsAudienceDto>(TIKTOK_ADS_ENDPOINTS.AUDIENCES, data);
  },

  /**
   * Cập nhật audience TikTok Ads
   */
  async updateAudience(id: number, data: UpdateTikTokAdsAudienceDto): Promise<ApiResponseDto<TikTokAdsAudienceDto>> {
    if (!id || id <= 0) {
      throw new Error('Invalid audience ID');
    }

    return apiClient.put<TikTokAdsAudienceDto>(TIKTOK_ADS_ENDPOINTS.AUDIENCE_DETAIL(id), data);
  },

  /**
   * Xóa audience TikTok Ads
   */
  async deleteAudience(id: number): Promise<ApiResponseDto<void>> {
    if (!id || id <= 0) {
      throw new Error('Invalid audience ID');
    }

    return apiClient.delete<void>(TIKTOK_ADS_ENDPOINTS.AUDIENCE_DETAIL(id));
  },
};
