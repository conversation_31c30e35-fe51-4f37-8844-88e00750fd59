{"sms": {"title": "SMS Marketing", "description": "Manage SMS campaigns and messages", "overview": {"title": "SMS Overview", "description": "SMS Marketing statistics and performance", "stats": {"totalSent": "Total Messages Sent", "totalSentDescription": "+12% from last month", "totalDelivered": "Successfully Delivered", "deliveryRateDescription": "{{rate}}% success rate", "totalFailed": "Failed to Send", "totalCost": "Total Cost", "averageCostDescription": "${{cost}} average/SMS", "deliveryRate": "Delivery Rate", "averageCost": "Average Cost", "activeProviders": "Active Providers", "activeProvidersDescription": "Active providers", "activeCampaigns": "Active Campaigns"}, "quickActions": {"title": "Quick Actions", "sendSms": "Send SMS", "sendSmsDescription": "Send SMS message quickly", "createCampaign": "Create Campaign", "createCampaignDescription": "Create new SMS campaign", "createTemplate": "Create Template", "createTemplateDescription": "Create new message template", "manageContacts": "Manage Contacts", "manageContactsDescription": "Manage contact lists"}}, "campaigns": {"title": "SMS Campaigns", "description": "Manage SMS campaigns", "create": "Create Campaign", "edit": "Edit Campaign", "delete": "Delete Campaign", "duplicate": "Duplicate Campaign", "start": "Start", "pause": "Pause", "stop": "Stop", "resume": "Resume", "viewDetails": "View Details", "viewAnalytics": "View Analytics", "empty": "No campaigns yet", "form": {"name": "Campaign Name", "namePlaceholder": "Enter campaign name", "description": "Description", "descriptionPlaceholder": "Brief description of the campaign", "type": "Campaign Type", "priority": "Priority", "template": "Message Template", "selectTemplate": "Select message template", "contactLists": "Contact Lists", "selectContactLists": "Select contact lists", "schedule": "Schedule", "sendNow": "Send Now", "sendLater": "Send Later", "scheduledAt": "Send Time", "provider": "SMS Provider", "selectProvider": "Select provider", "sender": "Sender", "senderPlaceholder": "Sender name or phone number"}, "status": {"draft": "Draft", "scheduled": "Scheduled", "sending": "Sending", "sent": "<PERSON><PERSON>", "paused": "Paused", "cancelled": "Cancelled", "failed": "Failed", "running": "Running", "completed": "Completed"}, "type": {"immediate": "Send Now", "scheduled": "Scheduled", "triggered": "Event Triggered", "recurring": "Recurring"}, "priority": {"low": "Low", "normal": "Normal", "high": "High", "urgent": "<PERSON><PERSON>"}}, "templates": {"title": "Message Templates", "description": "Manage SMS message templates", "create": "Create Template", "edit": "Edit Template", "delete": "Delete Template", "duplicate": "Duplicate Template", "preview": "Preview", "validate": "Validate", "empty": "No templates yet", "form": {"name": "Template Name", "namePlaceholder": "Enter template name", "description": "Description", "descriptionPlaceholder": "Brief description of the template", "category": "Category", "selectCategory": "Select category", "content": "Message Content", "contentPlaceholder": "Enter message content...", "variables": "Variables", "addVariable": "Add Variable", "variableName": "Variable Name", "variableDescription": "Variable Description", "variableType": "Variable Type", "variableRequired": "Required", "variableDefault": "Default Value", "tags": "Tags", "tagsPlaceholder": "Enter tags and press Enter", "language": "Language", "isDefault": "<PERSON><PERSON><PERSON>"}, "category": {"marketing": "Marketing", "transactional": "Transactional", "reminder": "Reminder", "alert": "<PERSON><PERSON>", "otp": "OTP", "notification": "Notification", "welcome": "Welcome", "promotional": "Promotional"}, "status": {"draft": "Draft", "active": "Active", "inactive": "Inactive", "archived": "Archived", "pending_approval": "Pending Approval", "rejected": "Rejected"}, "validation": {"characterCount": "Character Count", "smsCount": "SMS Count", "containsUnicode": "Contains Unicode", "missingVariables": "Missing Variables", "unusedVariables": "Unused Variables", "tooLong": "Message Too Long", "invalidVariables": "Invalid Variables"}}, "providers": {"title": "SMS Providers", "description": "Manage SMS service providers", "add": "Add Provider", "edit": "Edit", "delete": "Delete", "test": {"title": "Test Provider", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number to test", "message": "Test Message", "messagePlaceholder": "Enter test message", "send": "Send Test", "success": "Test Successful", "failed": "Test Failed", "pending": "Testing..."}, "configure": "Configure", "activate": "Activate", "deactivate": "Deactivate", "empty": "No providers yet", "form": {"name": "Provider Name", "namePlaceholder": "Enter provider name", "type": "Provider Type", "selectType": "Select provider type", "description": "Description", "descriptionPlaceholder": "Description about the provider", "credentials": "Credentials", "apiKey": "API Key", "apiSecret": "API Secret", "accountSid": "Account SID", "authToken": "<PERSON><PERSON>", "region": "Region", "endpoint": "Endpoint", "username": "Username", "password": "Password", "settings": "Settings", "defaultSender": "<PERSON><PERSON><PERSON>", "maxLength": "Max Length", "supportUnicode": "Support Unicode", "supportDeliveryReports": "Support Delivery Reports", "costPerSms": "Cost per SMS", "currency": "<PERSON><PERSON><PERSON><PERSON>", "priority": "Priority", "rateLimits": "Rate Limits", "perSecond": "Per Second", "perMinute": "Per <PERSON>", "perHour": "Per Hour", "perDay": "Per Day"}, "types": {"twilio": "<PERSON><PERSON><PERSON>", "aws-sns": "AWS SNS", "viettel": "Viettel SMS", "vnpt": "VNPT SMS", "fpt": "FPT SMS", "custom": "Custom API"}, "status": {"active": "Active", "inactive": "Inactive", "error": "Error", "testing": "Testing"}}, "contacts": {"title": "Contact Lists", "description": "Manage contact lists and groups", "create": "Create List", "edit": "Edit", "delete": "Delete", "import": {"title": "Import Contact List", "selectFile": "Select File", "supportedFormats": "Supported: CSV, Excel", "mapping": "Column Mapping", "preview": "Preview", "import": "Import Data", "success": "Import Successful", "failed": "Import Failed"}, "export": "Export File", "addContact": "Add Contact", "removeContact": "Remove Contact", "empty": "No contact lists yet", "form": {"listName": "List Name", "listNamePlaceholder": "Enter list name", "description": "Description", "descriptionPlaceholder": "Description about the list", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number", "name": "Full Name", "namePlaceholder": "Enter full name", "email": "Email", "emailPlaceholder": "Enter email", "firstName": "First Name", "lastName": "Last Name", "country": "Country", "timezone": "Timezone", "language": "Language", "tags": "Tags", "customFields": "Custom Fields"}}, "analytics": {"title": "Reports & Analytics", "description": "SMS Marketing performance statistics", "viewDetailedReport": "View Detailed Report", "overview": "Overview", "campaigns": "By Campaign", "providers": "By Provider", "geographic": "By Geography", "timeRange": "Time Range", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days", "customRange": "Custom Range", "metrics": {"deliveryRate": "Delivery Rate", "clickRate": "Click Rate", "unsubscribeRate": "Unsubscribe Rate", "costPerSms": "Cost per SMS", "roi": "ROI", "conversionRate": "Conversion Rate"}}, "settings": {"title": "SMS Settings", "description": "General configuration for SMS Marketing", "configureSms": "Configure SMS", "general": "General", "compliance": "Compliance", "rateLimiting": "Rate Limiting", "costManagement": "Cost Management", "form": {"defaultProvider": "<PERSON><PERSON><PERSON> Provider", "defaultSender": "<PERSON><PERSON><PERSON>", "enableDeliveryReports": "Enable Delivery Reports", "enableClickTracking": "Enable Click Tracking", "enableOptOutManagement": "Enable Opt-out Management", "maxDailyLimit": "Daily Limit", "maxMonthlyLimit": "Monthly Limit", "timezone": "Timezone", "language": "Language", "requireOptIn": "Require Opt-in", "optOutKeywords": "Opt-out Keywords", "optInMessage": "Opt-in Message", "optOutMessage": "Opt-out Message", "dailyBudgetLimit": "Daily Budget Limit", "monthlyBudgetLimit": "Monthly Budget Limit", "costAlertThreshold": "Cost <PERSON><PERSON>"}}, "send": {"title": "Send SMS", "description": "Send SMS messages quickly to your customers", "brandnameSelection": "Select Brandname", "brandname": "Brandname", "selectBrandname": "Select brandname", "recipients": "Recipients", "recipientType": "Recipient Type", "singleRecipient": "Single Recipient", "multipleRecipients": "Multiple Recipients", "fileUpload": "File Upload", "phoneNumber": "Phone Number", "phoneNumbers": "Phone Numbers List", "phoneNumbersPlaceholder": "Enter each phone number on a new line", "uploadFile": "Upload File", "fileFormats": "Supports CSV, Excel, TXT files", "validatePhones": "Validate Phone Numbers", "invalidPhones": "{{count}} invalid phone numbers found", "allPhonesValid": "All phone numbers are valid", "messageContent": "Message Content", "template": "Message Template", "selectTemplate": "Select message template", "noTemplate": "No template", "content": "Content", "contentPlaceholder": "Enter message content", "characterCount": "Character count: {{count}}", "smsCount": "SMS count: {{count}}", "schedule": "Schedule", "scheduleType": "Send Time", "sendNow": "Send Now", "sendLater": "Schedule Send", "scheduleDateTime": "Date & Time", "options": "Options", "deliveryReport": "Delivery Report", "optOut": "Allow Opt-out", "singleSendSuccess": "Message sent successfully", "bulkSendSuccess": "Bulk messages sent successfully"}, "template": {"createTitle": "Create SMS Template", "createDescription": "Create SMS message templates for marketing campaigns", "basicInfo": "Basic Information", "name": "Template Name", "namePlaceholder": "Enter template name", "category": "Category", "selectCategory": "Select category", "description": "Description", "descriptionPlaceholder": "Enter template description", "content": "Content", "messageContent": "Message Content", "contentPlaceholder": "Enter template content. Use {{variable}} to add variables.", "characterCount": "Character count: {{count}}", "smsCount": "SMS count: {{count}}", "variables": "Variables", "variablesDescription": "Define variables that can be used in the template", "addVariable": "Add Variable", "variable": "Variable {{index}}", "variableName": "Variable Name", "variableType": "Type", "variableDescription": "Description", "variableDescriptionPlaceholder": "Describe this variable", "variableRequired": "Required", "tags": "Tags", "addTagPlaceholder": "Enter new tag", "addTag": "Add", "settings": "Settings", "language": "Language", "status": {"draft": "Draft", "active": "Active", "inactive": "Inactive"}, "isDefault": "<PERSON><PERSON><PERSON>", "create": "Create Template", "update": "Update", "createSuccess": "Template created successfully", "updateSuccess": "Template updated successfully", "categories": {"marketing": "Marketing", "transactional": "Transactional", "reminder": "Reminder", "alert": "<PERSON><PERSON>", "otp": "OTP", "notification": "Notification", "welcome": "Welcome", "promotional": "Promotional"}, "variableTypes": {"text": "Text", "number": "Number", "date": "Date", "url": "URL", "phone": "Phone Number"}, "languages": {"vi": "Vietnamese", "en": "English"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "loading": "Loading...", "noData": "No data", "error": "An error occurred", "success": "Success", "confirm": "Confirm", "yes": "Yes", "no": "No", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "selectAll": "Select All", "deselectAll": "Deselect All", "actions": "Actions", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "total": "Total", "showing": "Showing", "of": "of", "items": "items"}, "errors": {"invalidPhoneNumber": "Invalid phone number", "invalidMessageContent": "Invalid message content", "providerNotAvailable": "Provider not available", "rateLimitExceeded": "Rate limit exceeded", "insufficientBalance": "Insufficient balance", "templateNotFound": "Temp<PERSON> not found", "campaignNotFound": "Campaign not found", "contactOptedOut": "Contact has opted out", "messageTooLong": "Message too long", "invalidSenderId": "Invalid sender ID"}}}