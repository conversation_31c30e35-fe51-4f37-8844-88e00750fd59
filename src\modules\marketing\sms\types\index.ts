/**
 * SMS Marketing Types
 */

// Provider types
export * from './sms-provider.types';

// Campaign types
export * from './sms-campaign.types';

// Template types
export * from './sms-template.types';

// Contact types
export interface SmsContact {
  id: string;
  phoneNumber: string;
  name?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  country?: string;
  timezone?: string;
  language?: string;
  tags: string[];
  customFields: Record<string, unknown>;
  isOptedIn: boolean;
  optedInAt?: string;
  optedOutAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Contact List types
export interface SmsContactList {
  id: string;
  name: string;
  description?: string;
  contactCount: number;
  tags: string[];
  isDefault: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Analytics types
export interface SmsAnalytics {
  overview: {
    totalSent: number;
    totalDelivered: number;
    totalFailed: number;
    totalCost: number;
    deliveryRate: number;
    averageCostPerSms: number;
  };
  
  timeSeriesData: {
    date: string;
    sent: number;
    delivered: number;
    failed: number;
    cost: number;
  }[];
  
  providerPerformance: {
    providerId: string;
    providerName: string;
    sent: number;
    delivered: number;
    failed: number;
    deliveryRate: number;
    averageCost: number;
  }[];
  
  campaignPerformance: {
    campaignId: string;
    campaignName: string;
    sent: number;
    delivered: number;
    clickRate: number;
    cost: number;
    roi: number;
  }[];
  
  geographicData: {
    country: string;
    sent: number;
    delivered: number;
    cost: number;
  }[];
}

// Common SMS types
export interface SmsMessage {
  id: string;
  campaignId?: string;
  templateId?: string;
  providerId: string;
  phoneNumber: string;
  content: string;
  sender?: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'bounced';
  messageId?: string;
  cost?: number;
  sentAt?: string;
  deliveredAt?: string;
  failedReason?: string;
  metadata?: Record<string, unknown>;
}

// SMS Settings types
export interface SmsSettings {
  defaultProvider?: string;
  defaultSender?: string;
  enableDeliveryReports: boolean;
  enableClickTracking: boolean;
  enableOptOutManagement: boolean;
  maxDailyLimit?: number;
  maxMonthlyLimit?: number;
  timezone: string;
  language: string;
  
  // Compliance settings
  requireOptIn: boolean;
  optOutKeywords: string[];
  optInMessage?: string;
  optOutMessage?: string;
  
  // Rate limiting
  globalRateLimit: {
    perSecond: number;
    perMinute: number;
    perHour: number;
  };
  
  // Cost management
  dailyBudgetLimit?: number;
  monthlyBudgetLimit?: number;
  costAlertThreshold?: number;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  };
}

// Query parameters
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  dateFrom?: string;
  dateTo?: string;
}

// Pagination
export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Error types
export interface SmsError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

// Webhook types
export interface SmsWebhook {
  id: string;
  url: string;
  events: string[];
  isActive: boolean;
  secret?: string;
  retryCount: number;
  lastTriggeredAt?: string;
  createdAt: string;
}

// Opt-out management
export interface SmsOptOut {
  id: string;
  phoneNumber: string;
  reason?: string;
  source: 'manual' | 'keyword' | 'api' | 'complaint';
  optedOutAt: string;
  campaignId?: string;
}

// SMS Queue
export interface SmsQueueItem {
  id: string;
  campaignId?: string;
  phoneNumber: string;
  content: string;
  providerId: string;
  priority: number;
  scheduledAt?: string;
  attempts: number;
  maxAttempts: number;
  status: 'pending' | 'processing' | 'sent' | 'failed' | 'cancelled';
  createdAt: string;
  processedAt?: string;
}
