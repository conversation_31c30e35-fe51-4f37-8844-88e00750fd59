/**
 * SMS Campaign Types
 */

/**
 * SMS Campaign Status
 */
export type SmsCampaignStatus = 
  | 'draft'
  | 'scheduled'
  | 'sending'
  | 'sent'
  | 'paused'
  | 'cancelled'
  | 'failed';

/**
 * SMS Campaign Type
 */
export type SmsCampaignType = 
  | 'immediate'
  | 'scheduled'
  | 'triggered'
  | 'recurring';

/**
 * SMS Campaign Priority
 */
export type SmsCampaignPriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * SMS Delivery Status
 */
export type SmsDeliveryStatus = 
  | 'pending'
  | 'sent'
  | 'delivered'
  | 'failed'
  | 'bounced'
  | 'unsubscribed';

/**
 * SMS Campaign Recipient
 */
export interface SmsCampaignRecipient {
  id: string;
  phoneNumber: string;
  name?: string;
  variables?: Record<string, string>;
  status: SmsDeliveryStatus;
  sentAt?: string;
  deliveredAt?: string;
  failedReason?: string;
  messageId?: string;
  cost?: number;
}

/**
 * SMS Campaign Schedule
 */
export interface SmsCampaignSchedule {
  type: 'immediate' | 'scheduled' | 'recurring';
  scheduledAt?: string;
  timezone?: string;
  recurring?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    daysOfWeek?: number[];
    dayOfMonth?: number;
    endDate?: string;
  };
}

/**
 * SMS Campaign Settings
 */
export interface SmsCampaignSettings {
  providerId?: string;
  sender?: string;
  enableDeliveryReports?: boolean;
  enableClickTracking?: boolean;
  rateLimitPerMinute?: number;
  retryFailedMessages?: boolean;
  maxRetries?: number;
  fallbackProviderIds?: string[];
}

/**
 * SMS Campaign A/B Test
 */
export interface SmsCampaignABTest {
  enabled: boolean;
  testPercentage: number;
  variants: {
    id: string;
    name: string;
    templateId: string;
    percentage: number;
  }[];
  winnerCriteria: 'delivery_rate' | 'click_rate' | 'conversion_rate';
  testDuration: number; // hours
}

/**
 * SMS Campaign
 */
export interface SmsCampaign {
  id: string;
  name: string;
  description?: string;
  type: SmsCampaignType;
  status: SmsCampaignStatus;
  priority: SmsCampaignPriority;
  templateId: string;
  contactListIds: string[];
  schedule: SmsCampaignSchedule;
  settings: SmsCampaignSettings;
  abTest?: SmsCampaignABTest;
  
  // Statistics
  totalRecipients: number;
  sentCount: number;
  deliveredCount: number;
  failedCount: number;
  clickCount: number;
  unsubscribeCount: number;
  totalCost: number;
  
  // Metadata
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  sentAt?: string;
  completedAt?: string;
}

/**
 * SMS Campaign Create Request
 */
export interface CreateSmsCampaignRequest {
  name: string;
  description?: string;
  type: SmsCampaignType;
  priority?: SmsCampaignPriority;
  templateId: string;
  contactListIds: string[];
  schedule: SmsCampaignSchedule;
  settings?: Partial<SmsCampaignSettings>;
  abTest?: SmsCampaignABTest;
}

/**
 * SMS Campaign Update Request
 */
export interface UpdateSmsCampaignRequest {
  name?: string;
  description?: string;
  priority?: SmsCampaignPriority;
  templateId?: string;
  contactListIds?: string[];
  schedule?: SmsCampaignSchedule;
  settings?: Partial<SmsCampaignSettings>;
  abTest?: SmsCampaignABTest;
}

/**
 * SMS Campaign List Response
 */
export interface SmsCampaignListResponse {
  items: SmsCampaign[];
  total: number;
  page: number;
  limit: number;
}

/**
 * SMS Campaign Query Parameters
 */
export interface SmsCampaignQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: SmsCampaignStatus;
  type?: SmsCampaignType;
  priority?: SmsCampaignPriority;
  templateId?: string;
  createdBy?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'name' | 'status' | 'createdAt' | 'sentAt' | 'totalRecipients';
  sortOrder?: 'asc' | 'desc';
}

/**
 * SMS Campaign Analytics
 */
export interface SmsCampaignAnalytics {
  campaignId: string;
  deliveryRate: number;
  clickRate: number;
  unsubscribeRate: number;
  costPerSms: number;
  roi: number;
  conversionRate: number;
  
  // Time-based data
  hourlyStats: {
    hour: string;
    sent: number;
    delivered: number;
    clicked: number;
  }[];
  
  // Provider performance
  providerStats: {
    providerId: string;
    providerName: string;
    sent: number;
    delivered: number;
    failed: number;
    cost: number;
  }[];
  
  // Geographic data
  countryStats: {
    country: string;
    sent: number;
    delivered: number;
    cost: number;
  }[];
}
