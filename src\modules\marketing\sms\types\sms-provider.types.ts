/**
 * SMS Provider Types
 */

/**
 * Supported SMS Provider Types
 */
export type SmsProviderType = 
  | 'twilio'
  | 'aws-sns'
  | 'viettel'
  | 'vnpt'
  | 'fpt'
  | 'custom';

/**
 * SMS Provider Status
 */
export type SmsProviderStatus = 'active' | 'inactive' | 'error' | 'testing';

/**
 * SMS Provider Credentials
 */
export interface SmsProviderCredentials {
  apiKey?: string;
  apiSecret?: string;
  accountSid?: string;
  authToken?: string;
  region?: string;
  endpoint?: string;
  username?: string;
  password?: string;
  [key: string]: string | undefined;
}

/**
 * SMS Provider Rate Limits
 */
export interface SmsProviderRateLimits {
  perSecond: number;
  perMinute: number;
  perHour: number;
  perDay: number;
}

/**
 * SMS Provider Settings
 */
export interface SmsProviderSettings {
  defaultSender?: string;
  maxLength?: number;
  supportUnicode?: boolean;
  supportDeliveryReports?: boolean;
  rateLimits?: SmsProviderRateLimits;
  costPerSms?: number;
  currency?: string;
  timezone?: string;
}

/**
 * SMS Provider Configuration
 */
export interface SmsProviderConfig {
  id: string;
  name: string;
  type: SmsProviderType;
  description?: string;
  credentials: SmsProviderCredentials;
  settings: SmsProviderSettings;
  isActive: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
  lastTestedAt?: string;
  testStatus?: 'success' | 'failed' | 'pending';
  testMessage?: string;
}

/**
 * SMS Provider Test Request
 */
export interface SmsProviderTestRequest {
  providerId: string;
  phoneNumber: string;
  message: string;
}

/**
 * SMS Provider Test Response
 */
export interface SmsProviderTestResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  deliveryStatus?: string;
  cost?: number;
  timestamp: string;
}

/**
 * SMS Provider Statistics
 */
export interface SmsProviderStats {
  providerId: string;
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  deliveryRate: number;
  averageCost: number;
  lastUsed?: string;
  monthlyUsage: {
    month: string;
    sent: number;
    cost: number;
  }[];
}

/**
 * SMS Provider Create Request
 */
export interface CreateSmsProviderRequest {
  name: string;
  type: SmsProviderType;
  description?: string;
  credentials: SmsProviderCredentials;
  settings: SmsProviderSettings;
  priority?: number;
}

/**
 * SMS Provider Update Request
 */
export interface UpdateSmsProviderRequest {
  name?: string;
  description?: string;
  credentials?: Partial<SmsProviderCredentials>;
  settings?: Partial<SmsProviderSettings>;
  isActive?: boolean;
  priority?: number;
}

/**
 * SMS Provider List Response
 */
export interface SmsProviderListResponse {
  items: SmsProviderConfig[];
  total: number;
  page: number;
  limit: number;
}

/**
 * SMS Provider Query Parameters
 */
export interface SmsProviderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: SmsProviderType;
  status?: SmsProviderStatus;
  sortBy?: 'name' | 'type' | 'priority' | 'createdAt' | 'lastUsed';
  sortOrder?: 'asc' | 'desc';
}
