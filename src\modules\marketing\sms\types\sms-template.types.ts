/**
 * SMS Template Types
 */

/**
 * SMS Template Category
 */
export type SmsTemplateCategory = 
  | 'marketing'
  | 'transactional'
  | 'reminder'
  | 'alert'
  | 'otp'
  | 'notification'
  | 'welcome'
  | 'promotional';

/**
 * SMS Template Status
 */
export type SmsTemplateStatus = 
  | 'draft'
  | 'active'
  | 'inactive'
  | 'archived'
  | 'pending_approval'
  | 'rejected';

/**
 * SMS Template Variable
 */
export interface SmsTemplateVariable {
  name: string;
  description: string;
  type: 'text' | 'number' | 'date' | 'url' | 'phone';
  required: boolean;
  defaultValue?: string;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

/**
 * SMS Template Content
 */
export interface SmsTemplateContent {
  text: string;
  variables: SmsTemplateVariable[];
  characterCount: number;
  smsCount: number; // Number of SMS parts
  containsUnicode: boolean;
}

/**
 * SMS Template Approval
 */
export interface SmsTemplateApproval {
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedAt?: string;
  rejectedReason?: string;
  notes?: string;
}

/**
 * SMS Template Version
 */
export interface SmsTemplateVersion {
  id: string;
  version: number;
  content: SmsTemplateContent;
  createdBy: string;
  createdAt: string;
  isActive: boolean;
  changeLog?: string;
}

/**
 * SMS Template
 */
export interface SmsTemplate {
  id: string;
  name: string;
  description?: string;
  category: SmsTemplateCategory;
  status: SmsTemplateStatus;
  content: SmsTemplateContent;
  
  // Metadata
  tags: string[];
  language: string;
  isDefault: boolean;
  usageCount: number;
  
  // Approval workflow
  approval?: SmsTemplateApproval;
  
  // Versioning
  currentVersion: number;
  versions: SmsTemplateVersion[];
  
  // Timestamps
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lastUsedAt?: string;
}

/**
 * SMS Template Create Request
 */
export interface CreateSmsTemplateRequest {
  name: string;
  description?: string;
  category: SmsTemplateCategory;
  content: {
    text: string;
    variables: SmsTemplateVariable[];
  };
  tags?: string[];
  language?: string;
  isDefault?: boolean;
}

/**
 * SMS Template Update Request
 */
export interface UpdateSmsTemplateRequest {
  name?: string;
  description?: string;
  category?: SmsTemplateCategory;
  content?: {
    text: string;
    variables: SmsTemplateVariable[];
  };
  tags?: string[];
  status?: SmsTemplateStatus;
  isDefault?: boolean;
  changeLog?: string;
}

/**
 * SMS Template List Response
 */
export interface SmsTemplateListResponse {
  items: SmsTemplate[];
  total: number;
  page: number;
  limit: number;
}

/**
 * SMS Template Query Parameters
 */
export interface SmsTemplateQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: SmsTemplateCategory;
  status?: SmsTemplateStatus;
  language?: string;
  tags?: string[];
  createdBy?: string;
  isDefault?: boolean;
  sortBy?: 'name' | 'category' | 'status' | 'createdAt' | 'lastUsedAt' | 'usageCount';
  sortOrder?: 'asc' | 'desc';
}

/**
 * SMS Template Preview Request
 */
export interface SmsTemplatePreviewRequest {
  templateId: string;
  variables: Record<string, string>;
  phoneNumber?: string;
}

/**
 * SMS Template Preview Response
 */
export interface SmsTemplatePreviewResponse {
  text: string;
  characterCount: number;
  smsCount: number;
  containsUnicode: boolean;
  estimatedCost?: number;
  warnings?: string[];
}

/**
 * SMS Template Validation
 */
export interface SmsTemplateValidation {
  isValid: boolean;
  errors: {
    field: string;
    message: string;
  }[];
  warnings: {
    field: string;
    message: string;
  }[];
  characterCount: number;
  smsCount: number;
  missingVariables: string[];
  unusedVariables: string[];
}

/**
 * SMS Template Analytics
 */
export interface SmsTemplateAnalytics {
  templateId: string;
  totalUsage: number;
  successRate: number;
  averageDeliveryTime: number;
  clickRate: number;
  unsubscribeRate: number;
  
  // Usage over time
  usageHistory: {
    date: string;
    usage: number;
    deliveryRate: number;
  }[];
  
  // Campaign performance
  campaignStats: {
    campaignId: string;
    campaignName: string;
    sent: number;
    delivered: number;
    clicked: number;
    cost: number;
  }[];
  
  // Variable usage
  variableStats: {
    variable: string;
    usageCount: number;
    averageLength: number;
  }[];
}

/**
 * SMS Template Duplicate Request
 */
export interface DuplicateSmsTemplateRequest {
  templateId: string;
  newName: string;
  newDescription?: string;
}

/**
 * SMS Template Bulk Action Request
 */
export interface BulkSmsTemplateActionRequest {
  templateIds: string[];
  action: 'activate' | 'deactivate' | 'archive' | 'delete';
  reason?: string;
}
