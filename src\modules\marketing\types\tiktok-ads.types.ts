/**
 * TikTok Ads Types
 * Định nghĩa các types cho TikTok Ads API
 */

import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

// ==================== ENUMS ====================

/**
 * Trạng thái tài khoản TikTok Ads
 */
export enum TikTokAdsAccountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING = 'PENDING',
}

/**
 * Trạng thái chiến dịch TikTok Ads
 */
export enum TikTokAdsCampaignStatus {
  ENABLED = 'ENABLED',
  PAUSED = 'PAUSED',
  DELETED = 'DELETED',
  PENDING = 'PENDING',
}

/**
 * Mục tiêu chiến dịch TikTok Ads
 */
export enum TikTokAdsCampaignObjective {
  REACH = 'REACH',
  TRAFFIC = 'TRAFFIC',
  VIDEO_VIEWS = 'VIDEO_VIEWS',
  LEAD_GENERATION = 'LEAD_GENERATION',
  CONVERSIONS = 'CONVERSIONS',
  APP_PROMOTION = 'APP_PROMOTION',
  CATALOG_SALES = 'CATALOG_SALES',
}

/**
 * Trạng thái Ad Group
 */
export enum TikTokAdsAdGroupStatus {
  ENABLED = 'ENABLED',
  PAUSED = 'PAUSED',
  DELETED = 'DELETED',
}

/**
 * Trạng thái Creative/Ad
 */
export enum TikTokAdsCreativeStatus {
  ENABLED = 'ENABLED',
  PAUSED = 'PAUSED',
  DELETED = 'DELETED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  REJECTED = 'REJECTED',
}

/**
 * Loại creative
 */
export enum TikTokAdsCreativeType {
  VIDEO = 'VIDEO',
  IMAGE = 'IMAGE',
  SPARK_AD = 'SPARK_AD',
  COLLECTION = 'COLLECTION',
}

/**
 * Trạng thái audience
 */
export enum TikTokAdsAudienceStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
}

// ==================== DTOs ====================

/**
 * TikTok Ads Account DTO
 */
export interface TikTokAdsAccountDto {
  id: number;
  accountId: string;
  name: string;
  status: TikTokAdsAccountStatus;
  currency: string;
  timezone: string;
  balance: number;
  spendCap: number | null;
  createdAt: string;
  updatedAt: string;
}

/**
 * TikTok Ads Campaign DTO
 */
export interface TikTokAdsCampaignDto {
  id: number;
  campaignId: string;
  accountId: string;
  name: string;
  objective: TikTokAdsCampaignObjective;
  status: TikTokAdsCampaignStatus;
  budget: number;
  budgetMode: 'DAILY' | 'TOTAL';
  startTime: string;
  endTime: string | null;
  createdAt: string;
  updatedAt: string;
  // Performance metrics
  impressions?: number;
  clicks?: number;
  spend?: number;
  conversions?: number;
  ctr?: number;
  cpc?: number;
  cpm?: number;
  cvr?: number;
}

/**
 * TikTok Ads Ad Group DTO
 */
export interface TikTokAdsAdGroupDto {
  id: number;
  adGroupId: string;
  campaignId: string;
  name: string;
  status: TikTokAdsAdGroupStatus;
  budget: number;
  budgetMode: 'DAILY' | 'TOTAL';
  bidType: 'CPC' | 'CPM' | 'CPA';
  bidAmount: number;
  targeting: TikTokAdsTargeting;
  createdAt: string;
  updatedAt: string;
}

/**
 * TikTok Ads Creative DTO
 */
export interface TikTokAdsCreativeDto {
  id: number;
  creativeId: string;
  adGroupId: string;
  name: string;
  type: TikTokAdsCreativeType;
  status: TikTokAdsCreativeStatus;
  title: string;
  description: string;
  callToAction: string;
  landingPageUrl: string;
  videoUrl?: string;
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
  // Performance metrics
  impressions?: number;
  clicks?: number;
  spend?: number;
  conversions?: number;
}

/**
 * TikTok Ads Audience DTO
 */
export interface TikTokAdsAudienceDto {
  id: number;
  audienceId: string;
  accountId: string;
  name: string;
  type: 'CUSTOM' | 'LOOKALIKE' | 'SAVED';
  status: TikTokAdsAudienceStatus;
  size: number;
  description: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * TikTok Ads Targeting
 */
export interface TikTokAdsTargeting {
  locations: string[];
  ageMin: number;
  ageMax: number;
  genders: ('MALE' | 'FEMALE')[];
  languages: string[];
  interests: string[];
  behaviors: string[];
  deviceTypes: string[];
  operatingSystems: string[];
}

// ==================== QUERY DTOs ====================

/**
 * TikTok Ads Account Query DTO
 */
export interface TikTokAdsAccountQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: TikTokAdsAccountStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * TikTok Ads Campaign Query DTO
 */
export interface TikTokAdsCampaignQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  accountId?: string;
  status?: TikTokAdsCampaignStatus;
  objective?: TikTokAdsCampaignObjective;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  startDate?: string;
  endDate?: string;
}

/**
 * TikTok Ads Ad Group Query DTO
 */
export interface TikTokAdsAdGroupQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  campaignId?: string;
  status?: TikTokAdsAdGroupStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * TikTok Ads Creative Query DTO
 */
export interface TikTokAdsCreativeQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  adGroupId?: string;
  status?: TikTokAdsCreativeStatus;
  type?: TikTokAdsCreativeType;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * TikTok Ads Audience Query DTO
 */
export interface TikTokAdsAudienceQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  accountId?: string;
  status?: TikTokAdsAudienceStatus;
  type?: 'CUSTOM' | 'LOOKALIKE' | 'SAVED';
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

// ==================== CREATE/UPDATE DTOs ====================

/**
 * Create TikTok Ads Account DTO
 */
export interface CreateTikTokAdsAccountDto {
  name: string;
  currency: string;
  timezone: string;
  spendCap?: number;
}

/**
 * Update TikTok Ads Account DTO
 */
export interface UpdateTikTokAdsAccountDto {
  name?: string;
  spendCap?: number;
  status?: TikTokAdsAccountStatus;
}

/**
 * Create TikTok Ads Campaign DTO
 */
export interface CreateTikTokAdsCampaignDto {
  accountId: string;
  name: string;
  objective: TikTokAdsCampaignObjective;
  budget: number;
  budgetMode: 'DAILY' | 'TOTAL';
  startTime: string;
  endTime?: string;
}

/**
 * Update TikTok Ads Campaign DTO
 */
export interface UpdateTikTokAdsCampaignDto {
  name?: string;
  budget?: number;
  budgetMode?: 'DAILY' | 'TOTAL';
  startTime?: string;
  endTime?: string;
  status?: TikTokAdsCampaignStatus;
}

/**
 * Create TikTok Ads Creative DTO
 */
export interface CreateTikTokAdsCreativeDto {
  adGroupId: string;
  name: string;
  type: TikTokAdsCreativeType;
  title: string;
  description: string;
  callToAction: string;
  landingPageUrl: string;
  videoUrl?: string;
  imageUrl?: string;
}

/**
 * Update TikTok Ads Creative DTO
 */
export interface UpdateTikTokAdsCreativeDto {
  name?: string;
  title?: string;
  description?: string;
  callToAction?: string;
  landingPageUrl?: string;
  status?: TikTokAdsCreativeStatus;
}

/**
 * Create TikTok Ads Audience DTO
 */
export interface CreateTikTokAdsAudienceDto {
  accountId: string;
  name: string;
  type: 'CUSTOM' | 'LOOKALIKE' | 'SAVED';
  description: string;
  targeting?: TikTokAdsTargeting;
}

/**
 * Update TikTok Ads Audience DTO
 */
export interface UpdateTikTokAdsAudienceDto {
  name?: string;
  description?: string;
  status?: TikTokAdsAudienceStatus;
}

// ==================== RESPONSE TYPES ====================

/**
 * TikTok Ads Account Response
 */
export type TikTokAdsAccountResponse = PaginatedResult<TikTokAdsAccountDto>;

/**
 * TikTok Ads Campaign Response
 */
export type TikTokAdsCampaignResponse = PaginatedResult<TikTokAdsCampaignDto>;

/**
 * TikTok Ads Creative Response
 */
export type TikTokAdsCreativeResponse = PaginatedResult<TikTokAdsCreativeDto>;

/**
 * TikTok Ads Audience Response
 */
export type TikTokAdsAudienceResponse = PaginatedResult<TikTokAdsAudienceDto>;
