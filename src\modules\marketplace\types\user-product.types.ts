/**
 * Types và enums cho user product creation
 */

// Define product categories for user (chỉ 3 lựa chọn)
export enum UserProductCategory {
  KNOWLEDGE_FILE = 'KNOWLEDGE_FILE',
  AGENT = 'AGENT',
  OTHER = 'OTHER',
}

// Define the data structure for user product creation
export interface CreateUserProductDto {
  name: string;
  description: string;
  listedPrice: number;
  discountedPrice: number;
  category: UserProductCategory;
  sourceId?: string; // Optional cho category OTHER
  imagesMediaTypes?: string[];
  userManualMediaType?: string;
  detailMediaType?: string;
}
