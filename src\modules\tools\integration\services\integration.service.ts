import { apiClient } from '@/shared/api/axios';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  IntegrationToolListItem,
  IntegrationToolDetail,
  IntegrationResult,
  BaseQueryParams,
  IntegrateFromOpenApiParams,
  UpdateBaseUrlParams,
  UpdateToolAuthParams,
} from '../types/integration.types';

/**
 * Service xử lý các API liên quan đến tích hợp tool
 */
export class IntegrationService {
  private baseUrl = '/user/tools/integration';

  /**
   * Tích hợp công cụ từ đặc tả OpenAPI
   * @param params Tham số tích hợp
   * @returns Kết quả tích hợp
   */
  async integrateFromOpenApi(params: IntegrateFromOpenApiParams): Promise<IntegrationResult> {
    try {
      const response = await apiClient.post<IntegrationResult>(`${this.baseUrl}/openapi`, params, {
        tokenType: 'user',
      });
      return response.result;
    } catch (error) {
      console.error('Error integrating from OpenAPI:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách công cụ tùy chỉnh
   * @param params Tham số truy vấn
   * @returns Danh sách công cụ tùy chỉnh với phân trang
   */
  async getCustomTools(params: BaseQueryParams): Promise<PaginatedResult<IntegrationToolListItem>> {
    try {
      const response = await apiClient.get<PaginatedResult<IntegrationToolListItem>>(
        `${this.baseUrl}/tools`,
        {
          params,
          tokenType: 'user',
        }
      );
      return response.result;
    } catch (error) {
      console.error('Error fetching custom tools:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết công cụ tùy chỉnh
   * @param toolId ID của công cụ
   * @returns Thông tin chi tiết công cụ
   */
  async getCustomToolById(toolId: string): Promise<IntegrationToolDetail> {
    try {
      const response = await apiClient.get<IntegrationToolDetail>(
        `${this.baseUrl}/tools/${toolId}`,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error fetching custom tool detail:', error);
      throw error;
    }
  }

  /**
   * Xóa công cụ tùy chỉnh
   * @param toolId ID của công cụ
   * @returns Kết quả xóa
   */
  async deleteCustomTool(toolId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/tools/${toolId}`, { tokenType: 'user' });
    } catch (error) {
      console.error('Error deleting custom tool:', error);
      throw error;
    }
  }

  /**
   * Cập nhật base URL cho công cụ tùy chỉnh
   * @param params Tham số cập nhật
   * @returns Kết quả cập nhật
   */
  async updateBaseUrl(params: UpdateBaseUrlParams): Promise<IntegrationToolDetail> {
    try {
      const response = await apiClient.patch<IntegrationToolDetail>(
        `${this.baseUrl}/tools/base-url`,
        params,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error updating base URL:', error);
      throw error;
    }
  }

  /**
   * Cập nhật cấu hình xác thực cho công cụ
   * @param params Tham số cập nhật
   * @returns Kết quả cập nhật
   */
  async updateToolAuth(params: UpdateToolAuthParams): Promise<IntegrationToolDetail> {
    try {
      const response = await apiClient.patch<IntegrationToolDetail>(
        `${this.baseUrl}/tools/auth`,
        params,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error updating tool auth:', error);
      throw error;
    }
  }

  /**
   * Kích hoạt/vô hiệu hóa công cụ tùy chỉnh
   * @param toolId ID của công cụ
   * @returns Kết quả cập nhật
   */
  async toggleToolStatus(toolId: string): Promise<IntegrationToolDetail> {
    try {
      const response = await apiClient.patch<IntegrationToolDetail>(
        `${this.baseUrl}/tools/${toolId}/toggle-status`,
        {},
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error toggling tool status:', error);
      throw error;
    }
  }

  /**
   * Test kết nối với API
   * @param toolId ID của công cụ
   * @returns Kết quả test
   */
  async testConnection(toolId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post<{ success: boolean; message: string }>(
        `${this.baseUrl}/tools/${toolId}/test`,
        {},
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error testing connection:', error);
      throw error;
    }
  }

  /**
   * Refresh OpenAPI spec
   * @param toolId ID của công cụ
   * @returns Kết quả refresh
   */
  async refreshOpenApiSpec(toolId: string): Promise<IntegrationToolDetail> {
    try {
      const response = await apiClient.post<IntegrationToolDetail>(
        `${this.baseUrl}/tools/${toolId}/refresh`,
        {},
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error refreshing OpenAPI spec:', error);
      throw error;
    }
  }

  /**
   * Export integration configuration
   * @param toolId ID của công cụ
   * @returns Configuration data
   */
  async exportConfiguration(toolId: string): Promise<Record<string, unknown>> {
    try {
      const response = await apiClient.get<Record<string, unknown>>(
        `${this.baseUrl}/tools/${toolId}/export`,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error exporting configuration:', error);
      throw error;
    }
  }

  /**
   * Import integration configuration
   * @param config Configuration data
   * @returns Import result
   */
  async importConfiguration(config: Record<string, unknown>): Promise<IntegrationResult> {
    try {
      const response = await apiClient.post<IntegrationResult>(`${this.baseUrl}/import`, config, {
        tokenType: 'user',
      });
      return response.result;
    } catch (error) {
      console.error('Error importing configuration:', error);
      throw error;
    }
  }
}

export const integrationService = new IntegrationService();
