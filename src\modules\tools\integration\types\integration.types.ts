/**
 * Types cho user tool integration
 */

// Enums
export enum AuthType {
  NONE = 'none',
  API_KEY = 'api_key',
  OAUTH = 'oauth',
}

export enum ApiKeyLocation {
  HEADER = 'header',
  QUERY = 'query',
  COOKIE = 'cookie',
}

export enum TokenSource {
  HEADER = 'header',
  QUERY = 'query',
  BODY = 'body',
}

export enum IntegrationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
}

// JSON Schema type definition
export interface JSONSchema {
  type?: string | string[];
  format?: string;
  title?: string;
  description?: string;
  default?: unknown;
  multipleOf?: number;
  maximum?: number;
  exclusiveMaximum?: boolean | number;
  minimum?: number;
  exclusiveMinimum?: boolean | number;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  maxItems?: number;
  minItems?: number;
  uniqueItems?: boolean;
  maxProperties?: number;
  minProperties?: number;
  required?: string[];
  enum?: unknown[];
  items?: JSONSchema | JSONSchema[];
  properties?: Record<string, JSONSchema>;
  additionalProperties?: boolean | JSONSchema;
  allOf?: JSONSchema[];
  anyOf?: JSONSchema[];
  oneOf?: JSONSchema[];
  not?: JSONSchema;
  $ref?: string;
  nullable?: boolean;
  readOnly?: boolean;
  writeOnly?: boolean;
  example?: unknown;
  examples?: Record<string, unknown>;
}

// OpenAPI types
export interface ReferenceObject {
  $ref: string;
}

export interface HeaderObject {
  description?: string;
  required?: boolean;
  deprecated?: boolean;
  schema?: JSONSchema;
}

export interface ParameterObject {
  name: string;
  in: 'query' | 'header' | 'path' | 'cookie';
  description?: string;
  required?: boolean;
  deprecated?: boolean;
  schema?: JSONSchema;
}

export interface ExampleObject {
  summary?: string;
  description?: string;
  value?: unknown;
  externalValue?: string;
}

export interface EncodingObject {
  contentType?: string;
  headers?: Record<string, HeaderObject | ReferenceObject>;
  style?: string;
  explode?: boolean;
  allowReserved?: boolean;
}

export interface MediaTypeObject {
  schema?: JSONSchema | ReferenceObject;
  example?: unknown;
  examples?: Record<string, ExampleObject | ReferenceObject>;
  encoding?: Record<string, EncodingObject>;
}

export interface RequestBodyObject {
  description?: string;
  content: Record<string, MediaTypeObject>;
  required?: boolean;
}

export interface ResponseObject {
  description: string;
  headers?: Record<string, HeaderObject | ReferenceObject>;
  content?: Record<string, MediaTypeObject>;
}

export interface OAuthFlowObject {
  authorizationUrl?: string;
  tokenUrl?: string;
  refreshUrl?: string;
  scopes: Record<string, string>;
}

export interface OAuthFlowsObject {
  implicit?: OAuthFlowObject;
  password?: OAuthFlowObject;
  clientCredentials?: OAuthFlowObject;
  authorizationCode?: OAuthFlowObject;
}

export interface SecuritySchemeObject {
  type: 'apiKey' | 'http' | 'oauth2' | 'openIdConnect';
  description?: string;
  name?: string;
  in?: 'query' | 'header' | 'cookie';
  scheme?: string;
  bearerFormat?: string;
  flows?: OAuthFlowsObject;
  openIdConnectUrl?: string;
}

export interface SecurityRequirementObject {
  [key: string]: string[];
}

export interface ServerObject {
  url: string;
  description?: string;
  variables?: Record<
    string,
    {
      enum?: string[];
      default: string;
      description?: string;
    }
  >;
}

export interface OperationObject {
  tags?: string[];
  summary?: string;
  description?: string;
  operationId?: string;
  parameters?: (ParameterObject | ReferenceObject)[];
  requestBody?: RequestBodyObject | ReferenceObject;
  responses: Record<string, ResponseObject | ReferenceObject>;
  security?: SecurityRequirementObject[];
  servers?: ServerObject[];
  deprecated?: boolean;
}

export interface PathItemObject {
  $ref?: string;
  summary?: string;
  description?: string;
  get?: OperationObject;
  put?: OperationObject;
  post?: OperationObject;
  delete?: OperationObject;
  options?: OperationObject;
  head?: OperationObject;
  patch?: OperationObject;
  trace?: OperationObject;
  servers?: ServerObject[];
  parameters?: (ParameterObject | ReferenceObject)[];
}

export interface OpenApiSpec {
  openapi: string;
  info: {
    title: string;
    version: string;
    description?: string;
    termsOfService?: string;
    contact?: {
      name?: string;
      url?: string;
      email?: string;
    };
    license?: {
      name?: string;
      url?: string;
    };
  };
  servers?: ServerObject[];
  paths: Record<string, PathItemObject>;
  components?: {
    schemas?: Record<string, JSONSchema | ReferenceObject>;
    responses?: Record<string, ResponseObject | ReferenceObject>;
    parameters?: Record<string, ParameterObject | ReferenceObject>;
    examples?: Record<string, ExampleObject | ReferenceObject>;
    requestBodies?: Record<string, RequestBodyObject | ReferenceObject>;
    headers?: Record<string, HeaderObject | ReferenceObject>;
    securitySchemes?: Record<string, SecuritySchemeObject | ReferenceObject>;
  };
  security?: SecurityRequirementObject[];
  tags?: Array<{
    name: string;
    description?: string;
    externalDocs?: {
      url: string;
      description?: string;
    };
  }>;
  externalDocs?: {
    url: string;
    description?: string;
  };
}

// Auth Config Types
export interface ApiKeyAuthConfig {
  authType: AuthType.API_KEY;
  schemeName: string;
  apiKey: string;
  apiKeyLocation: ApiKeyLocation;
  paramName: string;
}

export interface OAuthAuthConfig {
  authType: AuthType.OAUTH;
  schemeName: string;
  token: string;
  tokenSource: TokenSource;
}

export interface NoAuthConfig {
  authType: AuthType.NONE;
}

export type AuthConfig = ApiKeyAuthConfig | OAuthAuthConfig | NoAuthConfig;

// Tool Parameter Types
export interface ToolParameter {
  name: string;
  type: string;
  description?: string;
  required: boolean;
  default?: unknown;
  schema?: JSONSchema;
}

// Integration Tool Types
export interface IntegrationToolListItem {
  id: string;
  name: string;
  description?: string;
  baseUrl?: string;
  status: IntegrationStatus;
  authType: AuthType;
  method?: string;
  createdAt: number;
  updatedAt: number;
  toolCount?: number;
}

export interface IntegratedTool {
  id: string;
  name: string;
  description?: string;
  method: string;
  path: string;
  parameters?: Record<string, ToolParameter>;
  isActive: boolean;
}

export interface IntegrationToolDetail extends IntegrationToolListItem {
  openapiSpec?: OpenApiSpec;
  authConfig?: AuthConfig;
  tools?: IntegratedTool[];
}

// API Params Types
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  status?: IntegrationStatus;
  authType?: AuthType;
}

export interface IntegrationSearchParams extends BaseQueryParams {
  startDate?: string;
  endDate?: string;
}

export interface IntegrateFromOpenApiParams {
  openapiSpec: OpenApiSpec;
  baseUrl?: string;
  authConfig?: AuthConfig;
}

export interface UpdateBaseUrlParams {
  toolId: string;
  baseUrl: string;
}

export interface UpdateToolAuthParams {
  toolId: string;
  authConfig: AuthConfig;
}

export interface IntegrationResult {
  id: string;
  name: string;
  description?: string;
  baseUrl?: string;
  toolsCreated: number;
  status: IntegrationStatus;
  message?: string;
}

// Form Types
export interface IntegrationFormData {
  name?: string;
  description?: string;
  baseUrl?: string;
  openapiSpec?: OpenApiSpec;
  authConfig?: AuthConfig;
}

export interface CreateIntegrationFormData extends IntegrationFormData {
  openapiSpec: OpenApiSpec;
}

export interface UpdateIntegrationFormData extends IntegrationFormData {
  id: string;
}

// Response Types
export interface IntegrationToolListResponse {
  items: IntegrationToolListItem[];
  total: number;
  page: number;
  limit: number;
}

export interface IntegrationToolResponse {
  tool: IntegrationToolDetail;
}

export interface IntegrationResultResponse {
  result: IntegrationResult;
}

// Error Types
export interface IntegrationError {
  code: string;
  message: string;
  details?: Record<string, string>;
}

// Utility Types
export type RequestHeaders = Record<string, string>;
export type RequestParams = Record<string, string | number | boolean | null>;
export type RequestBody = Record<string, unknown>;
