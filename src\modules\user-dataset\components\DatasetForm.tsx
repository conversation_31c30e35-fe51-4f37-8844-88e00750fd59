import React from 'react';
import { ImportedConversation } from '../user-data-fine-tune/types/user-data-fine-tune.types';
import ChatLayoutTraining from './ChatLayoutTraining';

interface DatasetFormProps {
  /**
   * Callback khi conversations thay đổi
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component hiển thị ChatLayoutTraining cho training data
 * Role Logic: SystemRole → UserRole → UserRole/AssistantRole
 */
const DatasetForm: React.FC<DatasetFormProps> = ({ onConversationsChange }) => {
  // Handle conversations change từ ChatLayoutTraining
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    console.log(
      '📦 [DatasetForm] Received conversations from ChatLayoutTraining:',
      updatedConversations.length
    );
    // Notify parent component
    if (onConversationsChange) {
      console.log(
        '📦 [DatasetForm] Calling parent onConversationsChange with:',
        updatedConversations.length
      );
      onConversationsChange(updatedConversations);
    } else {
      console.warn('📦 [DatasetForm] No onConversationsChange callback provided!');
    }
  };

  return (
    <div className="h-full">
      {/* Training Data Chat Layout Container - Full height */}
      <div className="h-full">
        <ChatLayoutTraining onConversationsChange={handleConversationsChange} />
      </div>
    </div>
  );
};

export default DatasetForm;
