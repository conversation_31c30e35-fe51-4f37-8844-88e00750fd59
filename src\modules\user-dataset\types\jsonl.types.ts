/**
 * Interface cho JSONL dataset format
 */

export interface Message {
  role: string;
  content: string;
  weight?: number;
}

export interface Messages {
  messages: Message[];
}

export interface JsonlLine {
  [key: string]: Messages;
}

/**
 * Interface cho dataset creation payload
 */
export interface DatasetCreationPayload {
  trainData: Messages[];
  validData: Messages[];
  name?: string;
  description?: string;
  provider?: string;
}

/**
 * Utility functions để convert data
 */
import {
  ImportedConversation,
  DatasetMessage,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

/**
 * Convert DatasetMessage to Message format
 */
export const convertDatasetMessageToMessage = (datasetMessage: DatasetMessage): Message => {
  return {
    role: datasetMessage.role,
    content: datasetMessage.content,
    ...(datasetMessage.weight !== undefined && { weight: datasetMessage.weight }),
  };
};

/**
 * Convert ImportedConversation to Messages format
 */
export const convertConversationToMessages = (conversation: ImportedConversation): Messages => {
  return {
    messages: conversation.messages.map(convertDatasetMessageToMessage),
  };
};

/**
 * Convert array of ImportedConversation to array of Messages
 */
export const convertConversationsToMessagesArray = (
  conversations: ImportedConversation[]
): Messages[] => {
  return conversations.map(convertConversationToMessages);
};

/**
 * Create dataset payload from training and validation conversations
 */
export const createDatasetPayload = (
  trainingConversations: ImportedConversation[],
  validationConversations: ImportedConversation[],
  additionalData?: {
    name?: string;
    description?: string;
    provider?: string;
  }
): DatasetCreationPayload => {
  return {
    trainData: convertConversationsToMessagesArray(trainingConversations),
    validData: convertConversationsToMessagesArray(validationConversations),
    ...additionalData,
  };
};
