import { apiClient } from '@/shared/api/axios';
import {
  CreateUserDataFineTuneDto,
  UpdateUserDataFineTuneDto,
  UserDataFineTuneResponseDto,
  UserDataFineTuneDetailResponseDto,
  UserDataFineTuneQueryDto,
  CreateDatasetResponse,
  UploadUrlResponse,
  UpdateStatusResponse,
  PaginatedResult,
} from '../types/user-data-fine-tune.types';

const API_BASE_URL = '/user/data-fine-tune';

/**
 * Tạo mới dataset fine tune
 * @param data Dữ liệu để tạo dataset
 * @returns Thông tin dataset đã tạo với upload URLs
 */
export const createUserDataFineTune = async (
  data: CreateUserDataFineTuneDto
): Promise<CreateDatasetResponse> => {
  const response = await apiClient.post<CreateDatasetResponse>(API_BASE_URL, data);
  return response.result;
};

/**
 * Cập nhật trạng thái upload dataset
 * @param id ID của dataset
 * @param status Trạng thái upload (true/false)
 * @returns Thông báo kết quả
 */
export const updateUploadStatus = async (
  id: string,
  status: boolean
): Promise<UpdateStatusResponse> => {
  const response = await apiClient.patch<UpdateStatusResponse>(
    `${API_BASE_URL}/${id}/upload-url-success?status=${status}`
  );
  return response.result;
};

/**
 * Lấy danh sách dataset fine tune của user có phân trang
 * @param queryDto Tham số truy vấn
 * @returns Danh sách dataset với phân trang
 */
export const getUserDataFineTuneList = async (
  queryDto?: UserDataFineTuneQueryDto
): Promise<PaginatedResult<UserDataFineTuneResponseDto>> => {
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.search) queryParams.append('search', queryDto.search);
  if (queryDto?.status) queryParams.append('status', queryDto.status);
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_URL}?${queryString}` : API_BASE_URL;

  const response = await apiClient.get<PaginatedResult<UserDataFineTuneResponseDto>>(url);
  return response.result;
};

/**
 * Lấy chi tiết dataset fine tune
 * @param id ID của dataset
 * @returns Thông tin chi tiết dataset
 */
export const getUserDataFineTuneDetail = async (
  id: string
): Promise<UserDataFineTuneDetailResponseDto> => {
  const response = await apiClient.get<UserDataFineTuneDetailResponseDto>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Cập nhật dataset fine tune
 * @param id ID của dataset
 * @param data Dữ liệu cần cập nhật
 * @returns Thông tin dataset đã cập nhật
 */
export const updateUserDataFineTune = async (
  id: string,
  data: UpdateUserDataFineTuneDto
): Promise<UserDataFineTuneDetailResponseDto> => {
  const response = await apiClient.patch<UserDataFineTuneDetailResponseDto>(
    `${API_BASE_URL}/${id}`,
    data
  );
  return response.result;
};

/**
 * Xóa dataset fine tune
 * @param id ID của dataset
 * @returns Kết quả xóa
 */
export const deleteUserDataFineTune = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Lấy URL upload dataset
 * @param mime MIME type của file
 * @returns URL để upload
 */
export const getUploadUrl = async (mime: string): Promise<UploadUrlResponse> => {
  const response = await apiClient.get<UploadUrlResponse>(
    `${API_BASE_URL}/upload-url?mime=${encodeURIComponent(mime)}`
  );
  return response.result;
};

/**
 * Upload file to presigned URL
 * @param uploadUrl URL để upload
 * @param file File cần upload
 */
export const uploadFile = async (uploadUrl: string, file: File): Promise<void> => {
  await fetch(uploadUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type,
    },
  });
};

/**
 * Upload JSONL data as string
 * @param uploadUrl URL để upload
 * @param jsonlData Dữ liệu JSONL dạng string
 */
export const uploadJsonlData = async (uploadUrl: string, jsonlData: string): Promise<void> => {
  const blob = new Blob([jsonlData], { type: 'application/jsonl' });
  await fetch(uploadUrl, {
    method: 'PUT',
    body: blob,
    headers: {
      'Content-Type': 'application/jsonl',
    },
  });
};

/**
 * Validate JSONL format
 * @param jsonlData Dữ liệu JSONL cần validate
 * @returns Kết quả validation
 */
export const validateJsonlData = (jsonlData: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const lines = jsonlData.trim().split('\n');

  if (lines.length === 0) {
    errors.push('JSONL data cannot be empty');
    return { isValid: false, errors };
  }

  /**
   * Validates each line of JSONL data for correct structure and content
   *
   * Checks for:
   * - Non-empty lines
   * - Valid JSON parsing
   * - Presence of 'messages' array
   * - Non-empty 'messages' array
   * - Each message having 'role' and 'content' fields
   * - Valid message roles ('system', 'user', 'assistant')
   *
   * Populates the errors array with detailed validation messages
   *
   * @param lines Array of JSONL data lines to validate
   * @param errors Array to collect validation error messages
   */
  lines.forEach((line, index) => {
    if (line.trim() === '') {
      errors.push(`Line ${index + 1}: Empty line is not allowed`);
      return;
    }

    try {
      const parsed = JSON.parse(line);

      // Validate required structure for fine-tuning
      if (!parsed.messages || !Array.isArray(parsed.messages)) {
        errors.push(`Line ${index + 1}: Missing or invalid 'messages' array`);
        return;
      }

      if (parsed.messages.length === 0) {
        errors.push(`Line ${index + 1}: 'messages' array cannot be empty`);
        return;
      }

      // Validate message structure
      parsed.messages.forEach((message: unknown, msgIndex: number) => {
        const msg = message as { role?: string; content?: string };
        if (!msg.role || !msg.content) {
          errors.push(
            `Line ${index + 1}, Message ${msgIndex + 1}: Missing 'role' or 'content' field`
          );
        }

        if (!['system', 'user', 'assistant'].includes(msg.role || '')) {
          errors.push(
            `Line ${index + 1}, Message ${msgIndex + 1}: Invalid role '${msg.role}'. Must be 'system', 'user', or 'assistant'`
          );
        }
      });
    } catch (error) {
      console.error(`Error parsing line ${index + 1}:`, error);
      errors.push(`Line ${index + 1}: Invalid JSON format`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Convert conversations to JSONL format
 * @param conversations Mảng conversations cần convert
 * @returns Dữ liệu JSONL dạng string
 */
export const convertConversationsToJsonl = (conversations: { messages: unknown[] }[]): string => {
  return conversations.map(conv => JSON.stringify({ messages: conv.messages })).join('\n');
};

/**
 * Get file size in bytes
 * @param content Nội dung file
 * @returns Kích thước file tính bằng bytes
 */
export const getFileSizeInBytes = (content: string): number => {
  return new Blob([content]).size;
};

/**
 * Format file size for display
 * @param bytes Kích thước tính bằng bytes
 * @returns Chuỗi hiển thị kích thước file
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
