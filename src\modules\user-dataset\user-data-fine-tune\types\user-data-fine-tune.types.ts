/**
 * Enums và constants
 */
export enum ProviderFineTuneEnum {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
}

export enum DataFineTuneStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export enum UserDataFineTuneSortBy {
  CREATED_AT = 'createdAt',
  NAME = 'name',
}

export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho việc tạo mới user data fine tune
 */
export interface CreateUserDataFineTuneDto {
  /**
   * Tên của bộ dữ liệu fine-tune
   */
  name: string;

  /**
   * Mô tả nội dung bộ dữ liệu
   */
  description?: string;

  /**
   * Nhà cung cấp AI
   */
  provider: ProviderFineTuneEnum;

  /**
   * Tập dữ liệu huấn luyện, định dạng JSON
   */
  trainDataset: string;

  /**
   * Tập dữ liệu validation, định dạng JSON (nếu có)
   */
  validDataset?: string;
}

/**
 * DTO cho việc cập nhật user data fine tune
 */
export interface UpdateUserDataFineTuneDto {
  /**
   * Tên của bộ dữ liệu fine-tune
   */
  name?: string;

  /**
   * Mô tả nội dung bộ dữ liệu
   */
  description?: string;

  /**
   * Nhà cung cấp AI
   */
  provider?: ProviderFineTuneEnum;

  /**
   * Tập dữ liệu huấn luyện, định dạng JSON
   */
  trainDataset?: string;

  /**
   * Tập dữ liệu validation, định dạng JSON (nếu có)
   */
  validDataset?: string;
}

/**
 * DTO cho response của user data fine-tune
 */
export interface UserDataFineTuneResponseDto {
  /**
   * ID của bộ dữ liệu
   */
  id: string;

  /**
   * Tên bộ dữ liệu
   */
  name: string;

  /**
   * Mô tả về bộ dữ liệu
   */
  description: string | null;

  /**
   * Thời gian tạo (epoch timestamp)
   */
  createdAt: number;

  /**
   * Trạng thái của bộ dữ liệu
   */
  status: DataFineTuneStatus;
}

/**
 * DTO cho response chi tiết của user data fine-tune
 */
export interface UserDataFineTuneDetailResponseDto extends UserDataFineTuneResponseDto {
  /**
   * Đường dẫn đến tập dữ liệu huấn luyện
   */
  trainDatasetUrl: string;

  /**
   * Đường dẫn đến tập dữ liệu validation
   */
  validDatasetUrl: string | null;

  /**
   * Nhà cung cấp AI
   */
  provider: ProviderFineTuneEnum;
}

/**
 * DTO cho việc truy vấn danh sách user data fine-tune
 */
export interface UserDataFineTuneQueryDto {
  /**
   * Số trang (bắt đầu từ 1)
   */
  page?: number;

  /**
   * Số lượng item trên mỗi trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Tìm kiếm theo trạng thái
   */
  status?: DataFineTuneStatus;

  /**
   * Trường sắp xếp
   */
  sortBy?: UserDataFineTuneSortBy;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: SortDirection;
}

/**
 * Response cho việc tạo dataset với upload URLs
 */
export interface CreateDatasetResponse {
  id: string;
  trainUploadUrl: string;
  validUploadUrl: string | null;
}

/**
 * Response cho việc lấy upload URL
 */
export interface UploadUrlResponse {
  uploadUrl: string;
}

/**
 * Response cho việc cập nhật trạng thái upload
 */
export interface UpdateStatusResponse {
  message: string;
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface DatasetMessage {
  /**
   * Vai trò của message (system, user, assistant)
   */
  role: 'system' | 'user' | 'assistant';

  /**
   * Nội dung của message
   */
  content: string;
  weight?: number;
}

export interface ImportedConversation {
  /**
   * ID duy nhất của conversation
   */
  id: string;

  /**
   * Tiêu đề conversation (từ first user message)
   */
  title: string;

  /**
   * Danh sách messages trong conversation
   */
  messages: DatasetMessage[];

  /**
   * Thời gian tạo
   */
  createdAt: Date;
}
