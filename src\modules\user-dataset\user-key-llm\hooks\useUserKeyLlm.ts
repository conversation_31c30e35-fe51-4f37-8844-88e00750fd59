import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import {
  getUserKeyLlmList,
  getUserKeyLlmDetail,
  createUserKeyLlm,
  updateUser<PERSON><PERSON>Llm,
  deleteUserKeyLlm,
  testUserKeyLlm,
  getUserKeyLlmUsageStats,
  getKeyQuotaInfo,
  getAvailableModels,
  activateUserKeyLlm,
  deactivateUserKeyLlm,
  regenerateUserKeyLlm,
  duplicateUserKeyLlm,
  validateKeyFormat,
  batchDeleteUserKeyLlm,
  batchActivateUserKeyLlm,
  batchDeactivateUserKeyLlm,
  exportUserKeyLlm,
  importUserKeyLlm,
} from '../services/user-key-llm.service';
import {
  UserKeyLlmQueryDto,
  CreateUserKeyLlmDto,
  UpdateUserKeyLlmDto,
  TestKeyDto,
  TestKeyResponseDto,
} from '../types/user-key-llm.types';

/**
 * Interface cho API error response
 */
interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
  message?: string;
}

/**
 * Query keys cho User Key LLM
 */
export const userKeyLlmKeys = {
  all: ['user-key-llm'] as const,
  lists: () => [...userKeyLlmKeys.all, 'list'] as const,
  list: (params: UserKeyLlmQueryDto) => [...userKeyLlmKeys.lists(), params] as const,
  details: () => [...userKeyLlmKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeyLlmKeys.details(), id] as const,
  usageStats: (id: string) => [...userKeyLlmKeys.all, 'usage-stats', id] as const,
  quotaInfo: (id: string) => [...userKeyLlmKeys.all, 'quota', id] as const,
  models: (provider: string) => [...userKeyLlmKeys.all, 'models', provider] as const,
};

/**
 * Hook để lấy danh sách key LLM
 */
export const useUserKeyLlmList = (params: UserKeyLlmQueryDto) => {
  return useQuery({
    queryKey: userKeyLlmKeys.list(params),
    queryFn: () => getUserKeyLlmList(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết key LLM
 */
export const useUserKeyLlmDetail = (id: string, enabled = true) => {
  return useQuery({
    queryKey: userKeyLlmKeys.detail(id),
    queryFn: () => getUserKeyLlmDetail(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thống kê sử dụng
 */
export const useUserKeyLlmUsageStats = (id: string, enabled = true) => {
  return useQuery({
    queryKey: userKeyLlmKeys.usageStats(id),
    queryFn: () => getUserKeyLlmUsageStats(id),
    enabled: enabled && !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook để lấy thông tin quota
 */
export const useUserKeyLlmQuotaInfo = (id: string, enabled = true) => {
  return useQuery({
    queryKey: userKeyLlmKeys.quotaInfo(id),
    queryFn: () => getKeyQuotaInfo(id),
    enabled: enabled && !!id,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

/**
 * Hook để lấy danh sách models
 */
export const useAvailableModels = (provider: string, enabled = true) => {
  return useQuery({
    queryKey: userKeyLlmKeys.models(provider),
    queryFn: () => getAvailableModels(provider),
    enabled: enabled && !!provider,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook để tạo key LLM
 */
export const useCreateUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (data: CreateUserKeyLlmDto) => createUserKeyLlm(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      success({ message: 'Tạo key LLM thành công' });
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi tạo key LLM' });
    },
  });
};

/**
 * Hook để cập nhật key LLM
 */
export const useUpdateUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserKeyLlmDto }) =>
      updateUserKeyLlm(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.detail(id) });
      success({ message: 'Cập nhật key LLM thành công' });
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật key LLM' });
    },
  });
};

/**
 * Hook để xóa key LLM
 */
export const useDeleteUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (id: string) => deleteUserKeyLlm(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      success({ message: 'Xóa key LLM thành công' });
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi xóa key LLM' });
    },
  });
};

/**
 * Hook để test key LLM
 */
export const useTestUserKeyLlm = () => {
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data?: TestKeyDto }) =>
      testUserKeyLlm(id, data),
    onSuccess: (result: TestKeyResponseDto) => {
      if (result.success) {
        success({ message: 'Test key thành công' });
      } else {
        error({ message: `Test key thất bại: ${result.message}` });
      }
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi test key' });
    },
  });
};

/**
 * Hook để kích hoạt key
 */
export const useActivateUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (id: string) => activateUserKeyLlm(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.detail(id) });
      success({ message: 'Kích hoạt key thành công' });
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi kích hoạt key' });
    },
  });
};

/**
 * Hook để vô hiệu hóa key
 */
export const useDeactivateUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (id: string) => deactivateUserKeyLlm(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.detail(id) });
      success({ message: 'Vô hiệu hóa key thành công' });
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi vô hiệu hóa key' });
    },
  });
};

/**
 * Hook để làm mới key
 */
export const useRegenerateUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (id: string) => regenerateUserKeyLlm(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.detail(id) });
      success({ message: 'Làm mới key thành công' });
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi làm mới key' });
    },
  });
};

/**
 * Hook để nhân bản key
 */
export const useDuplicateUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      duplicateUserKeyLlm(id, name),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      success({ message: 'Nhân bản key thành công' });
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi nhân bản key' });
    },
  });
};

/**
 * Hook để validate key format
 */
export const useValidateKeyFormat = () => {
  const { error } = useSmartNotification();

  return useMutation({
    mutationFn: ({ provider, apiKey }: { provider: string; apiKey: string }) =>
      validateKeyFormat(provider, apiKey),
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi validate key' });
    },
  });
};

/**
 * Hook để batch delete keys
 */
export const useBatchDeleteUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (ids: string[]) => batchDeleteUserKeyLlm(ids),
    onSuccess: (result: { deleted?: number; failed?: number }, variables: string[]) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      success({ message: `Đã xóa ${result.deleted || variables.length} key(s) thành công` });
      if (result.failed && result.failed > 0) {
        error({ message: `${result.failed} key(s) xóa thất bại` });
      }
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi xóa keys' });
    },
  });
};

/**
 * Hook để batch activate keys
 */
export const useBatchActivateUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (ids: string[]) => batchActivateUserKeyLlm(ids),
    onSuccess: (result: { activated?: number; failed?: number }, variables: string[]) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      success({ message: `Đã kích hoạt ${result.activated || variables.length} key(s) thành công` });
      if (result.failed && result.failed > 0) {
        error({ message: `${result.failed} key(s) kích hoạt thất bại` });
      }
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi kích hoạt keys' });
    },
  });
};

/**
 * Hook để batch deactivate keys
 */
export const useBatchDeactivateUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (ids: string[]) => batchDeactivateUserKeyLlm(ids),
    onSuccess: (result: { deactivated?: number; failed?: number }, variables: string[]) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      success({ message: `Đã vô hiệu hóa ${result.deactivated || variables.length} key(s) thành công` });
      if (result.failed && result.failed > 0) {
        error({ message: `${result.failed} key(s) vô hiệu hóa thất bại` });
      }
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi vô hiệu hóa keys' });
    },
  });
};

/**
 * Hook để export keys
 */
export const useExportUserKeyLlm = () => {
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: () => exportUserKeyLlm(),
    onSuccess: (blob: Blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `user-keys-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      success({ message: 'Export keys thành công' });
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi export keys' });
    },
  });
};

/**
 * Hook để import keys
 */
export const useImportUserKeyLlm = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (file: File) => importUserKeyLlm(file),
    onSuccess: (result: { imported?: number; failed?: number }) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      success({ message: `Import thành công ${result.imported || 0} key(s)` });
      if (result.failed && result.failed > 0) {
        error({ message: `${result.failed} key(s) import thất bại` });
      }
    },
    onError: (apiError: ApiError) => {
      error({ message: apiError?.response?.data?.message || 'Có lỗi xảy ra khi import keys' });
    },
  });
};
