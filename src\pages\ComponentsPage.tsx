import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  PhoneInput,
  Form,
  FormItem,
  Button,
  Divider,
  ResponsiveGrid,
  CodeBlock,
} from '@/shared/components/common';
import type { FieldValues } from 'react-hook-form';
import CollapsibleCardDemo from '@/shared/components/common/CollapsibleCard.demo';


// Define E164Number type locally since it's not exported
type E164Number = string;

interface PhoneFormValues {
  phone: E164Number | undefined;
  phoneInternational: E164Number | undefined;
  phoneWithCode: E164Number | undefined;
}

/**
 * Components demo page showcasing PhoneInput component
 */
const ComponentsPage: React.FC = () => {
  const { t } = useTranslation(['common']);
  // State for controlled components
  const [basicPhone, setBasicPhone] = useState<E164Number | undefined>();
  const [internationalPhone, setInternationalPhone] = useState<E164Number | undefined>();
  const [phoneWithCode, setPhoneWithCode] = useState<E164Number | undefined>();
  const [disabledPhone, setDisabledPhone] = useState<E164Number | undefined>('+84987654321' as E164Number);



  const handleFormSubmit = (values: FieldValues) => {
    const phoneValues = values as PhoneFormValues;
    console.log('Form submitted with values:', phoneValues);

    // Simple validation example
    if (!phoneValues.phone) {
      alert('Số điện thoại là bắt buộc');
      return;
    }

    alert(`Form submitted successfully!\nPhone: ${phoneValues.phone}\nInternational: ${phoneValues.phoneInternational}\nWith Code: ${phoneValues.phoneWithCode}`);
  };



  const basicUsageCode = useMemo(() => `import { PhoneInput } from '@/shared/components/common';

const [phone, setPhone] = useState<string | undefined>();

<PhoneInput
  value={phone}
  onChange={setPhone}
  placeholder="Nhập số điện thoại"
  defaultCountry="VN"
  fullWidth
/>`, []);

  const formUsageCode = useMemo(() => `import { Form, FormItem, PhoneInput } from '@/shared/components/common';

<Form onSubmit={handleSubmit}>
  <FormItem name="phone" label="Số điện thoại" required>
    <PhoneInput
      placeholder="Nhập số điện thoại"
      defaultCountry="VN"
      fullWidth
    />
  </FormItem>
</Form>`, []);

  const internationalCode = useMemo(() => `<PhoneInput
  value={phone}
  onChange={setPhone}
  international
  withCountryCallingCode
  defaultCountry="VN"
  fullWidth
/>`, []);



  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <Typography variant="h1">Phone Input Components</Typography>
          <Typography variant="body1" className="text-muted-foreground">
            Các component nhập số điện thoại với tùy chọn chọn quốc gia, hỗ trợ đa ngôn ngữ và validation.
          </Typography>
        </div>

        <ResponsiveGrid maxColumns={{ xs: 1, lg: 2 }} gap={4}>
          {/* Basic Usage */}
          <Card className="p-6">
            <div className="space-y-4">
              <Typography variant="h3">Sử dụng cơ bản</Typography>
              <Typography variant="body2" className="text-muted-foreground">
                PhoneInput với quốc gia mặc định là Việt Nam
              </Typography>
              
              <PhoneInput
                value={basicPhone || ''}
                onChange={setBasicPhone}
                placeholder={t('common:phoneNumber')}
                defaultCountry="VN"
                fullWidth
              />
              
              <Typography variant="body2" className="text-muted-foreground">
                Giá trị: {basicPhone || 'undefined'}
              </Typography>
              
              <CodeBlock language="tsx" code={basicUsageCode} />
            </div>
          </Card>

          {/* International Format */}
          <Card className="p-6">
            <div className="space-y-4">
              <Typography variant="h3">Định dạng quốc tế</Typography>
              <Typography variant="body2" className="text-muted-foreground">
                Hiển thị mã quốc gia trong input
              </Typography>
              
              <PhoneInput
                value={internationalPhone}
                onChange={setInternationalPhone}
                international
                withCountryCallingCode
                placeholder="Nhập số điện thoại"
                defaultCountry="VN"
                fullWidth
              />
              
              <Typography variant="body2" className="text-muted-foreground">
                Giá trị: {internationalPhone || 'undefined'}
              </Typography>
              
              <CodeBlock language="tsx" code={internationalCode} />
            </div>
          </Card>

          {/* Different Countries */}
          <Card className="p-6">
            <div className="space-y-4">
              <Typography variant="h3">Các quốc gia khác nhau</Typography>
              <Typography variant="body2" className="text-muted-foreground">
                Thử với các quốc gia khác nhau
              </Typography>
              
              <div className="space-y-3">
                <div>
                  <Typography variant="body2" className="mb-2">Việt Nam (VN)</Typography>
                  <PhoneInput
                    value={phoneWithCode}
                    onChange={setPhoneWithCode}
                    defaultCountry="VN"
                    fullWidth
                  />
                </div>
                
                <div>
                  <Typography variant="body2" className="mb-2">Hoa Kỳ (US)</Typography>
                  <PhoneInput
                    defaultCountry="US"
                    fullWidth
                  />
                </div>
                
                <div>
                  <Typography variant="body2" className="mb-2">Nhật Bản (JP)</Typography>
                  <PhoneInput
                    defaultCountry="JP"
                    fullWidth
                  />
                </div>
              </div>
            </div>
          </Card>

          {/* States */}
          <Card className="p-6">
            <div className="space-y-4">
              <Typography variant="h3">Các trạng thái</Typography>
              <Typography variant="body2" className="text-muted-foreground">
                Disabled, error, readonly states
              </Typography>
              
              <div className="space-y-3">
                <div>
                  <Typography variant="body2" className="mb-2">Disabled</Typography>
                  <PhoneInput
                    value={disabledPhone}
                    onChange={setDisabledPhone}
                    disabled
                    defaultCountry="VN"
                    fullWidth
                  />
                </div>
                
                <div>
                  <Typography variant="body2" className="mb-2">Error state</Typography>
                  <PhoneInput
                    error
                    placeholder="Số điện thoại không hợp lệ"
                    defaultCountry="VN"
                    fullWidth
                  />
                </div>
                
                <div>
                  <Typography variant="body2" className="mb-2">Read only</Typography>
                  <PhoneInput
                    value="+84987654321"
                    readOnly
                    defaultCountry="VN"
                    fullWidth
                  />
                </div>
              </div>
            </div>
          </Card>
        </ResponsiveGrid>

        <Divider />

        {/* Form Integration */}
        <Card className="p-6">
          <div className="space-y-4">
            <Typography variant="h3">Tích hợp với Form</Typography>
            <Typography variant="body2" className="text-muted-foreground">
              Sử dụng PhoneInput trong Form với validation
            </Typography>
            
            <Form onSubmit={handleFormSubmit} className="space-y-4">
              <ResponsiveGrid maxColumns={{ xs: 1, md: 3 }} gap={3}>
                <FormItem name="phone" label="Số điện thoại cơ bản" required>
                  <PhoneInput
                    placeholder="Nhập số điện thoại"
                    defaultCountry="VN"
                    fullWidth
                  />
                </FormItem>

                <FormItem name="phoneInternational" label="Số điện thoại quốc tế">
                  <PhoneInput
                    international
                    withCountryCallingCode
                    placeholder="Nhập số điện thoại"
                    defaultCountry="VN"
                    fullWidth
                  />
                </FormItem>

                <FormItem name="phoneWithCode" label="Với mã quốc gia">
                  <PhoneInput
                    withCountryCallingCode
                    placeholder="Nhập số điện thoại"
                    defaultCountry="US"
                    fullWidth
                  />
                </FormItem>
              </ResponsiveGrid>
              
              <Button type="submit" variant="primary">
                Submit Form
              </Button>
            </Form>
            
            <CodeBlock language="tsx" code={formUsageCode} />
          </div>
        </Card>

        <Divider />

        {/* CollapsibleCard Dropdown Test */}
        <Card className="p-6">
          <div className="space-y-4">
            <Typography variant="h3">CollapsibleCard Dropdown Overflow Test</Typography>
            <Typography variant="body2" className="text-muted-foreground">
              Test để kiểm tra dropdown menu hiển thị đúng trong CollapsibleCard
            </Typography>

            <CollapsibleCardDemo />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ComponentsPage;
