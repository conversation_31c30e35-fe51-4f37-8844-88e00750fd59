import { useState } from 'react';
import { Calendar } from '@/shared/components/common/DatePicker';

/**
 * Simple Calendar Demo Page
 * Basic demo without complex dependencies
 */
const SimpleCalendarDemo = () => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          Calendar Components Demo
        </h1>
        
        <div className="grid md:grid-cols-2 gap-8">
          {/* Basic Calendar */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Basic Calendar
            </h2>
            <div className="flex justify-center">
              <Calendar
                selectedDate={selectedDate}
                onSelectDate={setSelectedDate}
                showTodayButton
                showWeekNumbers
              />
            </div>
            <div className="mt-4 text-center text-sm text-gray-600">
              Selected: {selectedDate ? selectedDate.toLocaleDateString() : 'None'}
            </div>
          </div>

          {/* Calendar Info */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Features
            </h2>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Responsive Design
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Touch Support
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Keyboard Navigation
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Accessibility
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Custom Themes
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                TypeScript Support
              </li>
            </ul>

            <div className="mt-6">
              <h3 className="font-semibold text-gray-900 mb-2">Usage</h3>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-x-auto">
{`import { Calendar } from '@/shared/components/common/DatePicker';

<Calendar
  selectedDate={selectedDate}
  onSelectDate={setSelectedDate}
  showTodayButton
  showWeekNumbers
/>`}
              </pre>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <a
            href="/components"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            View All Components
          </a>
        </div>
      </div>
    </div>
  );
};

export default SimpleCalendarDemo;
