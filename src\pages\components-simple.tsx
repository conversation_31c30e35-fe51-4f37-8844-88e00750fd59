/**
 * Simple Components showcase page
 * Trang tổng quan về components không cần dependencies phức tạp
 */
const SimpleComponentsPage = () => {
  const componentCategories = [
    {
      id: 'calendar',
      title: 'Calendar Components',
      description: 'Advanced calendar components with animations, themes, and time zone support',
      href: '/calendar-demo-simple',
      components: [
        'Basic Calendar',
        'Multi-Select Calendar', 
        'Event Calendar',
        'Animated Calendar',
        'Advanced Range Picker',
        'Time Zone Calendar',
        'Recurring Events Calendar',
        'Theme Customization'
      ],
      features: [
        'Responsive Design',
        'Touch Gestures',
        'Keyboard Navigation',
        'Accessibility Support',
        'Custom Themes',
        'Animations',
        'Time Zone Support',
        'Recurring Events'
      ]
    },
    {
      id: 'forms',
      title: 'Form Components',
      description: 'Form inputs, validation, and interactive form elements',
      href: '#',
      components: [
        'Input Fields',
        'Select Dropdowns',
        'Checkboxes & Radios',
        'File Upload',
        'Form Validation',
        'Multi-step Forms'
      ],
      features: [
        'Real-time Validation',
        'Custom Styling',
        'Error Handling',
        'Accessibility',
        'TypeScript Support'
      ]
    },
    {
      id: 'data-display',
      title: 'Data Display',
      description: 'Tables, charts, and data visualization components',
      href: '#',
      components: [
        'Data Tables',
        'Charts & Graphs',
        'Statistics Cards',
        'Progress Indicators',
        'Badges & Tags'
      ],
      features: [
        'Sorting & Filtering',
        'Pagination',
        'Export Functions',
        'Interactive Charts',
        'Real-time Updates'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <a href="/" className="text-blue-600 hover:text-blue-700 mr-4">
                ← Home
              </a>
              <h1 className="text-2xl font-bold text-gray-900">
                Components Library
              </h1>
            </div>
            <div className="text-sm text-gray-600">
              {componentCategories.length} Categories
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            RedAI Component Library
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive collection of reusable React components built with TypeScript, 
            Tailwind CSS, and modern best practices. Fully accessible, responsive, and customizable.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-blue-600 mb-2">50+</div>
            <div className="text-sm text-gray-600">Components</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-green-600 mb-2">100%</div>
            <div className="text-sm text-gray-600">TypeScript</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-purple-600 mb-2">WCAG</div>
            <div className="text-sm text-gray-600">Accessible</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-orange-600 mb-2">Mobile</div>
            <div className="text-sm text-gray-600">First</div>
          </div>
        </div>

        {/* Component Categories */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {componentCategories.map((category) => (
            <div
              key={category.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
            >
              {/* Header */}
              <div className="p-6 border-b border-gray-200 bg-blue-50">
                <div className="flex items-center mb-3">
                  <div className="w-8 h-8 bg-blue-600 rounded mr-3 flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {category.title.charAt(0)}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-blue-700">{category.title}</h3>
                </div>
                <p className="text-sm text-blue-600">{category.description}</p>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Components List */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">
                    Components ({category.components.length})
                  </h4>
                  <div className="space-y-2">
                    {category.components.slice(0, 4).map((component, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></div>
                        {component}
                      </div>
                    ))}
                    {category.components.length > 4 && (
                      <div className="text-sm text-gray-500">
                        +{category.components.length - 4} more...
                      </div>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">
                    Key Features
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {category.features.slice(0, 3).map((feature, idx) => (
                      <span
                        key={idx}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {feature}
                      </span>
                    ))}
                    {category.features.length > 3 && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        +{category.features.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {/* Action Button */}
                <div>
                  {category.href === '#' ? (
                    <button
                      disabled
                      className="w-full px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 rounded-md cursor-not-allowed"
                    >
                      Coming Soon
                    </button>
                  ) : (
                    <a
                      href={category.href}
                      className="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                    >
                      View Components
                      <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Getting Started */}
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8 border border-gray-200">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Getting Started
          </h3>
          <p className="text-gray-600 mb-6">
            All components are built with modern React patterns and best practices. 
            They're fully typed with TypeScript and designed to be accessible and responsive.
          </p>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📦</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Import</h4>
              <p className="text-sm text-gray-600">
                Import components from the shared library
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎨</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Customize</h4>
              <p className="text-sm text-gray-600">
                Use props and themes to customize appearance
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚀</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Deploy</h4>
              <p className="text-sm text-gray-600">
                Build and deploy with confidence
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleComponentsPage;
