import { useState } from 'react';
// import { useTranslation } from 'react-i18next'; // Unused for now
import { motion } from 'framer-motion';
import {
  Calendar,
  MultiSelectCalendar,
  EventCalendar,
  AnimatedCalendar,
  AdvancedRangePicker,
  TimeZoneCalendar,
  RecurringEventCalendar,
  CalendarThemeProvider,
  CalendarThemeCustomizer,
  CalendarEvent,
  RecurringEvent,
} from '@/shared/components/common/DatePicker';

/**
 * Calendar Demo Page
 * Showcase tất cả các Calendar components với examples
 */
const CalendarDemoPage = () => {
  // const { t } = useTranslation(); // Unused for now

  // States cho các demos
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  const [rangeValue, setRangeValue] = useState<[Date | null, Date | null]>([null, null]);
  const [timeZoneDate, setTimeZoneDate] = useState<Date | null>(new Date());

  // Sample events cho EventCalendar
  const sampleEvents: CalendarEvent[] = [
    {
      id: '1',
      date: new Date(2024, new Date().getMonth(), 5),
      title: 'Team Meeting',
      description: 'Weekly team standup meeting',
      color: '#3B82F6',
      type: 'dot',
      priority: 'high',
      startTime: '09:00',
      endTime: '10:00',
    },
    {
      id: '2',
      date: new Date(2024, new Date().getMonth(), 10),
      title: 'Project Deadline',
      description: 'Final submission deadline',
      color: '#EF4444',
      type: 'badge',
      priority: 'high',
    },
    {
      id: '3',
      date: new Date(2024, new Date().getMonth(), 15),
      title: 'Holiday',
      description: 'National holiday',
      color: '#10B981',
      type: 'highlight',
      priority: 'medium',
    },
    {
      id: '4',
      date: new Date(2024, new Date().getMonth(), 20),
      title: 'Conference',
      description: 'Tech conference',
      color: '#8B5CF6',
      type: 'dot',
      priority: 'medium',
      startTime: '08:00',
      endTime: '18:00',
    },
  ];

  // Sample recurring events
  const [recurringEvents, setRecurringEvents] = useState<RecurringEvent[]>([
    {
      id: 'recurring-1',
      title: 'Daily Standup',
      startDate: new Date(2024, 0, 1),
      recurrenceRule: {
        pattern: 'weekdays',
        interval: 1,
      },
      color: '#3B82F6',
      type: 'dot',
      description: 'Daily team standup meeting',
    },
    {
      id: 'recurring-2',
      title: 'Weekly Review',
      startDate: new Date(2024, 0, 5),
      recurrenceRule: {
        pattern: 'weekly',
        interval: 1,
        daysOfWeek: [5], // Friday
      },
      color: '#10B981',
      type: 'badge',
      description: 'Weekly project review',
    },
  ]);

  // Demo sections
  const demoSections = [
    {
      id: 'basic-calendar',
      title: 'Basic Calendar',
      description: 'Calendar cơ bản với tất cả tính năng tối ưu hóa',
    },
    {
      id: 'multi-select',
      title: 'Multi-Select Calendar',
      description: 'Calendar hỗ trợ chọn nhiều ngày',
    },
    {
      id: 'event-calendar',
      title: 'Event Calendar',
      description: 'Calendar với events và tooltips',
    },
    {
      id: 'animated-calendar',
      title: 'Animated Calendar',
      description: 'Calendar với animations mượt mà',
    },
    {
      id: 'range-picker',
      title: 'Advanced Range Picker',
      description: 'Range picker với preset ranges',
    },
    {
      id: 'timezone-calendar',
      title: 'Time Zone Calendar',
      description: 'Calendar với hỗ trợ multiple time zones',
    },
    {
      id: 'recurring-events',
      title: 'Recurring Events Calendar',
      description: 'Calendar với recurring events',
    },
    {
      id: 'theme-customization',
      title: 'Theme Customization',
      description: 'Customize themes và color schemes',
    },
  ];

  return (
    <div>
      {/* SEO Meta */}
      <title>Calendar Components Demo - RedAI</title>

      <CalendarThemeProvider
        defaultVariant="auto"
        defaultColorScheme="blue"
        enableAnimations={true}
        persistSettings={true}
      >
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          {/* Header */}
          <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between h-16">
                <div className="flex items-center">
                  <a href="/components" className="text-blue-600 hover:text-blue-700 mr-4">
                    ← Back to Components
                  </a>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Calendar Components Demo
                  </h1>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {demoSections.length} Components
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Quick Navigation
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {demoSections.map((section) => (
                  <a
                    key={section.id}
                    href={`#${section.id}`}
                    className="p-3 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="font-medium text-sm text-gray-900 dark:text-white">
                      {section.title}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      {section.description}
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Demo Sections */}
            <div className="space-y-12">
              {/* Basic Calendar */}
              <motion.section
                id="basic-calendar"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Basic Calendar
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Calendar cơ bản với responsive design, keyboard navigation và touch support.
                </p>
                <div className="flex justify-center">
                  <Calendar
                    selectedDate={selectedDate}
                    onSelectDate={setSelectedDate}
                    showTodayButton
                    showWeekNumbers
                  />
                </div>
                <div className="mt-4 text-center text-sm text-gray-600 dark:text-gray-400">
                  Selected: {selectedDate ? selectedDate.toLocaleDateString() : 'None'}
                </div>
              </motion.section>

              {/* Multi-Select Calendar */}
              <motion.section
                id="multi-select"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Multi-Select Calendar
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Calendar hỗ trợ chọn nhiều ngày với giới hạn và clear functionality.
                </p>
                <div className="flex justify-center">
                  <MultiSelectCalendar
                    selectedDates={selectedDates}
                    onSelectDates={setSelectedDates}
                    maxSelections={5}
                    showSelectedCount
                    showClearButton
                    showTodayButton
                  />
                </div>
                <div className="mt-4 text-center text-sm text-gray-600 dark:text-gray-400">
                  Selected {selectedDates.length} dates
                </div>
              </motion.section>

              {/* Event Calendar */}
              <motion.section
                id="event-calendar"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Event Calendar
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Calendar với events, tooltips và legend. Hover để xem chi tiết events.
                </p>
                <div className="flex justify-center">
                  <EventCalendar
                    events={sampleEvents}
                    onEventClick={(event) => alert(`Event: ${event.title}`)}
                    showEventTooltip
                    showEventLegend
                    showTodayButton
                  />
                </div>
              </motion.section>

              {/* Animated Calendar */}
              <motion.section
                id="animated-calendar"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Animated Calendar
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Calendar với smooth animations sử dụng Framer Motion.
                </p>
                <div className="flex justify-center">
                  <AnimatedCalendar
                    enableAnimations={true}
                    animationPreset="smooth"
                    animationDirection="horizontal"
                    showTodayButton
                  />
                </div>
              </motion.section>

              {/* Advanced Range Picker */}
              <motion.section
                id="range-picker"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Advanced Range Picker
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Range picker với preset ranges và dual calendar view.
                </p>
                <div className="flex justify-center">
                  <AdvancedRangePicker
                    value={rangeValue}
                    onChange={setRangeValue}
                    showPresets={true}
                    showTwoCalendars={false}
                    showDaysCount={true}
                    enableAnimations={true}
                    layout="vertical"
                  />
                </div>
              </motion.section>

              {/* Time Zone Calendar */}
              <motion.section
                id="timezone-calendar"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Time Zone Calendar
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Calendar với hỗ trợ multiple time zones và current time display.
                </p>
                <div className="flex justify-center">
                  <TimeZoneCalendar
                    selectedDate={timeZoneDate}
                    onSelectDate={setTimeZoneDate}
                    showTimeZoneSelector={true}
                    showCurrentTime={true}
                    showUTC={true}
                    compactTimeZoneSelector={true}
                  />
                </div>
              </motion.section>

              {/* Recurring Events Calendar */}
              <motion.section
                id="recurring-events"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Recurring Events Calendar
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Calendar với recurring events và event creator.
                </p>
                <div className="flex justify-center">
                  <RecurringEventCalendar
                    recurringEvents={recurringEvents}
                    oneTimeEvents={sampleEvents}
                    onCreateRecurringEvent={(event) => {
                      setRecurringEvents(prev => [...prev, event]);
                    }}
                    showRecurringEventCreator={true}
                    showEventTooltip={true}
                  />
                </div>
              </motion.section>

              {/* Theme Customization */}
              <motion.section
                id="theme-customization"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Theme Customization
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Customize theme variant, color scheme và animations.
                </p>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Theme Controls
                    </h3>
                    <CalendarThemeCustomizer
                      showVariantSelector={true}
                      showColorSchemeSelector={true}
                      showAnimationToggle={true}
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Preview
                    </h3>
                    <Calendar showTodayButton />
                  </div>
                </div>
              </motion.section>
            </div>

            {/* Footer */}
            <div className="mt-12 text-center">
              <a
                href="/components"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                ← Back to Components
              </a>
            </div>
          </div>
        </div>
      </CalendarThemeProvider>
    </div>
  );
};

export default CalendarDemoPage;
