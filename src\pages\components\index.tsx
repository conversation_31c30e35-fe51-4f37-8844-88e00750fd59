// import { useTranslation } from 'react-i18next'; // Unused for now
import { motion } from 'framer-motion';

// Simple icon components to replace heroicons
const CalendarIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const CogIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const ChartBarIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const ArrowPathIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
);

const SparklesIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
  </svg>
);

const PaintBrushIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3V1m0 18v2" />
  </svg>
);

/**
 * Components showcase page
 * Trang tổng quan về tất cả components có sẵn
 */
const ComponentsPage = () => {
  // const { t } = useTranslation(); // Unused for now

  // Component categories
  const componentCategories = [
    {
      id: 'calendar',
      title: 'Calendar Components',
      description: 'Advanced calendar components with animations, themes, and time zone support',
      icon: CalendarIcon,
      color: 'blue',
      href: '/components/calendar-demo',
      components: [
        'Basic Calendar',
        'Multi-Select Calendar',
        'Event Calendar',
        'Animated Calendar',
        'Advanced Range Picker',
        'Time Zone Calendar',
        'Recurring Events Calendar',
        'Theme Customization'
      ],
      features: [
        'Responsive Design',
        'Touch Gestures',
        'Keyboard Navigation',
        'Accessibility Support',
        'Custom Themes',
        'Animations',
        'Time Zone Support',
        'Recurring Events'
      ]
    },
    {
      id: 'forms',
      title: 'Form Components',
      description: 'Form inputs, validation, and interactive form elements',
      icon: CogIcon,
      color: 'green',
      href: '/components/phone-input',
      components: [
        'Input Fields',
        'Phone Input with Country Selector',
        'Select Dropdowns',
        'Checkboxes & Radios',
        'File Upload',
        'Form Validation',
        'Multi-step Forms'
      ],
      features: [
        'International Phone Numbers',
        'Real-time Validation',
        'Custom Styling',
        'Error Handling',
        'Accessibility',
        'TypeScript Support'
      ]
    },
    {
      id: 'data-display',
      title: 'Data Display',
      description: 'Tables, charts, and data visualization components',
      icon: ChartBarIcon,
      color: 'purple',
      href: '#',
      components: [
        'Data Tables',
        'Charts & Graphs',
        'Statistics Cards',
        'Progress Indicators',
        'Badges & Tags'
      ],
      features: [
        'Sorting & Filtering',
        'Pagination',
        'Export Functions',
        'Interactive Charts',
        'Real-time Updates'
      ]
    },
    {
      id: 'navigation',
      title: 'Navigation',
      description: 'Navigation menus, breadcrumbs, and routing components',
      icon: ArrowPathIcon,
      color: 'orange',
      href: '#',
      components: [
        'Navigation Menus',
        'Breadcrumbs',
        'Tabs',
        'Pagination',
        'Sidebar Navigation'
      ],
      features: [
        'Responsive Design',
        'Active States',
        'Nested Navigation',
        'Mobile Optimized'
      ]
    },
    {
      id: 'feedback',
      title: 'Feedback',
      description: 'Notifications, alerts, and user feedback components',
      icon: SparklesIcon,
      color: 'red',
      href: '#',
      components: [
        'Toast Notifications',
        'Alert Messages',
        'Loading Spinners',
        'Progress Bars',
        'Confirmation Dialogs'
      ],
      features: [
        'Auto-dismiss',
        'Custom Positioning',
        'Animation Effects',
        'Accessibility'
      ]
    },
    {
      id: 'layout',
      title: 'Layout',
      description: 'Layout components for structuring your application',
      icon: PaintBrushIcon,
      color: 'indigo',
      href: '#',
      components: [
        'Grid Systems',
        'Containers',
        'Cards',
        'Modals',
        'Sidebars'
      ],
      features: [
        'Responsive Grid',
        'Flexible Layouts',
        'Custom Breakpoints',
        'CSS Grid Support'
      ]
    }
  ];

  // Color mapping
  const colorClasses: Record<string, string> = {
    blue: 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300',
    green: 'bg-green-50 border-green-200 text-green-700 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300',
    purple: 'bg-purple-50 border-purple-200 text-purple-700 dark:bg-purple-900/20 dark:border-purple-800 dark:text-purple-300',
    orange: 'bg-orange-50 border-orange-200 text-orange-700 dark:bg-orange-900/20 dark:border-orange-800 dark:text-orange-300',
    red: 'bg-red-50 border-red-200 text-red-700 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300',
    indigo: 'bg-indigo-50 border-indigo-200 text-indigo-700 dark:bg-indigo-900/20 dark:border-indigo-800 dark:text-indigo-300',
  };

  const iconColorClasses: Record<string, string> = {
    blue: 'text-blue-600 dark:text-blue-400',
    green: 'text-green-600 dark:text-green-400',
    purple: 'text-purple-600 dark:text-purple-400',
    orange: 'text-orange-600 dark:text-orange-400',
    red: 'text-red-600 dark:text-red-400',
    indigo: 'text-indigo-600 dark:text-indigo-400',
  };

  return (
    <div>
      {/* SEO Meta */}
      <title>Components Library - RedAI</title>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <a href="/" className="text-blue-600 hover:text-blue-700 mr-4">
                  ← Home
                </a>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Components Library
                </h1>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {componentCategories.length} Categories
              </div>
            </div>
          </div>
        </div>

        {/* Hero Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              RedAI Component Library
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Comprehensive collection of reusable React components built with TypeScript,
              Tailwind CSS, and modern best practices. Fully accessible, responsive, and customizable.
            </p>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">50+</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Components</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">100%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">TypeScript</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">WCAG</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Accessible</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2">Mobile</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">First</div>
            </div>
          </motion.div>

          {/* Component Categories */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {componentCategories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow"
              >
                {/* Header */}
                <div className={`p-6 border-b border-gray-200 dark:border-gray-700 ${colorClasses[category.color]}`}>
                  <div className="flex items-center mb-3">
                    <category.icon className={`h-8 w-8 ${iconColorClasses[category.color]} mr-3`} />
                    <h3 className="text-xl font-semibold">{category.title}</h3>
                  </div>
                  <p className="text-sm opacity-90">{category.description}</p>
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Components List */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                      Components ({category.components.length})
                    </h4>
                    <div className="space-y-2">
                      {category.components.slice(0, 4).map((component, idx) => (
                        <div key={idx} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></div>
                          {component}
                        </div>
                      ))}
                      {category.components.length > 4 && (
                        <div className="text-sm text-gray-500 dark:text-gray-500">
                          +{category.components.length - 4} more...
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                      Key Features
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {category.features.slice(0, 3).map((feature, idx) => (
                        <span
                          key={idx}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                        >
                          {feature}
                        </span>
                      ))}
                      {category.features.length > 3 && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          +{category.features.length - 3}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Action Button */}
                  <div>
                    {category.href === '#' ? (
                      <button
                        disabled
                        className="w-full px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-md cursor-not-allowed"
                      >
                        Coming Soon
                      </button>
                    ) : (
                      <a
                        href={category.href}
                        className="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                      >
                        View Components
                        <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </a>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Getting Started */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-16 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 border border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Getting Started
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              All components are built with modern React patterns and best practices.
              They're fully typed with TypeScript and designed to be accessible and responsive.
            </p>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📦</span>
                </div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Import</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Import components from the shared library
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎨</span>
                </div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Customize</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Use props and themes to customize appearance
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🚀</span>
                </div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Deploy</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Build and deploy with confidence
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ComponentsPage;
