
import { useTranslation } from 'react-i18next';
import { ResponsiveGrid } from '@/shared/components/common';
import { CampaignAnalyticsStatsGrid, type CampaignAnalyticsStats } from './CampaignAnalyticsStatsGrid';
import { CampaignAnalyticsRateCard } from './CampaignAnalyticsRateCard';

/**
 * Props cho CampaignAnalyticsPanel
 */
interface CampaignAnalyticsPanelProps {
  /**
   * Dữ liệu analytics
   */
  stats: CampaignAnalyticsStats;
  
  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
  
  /**
   * Namespace cho translation keys
   */
  translationNamespace?: string;
  
  /**
   * Hiển thị rate cards (open rate, click rate)
   */
  showRateCards?: boolean;
  
  /**
   * Số cột tối đa cho stats grid khi chatpanel đóng
   * @default { xs: 2, sm: 2, md: 4, lg: 4 }
   */
  statsMaxColumns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * <PERSON>ố cột tối đa cho stats grid khi chatpanel mở
   * @default { xs: 2, sm: 2, md: 2, lg: 4, xl: 4 }
   */
  statsMaxColumnsWithChatPanel?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Số cột tối đa cho rate cards khi chatpanel đóng
   * @default { xs: 1, sm: 1, md: 2, lg: 2 }
   */
  rateMaxColumns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Số cột tối đa cho rate cards khi chatpanel mở
   * @default { xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }
   */
  rateMaxColumnsWithChatPanel?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Class name bổ sung
   */
  className?: string;
}

/**
 * Component panel tổng hợp hiển thị analytics campaign theo quy tắc RedAI
 */
export function CampaignAnalyticsPanel({
  stats,
  isLoading = false,
  translationNamespace = 'marketing',
  showRateCards = true,
  statsMaxColumns = { xs: 2, sm: 2, md: 4, lg: 4 },
  statsMaxColumnsWithChatPanel = { xs: 2, sm: 2, md: 2, lg: 4, xl: 4 },
  rateMaxColumns = { xs: 1, sm: 1, md: 2, lg: 2 },
  rateMaxColumnsWithChatPanel = { xs: 1, sm: 1, md: 1, lg: 2, xl: 2 },
  className = '',
}: CampaignAnalyticsPanelProps) {
  const { t } = useTranslation([translationNamespace, 'common']);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Stats Grid */}
      <CampaignAnalyticsStatsGrid
        stats={stats}
        isLoading={isLoading}
        translationNamespace={translationNamespace}
        maxColumns={statsMaxColumns}
        maxColumnsWithChatPanel={statsMaxColumnsWithChatPanel}
      />

      {/* Rate Cards */}
      {showRateCards && (
        <ResponsiveGrid
          maxColumns={rateMaxColumns}
          maxColumnsWithChatPanel={rateMaxColumnsWithChatPanel}
        >
          <CampaignAnalyticsRateCard
            title={t(`${translationNamespace}:email.campaigns.analytics.openRate`, 'Tỷ lệ mở email')}
            total={stats.totalRecipients}
            count={stats.openedCount}
            color="green"
            isLoading={isLoading}
          />
          
          <CampaignAnalyticsRateCard
            title={t(`${translationNamespace}:email.campaigns.analytics.clickRate`, 'Tỷ lệ click')}
            total={stats.totalRecipients}
            count={stats.clickedCount}
            color="blue"
            isLoading={isLoading}
          />
        </ResponsiveGrid>
      )}
    </div>
  );
}

export default CampaignAnalyticsPanel;
