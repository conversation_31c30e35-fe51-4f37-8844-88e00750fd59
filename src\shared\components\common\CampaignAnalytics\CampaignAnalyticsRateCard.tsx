
import { Card, Typography } from '@/shared/components/common';

/**
 * Props cho CampaignAnalyticsRateCard
 */
interface CampaignAnalyticsRateCardProps {
  /**
   * Tiêu đề của card
   */
  title: string;
  
  /**
   * Tổng số để tính tỷ lệ
   */
  total: number;
  
  /**
   * Số lượng thành công
   */
  count: number;
  
  /**
   * <PERSON>àu của progress bar và text
   */
  color?: 'green' | 'blue' | 'purple' | 'orange' | 'red';
  
  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
  
  /**
   * Class name bổ sung
   */
  className?: string;
}

/**
 * Component card hiển thị tỷ lệ với progress bar theo quy tắc RedAI
 */
export function CampaignAnalyticsRateCard({
  title,
  total,
  count,
  color = 'green',
  isLoading = false,
  className = '',
}: CampaignAnalyticsRateCardProps) {
  const rate = total > 0 ? (count / total) * 100 : 0;
  const displayRate = rate.toFixed(1);

  const colorClasses = {
    green: {
      text: 'text-green-600',
      bar: 'bg-green-500',
    },
    blue: {
      text: 'text-blue-600',
      bar: 'bg-blue-500',
    },
    purple: {
      text: 'text-purple-600',
      bar: 'bg-purple-500',
    },
    orange: {
      text: 'text-orange-600',
      bar: 'bg-orange-500',
    },
    red: {
      text: 'text-red-600',
      bar: 'bg-red-500',
    },
  };

  const colorClass = colorClasses[color];

  if (isLoading) {
    return (
      <Card className={`p-4 animate-pulse ${className}`}>
        <div className="h-5 bg-gray-200 rounded mb-2"></div>
        <div className="flex items-center justify-between">
          <div className="h-8 w-16 bg-gray-200 rounded"></div>
          <div className="w-24 bg-gray-200 rounded-full h-2"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-4 ${className}`}>
      <Typography variant="subtitle2" className="mb-2">
        {title}
      </Typography>
      <div className="flex items-center justify-between">
        <Typography variant="h3" className={colorClass.text}>
          {displayRate}%
        </Typography>
        <div className="w-24 bg-gray-200 rounded-full h-2">
          <div
            className={`${colorClass.bar} h-2 rounded-full transition-all duration-300`}
            style={{
              width: `${Math.min(rate, 100)}%`,
            }}
          />
        </div>
      </div>
    </Card>
  );
}

export default CampaignAnalyticsRateCard;
