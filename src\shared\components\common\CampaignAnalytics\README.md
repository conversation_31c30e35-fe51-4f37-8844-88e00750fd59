# Campaign Analytics Components

Bộ component hiển thị analytics cho campaign theo quy tắc RedAI.

## Components

### CampaignAnalyticsStatsGrid

Component grid hiển thị thống kê cơ bản của campaign (tổng ng<PERSON><PERSON><PERSON>, đ<PERSON>, đ<PERSON> mở, đ<PERSON> click).

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| stats | CampaignAnalyticsStats | - | Dữ liệu analytics |
| isLoading | boolean | false | Trạng thái loading |
| translationNamespace | string | 'marketing' | Namespace cho translation keys |
| maxColumns | object | { xs: 2, sm: 2, md: 4, lg: 4 } | Số cột tối đa khi chatpanel đóng |
| maxColumnsWithChatPanel | object | { xs: 2, sm: 2, md: 2, lg: 4, xl: 4 } | <PERSON><PERSON> cột tối đa khi chatpanel mở |
| className | string | '' | Class name bổ sung |

#### Usage

```tsx
import { CampaignAnalyticsStatsGrid } from '@/shared/components/common';

const stats = {
  totalRecipients: 1000,
  deliveredCount: 950,
  openedCount: 380,
  clickedCount: 76,
};

<CampaignAnalyticsStatsGrid
  stats={stats}
  translationNamespace="marketing"
/>
```

### CampaignAnalyticsRateCard

Component card hiển thị tỷ lệ với progress bar.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| title | string | - | Tiêu đề của card |
| total | number | - | Tổng số để tính tỷ lệ |
| count | number | - | Số lượng thành công |
| color | 'green' \| 'blue' \| 'purple' \| 'orange' \| 'red' | 'green' | Màu của progress bar và text |
| isLoading | boolean | false | Trạng thái loading |
| className | string | '' | Class name bổ sung |

#### Usage

```tsx
import { CampaignAnalyticsRateCard } from '@/shared/components/common';

<CampaignAnalyticsRateCard
  title="Tỷ lệ mở email"
  total={1000}
  count={380}
  color="green"
/>
```

### CampaignAnalyticsPanel

Component panel tổng hợp hiển thị cả stats grid và rate cards.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| stats | CampaignAnalyticsStats | - | Dữ liệu analytics |
| isLoading | boolean | false | Trạng thái loading |
| translationNamespace | string | 'marketing' | Namespace cho translation keys |
| showRateCards | boolean | true | Hiển thị rate cards |
| statsMaxColumns | object | { xs: 2, sm: 2, md: 4, lg: 4 } | Số cột tối đa cho stats grid khi chatpanel đóng |
| statsMaxColumnsWithChatPanel | object | { xs: 2, sm: 2, md: 2, lg: 4, xl: 4 } | Số cột tối đa cho stats grid khi chatpanel mở |
| rateMaxColumns | object | { xs: 1, sm: 1, md: 2, lg: 2 } | Số cột tối đa cho rate cards khi chatpanel đóng |
| rateMaxColumnsWithChatPanel | object | { xs: 1, sm: 1, md: 1, lg: 2, xl: 2 } | Số cột tối đa cho rate cards khi chatpanel mở |
| className | string | '' | Class name bổ sung |

#### Usage

```tsx
import { CampaignAnalyticsPanel } from '@/shared/components/common';

const stats = {
  totalRecipients: 1000,
  deliveredCount: 950,
  openedCount: 380,
  clickedCount: 76,
};

<CampaignAnalyticsPanel
  stats={stats}
  translationNamespace="marketing"
  showRateCards={true}
/>
```

## Types

### CampaignAnalyticsStats

```typescript
interface CampaignAnalyticsStats {
  totalRecipients: number;
  deliveredCount: number;
  openedCount: number;
  clickedCount: number;
}
```

## Features

- ✅ Responsive design với ResponsiveGrid
- ✅ Hỗ trợ loading state
- ✅ Tùy chỉnh số cột cho từng breakpoint
- ✅ Hỗ trợ chatpanel responsive
- ✅ Internationalization (i18n)
- ✅ TypeScript support
- ✅ Theo quy tắc RedAI frontend

## Sử dụng trong các module khác

Components này có thể được sử dụng cho các loại campaign khác như SMS, Push Notification, v.v. bằng cách truyền đúng `translationNamespace` và dữ liệu `stats` tương ứng.
