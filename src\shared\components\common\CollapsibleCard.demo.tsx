import React from 'react';
import { Typography, Select, Input, FormItem } from './index';
import CollapsibleCard from './CollapsibleCard';

/**
 * Demo component để test CollapsibleCard với dropdown overflow
 */
const CollapsibleCardDemo: React.FC = () => {
  const priceTypeOptions = [
    { value: 'HAS_PRICE', label: 'Có giá' },
    { value: 'STRING_PRICE', label: 'Giá dạng text' },
    { value: 'NO_PRICE', label: 'Không có giá' },
  ];

  const currencyOptions = [
    { value: 'VND', label: 'VND - Việt Nam Đồng' },
    { value: 'USD', label: 'USD - US Dollar' },
    { value: 'EUR', label: 'EUR - Euro' },
    { value: 'JPY', label: 'JPY - Japanese Yen' },
    { value: 'GBP', label: 'GBP - British Pound' },
  ];

  return (
    <div className="w-full bg-background text-foreground p-6 space-y-6">
      <Typography variant="h4">CollapsibleCard Dropdown Overflow Test</Typography>
      
      {/* Test case 1: CollapsibleCard với allowOverflow=true (default) */}
      <CollapsibleCard
        title={
          <Typography variant="h6" className="font-medium">
            1. Giá sản phẩm (allowOverflow=true - default)
          </Typography>
        }
        defaultOpen={true}
        className="mb-4"
      >
        <div className="space-y-4">
          <FormItem label="Loại giá" required>
            <Select
              fullWidth
              placeholder="Chọn loại giá"
              options={priceTypeOptions}
            />
          </FormItem>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label="Giá niêm yết" required>
              <Input fullWidth type="number" min="0" placeholder="Nhập giá niêm yết" />
            </FormItem>
            <FormItem label="Giá bán" required>
              <Input fullWidth type="number" min="0" placeholder="Nhập giá bán" />
            </FormItem>
          </div>

          <FormItem label="Đơn vị tiền tệ" required>
            <Select
              fullWidth
              placeholder="Chọn đơn vị tiền tệ"
              options={currencyOptions}
              searchable
            />
          </FormItem>
        </div>
      </CollapsibleCard>

      {/* Test case 2: CollapsibleCard với allowOverflow=false */}
      <CollapsibleCard
        title={
          <Typography variant="h6" className="font-medium">
            2. Thông tin khác (allowOverflow=false)
          </Typography>
        }
        defaultOpen={true}
        allowOverflow={false}
        className="mb-4"
      >
        <div className="space-y-4">
          <FormItem label="Loại giá (sẽ bị cắt dropdown)" required>
            <Select
              fullWidth
              placeholder="Chọn loại giá"
              options={priceTypeOptions}
            />
          </FormItem>

          <FormItem label="Đơn vị tiền tệ (sẽ bị cắt dropdown)" required>
            <Select
              fullWidth
              placeholder="Chọn đơn vị tiền tệ"
              options={currencyOptions}
              searchable
            />
          </FormItem>
        </div>
      </CollapsibleCard>

      {/* Test case 3: Multiple CollapsibleCards để test z-index */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              3a. Card bên trái
            </Typography>
          }
          defaultOpen={true}
        >
          <div className="space-y-4">
            <FormItem label="Select 1">
              <Select
                fullWidth
                placeholder="Chọn option"
                options={priceTypeOptions}
              />
            </FormItem>
            <FormItem label="Select 2">
              <Select
                fullWidth
                placeholder="Chọn currency"
                options={currencyOptions}
                searchable
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              3b. Card bên phải
            </Typography>
          }
          defaultOpen={true}
        >
          <div className="space-y-4">
            <FormItem label="Select 3">
              <Select
                fullWidth
                placeholder="Chọn option"
                options={priceTypeOptions}
              />
            </FormItem>
            <FormItem label="Select 4">
              <Select
                fullWidth
                placeholder="Chọn currency"
                options={currencyOptions}
                searchable
              />
            </FormItem>
          </div>
        </CollapsibleCard>
      </div>

      <div className="mt-8 p-4 bg-card rounded-lg">
        <Typography variant="h6" className="mb-2">Hướng dẫn test:</Typography>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>Mở dropdown trong card 1 - dropdown phải hiển thị đầy đủ, không bị cắt</li>
          <li>Mở dropdown trong card 2 - dropdown sẽ bị cắt do allowOverflow=false</li>
          <li>Mở dropdown trong card 3a và 3b - dropdown phải hiển thị trên các card khác</li>
          <li>Test với searchable dropdown để kiểm tra tính năng tìm kiếm</li>
        </ul>
      </div>
    </div>
  );
};

export default CollapsibleCardDemo;
