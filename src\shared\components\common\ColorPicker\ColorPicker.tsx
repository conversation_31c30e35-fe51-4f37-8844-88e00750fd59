import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { IconCard, Button, Input } from '@/shared/components/common';

interface ColorPickerProps {
  value?: string;
  onChange?: (color: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showInput?: boolean; // Tùy chọn hiển thị input text
}

// Màu preset phổ biến
const PRESET_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
  '#A3E4D7', '#F9E79F', '#FADBD8', '#D5DBDB', '#AED6F1'
];

// Hàm generate màu ngẫu nhiên
const generateRandomColor = (): string => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

/**
 * Component ColorPicker với preset colors và random color generator
 */
const ColorPicker: React.FC<ColorPickerProps> = ({
  value = '#FF0000',
  onChange,
  placeholder = 'Choose color',
  disabled = false,
  className = '',
  showInput = false, // Mặc định không hiển thị input
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);

  // Xử lý thay đổi màu
  const handleColorChange = useCallback((color: string) => {
    setInputValue(color);
    onChange?.(color);
    setIsOpen(false);
  }, [onChange]);

  // Xử lý input thay đổi
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // Validate hex color format
    if (/^#[0-9A-Fa-f]{6}$/.test(newValue)) {
      onChange?.(newValue);
    }
  }, [onChange]);

  // Generate màu ngẫu nhiên
  const handleRandomColor = useCallback(() => {
    const randomColor = generateRandomColor();
    handleColorChange(randomColor);
  }, [handleColorChange]);

  return (
    <div className={`relative ${className}`}>
      {showInput ? (
        /* Input với color preview */
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Input
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              placeholder={placeholder}
              disabled={disabled}
              className="pr-12"
            />
            {/* Color preview */}
            <div
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-6 rounded border border-border cursor-pointer"
              style={{ backgroundColor: inputValue }}
              onClick={() => !disabled && setIsOpen(!isOpen)}
            />
          </div>

          {/* Random color button */}
          <IconCard
            icon="refresh-cw"
            size="sm"
            variant="ghost"
            onClick={handleRandomColor}
            disabled={disabled}
            title={t('marketing:tags.form.randomColor')}
          />
        </div>
      ) : (
        /* Chỉ hiển thị color preview button */
        <div className="flex items-center space-x-2">
          <div
            className="w-10 h-10 rounded-lg border-2 border-border cursor-pointer hover:scale-105 transition-transform shadow-sm"
            style={{ backgroundColor: inputValue }}
            onClick={() => !disabled && setIsOpen(!isOpen)}
            title={`${placeholder}: ${inputValue}`}
          />
        </div>
      )}

      {/* Color picker dropdown */}
      {isOpen && !disabled && (
        <div className="absolute top-full left-0 right-0 mt-1 p-4 bg-card border border-border rounded-lg shadow-lg z-50">
          {/* Preset colors grid */}
          <div className="grid grid-cols-10 gap-2 mb-4">
            {PRESET_COLORS.map((color) => (
              <button
                key={color}
                className={`w-8 h-8 rounded border-2 hover:scale-110 transition-transform ${
                  inputValue === color ? 'border-primary' : 'border-border'
                }`}
                style={{ backgroundColor: color }}
                onClick={() => handleColorChange(color)}
                title={color}
              />
            ))}
          </div>

          {/* Native color picker */}
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={inputValue}
              onChange={(e) => handleColorChange(e.target.value)}
              className="w-12 h-8 rounded border border-border cursor-pointer"
            />
            <span className="text-sm text-muted-foreground">
              hoặc sử dụng color picker
            </span>
          </div>

          {/* Actions */}
          <div className="flex justify-between items-center mt-4 pt-3 border-t border-border">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRandomColor}
              className="text-xs"
            >
              {t('marketing:tags.form.randomColor')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="text-xs"
            >
              {t('common:close', 'Đóng')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ColorPicker;
