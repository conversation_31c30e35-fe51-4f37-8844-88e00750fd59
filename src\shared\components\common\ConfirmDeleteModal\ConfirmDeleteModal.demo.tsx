import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, Card, Typography } from '@/shared/components/common';
import ConfirmDeleteModal from './ConfirmDeleteModal';

/**
 * Demo component để test ConfirmDeleteModal với đa ngôn ngữ
 */
const ConfirmDeleteModalDemo: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [showBasicModal, setShowBasicModal] = useState(false);
  const [showWithNameModal, setShowWithNameModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [showCustomModal, setShowCustomModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = async () => {
    setIsSubmitting(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsSubmitting(false);
    // Close all modals
    setShowBasicModal(false);
    setShowWithNameModal(false);
    setShowBulkModal(false);
    setShowCustomModal(false);
  };

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <Card className="max-w-4xl mx-auto">
        <div className="p-6">
          <Typography variant="h2" className="mb-6">
            ConfirmDeleteModal Demo
          </Typography>

          {/* Language Switcher */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="h4" className="mb-3">
              Language / Ngôn ngữ / 语言
            </Typography>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => changeLanguage('vi')}
                className={i18n.language === 'vi' ? 'bg-primary text-primary-foreground' : ''}
              >
                Tiếng Việt
              </Button>
              <Button
                variant="outline"
                onClick={() => changeLanguage('en')}
                className={i18n.language === 'en' ? 'bg-primary text-primary-foreground' : ''}
              >
                English
              </Button>
              <Button
                variant="outline"
                onClick={() => changeLanguage('zh')}
                className={i18n.language === 'zh' ? 'bg-primary text-primary-foreground' : ''}
              >
                中文
              </Button>
            </div>
          </div>

          {/* Demo Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <Typography variant="h4">Basic Examples</Typography>
              
              <Button
                variant="danger"
                onClick={() => setShowBasicModal(true)}
                className="w-full"
              >
                Basic Delete Modal
              </Button>

              <Button
                variant="danger"
                onClick={() => setShowWithNameModal(true)}
                className="w-full"
              >
                Delete with Item Name
              </Button>

              <Button
                variant="danger"
                onClick={() => setShowBulkModal(true)}
                className="w-full"
              >
                Bulk Delete (5 items)
              </Button>
            </div>

            <div className="space-y-4">
              <Typography variant="h4">Custom Examples</Typography>
              
              <Button
                variant="warning"
                onClick={() => setShowCustomModal(true)}
                className="w-full"
              >
                Custom Title & Message
              </Button>
            </div>
          </div>

          {/* Current Language Display */}
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <Typography variant="body2">
              Current Language: <strong>{i18n.language}</strong>
            </Typography>
            <Typography variant="body2">
              Sample translations:
            </Typography>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>confirmDeleteTitle: {t('common:confirmDeleteTitle', 'Xác nhận xóa')}</li>
              <li>confirmDeleteMessage: {t('common:confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa mục này?')}</li>
              <li>cancel: {t('common:cancel', 'Hủy')}</li>
              <li>delete: {t('common:delete', 'Xóa')}</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Basic Modal */}
      <ConfirmDeleteModal
        isOpen={showBasicModal}
        onClose={() => setShowBasicModal(false)}
        onConfirm={handleConfirm}
        isSubmitting={isSubmitting}
      />

      {/* Modal with Item Name */}
      <ConfirmDeleteModal
        isOpen={showWithNameModal}
        onClose={() => setShowWithNameModal(false)}
        onConfirm={handleConfirm}
        itemName="Sản phẩm ABC / Product ABC / 产品ABC"
        isSubmitting={isSubmitting}
      />

      {/* Bulk Delete Modal */}
      <ConfirmDeleteModal
        isOpen={showBulkModal}
        onClose={() => setShowBulkModal(false)}
        onConfirm={handleConfirm}
        itemCount={5}
        isSubmitting={isSubmitting}
      />

      {/* Custom Modal */}
      <ConfirmDeleteModal
        isOpen={showCustomModal}
        onClose={() => setShowCustomModal(false)}
        onConfirm={handleConfirm}
        title="Custom Title"
        message="This is a custom message that overrides the default translation."
        confirmButtonText="Remove"
        confirmButtonVariant="warning"
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default ConfirmDeleteModalDemo;
