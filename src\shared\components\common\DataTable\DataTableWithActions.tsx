import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, Card, ActionMenu, ConfirmDeleteModal } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useTableActions, UseTableActionsOptions } from '@/shared/hooks/table/useTableActions';
import { useDataTable, UseDataTableOptions } from '@/shared/hooks/table/useDataTable';
import { useActiveFilters } from '@/shared/hooks/filters';

export interface DataTableWithActionsProps<T, TQueryParams = Record<string, unknown>> {
  /**
   * Dữ liệu hiển thị
   */
  data: T[];

  /**
   * Trạng thái loading
   */
  loading?: boolean;

  /**
   * Cấu hình columns
   */
  columns: TableColumn<T>[];

  /**
   * Cấu hình data table
   */
  dataTableConfig: UseDataTableOptions<T, TQueryParams>;

  /**
   * Cấu hình actions
   */
  actionsConfig: UseTableActionsOptions<T>;

  /**
   * Cấu hình pagination
   */
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize?: number) => void;
  };

  /**
   * Cấu hình column visibility
   */
  columnVisibility?: {
    columns: ColumnVisibility[];
    onChange: (columns: ColumnVisibility[]) => void;
  };

  /**
   * Additional icons cho MenuIconBar
   */
  additionalIcons?: Array<{
    icon: string;
    tooltip: string;
    variant?: 'default' | 'primary' | 'secondary' | 'ghost';
    onClick: () => void;
    className?: string;
    condition?: boolean;
  }>;

  /**
   * Callback khi thêm mới
   */
  onAdd?: () => void;

  /**
   * Hiển thị date filter
   */
  showDateFilter?: boolean;

  /**
   * Hiển thị column filter
   */
  showColumnFilter?: boolean;

  /**
   * Row key
   */
  rowKey?: string;

  /**
   * Namespace cho i18n
   */
  i18nNamespace?: string;
}

/**
 * Component DataTable với đầy đủ actions (CRUD + bulk operations)
 */
function DataTableWithActions<T extends { id: string; [key: string]: unknown }, TQueryParams = Record<string, unknown>>({
  data,
  loading = false,
  columns: baseColumns,
  dataTableConfig,
  actionsConfig,
  pagination,
  columnVisibility,
  additionalIcons = [],
  onAdd,
  showDateFilter = false,
  showColumnFilter = true,
  rowKey = 'id',
  i18nNamespace = 'common',
}: DataTableWithActionsProps<T, TQueryParams>) {
  const { t } = useTranslation([i18nNamespace, 'common']);

  // Table actions hook
  const tableActions = useTableActions<T>(actionsConfig);

  // Data table hook
  const dataTable = useDataTable(dataTableConfig);

  // Active filters hook
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {},
      t,
    });

  // Enhanced columns with actions
  const columns = useMemo(() => {
    const actionsColumn: TableColumn<T> = {
      key: 'actions',
      title: '',
      width: 80,
      render: (_, record: T) => (
        <ActionMenu
          items={[
            ...(actionsConfig.onView ? [{
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => tableActions.handleView(record),
            }] : []),
            ...(actionsConfig.onEdit ? [{
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => tableActions.handleEdit(record),
            }] : []),
            ...(actionsConfig.singleDelete ? [{
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => tableActions.singleDelete.handleShowDeleteConfirm(record),
            }] : []),
          ]}
          menuTooltip={t('common:moreActions', 'Thêm hành động')}
          iconSize="sm"
          iconVariant="default"
          placement="bottom"
          menuWidth="180px"
          menuIcon="more-horizontal"
          showAllInMenu={true}
          preferRight={true}
        />
      ),
    };

    return [...baseColumns, actionsColumn];
  }, [baseColumns, actionsConfig, tableActions, t]);

  // Enhanced additional icons with bulk delete
  const enhancedAdditionalIcons = useMemo(() => {
    const bulkDeleteIcon = actionsConfig.bulkDelete ? {
      icon: 'trash',
      tooltip: t('common:bulkDelete', 'Xóa nhiều'),
      variant: 'primary' as const,
      onClick: () => tableActions.bulkDelete.handleShowBulkDeleteConfirm(data.length > 0),
      className: 'text-red-500',
      condition: tableActions.selectedRows.length > 0 && data.length > 0,
    } : null;

    return [
      ...additionalIcons,
      ...(bulkDeleteIcon ? [bulkDeleteIcon] : []),
    ];
  }, [additionalIcons, actionsConfig.bulkDelete, tableActions, data.length, t]);

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-4">
        <div>
          {/* MenuIconBar */}
          <MenuIconBar
            onSearch={dataTable.tableData.handleSearch}
            onAdd={onAdd}
            items={dataTable.filter.menuItems}
            onColumnVisibilityChange={columnVisibility?.onChange}
            columns={columnVisibility?.columns || []}
            showDateFilter={showDateFilter}
            showColumnFilter={showColumnFilter}
            additionalIcons={enhancedAdditionalIcons}
          />

          {/* ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            filterValue={dataTable.filter.selectedValue}
            filterLabel={getFilterLabel()}
            onClearFilter={handleClearFilter}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        <Card className="overflow-hidden">
          <Table
            columns={columns}
            data={data}
            rowKey={rowKey}
            loading={loading}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={pagination}
            rowSelection={{
              selectedRowKeys: tableActions.selectedRows,
              onChange: keys => tableActions.setSelectedRows(keys as string[]),
            }}
          />
        </Card>
      </div>

      {/* Single Delete Modal */}
      {actionsConfig.singleDelete && (
        <ConfirmDeleteModal
          isOpen={tableActions.singleDelete.showDeleteConfirm}
          onClose={tableActions.singleDelete.handleCancelDelete}
          onConfirm={tableActions.singleDelete.handleConfirmDelete}
          title={t('common:confirmDelete', 'Xác nhận xóa')}
          message={tableActions.singleDelete.getConfirmMessage()}
          itemName={
            (tableActions.singleDelete.itemToDelete as Record<string, unknown>)?.['title'] as string ||
            (tableActions.singleDelete.itemToDelete as Record<string, unknown>)?.['name'] as string
          }
        />
      )}

      {/* Bulk Delete Modal */}
      {actionsConfig.bulkDelete && tableActions.bulkDelete.bulkDeleteCount > 0 && (
        <ConfirmDeleteModal
          isOpen={tableActions.bulkDelete.showBulkDeleteConfirm}
          onClose={tableActions.bulkDelete.handleCancelBulkDelete}
          onConfirm={tableActions.bulkDelete.handleConfirmBulkDelete}
          title={t('common:confirmDelete', 'Xác nhận xóa')}
          message={tableActions.bulkDelete.getConfirmMessage()}
        />
      )}
    </div>
  );
}

export default DataTableWithActions;
