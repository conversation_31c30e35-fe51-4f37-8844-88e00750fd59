import React, { useState, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { RangePickerProps } from './types';
import Calendar from './Calendar';
import PresetRanges, { PresetRange } from './PresetRanges';
import { useCalendarResponsive } from './hooks';
// import { useCalendarTheme } from './hooks'; // Unused for now
import { formatDate, getDaysBetween } from './utils';

/**
 * Props cho AdvancedRangePicker component
 */
export interface AdvancedRangePickerProps extends Omit<RangePickerProps, 'value' | 'onChange'> {
  /**
   * Giá trị range đã chọn
   */
  value?: [Date | null, Date | null];
  
  /**
   * Callback khi range thay đổi
   */
  onChange?: (range: [Date | null, Date | null]) => void;
  
  /**
   * Hiển thị preset ranges
   */
  showPresets?: boolean;
  
  /**
   * Custom preset ranges
   */
  customPresets?: PresetRange[];
  
  /**
   * Hiển thị hai calendar cạnh nhau
   */
  showTwoCalendars?: boolean;
  
  /**
   * Hiển thị số ngày đã chọn
   */
  showDaysCount?: boolean;
  
  /**
   * Hiển thị quick actions
   */
  showQuickActions?: boolean;
  
  /**
   * Enable animations
   */
  enableAnimations?: boolean;
  
  /**
   * Layout direction
   */
  layout?: 'vertical' | 'horizontal' | 'auto';
  
  /**
   * Compact mode
   */
  compact?: boolean;
  
  /**
   * Custom footer content
   */
  footer?: React.ReactNode;
  
  /**
   * Callback khi apply range
   */
  onApply?: (range: [Date | null, Date | null]) => void;
  
  /**
   * Callback khi cancel
   */
  onCancel?: () => void;
  
  /**
   * Show apply/cancel buttons
   */
  showActionButtons?: boolean;
}

/**
 * AdvancedRangePicker component
 */
const AdvancedRangePicker: React.FC<AdvancedRangePickerProps> = ({
  value = [null, null],
  onChange,
  showPresets = true,
  customPresets = [],
  showTwoCalendars = false,
  showDaysCount = true,
  showQuickActions = true,
  enableAnimations = true,
  layout = 'auto',
  compact = false,
  footer,
  onApply,
  onCancel,
  showActionButtons = false,
  disabledDates,
  minDate,
  maxDate,
  // maxRange, // Unused for now
  firstDayOfWeek = 1,
  weekDayNames,
  monthNames,
  className = '',
  ...calendarProps
}) => {
  const { t } = useTranslation();
  const responsive = useCalendarResponsive();
  // const { theme } = useCalendarTheme(); // Unused for now

  // Internal state for temporary range selection
  const [tempRange, setTempRange] = useState<[Date | null, Date | null]>(value);
  const [leftMonth, setLeftMonth] = useState(() => value[0] || new Date());
  const [rightMonth, setRightMonth] = useState(() => {
    const baseMonth = value[1] || value[0] || new Date();
    return new Date(baseMonth.getFullYear(), baseMonth.getMonth() + 1, 1);
  });

  // Determine layout based on responsive design
  const finalLayout = useMemo(() => {
    if (layout === 'auto') {
      return responsive.isMobile ? 'vertical' : 'horizontal';
    }
    return layout;
  }, [layout, responsive.isMobile]);

  // Handle range selection
  const handleRangeSelect = useCallback((start: Date | null, end: Date | null) => {
    const newRange: [Date | null, Date | null] = [start, end];
    setTempRange(newRange);
    
    if (!showActionButtons) {
      onChange?.(newRange);
    }
  }, [onChange, showActionButtons]);

  // Handle preset selection
  const handlePresetSelect = useCallback((range: [Date, Date]) => {
    const newRange: [Date | null, Date | null] = range;
    setTempRange(newRange);
    
    // Update calendar months to show the selected range
    setLeftMonth(range[0]);
    if (showTwoCalendars) {
      const nextMonth = new Date(range[0].getFullYear(), range[0].getMonth() + 1, 1);
      setRightMonth(nextMonth);
    }
    
    if (!showActionButtons) {
      onChange?.(newRange);
    }
  }, [onChange, showActionButtons, showTwoCalendars]);

  // Handle apply
  const handleApply = useCallback(() => {
    onChange?.(tempRange);
    onApply?.(tempRange);
  }, [tempRange, onChange, onApply]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    setTempRange(value);
    onCancel?.();
  }, [value, onCancel]);

  // Handle clear
  const handleClear = useCallback(() => {
    const clearedRange: [Date | null, Date | null] = [null, null];
    setTempRange(clearedRange);
    
    if (!showActionButtons) {
      onChange?.(clearedRange);
    }
  }, [onChange, showActionButtons]);

  // Calculate days count
  const daysCount = useMemo(() => {
    if (!tempRange[0] || !tempRange[1]) return 0;
    return getDaysBetween(tempRange[0], tempRange[1]);
  }, [tempRange]);

  // Format range display
  const formatRangeDisplay = useCallback(() => {
    if (!tempRange[0] && !tempRange[1]) {
      return t('dateRange.selectRange', 'Select date range');
    }
    
    if (tempRange[0] && !tempRange[1]) {
      return formatDate(tempRange[0]);
    }
    
    if (tempRange[0] && tempRange[1]) {
      return `${formatDate(tempRange[0])} - ${formatDate(tempRange[1])}`;
    }
    
    return '';
  }, [tempRange, t]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.2, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0, 
      scale: 0.95,
      transition: { duration: 0.15, ease: 'easeIn' }
    },
  };

  // Container classes
  const containerClasses = `
    bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700
    ${compact ? 'p-3' : 'p-4'}
    ${className}
  `.trim();

  // Layout classes
  const layoutClasses = finalLayout === 'horizontal' 
    ? 'flex gap-4' 
    : 'space-y-4';

  const ContainerComponent = enableAnimations ? motion.div : 'div';
  const containerProps = enableAnimations ? {
    variants: containerVariants,
    initial: "hidden",
    animate: "visible",
    exit: "exit",
  } : {};

  return (
    <ContainerComponent
      className={containerClasses}
      {...containerProps}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('dateRange.selectRange', 'Select Date Range')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {formatRangeDisplay()}
          </p>
        </div>
        
        {showDaysCount && daysCount > 0 && (
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {daysCount} {t('dateRange.days', 'days')}
            </div>
          </div>
        )}
      </div>

      {/* Main content */}
      <div className={layoutClasses}>
        {/* Preset ranges */}
        {showPresets && (
          <div className={finalLayout === 'horizontal' ? 'w-64 flex-shrink-0' : ''}>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              {t('dateRange.quickSelect', 'Quick Select')}
            </h4>
            <PresetRanges
              onSelectRange={handlePresetSelect}
              selectedRange={tempRange}
              customPresets={customPresets}
              compact={compact}
              enableAnimations={enableAnimations}
            />
          </div>
        )}

        {/* Calendar(s) */}
        <div className="flex-1">
          {showTwoCalendars && !responsive.isMobile ? (
            <div className="flex gap-4">
              <Calendar
                month={leftMonth}
                onMonthChange={setLeftMonth}
                rangeMode
                startDate={tempRange[0]}
                endDate={tempRange[1]}
                onRangeSelect={handleRangeSelect}
                disabledDates={disabledDates}
                minDate={minDate}
                maxDate={maxDate}
                firstDayOfWeek={firstDayOfWeek}
                weekDayNames={weekDayNames}
                monthNames={monthNames}
                {...calendarProps}
              />
              <Calendar
                month={rightMonth}
                onMonthChange={setRightMonth}
                rangeMode
                startDate={tempRange[0]}
                endDate={tempRange[1]}
                onRangeSelect={handleRangeSelect}
                disabledDates={disabledDates}
                minDate={minDate}
                maxDate={maxDate}
                firstDayOfWeek={firstDayOfWeek}
                weekDayNames={weekDayNames}
                monthNames={monthNames}
                {...calendarProps}
              />
            </div>
          ) : (
            <Calendar
              month={leftMonth}
              onMonthChange={setLeftMonth}
              rangeMode
              startDate={tempRange[0]}
              endDate={tempRange[1]}
              onRangeSelect={handleRangeSelect}
              disabledDates={disabledDates}
              minDate={minDate}
              maxDate={maxDate}
              firstDayOfWeek={firstDayOfWeek}
              weekDayNames={weekDayNames}
              monthNames={monthNames}
              {...calendarProps}
            />
          )}
        </div>
      </div>

      {/* Quick actions */}
      {showQuickActions && (
        <div className="flex items-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleClear}
            className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
          >
            {t('dateRange.clear', 'Clear')}
          </button>
        </div>
      )}

      {/* Custom footer */}
      {footer && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          {footer}
        </div>
      )}

      {/* Action buttons */}
      {showActionButtons && (
        <div className="flex items-center justify-end gap-3 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            {t('common.cancel', 'Cancel')}
          </button>
          <button
            type="button"
            onClick={handleApply}
            disabled={!tempRange[0] || !tempRange[1]}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {t('common.apply', 'Apply')}
          </button>
        </div>
      )}
    </ContainerComponent>
  );
};

export default AdvancedRangePicker;
