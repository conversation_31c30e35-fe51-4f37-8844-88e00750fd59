import { useState, useCallback, useMemo, useEffect } from 'react';
import { addDays, addMonths, subMonths } from 'date-fns';
import { getCalendarDays, isSameDayFn } from '../utils';

/**
 * Hook tối ưu hóa cho Calendar component
 * Quản lý state và logic chính của calendar
 */
export interface UseCalendarOptions {
  initialDate?: Date | undefined;
  selectedDate?: Date | null | undefined;
  rangeMode?: boolean;
  startDate?: Date | null | undefined;
  endDate?: Date | null | undefined;
  firstDayOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  onSelectDate?: ((date: Date) => void) | undefined;
  onRangeSelect?: ((start: Date | null, end: Date | null) => void) | undefined;
  onMonthChange?: ((date: Date) => void) | undefined;
}

export interface UseCalendarReturn {
  // State
  month: Date;
  focusedDate: Date | null;
  rangeState: 'start' | 'end';
  
  // Computed values (memoized)
  calendarDays: Date[];
  
  // Actions
  setMonth: (date: Date) => void;
  setFocusedDate: (date: Date | null) => void;
  handleSelectDate: (date: Date) => void;
  handleMonthChange: (date: Date) => void;
  handleTodayClick: () => void;
  handleKeyboardNavigation: (key: string, currentFocused: Date) => Date | null;
  
  // Navigation helpers
  goToPreviousMonth: () => void;
  goToNextMonth: () => void;
  goToToday: () => void;
}

export const useCalendar = (options: UseCalendarOptions = {}): UseCalendarReturn => {
  const {
    initialDate,
    selectedDate,
    rangeMode = false,
    startDate,
    endDate,
    firstDayOfWeek = 1,
    onSelectDate,
    onRangeSelect,
    onMonthChange,
  } = options;

  // State để theo dõi tháng hiển thị
  const [month, setMonthState] = useState(() => {
    if (initialDate) return initialDate;
    if (selectedDate) return selectedDate;
    if (rangeMode && startDate) return startDate;
    return new Date();
  });

  // State để theo dõi trạng thái chọn range
  const [rangeState, setRangeState] = useState<'start' | 'end'>('start');

  // State để theo dõi ngày đang focus (cho keyboard navigation)
  const [focusedDate, setFocusedDate] = useState<Date | null>(null);

  // Memoized calendar days để tránh tính toán lại không cần thiết
  const calendarDays = useMemo(() => {
    return getCalendarDays(month, firstDayOfWeek);
  }, [month, firstDayOfWeek]);

  // Xử lý khi tháng thay đổi
  const handleMonthChange = useCallback(
    (newMonth: Date) => {
      setMonthState(newMonth);
      onMonthChange?.(newMonth);
    },
    [onMonthChange]
  );

  // Wrapper cho setMonth với callback
  const setMonth = useCallback(
    (date: Date) => {
      handleMonthChange(date);
    },
    [handleMonthChange]
  );

  // Xử lý khi chọn ngày
  const handleSelectDate = useCallback(
    (date: Date) => {
      if (rangeMode) {
        if (rangeState === 'start' || (startDate && endDate)) {
          // Bắt đầu chọn range mới
          onRangeSelect?.(date, null);
          setRangeState('end');
        } else {
          // Kết thúc chọn range
          if (startDate && isSameDayFn(date, startDate)) {
            // Nếu chọn cùng ngày với startDate, reset range
            onRangeSelect?.(null, null);
            setRangeState('start');
          } else if (startDate && date < startDate) {
            // Nếu chọn ngày trước startDate, đổi thứ tự
            onRangeSelect?.(date, startDate);
            setRangeState('start');
          } else {
            // Chọn ngày kết thúc bình thường
            if (startDate) {
              onRangeSelect?.(startDate, date);
            }
            setRangeState('start');
          }
        }
      } else {
        onSelectDate?.(date);
      }
    },
    [rangeMode, rangeState, startDate, endDate, onRangeSelect, onSelectDate]
  );

  // Xử lý khi click vào button Today
  const handleTodayClick = useCallback(() => {
    const today = new Date();
    if (rangeMode) {
      onRangeSelect?.(today, today);
    } else {
      onSelectDate?.(today);
    }
    handleMonthChange(today);
    setFocusedDate(today);
  }, [rangeMode, onRangeSelect, onSelectDate, handleMonthChange]);

  // Xử lý keyboard navigation
  const handleKeyboardNavigation = useCallback(
    (key: string, currentFocused: Date): Date | null => {
      let newFocusDate: Date | null = null;

      switch (key) {
        case 'ArrowLeft':
          newFocusDate = addDays(currentFocused, -1);
          break;
        case 'ArrowRight':
          newFocusDate = addDays(currentFocused, 1);
          break;
        case 'ArrowUp':
          newFocusDate = addDays(currentFocused, -7);
          break;
        case 'ArrowDown':
          newFocusDate = addDays(currentFocused, 7);
          break;
        case 'Home':
          // Ngày đầu tuần
          newFocusDate = addDays(
            currentFocused,
            -currentFocused.getDay() + (firstDayOfWeek === 0 ? 0 : -1)
          );
          break;
        case 'End':
          // Ngày cuối tuần
          newFocusDate = addDays(
            currentFocused,
            6 - currentFocused.getDay() + (firstDayOfWeek === 0 ? 0 : 1)
          );
          break;
        case 'PageUp':
          // Tháng trước
          newFocusDate = new Date(
            currentFocused.getFullYear(),
            currentFocused.getMonth() - 1,
            currentFocused.getDate()
          );
          break;
        case 'PageDown':
          // Tháng sau
          newFocusDate = new Date(
            currentFocused.getFullYear(),
            currentFocused.getMonth() + 1,
            currentFocused.getDate()
          );
          break;
        default:
          return null;
      }

      // Nếu ngày mới ở tháng khác, cập nhật tháng hiển thị
      if (newFocusDate && newFocusDate.getMonth() !== month.getMonth()) {
        handleMonthChange(newFocusDate);
      }

      return newFocusDate;
    },
    [month, firstDayOfWeek, handleMonthChange]
  );

  // Navigation helpers
  const goToPreviousMonth = useCallback(() => {
    const prevMonth = subMonths(month, 1);
    handleMonthChange(prevMonth);
  }, [month, handleMonthChange]);

  const goToNextMonth = useCallback(() => {
    const nextMonth = addMonths(month, 1);
    handleMonthChange(nextMonth);
  }, [month, handleMonthChange]);

  const goToToday = useCallback(() => {
    const today = new Date();
    handleMonthChange(today);
    setFocusedDate(today);
  }, [handleMonthChange]);

  // Khởi tạo focusedDate khi component mount
  useEffect(() => {
    if (!focusedDate) {
      setFocusedDate(selectedDate || startDate || new Date());
    }
  }, [focusedDate, selectedDate, startDate]);

  return {
    // State
    month,
    focusedDate,
    rangeState,
    
    // Computed values
    calendarDays,
    
    // Actions
    setMonth,
    setFocusedDate,
    handleSelectDate,
    handleMonthChange,
    handleTodayClick,
    handleKeyboardNavigation,
    
    // Navigation helpers
    goToPreviousMonth,
    goToNextMonth,
    goToToday,
  };
};

export default useCalendar;
