import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  CalendarTheme, 
  CalendarThemeVariant, 
  CalendarColorScheme, 
  CalendarThemeConfig,
  UseCalendarThemeReturn 
} from '../types/theme.types';
import { defaultThemes, createThemeWithColorScheme } from '../constants/defaultThemes';

/**
 * Hook quản lý theme cho Calendar component
 * Hỗ trợ light/dark mode, color schemes và custom themes
 */
export interface UseCalendarThemeOptions {
  /**
   * Theme variant mặc định
   */
  defaultVariant?: CalendarThemeVariant;
  
  /**
   * Color scheme mặc định
   */
  defaultColorScheme?: CalendarColorScheme;
  
  /**
   * Custom theme
   */
  customTheme?: Partial<CalendarTheme>;
  
  /**
   * Bật/tắt animations
   */
  enableAnimations?: boolean;
  
  /**
   * Respect user's reduced motion preference
   */
  respectReducedMotion?: boolean;
  
  /**
   * Persist theme settings in localStorage
   */
  persistSettings?: boolean;
  
  /**
   * Storage key for persisting settings
   */
  storageKey?: string;
}

const DEFAULT_OPTIONS: Required<UseCalendarThemeOptions> = {
  defaultVariant: 'auto',
  defaultColorScheme: 'blue',
  customTheme: {},
  enableAnimations: true,
  respectReducedMotion: true,
  persistSettings: true,
  storageKey: 'calendar-theme-settings',
};

export const useCalendarTheme = (
  options: UseCalendarThemeOptions = {}
): UseCalendarThemeReturn => {
  const opts = useMemo(() => ({ ...DEFAULT_OPTIONS, ...options }), [options]);

  // Load persisted settings
  const loadPersistedSettings = useCallback((): CalendarThemeConfig => {
    if (!opts.persistSettings || typeof window === 'undefined') {
      return {
        variant: opts.defaultVariant,
        colorScheme: opts.defaultColorScheme,
        customTheme: opts.customTheme,
        enableAnimations: opts.enableAnimations,
        reducedMotion: false,
      };
    }

    try {
      const stored = localStorage.getItem(opts.storageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          variant: parsed.variant || opts.defaultVariant,
          colorScheme: parsed.colorScheme || opts.defaultColorScheme,
          customTheme: { ...opts.customTheme, ...parsed.customTheme },
          enableAnimations: parsed.enableAnimations ?? opts.enableAnimations,
          reducedMotion: parsed.reducedMotion || false,
        };
      }
    } catch (error) {
      console.warn('Failed to load calendar theme settings:', error);
    }

    return {
      variant: opts.defaultVariant,
      colorScheme: opts.defaultColorScheme,
      customTheme: opts.customTheme,
      enableAnimations: opts.enableAnimations,
      reducedMotion: false,
    };
  }, [opts]);

  // Theme configuration state
  const [config, setConfig] = useState<CalendarThemeConfig>(loadPersistedSettings);

  // Detect system theme preference
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>(() => {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  });

  // Detect reduced motion preference
  const [systemReducedMotion, setSystemReducedMotion] = useState<boolean>(() => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  });

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Listen for reduced motion changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemReducedMotion(e.matches);
      if (opts.respectReducedMotion) {
        setConfig(prev => ({ ...prev, reducedMotion: e.matches }));
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [opts.respectReducedMotion]);

  // Update reduced motion when system preference changes
  useEffect(() => {
    if (opts.respectReducedMotion) {
      setConfig(prev => ({ ...prev, reducedMotion: systemReducedMotion }));
    }
  }, [systemReducedMotion, opts.respectReducedMotion]);

  // Persist settings to localStorage
  useEffect(() => {
    if (!opts.persistSettings || typeof window === 'undefined') return;

    try {
      localStorage.setItem(opts.storageKey, JSON.stringify(config));
    } catch (error) {
      console.warn('Failed to persist calendar theme settings:', error);
    }
  }, [config, opts.persistSettings, opts.storageKey]);

  // Determine current theme variant
  const currentVariant = useMemo((): 'light' | 'dark' => {
    if (config.variant === 'auto') {
      return systemTheme;
    }
    return config.variant;
  }, [config.variant, systemTheme]);

  // Create final theme
  const theme = useMemo((): CalendarTheme => {
    // Start with base theme
    let baseTheme = defaultThemes[currentVariant];
    
    // Apply color scheme
    baseTheme = createThemeWithColorScheme(baseTheme, config.colorScheme);
    
    // Apply custom theme overrides
    if (config.customTheme && Object.keys(config.customTheme).length > 0) {
      baseTheme = {
        ...baseTheme,
        ...config.customTheme,
        colors: {
          ...baseTheme.colors,
          ...config.customTheme.colors,
        },
        spacing: {
          ...baseTheme.spacing,
          ...config.customTheme.spacing,
        },
        typography: {
          ...baseTheme.typography,
          ...config.customTheme.typography,
        },
        shadows: {
          ...baseTheme.shadows,
          ...config.customTheme.shadows,
        },
        borderRadius: {
          ...baseTheme.borderRadius,
          ...config.customTheme.borderRadius,
        },
        transitions: {
          ...baseTheme.transitions,
          ...config.customTheme.transitions,
        },
        animations: {
          ...baseTheme.animations,
          ...config.customTheme.animations,
        },
      };
    }

    return baseTheme;
  }, [currentVariant, config.colorScheme, config.customTheme]);

  // Theme setters
  const setVariant = useCallback((variant: CalendarThemeVariant) => {
    setConfig(prev => ({ ...prev, variant }));
  }, []);

  const setColorScheme = useCallback((colorScheme: CalendarColorScheme) => {
    setConfig(prev => ({ ...prev, colorScheme }));
  }, []);

  const setCustomTheme = useCallback((customTheme: Partial<CalendarTheme>) => {
    setConfig(prev => ({ ...prev, customTheme }));
  }, []);

  const toggleAnimations = useCallback(() => {
    setConfig(prev => ({ ...prev, enableAnimations: !prev.enableAnimations }));
  }, []);

  // Helper functions
  const getThemeClasses = useCallback((): string => {
    const classes = [
      `calendar-theme-${currentVariant}`,
      `calendar-scheme-${config.colorScheme}`,
    ];

    if (!config.enableAnimations || config.reducedMotion) {
      classes.push('calendar-no-animations');
    }

    return classes.join(' ');
  }, [currentVariant, config.colorScheme, config.enableAnimations, config.reducedMotion]);

  const getCSSVariables = useCallback((): Record<string, string> => {
    const variables: Record<string, string> = {};

    // Colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      if (typeof value === 'string') {
        variables[`--calendar-color-${key}`] = value;
      } else if (typeof value === 'object') {
        Object.entries(value).forEach(([subKey, subValue]) => {
          if (typeof subValue === 'string') {
            variables[`--calendar-color-${key}-${subKey}`] = subValue;
          } else if (typeof subValue === 'object' && subValue !== null) {
            Object.entries(subValue).forEach(([subSubKey, subSubValue]) => {
              variables[`--calendar-color-${key}-${subKey}-${subSubKey}`] = subSubValue as string;
            });
          }
        });
      }
    });

    // Spacing
    Object.entries(theme.spacing).forEach(([key, value]) => {
      if (typeof value === 'string') {
        variables[`--calendar-spacing-${key}`] = value;
      } else if (typeof value === 'object' && value !== null) {
        Object.entries(value).forEach(([subKey, subValue]) => {
          variables[`--calendar-spacing-${key}-${subKey}`] = subValue as string;
        });
      }
    });

    // Typography
    Object.entries(theme.typography).forEach(([key, value]) => {
      if (typeof value === 'string') {
        variables[`--calendar-typography-${key}`] = value;
      } else if (typeof value === 'object' && value !== null) {
        Object.entries(value).forEach(([subKey, subValue]) => {
          variables[`--calendar-typography-${key}-${subKey}`] = subValue as string;
        });
      }
    });

    // Shadows
    Object.entries(theme.shadows).forEach(([key, value]) => {
      variables[`--calendar-shadow-${key}`] = value;
    });

    // Border radius
    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      variables[`--calendar-radius-${key}`] = value;
    });

    // Transitions
    Object.entries(theme.transitions).forEach(([key, value]) => {
      if (typeof value === 'string') {
        variables[`--calendar-transition-${key}`] = value;
      } else if (typeof value === 'object' && value !== null) {
        Object.entries(value).forEach(([subKey, subValue]) => {
          variables[`--calendar-transition-${key}-${subKey}`] = subValue as string;
        });
      }
    });

    return variables;
  }, [theme]);

  return {
    theme,
    config,
    isDark: currentVariant === 'dark',
    isLight: currentVariant === 'light',
    isAuto: config.variant === 'auto',
    setVariant,
    setColorScheme,
    setCustomTheme,
    toggleAnimations,
    getThemeClasses,
    getCSSVariables,
  };
};

export default useCalendarTheme;
