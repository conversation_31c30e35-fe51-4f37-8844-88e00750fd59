import { useState, useCallback, useMemo, useEffect } from 'react';
import { format, formatInTimeZone, toZonedTime } from 'date-fns-tz';

/**
 * Hook xử lý Time Zone cho Calendar
 * Hỗ trợ chuyển đổi giữa các múi giờ và hiển thị thời gian chính xác
 */
export interface UseCalendarTimeZoneOptions {
  /**
   * Time zone mặc định
   */
  defaultTimeZone?: string;
  
  /**
   * Tự động detect time zone từ browser
   */
  autoDetect?: boolean;
  
  /**
   * Persist time zone setting
   */
  persistTimeZone?: boolean;
  
  /**
   * Storage key for persisting
   */
  storageKey?: string;
}

export interface UseCalendarTimeZoneReturn {
  // Current time zone
  timeZone: string;
  timeZoneName: string;
  timeZoneOffset: string;
  
  // Available time zones
  availableTimeZones: TimeZoneInfo[];
  popularTimeZones: TimeZoneInfo[];
  
  // Time zone setters
  setTimeZone: (timeZone: string) => void;
  
  // Date conversion utilities
  convertToTimeZone: (date: Date, targetTimeZone?: string) => Date;
  convertFromTimeZone: (date: Date, sourceTimeZone?: string) => Date;
  formatInCurrentTimeZone: (date: Date, formatStr?: string) => string;
  formatInTargetTimeZone: (date: Date, targetTimeZone: string, formatStr?: string) => string;
  
  // Current time in time zone
  getCurrentTimeInTimeZone: () => Date;
  
  // Time zone info
  getTimeZoneInfo: (timeZone?: string) => TimeZoneInfo;
  isValidTimeZone: (timeZone: string) => boolean;
}

export interface TimeZoneInfo {
  id: string;
  name: string;
  offset: string;
  offsetMinutes: number;
  country?: string;
  region?: string;
  popular?: boolean;
}

/**
 * Popular time zones list
 */
const POPULAR_TIME_ZONES = [
  'UTC',
  'America/New_York',
  'America/Los_Angeles',
  'America/Chicago',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Asia/Tokyo',
  'Asia/Shanghai',
  'Asia/Ho_Chi_Minh',
  'Asia/Bangkok',
  'Asia/Singapore',
  'Australia/Sydney',
  'Pacific/Auckland',
];

/**
 * Get browser's time zone
 */
const getBrowserTimeZone = (): string => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch {
    return 'UTC';
  }
};

/**
 * Get all available time zones
 */
const getAvailableTimeZones = (): TimeZoneInfo[] => {
  const timeZones: TimeZoneInfo[] = [];
  const now = new Date();
  
  // Get all supported time zones
  const supportedTimeZones = (Intl as { supportedValuesOf?: (type: string) => string[] }).supportedValuesOf?.('timeZone') || POPULAR_TIME_ZONES;

  supportedTimeZones.forEach((timeZoneId: string) => {
    try {
      const formatter = new Intl.DateTimeFormat('en', {
        timeZone: timeZoneId,
        timeZoneName: 'long',
      });
      
      const parts = formatter.formatToParts(now);
      const timeZoneName = parts.find(part => part.type === 'timeZoneName')?.value || timeZoneId;
      
      // Get offset
      const offsetFormatter = new Intl.DateTimeFormat('en', {
        timeZone: timeZoneId,
        timeZoneName: 'longOffset',
      });
      
      const offsetParts = offsetFormatter.formatToParts(now);
      const offset = offsetParts.find(part => part.type === 'timeZoneName')?.value || '+00:00';
      
      // Calculate offset in minutes
      const offsetMinutes = getTimeZoneOffsetMinutes(timeZoneId, now);

      // Parse region and country
      const [region, country] = timeZoneId.split('/');

      timeZones.push({
        id: timeZoneId,
        name: timeZoneName,
        offset,
        offsetMinutes,
        region,
        country,
        popular: POPULAR_TIME_ZONES.includes(timeZoneId),
      });
    } catch {
      // Skip invalid time zones
    }
  });
  
  return timeZones.sort((a, b) => {
    // Sort by popularity first, then by offset
    if (a.popular && !b.popular) return -1;
    if (!a.popular && b.popular) return 1;
    return a.offsetMinutes - b.offsetMinutes;
  });
};

/**
 * Get time zone offset in minutes
 */
const getTimeZoneOffsetMinutes = (timeZone: string, date: Date): number => {
  try {
    const utcDate = new Date(date.toISOString());
    const zonedDate = toZonedTime(utcDate, timeZone);
    return (zonedDate.getTime() - utcDate.getTime()) / (1000 * 60);
  } catch {
    return 0;
  }
};

/**
 * Format offset as string
 */
const formatOffset = (offsetMinutes: number): string => {
  const hours = Math.floor(Math.abs(offsetMinutes) / 60);
  const minutes = Math.abs(offsetMinutes) % 60;
  const sign = offsetMinutes >= 0 ? '+' : '-';
  return `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
};

export const useCalendarTimeZone = (
  options: UseCalendarTimeZoneOptions = {}
): UseCalendarTimeZoneReturn => {
  const {
    defaultTimeZone,
    autoDetect = true,
    persistTimeZone = true,
    storageKey = 'calendar-timezone',
  } = options;

  // Load persisted time zone
  const loadPersistedTimeZone = useCallback((): string => {
    if (!persistTimeZone || typeof window === 'undefined') {
      return defaultTimeZone || (autoDetect ? getBrowserTimeZone() : 'UTC');
    }

    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        return stored;
      }
    } catch (error) {
      console.warn('Failed to load persisted time zone:', error);
    }

    return defaultTimeZone || (autoDetect ? getBrowserTimeZone() : 'UTC');
  }, [defaultTimeZone, autoDetect, persistTimeZone, storageKey]);

  // Current time zone state
  const [timeZone, setTimeZoneState] = useState<string>(loadPersistedTimeZone);

  // Available time zones (memoized for performance)
  const availableTimeZones = useMemo(() => getAvailableTimeZones(), []);
  
  const popularTimeZones = useMemo(() => 
    availableTimeZones.filter(tz => tz.popular), 
    [availableTimeZones]
  );

  // Current time zone info
  const currentTimeZoneInfo = useMemo(() => {
    return availableTimeZones.find(tz => tz.id === timeZone) || {
      id: timeZone,
      name: timeZone,
      offset: formatOffset(getTimeZoneOffsetMinutes(timeZone, new Date())),
      offsetMinutes: getTimeZoneOffsetMinutes(timeZone, new Date()),
    };
  }, [timeZone, availableTimeZones]);

  // Persist time zone when it changes
  useEffect(() => {
    if (!persistTimeZone || typeof window === 'undefined') return;

    try {
      localStorage.setItem(storageKey, timeZone);
    } catch (error) {
      console.warn('Failed to persist time zone:', error);
    }
  }, [timeZone, persistTimeZone, storageKey]);

  // Validate time zone
  const isValidTimeZone = useCallback((tz: string): boolean => {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: tz });
      return true;
    } catch {
      return false;
    }
  }, []);

  // Set time zone
  const setTimeZone = useCallback((newTimeZone: string) => {
    if (isValidTimeZone(newTimeZone)) {
      setTimeZoneState(newTimeZone);
    } else {
      console.warn('Invalid time zone:', newTimeZone);
    }
  }, [isValidTimeZone]);

  // Convert date to time zone
  const convertToTimeZone = useCallback((date: Date, targetTimeZone?: string): Date => {
    const tz = targetTimeZone || timeZone;
    try {
      return toZonedTime(date, tz);
    } catch (error) {
      console.warn('Failed to convert to time zone:', error);
      return date;
    }
  }, [timeZone]);

  // Convert date from time zone
  const convertFromTimeZone = useCallback((date: Date, sourceTimeZone?: string): Date => {
    const tz = sourceTimeZone || timeZone;
    try {
      // Use a simple conversion since zonedTimeToUtc is not available
      const zonedDate = toZonedTime(date, tz);
      const offsetMinutes = getTimeZoneOffsetMinutes(tz, date);
      return new Date(zonedDate.getTime() - offsetMinutes * 60 * 1000);
    } catch (error) {
      console.warn('Failed to convert from time zone:', error);
      return date;
    }
  }, [timeZone]);

  // Format date in current time zone
  const formatInCurrentTimeZone = useCallback((date: Date, formatStr: string = 'yyyy-MM-dd HH:mm:ss'): string => {
    try {
      return formatInTimeZone(date, timeZone, formatStr);
    } catch (error) {
      console.warn('Failed to format in current time zone:', error);
      return format(date, formatStr);
    }
  }, [timeZone]);

  // Format date in target time zone
  const formatInTargetTimeZone = useCallback((date: Date, targetTimeZone: string, formatStr: string = 'yyyy-MM-dd HH:mm:ss'): string => {
    try {
      return formatInTimeZone(date, targetTimeZone, formatStr);
    } catch (error) {
      console.warn('Failed to format in target time zone:', error);
      return format(date, formatStr);
    }
  }, []);

  // Get current time in time zone
  const getCurrentTimeInTimeZone = useCallback((): Date => {
    return convertToTimeZone(new Date());
  }, [convertToTimeZone]);

  // Get time zone info
  const getTimeZoneInfo = useCallback((tz?: string): TimeZoneInfo => {
    const targetTimeZone = tz || timeZone;
    return availableTimeZones.find(tzInfo => tzInfo.id === targetTimeZone) || {
      id: targetTimeZone,
      name: targetTimeZone,
      offset: formatOffset(getTimeZoneOffsetMinutes(targetTimeZone, new Date())),
      offsetMinutes: getTimeZoneOffsetMinutes(targetTimeZone, new Date()),
    };
  }, [timeZone, availableTimeZones]);

  return {
    // Current time zone
    timeZone,
    timeZoneName: currentTimeZoneInfo.name,
    timeZoneOffset: currentTimeZoneInfo.offset,
    
    // Available time zones
    availableTimeZones,
    popularTimeZones,
    
    // Time zone setters
    setTimeZone,
    
    // Date conversion utilities
    convertToTimeZone,
    convertFromTimeZone,
    formatInCurrentTimeZone,
    formatInTargetTimeZone,
    
    // Current time
    getCurrentTimeInTimeZone,
    
    // Time zone info
    getTimeZoneInfo,
    isValidTimeZone,
  };
};
