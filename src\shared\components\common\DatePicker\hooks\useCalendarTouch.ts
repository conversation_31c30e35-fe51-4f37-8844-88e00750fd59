import { useCallback, useRef, useState } from 'react';
import { UseCalendarReturn } from './useCalendar';

/**
 * Hook xử lý touch gestures cho Calendar
 * Hỗ trợ swipe navigation và touch interactions
 */
export interface UseCalendarTouchOptions {
  calendar: UseCalendarReturn;
  disabled?: boolean;
  swipeThreshold?: number;
  enableSwipe?: boolean;
}

export interface TouchPosition {
  x: number;
  y: number;
  timestamp: number;
}

export interface UseCalendarTouchReturn {
  handleTouchStart: (e: React.TouchEvent) => void;
  handleTouchMove: (e: React.TouchEvent) => void;
  handleTouchEnd: (e: React.TouchEvent) => void;
  isSwipeInProgress: boolean;
  swipeDirection: 'left' | 'right' | null;
}

export const useCalendarTouch = (options: UseCalendarTouchOptions): UseCalendarTouchReturn => {
  const { calendar, disabled = false, swipeThreshold = 50, enableSwipe = true } = options;

  const { goToPreviousMonth, goToNextMonth } = calendar;

  // Refs để track touch positions
  const touchStartRef = useRef<TouchPosition | null>(null);
  const touchCurrentRef = useRef<TouchPosition | null>(null);
  const [isSwipeInProgress, setIsSwipeInProgress] = useState(false);
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null);

  // Helper function để lấy touch position
  const getTouchPosition = useCallback((e: React.TouchEvent): TouchPosition => {
    const touch = e.touches[0] || e.changedTouches[0];
    return {
      x: touch?.clientX || 0,
      y: touch?.clientY || 0,
      timestamp: Date.now(),
    };
  }, []);

  // Xử lý touch start
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (disabled || !enableSwipe) return;

      const position = getTouchPosition(e);
      touchStartRef.current = position;
      touchCurrentRef.current = position;
      setIsSwipeInProgress(false);
      setSwipeDirection(null);
    },
    [disabled, enableSwipe, getTouchPosition]
  );

  // Xử lý touch move
  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (disabled || !enableSwipe || !touchStartRef.current) return;

      const currentPosition = getTouchPosition(e);
      touchCurrentRef.current = currentPosition;

      const deltaX = currentPosition.x - touchStartRef.current.x;
      const deltaY = currentPosition.y - touchStartRef.current.y;

      // Kiểm tra xem có phải là horizontal swipe không
      const isHorizontalSwipe = Math.abs(deltaX) > Math.abs(deltaY);

      if (isHorizontalSwipe && Math.abs(deltaX) > 10) {
        setIsSwipeInProgress(true);
        setSwipeDirection(deltaX > 0 ? 'right' : 'left');

        // Prevent default để tránh scroll
        e.preventDefault();
      }
    },
    [disabled, enableSwipe, getTouchPosition]
  );

  // Xử lý touch end
  const handleTouchEnd = useCallback(() => {
    if (disabled || !enableSwipe || !touchStartRef.current || !touchCurrentRef.current) {
      setIsSwipeInProgress(false);
      setSwipeDirection(null);
      return;
    }

    const startPosition = touchStartRef.current;
    const endPosition = touchCurrentRef.current;

    const deltaX = endPosition.x - startPosition.x;
    const deltaY = endPosition.y - startPosition.y;
    const deltaTime = endPosition.timestamp - startPosition.timestamp;

    // Kiểm tra điều kiện swipe
    const isHorizontalSwipe = Math.abs(deltaX) > Math.abs(deltaY);
    const isSwipeDistance = Math.abs(deltaX) >= swipeThreshold;
    const isSwipeSpeed = deltaTime < 500; // Swipe trong vòng 500ms

    if (isHorizontalSwipe && isSwipeDistance && isSwipeSpeed) {
      if (deltaX > 0) {
        // Swipe right - previous month
        goToPreviousMonth();
      } else {
        // Swipe left - next month
        goToNextMonth();
      }
    }

    // Reset state
    touchStartRef.current = null;
    touchCurrentRef.current = null;
    setIsSwipeInProgress(false);
    setSwipeDirection(null);
  }, [disabled, enableSwipe, swipeThreshold, goToPreviousMonth, goToNextMonth]);

  return {
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    isSwipeInProgress,
    swipeDirection,
  };
};

export default useCalendarTouch;
