/**
 * Calendar Theme System Types
 * Định nghĩa các types cho hệ thống theme của Calendar
 */

export interface CalendarColors {
  // Background colors
  background: string;
  surface: string;
  overlay: string;
  
  // Text colors
  text: {
    primary: string;
    secondary: string;
    disabled: string;
    inverse: string;
  };
  
  // Border colors
  border: {
    default: string;
    focus: string;
    hover: string;
  };
  
  // State colors
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Calendar specific colors
  today: string;
  selected: string;
  hover: string;
  disabled: string;
  weekend: string;
  otherMonth: string;
  
  // Range colors
  rangeStart: string;
  rangeEnd: string;
  rangeMiddle: string;
  rangeHover: string;
  
  // Event colors
  event: {
    dot: string;
    badge: string;
    highlight: string;
    priority: {
      low: string;
      medium: string;
      high: string;
    };
  };
}

export interface CalendarSpacing {
  // Padding
  padding: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  
  // Margin
  margin: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  
  // Gap
  gap: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  
  // Cell specific
  cell: {
    size: string;
    padding: string;
    margin: string;
  };
  
  // Calendar specific
  calendar: {
    padding: string;
    headerHeight: string;
    footerHeight: string;
  };
}

export interface CalendarTypography {
  // Font families
  fontFamily: {
    primary: string;
    secondary: string;
    mono: string;
  };
  
  // Font sizes
  fontSize: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
  };
  
  // Font weights
  fontWeight: {
    light: string;
    normal: string;
    medium: string;
    semibold: string;
    bold: string;
  };
  
  // Line heights
  lineHeight: {
    tight: string;
    normal: string;
    relaxed: string;
  };
  
  // Letter spacing
  letterSpacing: {
    tight: string;
    normal: string;
    wide: string;
  };
}

export interface CalendarShadows {
  // Box shadows
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  
  // Calendar specific shadows
  calendar: string;
  cell: string;
  dropdown: string;
  tooltip: string;
}

export interface CalendarBorderRadius {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  full: string;
  
  // Calendar specific
  calendar: string;
  cell: string;
  button: string;
}

export interface CalendarTransitions {
  // Duration
  duration: {
    fast: string;
    normal: string;
    slow: string;
  };
  
  // Timing functions
  timing: {
    linear: string;
    ease: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
    bounce: string;
  };
  
  // Common transitions
  default: string;
  colors: string;
  transform: string;
  opacity: string;
}

export interface CalendarAnimations {
  // Keyframes
  keyframes: {
    fadeIn: string;
    fadeOut: string;
    slideIn: string;
    slideOut: string;
    scaleIn: string;
    scaleOut: string;
    bounce: string;
    pulse: string;
  };
  
  // Animation configs
  monthTransition: {
    duration: string;
    timing: string;
  };
  
  cellHover: {
    duration: string;
    timing: string;
  };
  
  selection: {
    duration: string;
    timing: string;
  };
}

/**
 * Main Calendar Theme Interface
 */
export interface CalendarTheme {
  name: string;
  colors: CalendarColors;
  spacing: CalendarSpacing;
  typography: CalendarTypography;
  shadows: CalendarShadows;
  borderRadius: CalendarBorderRadius;
  transitions: CalendarTransitions;
  animations: CalendarAnimations;
}

/**
 * Theme Variant Types
 */
export type CalendarThemeVariant = 'light' | 'dark' | 'auto';

export type CalendarColorScheme = 
  | 'blue' 
  | 'green' 
  | 'purple' 
  | 'red' 
  | 'orange' 
  | 'pink' 
  | 'indigo' 
  | 'teal';

/**
 * Theme Configuration
 */
export interface CalendarThemeConfig {
  variant: CalendarThemeVariant;
  colorScheme: CalendarColorScheme;
  customTheme?: Partial<CalendarTheme>;
  enableAnimations: boolean;
  reducedMotion: boolean;
}

/**
 * Theme Context Type
 */
export interface CalendarThemeContextType {
  theme: CalendarTheme;
  config: CalendarThemeConfig;
  setVariant: (variant: CalendarThemeVariant) => void;
  setColorScheme: (scheme: CalendarColorScheme) => void;
  setCustomTheme: (theme: Partial<CalendarTheme>) => void;
  toggleAnimations: () => void;
}

/**
 * Theme Hook Return Type
 */
export interface UseCalendarThemeReturn {
  theme: CalendarTheme;
  config: CalendarThemeConfig;
  isDark: boolean;
  isLight: boolean;
  isAuto: boolean;
  setVariant: (variant: CalendarThemeVariant) => void;
  setColorScheme: (scheme: CalendarColorScheme) => void;
  setCustomTheme: (theme: Partial<CalendarTheme>) => void;
  toggleAnimations: () => void;
  getThemeClasses: () => string;
  getCSSVariables: () => Record<string, string>;
}
