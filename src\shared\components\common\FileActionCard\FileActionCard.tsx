/**
 * Component card với button tải xuống và tải lên
 */
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Icon, Typography } from '@/shared/components/common';

export interface FileActionCardProps {
  /**
   * Tiêu đề của card
   */
  title?: string;

  /**
   * Mô tả của card
   */
  description?: string;

  /**
   * URL file để tải xuống
   */
  downloadUrl?: string;

  /**
   * Tên file khi tải xuống
   */
  downloadFileName?: string;

  /**
   * Callback khi tải xuống
   */
  onDownload?: () => void;

  /**
   * Callback khi tải lên file
   */
  onUpload?: (file: File) => void;

  /**
   * Các loại file được phép tải lên
   */
  acceptedFileTypes?: string;

  /**
   * Kích thước file tối đa (MB)
   */
  maxFileSize?: number;

  /**
   * <PERSON><PERSON> hiển thị button tải xuống không
   */
  showDownload?: boolean;

  /**
   * Có hiển thị button tải lên không
   */
  showUpload?: boolean;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;

  /**
   * Class CSS bổ sung
   */
  className?: string;
}

/**
 * Component card với button tải xuống và tải lên
 */
const FileActionCard: React.FC<FileActionCardProps> = ({
  title = 'Quản lý tệp',
  description = 'Tải xuống hoặc tải lên tệp của bạn',
  downloadUrl,
  downloadFileName = 'file.pdf',
  onDownload,
  onUpload,
  acceptedFileTypes = '.pdf,.doc,.docx,.jpg,.png',
  maxFileSize = 10,
  showDownload = true,
  showUpload = true,
  isLoading = false,
  className = '',
}) => {
  const { t } = useTranslation(['common']);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = downloadFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onUpload) {
      // Kiểm tra kích thước file
      if (file.size > maxFileSize * 1024 * 1024) {
        alert(`Kích thước file không được vượt quá ${maxFileSize}MB`);
        return;
      }
      onUpload(file);
    }
    // Reset input để có thể chọn lại cùng file
    event.target.value = '';
  };

  return (
    <Card className={`text-center ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <Typography variant="h4" className="mb-2">
            {title}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {description}
          </Typography>
        </div>

        {/* File Icon */}
        <div className="mb-6">
          <Icon name="file" size="xl" className="text-primary mx-auto text-4xl" />
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {showDownload && (
            <Button
              variant="primary"
              leftIcon={<Icon name="download" />}
              onClick={handleDownload}
              disabled={isLoading || (!downloadUrl && !onDownload)}
              isLoading={isLoading}
              className="flex-1 sm:flex-none"
            >
              {t('common:actions.download')}
            </Button>
          )}

          {showUpload && (
            <>
              <Button
                variant="outline"
                leftIcon={<Icon name="upload" />}
                onClick={handleUploadClick}
                disabled={isLoading}
                className="flex-1 sm:flex-none"
              >
                {t('common:actions.upload')}
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept={acceptedFileTypes}
                onChange={handleFileChange}
                className="hidden"
              />
            </>
          )}
        </div>

        {/* File Info */}
        {(acceptedFileTypes || maxFileSize) && (
          <div className="mt-4 text-xs text-muted-foreground">
            {acceptedFileTypes && (
              <div>Định dạng hỗ trợ: {acceptedFileTypes}</div>
            )}
            {maxFileSize && (
              <div>Kích thước tối đa: {maxFileSize}MB</div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export default FileActionCard;
