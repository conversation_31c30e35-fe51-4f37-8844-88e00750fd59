import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import {
  Checkbox,
  Typography,
  Icon,
} from '@/shared/components/common';

export interface GenericCustomFieldData {
  id: number;
  label: string;
  dataType: string;
  type: string;
}

export interface GenericCustomFieldSearchResult {
  items: GenericCustomFieldData[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
}

interface GenericCustomFieldSelectorProps {
  onFieldSelect: (fieldData: GenericCustomFieldData) => void;
  selectedFieldIds: number[];
  placeholder?: string;
  searchFunction: (params: { search?: string; page?: number; limit?: number }) => Promise<GenericCustomFieldSearchResult>;
  title?: string;
  translationNamespace?: string;
  usePortal?: boolean; // Mặc định false để không sticky
}

/**
 * Component dùng chung để chọn custom fields với cache và debounce
 * <PERSON><PERSON> thể sử dụng cho cả business và marketing modules
 */
const GenericCustomFieldSelector: React.FC<GenericCustomFieldSelectorProps> = ({
  onFieldSelect,
  selectedFieldIds,
  placeholder = 'Nhập từ khóa và nhấn Enter để tìm kiếm...',
  searchFunction,
  title = 'Trường tùy chỉnh',
  translationNamespace = 'common',
  usePortal = false, // Mặc định false để không sticky
}) => {
  console.log('🔧 GenericCustomFieldSelector rendered with props:', {
    selectedFieldIds,
    placeholder,
    title,
    searchFunction: !!searchFunction
  });

  const { t } = useTranslation([translationNamespace, 'common']);
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [isSelecting, setIsSelecting] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{
    top: number;
    left: number;
    width: number;
    maxHeight: number;
  } | null>(null);

  // State cho search results
  const [searchResults, setSearchResults] = useState<GenericCustomFieldData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Debounce timer
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate dropdown position (chỉ dùng khi usePortal = true)
  const calculateDropdownPosition = useCallback(() => {
    if (!usePortal || !containerRef.current) return null;

    const rect = containerRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const dropdownHeight = 350; // Estimated dropdown height

    // Check if element is still visible in viewport
    if (rect.bottom < 0 || rect.top > viewportHeight || rect.right < 0 || rect.left > viewportWidth) {
      // Element is out of viewport, close dropdown
      setIsOpen(false);
      setDropdownPosition(null);
      return null;
    }

    // Determine if dropdown should appear above or below
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    const shouldShowAbove = spaceBelow < dropdownHeight && spaceAbove > spaceBelow;

    // Ensure dropdown doesn't go outside viewport horizontally
    const maxWidth = Math.min(rect.width, viewportWidth - rect.left - 10);

    const position = {
      top: shouldShowAbove ? rect.top - dropdownHeight : rect.bottom,
      left: Math.max(10, Math.min(rect.left, viewportWidth - maxWidth - 10)),
      width: maxWidth,
      maxHeight: shouldShowAbove ? Math.min(spaceAbove - 10, 300) : Math.min(spaceBelow - 10, 300),
    };

    setDropdownPosition(position);
    return position;
  }, [usePortal]);

  // Handle search
  const handleSearch = useCallback(async (query: string, page = 1, append = false) => {
    console.log('🔍 handleSearch called:', { query, page, append, isLoading });

    // Cho phép tìm kiếm với query rỗng để load dữ liệu ban đầu
    // Chỉ return early nếu query rỗng và không phải lần đầu load (page > 1)
    if (!query.trim() && page > 1) {
      console.log('❌ Early return: empty query and page > 1');
      return;
    }

    // Không check isLoading ở đây để tránh race condition
    setIsLoading(true);
    try {
      const searchParams: { search?: string; page?: number; limit?: number } = {
        page,
        limit: 20,
      };

      if (query.trim()) {
        searchParams.search = query.trim();
      }

      console.log('📡 Calling searchFunction with params:', searchParams);
      const response = await searchFunction(searchParams);
      console.log('✅ Search response:', response);

      if (append) {
        setSearchResults(prev => [...prev, ...response.items]);
      } else {
        setSearchResults(response.items);
      }

      setHasMore(response.hasNextPage);
      setCurrentPage(page);
      setHasSearched(true);
    } catch (error) {
      console.error('❌ Error searching custom fields:', error);
      if (!append) {
        setSearchResults([]);
        setHasSearched(true);
      }
    } finally {
      setIsLoading(false);
    }
  }, [searchFunction]);

  // Load more results
  const loadMore = useCallback(() => {
    if (hasMore && !isLoading) {
      handleSearch(inputValue, currentPage + 1, true);
    }
  }, [hasMore, isLoading, inputValue, currentPage, handleSearch]);

  // Debounced search - KHÔNG SỬ DỤNG NỮA, chỉ search khi nhấn Enter
  // const debouncedSearch = useCallback((query: string) => {
  //   if (debounceTimerRef.current) {
  //     clearTimeout(debounceTimerRef.current);
  //   }
  //   debounceTimerRef.current = setTimeout(() => {
  //     handleSearch(query);
  //   }, 300);
  // }, [handleSearch]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Đảm bảo dropdown mở khi người dùng gõ
    if (!isOpen) {
      setIsOpen(true);
      if (usePortal) {
        calculateDropdownPosition();
      }
    }

    // KHÔNG gọi search tự động khi gõ - chỉ khi nhấn Enter
    // Chỉ load dữ liệu ban đầu nếu chưa có và input rỗng
    if (!value.trim() && searchResults.length === 0 && !hasSearched && !isLoading) {
      handleSearch('', 1, false);
    }
  };

  // Handle input focus
  const handleInputFocus = () => {
    if (!isOpen) {
      setIsOpen(true);
      if (usePortal) {
        calculateDropdownPosition();
      }
      // Gọi API lần đầu khi mở dropdown nếu chưa có dữ liệu
      if (searchResults.length === 0 && !hasSearched && !isLoading) {
        handleSearch('', 1, false);
      }
    }
  };

  // Handle key down
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    console.log('⌨️ Key pressed:', e.key, 'inputValue:', inputValue);

    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      console.log('🔄 Enter pressed, triggering search with:', inputValue);

      // Đảm bảo dropdown mở
      if (!isOpen) {
        setIsOpen(true);
        if (usePortal) {
          calculateDropdownPosition();
        }
      }

      // Reset về trang 1 và gọi API với từ khóa mới
      setCurrentPage(1);
      setSearchResults([]);
      setHasMore(true);
      setHasSearched(false); // Reset để cho phép search mới

      // Gọi search ngay lập tức
      handleSearch(inputValue, 1, false);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      e.stopPropagation();
      setIsOpen(false);
      if (usePortal) {
        setDropdownPosition(null);
      }
    }
  };

  // Handle field selection
  const handleFieldSelect = useCallback((field: GenericCustomFieldData, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isSelecting) return;
    
    setIsSelecting(true);
    onFieldSelect(field);
    
    // Reset selecting flag after a short delay
    setTimeout(() => {
      setIsSelecting(false);
    }, 100);

    // Menu stays open for multiple selections
  }, [onFieldSelect, isSelecting]);

  // Handle scroll for infinite loading
  const handleDropdownScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (
      scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
      hasMore &&
      !isLoading
    ) {
      loadMore();
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        (!usePortal || (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)))
      ) {
        if (!isSelecting) {
          setIsOpen(false);
          if (usePortal) {
            setDropdownPosition(null);
          }
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isSelecting, usePortal]);

  // Update dropdown position when window resizes or scrolls (chỉ khi usePortal = true)
  useEffect(() => {
    if (!usePortal || !isOpen) return;

    const handlePositionUpdate = () => {
      calculateDropdownPosition();
    };

    // Throttle scroll events to improve performance
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handlePositionUpdate();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('resize', handlePositionUpdate);
    window.addEventListener('scroll', handleScroll, true); // Use capture to catch all scroll events
    document.addEventListener('scroll', handleScroll, true);

    return () => {
      window.removeEventListener('resize', handlePositionUpdate);
      window.removeEventListener('scroll', handleScroll, true);
      document.removeEventListener('scroll', handleScroll, true);
    };
  }, [isOpen, calculateDropdownPosition, usePortal]);

  // Cleanup debounce timer
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);



  // Dropdown content
  const dropdownContent = isOpen && (
    <div
      ref={dropdownRef}
      className={`
        bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden
        ${usePortal ? 'fixed z-[99999] transform-none' : 'absolute top-full left-0 right-0 z-50 mt-1'}
      `}
      style={usePortal && dropdownPosition ? {
        top: `${dropdownPosition.top}px`,
        left: `${dropdownPosition.left}px`,
        width: `${dropdownPosition.width}px`,
      } : undefined}
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-red-500 to-orange-500 p-3 text-white">
        <div className="flex items-center justify-between">
          <Typography variant="subtitle2" className="font-medium">
            {title}
          </Typography>
          <button
            onClick={() => {
              setIsOpen(false);
              if (usePortal) {
                setDropdownPosition(null);
              }
            }}
            className="text-white hover:text-gray-200 transition-colors"
            title="Đóng menu (hoặc nhấn Escape)"
          >
            <Icon name="x" size="sm" />
          </button>
        </div>
        <Typography variant="caption" className="text-white/80 mt-1">
          Click để chọn/bỏ chọn • Nhấn Escape để đóng
        </Typography>
      </div>

      {/* Content */}
      <div
        ref={scrollRef}
        className="overflow-auto p-2"
        style={{
          maxHeight: usePortal && dropdownPosition ? `${dropdownPosition.maxHeight}px` : '300px'
        }}
        onScroll={handleDropdownScroll}
      >
        {isLoading && searchResults.length === 0 ? (
          <div className="flex items-center justify-center p-4 text-gray-500">
            <div className="animate-spin mr-2">
              <Icon name="loader" size="sm" />
            </div>
            <span>{t('common:loading', 'Đang tải...')}</span>
          </div>
        ) : searchResults.length === 0 && hasSearched ? (
          <div className="text-center py-4 text-gray-500">
            {t('common:noResults', 'Không có kết quả')}
          </div>
        ) : searchResults.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            {t('common:searchHint', 'Nhấp để tải dữ liệu hoặc nhập từ khóa để tìm kiếm')}
          </div>
        ) : (
          <div className="space-y-1">
            {searchResults.map((field) => {
              const isSelected = selectedFieldIds.includes(field.id);

              return (
                <div
                  key={field.id}
                  className={`
                    flex items-center p-2 rounded-lg cursor-pointer
                    hover:bg-gray-50 dark:hover:bg-gray-700
                    transition-colors duration-150
                    ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700' : ''}
                  `}
                  onClick={(e) => handleFieldSelect(field, e)}
                >
                  <div className="mr-3">
                    <Checkbox
                      checked={isSelected}
                      onChange={() => {}}
                      variant="filled"
                    />
                  </div>

                  <div className="flex-grow">
                    <div className="flex items-center justify-between">
                      <Typography variant="body2" className="font-medium">
                        {field.label}
                      </Typography>
                    </div>

                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                        {field.dataType}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Loading more indicator */}
            {isLoading && searchResults.length > 0 && (
              <div className="flex items-center justify-center p-2 text-gray-500">
                <div className="animate-spin mr-2">
                  <Icon name="loader" size="xs" />
                </div>
                <span className="text-xs">{t('common:loadingMore', 'Đang tải thêm...')}</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div ref={containerRef} className="relative w-full mb-4">
      {/* Input */}
      <div
        className={`
          flex items-center
          rounded-md
          bg-white dark:bg-gray-800
          py-2 px-3
          cursor-pointer
          transition-all duration-200
          ${isOpen ? 'ring-2 ring-primary/30' : 'hover:bg-gray-50 dark:hover:bg-gray-700'}
        `}
        onClick={() => {
          const newIsOpen = !isOpen;
          setIsOpen(newIsOpen);
          if (newIsOpen && usePortal) {
            calculateDropdownPosition();
          }
          // Gọi API lần đầu khi mở dropdown nếu chưa có dữ liệu
          if (newIsOpen && searchResults.length === 0 && !hasSearched && !isLoading) {
            handleSearch('', 1, false);
          }
        }}
      >
        <div className="flex-shrink-0 mr-2 text-gray-500">
          <Icon name="search" size="sm" />
        </div>

        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          onClick={(e) => e.stopPropagation()}
          placeholder={placeholder}
          className="w-full bg-transparent border-none focus:outline-none focus:ring-0 text-foreground placeholder-gray-500"
        />

        {inputValue && (
          <div className="flex-shrink-0 ml-2 text-xs text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
            Enter
          </div>
        )}

        <div className="flex-shrink-0 ml-2 text-gray-500">
          <Icon name={isOpen ? "chevron-up" : "chevron-down"} size="sm" />
        </div>
      </div>

      {usePortal ? createPortal(dropdownContent, document.body) : dropdownContent}
    </div>
  );
};

export default GenericCustomFieldSelector;
