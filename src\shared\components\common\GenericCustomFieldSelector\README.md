# GenericCustomFieldSelector

Component dùng chung để chọn custom fields với giao diện nhất quán và có thể cấu hình cho nhiều modules khác nhau.

## Tính năng

- ✅ **Giao diện nhất quán**: G<PERSON><PERSON>ng hệt `SimpleCustomFieldSelector` từ business module
- ✅ **Search với debounce**: Tìm kiếm với độ trễ 300ms
- ✅ **Infinite scroll pagination**: Tự động load thêm khi scroll
- ✅ **Keyboard navigation**: Enter để search, Escape để đóng
- ✅ **Click outside để đóng**: Tự động đóng khi click bên ngoài
- ✅ **Multi-select**: Chọn nhiều fields với visual feedback
- ✅ **Loading states**: Hiển thị trạng thái loading và empty
- ✅ **Dark mode support**: Hỗ trợ theme tối
- ✅ **Responsive design**: Tương thích mobile
- ✅ **Portal mode toggle**: <PERSON><PERSON> thể chọn sticky hoặc cố định
- ✅ **Configurable**: Search function, title, translation namespace

## Cách sử dụng

### Basic Usage

```tsx
import { GenericCustomFieldSelector } from '@/shared/components/common';

const MyComponent = () => {
  const [selectedFieldIds, setSelectedFieldIds] = useState<number[]>([]);

  const searchFunction = async (params: { search?: string; page?: number; limit?: number }) => {
    // Gọi API của module tương ứng
    const response = await myModuleAPI.searchCustomFields(params);
    
    return {
      items: response.items.map(item => ({
        id: item.id,
        label: item.displayName,
        dataType: item.dataType,
        type: item.dataType,
        required: item.isRequired,
      })),
      totalItems: response.meta.total,
      totalPages: response.meta.totalPages,
      currentPage: response.meta.page,
      hasNextPage: response.meta.hasNextPage,
    };
  };

  const handleFieldSelect = (fieldData) => {
    // Xử lý khi chọn field
    console.log('Selected field:', fieldData);
  };

  return (
    <GenericCustomFieldSelector
      onFieldSelect={handleFieldSelect}
      selectedFieldIds={selectedFieldIds}
      searchFunction={searchFunction}
      placeholder="Nhập từ khóa để tìm kiếm..."
      title="Trường tùy chỉnh"
      translationNamespace="myModule"
      usePortal={false} // Mặc định false để không sticky
    />
  );
};
```

### Marketing Module Usage

```tsx
import { GenericCustomFieldSelector } from '@/shared/components/common';
import { useMarketingCustomFields } from '@/modules/marketing';

const MyComponent = () => {
  const { data: customFieldsData } = useMarketingCustomFields({});
  const customFields = customFieldsData?.items || [];

  const searchFunction = useCallback(async (params: { search?: string; page?: number; limit?: number }) => {
    // Filter local data based on search
    const filteredFields = customFields.filter(field =>
      !params.search || field.displayName.toLowerCase().includes(params.search.toLowerCase())
    );

    // Simple pagination
    const page = params.page || 1;
    const limit = params.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedFields = filteredFields.slice(startIndex, endIndex);

    return {
      items: paginatedFields.map(item => ({
        id: item.id,
        label: item.displayName,
        component: item.dataType,
        type: item.dataType,
        required: false,
        configId: item.fieldKey,
        configJson: item.config || {},
      })),
      totalItems: filteredFields.length,
      totalPages: Math.ceil(filteredFields.length / limit),
      currentPage: page,
      hasNextPage: endIndex < filteredFields.length,
    };
  }, [customFields]);

  return (
    <GenericCustomFieldSelector
      onFieldSelect={handleFieldSelect}
      selectedFieldIds={selectedFieldIds}
      searchFunction={searchFunction}
      title={t('marketing:customField.title', 'Trường tùy chỉnh')}
      translationNamespace="marketing"
      usePortal={false}
    />
  );
};
```

### Business Module Usage

```tsx
import { GenericCustomFieldSelector } from '@/shared/components/common';
import { useCustomFieldSearch } from '@/modules/business';

// Wrapper function để transform business API
const searchFunction = async (params) => {
  const response = await useCustomFieldSearch()(params);
  return {
    items: response.items.map(item => ({
      id: item.id,
      label: item.label,
      dataType: item.dataType,
      type: item.type,
      required: item.required,
    })),
    totalItems: response.totalItems,
    totalPages: response.totalPages,
    currentPage: response.currentPage,
    hasNextPage: response.hasNextPage,
  };
};

<GenericCustomFieldSelector
  onFieldSelect={handleFieldSelect}
  selectedFieldIds={selectedFieldIds}
  searchFunction={searchFunction}
  title="Trường tùy chỉnh"
  translationNamespace="business"
  usePortal={false}
/>
```

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `onFieldSelect` | `(fieldData: GenericCustomFieldData) => void` | ✅ | - | Callback khi chọn field |
| `selectedFieldIds` | `number[]` | ✅ | - | Danh sách ID các field đã chọn |
| `searchFunction` | `(params) => Promise<GenericCustomFieldSearchResult>` | ✅ | - | Function để search fields |
| `placeholder` | `string` | ❌ | `'Nhập từ khóa và nhấn Enter để tìm kiếm...'` | Placeholder cho input |
| `title` | `string` | ❌ | `'Trường tùy chỉnh'` | Title hiển thị trong dropdown header |
| `translationNamespace` | `string` | ❌ | `'common'` | Namespace cho i18n |
| `usePortal` | `boolean` | ❌ | `false` | Có sử dụng portal (sticky) hay không |

## Interfaces

```typescript
interface GenericCustomFieldData {
  id: number;
  label: string;
  component: string;
  configId?: string;
  type: string;
  required: boolean;
}

interface GenericCustomFieldSearchResult {
  items: GenericCustomFieldData[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
}
```

## Portal Mode vs Normal Mode

### Normal Mode (Khuyến nghị - `usePortal={false}`)
- ✅ Dropdown cố định theo component
- ✅ Không bị trôi nổi
- ✅ Tương thích tốt với scroll containers
- ✅ Hiệu suất tốt hơn

### Portal Mode (`usePortal={true}`)
- ⚠️ Dropdown có thể trôi nổi
- ⚠️ Cần xử lý z-index cẩn thận
- ✅ Có thể vượt qua overflow containers
- ✅ Tương thích với modal/dialog

## Demo

Xem demo tại: `src/shared/components/common/GenericCustomFieldSelector/GenericCustomFieldSelectorDemo.tsx`

## Migration từ SimpleCustomFieldSelector

1. **Import mới**:
```typescript
// Cũ
import SimpleCustomFieldSelector from '@/modules/business/components/SimpleCustomFieldSelector';

// Mới
import { GenericCustomFieldSelector } from '@/shared/components/common';
```

2. **Thêm searchFunction prop**:
```typescript
// Cũ
<SimpleCustomFieldSelector
  onFieldSelect={handleFieldSelect}
  selectedFieldIds={selectedFieldIds}
/>

// Mới
<GenericCustomFieldSelector
  onFieldSelect={handleFieldSelect}
  selectedFieldIds={selectedFieldIds}
  searchFunction={mySearchFunction}
  usePortal={false} // Mặc định không sticky
/>
```

3. **Giao diện không đổi**: Component mới có giao diện hoàn toàn giống component cũ
