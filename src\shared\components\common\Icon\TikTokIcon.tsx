import React from 'react';

interface TikTokIconProps {
  className?: string;
  style?: React.CSSProperties;
  fill?: string;
}

// TikTok icon component
const TikTokIcon: React.FC<TikTokIconProps> = ({ className = '', style = {}, fill = 'currentColor' }) => {
  return (
    <svg
      className={className}
      style={style}
      fill={fill}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64c.298-.002.595.027.886.088V9.4a6.33 6.33 0 00-1-.08 6.34 6.34 0 00-6.34 6.33A6.34 6.34 0 009.33 22a6.34 6.34 0 006.34-6.33V9.79a7.81 7.81 0 004.3 1.25V7.6a4.85 4.85 0 01-1 .15l-.02-.06z" />
    </svg>
  );
};

export default TikTokIcon; 