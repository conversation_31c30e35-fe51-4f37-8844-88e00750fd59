import { IconName } from './Icon';

export interface PropsSocial {
  id: string;
  name: string;
  url: string;
  iconName: IconName;
  color?: string;
}

export const Socials: PropsSocial[] = [
  {
    id: 'twitter',
    name: 'Twitter',
    url: 'integration/twitter',
    iconName: 'x', // Twitter is now X
    color: '#1DA1F2',
  },
  {
    id: 'facebook',
    name: 'Facebook',
    url: 'integration/facebook',
    iconName: 'facebook',
    color: '#1877F2',
  },
  {
    id: 'instagram',
    name: 'Instagram',
    url: 'integration/instagram',
    iconName: 'instagram',
    color: '#E4405F',
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    url: 'integration/linkedin',
    iconName: 'linkedin',
    color: '#0A66C2',
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    url: 'integration/tiktok',
    iconName: 'tiktok',
    color: '#000000',
  },
  {
    id: 'telegram',
    name: 'Telegram',
    url: 'integration/telegram',
    iconName: 'telegram',
    color: '#0088CC',
  },
  {
    id: 'threads',
    name: 'Threads',
    url: 'integration/threads',
    iconName: 'threads',
    color: '#000000',
  },
  {
    id: 'website',
    name: 'Website',
    url: 'integration/website',
    iconName: 'website',
    color: '#6B7280',
  },
];
