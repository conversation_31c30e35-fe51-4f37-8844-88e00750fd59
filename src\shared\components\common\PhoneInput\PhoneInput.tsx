import { forwardRef, useState, useMemo, useEffect, useRef } from 'react';
import { getCountries, getCountryCallingCode } from 'react-phone-number-input';
import type { Country } from 'react-phone-number-input';
import { useTranslation } from 'react-i18next';
import { clsx } from 'clsx';

// Define E164Number type locally since it's not exported
type E164Number = string;

// Country names mapping
const countryNames: Record<string, { en: string; vi: string }> = {
  VN: { en: 'Vietnam', vi: 'Việt Nam' },
  US: { en: 'United States', vi: 'Hoa Kỳ' },
  GB: { en: 'United Kingdom', vi: 'Vương quốc Anh' },
  CN: { en: 'China', vi: 'Trung Quốc' },
  JP: { en: 'Japan', vi: 'Nhật Bản' },
  KR: { en: 'South Korea', vi: 'Hàn Quốc' },
  TH: { en: 'Thailand', vi: 'Thái <PERSON>n' },
  SG: { en: 'Singapore', vi: 'Singapore' },
  MY: { en: 'Malaysia', vi: 'Malaysia' },
  ID: { en: 'Indonesia', vi: 'Indonesia' },
  PH: { en: 'Philippines', vi: 'Philippines' },
  IN: { en: 'India', vi: 'Ấn Độ' },
  AU: { en: 'Australia', vi: 'Úc' },
  CA: { en: 'Canada', vi: 'Canada' },
  FR: { en: 'France', vi: 'Pháp' },
  DE: { en: 'Germany', vi: 'Đức' },
  IT: { en: 'Italy', vi: 'Ý' },
  ES: { en: 'Spain', vi: 'Tây Ban Nha' },
  BR: { en: 'Brazil', vi: 'Brazil' },
  RU: { en: 'Russia', vi: 'Nga' },
};

// Helper function to get country flag
const getCountryFlag = (countryCode: Country): string => {
  const flagMap: Record<string, string> = {
    VN: '🇻🇳', US: '🇺🇸', GB: '🇬🇧', CN: '🇨🇳', JP: '🇯🇵',
    KR: '🇰🇷', TH: '🇹🇭', SG: '🇸🇬', MY: '🇲🇾', ID: '🇮🇩',
    PH: '🇵🇭', IN: '🇮🇳', AU: '🇦🇺', CA: '🇨🇦', FR: '🇫🇷',
    DE: '🇩🇪', IT: '🇮🇹', ES: '🇪🇸', BR: '🇧🇷', RU: '🇷🇺',
  };
  return flagMap[countryCode] || '🌍';
};

// Define option type locally
interface CountryOption {
  value: Country;
  label: string;
  icon: React.ReactNode;
}

export interface PhoneInputProps {
  value?: E164Number | undefined | null;
  onChange?: (value: E164Number | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  readOnly?: boolean;
  error?: boolean;
  fullWidth?: boolean;
  defaultCountry?: Country;
  international?: boolean;
  withCountryCallingCode?: boolean;
  className?: string;
  inputClassName?: string;
  countrySelectClassName?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  autoComplete?: string;
  name?: string;
  id?: string;
  'data-testid'?: string;
}

/**
 * PhoneInput component with country selector
 * Supports international phone number input with country selection
 */
const PhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  (
    {
      value,
      onChange,
      placeholder,
      disabled = false,
      readOnly = false,
      error = false,
      fullWidth = false,
      defaultCountry = 'VN' as Country,
      // international = false, // Unused variable - commented out
      // withCountryCallingCode = false, // Unused variable - commented out
      className,
      inputClassName,
      countrySelectClassName,
      onFocus,
      onBlur,
      autoComplete = 'tel',
      name,
      id,
      'data-testid': dataTestId,
      ...rest
    },
    ref
  ) => {
    const { t, i18n } = useTranslation(['common']);
    const currentLanguage = i18n.language as 'en' | 'vi';

    // State for country and phone number
    const [selectedCountry, setSelectedCountry] = useState<Country>(defaultCountry);
    const [phoneNumber, setPhoneNumber] = useState<string>('');
    const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
    const [countrySearchText, setCountrySearchText] = useState('');

    // Ref for dropdown container
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Parse existing value to extract country and number
    useMemo(() => {
      if (value) {
        // Simple parsing - in real app you might want to use a proper phone number parser
        if (value.startsWith('+')) {
          const match = value.match(/^\+(\d{1,4})/);
          if (match) {
            const callingCode = match[1];
            const number = value.substring(match[0].length);

            // Find country by calling code
            const countries = getCountries();
            const country = countries.find(c => getCountryCallingCode(c) === callingCode);
            if (country) {
              setSelectedCountry(country);
              setPhoneNumber(number);
              return;
            }
          }
        }
        setPhoneNumber(value);
      } else {
        setPhoneNumber('');
      }
    }, [value]);

    // Generate country options
    const countryOptions = useMemo((): CountryOption[] => {
      const countries = getCountries();
      return countries.map(country => {
        const callingCode = getCountryCallingCode(country);
        const countryName = countryNames[country]?.[currentLanguage] || country;

        return {
          value: country,
          label: `${countryName} (+${callingCode})`,
          icon: (
            <span className="text-lg leading-none">
              {getCountryFlag(country)}
            </span>
          ),
        };
      });
    }, [currentLanguage]);

    // Filter country options based on search text
    const filteredCountryOptions = useMemo(() => {
      if (!countrySearchText) return countryOptions;
      return countryOptions.filter(option =>
        option.label.toLowerCase().includes(countrySearchText.toLowerCase())
      );
    }, [countryOptions, countrySearchText]);

    // Handle click outside to close dropdown
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsCountryDropdownOpen(false);
          setCountrySearchText('');
        }
      };

      if (isCountryDropdownOpen) {
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
      }

      return undefined;
    }, [isCountryDropdownOpen]);

    // Handle country change
    const handleCountryChange = (countryCode: string | string[] | number | number[]) => {
      const newCountry = countryCode as Country;
      setSelectedCountry(newCountry);

      // Update the full phone number
      const callingCode = getCountryCallingCode(newCountry);
      const fullNumber = phoneNumber ? `+${callingCode}${phoneNumber}` : '';
      onChange?.(fullNumber as E164Number);
    };

    // Handle phone number change
    const handlePhoneChange = (newPhoneNumber: string) => {
      setPhoneNumber(newPhoneNumber);

      // Update the full phone number
      const callingCode = getCountryCallingCode(selectedCountry);
      const fullNumber = newPhoneNumber ? `+${callingCode}${newPhoneNumber}` : '';
      onChange?.(fullNumber as E164Number);
    };

    // Get display value for country select (unused but kept for potential future use)
    // const getCountryDisplayValue = () => {
    //   const callingCode = getCountryCallingCode(selectedCountry);
    //   const countryName = countryNames[selectedCountry]?.[currentLanguage] || selectedCountry;
    //   return withCountryCallingCode ? `+${callingCode}` : countryName;
    // };

    return (
      <div
        className={clsx(
          'phone-input-container',
          fullWidth && 'w-full',
          className
        )}
      >
        {/* Flex layout for country selector and phone input */}
        <div className="flex gap-2">
          {/* Country Selector - Custom Dropdown */}
          <div ref={dropdownRef} className={clsx('flex-shrink-0 relative', countrySelectClassName)}>
            <button
              type="button"
              onClick={() => !disabled && setIsCountryDropdownOpen(!isCountryDropdownOpen)}
              disabled={disabled}
              className={clsx(
                'flex items-center justify-center w-16 h-10 px-2 rounded-md bg-card-muted',
                'text-sm text-foreground hover:bg-muted/50',
                'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                'disabled:cursor-not-allowed disabled:opacity-50',
                isCountryDropdownOpen && 'ring-2 ring-ring ring-offset-2'
              )}
            >
              <span className="text-lg">{getCountryFlag(selectedCountry)}</span>
              <svg className={clsx('w-3 h-3 ml-1 transition-transform', isCountryDropdownOpen && 'rotate-180')} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Dropdown Menu */}
            {isCountryDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-80 bg-card-muted rounded-md shadow-lg z-50 max-h-60 overflow-auto">
                {/* Search Input */}
                <div className="p-2">
                  <input
                    type="text"
                    value={countrySearchText}
                    onChange={(e) => setCountrySearchText(e.target.value)}
                    placeholder={t('common:selectCountry')}
                    className="w-full px-3 py-2 text-sm rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>

                {/* Country Options */}
                <div className="py-1">
                  {filteredCountryOptions.map((option) => (
                    <button
                      key={option.value}
                      type="button"
                      onClick={() => {
                        handleCountryChange(option.value);
                        setIsCountryDropdownOpen(false);
                        setCountrySearchText('');
                      }}
                      className={clsx(
                        'w-full flex items-center px-3 py-2 text-sm text-left hover:bg-muted/50',
                        selectedCountry === option.value && 'bg-primary/10 text-primary'
                      )}
                    >
                      {option.icon}
                      <span className="ml-2">{option.label}</span>
                      {selectedCountry === option.value && (
                        <svg className="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </button>
                  ))}
                  {filteredCountryOptions.length === 0 && (
                    <div className="px-3 py-2 text-sm text-muted-foreground">
                      {t('common:noResults')}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Phone Number Input with Country Code */}
          <div className="flex-1">
            <div className="relative">
              <input
                ref={ref}
                type="tel"
                value={phoneNumber}
                onChange={(e) => handlePhoneChange(e.target.value)}
                placeholder={placeholder || t('common:phoneNumber')}
                disabled={disabled}
                readOnly={readOnly}
                autoComplete={autoComplete}
                name={name}
                id={id}
                data-testid={dataTestId}
                onFocus={onFocus}
                onBlur={onBlur}
                className={clsx(
                  'w-full pl-16 pr-3 py-2 rounded-md bg-card-muted',
                  'text-sm text-foreground placeholder:text-muted-foreground',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                  'disabled:cursor-not-allowed disabled:opacity-50',
                  inputClassName
                )}
                {...rest}
              />
              {/* Country code prefix */}
              <div className="absolute left-0 top-0 bottom-0 flex items-center px-3 bg-muted/30 rounded-l-md">
                <span className="text-lg mr-1">{getCountryFlag(selectedCountry)}</span>
                <span className="text-sm text-muted-foreground font-medium">
                  +{getCountryCallingCode(selectedCountry)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Error message */}
        {error && typeof error === 'string' && (
          <p className="mt-1 text-sm text-destructive">{error}</p>
        )}
      </div>
    );
  }
);

PhoneInput.displayName = 'PhoneInput';

export default PhoneInput;
