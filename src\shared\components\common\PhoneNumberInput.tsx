import React, { useState, useRef, useEffect } from 'react';
import Icon from './Icon';
import Input from './Input';
import CountryFlag from './CountryFlag';
import { COUNTRIES, Country, findCountryByCode, filterCountries, formatPhoneNumber, getFullPhoneNumber } from '@/shared/data/countries';

export interface PhoneNumberInputProps {
  value?: string;
  onChange?: (value: string, country: Country) => void;
  onCountryChange?: (country: Country) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  defaultCountry?: string; // Mã quốc gia mặc định (VN, US, etc.)
  className?: string;
  variant?: 'outlined' | 'filled' | 'standard';
  size?: 'small' | 'medium' | 'large';
  showDialCode?: boolean; // Hiển thị mã vùng trong input
}

const PhoneNumberInput: React.FC<PhoneNumberInputProps> = ({
  value = '',
  onChange,
  onCountryChange,
  placeholder = 'Phone number',
  disabled = false,
  error = false,
  defaultCountry = 'VN',
  className = '',
  variant = 'outlined',
  size = 'medium',
  showDialCode = true,
}) => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(() => {
    const foundCountry = findCountryByCode(defaultCountry);
    const firstCountry = COUNTRIES[0];
    return foundCountry || (firstCountry as Country);
  });
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [phoneNumber, setPhoneNumber] = useState(value);
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Đóng dropdown khi click bên ngoài
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Cập nhật phone number khi value prop thay đổi
  useEffect(() => {
    setPhoneNumber(value);
  }, [value]);

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setIsDropdownOpen(false);
    setSearchTerm('');
    onCountryChange?.(country);
    
    // Cập nhật số điện thoại với mã vùng mới
    if (phoneNumber) {
      const fullNumber = getFullPhoneNumber(phoneNumber, country);
      onChange?.(fullNumber, country);
    }
  };

  const handlePhoneNumberChange = (newValue: string) => {
    setPhoneNumber(newValue);
    
    if (showDialCode) {
      // Nếu hiển thị mã vùng, gửi số đầy đủ
      const fullNumber = getFullPhoneNumber(newValue, selectedCountry);
      onChange?.(fullNumber, selectedCountry);
    } else {
      // Nếu không hiển thị mã vùng, chỉ gửi số local
      const formattedNumber = formatPhoneNumber(newValue, selectedCountry);
      onChange?.(formattedNumber, selectedCountry);
    }
  };

  const filteredCountries = filterCountries(searchTerm);

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'h-8 text-sm';
      case 'large':
        return 'h-12 text-lg';
      default:
        return 'h-10 text-base';
    }
  };

  const getVariantClasses = () => {
    const baseClasses = 'border transition-colors duration-200 bg-background';

    switch (variant) {
      case 'filled':
        return `${baseClasses} bg-muted border-border focus-within:bg-background focus-within:border-primary`;
      case 'standard':
        return `${baseClasses} border-0 border-b-2 border-border rounded-none focus-within:border-primary`;
      default:
        return `${baseClasses} border-border rounded-md focus-within:border-primary focus-within:ring-1 focus-within:ring-primary/20`;
    }
  };

  const displayValue = showDialCode && phoneNumber 
    ? `${selectedCountry.dialCode} ${formatPhoneNumber(phoneNumber, selectedCountry)}`
    : formatPhoneNumber(phoneNumber, selectedCountry);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className={`flex ${getSizeClasses()} ${getVariantClasses()} ${error ? 'border-destructive' : ''} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
        {/* Country Selector */}
        <button
          type="button"
          onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
          disabled={disabled}
          className={`flex items-center px-3 border-r border-border hover:bg-muted focus:outline-none focus:bg-muted text-foreground ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
        >
          <CountryFlag
            country={selectedCountry}
            size="md"
            className="mr-2"
          />
          <Icon name="chevron-down" size="sm" className={`transition-transform text-muted-foreground ${isDropdownOpen ? 'rotate-180' : ''}`} />
        </button>

        {/* Phone Number Input */}
        <Input
          ref={inputRef}
          type="tel"
          value={displayValue}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handlePhoneNumberChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className="flex-1 border-0 focus:ring-0 focus:outline-none bg-transparent text-foreground placeholder:text-muted-foreground"
        />
      </div>

      {/* Dropdown */}
      {isDropdownOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-background border border-border rounded-md shadow-lg max-h-60 overflow-hidden">
          {/* Search */}
          <div className="p-2 border-b border-border">
            <Input
              type="text"
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
              placeholder="Search countries..."
              className="w-full bg-background text-foreground placeholder:text-muted-foreground"
            />
          </div>

          {/* Countries List */}
          <div className="overflow-y-auto max-h-48">
            {filteredCountries.length > 0 ? (
              filteredCountries.map((country) => (
                <button
                  key={country.code}
                  type="button"
                  onClick={() => handleCountrySelect(country)}
                  className={`w-full flex items-center px-3 py-2 text-left hover:bg-muted focus:outline-none focus:bg-muted transition-colors ${
                    selectedCountry.code === country.code ? 'bg-primary/10 text-primary' : 'text-foreground'
                  }`}
                >
                  <CountryFlag
                    country={country}
                    size="sm"
                    className="mr-3"
                  />
                  <span className="flex-1 truncate">{country.name}</span>
                  <span className="text-muted-foreground ml-2 text-sm flex-shrink-0">{country.dialCode}</span>
                </button>
              ))
            ) : (
              <div className="px-3 py-2 text-muted-foreground text-center">
                No countries found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PhoneNumberInput;
