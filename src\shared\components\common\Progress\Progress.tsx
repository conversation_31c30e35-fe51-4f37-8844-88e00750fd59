import React from 'react';

export interface ProgressProps {
  /**
   * <PERSON><PERSON><PERSON> trị hiện tại (0-100)
   */
  value: number;

  /**
   * <PERSON>i<PERSON> trị tối đa
   */
  max?: number;

  /**
   * <PERSON><PERSON><PERSON> thước của progress bar
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * <PERSON><PERSON><PERSON> sắc của progress bar
   */
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';

  /**
   * <PERSON><PERSON><PERSON> hiển thị
   */
  variant?: 'default' | 'striped' | 'animated';

  /**
   * Hiển thị phần trăm
   */
  showPercent?: boolean;

  /**
   * Hiển thị label
   */
  label?: string;

  /**
   * Vị trí của label
   */
  labelPosition?: 'top' | 'bottom' | 'inside';

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Class cho container
   */
  containerClassName?: string;
}

/**
 * Progress component - thanh tiến trình
 */
const Progress: React.FC<ProgressProps> = ({
  value,
  max = 100,
  size = 'md',
  color = 'primary',
  variant = 'default',
  showPercent = false,
  label,
  labelPosition = 'top',
  className = '',
  containerClassName = '',
}) => {
  // Tính phần trăm
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  // Size classes
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  // Color classes
  const colorClasses = {
    primary: 'bg-primary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    danger: 'bg-red-500',
    info: 'bg-blue-500',
  };

  // Variant classes
  const variantClasses = {
    default: '',
    striped: 'bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:1rem_1rem]',
    animated: 'bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:1rem_1rem] animate-pulse',
  };

  const progressBarClasses = `
    ${sizeClasses[size]}
    ${colorClasses[color]}
    ${variantClasses[variant]}
    transition-all duration-300 ease-in-out
    rounded-full
    ${className}
  `;

  const containerClasses = `
    w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden
    ${containerClassName}
  `;

  const labelClasses = 'text-sm font-medium text-gray-700 dark:text-gray-300';

  const renderLabel = () => {
    if (!label && !showPercent) return null;

    const labelText = label || '';
    const percentText = showPercent ? `${Math.round(percentage)}%` : '';
    const displayText = label && showPercent ? `${labelText} (${percentText})` : labelText || percentText;

    return (
      <div className={labelClasses}>
        {displayText}
      </div>
    );
  };

  const renderProgress = () => (
    <div className={containerClasses}>
      <div
        className={progressBarClasses}
        style={{ width: `${percentage}%` }}
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={label}
      >
        {labelPosition === 'inside' && (
          <div className="flex items-center justify-center h-full text-xs font-medium text-white">
            {showPercent && `${Math.round(percentage)}%`}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="w-full">
      {labelPosition === 'top' && renderLabel()}
      {renderProgress()}
      {labelPosition === 'bottom' && renderLabel()}
    </div>
  );
};

export default Progress;
