import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { IconCard } from '@/shared/components/common';

interface RandomColorIconCardProps {
  onColorGenerated?: (color: string) => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'ghost' | 'primary' | 'secondary' | 'danger';
  className?: string;
  disabled?: boolean;
}

// Hàm generate màu ngẫu nhiên với độ sáng phù hợp
const generateRandomColor = (): string => {
  // Tạo màu với HSL để kiểm soát độ sáng và độ bão hòa
  const hue = Math.floor(Math.random() * 360);
  const saturation = Math.floor(Math.random() * 40) + 60; // 60-100%
  const lightness = Math.floor(Math.random() * 30) + 40; // 40-70%
  
  // Chuyển đổi HSL sang HEX
  const hslToHex = (h: number, s: number, l: number): string => {
    l /= 100;
    const a = s * Math.min(l, 1 - l) / 100;
    const f = (n: number) => {
      const k = (n + h / 30) % 12;
      const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
      return Math.round(255 * color).toString(16).padStart(2, '0');
    };
    return `#${f(0)}${f(8)}${f(4)}`;
  };
  
  return hslToHex(hue, saturation, lightness);
};

// Danh sách màu preset đẹp
const BEAUTIFUL_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
  '#A3E4D7', '#F9E79F', '#FADBD8', '#D5DBDB', '#AED6F1',
  '#FF8A80', '#82B1FF', '#B388FF', '#8C9EFF', '#80D8FF',
  '#84FFFF', '#A7FFEB', '#B9F6CA', '#CCFF90', '#F4FF81',
  '#FFFF8D', '#FFE57F', '#FFD180', '#FFAB91', '#BCAAA4'
];

/**
 * Component IconCard với chức năng generate màu ngẫu nhiên
 */
const RandomColorIconCard: React.FC<RandomColorIconCardProps> = ({
  onColorGenerated,
  size = 'sm',
  variant = 'default',
  className = '',
  disabled = false,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [currentColor, setCurrentColor] = useState<string>('#FF6B6B');
  const [isGenerating, setIsGenerating] = useState(false);

  // Xử lý generate màu ngẫu nhiên
  const handleGenerateColor = useCallback(async () => {
    if (disabled || isGenerating) return;

    setIsGenerating(true);
    
    // Animation effect
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Random choice: 70% từ preset, 30% generate random
    const usePreset = Math.random() < 0.7;
    let newColor: string;
    
    if (usePreset) {
      // Chọn màu từ preset
      const selectedColor = BEAUTIFUL_COLORS[Math.floor(Math.random() * BEAUTIFUL_COLORS.length)];
      newColor = selectedColor || BEAUTIFUL_COLORS[0] || '#FF6B6B';
    } else {
      // Generate màu ngẫu nhiên
      newColor = generateRandomColor();
    }

    // Đảm bảo không trùng màu hiện tại
    if (newColor === currentColor) {
      const fallbackColor = BEAUTIFUL_COLORS[Math.floor(Math.random() * BEAUTIFUL_COLORS.length)];
      newColor = fallbackColor || BEAUTIFUL_COLORS[0] || '#FF6B6B';
    }
    
    setCurrentColor(newColor);
    onColorGenerated?.(newColor);
    setIsGenerating(false);
  }, [currentColor, disabled, isGenerating, onColorGenerated]);

  return (
    <div className={`relative ${className}`}>
      <IconCard
        icon={isGenerating ? 'loading' : 'zap'}
        size={size}
        variant={variant}
        onClick={handleGenerateColor}
        disabled={disabled}
        title={t('marketing:tags.form.randomColor')}
        className={`transition-all duration-200 ${isGenerating ? '' : 'hover:scale-105'}`}
      />

      {/* Color preview dot */}
      <div
        className="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-background shadow-sm"
        style={{ backgroundColor: currentColor }}
      />
    </div>
  );
};

export default RandomColorIconCard;
