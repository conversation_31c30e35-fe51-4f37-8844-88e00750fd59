import { LucideIcon } from 'lucide-react';
import { Card, Typography, Skeleton } from '@/shared/components/common';

/**
 * Props cho StatsCard
 */
interface StatsCardProps {
  /**
   * Tiêu đề của card
   */
  title: string;
  
  /**
   * <PERSON><PERSON><PERSON> trị hiển thị
   */
  value: number | string;
  
  /**
   * <PERSON>ô tả phụ
   */
  subtitle: string;
  
  /**
   * Icon hiển thị
   */
  icon: LucideIcon;
  
  /**
   * Màu của icon và text
   */
  color: 'blue' | 'orange' | 'green' | 'purple' | 'red' | 'yellow' | 'indigo' | 'pink';
  
  /**
   * Trạng thái loading
   */
  isLoading?: boolean;

  /**
   * Class name bổ sung
   */
  className?: string;
}

/**
 * Component card thống kê dùng chung theo quy tắc RedAI
 */
export function StatsCard({
  title,
  value,
  subtitle,
  icon: Icon,
  color,
  isLoading = false,
  className = '',
}: StatsCardProps) {
  const colorClasses = {
    blue: {
      icon: 'text-blue-600',
      text: 'text-blue-600',
    },
    orange: {
      icon: 'text-orange-600',
      text: 'text-orange-600',
    },
    green: {
      icon: 'text-green-600',
      text: 'text-green-600',
    },
    purple: {
      icon: 'text-purple-600',
      text: 'text-purple-600',
    },
    red: {
      icon: 'text-red-600',
      text: 'text-red-600',
    },
    yellow: {
      icon: 'text-yellow-600',
      text: 'text-yellow-600',
    },
    indigo: {
      icon: 'text-indigo-600',
      text: 'text-indigo-600',
    },
    pink: {
      icon: 'text-pink-600',
      text: 'text-pink-600',
    },
  };

  const colorClass = colorClasses[color];

  return (
    <Card className={`p-4 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <Typography variant="caption" color="muted">
          {title}
        </Typography>
        <Icon className={`h-4 w-4 ${colorClass.icon}`} />
      </div>
      
      {isLoading ? (
        <Skeleton className="h-8 w-16 mb-2" />
      ) : (
        <Typography variant="h3" className={colorClass.text}>
          {value}
        </Typography>
      )}
      
      <Typography variant="caption" color="muted" className="mt-1">
        {subtitle}
      </Typography>
    </Card>
  );
}

export default StatsCard;
