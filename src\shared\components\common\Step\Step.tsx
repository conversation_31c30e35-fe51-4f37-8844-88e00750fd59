import React from 'react';
import { Typography, Icon } from '@/shared/components/common';

export interface StepProps {
  title: string;
  description?: string;
  status?: 'pending' | 'active' | 'completed' | 'error';
  isOptional?: boolean;
  onClick?: () => void;
  className?: string;
}

/**
 * Step component for wizard interfaces
 * 
 * @example
 * <Step
 *   title="Configuration"
 *   description="Set up your provider"
 *   status="active"
 *   onClick={() => handleStepClick()}
 * />
 */
const Step: React.FC<StepProps> = ({
  title,
  description,
  status = 'pending',
  isOptional = false,
  onClick,
  className = ''
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <Icon name="check" className="w-4 h-4 text-green-500" />;
      case 'active':
        return <Icon name="loader" className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'error':
        return <Icon name="x" className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStatusClasses = () => {
    switch (status) {
      case 'completed':
        return 'text-green-700 border-green-200 bg-green-50';
      case 'active':
        return 'text-blue-700 border-blue-200 bg-blue-50';
      case 'error':
        return 'text-red-700 border-red-200 bg-red-50';
      default:
        return 'text-gray-600 border-gray-200 bg-gray-50';
    }
  };

  return (
    <div
      className={`
        flex flex-col items-center p-4 rounded-lg border-2 transition-all duration-200
        ${getStatusClasses()}
        ${onClick ? 'cursor-pointer hover:shadow-md' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      {/* Status Icon */}
      <div className="mb-2">
        {getStatusIcon()}
      </div>

      {/* Title */}
      <Typography variant="subtitle2" className="text-center font-medium mb-1">
        {title}
        {isOptional && (
          <span className="text-xs text-muted-foreground ml-1">
            (Optional)
          </span>
        )}
      </Typography>

      {/* Description */}
      {description && (
        <Typography variant="caption" className="text-center text-muted-foreground">
          {description}
        </Typography>
      )}
    </div>
  );
};

export default Step;
