import React from 'react';

export interface SwitchProps {
  /**
   * Tr<PERSON><PERSON> thái checked của switch
   */
  checked?: boolean;

  /**
   * Tr<PERSON><PERSON> thái checked mặc định
   */
  defaultChecked?: boolean;

  /**
   * Callback khi thay đổi trạng thái
   */
  onChange?: (checked: boolean) => void;

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * K<PERSON>ch thước của switch
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * <PERSON>àu sắc của switch
   */
  color?: 'primary' | 'success' | 'warning' | 'danger';

  /**
   * Label hiển thị bên cạnh switch
   */
  label?: string;

  /**
   * Vị trí của label
   */
  labelPosition?: 'left' | 'right';

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * ID của switch
   */
  id?: string;

  /**
   * Name của switch (cho form)
   */
  name?: string;
}

/**
 * Switch component - toggle switch
 */
const Switch: React.FC<SwitchProps> = ({
  checked,
  defaultChecked = false,
  onChange,
  disabled = false,
  size = 'md',
  color = 'primary',
  label,
  labelPosition = 'right',
  className = '',
  id,
  name,
}) => {
  const [internalChecked, setInternalChecked] = React.useState(defaultChecked);
  
  const isChecked = checked !== undefined ? checked : internalChecked;

  const handleChange = () => {
    if (disabled) return;
    
    const newChecked = !isChecked;
    if (checked === undefined) {
      setInternalChecked(newChecked);
    }
    onChange?.(newChecked);
  };

  // Size classes
  const sizeClasses = {
    sm: {
      switch: 'w-8 h-4',
      thumb: 'w-3 h-3',
      translate: 'translate-x-4',
    },
    md: {
      switch: 'w-10 h-5',
      thumb: 'w-4 h-4',
      translate: 'translate-x-5',
    },
    lg: {
      switch: 'w-12 h-6',
      thumb: 'w-5 h-5',
      translate: 'translate-x-6',
    },
  };

  // Color classes
  const colorClasses = {
    primary: 'bg-primary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    danger: 'bg-red-500',
  };

  const switchClasses = `
    relative inline-flex items-center ${sizeClasses[size].switch}
    rounded-full transition-colors duration-200 ease-in-out
    ${isChecked ? colorClasses[color] : 'bg-gray-300 dark:bg-gray-600'}
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    ${className}
  `;

  const thumbClasses = `
    inline-block ${sizeClasses[size].thumb}
    bg-white rounded-full shadow-lg transform transition-transform duration-200 ease-in-out
    ${isChecked ? sizeClasses[size].translate : 'translate-x-0.5'}
  `;

  const labelClasses = `
    text-sm font-medium text-gray-700 dark:text-gray-300
    ${disabled ? 'opacity-50' : ''}
  `;

  const renderSwitch = () => (
    <button
      type="button"
      className={switchClasses}
      onClick={handleChange}
      disabled={disabled}
      id={id}
      name={name}
      role="switch"
      aria-checked={isChecked}
      aria-disabled={disabled}
    >
      <span className={thumbClasses} />
    </button>
  );

  if (!label) {
    return renderSwitch();
  }

  return (
    <div className="flex items-center space-x-2">
      {labelPosition === 'left' && (
        <label htmlFor={id} className={labelClasses}>
          {label}
        </label>
      )}
      {renderSwitch()}
      {labelPosition === 'right' && (
        <label htmlFor={id} className={labelClasses}>
          {label}
        </label>
      )}
    </div>
  );
};

export default Switch;
