import React, { useState, useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { debounce } from 'lodash';
import { Loader2 } from 'lucide-react';
import { Icon, SelectOption } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import { TagService } from '@/modules/marketing/services/tag.service';
import { Tag, TagStatus } from '@/modules/marketing/types/tag.types';

export interface TagsSelectWithPaginationProps {
  /**
   * Giá trị đã chọn (array of tag IDs)
   */
  value?: number[];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: number[]) => void;

  /**
   * Thời gian debounce cho search (ms)
   */
  debounceTime?: number;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Name attribute
   */
  name?: string;

  /**
   * ID attribute
   */
  id?: string;

  /**
   * CSS class
   */
  className?: string;

  /**
   * Error message
   */
  error?: string;

  /**
   * Helper text
   */
  helperText?: string;

  /**
   * Size
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Full width
   */
  fullWidth?: boolean;

  /**
   * Số items mỗi trang
   */
  itemsPerPage?: number;

  /**
   * Tự động load trang đầu tiên khi mở
   */
  autoLoadInitial?: boolean;

  /**
   * Message khi không có options
   */
  noOptionsMessage?: string;

  /**
   * Message khi đang loading
   */
  loadingMessage?: string;

  /**
   * Search chỉ khi nhấn Enter
   */
  searchOnEnter?: boolean;
}

/**
 * Component TagsSelectWithPagination - Select tags với khả năng tải dữ liệu từ API có phân trang
 */
const TagsSelectWithPagination = forwardRef<HTMLInputElement, TagsSelectWithPaginationProps>(
  (
    {
      value = [],
      onChange,
      debounceTime = 300,
      placeholder = '',
      label,
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      itemsPerPage = 20,
      autoLoadInitial = true,
      noOptionsMessage,
      loadingMessage,
      searchOnEnter = true,
    },
    ref
  ) => {
    useTheme(); // Keep the hook call to avoid React hooks rules violation
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [options, setOptions] = useState<Tag[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [pendingSearchTerm, setPendingSearchTerm] = useState('');
    const lastSearchTermRef = useRef('');
    const hasLoadedInitialRef = useRef(false);
    const [hasSearched, setHasSearched] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(false);
    const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
    const [dropdownPosition, setDropdownPosition] = useState<{
      top: number;
      left: number;
      width: number;
    } | null>(null);

    const selectRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Forward ref to hidden input element
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update selectedTags when value prop changes
    useEffect(() => {
      if (value && value.length > 0) {
        // Find selected tags from current options or keep existing selectedTags
        const newSelectedTags = value.map(tagId => {
          const existingTag = selectedTags.find(tag => tag.id === tagId);
          if (existingTag) return existingTag;

          const optionTag = options.find(tag => tag.id === tagId);
          if (optionTag) return optionTag;

          // If tag not found, create a placeholder
          return { id: tagId, name: `Tag ${tagId}`, status: TagStatus.ACTIVE, createdAt: '', updatedAt: '' };
        });
        setSelectedTags(newSelectedTags);
      } else {
        setSelectedTags([]);
      }
    }, [value, options, selectedTags]);

    // Load data function
    const loadDataRef = useRef<(params: {
      search?: string;
      page?: number;
      reset?: boolean;
    }) => Promise<void>>();

    loadDataRef.current = async (params: {
      search?: string;
      page?: number;
      reset?: boolean;
    }) => {
      const { search = '', page = 1, reset = false } = params;

      // Tránh gọi API với cùng search term và page = 1 (trừ khi reset = true)
      if (page === 1 && !reset && search === lastSearchTermRef.current) {
        return;
      }

      // Nếu đang loading và là cùng một search term, không gọi API
      if (loading && page === 1 && search === searchTerm) {
        return;
      }

      // Cập nhật last search term khi search mới (page = 1)
      if (page === 1) {
        lastSearchTermRef.current = search;
        setHasSearched(true);
      }

      setLoading(true);
      try {
        const result = await TagService.getTags({
          search,
          page,
          limit: itemsPerPage,
        });

        const tags = result.result?.items || [];

        if (reset || page === 1) {
          setOptions(tags);
        } else {
          setOptions(prev => [...prev, ...tags]);
        }

        const meta = result.result?.meta;
        if (meta) {
          setCurrentPage(meta.currentPage);
          setHasMore(meta.currentPage < meta.totalPages);
        }
      } catch (error) {
        console.error('Error loading tags:', error);
        if (reset || page === 1) {
          setOptions([]);
        }
      } finally {
        setLoading(false);
      }
    };

    // Create debounced search function
    const debouncedSearchRef = useRef<ReturnType<typeof debounce>>();

    useEffect(() => {
      debouncedSearchRef.current?.cancel();

      debouncedSearchRef.current = debounce((search: string) => {
        if (!searchOnEnter) {
          if (search !== searchTerm) {
            setSearchTerm(search);
            setCurrentPage(1);
            loadDataRef.current?.({ search, page: 1, reset: true });
          }
        }
      }, debounceTime);

      return () => {
        debouncedSearchRef.current?.cancel();
      };
    }, [debounceTime, searchOnEnter, searchTerm]);

    // Handle search input change
    const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setPendingSearchTerm(value);

      if (!searchOnEnter && debouncedSearchRef.current) {
        debouncedSearchRef.current(value);
      }
    };

    // Handle search on Enter
    const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && searchOnEnter) {
        e.preventDefault();

        if (pendingSearchTerm !== searchTerm) {
          setSearchTerm(pendingSearchTerm);
          setCurrentPage(1);
          loadDataRef.current?.({ search: pendingSearchTerm, page: 1, reset: true });
        }
      }
    };

    // Load more data when scrolling
    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

      if (
        scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
        hasMore &&
        !loading
      ) {
        loadDataRef.current?.({ search: searchTerm, page: currentPage + 1 });
      }
    }, [hasMore, loading, searchTerm, currentPage]);

    // Calculate dropdown position when opening
    const calculateDropdownPosition = useCallback(() => {
      if (!selectRef.current) return null;

      const rect = selectRef.current.getBoundingClientRect();
      const scrollTop = window.scrollY || document.documentElement.scrollTop;
      const scrollLeft = window.scrollX || document.documentElement.scrollLeft;

      return {
        top: rect.bottom + scrollTop + 4, // 4px gap
        left: rect.left + scrollLeft,
        width: rect.width,
      };
    }, []);

    // Handle option click
    const handleOptionClick = (tag: Tag) => {
      const isSelected = selectedTags.some(selectedTag => selectedTag.id === tag.id);
      let newSelectedTags: Tag[];

      if (isSelected) {
        // Remove tag
        newSelectedTags = selectedTags.filter(selectedTag => selectedTag.id !== tag.id);
      } else {
        // Add tag
        newSelectedTags = [...selectedTags, tag];
      }

      setSelectedTags(newSelectedTags);

      // Call onChange with the new tag IDs
      if (onChange) {
        onChange(newSelectedTags.map(tag => tag.id));
      }

      // Clear search input when option is selected và reset search
      setPendingSearchTerm('');

      // Reset search term về empty để load lại data ban đầu
      if (searchTerm !== '') {
        setSearchTerm('');
        setCurrentPage(1);
        lastSearchTermRef.current = '';
        loadDataRef.current?.({ search: '', page: 1, reset: true });
      }
    };

    // Remove selected tag
    const handleRemoveTag = (tagId: number) => {
      const newSelectedTags = selectedTags.filter(tag => tag.id !== tagId);
      setSelectedTags(newSelectedTags);

      if (onChange) {
        onChange(newSelectedTags.map(tag => tag.id));
      }
    };

    // Get display value
    const getDisplayValue = () => {
      if (selectedTags.length === 0) return placeholder;
      return t('common.selected', { count: selectedTags.length });
    };

    // Get appropriate no results message and icon based on current state
    const getNoResultsContent = () => {
      // If user has searched and there's a search term
      if (hasSearched && (searchTerm.trim() || pendingSearchTerm.trim())) {
        const currentSearchTerm = searchTerm.trim() || pendingSearchTerm.trim();
        return {
          icon: '🔍',
          message: t('common.noSearchResults', `Không tìm thấy kết quả cho "${currentSearchTerm}"`),
          subMessage: t('common.tryDifferentSearch', 'Thử tìm kiếm với từ khóa khác'),
          className: 'text-muted-foreground'
        };
      }

      // If user has searched but with empty term (showing all data but no results)
      if (hasSearched && !searchTerm.trim() && !pendingSearchTerm.trim()) {
        return {
          icon: '📭',
          message: t('common.noDataAvailable', 'Không có dữ liệu'),
          subMessage: t('common.noDataDescription', 'Hiện tại chưa có dữ liệu nào'),
          className: 'text-muted-foreground'
        };
      }

      // Default message (initial state or custom message)
      return {
        icon: '📋',
        message: noOptionsMessage || t('common.noResults', 'Không có kết quả'),
        subMessage: t('common.startTyping', 'Bắt đầu nhập để tìm kiếm'),
        className: 'text-muted-foreground'
      };
    };

    // Render single option
    const renderSingleOption = (tag: Tag) => {
      const isSelected = selectedTags.some(selectedTag => selectedTag.id === tag.id);

      return (
        <SelectOption
          key={`option-${tag.id}`}
          value={tag.id}
          label={tag.name}
          disabled={false}
          selected={isSelected}
          onClick={() => handleOptionClick(tag)}
          data={tag as unknown as Record<string, unknown>}
        />
      );
    };

    // Close dropdown when clicking outside and handle scroll
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node;

        // Check if click is outside both select trigger and dropdown
        const isOutsideSelect = selectRef.current && !selectRef.current.contains(target);
        const isOutsideDropdown = !document.querySelector('.tags-select-dropdown')?.contains(target);

        if (isOutsideSelect && isOutsideDropdown) {
          setIsOpen(false);
        }
      };

      const handleScroll = () => {
        if (isOpen) {
          const position = calculateDropdownPosition();
          setDropdownPosition(position);
        }
      };

      const handleResize = () => {
        if (isOpen) {
          const position = calculateDropdownPosition();
          setDropdownPosition(position);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('scroll', handleScroll, true);
        window.removeEventListener('resize', handleResize);
      };
    }, [isOpen, calculateDropdownPosition]);

    // Focus input when dropdown opens and calculate position
    useEffect(() => {
      if (isOpen) {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
        const position = calculateDropdownPosition();
        setDropdownPosition(position);

        // Load initial data if needed
        if (autoLoadInitial && options.length === 0 && !hasLoadedInitialRef.current) {
          hasLoadedInitialRef.current = true;
          loadDataRef.current?.({ search: '', page: 1, reset: true });
        }
      } else {
        setDropdownPosition(null);
        hasLoadedInitialRef.current = false;
        setHasSearched(false);
      }
    }, [isOpen, calculateDropdownPosition, autoLoadInitial, options.length]);

    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={selectedTags.map(tag => tag.id).join(',')}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium mb-1">{label}</label>}

        {/* Select trigger */}
        <div
          className={`
          flex items-center justify-between px-3
          border-0 rounded-md bg-card-muted text-foreground
          ${sizeClasses}
          ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'ring-1 ring-error' : ''}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
        `}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex-grow truncate">{getDisplayValue()}</div>

          <div className="flex items-center">
            {loading ? (
              <Icon name="loading" className="animate-spin" size="sm" />
            ) : (
              <svg
                className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            )}
          </div>
        </div>

        {/* Selected tags display */}
        {selectedTags.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-1">
            {selectedTags.map((tag) => (
              <div
                key={tag.id}
                className="inline-flex items-center gap-1 px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-sm"
              >
                <span>{tag.name}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveTag(tag.id)}
                  className="ml-1 text-secondary-foreground/60 hover:text-secondary-foreground transition-colors"
                  aria-label={`Remove ${tag.name}`}
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Error message */}
        {error && (
          <p className="mt-1 text-sm text-destructive">{error}</p>
        )}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-muted-foreground">{helperText}</p>
        )}

        {/* Dropdown Portal */}
        {isOpen && dropdownPosition && createPortal(
          <div
            className="tags-select-dropdown fixed z-[99999] bg-card rounded-md shadow-lg border-0 animate-fade-in"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
              maxHeight: '300px',
            }}
          >
            {/* Search input */}
            <div className="sticky top-0 p-2 bg-card border-b-0">
              <div className="relative">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={pendingSearchTerm}
                  onChange={handleSearchInputChange}
                  onKeyDown={handleSearchKeyDown}
                  placeholder={
                    searchOnEnter
                      ? t('common.searchPressEnter', 'Tìm kiếm... (Nhấn Enter)')
                      : t('common.search', 'Tìm kiếm...')
                  }
                  className="w-full px-3 py-1 pr-8 text-sm border-0 rounded-md focus:outline-none focus:ring-1 focus:ring-primary/20 bg-card-muted text-foreground"
                  onClick={e => e.stopPropagation()}
                />
                {/* Clear search button */}
                {(pendingSearchTerm || searchTerm) && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPendingSearchTerm('');
                      setSearchTerm('');
                      setCurrentPage(1);
                      lastSearchTermRef.current = '';
                      loadDataRef.current?.({ search: '', page: 1, reset: true });
                    }}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* Options */}
            <div
              className="max-h-60 overflow-auto custom-scrollbar auto-hide"
              role="listbox"
              aria-multiselectable={true}
              onScroll={handleScroll}
            >
              {loading && options.length === 0 ? (
                <div className="px-4 py-2 text-sm text-muted flex items-center">
                  <Icon name="loading" className="animate-spin mr-2" size="sm" />
                  {loadingMessage || t('common.loading', 'Loading...')}
                </div>
              ) : options.length > 0 ? (
                <>
                  {options.map(tag => renderSingleOption(tag))}

                  {/* Loading more indicator */}
                  {loading && options.length > 0 && (
                    <div className="flex items-center justify-center p-2 border-t">
                      <Loader2 size={14} className="animate-spin mr-2" />
                      <span className="text-xs text-muted-foreground">
                        {t('common.loadingMore', 'Đang tải thêm...')}
                      </span>
                    </div>
                  )}
                </>
              ) : (
                (() => {
                  const noResultsContent = getNoResultsContent();
                  return (
                    <div className={`px-4 py-4 text-sm ${noResultsContent.className} text-center`}>
                      <div className="flex flex-col items-center space-y-2">
                        <span className="text-2xl opacity-50">{noResultsContent.icon}</span>
                        <div className="space-y-1">
                          <div className="font-medium">{noResultsContent.message}</div>
                          {noResultsContent.subMessage && (
                            <div className="text-xs opacity-75">{noResultsContent.subMessage}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })()
              )}
            </div>
          </div>,
          document.body
        )}
      </div>
    );
  }
);

TagsSelectWithPagination.displayName = 'TagsSelectWithPagination';

export default TagsSelectWithPagination;
