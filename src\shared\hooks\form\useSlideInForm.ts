import { useState } from 'react';

/**
 * Hook quản lý trạng thái hiển thị/ẩn SlideInForm
 * @returns Các hàm và state để quản lý form
 */
export const useSlideInForm = (initialState = false) => {
  // State quản lý việc hiển thị form
  const [isOpen, setIsOpen] = useState(initialState);

  // Hàm mở form
  const openForm = () => setIsOpen(true);

  // Hàm đóng form
  const closeForm = () => setIsOpen(false);

  // Hàm toggle form
  const toggleForm = () => setIsOpen(prev => !prev);

  return {
    isOpen,
    openForm,
    closeForm,
    toggleForm,
  };
};

export default useSlideInForm;
