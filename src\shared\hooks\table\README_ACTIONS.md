# Table Actions Hooks

Bộ hooks tái sử dụng cho các thao tác CRUD và bulk operations trong table.

## Hooks

### 1. `useBulkDelete`

Hook xử lý logic xóa nhiều items cùng lúc.

```typescript
import { useBulkDelete } from '@/shared/hooks/table';

const bulkDelete = useBulkDelete({
  deleteMutation: async (ids: string[]) => {
    await api.deleteMultiple({ ids });
  },
  i18nNamespace: 'data',
  entityName: 'URL',
  onSuccess: (count) => console.log(`Deleted ${count} items`),
});
```

### 2. `useSingleDelete`

Hook xử lý logic xóa một item.

```typescript
import { useSingleDelete } from '@/shared/hooks/table';

const singleDelete = useSingleDelete({
  deleteMutation: async (id: string) => {
    await api.delete(id);
  },
  i18nNamespace: 'data',
  entityName: 'URL',
});
```

### 3. `useTableActions`

Hook tổng hợp tất cả actions cho table.

```typescript
import { useTableActions } from '@/shared/hooks/table';

const tableActions = useTableActions({
  singleDelete: {
    deleteMutation: deleteOne,
    entityName: 'URL',
  },
  bulkDelete: {
    deleteMutation: deleteMultiple,
    entityName: 'URL',
  },
  onEdit: (item) => setEditingItem(item),
  onView: (item) => setViewingItem(item),
});
```

## Component

### `DataTableWithActions`

Component table đầy đủ tính năng với CRUD operations.

```typescript
import { DataTableWithActions } from '@/shared/components/common';

<DataTableWithActions
  data={data}
  loading={loading}
  columns={columns}
  dataTableConfig={dataTableConfig}
  actionsConfig={{
    onEdit: handleEdit,
    singleDelete: {
      deleteMutation: deleteUrl,
      entityName: 'URL',
    },
    bulkDelete: {
      deleteMutation: deleteMultipleUrls,
      entityName: 'URL',
    },
  }}
  pagination={pagination}
  onAdd={showAddForm}
/>
```

## Tính năng

### Bulk Delete
- ✅ State management cho selected items
- ✅ Validation trước khi xóa
- ✅ Modal xác nhận với số lượng chính xác
- ✅ Error handling
- ✅ Success notifications
- ✅ Auto reset selection sau khi xóa

### Single Delete
- ✅ Modal xác nhận xóa
- ✅ Error handling
- ✅ Success notifications

### Table Integration
- ✅ Row selection
- ✅ Action menu cho mỗi row
- ✅ Bulk action buttons
- ✅ Column visibility
- ✅ Search & filters

## Lợi ích

1. **Tái sử dụng**: Dùng được cho nhiều entity khác nhau
2. **Type Safety**: Full TypeScript support
3. **Consistent UX**: UI/UX nhất quán across app
4. **Error Handling**: Xử lý lỗi tự động
5. **I18n Support**: Hỗ trợ đa ngôn ngữ
6. **Customizable**: Có thể customize messages và behaviors

## Migration Guide

### Từ UrlPage cũ sang DataTableWithActions

```typescript
// Cũ - 300+ lines code
const UrlPage = () => {
  const [selectedRows, setSelectedRows] = useState([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  // ... 200+ lines logic
  
  return (
    <div>
      <MenuIconBar />
      <Table />
      <ConfirmDeleteModal />
      <ConfirmDeleteModal />
    </div>
  );
};

// Mới - 100 lines code
const UrlPageRefactored = () => {
  return (
    <DataTableWithActions
      data={data}
      columns={columns}
      actionsConfig={{
        singleDelete: { deleteMutation: deleteUrl },
        bulkDelete: { deleteMutation: deleteMultiple },
      }}
    />
  );
};
```

### Giảm code từ 300+ lines xuống 100 lines (66% reduction)
