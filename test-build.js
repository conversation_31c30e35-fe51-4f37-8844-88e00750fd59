// Simple test to check if our Calendar components can be imported without errors
try {
  console.log('Testing Calendar component imports...');
  
  // Test basic imports
  const Calendar = require('./src/shared/components/common/DatePicker/Calendar.tsx');
  console.log('✅ Calendar imported successfully');
  
  const MultiSelectCalendar = require('./src/shared/components/common/DatePicker/MultiSelectCalendar.tsx');
  console.log('✅ MultiSelectCalendar imported successfully');
  
  const EventCalendar = require('./src/shared/components/common/DatePicker/EventCalendar.tsx');
  console.log('✅ EventCalendar imported successfully');
  
  console.log('🎉 All Calendar components can be imported successfully!');
  
} catch (error) {
  console.error('❌ Error importing Calendar components:', error.message);
  process.exit(1);
}
