/**
 * <PERSON>ript để tạo template test trong browser console
 * Chạy trong DevTools Console trên trang /marketing/email/templates
 */

// Test data cho tạo template
const testTemplateData = {
  name: "Test Template for Edit",
  subject: "Test Subject for Edit",
  htmlContent: "<h1>Hello {customer_name}!</h1><p>This is a test template for editing.</p>",
  textContent: "Hello {customer_name}! This is a test template for editing.",
  type: "NEWSLETTER",
  previewText: "Test preview text for editing...",
  tags: ["test", "edit"],
  variables: [
    {
      name: "customer_name",
      type: "TEXT",
      defaultValue: "Customer",
      required: true,
      description: "Customer name for testing"
    }
  ]
};

// Function để tạo template test
async function createTestTemplate() {
  try {
    console.log('🧪 [TEST] Creating test template...');
    
    // Gọi API tạo template
    const response = await fetch('/api/v1/marketing/template-emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token') || sessionStorage.getItem('token')}`
      },
      body: JSON.stringify(testTemplateData)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('✅ [TEST] Template created successfully!');
    console.log('📄 [TEST] Template data:', result);
    
    // Refresh trang để thấy template mới
    window.location.reload();
    
    return result;
  } catch (error) {
    console.error('❌ [TEST] Failed to create template:', error);
    throw error;
  }
}

// Function để test trực tiếp với service
async function createTestTemplateWithService() {
  try {
    console.log('🧪 [TEST] Creating test template with service...');
    
    // Import service (chỉ hoạt động trong app context)
    const { EmailTemplateAdapterService } = await import('./src/modules/marketing/services/email-template-adapter.service.ts');
    
    const result = await EmailTemplateAdapterService.createEmailTemplate(testTemplateData);
    
    console.log('✅ [TEST] Template created with service!');
    console.log('📄 [TEST] Result:', result);
    
    return result;
  } catch (error) {
    console.error('❌ [TEST] Failed to create template with service:', error);
    throw error;
  }
}

// Export functions for console use
window.testTemplate = {
  create: createTestTemplate,
  createWithService: createTestTemplateWithService,
  data: testTemplateData
};

console.log('🧪 [TEST] Template creation tools loaded!');
console.log('📋 [TEST] Available functions:');
console.log('  - testTemplate.create() - Create via direct API call');
console.log('  - testTemplate.createWithService() - Create via service');
console.log('💡 [TEST] Usage: testTemplate.create()');
