#!/usr/bin/env node

/**
 * i18n Audit Tool for Admin Data Module
 * Scans for translation usage, hardcoded text, and namespace inconsistencies
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  targetDir: 'src/modules/admin/data',
  outputFile: 'docs/i18n-audit-report.md',
  fileExtensions: ['.tsx', '.ts'],
  excludeDirs: ['node_modules', '.git', 'dist', 'build'],
};

// Patterns to detect
const PATTERNS = {
  useTranslation: /useTranslation\(\[(.*?)\]\)/g,
  translationCall: /t\(['"`](.*?)['"`](?:,\s*['"`](.*?)['"`])?\)/g,
  hardcodedText: />\s*([A-Za-z\u00C0-\u017F\u0400-\u04FF\u4e00-\u9fff][^<>{]*?)\s*</g,
  jsxText: /['"`]([A-Za-z\u00C0-\u017F\u0400-\u04FF\u4e00-\u9fff][^'"`]*?)['"`]/g,
};

class I18nAuditor {
  constructor() {
    this.results = {
      files: [],
      summary: {
        totalFiles: 0,
        filesWithTranslations: 0,
        filesWithHardcodedText: 0,
        totalTranslationCalls: 0,
        totalHardcodedStrings: 0,
        namespaceUsage: {},
        issues: [],
      }
    };
  }

  // Scan directory recursively
  scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !CONFIG.excludeDirs.includes(file)) {
        this.scanDirectory(filePath);
      } else if (stat.isFile() && this.shouldScanFile(file)) {
        this.scanFile(filePath);
      }
    }
  }

  // Check if file should be scanned
  shouldScanFile(filename) {
    return CONFIG.fileExtensions.some(ext => filename.endsWith(ext));
  }

  // Scan individual file
  scanFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    const fileResult = {
      path: relativePath,
      useTranslationCalls: [],
      translationCalls: [],
      hardcodedStrings: [],
      issues: [],
    };

    // Find useTranslation calls
    let match;
    while ((match = PATTERNS.useTranslation.exec(content)) !== null) {
      const namespaces = match[1].split(',').map(ns => ns.trim().replace(/['"]/g, ''));
      fileResult.useTranslationCalls.push({
        line: this.getLineNumber(content, match.index),
        namespaces: namespaces,
        raw: match[0],
      });
      
      // Track namespace usage
      namespaces.forEach(ns => {
        this.results.summary.namespaceUsage[ns] = (this.results.summary.namespaceUsage[ns] || 0) + 1;
      });
    }

    // Reset regex
    PATTERNS.useTranslation.lastIndex = 0;

    // Find translation calls
    while ((match = PATTERNS.translationCall.exec(content)) !== null) {
      fileResult.translationCalls.push({
        line: this.getLineNumber(content, match.index),
        key: match[1],
        fallback: match[2] || null,
        raw: match[0],
      });
    }

    // Reset regex
    PATTERNS.translationCall.lastIndex = 0;

    // Find potential hardcoded text (simplified detection)
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      // Skip comments and imports
      if (line.trim().startsWith('//') || line.trim().startsWith('import') || line.trim().startsWith('*')) {
        return;
      }

      // Look for JSX text content
      const jsxTextMatches = line.match(/>\s*([A-Za-z\u00C0-\u017F\u0400-\u04FF\u4e00-\u9fff][^<>{}]*?)\s*</g);
      if (jsxTextMatches) {
        jsxTextMatches.forEach(match => {
          const text = match.replace(/[><]/g, '').trim();
          if (text.length > 2 && !this.isLikelyNotHardcoded(text)) {
            fileResult.hardcodedStrings.push({
              line: index + 1,
              text: text,
              context: line.trim(),
            });
          }
        });
      }

      // Look for string literals that might be hardcoded
      const stringMatches = line.match(/['"`]([A-Za-z\u00C0-\u017F\u0400-\u04FF\u4e00-\u9fff][^'"`]*?)['"`]/g);
      if (stringMatches) {
        stringMatches.forEach(match => {
          const text = match.replace(/['"]/g, '');
          if (text.length > 3 && this.isLikelyHardcodedText(text, line)) {
            fileResult.hardcodedStrings.push({
              line: index + 1,
              text: text,
              context: line.trim(),
            });
          }
        });
      }
    });

    // Analyze issues
    this.analyzeFileIssues(fileResult);

    // Update summary
    this.results.summary.totalFiles++;
    if (fileResult.translationCalls.length > 0) {
      this.results.summary.filesWithTranslations++;
    }
    if (fileResult.hardcodedStrings.length > 0) {
      this.results.summary.filesWithHardcodedText++;
    }
    this.results.summary.totalTranslationCalls += fileResult.translationCalls.length;
    this.results.summary.totalHardcodedStrings += fileResult.hardcodedStrings.length;

    this.results.files.push(fileResult);
  }

  // Get line number from character index
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  // Check if text is likely not hardcoded
  isLikelyNotHardcoded(text) {
    const excludePatterns = [
      /^[0-9]+$/,           // Numbers
      /^[a-z-]+$/,          // CSS classes or IDs
      /^[A-Z_]+$/,          // Constants
      /^\w+\.\w+$/,         // Object properties
      /^(true|false)$/,     // Booleans
      /^(px|em|rem|%|vh|vw)$/, // CSS units
    ];
    
    return excludePatterns.some(pattern => pattern.test(text));
  }

  // Check if text is likely hardcoded UI text
  isLikelyHardcodedText(text, line) {
    // Skip if it's in a t() call
    if (line.includes(`t(`)) return false;
    
    // Skip common non-UI strings
    const skipPatterns = [
      /^[a-z-]+$/,          // CSS classes
      /^[A-Z_]+$/,          // Constants
      /^\w+\.\w+$/,         // Object properties
      /^(GET|POST|PUT|DELETE)$/, // HTTP methods
      /^(error|success|warning|info)$/, // Log levels
    ];
    
    if (skipPatterns.some(pattern => pattern.test(text))) return false;
    
    // Likely UI text if it contains spaces or Vietnamese/Chinese characters
    return /\s/.test(text) || /[\u00C0-\u017F\u0400-\u04FF\u4e00-\u9fff]/.test(text);
  }

  // Analyze issues in a file
  analyzeFileIssues(fileResult) {
    // Check for inconsistent namespace usage
    if (fileResult.useTranslationCalls.length > 1) {
      const allNamespaces = fileResult.useTranslationCalls.map(call => call.namespaces.join(','));
      const uniqueNamespaces = [...new Set(allNamespaces)];
      if (uniqueNamespaces.length > 1) {
        fileResult.issues.push({
          type: 'inconsistent_namespaces',
          message: 'Multiple different namespace patterns found',
          details: uniqueNamespaces,
        });
      }
    }

    // Check for missing useTranslation when t() is used
    if (fileResult.translationCalls.length > 0 && fileResult.useTranslationCalls.length === 0) {
      fileResult.issues.push({
        type: 'missing_use_translation',
        message: 'Translation calls found but no useTranslation hook',
      });
    }

    // Add issues to summary
    this.results.summary.issues.push(...fileResult.issues);
  }

  // Generate report
  generateReport() {
    const report = this.buildMarkdownReport();
    
    // Ensure output directory exists
    const outputDir = path.dirname(CONFIG.outputFile);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    fs.writeFileSync(CONFIG.outputFile, report);
    console.log(`✅ Audit report generated: ${CONFIG.outputFile}`);
  }

  // Build markdown report
  buildMarkdownReport() {
    const { summary, files } = this.results;
    
    return `# i18n Audit Report - Admin Data Module

Generated on: ${new Date().toISOString()}

## 📊 Summary

- **Total Files Scanned**: ${summary.totalFiles}
- **Files with Translations**: ${summary.filesWithTranslations}
- **Files with Hardcoded Text**: ${summary.filesWithHardcodedText}
- **Total Translation Calls**: ${summary.totalTranslationCalls}
- **Total Hardcoded Strings**: ${summary.totalHardcodedStrings}
- **Total Issues**: ${summary.issues.length}

## 🔍 Namespace Usage

${Object.entries(summary.namespaceUsage)
  .map(([ns, count]) => `- **${ns}**: ${count} files`)
  .join('\n')}

## ⚠️ Issues Found

${summary.issues.length === 0 ? 'No issues found.' : 
  summary.issues.map((issue, i) => `${i + 1}. **${issue.type}**: ${issue.message}`).join('\n')}

## 📁 File Details

${files.map(file => this.formatFileReport(file)).join('\n\n')}

---
*Generated by i18n-audit tool*
`;
  }

  // Format individual file report
  formatFileReport(file) {
    return `### ${file.path}

**useTranslation Calls**: ${file.useTranslationCalls.length}
${file.useTranslationCalls.map(call => 
  `- Line ${call.line}: \`${call.raw}\` (namespaces: ${call.namespaces.join(', ')})`
).join('\n')}

**Translation Calls**: ${file.translationCalls.length}
${file.translationCalls.slice(0, 5).map(call => 
  `- Line ${call.line}: \`${call.key}\`${call.fallback ? ` (fallback: "${call.fallback}")` : ''}`
).join('\n')}
${file.translationCalls.length > 5 ? `... and ${file.translationCalls.length - 5} more` : ''}

**Hardcoded Strings**: ${file.hardcodedStrings.length}
${file.hardcodedStrings.slice(0, 3).map(str => 
  `- Line ${str.line}: "${str.text}"`
).join('\n')}
${file.hardcodedStrings.length > 3 ? `... and ${file.hardcodedStrings.length - 3} more` : ''}

**Issues**: ${file.issues.length}
${file.issues.map(issue => `- ${issue.type}: ${issue.message}`).join('\n')}`;
  }

  // Run audit
  run() {
    console.log('🔍 Starting i18n audit for admin data module...');
    console.log(`📁 Scanning directory: ${CONFIG.targetDir}`);
    
    if (!fs.existsSync(CONFIG.targetDir)) {
      console.error(`❌ Directory not found: ${CONFIG.targetDir}`);
      process.exit(1);
    }
    
    this.scanDirectory(CONFIG.targetDir);
    this.generateReport();
    
    console.log('\n📊 Audit Summary:');
    console.log(`- Files scanned: ${this.results.summary.totalFiles}`);
    console.log(`- Translation calls: ${this.results.summary.totalTranslationCalls}`);
    console.log(`- Hardcoded strings: ${this.results.summary.totalHardcodedStrings}`);
    console.log(`- Issues found: ${this.results.summary.issues.length}`);
  }
}

// Run if called directly
if (require.main === module) {
  const auditor = new I18nAuditor();
  auditor.run();
}

module.exports = I18nAuditor;
