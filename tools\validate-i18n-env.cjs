#!/usr/bin/env node

/**
 * i18n Environment Validation Tool
 * Validates that the development environment is ready for i18n refactoring
 */

const fs = require('fs');
const path = require('path');

class I18nEnvironmentValidator {
  constructor() {
    this.results = {
      dependencies: { status: 'pending', details: [] },
      configuration: { status: 'pending', details: [] },
      tools: { status: 'pending', details: [] },
      documentation: { status: 'pending', details: [] },
      overall: { status: 'pending', score: 0 }
    };
  }

  // Validate i18n dependencies
  validateDependencies() {
    console.log('🔍 Validating i18n dependencies...');
    
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      const requiredDeps = {
        'i18next': '^25.0.0',
        'react-i18next': '^15.0.0',
        'eslint-plugin-i18n-json': '^4.0.0'
      };
      
      let score = 0;
      const details = [];
      
      for (const [dep, minVersion] of Object.entries(requiredDeps)) {
        if (deps[dep]) {
          details.push(`✅ ${dep}: ${deps[dep]}`);
          score++;
        } else {
          details.push(`❌ ${dep}: Missing`);
        }
      }
      
      this.results.dependencies = {
        status: score === Object.keys(requiredDeps).length ? 'pass' : 'fail',
        details,
        score: score / Object.keys(requiredDeps).length
      };
      
    } catch (error) {
      this.results.dependencies = {
        status: 'error',
        details: [`❌ Error reading package.json: ${error.message}`],
        score: 0
      };
    }
  }

  // Validate i18n configuration
  validateConfiguration() {
    console.log('🔍 Validating i18n configuration...');
    
    const configChecks = [
      {
        name: 'Main i18n config',
        path: 'src/lib/i18n.ts',
        required: true
      },
      {
        name: 'Admin data locales',
        path: 'src/modules/admin/data/locales',
        required: true
      },
      {
        name: 'Vietnamese translations',
        path: 'src/modules/admin/data/locales/vi.json',
        required: true
      },
      {
        name: 'English translations',
        path: 'src/modules/admin/data/locales/en.json',
        required: true
      },
      {
        name: 'Chinese translations',
        path: 'src/modules/admin/data/locales/zh.json',
        required: true
      }
    ];
    
    let score = 0;
    const details = [];
    
    for (const check of configChecks) {
      if (fs.existsSync(check.path)) {
        details.push(`✅ ${check.name}: Found`);
        score++;
      } else {
        details.push(`❌ ${check.name}: Missing`);
      }
    }
    
    // Check i18n integration
    try {
      const i18nConfig = fs.readFileSync('src/lib/i18n.ts', 'utf8');
      if (i18nConfig.includes('dataResources')) {
        details.push(`✅ Admin data module integrated in i18n config`);
        score += 0.5;
      } else {
        details.push(`❌ Admin data module not integrated in i18n config`);
      }
    } catch (error) {
      details.push(`❌ Error reading i18n config: ${error.message}`);
    }
    
    this.results.configuration = {
      status: score >= configChecks.length ? 'pass' : 'partial',
      details,
      score: score / (configChecks.length + 0.5)
    };
  }

  // Validate development tools
  validateTools() {
    console.log('🔍 Validating development tools...');
    
    const toolChecks = [
      {
        name: 'i18n audit tool',
        path: 'tools/i18n-audit.cjs',
        required: true
      },
      {
        name: 'Environment validator',
        path: 'tools/validate-i18n-env.cjs',
        required: true
      },
      {
        name: 'Audit report',
        path: 'docs/i18n-audit-report.md',
        required: false
      }
    ];
    
    let score = 0;
    const details = [];
    
    for (const check of toolChecks) {
      if (fs.existsSync(check.path)) {
        details.push(`✅ ${check.name}: Available`);
        score++;
      } else {
        details.push(`${check.required ? '❌' : '⚠️'} ${check.name}: ${check.required ? 'Missing' : 'Not found'}`);
        if (!check.required) score += 0.5;
      }
    }
    
    // Test audit tool
    try {
      const auditTool = fs.readFileSync('tools/i18n-audit.cjs', 'utf8');
      if (auditTool.includes('class I18nAuditor')) {
        details.push(`✅ Audit tool appears functional`);
        score += 0.5;
      }
    } catch (error) {
      details.push(`❌ Error validating audit tool: ${error.message}`);
    }
    
    this.results.tools = {
      status: score >= toolChecks.length ? 'pass' : 'partial',
      details,
      score: score / (toolChecks.length + 0.5)
    };
  }

  // Validate documentation workspace
  validateDocumentation() {
    console.log('🔍 Validating documentation workspace...');
    
    const docChecks = [
      {
        name: 'i18n documentation directory',
        path: 'docs/i18n',
        required: true
      },
      {
        name: 'Documentation README',
        path: 'docs/i18n/README.md',
        required: true
      },
      {
        name: 'Namespace standards',
        path: 'docs/i18n/guidelines/namespace-standards.md',
        required: true
      },
      {
        name: 'Initial audit report',
        path: 'docs/i18n/audit-reports/initial-audit.md',
        required: true
      },
      {
        name: 'Translation process guide',
        path: 'docs/i18n/workflows/translation-process.md',
        required: true
      }
    ];
    
    let score = 0;
    const details = [];
    
    for (const check of docChecks) {
      if (fs.existsSync(check.path)) {
        details.push(`✅ ${check.name}: Available`);
        score++;
      } else {
        details.push(`❌ ${check.name}: Missing`);
      }
    }
    
    this.results.documentation = {
      status: score === docChecks.length ? 'pass' : 'partial',
      details,
      score: score / docChecks.length
    };
  }

  // Calculate overall readiness
  calculateOverallReadiness() {
    const categories = ['dependencies', 'configuration', 'tools', 'documentation'];
    let totalScore = 0;
    let passCount = 0;
    
    for (const category of categories) {
      totalScore += this.results[category].score;
      if (this.results[category].status === 'pass') {
        passCount++;
      }
    }
    
    const averageScore = totalScore / categories.length;
    
    let status = 'fail';
    if (averageScore >= 0.9 && passCount >= 3) {
      status = 'ready';
    } else if (averageScore >= 0.7) {
      status = 'partial';
    }
    
    this.results.overall = {
      status,
      score: averageScore,
      passCount,
      totalCategories: categories.length
    };
  }

  // Generate validation report
  generateReport() {
    const report = `# i18n Environment Validation Report

Generated on: ${new Date().toISOString()}

## 📊 Overall Readiness

**Status**: ${this.getStatusEmoji(this.results.overall.status)} ${this.results.overall.status.toUpperCase()}
**Score**: ${(this.results.overall.score * 100).toFixed(1)}%
**Categories Passed**: ${this.results.overall.passCount}/${this.results.overall.totalCategories}

## 📋 Category Results

### 1. Dependencies ${this.getStatusEmoji(this.results.dependencies.status)}
**Status**: ${this.results.dependencies.status}
**Score**: ${(this.results.dependencies.score * 100).toFixed(1)}%

${this.results.dependencies.details.map(detail => `- ${detail}`).join('\n')}

### 2. Configuration ${this.getStatusEmoji(this.results.configuration.status)}
**Status**: ${this.results.configuration.status}
**Score**: ${(this.results.configuration.score * 100).toFixed(1)}%

${this.results.configuration.details.map(detail => `- ${detail}`).join('\n')}

### 3. Development Tools ${this.getStatusEmoji(this.results.tools.status)}
**Status**: ${this.results.tools.status}
**Score**: ${(this.results.tools.score * 100).toFixed(1)}%

${this.results.tools.details.map(detail => `- ${detail}`).join('\n')}

### 4. Documentation ${this.getStatusEmoji(this.results.documentation.status)}
**Status**: ${this.results.documentation.status}
**Score**: ${(this.results.documentation.score * 100).toFixed(1)}%

${this.results.documentation.details.map(detail => `- ${detail}`).join('\n')}

## 🎯 Recommendations

${this.generateRecommendations()}

## ✅ Next Steps

${this.generateNextSteps()}

---
*Generated by i18n environment validation tool*
`;

    fs.writeFileSync('docs/i18n-environment-validation.md', report);
    console.log('\n📄 Validation report generated: docs/i18n-environment-validation.md');
  }

  getStatusEmoji(status) {
    const emojis = {
      'pass': '✅',
      'ready': '🚀',
      'partial': '⚠️',
      'fail': '❌',
      'error': '🚨',
      'pending': '⏳'
    };
    return emojis[status] || '❓';
  }

  generateRecommendations() {
    const recommendations = [];
    
    if (this.results.dependencies.status !== 'pass') {
      recommendations.push('- Install missing i18n dependencies');
    }
    
    if (this.results.configuration.status !== 'pass') {
      recommendations.push('- Complete i18n configuration setup');
      recommendations.push('- Ensure all translation files exist');
    }
    
    if (this.results.tools.status !== 'pass') {
      recommendations.push('- Set up development tools for i18n auditing');
    }
    
    if (this.results.documentation.status !== 'pass') {
      recommendations.push('- Complete documentation workspace setup');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('- Environment is ready for i18n refactoring!');
      recommendations.push('- Proceed with the next phase of the project');
    }
    
    return recommendations.join('\n');
  }

  generateNextSteps() {
    if (this.results.overall.status === 'ready') {
      return `- ✅ Environment validation complete
- 🚀 Ready to proceed with i18n refactoring
- 📋 Begin with Task 2: Audit Translation Usage in AdminDataManagementPage`;
    } else {
      return `- 🔧 Address the issues identified above
- 🔄 Re-run validation: \`node tools/validate-i18n-env.cjs\`
- 📋 Complete environment setup before proceeding`;
    }
  }

  // Run validation
  run() {
    console.log('🚀 Starting i18n environment validation...\n');
    
    this.validateDependencies();
    this.validateConfiguration();
    this.validateTools();
    this.validateDocumentation();
    this.calculateOverallReadiness();
    
    console.log('\n📊 Validation Summary:');
    console.log(`Overall Status: ${this.getStatusEmoji(this.results.overall.status)} ${this.results.overall.status.toUpperCase()}`);
    console.log(`Score: ${(this.results.overall.score * 100).toFixed(1)}%`);
    console.log(`Categories Passed: ${this.results.overall.passCount}/${this.results.overall.totalCategories}`);
    
    this.generateReport();
    
    if (this.results.overall.status === 'ready') {
      console.log('\n🎉 Environment is ready for i18n refactoring!');
    } else {
      console.log('\n⚠️ Environment needs attention before proceeding.');
    }
  }
}

// Run if called directly
if (require.main === module) {
  const validator = new I18nEnvironmentValidator();
  validator.run();
}

module.exports = I18nEnvironmentValidator;
